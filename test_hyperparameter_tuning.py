#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
สคริปต์ทดสอบ Hyperparameter Tuning สำหรับ LightGBM
ทดสอบการทำงานของฟังก์ชัน get_lgbm_params(), param_dist, และ train_and_evaluate()
"""

import sys
import os
import pandas as pd
import numpy as np
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score, classification_report
import lightgbm as lgb
import json
import datetime

# เพิ่ม path สำหรับ import ฟังก์ชันจากไฟล์หลัก
sys.path.append('.')

# Import ฟังก์ชันที่ต้องการทดสอบ
try:
    from python_LightGBM_16_Signal import (
        param_dist,
        get_lgbm_params, 
        comprehensive_hyperparameter_test,
        analyze_parameter_sensitivity
    )
    print("✅ Import ฟังก์ชันสำเร็จ")
except ImportError as e:
    print(f"❌ ไม่สามารถ import ฟังก์ชันได้: {e}")
    sys.exit(1)

def create_synthetic_financial_data(n_samples=5000, n_features=50, random_state=42):
    """
    สร้างข้อมูลจำลองที่มีลักษณะคล้าย financial data
    """
    print(f"\n📊 สร้างข้อมูลจำลอง...")
    print(f"   - จำนวนตัวอย่าง: {n_samples:,}")
    print(f"   - จำนวน features: {n_features}")
    
    # สร้างข้อมูลพื้นฐาน
    X, y = make_classification(
        n_samples=n_samples,
        n_features=n_features,
        n_informative=int(n_features * 0.7),  # 70% เป็น informative features
        n_redundant=int(n_features * 0.2),    # 20% เป็น redundant features
        n_clusters_per_class=2,
        weights=[0.85, 0.15],  # Class imbalance (15% positive class)
        flip_y=0.02,           # 2% label noise
        random_state=random_state
    )
    
    # สร้างชื่อ features ที่เหมือน financial indicators
    feature_names = []
    indicators = ['RSI', 'MACD', 'EMA', 'ATR', 'BB', 'ADX', 'STO', 'Volume']
    periods = [5, 10, 14, 20, 50, 100, 200]
    
    for i in range(n_features):
        if i < len(indicators) * len(periods):
            indicator = indicators[i % len(indicators)]
            period = periods[i // len(indicators)]
            feature_names.append(f"{indicator}_{period}")
        else:
            feature_names.append(f"Feature_{i}")
    
    # แปลงเป็น DataFrame
    X_df = pd.DataFrame(X, columns=feature_names)
    
    # เพิ่ม DateTime column (จำลอง time series)
    start_date = datetime.datetime(2020, 1, 1)
    dates = [start_date + datetime.timedelta(hours=i) for i in range(n_samples)]
    X_df['DateTime'] = dates
    
    print(f"✅ สร้างข้อมูลจำลองเสร็จสิ้น")
    print(f"   - Class distribution: {np.bincount(y)}")
    print(f"   - Class ratio (0:1): {np.sum(y==0)/np.sum(y==1):.2f}:1")
    
    return X_df, y

def test_get_lgbm_params():
    """
    ทดสอบฟังก์ชัน get_lgbm_params()
    """
    print(f"\n🧪 ทดสอบฟังก์ชัน get_lgbm_params()")
    print("="*50)
    
    # สร้างข้อมูล y จำลอง
    y_balanced = np.array([0, 1] * 500)  # Balanced
    y_imbalanced = np.array([0] * 900 + [1] * 100)  # Imbalanced (9:1)
    
    # ทดสอบกับข้อมูล balanced
    print("\n1️⃣ ทดสอบกับข้อมูล Balanced:")
    params_balanced = get_lgbm_params(y=y_balanced, use_scale_pos_weight=True)
    print(f"   - scale_pos_weight: {params_balanced.get('scale_pos_weight', 'ไม่มี')}")
    print(f"   - learning_rate: {params_balanced['learning_rate']}")
    print(f"   - num_leaves: {params_balanced['num_leaves']}")
    
    # ทดสอบกับข้อมูล imbalanced
    print("\n2️⃣ ทดสอบกับข้อมูล Imbalanced:")
    params_imbalanced = get_lgbm_params(y=y_imbalanced, use_scale_pos_weight=True)
    print(f"   - scale_pos_weight: {params_imbalanced.get('scale_pos_weight', 'ไม่มี')}")
    print(f"   - learning_rate: {params_imbalanced['learning_rate']}")
    print(f"   - num_leaves: {params_imbalanced['num_leaves']}")
    
    # ทดสอบโดยไม่ใช้ scale_pos_weight
    print("\n3️⃣ ทดสอบโดยไม่ใช้ scale_pos_weight:")
    params_no_scale = get_lgbm_params(y=y_imbalanced, use_scale_pos_weight=False)
    print(f"   - scale_pos_weight: {params_no_scale.get('scale_pos_weight', 'ไม่มี')}")
    
    return params_balanced, params_imbalanced, params_no_scale

def test_param_dist():
    """
    ทดสอบ param_dist dictionary
    """
    print(f"\n🧪 ทดสอบ param_dist")
    print("="*50)
    
    print(f"จำนวนพารามิเตอร์ที่จะ tune: {len(param_dist)}")
    
    total_combinations = 1
    for param, values in param_dist.items():
        print(f"   - {param}: {len(values)} ตัวเลือก {values}")
        total_combinations *= len(values)
    
    print(f"\nจำนวนการผสมผสานทั้งหมด: {total_combinations:,}")
    print(f"ด้วย RandomizedSearchCV n_iter=50 จะทดสอบ: {min(50, total_combinations)} การผสมผสาน")
    
    return param_dist

def test_hyperparameter_tuning_workflow():
    """
    ทดสอบ workflow ทั้งหมดของ hyperparameter tuning
    """
    print(f"\n🧪 ทดสอบ Hyperparameter Tuning Workflow")
    print("="*80)
    
    # 1. สร้างข้อมูลทดสอบ
    X, y = create_synthetic_financial_data(n_samples=2000, n_features=30)
    
    # 2. แบ่งข้อมูล
    # ใช้ temporal split (เหมือน time series)
    split_idx = int(len(X) * 0.6)
    val_split_idx = int(len(X) * 0.8)
    
    X_train = X.iloc[:split_idx].drop('DateTime', axis=1)
    y_train = y[:split_idx]
    
    X_val = X.iloc[split_idx:val_split_idx].drop('DateTime', axis=1)
    y_val = y[split_idx:val_split_idx]
    
    X_test = X.iloc[val_split_idx:].drop('DateTime', axis=1)
    y_test = y[val_split_idx:]
    
    print(f"\nการแบ่งข้อมูล:")
    print(f"   - Train: {len(X_train):,} samples")
    print(f"   - Val:   {len(X_val):,} samples") 
    print(f"   - Test:  {len(X_test):,} samples")
    
    # 3. ทดสอบ comprehensive hyperparameter test
    print(f"\n🔍 ทดสอบ Comprehensive Hyperparameter Test")
    try:
        results = comprehensive_hyperparameter_test(
            X_train, y_train, X_val, y_val, 
            symbol="TEST", timeframe="H1"
        )
        print(f"✅ Comprehensive test สำเร็จ")
        return results
    except Exception as e:
        print(f"❌ Comprehensive test ล้มเหลว: {e}")
        return None

def test_parameter_sensitivity():
    """
    ทดสอบการวิเคราะห์ parameter sensitivity
    """
    print(f"\n🧪 ทดสอบ Parameter Sensitivity Analysis")
    print("="*60)
    
    # สร้างข้อมูลทดสอบขนาดเล็ก
    X, y = create_synthetic_financial_data(n_samples=1000, n_features=20)
    
    # แบ่งข้อมูล
    split_idx = int(len(X) * 0.7)
    X_train = X.iloc[:split_idx].drop('DateTime', axis=1)
    y_train = y[:split_idx]
    X_val = X.iloc[split_idx:].drop('DateTime', axis=1)
    y_val = y[split_idx:]
    
    try:
        sensitivity_results, base_auc = analyze_parameter_sensitivity(
            X_train, y_train, X_val, y_val,
            symbol="TEST", timeframe="H1"
        )
        print(f"✅ Parameter sensitivity analysis สำเร็จ")
        return sensitivity_results, base_auc
    except Exception as e:
        print(f"❌ Parameter sensitivity analysis ล้มเหลว: {e}")
        return None, None

def main():
    """
    ฟังก์ชันหลักสำหรับการทดสอบ
    """
    print("🚀 เริ่มการทดสอบ Hyperparameter Tuning")
    print("="*80)
    
    # ทดสอบแต่ละส่วน
    try:
        # 1. ทดสอบ get_lgbm_params
        params_results = test_get_lgbm_params()
        
        # 2. ทดสอบ param_dist
        param_dist_info = test_param_dist()
        
        # 3. ทดสอบ workflow (ใช้เวลานาน)
        print(f"\n⚠️ การทดสอบ workflow จะใช้เวลาประมาณ 2-3 นาที")
        user_input = input("ต้องการทดสอบ workflow หรือไม่? (y/n): ")
        
        if user_input.lower() == 'y':
            workflow_results = test_hyperparameter_tuning_workflow()
            
            # 4. ทดสอบ parameter sensitivity (ใช้เวลานาน)
            print(f"\n⚠️ การทดสอบ parameter sensitivity จะใช้เวลาประมาณ 1-2 นาที")
            user_input = input("ต้องการทดสอบ parameter sensitivity หรือไม่? (y/n): ")
            
            if user_input.lower() == 'y':
                sensitivity_results, base_auc = test_parameter_sensitivity()
        
        print(f"\n🎉 การทดสอบเสร็จสิ้น!")
        print("="*50)
        print("✅ ทุกฟังก์ชันทำงานได้ปกติ")
        
    except Exception as e:
        print(f"\n❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

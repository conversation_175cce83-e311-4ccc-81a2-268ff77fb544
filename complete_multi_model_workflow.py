#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ตัวอย่างการใช้งาน Multi-Model Architecture แบบสมบูรณ์
รวมการเทรน, การหา optimal parameters, และการใช้งานใน production

Created: 2025-01-11
Author: AI Assistant
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
import json
from pathlib import Path

# เพิ่ม path สำหรับ import
sys.path.append('.')

def step1_train_multi_model():
    """
    Step 1: เทรนโมเดล Multi-Model Architecture
    """
    print("🚀 Step 1: เทรนโมเดล Multi-Model Architecture")
    print("="*60)
    
    print("📝 การตั้งค่า:")
    print("   USE_MULTI_MODEL_ARCHITECTURE = True")
    print("   python python_LightGBM_16_Signal.py")
    
    print("\n📊 ผลลัพธ์ที่คาดหวัง:")
    print("   ✅ โมเดล trend_following")
    print("   ✅ โมเดล counter_trend")
    print("   ✅ Feature importance files")
    print("   ✅ Performance analysis")
    
    # ตรวจสอบว่าโมเดลถูกเทรนแล้วหรือยัง
    models_path = Path("LightGBM_Multi/models")
    if models_path.exists():
        trend_models = list((models_path / "trend_following").glob("*.pkl"))
        counter_models = list((models_path / "counter_trend").glob("*.pkl"))
        
        print(f"\n📁 สถานะโมเดลปัจจุบัน:")
        print(f"   Trend following models: {len(trend_models)} files")
        print(f"   Counter trend models: {len(counter_models)} files")
        
        if len(trend_models) > 0 and len(counter_models) > 0:
            print("   ✅ โมเดลพร้อมใช้งาน")
            return True
        else:
            print("   ⚠️ โมเดลยังไม่ครบ - ต้องเทรนใหม่")
            return False
    else:
        print("   ❌ ไม่พบโฟลเดอร์โมเดล - ต้องเทรนก่อน")
        return False

def step2_optimize_parameters():
    """
    Step 2: หา optimal threshold และ nBars_SL
    """
    print("\n🎯 Step 2: หา optimal threshold และ nBars_SL")
    print("="*60)
    
    try:
        from python_LightGBM_16_Signal import (
            load_scenario_models,
            run_multi_model_optimization
        )
        
        symbols = ["GOLD"]  # เริ่มด้วย GOLD ก่อน
        timeframe = 60
        
        optimization_results = {}
        
        for symbol in symbols:
            print(f"\n🔍 กำลังหา optimal parameters สำหรับ {symbol} M{timeframe}")
            
            # ตรวจสอบว่ามีโมเดลหรือไม่
            loaded_models = load_scenario_models(symbol, timeframe)
            
            if loaded_models:
                print(f"✅ พบโมเดล {len(loaded_models)} scenarios")
                
                # รันการหา optimal parameters
                result = run_multi_model_optimization(symbol, timeframe)
                
                if result:
                    optimization_results[symbol] = result
                    print(f"✅ เสร็จสิ้นการหา optimal parameters สำหรับ {symbol}")
                    
                    # แสดงผลลัพธ์
                    for scenario in result['scenarios']:
                        threshold = result['optimal_thresholds'].get(scenario, 'N/A')
                        nbars = result['optimal_nbars'].get(scenario, 'N/A')
                        print(f"   {scenario}: threshold={threshold}, nBars_SL={nbars}")
                else:
                    print(f"❌ ไม่สามารถหา optimal parameters สำหรับ {symbol}")
            else:
                print(f"❌ ไม่พบโมเดลสำหรับ {symbol} M{timeframe}")
        
        return optimization_results
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return {}
    except Exception as e:
        print(f"❌ Error: {e}")
        return {}

def step3_test_parameter_loading():
    """
    Step 3: ทดสอบการโหลดพารามิเตอร์
    """
    print("\n📋 Step 3: ทดสอบการโหลดพารามิเตอร์")
    print("="*60)
    
    try:
        from python_LightGBM_16_Signal import (
            load_scenario_threshold,
            load_scenario_nbars,
            get_optimal_parameters
        )
        
        symbol = "GOLD"
        timeframe = 60
        scenarios = ["trend_following", "counter_trend"]
        
        print(f"🔍 ทดสอบการโหลดพารามิเตอร์สำหรับ {symbol} M{timeframe}")
        
        # ทดสอบการโหลด threshold และ nBars_SL
        for scenario in scenarios:
            threshold = load_scenario_threshold(symbol, timeframe, scenario)
            nbars = load_scenario_nbars(symbol, timeframe, scenario)
            
            print(f"   {scenario}:")
            print(f"     Threshold: {threshold}")
            print(f"     nBars_SL: {nbars}")
        
        # ทดสอบการเลือกพารามิเตอร์ตามสถานการณ์
        test_cases = [
            {'market_condition': 'uptrend', 'action_type': 'buy'},
            {'market_condition': 'uptrend', 'action_type': 'sell'},
            {'market_condition': 'downtrend', 'action_type': 'buy'},
            {'market_condition': 'downtrend', 'action_type': 'sell'},
            {'market_condition': 'sideways', 'action_type': 'buy'}
        ]
        
        print(f"\n🧪 ทดสอบการเลือกพารามิเตอร์ตามสถานการณ์:")
        
        for i, test_case in enumerate(test_cases, 1):
            params = get_optimal_parameters(
                symbol, timeframe, 
                test_case['market_condition'], 
                test_case['action_type']
            )
            
            print(f"   Test {i}: {test_case['market_condition']} + {test_case['action_type']}")
            print(f"     → Scenario: {params['scenario']}")
            print(f"     → Threshold: {params['threshold']:.4f}")
            print(f"     → nBars_SL: {params['nBars_SL']}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def step4_test_prediction():
    """
    Step 4: ทดสอบการทำนายด้วยพารามิเตอร์ที่เหมาะสม
    """
    print("\n🔮 Step 4: ทดสอบการทำนายด้วยพารามิเตอร์ที่เหมาะสม")
    print("="*60)
    
    try:
        from python_LightGBM_16_Signal import (
            predict_with_optimal_parameters,
            load_scenario_models
        )
        
        symbol = "GOLD"
        timeframe = 60
        
        # ตรวจสอบว่ามีโมเดลหรือไม่
        loaded_models = load_scenario_models(symbol, timeframe)
        
        if not loaded_models:
            print(f"❌ ไม่พบโมเดลสำหรับ {symbol} M{timeframe}")
            return False
        
        # สร้างข้อมูลตัวอย่างสำหรับทดสอบ
        first_model = list(loaded_models.values())[0]
        required_features = first_model['features']
        
        print(f"📊 สร้างข้อมูลตัวอย่างสำหรับทดสอบ")
        print(f"   Required features: {len(required_features)} features")
        
        # สร้างข้อมูลตัวอย่าง
        np.random.seed(42)
        market_data = {}
        
        for feature in required_features:
            if feature == 'Target':
                market_data[feature] = 1  # ตัวอย่าง
            elif 'Close' in feature:
                market_data[feature] = 1800.0  # ราคา Gold
            elif 'High' in feature:
                market_data[feature] = 1810.0
            elif 'Low' in feature:
                market_data[feature] = 1790.0
            elif 'EMA200' in feature:
                market_data[feature] = 1750.0  # EMA200 ต่ำกว่าราคา = uptrend
            else:
                market_data[feature] = np.random.randn()
        
        # ทดสอบการทำนาย
        test_cases = ['buy', 'sell']
        
        for action_type in test_cases:
            print(f"\n🧪 ทดสอบการทำนาย {action_type.upper()}:")
            
            result = predict_with_optimal_parameters(
                symbol, timeframe, market_data, action_type
            )
            
            if result['success']:
                print(f"   ✅ การทำนายสำเร็จ")
                print(f"   📊 Prediction: {result['prediction']}")
                print(f"   📊 Confidence: {result['confidence']:.4f}")
                print(f"   📊 Scenario: {result['scenario']}")
                print(f"   📊 Market condition: {result['market_condition']}")
                print(f"   📊 Threshold: {result['threshold']:.4f}")
                print(f"   📊 nBars_SL: {result['nBars_SL']}")
            else:
                print(f"   ❌ การทำนายล้มเหลว: {result['error']}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def step5_check_files():
    """
    Step 5: ตรวจสอบไฟล์ที่สร้างขึ้น
    """
    print("\n📁 Step 5: ตรวจสอบไฟล์ที่สร้างขึ้น")
    print("="*60)
    
    base_path = Path("LightGBM_Multi")
    
    # ตรวจสอบโฟลเดอร์และไฟล์
    folders_to_check = {
        "models/trend_following": "โมเดล Trend Following",
        "models/counter_trend": "โมเดล Counter Trend", 
        "thresholds": "ไฟล์ Threshold และ nBars_SL",
        "results": "ผลการวิเคราะห์",
        "feature_importance": "Feature Importance"
    }
    
    for folder, description in folders_to_check.items():
        folder_path = base_path / folder
        
        print(f"\n📂 {description}:")
        print(f"   Path: {folder_path}")
        
        if folder_path.exists():
            files = list(folder_path.glob("*.pkl")) + list(folder_path.glob("*.csv")) + list(folder_path.glob("*.json"))
            print(f"   ✅ พบ {len(files)} ไฟล์")
            
            # แสดงไฟล์บางส่วน
            for file in files[:5]:  # แสดง 5 ไฟล์แรก
                print(f"     - {file.name}")
            
            if len(files) > 5:
                print(f"     ... และอีก {len(files) - 5} ไฟล์")
        else:
            print(f"   ❌ ไม่พบโฟลเดอร์")
    
    # ตรวจสอบไฟล์สรุป
    summary_files = [
        "optimization_summary.json",
        "multi_scenario_performance_analysis.txt"
    ]
    
    print(f"\n📄 ไฟล์สรุป:")
    for file_name in summary_files:
        file_path = base_path / file_name
        if file_path.exists():
            print(f"   ✅ {file_name}")
        else:
            print(f"   ❌ {file_name}")

def main():
    """
    ฟังก์ชันหลัก - รันขั้นตอนทั้งหมด
    """
    print("🚀 Complete Multi-Model Architecture Workflow")
    print("="*80)
    
    # Step 1: ตรวจสอบการเทรนโมเดล
    models_ready = step1_train_multi_model()
    
    if not models_ready:
        print("\n⚠️ โมเดลยังไม่พร้อม - กรุณาเทรนโมเดลก่อน")
        print("💡 รัน: USE_MULTI_MODEL_ARCHITECTURE = True และ python python_LightGBM_16_Signal.py")
        return
    
    # Step 2: หา optimal parameters
    optimization_results = step2_optimize_parameters()
    
    if not optimization_results:
        print("\n⚠️ ไม่สามารถหา optimal parameters ได้")
        print("💡 ตรวจสอบข้อมูล validation และโมเดล")
    
    # Step 3: ทดสอบการโหลดพารามิเตอร์
    loading_success = step3_test_parameter_loading()
    
    # Step 4: ทดสอบการทำนาย
    if loading_success:
        prediction_success = step4_test_prediction()
    
    # Step 5: ตรวจสอบไฟล์
    step5_check_files()
    
    print("\n" + "="*80)
    print("✅ Complete Multi-Model Architecture Workflow เสร็จสิ้น")
    
    # สรุปผลลัพธ์
    print(f"\n📊 สรุปผลลัพธ์:")
    print(f"   Step 1 - โมเดล: {'✅' if models_ready else '❌'}")
    print(f"   Step 2 - Optimization: {'✅' if optimization_results else '❌'}")
    print(f"   Step 3 - Parameter Loading: {'✅' if loading_success else '❌'}")
    print(f"   Step 4 - Prediction: {'✅' if 'prediction_success' in locals() and prediction_success else '❌'}")
    
    if models_ready and optimization_results and loading_success:
        print(f"\n🎉 ระบบ Multi-Model Architecture พร้อมใช้งานใน Production!")
        
        print(f"\n📝 ขั้นตอนการใช้งานใน Production:")
        print(f"1. โหลดโมเดล: load_scenario_models(symbol, timeframe)")
        print(f"2. ดึงพารามิเตอร์: get_optimal_parameters(symbol, timeframe, market_condition, action_type)")
        print(f"3. ทำนาย: predict_with_optimal_parameters(symbol, timeframe, market_data, action_type)")
    else:
        print(f"\n⚠️ ระบบยังไม่พร้อม - ตรวจสอบขั้นตอนที่ล้มเหลว")

if __name__ == "__main__":
    main()

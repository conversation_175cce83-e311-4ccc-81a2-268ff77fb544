#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขปัญหา duplicate labels ใน pandas Series
"""

import pandas as pd
import numpy as np

def test_duplicate_labels_detection():
    """ทดสอบการตรวจจับและแก้ไข duplicate labels"""
    print("🔍 ทดสอบการตรวจจับ duplicate labels...")
    
    # สร้างข้อมูลทดสอบที่มี duplicate labels
    data = {
        'Close': 2650.50,
        'High': 2655.00,
        'Low': 2645.00,
        'Open': 2648.00,
        'Close': 2652.75,  # Duplicate 'Close'
        'Volume': 1000,
        'RSI14': 65.5,
        'RSI14': 67.2,     # Duplicate 'RSI14'
        'ATR': 15.2
    }
    
    # สร้าง Series ที่มี duplicate index
    row = pd.Series(data)
    
    print(f"📊 Original data:")
    print(f"   Length: {len(row)}")
    print(f"   Index: {row.index.tolist()}")
    print(f"   Has duplicates: {row.index.duplicated().any()}")
    print(f"   Duplicate indices: {row.index[row.index.duplicated()].tolist()}")
    
    # ทดสอบการแก้ไข duplicate labels
    if row.index.duplicated().any():
        print(f"\n⚠️ พบ duplicate labels ในข้อมูล, กำลังแก้ไข...")
        # ลบ duplicates โดยเก็บค่าแรก
        row_fixed = row[~row.index.duplicated(keep='first')]
        print(f"✅ แก้ไข duplicate labels แล้ว, เหลือ {len(row_fixed)} features")
        
        print(f"📊 Fixed data:")
        print(f"   Length: {len(row_fixed)}")
        print(f"   Index: {row_fixed.index.tolist()}")
        print(f"   Values: {row_fixed.values}")
        print(f"   Has duplicates: {row_fixed.index.duplicated().any()}")
        
        return row_fixed
    else:
        print("✅ ไม่มี duplicate labels")
        return row

def test_reindex_with_duplicates():
    """ทดสอบการใช้ reindex หลังจากแก้ไข duplicate labels"""
    print(f"\n🔧 ทดสอบ reindex หลังจากแก้ไข duplicate labels...")
    
    # สร้างข้อมูลที่มี duplicates
    data = {}
    for i in range(50):
        data[f'feature_{i}'] = np.random.random()
    
    # เพิ่ม duplicates
    data['Close'] = 2650.50
    data['Close'] = 2652.75  # Duplicate
    data['RSI14'] = 65.5
    data['RSI14'] = 67.2     # Duplicate
    
    row = pd.Series(data)
    
    # Features ที่โมเดลต้องการ
    required_features = [f'feature_{i}' for i in range(60)]  # ต้องการ 60 features
    missing_features = ['missing_1', 'missing_2', 'missing_3']
    features = required_features + missing_features
    
    print(f"📊 Test data:")
    print(f"   Available features: {len(row)}")
    print(f"   Required features: {len(features)}")
    print(f"   Has duplicates: {row.index.duplicated().any()}")
    
    # แก้ไข duplicate labels
    if row.index.duplicated().any():
        print(f"⚠️ พบ duplicate labels, กำลังแก้ไข...")
        row = row[~row.index.duplicated(keep='first')]
        print(f"✅ แก้ไข duplicate labels แล้ว, เหลือ {len(row)} features")
    
    # ทดสอบ reindex
    try:
        missing_features_check = [f for f in features if f not in row.index]
        if missing_features_check:
            print(f"⚠️ Missing features ({len(missing_features_check)}): {missing_features_check[:5]}...")
            
            # ใช้ reindex
            complete_row = row.reindex(features, fill_value=0.0)
            
            available_features = [f for f in features if f in row.index]
            print(f"✅ reindex สำเร็จ: ใช้ {len(available_features)} features, เติม 0 สำหรับ {len(missing_features_check)} features")
            
            # เตรียมข้อมูลสำหรับโมเดล
            X = complete_row.values.reshape(1, -1)
            print(f"✅ X shape: {X.shape}")
            
            return True
        else:
            print("✅ ไม่มี missing features")
            X = row[features].values.reshape(1, -1)
            print(f"✅ X shape: {X.shape}")
            return True
            
    except Exception as e:
        print(f"❌ reindex ล้มเหลว: {e}")
        
        # ทดสอบ fallback method
        print("🔄 ทดสอบ fallback method...")
        try:
            X = np.zeros((1, len(features)))
            available_features = [f for f in features if f in row.index]
            for i, feature in enumerate(features):
                if feature in row.index:
                    X[0, i] = row[feature]
            print(f"✅ fallback method สำเร็จ: {len(available_features)} features")
            print(f"✅ X shape: {X.shape}")
            return True
        except Exception as fallback_error:
            print(f"❌ fallback method ล้มเหลว: {fallback_error}")
            return False

def test_real_scenario_simulation():
    """จำลองสถานการณ์จริงที่อาจเกิดขึ้นใน MT5 WebRequest Server"""
    print(f"\n🎯 จำลองสถานการณ์จริงจาก MT5...")
    
    try:
        from python_LightGBM_16_Signal import predict_with_scenario_model, load_scenario_models
        
        # โหลดโมเดล
        symbol = "GOLD"
        timeframe = 60
        scenario_models = load_scenario_models(symbol, timeframe)
        
        if not scenario_models:
            print("❌ ไม่สามารถโหลดโมเดลได้")
            return False
        
        # สร้างข้อมูลที่มี duplicate labels (จำลองปัญหาจริง)
        test_data_dict = {
            'Close': 2650.50,
            'High': 2655.00,
            'Low': 2645.00,
            'Open': 2648.00,
            'Volume': 1000,
            'EMA200': 2640.00,
            'RSI14': 65.5,
            'ATR': 15.2,
            'Close': 2652.75,  # Duplicate Close (อาจเกิดจากการคำนวณ indicator หลายรอบ)
            'RSI14': 67.2,     # Duplicate RSI14
        }
        
        # เพิ่ม features อื่นๆ
        for i in range(100):
            test_data_dict[f'feature_{i}'] = np.random.random()
        
        # สร้าง Series
        test_data = pd.Series(test_data_dict)
        
        print(f"📊 Simulated MT5 data:")
        print(f"   Features: {len(test_data)}")
        print(f"   Has duplicates: {test_data.index.duplicated().any()}")
        print(f"   Duplicate count: {test_data.index.duplicated().sum()}")
        
        # ทดสอบการทำนาย
        print(f"\n🤖 ทดสอบการทำนายด้วยข้อมูลที่มี duplicates...")
        
        should_trade_buy, confidence_buy, model_used_buy = predict_with_scenario_model(
            test_data, 'buy', scenario_models, 0.5
        )
        
        print(f"✅ การทำนาย BUY สำเร็จ:")
        print(f"   Should trade: {should_trade_buy}")
        print(f"   Confidence: {confidence_buy:.4f}")
        print(f"   Model used: {type(model_used_buy) if model_used_buy else 'None'}")
        
        should_trade_sell, confidence_sell, model_used_sell = predict_with_scenario_model(
            test_data, 'sell', scenario_models, 0.5
        )
        
        print(f"✅ การทำนาย SELL สำเร็จ:")
        print(f"   Should trade: {should_trade_sell}")
        print(f"   Confidence: {confidence_sell:.4f}")
        print(f"   Model used: {type(model_used_sell) if model_used_sell else 'None'}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการจำลอง: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """ฟังก์ชันหลักสำหรับทดสอบ"""
    print("🚀 เริ่มทดสอบการแก้ไขปัญหา duplicate labels")
    print("="*80)
    
    # ทดสอบการตรวจจับ duplicate labels
    test_duplicate_labels_detection()
    
    # ทดสอบ reindex หลังจากแก้ไข duplicates
    if not test_reindex_with_duplicates():
        print("❌ การทดสอบ reindex ล้มเหลว")
        return
    
    # จำลองสถานการณ์จริง
    if not test_real_scenario_simulation():
        print("❌ การจำลองสถานการณ์จริงล้มเหลว")
        return
    
    print("\n" + "="*80)
    print("✅ การทดสอบทั้งหมดผ่านเรียบร้อย!")
    print("🎉 การแก้ไขปัญหา duplicate labels สำเร็จ")
    print("🚀 MT5 WebRequest Server พร้อมจัดการ duplicate labels ได้แล้ว")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# Simple server test to debug the issue

from flask import Flask, request, jsonify
import json
import datetime
import pandas as pd
import numpy as np
import sys
import os

# Add path for imports
python_lightgbm_folder = r'D:\test_gold'
if python_lightgbm_folder not in sys.path:
    sys.path.append(python_lightgbm_folder)

try:
    from python_LightGBM_17_Signal import (
        USE_MULTI_MODEL_ARCHITECTURE, MARKET_SCENARIOS
    )
    print(f"✅ Import successful! Multi-Model: {USE_MULTI_MODEL_ARCHITECTURE}")
except Exception as e:
    print(f"❌ Import error: {e}")
    exit()

app = Flask(__name__)

@app.route('/test', methods=['GET'])
def test():
    return jsonify({
        "status": "ok",
        "multi_model": USE_MULTI_MODEL_ARCHITECTURE,
        "scenarios": list(MARKET_SCENARIOS.keys()),
        "timestamp": datetime.datetime.now().isoformat()
    })

@app.route('/process_data', methods=['POST'])
def process_data():
    try:
        data = request.get_json()
        symbol = data.get('symbol', 'UNKNOWN')
        timeframe = data.get('timeframe', 0)
        bars = data.get('bars', [])
        
        print(f"\n🔍 Received request for {symbol} M{timeframe}")
        print(f"📊 Bars count: {len(bars)}")
        
        # Simple response for testing
        response = {
            "symbol": symbol,
            "timeframe": timeframe,
            "signal": "HOLD",
            "confidence": 0.5,
            "class": "HOLD",
            "entry_price": 0.0,
            "sl_price": 0.0,
            "tp_price": 0.0,
            "timestamp": datetime.datetime.now().isoformat(),
            "debug": {
                "multi_model": USE_MULTI_MODEL_ARCHITECTURE,
                "bars_received": len(bars),
                "scenarios": list(MARKET_SCENARIOS.keys())
            }
        }
        
        print(f"🎯 Sending response: {response['signal']} with confidence {response['confidence']}")
        return jsonify(response)
        
    except Exception as e:
        print(f"❌ Error in process_data: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    print("🚀 Starting Simple Test Server...")
    print(f"🔄 Multi-Model Architecture: {USE_MULTI_MODEL_ARCHITECTURE}")
    print(f"📁 Available scenarios: {list(MARKET_SCENARIOS.keys())}")
    
    app.run(host='127.0.0.1', port=54321, debug=False)

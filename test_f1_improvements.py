#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการปรับปรุง F1 Score
"""

import sys
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Import functions จากไฟล์หลัก
try:
    from python_LightGBM_15_Tuning import (
        get_optimal_class_weight,
        find_optimal_threshold,
        get_lgbm_params,
        param_dist
    )
    print("✅ Import functions สำเร็จ")
except ImportError as e:
    print(f"❌ Import ไม่สำเร็จ: {str(e)}")
    sys.exit(1)

def test_improved_class_weights():
    """ทดสอบ class weight ที่ปรับปรุงแล้ว"""
    print("🧪 ทดสอบ Improved Class Weights")
    print("-" * 60)
    
    test_cases = [
        ("Balanced", np.array([0, 1] * 500)),
        ("Mild Imbalance (2:1)", np.array([0] * 667 + [1] * 333)),
        ("Moderate Imbalance (4:1)", np.array([0] * 800 + [1] * 200)),
        ("Severe Imbalance (6:1)", np.array([0] * 857 + [1] * 143)),
        ("Very Severe (10:1)", np.array([0] * 909 + [1] * 91))
    ]
    
    print("Class Weight Recommendations:")
    print("-" * 60)
    
    for case_name, y_data in test_cases:
        class_weight = get_optimal_class_weight(y_data)
        class_counts = pd.Series(y_data).value_counts()
        ratio = class_counts.max() / class_counts.min()
        
        print(f"{case_name}:")
        print(f"  Actual ratio: {ratio:.1f}:1")
        print(f"  Recommended weight: {class_weight}")
        print()

def test_improved_parameters():
    """ทดสอบพารามิเตอร์ที่ปรับปรุงแล้ว"""
    print("🧪 ทดสอบ Improved Parameters")
    print("-" * 60)
    
    # ทดสอบ param_dist ใหม่
    print("📊 Updated param_dist:")
    for key, values in param_dist.items():
        print(f"  {key}: {values} ({len(values)} choices)")
    
    total_combinations = 1
    for values in param_dist.values():
        total_combinations *= len(values)
    
    print(f"\nTotal parameter combinations: {total_combinations:,}")
    
    # ทดสอบ default parameters
    print(f"\n📊 Updated default parameters:")
    y_test = np.array([0] * 800 + [1] * 200)  # 4:1 imbalance
    params = get_lgbm_params(y=y_test)
    
    key_params = ['learning_rate', 'num_leaves', 'max_depth', 'min_data_in_leaf']
    for param in key_params:
        print(f"  {param}: {params[param]}")
    
    if 'class_weight' in params:
        print(f"  class_weight: {params['class_weight']}")

def compare_old_vs_new_params():
    """เปรียบเทียบพารามิเตอร์เดิมกับใหม่"""
    print("\n📊 เปรียบเทียบพารามิเตอร์ Old vs New (F1-Focused)")
    print("-" * 80)
    
    # พารามิเตอร์เดิม (Conservative - เน้น overfitting)
    old_params = {
        'learning_rate': 0.05,
        'num_leaves': 8,
        'max_depth': 4,
        'min_data_in_leaf': 25
    }
    
    # พารามิเตอร์ใหม่ (Balanced - เน้น F1 Score)
    y_test = np.array([0] * 800 + [1] * 200)
    new_params = get_lgbm_params(y=y_test)
    
    print(f"{'Parameter':<20} {'Old (Conservative)':<20} {'New (F1-Focused)':<20} {'Change'}")
    print("-" * 80)
    
    for param in old_params.keys():
        old_val = old_params[param]
        new_val = new_params.get(param, 'N/A')
        
        if isinstance(old_val, (int, float)) and isinstance(new_val, (int, float)):
            change_pct = ((new_val - old_val) / old_val) * 100
            change = f"{change_pct:+.1f}%"
        else:
            change = "Changed" if old_val != new_val else "Same"
        
        print(f"{param:<20} {str(old_val):<20} {str(new_val):<20} {change}")
    
    print(f"\n💡 การเปลี่ยนแปลงหลัก:")
    print("  • learning_rate: เพิ่มขึ้น 60% (0.05 → 0.08) - เพื่อ F1 Score")
    print("  • num_leaves: เพิ่มขึ้น 25% (8 → 10) - เพื่อความซับซ้อนที่เหมาะสม")
    print("  • max_depth: เพิ่มขึ้น 25% (4 → 5) - เพื่อ balance")
    print("  • min_data_in_leaf: ลดลง 20% (25 → 20) - เพื่อ F1 Score")

def simulate_f1_improvement():
    """จำลองการปรับปรุง F1 Score"""
    print("\n🎯 จำลองการปรับปรุง F1 Score")
    print("-" * 60)
    
    # สร้างข้อมูลทดสอบ
    np.random.seed(42)
    n_samples = 1000
    
    # สร้าง imbalanced dataset
    y_true = np.random.choice([0, 1], size=n_samples, p=[0.8, 0.2])  # 4:1 imbalance
    
    # จำลอง probability predictions
    y_proba_old = np.random.beta(2, 8, size=n_samples)  # Conservative model
    y_proba_new = np.random.beta(3, 7, size=n_samples)  # Improved model
    
    # ทดสอบ threshold optimization
    print("F1 Score Comparison:")
    print("-" * 40)
    
    # Default threshold (0.5)
    from sklearn.metrics import f1_score
    
    y_pred_old_default = (y_proba_old >= 0.5).astype(int)
    y_pred_new_default = (y_proba_new >= 0.5).astype(int)
    
    f1_old_default = f1_score(y_true, y_pred_old_default, zero_division=0)
    f1_new_default = f1_score(y_true, y_pred_new_default, zero_division=0)
    
    print(f"Default threshold (0.5):")
    print(f"  Old model F1: {f1_old_default:.3f}")
    print(f"  New model F1: {f1_new_default:.3f}")
    print(f"  Improvement: {((f1_new_default - f1_old_default) / f1_old_default) * 100:+.1f}%")
    
    # Optimal threshold
    threshold_old, f1_old_optimal = find_optimal_threshold(y_true, y_proba_old, 'f1')
    threshold_new, f1_new_optimal = find_optimal_threshold(y_true, y_proba_new, 'f1')
    
    print(f"\nOptimal threshold:")
    print(f"  Old model: threshold={threshold_old:.3f}, F1={f1_old_optimal:.3f}")
    print(f"  New model: threshold={threshold_new:.3f}, F1={f1_new_optimal:.3f}")
    print(f"  Improvement: {((f1_new_optimal - f1_old_optimal) / f1_old_optimal) * 100:+.1f}%")

def generate_testing_recommendations():
    """สร้างคำแนะนำการทดสอบ"""
    print("\n🚀 คำแนะนำการทดสอบ")
    print("-" * 60)
    
    recommendations = [
        {
            "step": 1,
            "action": "ทดสอบ Quick Tuning ใหม่",
            "command": "python quick_tuning_test.py",
            "expected": "F1 Score ดีขึ้น, AUC คงที่หรือดีขึ้น",
            "time": "2-3 นาที"
        },
        {
            "step": 2,
            "action": "ทดสอบ 1 symbol ที่มีปัญหา F1 Score",
            "command": "รัน hyperparameter tuning สำหรับ USDJPY M30",
            "expected": "F1 Score > 0.6, AUC > 0.85",
            "time": "15-20 นาที"
        },
        {
            "step": 3,
            "action": "เปรียบเทียบผลลัพธ์",
            "command": "เปรียบเทียบ metrics กับรอบก่อน",
            "expected": "F1 Score ดีขึ้น, Overfitting ยังคงต่ำ",
            "time": "5 นาที"
        },
        {
            "step": 4,
            "action": "รัน Full Training (ถ้าผลดี)",
            "command": "python python_LightGBM_15_Tuning.py",
            "expected": "F1 Score เฉลี่ย > 0.65, AUC > 0.88",
            "time": "3-4 ชั่วโมง"
        }
    ]
    
    print("📋 Testing Plan:")
    for rec in recommendations:
        print(f"{rec['step']}. {rec['action']}")
        print(f"   💻 {rec['command']}")
        print(f"   🎯 คาดหวัง: {rec['expected']}")
        print(f"   ⏱️ เวลา: {rec['time']}")
        print()

def main():
    """ฟังก์ชันหลัก"""
    print("🎯 ทดสอบการปรับปรุง F1 Score")
    print("=" * 80)
    
    # 1. ทดสอบ class weights ใหม่
    test_improved_class_weights()
    
    # 2. ทดสอบพารามิเตอร์ใหม่
    test_improved_parameters()
    
    # 3. เปรียบเทียบพารามิเตอร์
    compare_old_vs_new_params()
    
    # 4. จำลองการปรับปรุง F1
    simulate_f1_improvement()
    
    # 5. คำแนะนำการทดสอบ
    generate_testing_recommendations()
    
    # 6. สรุป
    print("🎉 สรุปการปรับปรุง F1 Score")
    print("=" * 80)
    
    print("✅ การปรับปรุงที่ทำ:")
    print("  • Class weight ที่แข็งแกร่งขึ้น (เพิ่มน้ำหนัก minority class)")
    print("  • Parameter balance ระหว่าง overfitting และ F1 Score")
    print("  • ขยาย param_dist เพื่อหาค่าที่เหมาะสมกับ F1")
    print("  • Default parameters ที่เน้น F1 Score มากขึ้น")
    
    print(f"\n🎯 คาดหวังผลลัพธ์:")
    print("  • F1 Score: 0.579 → 0.65+ (+12%)")
    print("  • AUC: คงที่หรือดีขึ้นเล็กน้อย")
    print("  • Overfitting Gap: ยังคงต่ำ (< 0.05)")
    print("  • Parameter Stability: ดีขึ้น")
    
    print(f"\n🚀 ขั้นตอนถัดไป:")
    print("1. รัน quick_tuning_test.py เพื่อยืนยันการปรับปรุง")
    print("2. ทดสอบกับ 1-2 symbols ที่มีปัญหา F1 Score")
    print("3. รัน full training ใหม่ (ถ้าผลดี)")

if __name__ == "__main__":
    main()

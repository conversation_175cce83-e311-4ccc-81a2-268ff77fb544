import os
import pickle

folder = r"D:\test_gold\Test_LightGBM\thresholds"
files = os.listdir(folder)

# แยกกลุ่ม
time_filters_30 = []
time_filters_60 = []

for f in files:
    if f.endswith("time_filters.pkl"):
        if "_30_" in f:
            time_filters_30.append(f)
        elif "_60_" in f:
            time_filters_60.append(f)

# for f in files:
#     if f.endswith("optimal_threshold.pkl"):
#         if "_30_" in f:
#             time_filters_30.append(f)
#         elif "_60_" in f:
#             time_filters_60.append(f)

def show_pkl_content(file_list, folder, title):
    print(f"\n=== {title} ===")
    for f in sorted(file_list):
        print(f"\n{f}:")
        try:
            with open(os.path.join(folder, f), "rb") as pf:
                content = pickle.load(pf)
            print(content)
        except Exception as e:
            print(f"⚠️ Error reading {f}: {e}")

show_pkl_content(time_filters_30, folder, "time_filters 30")
show_pkl_content(time_filters_60, folder, "time_filters 60")


# แสดง 1 ไฟล์ที่ต้องการ
# file_path = r"D:\test_gold\Test_LightGBM\feature_importance\060_must_have_features.pkl"
# with open(file_path, "rb") as pf:
#     content = pickle.load(pf)
# print(content)

# แสดง 2 ไฟล์ที่ต้องการ
# file_paths = [
#     r"D:\test_gold\Test_LightGBM\feature_importance\030_must_have_features.pkl",
#     r"D:\test_gold\Test_LightGBM\feature_importance\060_must_have_features.pkl"
# ]
# for file_path in file_paths:
#     print(f"\n=== {file_path} ===")
#     with open(file_path, "rb") as pf:
#         content = pickle.load(pf)
#     print(content)
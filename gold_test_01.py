import pandas as pd
import numpy as np

import pandas_ta as ta

# print ภาษาไทย
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding="utf-8")

col_name = ["Date","Time","Open","High","Low","Close","Volume"]
df = pd.read_csv("GOLDm#60.csv",names=col_name)

# สร้าง index = DateTime
df["DateTime"] = pd.to_datetime(df["Date"]+" "+df["Time"])
df = df.set_index("DateTime")

# ลบคอลัมน์ Date และ Time
df = df.drop(columns=["Date", "Time"])

# df.set_index("DateTime") และจากนั้นพยายามเข้าถึง df["DateTime"] ซึ่งไม่อยู่ใน columns แล้วครับ เพราะตอนนี้ DateTime กลายเป็น index ไม่ใช่ column แล้ว
# df['DayOfWeek'] = df['DateTime'].dt.DayOfWeek  # 0=Monday, 6=Sunday
# df['Hour'] = df['DateTime'].dt.Hour
df['DayOfWeek'] = df.index.dayofweek  # 0=Monday, 6=Sunday
df['Hour'] = df.index.hour

df['IsMorning'] = ((df['Hour'] >= 8) & (df['Hour'] < 12)).astype(int)
df['IsAfternoon'] = ((df['Hour'] >= 12) & (df['Hour'] < 16)).astype(int)
df['IsEvening'] = ((df['Hour'] >= 16) & (df['Hour'] < 20)).astype(int)
df['IsNight'] = ((df['Hour'] >= 20) | (df['Hour'] < 4)).astype(int)

# สร้างคอลัมน์ Bar_CL
df["Bar_CL"] = 0.0  # ตั้งค่าเริ่มต้นเป็น 0.0
# if (df['Close'] > df['Open']): df["Bar_CL"] = 1.0
# elif (df['Close'] < df['Open']): df["Bar_CL"] = -1.0
# การใช้ if...elif กับทั้ง Series ทำให้เกิด ValueError 
# เพราะ if กับ elif ใช้กับค่าเดียว ไม่สามารถเปรียบเทียบ Series กับ Series ได้โดยตรง
# สำหรับ Pandas ต้องใช้ การกรองด้วย Boolean indexing หรือใช้ np.where() หรือ .loc[] แทนครับ

# df["Bar_CL"] = np.where(df['Close'] > df['Open'], 1.0, 
#                             np.where(df['Close'] < df['Open'], -1.0, 0.0))

df.loc[df['Close'] > df['Open'], "Bar_CL"] = 1.0
df.loc[df['Close'] < df['Open'], "Bar_CL"] = -1.0

df["Bar_CL_OC"] = 0.0
df.loc[df['Close'] > np.maximum(df['Open'].shift(1), df['Close'].shift(1)), "Bar_CL_OC"] = 1.0
df.loc[df['Close'] < np.minimum(df['Open'].shift(1), df['Close'].shift(1)), "Bar_CL_OC"] = -1.0

df["Bar_CL_HL"] = 0.0
df.loc[(df['Close'] > df['High'].shift(1)) & (df['Close'] > df['Open']), "Bar_CL_HL"] = 1.0
df.loc[(df['Close'] < df['Low'].shift(1)) & (df['Close'] < df['Open']), "Bar_CL_HL"] = -1.0

df["Bar_SW"] = 0.0
df.loc[(df['Close'] > df['Open']) & (df['High'] > df['High'].shift(1)) & (df['Low'] > df['Low'].shift(1)), "Bar_SW"] = 1.0
df.loc[(df['Close'] < df['Open']) & (df['High'] < df['High'].shift(1)) & (df['Low'] < df['Low'].shift(1)), "Bar_SW"] = -1.0

df["Bar_TL"] = 0.0
df.loc[(df['Close'] > df['Open']) & (df['High'] < df['High'].shift(1)) & (df['Low'] < df['Low'].shift(1)), "Bar_TL"] = 1.0
df.loc[(df['Close'] < df['Open']) & (df['High'] > df['High'].shift(1)) & (df['Low'] > df['Low'].shift(1)), "Bar_TL"] = 1.0
# print(df[df["Bar_TL"]==1.0]) # ต้องการแสดงแบบเฉพาะเจาะจง

df["Bar_DTB"] = 0.0
df.loc[(df['Close'] > df['Open']) & (df['Low'] == df['Low'].shift(1)), "Bar_DTB"] = 1.0
df.loc[(df['Close'] < df['Open']) & (df['High'] == df['High'].shift(1)), "Bar_DTB"] = -1.0
# print(df[df["Bar_DTB"]==1.0])

df["Bar_OSB"] = 0.0
df.loc[(df['Close'] > df['Open']) & (df['High'] > df['High'].shift(1)) & (df['Low'] < df['Low'].shift(1)), "Bar_OSB"] = 1.0
df.loc[(df['Close'] < df['Open']) & (df['High'] > df['High'].shift(1)) & (df['Low'] < df['Low'].shift(1)), "Bar_OSB"] = -1.0

df["Bar_FVG"] = 0.0
df.loc[(df["Low"] > df["High"].shift(2)) & (df["Close"] > df["Open"]), "Bar_FVG"] = 1.0
df.loc[(df["High"] < df["Low"].shift(2)) & (df["Close"] < df["Open"]), "Bar_FVG"] = -1.0

df["Bar_longwick"] = 0.0
epsilon = 1e-9  # ค่าเล็กๆ เพื่อป้องกันหารด้วยศูนย์
# ตัวหารสำหรับ pinbar_up และ pinbar_down
lower_wick = (np.minimum(df['Open'], df['Close']) - df['Low']).replace(0, epsilon)
upper_wick = (df['High'] - np.maximum(df['Open'], df['Close'])).replace(0, epsilon)
# คำนวณ pinbar ratios แบบปลอดภัย
pinbar_up = (df['High'] - np.minimum(df['Open'], df['Close'])) / lower_wick
pinbar_down = (np.maximum(df['Open'], df['Close']) - df['Low']) / upper_wick
# df["Bar_longwick"] = np.where(pinbar_up > pinbar_down, pinbar_up,
#                                   np.where(pinbar_down > pinbar_up, -1 * pinbar_down, 0.0))
df.loc[pinbar_up > pinbar_down, "Bar_longwick"] = pinbar_up
df.loc[pinbar_down > pinbar_up, "Bar_longwick"] = -1 * pinbar_down

df['Price_Range'] = df["High"] - df["Low"]

df['Price_Strangth'] = 0.0
body_size_oc = np.abs(df["Close"]-df["Open"])
body_size_ocp = np.abs(df["Close"].shift(1)-df["Open"].shift(1))
body_size_hl = np.abs(df["High"]-df["Low"])
body_size_hlp = np.abs(df["High"].shift(1)-df["Low"].shift(1))
df.loc[(df["Close"] < df["Open"]) & (body_size_oc < body_size_ocp) & (body_size_hl < body_size_hlp), "Price_Strangth"] = 1
df.loc[(df["Close"] > df["Open"]) & (body_size_oc < body_size_ocp) & (body_size_hl < body_size_hlp), "Price_Strangth"] = -1

# -------- MACD --------
macd = ta.macd(df["Close"])
df = pd.concat([df, macd], axis=1)
# MACD_12_26_9 — ค่า MACD line
# MACDh_12_26_9 — ค่า histogram (MACD - Signal)
# MACDs_12_26_9 — ค่า Signal line

# ตัวอย่างสัญญาณจาก MACD crossover

df["MACD_line"] = 0.0
df.loc[df["MACD_12_26_9"] > 0.0, "MACD_line"] = 1
df.loc[df["MACD_12_26_9"] < 0.0, "MACD_line"] = -1

df["MACD_deep"] = 0.0
df.loc[df["MACD_12_26_9"] > df["MACD_12_26_9"].shift(1), "MACD_deep"] = 1
df.loc[df["MACD_12_26_9"] < df["MACD_12_26_9"].shift(1), "MACD_deep"] = -1

df["MACD_signal"] = 0.0
df.loc[df["MACD_12_26_9"] > df["MACDs_12_26_9"], "MACD_signal"] = 1
df.loc[df["MACD_12_26_9"] < df["MACDs_12_26_9"], "MACD_signal"] = -1

# print(df[['MACD_12_26_9', 'MACDh_12_26_9', 'MACDs_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal']].head(50))

# -------- Stochastic Oscillator --------
stoch = ta.stoch(high=df["High"], low=df["Low"], close=df["Close"])
df = pd.concat([df, stoch], axis=1)
# STOCHk_14_3_3 — ค่า %K
# STOCHd_14_3_3 — ค่า %D

df["STO_cross"] = 0.0
df.loc[df["STOCHk_14_3_3"] > df["STOCHd_14_3_3"], "STO_cross"] = 1
df.loc[df["STOCHk_14_3_3"] < df["STOCHd_14_3_3"], "STO_cross"] = -1

df["STO_zone"] = 0.0
df.loc[df["STOCHk_14_3_3"] > 50, "STO_zone"] = 1
df.loc[df["STOCHk_14_3_3"] < 50, "STO_zone"] = -1

df["STO_overbought"] = 0.0
df['STO_overbought'] = (df['STOCHk_14_3_3'] > 80).astype(int)

df["STO_Oversold"] = 0.0
df['STO_Oversold'] = (df['STOCHk_14_3_3'] < 20).astype(int)

# print(df[['STOCHk_14_3_3', 'STOCHd_14_3_3', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold']].head(50))

# -------- ADX --------
adx = ta.adx(high=df["High"], low=df["Low"], close=df["Close"])
df = pd.concat([df, adx], axis=1)
# ADX_14 — ค่าความแข็งแรงของแนวโน้ม
# DMP_14 — +DI
# DMN_14 — -DI

df["ADX_zone"] = df['ADX_14']

df["ADX_cross"] = 0.0
df.loc[df['DMP_14'] > df['DMN_14'], "ADX_cross"] = 1
df.loc[df['DMN_14'] > df['DMP_14'], "ADX_cross"] = -1

# print(df[['ADX_14', 'DMP_14', 'DMN_14', 'ADX_zone', 'ADX_cross']].head(50))

# print(df.info())
# print(df)

# จัดการ NaN
print(df)

print(df.isnull().sum())
df = df.dropna()
print(df.isnull().sum())

print(df)


for i in range(10):
  print(i)
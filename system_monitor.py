#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import json
import requests
import pandas as pd
from datetime import datetime, timedelta
import threading
import logging

class TradingSystemMonitor:
    def __init__(self):
        self.server_url = "http://127.0.0.1:54321/data"
        self.log_file = f"system_monitor_{datetime.now().strftime('%Y%m%d')}.txt"
        self.status_file = "system_status.json"
        self.running = True
        
        # สถิติการทำงาน
        self.stats = {
            "start_time": datetime.now().isoformat(),
            "server_requests": 0,
            "server_responses": 0,
            "server_errors": 0,
            "signals_generated": {"BUY": 0, "SELL": 0, "HOLD": 0, "ERROR": 0},
            "last_signal_time": None,
            "last_server_check": None,
            "uptime_hours": 0
        }
        
        self.setup_logging()
    
    def setup_logging(self):
        """ตั้งค่า logging system"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def log_event(self, event_type, message, details=None):
        """บันทึก event ลงไฟล์"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = {
            "timestamp": timestamp,
            "type": event_type,
            "message": message,
            "details": details or {}
        }
        
        # เขียนลงไฟล์ log
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(f"{json.dumps(log_entry, ensure_ascii=False)}\n")
        
        # แสดงใน console
        self.logger.info(f"[{event_type}] {message}")
    
    def check_server_status(self):
        """ตรวจสอบสถานะ server"""
        try:
            # สร้างข้อมูลทดสอบ
            test_data = {
                "symbol": "GOLD",
                "timeframe_str": "PERIOD_M30",
                "bars": [
                    {
                        "time": int(time.time()) - 3600,
                        "open": 2650.0,
                        "high": 2655.0,
                        "low": 2645.0,
                        "close": 2652.0,
                        "volume": 1000,
                        "tick_volume": 1000,
                        "spread": 5,
                        "real_volume": 1000
                    }
                ] * 210  # ส่ง 210 แท่ง
            }
            
            self.stats["server_requests"] += 1
            response = requests.post(self.server_url, json=test_data, timeout=30)
            
            if response.status_code == 200:
                self.stats["server_responses"] += 1
                result = response.json()
                signal = result.get('signal', 'UNKNOWN')
                confidence = result.get('confidence', 0.0)
                
                # นับสัญญาณ
                if signal in self.stats["signals_generated"]:
                    self.stats["signals_generated"][signal] += 1
                
                self.stats["last_signal_time"] = datetime.now().isoformat()
                self.stats["last_server_check"] = datetime.now().isoformat()
                
                self.log_event("SERVER_CHECK", "Server responding normally", {
                    "signal": signal,
                    "confidence": confidence,
                    "response_time": response.elapsed.total_seconds()
                })
                
                return True, signal, confidence
            else:
                self.stats["server_errors"] += 1
                self.log_event("SERVER_ERROR", f"HTTP {response.status_code}", {
                    "response": response.text[:200]
                })
                return False, "ERROR", 0.0
                
        except Exception as e:
            self.stats["server_errors"] += 1
            self.log_event("SERVER_ERROR", f"Connection failed: {str(e)}")
            return False, "ERROR", 0.0
    
    def check_mt5_logs(self):
        """ตรวจสอบ log files จาก MT5"""
        try:
            # ค้นหาไฟล์ log ของ MT5
            mt5_log_pattern = "MT5_Trading_Log_*.txt"
            log_files = []
            
            # ค้นหาในโฟลเดอร์ปัจจุบัน
            for file in os.listdir('.'):
                if file.startswith('MT5_Trading_Log_') and file.endswith('.txt'):
                    log_files.append(file)
            
            if log_files:
                # อ่านไฟล์ล่าสุด
                latest_file = max(log_files, key=os.path.getctime)
                
                try:
                    with open(latest_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    # วิเคราะห์ log ล่าสุด
                    recent_lines = lines[-10:] if len(lines) > 10 else lines
                    
                    trade_count = 0
                    server_requests = 0
                    errors = 0
                    
                    for line in recent_lines:
                        if 'TRADE_' in line:
                            trade_count += 1
                        elif 'SERVER_' in line:
                            server_requests += 1
                        elif 'ERROR' in line:
                            errors += 1
                    
                    self.log_event("MT5_LOG_CHECK", f"Analyzed {len(recent_lines)} recent log entries", {
                        "file": latest_file,
                        "trades": trade_count,
                        "server_requests": server_requests,
                        "errors": errors
                    })
                    
                    return True
                    
                except Exception as e:
                    self.log_event("MT5_LOG_ERROR", f"Error reading MT5 log: {str(e)}")
                    return False
            else:
                self.log_event("MT5_LOG_WARNING", "No MT5 log files found")
                return False
                
        except Exception as e:
            self.log_event("MT5_LOG_ERROR", f"Error checking MT5 logs: {str(e)}")
            return False
    
    def generate_status_report(self):
        """สร้างรายงานสถานะ"""
        # คำนวณ uptime
        start_time = datetime.fromisoformat(self.stats["start_time"])
        uptime = datetime.now() - start_time
        self.stats["uptime_hours"] = round(uptime.total_seconds() / 3600, 2)
        
        # สร้างรายงาน
        report = {
            "timestamp": datetime.now().isoformat(),
            "system_status": "RUNNING" if self.running else "STOPPED",
            "statistics": self.stats.copy(),
            "health_check": {
                "server_health": "OK" if self.stats["server_errors"] < 5 else "WARNING",
                "signal_diversity": len([k for k, v in self.stats["signals_generated"].items() if v > 0]),
                "error_rate": round(self.stats["server_errors"] / max(self.stats["server_requests"], 1) * 100, 2)
            }
        }
        
        # บันทึกลงไฟล์
        with open(self.status_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report
    
    def run_monitoring(self, check_interval=300):  # ตรวจสอบทุก 5 นาที
        """เริ่มการตรวจสอบระบบ"""
        self.log_event("MONITOR_START", "System monitoring started", {
            "check_interval": check_interval,
            "server_url": self.server_url
        })
        
        while self.running:
            try:
                # ตรวจสอบ server
                server_ok, signal, confidence = self.check_server_status()
                
                # ตรวจสอบ MT5 logs
                mt5_ok = self.check_mt5_logs()
                
                # สร้างรายงานสถานะ
                report = self.generate_status_report()
                
                # แสดงสรุป
                print(f"\n{'='*50}")
                print(f"📊 System Status Report - {datetime.now().strftime('%H:%M:%S')}")
                print(f"{'='*50}")
                print(f"🖥️  Server: {'✅ OK' if server_ok else '❌ ERROR'}")
                print(f"📈 MT5 Logs: {'✅ OK' if mt5_ok else '⚠️  WARNING'}")
                print(f"⏱️  Uptime: {self.stats['uptime_hours']} hours")
                print(f"📡 Requests: {self.stats['server_requests']} (Errors: {self.stats['server_errors']})")
                print(f"🎯 Signals: BUY:{self.stats['signals_generated']['BUY']} SELL:{self.stats['signals_generated']['SELL']} HOLD:{self.stats['signals_generated']['HOLD']}")
                print(f"📊 Last Signal: {signal} (Confidence: {confidence:.4f})")
                
                # รอก่อนตรวจสอบครั้งต่อไป
                time.sleep(check_interval)
                
            except KeyboardInterrupt:
                self.log_event("MONITOR_STOP", "Monitoring stopped by user")
                self.running = False
                break
            except Exception as e:
                self.log_event("MONITOR_ERROR", f"Monitoring error: {str(e)}")
                time.sleep(60)  # รอ 1 นาทีก่อนลองใหม่

if __name__ == "__main__":
    monitor = TradingSystemMonitor()
    
    print("🚀 Starting Trading System Monitor...")
    print("📝 Logs will be saved to:", monitor.log_file)
    print("📊 Status will be saved to:", monitor.status_file)
    print("⏹️  Press Ctrl+C to stop monitoring")
    
    monitor.run_monitoring(check_interval=300)  # ตรวจสอบทุก 5 นาที

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ตรวจสอบการปรับปรุงพารามิเตอร์ตามผลการวิเคราะห์ stability
"""

import re
import json

def extract_param_dist():
    """แยกค่า param_dist จากไฟล์หลัก"""
    print("🔍 ตรวจสอบ param_dist ที่อัปเดตแล้ว")
    print("="*60)
    
    try:
        with open("python_LightGBM_15_Tuning.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # หา param_dist block
        param_dist_pattern = r"param_dist\s*=\s*\{(.*?)\}"
        match = re.search(param_dist_pattern, content, re.DOTALL)
        
        if match:
            param_block = match.group(1)
            
            # แยกพารามิเตอร์แต่ละตัว
            params = {}
            
            # Learning rate
            lr_match = re.search(r"'learning_rate':\s*\[(.*?)\]", param_block)
            if lr_match:
                lr_values = [float(x.strip()) for x in lr_match.group(1).split(',')]
                params['learning_rate'] = lr_values
            
            # Num leaves
            nl_match = re.search(r"'num_leaves':\s*\[(.*?)\]", param_block)
            if nl_match:
                nl_values = [int(x.strip()) for x in nl_match.group(1).split(',')]
                params['num_leaves'] = nl_values
            
            # Min data in leaf
            mdl_match = re.search(r"'min_data_in_leaf':\s*\[(.*?)\]", param_block)
            if mdl_match:
                mdl_values = [int(x.strip()) for x in mdl_match.group(1).split(',')]
                params['min_data_in_leaf'] = mdl_values
            
            return params
            
    except Exception as e:
        print(f"❌ ไม่สามารถอ่านไฟล์ได้: {e}")
        return {}

def extract_default_params():
    """แยกค่า default parameters"""
    print("\n🔍 ตรวจสอบ default parameters ที่อัปเดตแล้ว")
    print("="*60)
    
    try:
        with open("python_LightGBM_15_Tuning.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        defaults = {}
        
        # หา default parameters ใน lgb_params
        lgb_params_pattern = r"lgb_params\s*=\s*\{(.*?)\}"
        match = re.search(lgb_params_pattern, content, re.DOTALL)
        
        if match:
            params_block = match.group(1)
            
            # Learning rate
            lr_match = re.search(r"'learning_rate':\s*([\d.]+)", params_block)
            if lr_match:
                defaults['learning_rate'] = float(lr_match.group(1))
            
            # Num leaves
            nl_match = re.search(r"'num_leaves':\s*(\d+)", params_block)
            if nl_match:
                defaults['num_leaves'] = int(nl_match.group(1))
            
            # Min data in leaf
            mdl_match = re.search(r"'min_data_in_leaf':\s*(\d+)", params_block)
            if mdl_match:
                defaults['min_data_in_leaf'] = int(mdl_match.group(1))
        
        return defaults
        
    except Exception as e:
        print(f"❌ ไม่สามารถอ่านไฟล์ได้: {e}")
        return {}

def analyze_stability_alignment():
    """วิเคราะห์ความสอดคล้องกับผลการวิเคราะห์ stability"""
    print("\n📊 วิเคราะห์ความสอดคล้องกับผลการวิเคราะห์ stability")
    print("="*80)
    
    # ข้อมูลจากการวิเคราะห์ stability
    stability_results = {
        'learning_rate': {'mean': 0.1388, 'std': 0.0150, 'cv': 10.8},
        'num_leaves': {'mean': 13.0, 'std': 4.0, 'cv': 30.8},
        'min_data_in_leaf': {'mean': 11.875, 'std': 2.5, 'cv': 21.1}
    }
    
    # ดึงค่าปัจจุบัน
    param_dist = extract_param_dist()
    defaults = extract_default_params()
    
    print("📋 เปรียบเทียบกับผลการวิเคราะห์:")
    print("-"*80)
    
    for param_name, stability_data in stability_results.items():
        mean_val = stability_data['mean']
        std_val = stability_data['std']
        cv_val = stability_data['cv']
        
        print(f"\n🔧 {param_name}:")
        print(f"  📊 Stability: Mean={mean_val:.3f}, Std={std_val:.3f}, CV={cv_val:.1f}%")
        
        # ตรวจสอบ default
        if param_name in defaults:
            default_val = defaults[param_name]
            distance_from_mean = abs(default_val - mean_val)
            within_1std = distance_from_mean <= std_val
            
            print(f"  🎯 Default: {default_val} ", end="")
            if within_1std:
                print("✅ (อยู่ในช่วง ±1 std)")
            else:
                print(f"⚠️ (ห่างจาก mean {distance_from_mean:.3f})")
        
        # ตรวจสอบ param_dist
        if param_name in param_dist:
            dist_values = param_dist[param_name]
            print(f"  🔍 Param_dist: {dist_values}")
            
            # ตรวจสอบว่าครอบคลุม mean ±1std หรือไม่
            min_range = mean_val - std_val
            max_range = mean_val + std_val
            
            covers_mean = any(min_range <= val <= max_range for val in dist_values)
            includes_mean = any(abs(val - mean_val) < 0.01 for val in dist_values)
            
            if covers_mean:
                print(f"  ✅ ครอบคลุมช่วง optimal ({min_range:.3f} - {max_range:.3f})")
            else:
                print(f"  ⚠️ ไม่ครอบคลุมช่วง optimal ({min_range:.3f} - {max_range:.3f})")
            
            if includes_mean:
                print(f"  ✅ รวมค่าเฉลี่ย ({mean_val:.3f})")
            else:
                print(f"  💡 แนะนำเพิ่มค่าเฉลี่ย ({mean_val:.3f})")

def calculate_search_space():
    """คำนวณขนาด search space"""
    print(f"\n🔢 คำนวณขนาด Search Space")
    print("="*60)
    
    param_dist = extract_param_dist()
    
    total_combinations = 1
    print("📊 จำนวนตัวเลือกแต่ละพารามิเตอร์:")
    
    for param_name, values in param_dist.items():
        count = len(values)
        total_combinations *= count
        print(f"  {param_name}: {count} ตัวเลือก")
    
    print(f"\n🎯 Total combinations: {total_combinations:,}")
    print(f"⏰ ประเมินเวลา (n_iter=100): ~{total_combinations/100*5:.1f} นาที/model")
    
    # เปรียบเทียบกับเดิม
    old_combinations = 6 * 6 * 5  # เดิมมี 6, 6, 5 ตัวเลือก
    reduction = (old_combinations - total_combinations) / old_combinations * 100
    
    print(f"📉 ลดลงจากเดิม: {old_combinations} → {total_combinations} (-{reduction:.1f}%)")
    print(f"⚡ ประหยัดเวลา: ~{(old_combinations-total_combinations)/100*5:.1f} นาที/model")

def main():
    """ฟังก์ชันหลัก"""
    print("🚀 ตรวจสอบการปรับปรุงพารามิเตอร์ตาม Stability Analysis")
    print("="*80)
    
    # 1. ตรวจสอบ param_dist
    param_dist = extract_param_dist()
    if param_dist:
        print("✅ พบ param_dist:")
        for param, values in param_dist.items():
            print(f"  {param}: {values}")
    else:
        print("❌ ไม่พบ param_dist")
    
    # 2. ตรวจสอบ defaults
    defaults = extract_default_params()
    if defaults:
        print("\n✅ พบ default parameters:")
        for param, value in defaults.items():
            print(f"  {param}: {value}")
    else:
        print("\n❌ ไม่พบ default parameters")
    
    # 3. วิเคราะห์ความสอดคล้อง
    analyze_stability_alignment()
    
    # 4. คำนวณ search space
    calculate_search_space()
    
    # 5. สรุปและแนะนำ
    print(f"\n💡 สรุปและแนะนำ")
    print("="*60)
    print("✅ การปรับปรุงที่ทำ:")
    print("  1. ลด learning_rate range เป็น [0.119, 0.139, 0.159]")
    print("  2. ปรับ num_leaves เป็น [10, 13, 15, 18]")
    print("  3. ลด min_data_in_leaf เป็น [10, 12, 15]")
    print("  4. อัปเดต defaults ตามค่าเฉลี่ยจากการวิเคราะห์")
    
    print(f"\n🎯 ประโยชน์:")
    print("  ⚡ ลดเวลาการ tuning")
    print("  🎯 เน้นช่วงที่มีแนวโน้มให้ผลดี")
    print("  📊 ใช้ข้อมูลจากการวิเคราะห์ 16 models")
    
    print(f"\n🚀 ขั้นตอนถัดไป:")
    print("  1. รัน: python python_LightGBM_15_Tuning.py")
    print("  2. ตรวจสอบผลลัพธ์ใหม่")
    print("  3. เปรียบเทียบกับผลเดิม")

if __name__ == "__main__":
    main()

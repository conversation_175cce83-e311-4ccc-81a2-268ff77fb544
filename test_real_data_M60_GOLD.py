#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบระบบ Multi-Model Architecture กับข้อมูลจริง
M60 + GOLD_H1_FIXED.csv
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_real_data_multi_model():
    """ทดสอบ train_all_scenario_models() กับข้อมูลจริง"""
    print("\n🚀 ทดสอบ Multi-Model Architecture กับข้อมูลจริง")
    print("="*70)
    
    try:
        # Import ฟังก์ชันที่ต้องการ
        from python_LightGBM_16_Signal import (
            train_all_scenario_models,
            MARKET_SCENARIOS,
            USE_MULTICLASS_TARGET,
            Steps_to_do
        )
        
        print(f"✅ Import สำเร็จ")
        print(f"📊 MARKET_SCENARIOS: {list(MARKET_SCENARIOS.keys())}")
        print(f"📊 USE_MULTICLASS_TARGET: {USE_MULTICLASS_TARGET}")
        print(f"📊 Steps_to_do: {Steps_to_do}")
        
        # ตรวจสอบไฟล์ข้อมูล
        data_file = "CSV_Files_Fixed/GOLD_H1_FIXED.csv"
        if not os.path.exists(data_file):
            print(f"❌ ไม่พบไฟล์ข้อมูล: {data_file}")
            return None
        
        print(f"✅ พบไฟล์ข้อมูล: {data_file}")
        
        # อ่านข้อมูล
        print(f"📖 กำลังอ่านข้อมูล...")
        df = pd.read_csv(data_file, sep=',', skiprows=1)  # ใช้ comma separator และข้าม template row

        print(f"📊 ข้อมูลที่อ่านได้:")
        print(f"   - จำนวนแถว: {len(df):,}")
        print(f"   - จำนวนคอลัมน์: {len(df.columns)}")
        print(f"   - คอลัมน์: {list(df.columns)}")

        # แปลงชื่อคอลัมน์ให้เป็นมาตรฐาน
        column_mapping = {
            '<DATE>': 'Date',
            '<TIME>': 'Time',
            '<OPEN>': 'Open',
            '<HIGH>': 'High',
            '<LOW>': 'Low',
            '<CLOSE>': 'Close',
            '<TICKVOL>': 'TickVol',
            '<VOL>': 'Volume',
            '<SPREAD>': 'Spread'
        }

        df = df.rename(columns=column_mapping)
        print(f"✅ แปลงชื่อคอลัมน์เรียบร้อย: {list(df.columns)}")

        # สร้างคอลัมน์ที่จำเป็นเพิ่มเติม
        print(f"🔧 สร้างคอลัมน์เพิ่มเติม...")

        # สร้าง EMA200
        df['EMA200'] = df['Close'].ewm(span=200).mean()

        # สร้าง Technical Indicators
        df['RSI_14'] = np.random.uniform(20, 80, len(df))  # จำลอง RSI
        df['MACD'] = np.random.uniform(-5, 5, len(df))     # จำลอง MACD
        df['MACD_Signal'] = df['MACD'] * 0.8               # จำลอง MACD Signal
        df['ATR_14'] = np.random.uniform(1, 10, len(df))   # จำลอง ATR
        df['BB_Upper'] = df['Close'] * 1.02                # จำลอง Bollinger Upper
        df['BB_Lower'] = df['Close'] * 0.98                # จำลอง Bollinger Lower
        df['Stoch_K'] = np.random.uniform(0, 100, len(df)) # จำลอง Stochastic K
        df['Stoch_D'] = df['Stoch_K'] * 0.9                # จำลอง Stochastic D

        # สร้าง Target สำหรับการทดสอบ
        np.random.seed(42)  # เพื่อให้ผลลัพธ์เหมือนเดิม

        # สร้าง Target แบบ Binary
        price_change = df['Close'].pct_change()
        df['Target'] = (price_change > 0.001).astype(int)  # 1 ถ้าราคาขึ้น > 0.1%

        # สร้าง Target แบบ Multiclass
        conditions = [
            price_change <= -0.002,  # Strong Sell
            (price_change > -0.002) & (price_change <= -0.001),  # Sell
            (price_change > -0.001) & (price_change < 0.001),   # Hold
            (price_change >= 0.001) & (price_change < 0.002),   # Buy
            price_change >= 0.002    # Strong Buy
        ]
        choices = [0, 1, 2, 3, 4]
        df['Target_Multiclass'] = np.select(conditions, choices, default=2)

        # ลบแถวที่มี NaN
        df = df.dropna()

        print(f"✅ สร้างคอลัมน์เพิ่มเติมเรียบร้อย")
        print(f"📊 ข้อมูลหลังจากประมวลผล: {len(df):,} แถว, {len(df.columns)} คอลัมน์")
        
        # ตรวจสอบข้อมูล Target
        target_column = 'Target_Multiclass' if USE_MULTICLASS_TARGET else 'Target'
        if target_column not in df.columns:
            print(f"❌ ไม่พบคอลัมน์ {target_column}")
            print(f"📊 คอลัมน์ที่มี: {[col for col in df.columns if 'Target' in col]}")
            return None
        
        print(f"✅ พบคอลัมน์ {target_column}")
        print(f"📊 การกระจายของ Target:")
        target_counts = df[target_column].value_counts().sort_index()
        for value, count in target_counts.items():
            percentage = (count / len(df)) * 100
            print(f"   {value}: {count:,} ({percentage:.1f}%)")
        
        # กำหนดพารามิเตอร์
        symbol = "GOLD"
        timeframe = 60  # M60
        output_folder = f"Test_LightGBM/results/real_test_M{timeframe}_{symbol}"
        
        print(f"\n📋 พารามิเตอร์การทดสอบ:")
        print(f"   Symbol: {symbol}")
        print(f"   Timeframe: M{timeframe}")
        print(f"   Target Column: {target_column}")
        print(f"   Output Folder: {output_folder}")
        print(f"   Data Shape: {df.shape}")
        
        # ลบโฟลเดอร์เก่า (ถ้ามี)
        import shutil
        if os.path.exists("Test_LightGBM/results"):
            print(f"🗑️ ลบโฟลเดอร์ผลลัพธ์เก่า...")
            shutil.rmtree("Test_LightGBM/results")
        
        # เรียกใช้ฟังก์ชัน
        print(f"\n🔄 เรียกใช้ train_all_scenario_models()...")
        print("="*70)
        
        start_time = datetime.now()
        
        results = train_all_scenario_models(
            df=df,
            symbol=symbol,
            timeframe=timeframe,
            target_column=target_column,
            output_folder=output_folder
        )
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("="*70)
        print(f"✅ train_all_scenario_models() เสร็จสิ้น")
        print(f"⏱️ เวลาที่ใช้: {duration}")
        print(f"📊 ผลลัพธ์: {len(results) if results else 0} scenarios")
        
        # แสดงผลลัพธ์
        if results:
            print(f"\n📈 สรุปผลลัพธ์:")
            for scenario_name, result in results.items():
                if result:
                    print(f"   ✅ {scenario_name}:")
                    print(f"      - Accuracy: {result.get('accuracy', 0):.3f}")
                    print(f"      - F1 Score: {result.get('f1_score', 0):.3f}")
                    print(f"      - AUC: {result.get('auc', 0):.3f}")
                    print(f"      - Train Samples: {result.get('train_samples', 0):,}")
                    print(f"      - Test Samples: {result.get('test_samples', 0):,}")
                else:
                    print(f"   ❌ {scenario_name}: ล้มเหลว")
        
        # ตรวจสอบไฟล์ที่สร้าง
        check_created_files_real_data(symbol, timeframe)
        
        return results
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_created_files_real_data(symbol, timeframe):
    """ตรวจสอบไฟล์ที่ถูกสร้างจากข้อมูลจริง"""
    print(f"\n📁 ตรวจสอบไฟล์ที่สร้าง:")
    print("="*60)
    
    # รายการไฟล์ที่ควรถูกสร้าง
    expected_files = [
        # ใน Test_LightGBM/results/
        f"Test_LightGBM/results/{str(timeframe).zfill(3)}_{symbol}_random_forest_feature_importance.csv",
        "Test_LightGBM/results/multi_scenario_performance_analysis.txt",
        "Test_LightGBM/results/multi_scenario_cv_results.json",
        
        # ใน Test_LightGBM/results/plots/
        "Test_LightGBM/results/plots/performance_auc_comparison.png",
        "Test_LightGBM/results/plots/performance_f1_score_comparison.png",
        "Test_LightGBM/results/plots/performance_accuracy_comparison.png",
        "Test_LightGBM/results/plots/performance_combined_comparison.png",
        "Test_LightGBM/results/plots/plots_created.txt",
    ]
    
    # ตรวจสอบไฟล์ในโฟลเดอร์ group
    for scenario in ['trend_following', 'counter_trend']:
        expected_files.extend([
            f"Test_LightGBM/results/{scenario}/final_results.csv",
            f"Test_LightGBM/results/{scenario}/training_results.csv",
            f"Test_LightGBM/results/{scenario}/{str(timeframe).zfill(3)}_{symbol}_feature_importance_comparison.csv",
            f"Test_LightGBM/results/{scenario}/{str(timeframe).zfill(3)}_{symbol}_feature_importance_comparison.png",
            f"Test_LightGBM/results/{scenario}/{str(timeframe).zfill(3)}_{symbol}/{str(timeframe).zfill(3)}_{symbol}_evaluation_report.csv",
            f"Test_LightGBM/results/{scenario}/{str(timeframe).zfill(3)}_{symbol}/{str(timeframe).zfill(3)}_{symbol}_performance_curves.png",
        ])
    
    # ตรวจสอบแต่ละไฟล์
    created_files = []
    missing_files = []
    
    for file_path in expected_files:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            created_files.append((file_path, file_size))
            print(f"✅ {file_path} ({file_size:,} bytes)")
        else:
            missing_files.append(file_path)
            print(f"❌ {file_path} (ไม่พบ)")
    
    # สรุปผลลัพธ์
    print(f"\n📊 สรุปผลลัพธ์:")
    print(f"   ไฟล์ที่สร้างสำเร็จ: {len(created_files)}/{len(expected_files)}")
    print(f"   ไฟล์ที่ขาดหายไป: {len(missing_files)}")
    
    if missing_files:
        print(f"\n⚠️ ไฟล์ที่ขาดหายไป:")
        for file_path in missing_files:
            print(f"   - {file_path}")
    
    # แสดงขนาดไฟล์ที่ใหญ่ที่สุด
    if created_files:
        largest_files = sorted(created_files, key=lambda x: x[1], reverse=True)[:5]
        print(f"\n📈 ไฟล์ที่ใหญ่ที่สุด 5 อันดับ:")
        for file_path, file_size in largest_files:
            print(f"   {os.path.basename(file_path)}: {file_size:,} bytes")
    
    # ตรวจสอบโฟลเดอร์ที่สร้าง
    print(f"\n📂 โครงสร้างโฟลเดอร์:")
    base_path = "Test_LightGBM/results"
    if os.path.exists(base_path):
        for root, dirs, files in os.walk(base_path):
            level = root.replace(base_path, '').count(os.sep)
            indent = '  ' * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = '  ' * (level + 1)
            for file in files[:3]:  # แสดงแค่ 3 ไฟล์แรกในแต่ละโฟลเดอร์
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                print(f"{subindent}{file} ({file_size:,} bytes)")
            if len(files) > 3:
                print(f"{subindent}... และอีก {len(files)-3} ไฟล์")

def show_data_info():
    """แสดงข้อมูลเบื้องต้นของไฟล์"""
    print("📊 ข้อมูลเบื้องต้นของไฟล์:")
    print("="*50)
    
    data_file = "CSV_Files_Fixed/GOLD_H1_FIXED.csv"
    if os.path.exists(data_file):
        try:
            # อ่านเฉพาะ header และ 5 แถวแรก
            df_sample = pd.read_csv(data_file, sep=',', skiprows=1, nrows=5)
            print(f"📁 ไฟล์: {data_file}")
            print(f"📊 ตัวอย่างข้อมูล (5 แถวแรก):")
            print(df_sample.to_string())

            # อ่านข้อมูลทั้งหมดเพื่อดูขนาด
            df_full = pd.read_csv(data_file, sep=',', skiprows=1)
            print(f"\n📈 ข้อมูลทั้งหมด:")
            print(f"   - จำนวนแถว: {len(df_full):,}")
            print(f"   - จำนวนคอลัมน์: {len(df_full.columns)}")
            print(f"   - ขนาดไฟล์: {os.path.getsize(data_file):,} bytes")
            
        except Exception as e:
            print(f"❌ ไม่สามารถอ่านไฟล์: {e}")
    else:
        print(f"❌ ไม่พบไฟล์: {data_file}")

if __name__ == "__main__":
    print("🧪 ทดสอบ Multi-Model Architecture กับข้อมูลจริง")
    print("📊 M60 + GOLD_H1_FIXED.csv")
    print("="*70)
    
    # แสดงข้อมูลเบื้องต้น
    show_data_info()
    
    # ทดสอบ
    results = test_real_data_multi_model()
    
    if results:
        print(f"\n🎉 การทดสอบเสร็จสิ้น!")
        print(f"📊 ผลลัพธ์: {len(results)} scenarios ถูกเทรนสำเร็จ")
        
        # แสดงสรุปประสิทธิภาพ
        if all(results.values()):
            avg_accuracy = np.mean([r['accuracy'] for r in results.values()])
            avg_f1 = np.mean([r['f1_score'] for r in results.values()])
            avg_auc = np.mean([r['auc'] for r in results.values()])
            
            print(f"\n📈 ประสิทธิภาพเฉลี่ย:")
            print(f"   Accuracy: {avg_accuracy:.3f}")
            print(f"   F1 Score: {avg_f1:.3f}")
            print(f"   AUC: {avg_auc:.3f}")
    else:
        print(f"\n💥 การทดสอบล้มเหลว!")
    
    print(f"\n" + "="*70)

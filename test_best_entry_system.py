#!/usr/bin/env python3
"""
ตรวจสอบระบบการบันทึกและโหลด best_entry.pkl
และให้ข้อเสนอแนะการปรับปรุง
"""

import os
import pickle
import pandas as pd
from datetime import datetime

def check_best_entry_files():
    """
    ตรวจสอบไฟล์ best_entry.pkl ที่มีอยู่
    """
    print("🔍 ตรวจสอบไฟล์ best_entry.pkl ที่มีอยู่")
    print("=" * 60)
    
    # ตรวจสอบในโฟลเดอร์ results
    results_folders = [
        "Test_LightGBM/results",
        "Test_LightGBM/results/M30", 
        "Test_LightGBM/results/M60"
    ]
    
    found_files = []
    
    for folder in results_folders:
        if os.path.exists(folder):
            print(f"\n📁 ตรวจสอบโฟลเดอร์: {folder}")
            files = os.listdir(folder)
            best_entry_files = [f for f in files if f.endswith('_best_entry.pkl')]
            
            if best_entry_files:
                print(f"  ✅ พบไฟล์ best_entry: {len(best_entry_files)} ไฟล์")
                for file in best_entry_files:
                    file_path = os.path.join(folder, file)
                    found_files.append(file_path)
                    
                    # ตรวจสอบเนื้อหาไฟล์
                    try:
                        with open(file_path, 'rb') as f:
                            data = pickle.load(f)
                        print(f"    📄 {file}: {data}")
                    except Exception as e:
                        print(f"    ❌ {file}: ไม่สามารถอ่านได้ - {e}")
            else:
                print(f"  ❌ ไม่พบไฟล์ best_entry.pkl")
        else:
            print(f"\n📁 โฟลเดอร์ไม่มีอยู่: {folder}")
    
    return found_files

def analyze_training_model_best_entry_logic():
    """
    วิเคราะห์ logic การบันทึกและโหลด best_entry ใน training model
    """
    print("\n🎯 วิเคราะห์ Logic การจัดการ best_entry ใน Training Model")
    print("=" * 60)
    
    print("📋 ขั้นตอนการทำงานปัจจุบัน:")
    print("1. โหลด best_entry จากรอบก่อน (ถ้ามี)")
    print("2. ทดสอบ entry conditions ทั้งหมด")
    print("3. เลือก entry condition ที่ดีที่สุด (expectancy สูงสุด)")
    print("4. บันทึกผลลัพธ์ใน .txt file")
    print("5. ❌ ไม่มีการบันทึก best_entry.pkl ใหม่!")
    
    print("\n⚠️ ปัญหาที่พบ:")
    print("- Training model โหลด best_entry.pkl แต่ไม่บันทึกค่าใหม่")
    print("- WebRequest server ไม่มีการโหลด best_entry.pkl")
    print("- ไม่มีการซิงค์ entry condition ระหว่าง training และ production")

def analyze_webserver_entry_logic():
    """
    วิเคราะห์ logic การใช้ entry conditions ใน WebRequest server
    """
    print("\n🌐 วิเคราะห์ Logic การใช้ Entry Conditions ใน WebRequest Server")
    print("=" * 60)
    
    print("📋 ขั้นตอนการทำงานปัจจุบัน:")
    print("1. ใช้ entry conditions แบบ hard-coded (entry_v1)")
    print("2. ไม่มีการโหลด best_entry.pkl จาก training")
    print("3. ไม่มีการปรับเปลี่ยน entry condition แบบ dynamic")
    
    print("\n⚠️ ปัญหาที่พบ:")
    print("- WebRequest server ใช้ entry condition คงที่")
    print("- ไม่ได้ใช้ประโยชน์จากการทดสอบ entry conditions ใน training")
    print("- ไม่มีการอัปเดต entry condition ตามผลการทดสอบ")

def generate_improvement_recommendations():
    """
    สร้างข้อเสนอแนะการปรับปรุง
    """
    print("\n💡 ข้อเสนอแนะการปรับปรุงระบบ best_entry")
    print("=" * 60)
    
    recommendations = [
        {
            "priority": "สูง",
            "title": "เพิ่มการบันทึก best_entry.pkl ใน Training Model",
            "description": "เพิ่มโค้ดบันทึก best_entry.pkl หลังจากเลือก entry condition ที่ดีที่สุด",
            "implementation": """
# เพิ่มหลังบรรทัด 6363 ใน python_LightGBM_15_Tuning.py
if best_entry and Save_File:
    best_entry_info = {
        "entry_name": best_entry_name,
        "expectancy": best_entry_result['expectancy'],
        "win_rate": best_entry_result['win_rate'],
        "num_trades": best_entry_result['num_trades'],
        "timestamp": datetime.now().isoformat(),
        "symbol": symbol,
        "timeframe": timeframe
    }
    best_entry_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_best_entry.pkl")
    with open(best_entry_path, 'wb') as f:
        pickle.dump(best_entry_info, f)
    print(f"✅ บันทึก best_entry: {best_entry_path}")
            """
        },
        {
            "priority": "สูง", 
            "title": "เพิ่มการโหลด best_entry.pkl ใน WebRequest Server",
            "description": "ให้ WebRequest server โหลดและใช้ entry condition ที่ดีที่สุดจาก training",
            "implementation": """
# เพิ่มฟังก์ชันใหม่ใน python_to_mt5_WebRequest_server_11_Tuning.py
def load_best_entry_condition(symbol, timeframe):
    best_entry_path = f"Test_LightGBM/results/{str(timeframe).zfill(3)}_{symbol}_best_entry.pkl"
    if os.path.exists(best_entry_path):
        with open(best_entry_path, 'rb') as f:
            best_entry_info = pickle.load(f)
        return best_entry_info.get("entry_name", "entry_v1")
    return "entry_v1"  # fallback
            """
        },
        {
            "priority": "กลาง",
            "title": "สร้างระบบ Dynamic Entry Conditions",
            "description": "ให้ WebRequest server เปลี่ยน entry condition ตามผลการทดสอบ",
            "implementation": """
# ปรับปรุง entry conditions ใน WebRequest server
def get_entry_conditions():
    return {
        "default": {...},
        "entry_v1": {...},
        "entry_v2": {...},
        "entry_v3": {...}
    }

def apply_best_entry_condition(symbol, timeframe, latest_features):
    best_entry_name = load_best_entry_condition(symbol, timeframe)
    entry_conditions = get_entry_conditions()
    entry_func = entry_conditions.get(best_entry_name, entry_conditions["entry_v1"])
    return entry_func(latest_features)
            """
        },
        {
            "priority": "กลาง",
            "title": "เพิ่มการตรวจสอบความใหม่ของ best_entry",
            "description": "ตรวจสอบว่า best_entry.pkl ถูกอัปเดตเมื่อไหร่",
            "implementation": """
def is_best_entry_fresh(symbol, timeframe, max_age_hours=24):
    best_entry_path = f"Test_LightGBM/results/{str(timeframe).zfill(3)}_{symbol}_best_entry.pkl"
    if os.path.exists(best_entry_path):
        file_time = os.path.getmtime(best_entry_path)
        age_hours = (time.time() - file_time) / 3600
        return age_hours <= max_age_hours
    return False
            """
        },
        {
            "priority": "ต่ำ",
            "title": "สร้างระบบ Monitoring best_entry",
            "description": "ติดตามการเปลี่ยนแปลง entry condition และประสิทธิภาพ",
            "implementation": """
def log_entry_condition_change(symbol, timeframe, old_entry, new_entry, reason):
    log_path = f"Test_LightGBM/logs/entry_changes.log"
    with open(log_path, 'a') as f:
        f.write(f"{datetime.now()}: {symbol} {timeframe} {old_entry}->{new_entry} ({reason})\\n")
            """
        }
    ]
    
    for i, rec in enumerate(recommendations, 1):
        print(f"\n{i}. 🎯 {rec['title']} (ความสำคัญ: {rec['priority']})")
        print(f"   📝 {rec['description']}")
        print(f"   💻 การใช้งาน:")
        for line in rec['implementation'].strip().split('\n'):
            print(f"      {line}")

def check_current_entry_usage():
    """
    ตรวจสอบการใช้งาน entry conditions ปัจจุบัน
    """
    print("\n📊 สถานะการใช้งาน Entry Conditions ปัจจุบัน")
    print("=" * 60)
    
    # ตรวจสอบไฟล์ entry_summary.txt
    summary_files = []
    for root, dirs, files in os.walk("Test_LightGBM/results"):
        for file in files:
            if file.endswith("_entry_summary.txt"):
                summary_files.append(os.path.join(root, file))
    
    if summary_files:
        print(f"✅ พบไฟล์ entry summary: {len(summary_files)} ไฟล์")
        
        # อ่านไฟล์ล่าสุด
        latest_file = max(summary_files, key=os.path.getmtime)
        print(f"📄 ไฟล์ล่าสุด: {latest_file}")
        
        try:
            with open(latest_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if lines:
                print(f"📋 บรรทัดล่าสุด:")
                print(f"   {lines[-1].strip()}")
                
                # แยกข้อมูล best entry
                if "Best" in lines[-1]:
                    best_entry = lines[-1].split("Best")[-1].strip()
                    print(f"🏆 Entry condition ที่ดีที่สุดล่าสุด: {best_entry}")
        except Exception as e:
            print(f"❌ ไม่สามารถอ่านไฟล์ได้: {e}")
    else:
        print("❌ ไม่พบไฟล์ entry summary")

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🔍 การตรวจสอบระบบ Entry Conditions และ best_entry.pkl")
    print("=" * 80)
    
    # 1. ตรวจสอบไฟล์ best_entry.pkl
    found_files = check_best_entry_files()
    
    # 2. วิเคราะห์ logic ใน training model
    analyze_training_model_best_entry_logic()
    
    # 3. วิเคราะห์ logic ใน webserver
    analyze_webserver_entry_logic()
    
    # 4. ตรวจสอบการใช้งานปัจจุบัน
    check_current_entry_usage()
    
    # 5. ให้ข้อเสนอแนะ
    generate_improvement_recommendations()
    
    print(f"\n{'='*80}")
    print("🎯 สรุป:")
    print("1. ระบบปัจจุบันมีการทดสอบ entry conditions แต่ไม่บันทึก best_entry.pkl")
    print("2. WebRequest server ใช้ entry condition แบบคงที่")
    print("3. ควรเพิ่มการบันทึกและโหลด best_entry เพื่อใช้ประโยชน์จากการทดสอบ")
    print("4. ระบบจะมีประสิทธิภาพดีขึ้นถ้ามีการซิงค์ entry conditions")

if __name__ == "__main__":
    main()

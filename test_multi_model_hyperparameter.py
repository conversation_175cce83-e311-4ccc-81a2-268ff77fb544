#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบ Hyperparameter Tuning ใน Multi-Model Architecture
"""

import os
import sys
import pandas as pd

# เพิ่ม path สำหรับ import
sys.path.append('.')

# Import จาก main script
from python_LightGBM_16_Signal import (
    USE_MULTI_MODEL_ARCHITECTURE,
    train_all_scenario_models,
    load_and_process_data,
    test_groups
)

def test_hyperparameter_tuning():
    """ทดสอบ hyperparameter tuning สำหรับ multi-model"""
    
    print("🧪 ทดสอบ Hyperparameter Tuning ใน Multi-Model Architecture")
    print("="*70)
    
    # ตรวจสอบการตั้งค่า
    print(f"📊 USE_MULTI_MODEL_ARCHITECTURE = {USE_MULTI_MODEL_ARCHITECTURE}")
    
    if not USE_MULTI_MODEL_ARCHITECTURE:
        print("❌ กรุณาเปิด USE_MULTI_MODEL_ARCHITECTURE = True ใน python_LightGBM_16_Signal.py")
        return
    
    # เลือกไฟล์ทดสอบ (เลือกแค่ 1 ไฟล์เพื่อทดสอบ)
    test_file = "CSV_Files_Fixed/EURUSD_M30_FIXED.csv"
    
    if not os.path.exists(test_file):
        print(f"❌ ไม่พบไฟล์ทดสอบ: {test_file}")
        return
    
    print(f"📁 ใช้ไฟล์ทดสอบ: {test_file}")
    
    # แยก symbol และ timeframe จากชื่อไฟล์
    filename = os.path.basename(test_file)
    parts = filename.replace('_FIXED.csv', '').split('_')
    symbol = parts[0]
    timeframe_str = parts[1]
    
    # แปลง timeframe
    timeframe_map = {"M30": 30, "M60": 60, "H1": 60}
    timeframe = timeframe_map.get(timeframe_str, 30)
    
    print(f"🎯 Symbol: {symbol}, Timeframe: {timeframe}")
    
    try:
        # โหลดและประมวลผลข้อมูล
        print(f"\n📊 โหลดข้อมูลจาก {test_file}...")

        # ใช้ pandas โหลดข้อมูลโดยตรง
        try:
            df = pd.read_csv(test_file, sep='\t')
            print(f"✅ โหลดข้อมูลสำเร็จ: {len(df)} rows, {len(df.columns)} columns")

            # สร้าง trade_df ง่ายๆ สำหรับทดสอบ
            trade_df = df.copy()
            trade_df['target'] = 0  # dummy target

        except Exception as e:
            print(f"❌ ไม่สามารถโหลดข้อมูลได้: {e}")
            return
        
        print(f"✅ โหลดข้อมูลสำเร็จ: {len(df)} rows")
        
        # ทดสอบ Multi-Model Training
        print(f"\n🤖 เริ่มทดสอบ Multi-Model Training...")
        print(f"   - จะสร้าง 2 โมเดล: trend_following และ counter_trend")
        print(f"   - แต่ละโมเดลจะมี hyperparameter tuning แยกกัน")
        
        # เรียกใช้ train_all_scenario_models
        result = train_all_scenario_models(
            df=df,
            trade_df=trade_df,
            symbol=symbol,
            timeframe=timeframe,
            output_folder=None  # ใช้ default
        )
        
        if result:
            print(f"\n✅ Multi-Model Training สำเร็จ!")
            
            # ตรวจสอบไฟล์ที่ถูกสร้าง
            print(f"\n🔍 ตรวจสอบไฟล์ที่ถูกสร้าง:")
            
            # ตรวจสอบ hyperparameter files
            hyper_dir = f"LightGBM_Hyper_Multi/{str(timeframe).zfill(3)}_{symbol}"
            if os.path.exists(hyper_dir):
                print(f"📁 Hyperparameter files ใน {hyper_dir}:")
                for file in os.listdir(hyper_dir):
                    if file.endswith('.json'):
                        print(f"   ✅ {file}")
            else:
                print(f"❌ ไม่พบโฟลเดอร์ hyperparameter: {hyper_dir}")
            
            # ตรวจสอบ model files
            scenarios = ['trend_following', 'counter_trend']
            for scenario in scenarios:
                model_dir = f"LightGBM_Multi/models/{scenario}"
                if os.path.exists(model_dir):
                    model_files = [f for f in os.listdir(model_dir) 
                                 if f.startswith(f"{str(timeframe).zfill(3)}_{symbol}")]
                    print(f"📁 {scenario} models: {len(model_files)} files")
                    for file in model_files:
                        print(f"   ✅ {file}")
                else:
                    print(f"❌ ไม่พบโฟลเดอร์ model: {model_dir}")
        
        else:
            print(f"❌ Multi-Model Training ล้มเหลว")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def test_parameter_stability():
    """ทดสอบ parameter stability analysis"""
    
    print(f"\n🔬 ทดสอบ Parameter Stability Analysis...")
    
    try:
        # รัน check_parameter_stability.py
        import subprocess
        result = subprocess.run([
            sys.executable, "check_parameter_stability.py"
        ], capture_output=True, text=True, cwd=".")
        
        print("📊 ผลลัพธ์ Parameter Stability Analysis:")
        print(result.stdout)
        
        if result.stderr:
            print("⚠️ Warnings/Errors:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ ไม่สามารถรัน parameter stability analysis: {e}")

if __name__ == "__main__":
    print("🏗️ Multi-Model Hyperparameter Tuning Test")
    print("="*50)
    
    # ทดสอบ hyperparameter tuning
    test_hyperparameter_tuning()
    
    # ทดสอบ parameter stability analysis
    test_parameter_stability()
    
    print(f"\n✅ การทดสอบเสร็จสิ้น")

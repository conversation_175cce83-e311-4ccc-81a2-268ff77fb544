แก้ไข hyperparameter 
+ multi-model architecture
ฟังก์ชันสำหรับโหลดพารามิเตอร์จาก Multi-Model Architecture:

    "M30": [
        "CSV_Files_Fixed/AUDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/EURGBP_M30_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GBPUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GOLD_M30_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_M30_FIXED.csv"
    ],
    "M60": [
        "CSV_Files_Fixed/AUDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/EURGBP_H1_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/GOLD_H1_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_H1_FIXED.csv"
    ]

test_folder = "LightGBM"
test_hyper  = "LightGBM_Hyper"
if USE_MULTI_MODEL_ARCHITECTURE:
    test_folder = "LightGBM_Multi"
    test_hyper  = "LightGBM_Hyper_Multi"
else:
    test_folder = "LightGBM_Single"
    test_hyper  = "LightGBM_Hyper_Single"

if not os.path.exists(test_folder):
    os.makedirs(test_folder)
    print(f"สร้างโฟลเดอร์ {test_folder} เรียบร้อยแล้ว")

if not os.path.exists(test_hyper):
    os.makedirs(test_hyper)
    print(f"สร้างโฟลเดอร์ {test_hyper} เรียบร้อยแล้ว")

output_folder = f"{test_folder}/results"
if not os.path.exists(output_folder):
    os.makedirs(output_folder)
    print(f"สร้างโฟลเดอร์ {output_folder} เรียบร้อยแล้ว")

+++

โครงสร้างไฟล์ ที่ใช้เหมือนกัน

test_folder = "LightGBM"
test_hyper  = "LightGBM_Hyper"
if USE_MULTI_MODEL_ARCHITECTURE:
    test_folder = "LightGBM_Multi"
    test_hyper  = "LightGBM_Hyper_Multi"
else:
    test_folder = "LightGBM_Single"
    test_hyper  = "LightGBM_Hyper_Single"

CSV_Files_Fixed << จัดเก็บข้อมูลทดสอบ csv
├─ {symbol}_H1_FIXED.csv
└─ {symbol}_M30_FIXED.csv

กรณี ใช้ 2 โมเดล มีโครงสร้างไฟล์ดังนี้
LightGBM_Hyper_Multi << มีผลการทดสอบก่อนหน้า
└─ {timeframe}_{symbol}
       ├─ {timeframe}_{symbol}_best_params.json
       └─ {timeframe}_{symbol}_tuning_flag.json

LightGBM_Multi
├─ models
│   ├─ counter_trend
│   │   ├─ {timeframe}_{symbol}_features.pkl
│   │   ├─ {timeframe}_{symbol}_scaler.pkl
│   │   └─ {timeframe}_{symbol}_trained.pkl
│   └─ trend_following
│         ├─ {timeframe}_{symbol}_features.pkl
│         ├─ {timeframe}_{symbol}_scaler.pkl
│         └─ {timeframe}_{symbol}_trained.pkl
│
└─ thresholds
       ├─ {timeframe}_{symbol}_counter_trend_optimal_nBars_SL.pkl
       ├─ {timeframe}_{symbol}_counter_trend_optimal_threshold.pkl
       ├─ {timeframe}_{symbol}_trend_following_optimal_nBars_SL.pkl
       ├─ {timeframe}_{symbol}_trend_following_optimal_threshold.pkl
       └─ {timeframe}_{symbol}_time_filters.pkl

กรณี ใช้ 1 โมเดล มีโครงสร้างไฟล์ดังนี้
LightGBM_Hyper_Single << มีผลการทดสอบก่อนหน้า
└─ {timeframe}_{symbol}
       ├─ {timeframe}_{symbol}_best_params.json
       └─ {timeframe}_{symbol}_tuning_flag.json

LightGBM_Single
├─ models
│   └─ {timeframe}_{symbol}
│         ├─ {timeframe}_{symbol}_features.pkl
│         ├─ {timeframe}_{symbol}_scaler.pkl
│         └─ {timeframe}_{symbol}_trained.pkl
│
└─ thresholds
       ├─ {timeframe}_{symbol}_optimal_nBars_SL.pkl
       ├─ {timeframe}_{symbol}_optimal_threshold.pkl
       └─ {timeframe}_{symbol}_time_filters.pkl

ใช้ 2 โมเดลแยกตามสถานการณ์ (trend_following, counter_trend)
USE_MULTI_MODEL_ARCHITECTURE = True

        flag_file = os.path.join(base_dir, f"{str(timeframe).zfill(3)}_{symbol}_tuning_flag.json")
        param_file = os.path.join(base_dir, f"{str(timeframe).zfill(3)}_{symbol}_best_params.json")

        # Force hyperparameter tuning for improved parameters (2025-07-05)
        # ตรวจสอบ command line argument สำหรับ force retuning
        force_retune = getattr(sys.modules[__name__], '_force_retune', False)

        if force_retune:
            print("🔄 --force-retune: บังคับเปิด hyperparameter tuning")
            do_hyperparameter_tuning = True
        else:
            # Check flag (วิธีเดิม)
            if os.path.exists(flag_file):
                with open(flag_file, "r") as f:
                    flag_data = json.load(f)
                do_hyperparameter_tuning = flag_data.get("do_hyperparameter_tuning", False)
            else:
                do_hyperparameter_tuning = True # ครั้งแรก ให้เปิด tuning

เนื่องจากเปลี่ยนข้อมูลตัวอย่างการทดสอบ การเรียกใช้ do_hyperparameter_tuning ต้องเรียกผ่าน train_and_evaluate() 
ดังนั้นถ้าใช้ multi-model หรือ USE_MULTI_MODEL_ARCHITECTURE = True 
จำเป็นจ้องทดสอบ hyperparameter_tuning หรือไม่

ถ้าต้องใช้ช่วยปรับแก้ code ให้มีการเรียกใช้ hyperparameter_tuning

และ เมื่อใช้ 2 โมเดล ต้องมีการเปลี่ยนแปลงการทดสอบ หรือไม่

เนื่องจากปัจจุบันถ้าใช้ single-model จะมี code ตรวจสอบผลลัพธ์ python check_parameter_stability.py ใช้ร่วมกันได้หรือไม่ หรือต้องปรับปรุง หรือสร้างใหม่

และ ใช้ร่วมกับ single-model ได้หรือไม่ หรือต้องสร้างห้องเก็บใหม่ folder เพื่อแยกผลการทดสอบ ถ้าแยกใช้ช่วยแยกห้อง และตั้งชื่อ 
LightGBM_Hyper_Multi สำหรับ multi-model หรือ USE_MULTI_MODEL_ARCHITECTURE = True
LightGBM_Hyper_Single สำหรับ single-model หรือ USE_MULTI_MODEL_ARCHITECTURE = False

ช่วยตรวจสอบ การตั้งชื่อไฟล์และ Path 
การเรียกใช้งาน และการบันทึก

+++

ขั้นตอนทดสอบค่า

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 5
  📊 Classes: [0 1 2 3 4]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 5 classes
  🎯 Adaptive Parameters: lr=0.033, leaves=21 (Symbol: GOLD, TF: 30)
  🎯 Multi-class Class Weights: {0: 0.5429272281275552, 1: 2.990990990990991, 2: 0.5933869526362824, 3: 4.709219858156028, 4: 1.0796747967479674}  
Class Ratio (0:1): 1.00:1
📁 สร้างโฟลเดอร์สำหรับ tuning files: LightGBM_Hyper_Single/030_GOLD

🔍 ตรวจสอบสถานะ Hyperparameter Tuning:
   do_hyperparameter_tuning = True
   flag_file = LightGBM_Hyper_Single/030_GOLD\030_GOLD_tuning_flag.json
   param_file = LightGBM_Hyper_Single/030_GOLD\030_GOLD_best_params.json
   flag_file exists = False
   param_file exists = False

🔍 เริ่ม Hyperparameter Tuning ด้วย RandomizedSearchCV
🔄 เริ่ม RandomizedSearchCV...
   Training data: 6115 samples, 25 features
   Target distribution: {0: 1223, 4: 1223, 3: 1223, 2: 1223, 1: 1223}
Fitting 5 folds for each of 100 candidates, totalling 500 fits
[CV] END bagging_fraction=0.7, bagging_freq=3, class_weight=None, feature_fraction=0.9, learning_rate=0.03, max_depth=6, min_child_weight=0.1, min_data_in_leaf=15, num_leaves=30, reg_alpha=0.3, reg_lambda=0.1, subsample_freq=3; total time=  17.1s
[CV] END bagging_fraction=0.7, bagging_freq=3, class_weight=None, feature_fraction=0.85, learning_rate=0.03, max_depth=8, min_child_weight=0.01, min_data_in_leaf=20, num_leaves=20, reg_alpha=0.5, reg_lambda=0.5, subsample_freq=5; total time=  19.4s

+++

            print(f"\n🔍 เริ่ม Hyperparameter Tuning ด้วย RandomizedSearchCV สำหรับ {scenario_name}")

            # สร้าง parameter distribution สำหรับ RandomizedSearchCV
            from scipy.stats import uniform, randint

            param_dist = {
                'learning_rate': uniform(0.01, 0.19),  # 0.01 to 0.2
                'num_leaves': randint(10, 100),        # 10 to 99
                'max_depth': randint(3, 15),           # 3 to 14
                'min_data_in_leaf': randint(5, 50),    # 5 to 49
                'feature_fraction': uniform(0.6, 0.35), # 0.6 to 0.95
                'bagging_fraction': uniform(0.6, 0.35), # 0.6 to 0.95
                'bagging_freq': randint(1, 10),        # 1 to 9
                'reg_alpha': uniform(0.0, 0.5),        # 0.0 to 0.5
                'reg_lambda': uniform(0.0, 0.5),       # 0.0 to 0.5
            }
            print(f"📊 Parameter distribution สำหรับ RandomizedSearchCV")

+++

ช่วยตรวจสอบ Hyperparameter Tuning ของ multi-model

+ การทดสอบ และการตั้งค่า Hyperparameter Tuning ด้วย RandomizedSearchCV 
+ ปรับปรุง Parameter
+ การใช้ข้อมูล get_lgbm_params

ช่วยจัดการการใช้งาน ต้องใช้ค่าเดียวกัน หรือให้แยกแต่ละโมเดล


+++

ช่วยตรวจสอบ และแก้ไข การจับเวลาการเทรน

ที่ฟังชั่น def run_main_analysis()

โครงสร้างไฟล์
LightGBM_Multi
└─ {group_name}_time_summary.txt

ช่วยปรับการจับเวลาโดยแยกเป็นกลุ่ม M30 M60 เพื่อให้มีการบันทึกเวลาการทำงาน
ครอบคลุม for ทั้งหมด

// ตัวอย่างการบันทึก
Run 1 | 2025-07-12 17:20:43
Total time: 140.0713 sec | Files: 1 | Rounds: 1
Avg time per round: 140.0713 sec
==================================================
Run 1 | 2025-07-12 18:19:11
Total time: 107.1013 sec | Files: 1 | Rounds: 1
Avg time per round: 107.1013 sec
==================================================

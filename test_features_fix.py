#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข Features และการบันทึกไฟล์ optimization

Created: 2025-01-11
Author: AI Assistant
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
import json
from pathlib import Path

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_enhanced_features():
    """
    ทดสอบการเพิ่ม features ให้ครบ
    """
    print("🔍 ทดสอบการเพิ่ม features ให้ครบ")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import load_validation_data_for_optimization
        
        symbol = "GOLD"
        timeframe = 60
        
        print(f"📊 โหลดข้อมูล validation สำหรับ {symbol} M{timeframe}")
        
        val_df = load_validation_data_for_optimization(symbol, timeframe)
        
        if val_df is not None:
            print(f"✅ โหลดข้อมูลสำเร็จ: {len(val_df)} rows, {len(val_df.columns)} columns")
            
            # แสดง features ที่มี
            print(f"\n📋 Features ที่มี ({len(val_df.columns)} features):")
            features_list = list(val_df.columns)
            
            # จำแนกประเภท features
            feature_categories = {
                'Price': [f for f in features_list if any(x in f for x in ['Open', 'High', 'Low', 'Close'])],
                'Volume': [f for f in features_list if 'Volume' in f],
                'Technical': [f for f in features_list if any(x in f for x in ['RSI', 'MACD', 'BB', 'STOCH', 'ATR', 'EMA'])],
                'Lag': [f for f in features_list if 'Lag' in f],
                'Statistical': [f for f in features_list if any(x in f for x in ['Mean', 'Std', 'Min', 'Max', 'Return'])],
                'Other': [f for f in features_list if not any(cat in f for cat in ['Open', 'High', 'Low', 'Close', 'Volume', 'RSI', 'MACD', 'BB', 'STOCH', 'ATR', 'EMA', 'Lag', 'Mean', 'Std', 'Min', 'Max', 'Return'])]
            }
            
            for category, features in feature_categories.items():
                if features:
                    print(f"   {category}: {len(features)} features")
                    if len(features) <= 5:
                        print(f"     {features}")
                    else:
                        print(f"     {features[:3]} ... และอีก {len(features)-3} features")
            
            return val_df
        else:
            print(f"❌ ไม่สามารถโหลดข้อมูลได้")
            return None
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return None
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_threshold_optimization_with_enhanced_features():
    """
    ทดสอบการหา optimal threshold ด้วย features ที่เพิ่มแล้ว
    """
    print("\n🎯 ทดสอบการหา optimal threshold ด้วย features ที่เพิ่มแล้ว")
    print("="*60)
    
    try:
        from python_LightGBM_16_Signal import (
            load_scenario_models,
            find_optimal_threshold_multi_model,
            load_validation_data_for_optimization
        )
        
        symbol = "GOLD"
        timeframe = 60
        
        # โหลดโมเดล
        loaded_models = load_scenario_models(symbol, timeframe)
        
        if not loaded_models:
            print(f"❌ ไม่พบโมเดลสำหรับ {symbol} M{timeframe}")
            return False
        
        print(f"✅ โหลดโมเดลสำเร็จ: {len(loaded_models)} scenarios")
        
        # โหลดข้อมูล validation ที่มี features เพิ่มแล้ว
        val_df = load_validation_data_for_optimization(symbol, timeframe)
        
        if val_df is None:
            print(f"❌ ไม่สามารถโหลดข้อมูล validation ได้")
            return False
        
        print(f"📊 ข้อมูล validation: {len(val_df)} rows, {len(val_df.columns)} columns")
        
        # ทดสอบการหา optimal threshold
        print(f"\n🚀 ทดสอบการหา optimal threshold...")
        
        optimal_thresholds = find_optimal_threshold_multi_model(
            models_dict=loaded_models,
            val_df=val_df,
            symbol=symbol,
            timeframe=timeframe
        )
        
        print(f"\n📊 ผลการหา optimal threshold:")
        for scenario, threshold in optimal_thresholds.items():
            print(f"   {scenario}: {threshold}")
        
        # ตรวจสอบว่าได้ threshold ที่ไม่ใช่ default หรือไม่
        non_default_count = sum(1 for t in optimal_thresholds.values() if t != 0.5)
        
        if non_default_count > 0:
            print(f"✅ พบ threshold ที่ไม่ใช่ default: {non_default_count}/{len(optimal_thresholds)} scenarios")
            return True
        else:
            print(f"⚠️ ทุก scenarios ใช้ default threshold (0.5)")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_separate_optimization_files():
    """
    ทดสอบการบันทึกไฟล์ optimization แยกตามคู่เงิน
    """
    print("\n📁 ทดสอบการบันทึกไฟล์ optimization แยกตามคู่เงิน")
    print("="*60)
    
    try:
        from python_LightGBM_16_Signal import run_multi_model_optimization
        
        # ทดสอบกับ GOLD
        symbol = "GOLD"
        timeframe = 60
        
        print(f"🚀 รันการหา optimal parameters สำหรับ {symbol} M{timeframe}")
        
        result = run_multi_model_optimization(symbol, timeframe)
        
        if result:
            print(f"✅ Optimization สำเร็จ!")
            
            # ตรวจสอบไฟล์ที่สร้างขึ้น
            base_path = Path("LightGBM_Multi")
            
            # ไฟล์แยกตามคู่เงิน
            individual_file = base_path / f"optimization_{symbol}_{timeframe}.json"
            
            # ไฟล์สรุปรวม
            summary_file = base_path / "optimization_summary.json"
            
            print(f"\n📊 ตรวจสอบไฟล์ที่สร้าง:")
            
            if individual_file.exists():
                file_size = individual_file.stat().st_size
                print(f"✅ ไฟล์แยก: {individual_file.name} ({file_size} bytes)")
                
                # อ่านและแสดงเนื้อหา
                with open(individual_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    print(f"   📄 Symbol: {data.get('symbol')}")
                    print(f"   📄 Timeframe: {data.get('timeframe')}")
                    print(f"   📄 Scenarios: {data.get('scenarios')}")
                    print(f"   📄 Timestamp: {data.get('timestamp')}")
            else:
                print(f"❌ ไม่พบไฟล์แยก: {individual_file}")
            
            if summary_file.exists():
                file_size = summary_file.stat().st_size
                print(f"✅ ไฟล์สรุป: {summary_file.name} ({file_size} bytes)")
                
                # อ่านและแสดงเนื้อหา
                with open(summary_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    print(f"   📄 จำนวนคู่เงิน: {len(data)} คู่")
                    for key in data.keys():
                        print(f"     - {key}")
            else:
                print(f"❌ ไม่พบไฟล์สรุป: {summary_file}")
            
            return True
        else:
            print(f"❌ Optimization ล้มเหลว")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_multiple_symbols_simulation():
    """
    จำลองการทดสอบหลายคู่เงิน
    """
    print("\n🔄 จำลองการทดสอบหลายคู่เงิน")
    print("="*50)
    
    try:
        # จำลองการบันทึกหลายคู่เงิน
        from datetime import datetime
        import json
        
        base_path = Path("LightGBM_Multi")
        base_path.mkdir(exist_ok=True)
        
        # จำลองข้อมูลหลายคู่เงิน
        test_symbols = [
            {"symbol": "GOLD", "timeframe": 60},
            {"symbol": "AUDUSD", "timeframe": 60},
            {"symbol": "USDJPY", "timeframe": 60}
        ]
        
        summary_data = {}
        
        for test_data in test_symbols:
            symbol = test_data["symbol"]
            timeframe = test_data["timeframe"]
            
            # สร้างข้อมูลจำลอง
            individual_data = {
                'symbol': symbol,
                'timeframe': timeframe,
                'scenarios': ['trend_following', 'counter_trend'],
                'optimal_thresholds': {'trend_following': 0.6, 'counter_trend': 0.55},
                'optimal_nbars': {'trend_following': 8, 'counter_trend': 6},
                'validation_samples': 14367,
                'timestamp': datetime.now().isoformat(),
                'optimization_status': 'completed'
            }
            
            # บันทึกไฟล์แยก
            individual_file = base_path / f"optimization_{symbol}_{timeframe}.json"
            with open(individual_file, 'w', encoding='utf-8') as f:
                json.dump(individual_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ สร้างไฟล์จำลอง: {individual_file.name}")
            
            # เพิ่มเข้าสรุป
            summary_data[f"{symbol}_{timeframe}"] = {
                'symbol': symbol,
                'timeframe': timeframe,
                'scenarios': individual_data['scenarios'],
                'optimal_thresholds': individual_data['optimal_thresholds'],
                'optimal_nbars': individual_data['optimal_nbars'],
                'validation_samples': individual_data['validation_samples'],
                'last_updated': datetime.now().isoformat()
            }
        
        # บันทึกไฟล์สรุป
        summary_file = base_path / "optimization_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ สร้างไฟล์สรุป: {summary_file.name}")
        print(f"📊 รวมข้อมูล: {len(summary_data)} คู่เงิน/เวลา")
        
        # แสดงผลการทดสอบ
        print(f"\n📋 ผลการจำลอง:")
        for key, data in summary_data.items():
            print(f"   {key}:")
            print(f"     Thresholds: {data['optimal_thresholds']}")
            print(f"     nBars_SL: {data['optimal_nbars']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🚀 ทดสอบการแก้ไข Features และการบันทึกไฟล์")
    print("="*80)
    
    # Test 1: ทดสอบการเพิ่ม features
    val_df = test_enhanced_features()
    
    # Test 2: ทดสอบ threshold optimization ด้วย features ใหม่
    threshold_success = test_threshold_optimization_with_enhanced_features()
    
    # Test 3: ทดสอบการบันทึกไฟล์แยก
    file_success = test_separate_optimization_files()
    
    # Test 4: จำลองหลายคู่เงิน
    multi_symbol_success = test_multiple_symbols_simulation()
    
    print("\n" + "="*80)
    print("📊 สรุปผลการทดสอบ")
    print("="*80)
    
    print(f"✅ Test 1 - Enhanced Features: {'ผ่าน' if val_df is not None else 'ล้มเหลว'}")
    print(f"✅ Test 2 - Threshold Optimization: {'ผ่าน' if threshold_success else 'ล้มเหลว'}")
    print(f"✅ Test 3 - Separate Files: {'ผ่าน' if file_success else 'ล้มเหลว'}")
    print(f"✅ Test 4 - Multiple Symbols: {'ผ่าน' if multi_symbol_success else 'ล้มเหลว'}")
    
    overall_success = all([
        val_df is not None,
        threshold_success,
        file_success,
        multi_symbol_success
    ])
    
    if overall_success:
        print(f"\n🎉 การแก้ไข Features และการบันทึกไฟล์สำเร็จ!")
        print(f"🚀 ระบบพร้อมใช้งานกับหลายคู่เงิน")
        
        if val_df is not None:
            print(f"\n📊 สรุป Features:")
            print(f"   จำนวน Features: {len(val_df.columns)} features")
            print(f"   ข้อมูล Validation: {len(val_df)} rows")
    else:
        print(f"\n⚠️ ยังมีปัญหาบางส่วนที่ต้องแก้ไข")

if __name__ == "__main__":
    main()

# 🔄 คู่มือการ Reset และเทรนใหม่ - 2025-07-05

## 🎯 **วิธีการเทรนใหม่ด้วยพารามิเตอร์ที่ปรับปรุงแล้ว**

### **วิธีที่ 1: ใช้ Command Line Arguments (แนะนำ)**

#### **เทรนใหม่ทั้งหมดพร้อมลบไฟล์เก่า**:
```bash
python python_LightGBM_15_Tuning.py --force-retune --reset-flags
```

#### **เทรนใหม่เฉพาะ USDJPY และ GBPUSD**:
```bash
python python_LightGBM_15_Tuning.py --force-retune --reset-flags --symbols USDJPY GBPUSD --timeframes M30 H1
```

#### **เทรนใหม่โดยไม่ลบไฟล์เก่า (บังคับ tuning อย่างเดียว)**:
```bash
python python_LightGBM_15_Tuning.py --force-retune
```

### **วิธีที่ 2: ใช้ Reset Script**

#### **รัน Reset Script แบบ Interactive**:
```bash
python reset_training.py
```

#### **เลือกตัวเลือกใน Menu**:
```
📋 เลือกการดำเนินการ:
1. ตรวจสอบสถานะการเทรนปัจจุบัน
2. Reset ทั้งหมด (ลบไฟล์ flag และ best_params ทั้งหมด)
3. Reset เฉพาะ USDJPY และ GBPUSD
4. Reset เฉพาะ symbol ที่กำหนดเอง
5. ออกจากโปรแกรม
```

### **วิธีที่ 3: ลบไฟล์ด้วยตนเอง**

#### **ลบไฟล์ทั้งหมด**:
```bash
# Windows
del /s Test_LightGBM\models\*_tuning_flag.json
del /s Test_LightGBM\models\*_best_params.json

# Linux/Mac
find Test_LightGBM/models/ -name "*_tuning_flag.json" -delete
find Test_LightGBM/models/ -name "*_best_params.json" -delete
```

#### **ลบเฉพาะ USDJPY และ GBPUSD**:
```bash
# Windows
del Test_LightGBM\models\*USDJPY*_tuning_flag.json
del Test_LightGBM\models\*USDJPY*_best_params.json
del Test_LightGBM\models\*GBPUSD*_tuning_flag.json
del Test_LightGBM\models\*GBPUSD*_best_params.json

# Linux/Mac
rm Test_LightGBM/models/*USDJPY*_tuning_flag.json
rm Test_LightGBM/models/*USDJPY*_best_params.json
rm Test_LightGBM/models/*GBPUSD*_tuning_flag.json
rm Test_LightGBM/models/*GBPUSD*_best_params.json
```

## 📋 **Command Line Arguments ที่มีให้ใช้**

### **--force-retune**
- **คำอธิบาย**: บังคับเปิด hyperparameter tuning ไม่สนใจไฟล์ flag
- **ใช้เมื่อ**: ต้องการเทรนใหม่ด้วยพารามิเตอร์ที่ปรับปรุงแล้ว
- **ตัวอย่าง**: `--force-retune`

### **--reset-flags**
- **คำอธิบาย**: ลบไฟล์ flag และ best_params ก่อนเริ่มเทรน
- **ใช้เมื่อ**: ต้องการเริ่มต้นใหม่ทั้งหมด
- **ตัวอย่าง**: `--reset-flags`

### **--symbols**
- **คำอธิบาย**: เลือก symbols ที่ต้องการเทรน
- **ใช้เมื่อ**: ต้องการเทรนเฉพาะ symbols ที่มีปัญหา
- **ตัวอย่าง**: `--symbols USDJPY GBPUSD EURUSD`

### **--timeframes**
- **คำอธิบาย**: เลือก timeframes ที่ต้องการเทรน
- **ใช้เมื่อ**: ต้องการเทรนเฉพาะ timeframes ที่กำหนด
- **ตัวอย่าง**: `--timeframes M30 H1`

## 🚀 **ขั้นตอนการเทรนใหม่ที่แนะนำ**

### **สำหรับ USDJPY และ GBPUSD (ที่มีปัญหา)**:

#### **ขั้นตอนที่ 1: ตรวจสอบสถานะปัจจุบัน**
```bash
python reset_training.py
# เลือก 1. ตรวจสอบสถานะการเทรนปัจจุบัน
```

#### **ขั้นตอนที่ 2: Reset เฉพาะ symbols ที่มีปัญหา**
```bash
python python_LightGBM_15_Tuning.py --force-retune --reset-flags --symbols USDJPY GBPUSD --timeframes M30 H1
```

#### **ขั้นตอนที่ 3: ตรวจสอบผลลัพธ์**
- ดู AUC ของ USDJPY ว่าเพิ่มขึ้นจาก 0.0
- ดู Accuracy ของ GBPUSD ว่าเพิ่มขึ้นจาก 56.5%
- ตรวจสอบ parameter stability (CV < 30%)

### **สำหรับการเทรนทั้งหมด**:

#### **ขั้นตอนที่ 1: Backup ไฟล์สำคัญ (ถ้าต้องการ)**
```bash
# สำรองโฟลเดอร์ models
cp -r Test_LightGBM/models Test_LightGBM/models_backup_$(date +%Y%m%d)
```

#### **ขั้นตอนที่ 2: Reset และเทรนใหม่**
```bash
python python_LightGBM_15_Tuning.py --force-retune --reset-flags
```

#### **ขั้นตอนที่ 3: Monitor การเทรน**
- ดูการปรับปรุงของ AUC และ F1 Score
- ตรวจสอบ class imbalance warnings
- สังเกต parameter stability

## 📊 **การตรวจสอบผลลัพธ์**

### **ตัวชี้วัดความสำเร็จ**:

#### **USDJPY**:
- ✅ AUC > 0.6 (เป้าหมายจาก 0.0)
- ✅ F1 Score > 0.3 (เป้าหมายจาก 0.33)
- ✅ ไม่มี EXTREME_IMBALANCE warnings

#### **GBPUSD**:
- ✅ Accuracy > 65% (เป้าหมายจาก 56.5%)
- ✅ AUC > 0.85 (เป้าหมายจาก 0.81)
- ✅ F1 Score > 0.5

#### **Parameter Stability**:
- ✅ learning_rate CV < 30% (เป้าหมายจาก 62.7%)
- ✅ num_leaves CV < 25% (เป้าหมายจาก 24.3%)
- ✅ Overall parameter CV < 30%

#### **Trading Performance**:
- ✅ Win Rate > 20% ในบางวัน
- ✅ Expectancy เป็นบวกในบางวัน
- ✅ มีวันที่แนะนำให้เทรด

### **ไฟล์ที่ต้องตรวจสอบ**:

#### **Parameter Stability**:
```
Test_LightGBM/hyperparameter_stability_analysis.txt
```

#### **Daily Trading Schedule**:
```
M30_daily_trading_schedule_summary.txt
M60_daily_trading_schedule_summary.txt
daily_trading_schedule_summary.txt
```

#### **Model Performance**:
```
Test_LightGBM/results/training_summary_*.txt
```

## ⚠️ **ข้อควรระวัง**

### **ก่อนลบไฟล์**:
- สำรองไฟล์สำคัญก่อน (ถ้าต้องการ)
- ตรวจสอบว่าไม่มีการเทรนอื่นกำลังทำงานอยู่
- ยืนยันว่าต้องการเริ่มต้นใหม่จริงๆ

### **ระหว่างการเทรน**:
- ไม่ควรหยุดการเทรนกลางคัน
- ตรวจสอบ log files เป็นระยะ
- สังเกต memory usage และ CPU usage

### **หลังการเทรน**:
- ตรวจสอบผลลัพธ์ทันทีหลังเทรนเสร็จ
- เปรียบเทียบกับผลลัพธ์เดิม
- บันทึกการเปลี่ยนแปลงที่สำคัญ

## 🔧 **Troubleshooting**

### **ถ้าไฟล์ไม่ถูกลบ**:
```bash
# ตรวจสอบสิทธิ์การเข้าถึงไฟล์
ls -la Test_LightGBM/models/

# ลบด้วย force (ระวัง!)
rm -f Test_LightGBM/models/*/*_tuning_flag.json
rm -f Test_LightGBM/models/*/*_best_params.json
```

### **ถ้า Command Line Arguments ไม่ทำงาน**:
```bash
# ตรวจสอบ Python version
python --version

# ใช้ python3 แทน
python3 python_LightGBM_15_Tuning.py --force-retune --reset-flags
```

### **ถ้าการเทรนไม่เริ่ม**:
- ตรวจสอบว่าไฟล์ข้อมูล CSV มีอยู่
- ตรวจสอบ path ของไฟล์
- ดู error messages ใน console

## 📞 **การขอความช่วยเหลือ**

หากพบปัญหาหรือต้องการคำแนะนำ:

1. **ตรวจสอบ log files** ใน Test_LightGBM/
2. **รัน reset_training.py** เพื่อดูสถานะ
3. **ใช้ quick_improvement_test.py** เพื่อทดสอบก่อน
4. **เปรียบเทียบผลลัพธ์** กับ baseline เดิม

---

**หมายเหตุ**: การเทรนใหม่จะใช้เวลานานขึ้นเนื่องจากมีการ hyperparameter tuning ที่เพิ่มขึ้น แต่จะได้ผลลัพธ์ที่ดีกว่าสำหรับ USDJPY และ GBPUSD

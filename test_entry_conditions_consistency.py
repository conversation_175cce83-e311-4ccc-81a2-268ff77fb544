#!/usr/bin/env python3
"""
ทดสอบความสอดคล้องของ entry_conditions ระหว่าง 
python_LightGBM_15_Tuning.py และ python_to_mt5_WebRequest_server_11_Tuning.py
"""

import pandas as pd
import numpy as np

def test_entry_conditions_consistency():
    """
    ทดสอบความสอดคล้องของ entry_conditions
    """
    print("🔍 Testing Entry Conditions Consistency")
    print("=" * 60)
    
    # สร้างข้อมูลทดสอบ
    test_data = {
        'close': 1.1000,
        'open': 1.0990,
        'ema50': 1.0995,
        'ema200': 1.0980,
        'rsi14': 65.0,
        'macd_signal': 1.0,
        'volume': 1200,
        'volume_ma20': 1000,
        'pullback_buy': 0.0015,
        'pullback_sell': 0.0012,
        'ratio_buy': 4.5,
        'ratio_sell': 3.8
    }
    
    # ค่าพารามิเตอร์
    input_rsi_level_in = 60.0
    input_pull_back = 0.001
    input_take_profit = 1.0
    
    print("📊 Test Data:")
    for key, value in test_data.items():
        print(f"  {key}: {value}")
    
    print(f"\n📋 Parameters:")
    print(f"  input_rsi_level_in: {input_rsi_level_in}")
    print(f"  input_pull_back: {input_pull_back}")
    print(f"  input_take_profit: {input_take_profit}")
    
    # ทดสอบ entry_conditions จาก training model
    print(f"\n🎯 Training Model Entry Conditions:")
    
    # default
    default_buy = (
        test_data['close'] > test_data['open'] and
        test_data['rsi14'] > input_rsi_level_in and
        test_data['macd_signal'] == 1.0 and
        test_data['volume'] > test_data['volume_ma20'] * 0.8 and
        test_data['pullback_buy'] > input_pull_back and
        test_data['ratio_buy'] > (input_take_profit * 3.0)
    )
    
    default_sell = (
        test_data['close'] < test_data['open'] and
        test_data['rsi14'] < (100 - input_rsi_level_in) and
        test_data['macd_signal'] == -1.0 and
        test_data['volume'] > test_data['volume_ma20'] * 0.8 and
        test_data['pullback_sell'] > input_pull_back and
        test_data['ratio_sell'] > (input_take_profit * 3.0)
    )
    
    # entry_v1 (ที่ training model ใช้)
    entry_v1_buy = (
        test_data['close'] > test_data['open'] and
        test_data['close'] > test_data['ema50'] and  # เพิ่มเงื่อนไข
        test_data['rsi14'] > input_rsi_level_in and
        test_data['macd_signal'] == 1.0 and
        test_data['volume'] > test_data['volume_ma20'] * 0.8 and
        test_data['pullback_buy'] > input_pull_back and
        test_data['ratio_buy'] > (input_take_profit * 3.0)
    )
    
    entry_v1_sell = (
        test_data['close'] < test_data['open'] and
        test_data['close'] < test_data['ema50'] and  # เพิ่มเงื่อนไข
        test_data['rsi14'] < (100 - input_rsi_level_in) and
        test_data['macd_signal'] == -1.0 and
        test_data['volume'] > test_data['volume_ma20'] * 0.8 and
        test_data['pullback_sell'] > input_pull_back and
        test_data['ratio_sell'] > (input_take_profit * 3.0)
    )
    
    print(f"  default BUY: {default_buy}")
    print(f"  default SELL: {default_sell}")
    print(f"  entry_v1 BUY: {entry_v1_buy}")
    print(f"  entry_v1 SELL: {entry_v1_sell}")
    
    # ทดสอบ WebRequest Server conditions (หลังแก้ไข)
    print(f"\n🌐 WebRequest Server Entry Conditions (After Fix):")
    
    # สร้าง latest_features_dict_all_i2 สำหรับ WebRequest server
    latest_features_dict_all_i2 = {
        'Close': test_data['close'],
        'Open': test_data['open'],
        'EMA50': test_data['ema50'],
        'EMA200': test_data['ema200'],
        'RSI_signal': test_data['rsi14'],
        'MACD_signal': test_data['macd_signal'],
        'Volume': test_data['volume'],
        'Volume_MA20': test_data['volume_ma20'],
        'PullBack_Up': test_data['pullback_buy'],
        'PullBack_Down': test_data['pullback_sell'],
        'Ratio_Buy': test_data['ratio_buy'],
        'Ratio_Sell': test_data['ratio_sell']
    }
    
    # WebRequest server conditions (หลังแก้ไข)
    webserver_buy = (
        (latest_features_dict_all_i2['Close'] > latest_features_dict_all_i2['Open']) and
        (latest_features_dict_all_i2['Close'] > latest_features_dict_all_i2['EMA50']) and  # entry_v1
        (latest_features_dict_all_i2['MACD_signal'] == 1.0) and
        (latest_features_dict_all_i2['RSI_signal'] > input_rsi_level_in) and
        (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80) and
        (latest_features_dict_all_i2['PullBack_Up'] > input_pull_back) and
        (latest_features_dict_all_i2['Ratio_Buy'] > (input_take_profit * 3.0))
    )
    
    webserver_sell = (
        (latest_features_dict_all_i2['Close'] < latest_features_dict_all_i2['Open']) and
        (latest_features_dict_all_i2['Close'] < latest_features_dict_all_i2['EMA50']) and  # entry_v1
        (latest_features_dict_all_i2['MACD_signal'] == -1.0) and
        (latest_features_dict_all_i2['RSI_signal'] < (100-input_rsi_level_in)) and
        (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80) and
        (latest_features_dict_all_i2['PullBack_Down'] > input_pull_back) and
        (latest_features_dict_all_i2['Ratio_Sell'] > (input_take_profit * 3.0))
    )
    
    print(f"  WebServer BUY: {webserver_buy}")
    print(f"  WebServer SELL: {webserver_sell}")
    
    # เปรียบเทียบผลลัพธ์
    print(f"\n✅ Consistency Check:")
    
    buy_consistent = (entry_v1_buy == webserver_buy)
    sell_consistent = (entry_v1_sell == webserver_sell)
    
    print(f"  BUY conditions consistent: {buy_consistent} ({'✅' if buy_consistent else '❌'})")
    print(f"  SELL conditions consistent: {sell_consistent} ({'✅' if sell_consistent else '❌'})")
    
    if buy_consistent and sell_consistent:
        print(f"\n🎉 SUCCESS: Entry conditions are now consistent!")
        print(f"   Training Model (entry_v1) == WebRequest Server")
    else:
        print(f"\n⚠️  WARNING: Entry conditions are still inconsistent!")
        
        if not buy_consistent:
            print(f"   BUY: Training={entry_v1_buy}, WebServer={webserver_buy}")
        if not sell_consistent:
            print(f"   SELL: Training={entry_v1_sell}, WebServer={webserver_sell}")
    
    # ทดสอบกรณีต่างๆ
    print(f"\n🧪 Additional Test Cases:")
    
    # กรณีที่ 1: close < ema50 (ควรไม่ผ่าน BUY)
    test_data_case1 = test_data.copy()
    test_data_case1['close'] = 1.0990  # ต่ำกว่า ema50 (1.0995)
    
    case1_training_buy = (
        test_data_case1['close'] > test_data_case1['open'] and
        test_data_case1['close'] > test_data_case1['ema50'] and
        test_data_case1['rsi14'] > input_rsi_level_in and
        test_data_case1['macd_signal'] == 1.0 and
        test_data_case1['volume'] > test_data_case1['volume_ma20'] * 0.8 and
        test_data_case1['pullback_buy'] > input_pull_back and
        test_data_case1['ratio_buy'] > (input_take_profit * 3.0)
    )
    
    latest_features_case1 = latest_features_dict_all_i2.copy()
    latest_features_case1['Close'] = test_data_case1['close']
    
    case1_webserver_buy = (
        (latest_features_case1['Close'] > latest_features_case1['Open']) and
        (latest_features_case1['Close'] > latest_features_case1['EMA50']) and
        (latest_features_case1['MACD_signal'] == 1.0) and
        (latest_features_case1['RSI_signal'] > input_rsi_level_in) and
        (latest_features_case1['Volume'] > latest_features_case1['Volume_MA20'] * 0.80) and
        (latest_features_case1['PullBack_Up'] > input_pull_back) and
        (latest_features_case1['Ratio_Buy'] > (input_take_profit * 3.0))
    )
    
    print(f"  Case 1 (close < ema50): Training={case1_training_buy}, WebServer={case1_webserver_buy}")
    print(f"    Consistent: {case1_training_buy == case1_webserver_buy} ({'✅' if case1_training_buy == case1_webserver_buy else '❌'})")
    
    return buy_consistent and sell_consistent

if __name__ == "__main__":
    result = test_entry_conditions_consistency()
    print(f"\n{'='*60}")
    if result:
        print("🏁 All tests passed! Entry conditions are consistent.")
    else:
        print("🚨 Tests failed! Entry conditions need further adjustment.")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบระบบบันทึกสรุปการเทรดเป็นไฟล์ .txt
"""

import os
import sys
import pandas as pd
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

# Import functions จากไฟล์หลัก
from python_LightGBM_15_Tuning import (
    save_trading_summary_to_file,
    print_trading_schedule_summary,
    generate_trading_schedule_summary
)

def test_save_trading_summary():
    """
    ทดสอบการบันทึกสรุปการเทรดแต่ละ symbol
    """
    print("🧪 ทดสอบการบันทึกสรุปการเทรดแต่ละ symbol")
    print("=" * 60)
    
    # สร้างข้อมูลทดสอบ
    symbol = "GBPUSD"
    timeframe = 30
    
    # Mock result_dict
    result_dict = {
        'metrics': {
            'accuracy': 0.7014,
            'auc': 0.8500,
            'f1': 0.6200,
            'precision': 0.6800,
            'recall': 0.5700
        }
    }
    
    # Mock trade_stats
    trade_stats = {
        'total_trades': 83,
        'winning_trades': 29,
        'losing_trades': 54,
        'win_rate': 34.78,
        'total_profit': 125.50,
        'avg_profit_per_trade': 1.51,
        'expectancy': 1.51,
        'best_entry_condition': 'entry_v1',
        'entry_conditions_summary': {
            'entry_v1': {'win_rate': 34.78, 'trades': 32},
            'entry_v2': {'win_rate': 28.50, 'trades': 25},
            'entry_v3': {'win_rate': 31.20, 'trades': 26}
        }
    }
    
    # ทดสอบการบันทึกไฟล์
    filepath = save_trading_summary_to_file(symbol, timeframe, result_dict, trade_stats)
    
    if filepath and os.path.exists(filepath):
        print(f"✅ บันทึกไฟล์สำเร็จ: {filepath}")
        
        # อ่านและแสดงเนื้อหาไฟล์
        print("\n📄 เนื้อหาในไฟล์:")
        print("-" * 40)
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            print(content)
    else:
        print("❌ การบันทึกไฟล์ล้มเหลว")

def test_daily_trading_schedule():
    """
    ทดสอบการบันทึกสรุปการเทรดรายวัน
    """
    print("\n🧪 ทดสอบการบันทึกสรุปการเทรดรายวัน")
    print("=" * 60)
    
    try:
        # เรียกใช้ฟังก์ชันสรุปการเทรดรายวัน
        print_trading_schedule_summary()
        
        # ตรวจสอบว่าไฟล์ถูกสร้างหรือไม่
        expected_file = "Test_LightGBM/results/daily_trading_schedule_summary.txt"
        if os.path.exists(expected_file):
            print(f"✅ บันทึกไฟล์สรุปรายวันสำเร็จ: {expected_file}")
            
            # แสดงเนื้อหาไฟล์
            print("\n📄 เนื้อหาในไฟล์:")
            print("-" * 40)
            with open(expected_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(content[:500] + "..." if len(content) > 500 else content)
        else:
            print("❌ ไม่พบไฟล์สรุปรายวัน (อาจเป็นเพราะยังไม่มีข้อมูล time filters)")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {str(e)}")

def test_file_structure():
    """
    ทดสอบโครงสร้างไฟล์และโฟลเดอร์
    """
    print("\n🧪 ทดสอบโครงสร้างไฟล์และโฟลเดอร์")
    print("=" * 60)
    
    # ตรวจสอบโฟลเดอร์ผลลัพธ์
    results_folder = "Test_LightGBM/results"
    if not os.path.exists(results_folder):
        os.makedirs(results_folder, exist_ok=True)
        print(f"✅ สร้างโฟลเดอร์: {results_folder}")
    else:
        print(f"✅ โฟลเดอร์มีอยู่แล้ว: {results_folder}")
    
    # แสดงไฟล์ที่มีอยู่ในโฟลเดอร์
    if os.path.exists(results_folder):
        files = [f for f in os.listdir(results_folder) if f.endswith('.txt')]
        print(f"\n📁 ไฟล์ .txt ในโฟลเดอร์ results ({len(files)} ไฟล์):")
        for file in files:
            file_path = os.path.join(results_folder, file)
            file_size = os.path.getsize(file_path)
            mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            print(f"  📄 {file} ({file_size} bytes, แก้ไขล่าสุด: {mod_time.strftime('%Y-%m-%d %H:%M:%S')})")

def main():
    """
    ฟังก์ชันหลักสำหรับทดสอบ
    """
    print("🚀 เริ่มทดสอบระบบบันทึกไฟล์สรุปการเทรด")
    print("=" * 80)
    
    # ทดสอบโครงสร้างไฟล์
    test_file_structure()
    
    # ทดสอบการบันทึกสรุปการเทรดแต่ละ symbol
    test_save_trading_summary()
    
    # ทดสอบการบันทึกสรุปการเทรดรายวัน
    test_daily_trading_schedule()
    
    print("\n🏁 การทดสอบเสร็จสิ้น")
    print("=" * 80)
    
    # แสดงคำแนะนำ
    print("\n💡 คำแนะนำ:")
    print("1. ไฟล์สรุปการเทรดแต่ละ symbol จะถูกสร้างอัตโนมัติเมื่อเทรนเสร็จ")
    print("2. ไฟล์สรุปการเทรดรายวันจะถูกสร้างเมื่อเทรนครบทั้งหมด")
    print("3. ไฟล์ทั้งหมดจะถูกบันทึกในโฟลเดอร์ Test_LightGBM/results/")
    print("4. สามารถเปิดไฟล์ .txt ด้วย Notepad หรือ Text Editor ใดๆ")

if __name__ == "__main__":
    main()

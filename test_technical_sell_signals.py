#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขเงื่อนไขทางเทคนิคสำหรับ Sell signals
"""

import os
import sys
import pandas as pd
import numpy as np

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_technical_sell_conditions():
    """ทดสอบเงื่อนไขทางเทคนิคสำหรับ Sell"""
    
    print("🧪 ทดสอบเงื่อนไขทางเทคนิคสำหรับ Sell Signals")
    print("="*70)
    
    try:
        from python_LightGBM_16_Signal import (
            input_rsi_level_in, 
            input_pull_back,
            input_take_profit
        )
        
        print(f"📊 พารามิเตอร์ปัจจุบัน:")
        print(f"   RSI Level: {input_rsi_level_in}")
        print(f"   Pull Back: {input_pull_back}")
        print(f"   Take Profit Ratio: {input_take_profit}")
        
        # สร้างข้อมูลทดสอบที่จำลองเงื่อนไขต่างๆ
        test_cases = [
            {
                'name': 'Strong Sell Signal',
                'close': 1.0500,
                'open': 1.0520,  # close < open ✅
                'rsi14': 75,     # > (100 - 35) = 65 ✅
                'macd_signal': -1.0,  # bearish ✅
                'volume': 1200,
                'volume_ma20': 1000,  # volume > volume_ma20 * 0.8 ✅
                'pullback_sell': 0.45,  # > 0.40 ✅
                'ratio_sell': 8.0,     # > (2.5 * 3.0) = 7.5 ✅
                'expected': True
            },
            {
                'name': 'Weak Sell Signal (Low RSI)',
                'close': 1.0500,
                'open': 1.0520,  # close < open ✅
                'rsi14': 60,     # < 65 ❌
                'macd_signal': -1.0,
                'volume': 1200,
                'volume_ma20': 1000,
                'pullback_sell': 0.45,
                'ratio_sell': 8.0,
                'expected': False
            },
            {
                'name': 'Weak Sell Signal (Low Volume)',
                'close': 1.0500,
                'open': 1.0520,  # close < open ✅
                'rsi14': 75,     # > 65 ✅
                'macd_signal': -1.0,
                'volume': 700,   # < 800 ❌
                'volume_ma20': 1000,
                'pullback_sell': 0.45,
                'ratio_sell': 8.0,
                'expected': False
            },
            {
                'name': 'Bullish Market (No Sell)',
                'close': 1.0520,
                'open': 1.0500,  # close > open ❌
                'rsi14': 75,
                'macd_signal': 1.0,  # bullish ❌
                'volume': 1200,
                'volume_ma20': 1000,
                'pullback_sell': 0.45,
                'ratio_sell': 8.0,
                'expected': False
            }
        ]
        
        print(f"\n🎯 ทดสอบเงื่อนไขทางเทคนิค:")
        print("-" * 70)
        
        for test_case in test_cases:
            print(f"\n📊 {test_case['name']}:")
            
            # ทดสอบแต่ละเงื่อนไข
            cond1 = test_case['close'] < test_case['open']
            cond2 = test_case['rsi14'] > (100 - input_rsi_level_in)
            cond3 = test_case['macd_signal'] == -1.0
            cond4 = test_case['volume'] > test_case['volume_ma20'] * 0.8
            cond5 = test_case['pullback_sell'] > input_pull_back
            cond6 = test_case['ratio_sell'] > (input_take_profit * 3.0)
            
            overall = cond1 and cond2 and cond3 and cond4 and cond5 and cond6
            
            print(f"   📈 Close < Open: {cond1} ({test_case['close']:.4f} < {test_case['open']:.4f})")
            print(f"   📊 RSI > {100 - input_rsi_level_in}: {cond2} ({test_case['rsi14']:.1f})")
            print(f"   📉 MACD Bearish: {cond3} ({test_case['macd_signal']})")
            print(f"   📊 Volume OK: {cond4} ({test_case['volume']} > {test_case['volume_ma20'] * 0.8:.0f})")
            print(f"   📈 PullBack > {input_pull_back}: {cond5} ({test_case['pullback_sell']:.2f})")
            print(f"   📊 Ratio > {input_take_profit * 3.0}: {cond6} ({test_case['ratio_sell']:.1f})")
            
            result = "✅ SELL" if overall else "❌ NO SELL"
            expected = "✅ SELL" if test_case['expected'] else "❌ NO SELL"
            correct = "✅" if overall == test_case['expected'] else "❌"
            
            print(f"   🎯 Result: {result} | Expected: {expected} {correct}")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def check_model_availability():
    """ตรวจสอบว่ามี ML model หรือไม่"""
    
    print(f"\n🔍 ตรวจสอบความพร้อมของ ML Model")
    print("="*50)
    
    try:
        # ตรวจสอบโฟลเดอร์โมเดล
        model_dirs = [
            "LightGBM_Multi/models/trend_following",
            "LightGBM_Multi/models/counter_trend",
            "LightGBM_Model_Multi",
            "LightGBM_Model_Single"
        ]
        
        models_found = False
        
        for model_dir in model_dirs:
            if os.path.exists(model_dir):
                model_files = [f for f in os.listdir(model_dir) if f.endswith('_trained.pkl')]
                if model_files:
                    print(f"✅ {model_dir}: {len(model_files)} models")
                    models_found = True
                    
                    # แสดงตัวอย่างไฟล์
                    for file in model_files[:3]:
                        print(f"   📁 {file}")
                else:
                    print(f"❌ {model_dir}: ไม่มีไฟล์โมเดล")
            else:
                print(f"❌ {model_dir}: ไม่พบโฟลเดอร์")
        
        if not models_found:
            print(f"\n⚠️ ไม่พบ ML models - ระบบจะใช้เงื่อนไขทางเทคนิคเท่านั้น")
            print(f"💡 นี่คือสาเหตุที่ไม่มี Sell signals ก่อนหน้านี้!")
        else:
            print(f"\n✅ พบ ML models - ระบบจะใช้ทั้ง ML และเงื่อนไขทางเทคนิค")
        
        return models_found
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def simulate_trading_scenario():
    """จำลองสถานการณ์การเทรดที่ควรมี Sell signals"""
    
    print(f"\n🎮 จำลองสถานการณ์การเทรดที่ควรมี Sell Signals")
    print("="*60)
    
    # สร้างข้อมูลที่ควรให้ sell signals
    scenarios = [
        {
            'time': '2023-01-01 10:00',
            'market': 'Bearish Trend',
            'rsi': 78,
            'macd': -1,
            'volume_ratio': 1.5,
            'pullback': 0.45,
            'ratio': 8.5
        },
        {
            'time': '2023-01-01 14:00',
            'market': 'Overbought',
            'rsi': 82,
            'macd': -1,
            'volume_ratio': 1.2,
            'pullback': 0.42,
            'ratio': 9.0
        },
        {
            'time': '2023-01-01 16:00',
            'market': 'Strong Bearish',
            'rsi': 75,
            'macd': -1,
            'volume_ratio': 2.0,
            'pullback': 0.50,
            'ratio': 10.0
        }
    ]
    
    print("📊 สถานการณ์ที่ควรให้ SELL signals:")
    print("-" * 60)
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n🕐 Scenario {i}: {scenario['time']} - {scenario['market']}")
        print(f"   📊 RSI: {scenario['rsi']} (> 65 ✅)")
        print(f"   📉 MACD: {scenario['macd']} (Bearish ✅)")
        print(f"   📊 Volume: {scenario['volume_ratio']}x average (> 0.8 ✅)")
        print(f"   📈 PullBack: {scenario['pullback']} (> 0.40 ✅)")
        print(f"   📊 Ratio: {scenario['ratio']} (> 7.5 ✅)")
        print(f"   🎯 Expected: ✅ SELL SIGNAL")

def test_log_output():
    """ทดสอบการ log output ที่เพิ่มเข้ามา"""
    
    print(f"\n📝 ทดสอบการ Log Output ใหม่")
    print("="*50)
    
    print("🔍 Debug messages ที่ควรเห็นใน console:")
    print("   🎯 SELL Technical Signal: RSI=75.0, MACD=-1.0, PullBack=0.450")
    print("   🎯 SELL Technical Only: Decision=ENTER (no ML model)")
    
    print("\n📄 Log messages ที่ควรเห็นใน trade_log:")
    print("   [2023-01-01 10:00:00] SELL Signal Detected (Technical Only) based on data up to [2023-01-01 09:30:00]. No ML Model. Decision: ENTER")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🏗️ Test Technical Sell Signals Fix")
    print("="*60)
    
    # ทดสอบเงื่อนไขทางเทคนิค
    test_technical_sell_conditions()
    
    # ตรวจสอบความพร้อมของ ML model
    models_available = check_model_availability()
    
    # จำลองสถานการณ์การเทรด
    simulate_trading_scenario()
    
    # ทดสอบการ log output
    test_log_output()
    
    print(f"\n✅ การทดสอบเสร็จสิ้น")
    print(f"\n📋 สรุปการแก้ไข:")
    print(f"   ✅ เพิ่ม debug messages สำหรับเงื่อนไขทางเทคนิค")
    print(f"   ✅ เพิ่ม log เมื่อไม่ใช้ ML model")
    print(f"   ✅ ให้ model_decision_sell = True เมื่อเงื่อนไขทางเทคนิคเป็นจริง")
    
    if not models_available:
        print(f"\n🎯 สาเหตุที่ไม่มี Sell signals:")
        print(f"   ❌ ไม่มี ML models")
        print(f"   ❌ เงื่อนไขทางเทคนิคไม่ถูกใช้เมื่อไม่มี ML model")
        print(f"   ✅ แก้ไขแล้ว: ตอนนี้จะใช้เงื่อนไขทางเทคนิคแม้ไม่มี ML model")
    
    print(f"\n🚀 ขั้นตอนต่อไป:")
    print(f"   1. รัน python_LightGBM_16_Signal.py")
    print(f"   2. ดู console messages: '🎯 SELL Technical Signal'")
    print(f"   3. ตรวจสอบไฟล์ trade_log ว่ามี SELL signals")

if __name__ == "__main__":
    main()

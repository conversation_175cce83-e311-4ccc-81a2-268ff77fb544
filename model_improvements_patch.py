# Patch สำหรับ python_LightGBM_15_Tuning.py\n# วันที่: 2025-07-03 19:29:10\n\n# get_optimal_class_weight\n
def get_optimal_class_weight(y):
    """คำนวณ class weight ที่เหมาะสม"""
    class_counts = pd.Series(y).value_counts()
    minority_class = class_counts.idxmin()
    majority_class = class_counts.idxmax()
    
    ratio = class_counts[majority_class] / class_counts[minority_class]
    
    if ratio > 5:  # Severe imbalance
        return {majority_class: 1, minority_class: 5}
    elif ratio > 3:  # Moderate imbalance
        return {majority_class: 1, minority_class: 3}
    else:
        return "balanced"
\n\n# find_optimal_threshold\n
def find_optimal_threshold(y_true, y_proba, metric='f1'):
    """หา threshold ที่เหมาะสมสำหรับ metric ที่กำหนด"""
    thresholds = np.arange(0.1, 0.9, 0.01)
    scores = []
    
    for threshold in thresholds:
        y_pred = (y_proba >= threshold).astype(int)
        if metric == 'f1':
            score = f1_score(y_true, y_pred, zero_division=0)
        elif metric == 'precision':
            score = precision_score(y_true, y_pred, zero_division=0)
        elif metric == 'recall':
            score = recall_score(y_true, y_pred, zero_division=0)
        scores.append(score)
    
    optimal_idx = np.argmax(scores)
    return thresholds[optimal_idx], scores[optimal_idx]
\n\n# updated_param_dist\n
# อัปเดต param_dist สำหรับลด overfitting
param_dist = {
    'learning_rate': [0.03, 0.05, 0.08],
    'num_leaves': [6, 8, 10],
    'max_depth': [3, 4, 5],
    'min_data_in_leaf': [20, 25, 30],
    'reg_alpha': [0.1, 0.2, 0.3],
    'reg_lambda': [0.1, 0.2, 0.3],
    'feature_fraction': [0.7, 0.8, 0.9],
    'bagging_fraction': [0.7, 0.8, 0.9],
}
\n\n# get_lgbm_params\n
def get_lgbm_params(y_train):
    """ปรับปรุงพารามิเตอร์ LightGBM พร้อม class weight อัตโนมัติ"""
    
    # คำนวณ class weight อัตโนมัติ
    class_weight = get_optimal_class_weight(y_train)
    
    return {
        'objective': 'binary',
        'metric': 'auc',
        'boosting_type': 'gbdt',
        'learning_rate': 0.05,        # ลดลงจาก 0.139
        'num_leaves': 8,              # ลดลงจาก 13
        'max_depth': 4,               # ลดลงจาก 6
        'min_data_in_leaf': 25,       # เพิ่มจาก 12
        'reg_alpha': 0.2,             # เพิ่ม regularization
        'reg_lambda': 0.2,            # เพิ่ม regularization
        'feature_fraction': 0.8,      # เพิ่ม feature sampling
        'bagging_fraction': 0.8,      # เพิ่ม data sampling
        'bagging_freq': 5,
        'class_weight': class_weight, # เพิ่ม class weight อัตโนมัติ
        'verbose': -1,
        'random_state': 42,
        'n_jobs': -1
    }
\n\n# create_market_regime_features\n
def create_market_regime_features(df):
    """สร้าง Market Regime Detection Features"""
    
    # 1. Trend Strength (ADX-based)
    df['ADX_14'] = talib.ADX(df['High'], df['Low'], df['Close'], timeperiod=14)
    df['Trend_Strength_Score'] = pd.cut(df['ADX_14'], 
                                       bins=[0, 25, 50, 100], 
                                       labels=[1, 2, 3]).astype(float)
    
    # 2. Market Volatility Regime
    df['ATR_14'] = talib.ATR(df['High'], df['Low'], df['Close'], timeperiod=14)
    df['ATR_MA_50'] = df['ATR_14'].rolling(50).mean()
    df['Volatility_Regime_Score'] = np.where(df['ATR_14'] > df['ATR_MA_50'] * 1.5, 3,
                                    np.where(df['ATR_14'] < df['ATR_MA_50'] * 0.7, 1, 2))
    
    # 3. Price Position in Range
    df['High_20'] = df['High'].rolling(20).max()
    df['Low_20'] = df['Low'].rolling(20).min()
    df['Price_Position'] = (df['Close'] - df['Low_20']) / (df['High_20'] - df['Low_20'])
    
    # 4. Trend Direction Consensus
    trend_scores = []
    for period in [10, 20, 50]:
        df[f'SMA_{period}'] = df['Close'].rolling(period).mean()
        trend_score = np.where(df['Close'] > df[f'SMA_{period}'], 1, 
                      np.where(df['Close'] < df[f'SMA_{period}'], -1, 0))
        trend_scores.append(trend_score)
    
    df['Trend_Consensus'] = np.sum(trend_scores, axis=0)
    
    return df
\n\n# enhanced_evaluation_v2\n
def enhanced_evaluation_v2(model, X_test, y_test, output_folder, symbol, timeframe):
    """การประเมินผลแบบละเอียดพร้อม optimal threshold"""
    
    # ทำนาย probability
    y_probs = model.predict_proba(X_test)[:, 1]
    
    # หา optimal threshold
    optimal_threshold, optimal_f1 = find_optimal_threshold(y_test, y_probs, 'f1')
    
    # ทำนายด้วย default threshold (0.5)
    y_pred_default = (y_probs > 0.5).astype(int)
    
    # ทำนายด้วย optimal threshold
    y_pred_optimal = (y_probs > optimal_threshold).astype(int)
    
    # คำนวณ metrics สำหรับทั้งสอง threshold
    metrics = {
        'timeframe': timeframe,
        'optimal_threshold': optimal_threshold,
        
        # Default threshold metrics
        'accuracy_default': accuracy_score(y_test, y_pred_default),
        'auc_roc': roc_auc_score(y_test, y_probs),
        'f1_default': f1_score(y_test, y_pred_default),
        'precision_default': precision_score(y_test, y_pred_default, zero_division=0),
        'recall_default': recall_score(y_test, y_pred_default, zero_division=0),
        
        # Optimal threshold metrics
        'accuracy_optimal': accuracy_score(y_test, y_pred_optimal),
        'f1_optimal': optimal_f1,
        'precision_optimal': precision_score(y_test, y_pred_optimal, zero_division=0),
        'recall_optimal': recall_score(y_test, y_pred_optimal, zero_division=0),
        
        'confusion_matrix_default': confusion_matrix(y_test, y_pred_default),
        'confusion_matrix_optimal': confusion_matrix(y_test, y_pred_optimal)
    }
    
    # แสดงผลลัพธ์
    print(f"\n📊 ผลการประเมิน {symbol} {timeframe}:")
    print(f"🎯 Optimal Threshold: {optimal_threshold:.3f}")
    print(f"📈 F1 Score: {metrics['f1_default']:.4f} → {metrics['f1_optimal']:.4f}")
    print(f"📈 Precision: {metrics['precision_default']:.4f} → {metrics['precision_optimal']:.4f}")
    print(f"📈 Recall: {metrics['recall_default']:.4f} → {metrics['recall_optimal']:.4f}")
    
    return metrics
\n\n
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Threshold Issues Fix
ทดสอบการแก้ไขปัญหา threshold ต่างๆ
"""

import pandas as pd
import numpy as np
import sys
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

# เพิ่ม path สำหรับ import
sys.path.append('.')

def create_challenging_test_data():
    """
    สร้างข้อมูลทดสอบที่ท้าทาย (โมเดลทำนาย positive น้อย)
    """
    print("📊 Creating challenging test data (low positive predictions)")
    
    np.random.seed(42)
    n_samples = 1000
    
    # สร้าง features
    features = ['RSI14', 'MACD', 'EMA50', 'EMA200', 'Volume_MA20', 'ATR', 'Close']
    data = {}
    
    for feature in features:
        if feature == 'RSI14':
            data[feature] = np.random.uniform(30, 70, n_samples)  # RSI ปกติ
        elif feature == 'Close':
            data[feature] = 1800 + np.random.randn(n_samples) * 10  # GOLD price
        elif feature == 'ATR':
            data[feature] = np.random.uniform(5, 15, n_samples)  # ATR for GOLD
        else:
            data[feature] = np.random.randn(n_samples)
    
    # สร้าง Target ที่มี positive น้อย (เลียนแบบปัญหาจริง)
    # ใช้เงื่อนไขที่เข้มงวดเพื่อให้ positive น้อย
    target_prob = 0.15  # เพียง 15% เป็น positive
    data['Target'] = np.random.binomial(1, target_prob, n_samples)
    
    val_df = pd.DataFrame(data)
    
    print(f"✅ Created challenging data:")
    print(f"   - Samples: {len(val_df)}")
    print(f"   - Features: {len(features)}")
    print(f"   - Target distribution: {val_df['Target'].value_counts().to_dict()}")
    print(f"   - Positive ratio: {val_df['Target'].mean():.3f}")
    
    return val_df, features

def create_biased_model(features):
    """
    สร้างโมเดลที่มี bias ต่ำ (ทำนาย positive น้อย)
    """
    print("🤖 Creating biased model (low positive predictions)")
    
    # สร้าง training data ที่มี positive น้อย
    n_train = 2000
    X_train = np.random.randn(n_train, len(features))
    
    # สร้าง y_train ที่มี positive เพียง 10%
    y_train = np.random.binomial(1, 0.1, n_train)
    
    # เทรนโมเดล
    model = RandomForestClassifier(n_estimators=50, random_state=42)
    model.fit(X_train, y_train)
    
    # สร้าง scaler
    scaler = StandardScaler()
    scaler.fit(X_train)
    
    print(f"✅ Model trained with {(y_train == 1).sum()}/{len(y_train)} positive samples")
    
    return model, scaler

def test_create_profit_function():
    """
    ทดสอบฟังก์ชันสร้าง Profit
    """
    print(f"\n🧪 Testing create_profit_from_target function")
    print("-" * 60)
    
    try:
        from python_LightGBM_16_Signal import create_profit_from_target
        
        # สร้างข้อมูลทดสอบ
        val_df, features = create_challenging_test_data()
        
        # ทดสอบการสร้าง Profit
        val_df_with_profit = create_profit_from_target(val_df, "GOLD")
        
        # ตรวจสอบผลลัพธ์
        if 'Profit' in val_df_with_profit.columns:
            profit_stats = {
                'mean': val_df_with_profit['Profit'].mean(),
                'std': val_df_with_profit['Profit'].std(),
                'positive_ratio': (val_df_with_profit['Profit'] > 0).mean(),
                'target_positive_ratio': val_df_with_profit['Target'].mean()
            }
            
            print(f"✅ Profit column created successfully:")
            print(f"   📊 Mean: {profit_stats['mean']:.2f}")
            print(f"   📊 Std: {profit_stats['std']:.2f}")
            print(f"   📊 Positive profit ratio: {profit_stats['positive_ratio']:.3f}")
            print(f"   📊 Target positive ratio: {profit_stats['target_positive_ratio']:.3f}")
            
            # ตรวจสอบความสัมพันธ์ระหว่าง Target และ Profit
            correlation = val_df_with_profit['Target'].corr(val_df_with_profit['Profit'])
            print(f"   📊 Target-Profit correlation: {correlation:.3f}")
            
            return True
        else:
            print(f"❌ Profit column not created")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_flexible_threshold_criteria():
    """
    ทดสอบเกณฑ์ threshold ที่ยืดหยุ่น
    """
    print(f"\n🧪 Testing flexible threshold criteria")
    print("-" * 60)
    
    try:
        from python_LightGBM_16_Signal import find_best_threshold_simple
        
        # สร้างข้อมูลทดสอบ
        val_df, features = create_challenging_test_data()
        model, scaler = create_biased_model(features)
        
        # เพิ่ม Profit
        from python_LightGBM_16_Signal import create_profit_from_target
        val_df = create_profit_from_target(val_df, "GOLD")
        
        print(f"\n📊 Testing with challenging data:")
        print(f"   - Target positive ratio: {val_df['Target'].mean():.3f}")
        
        # ทดสอบ threshold optimization
        result = find_best_threshold_simple(
            model=model,
            scaler=scaler,
            val_df=val_df,
            model_features=features
        )
        
        print(f"✅ Threshold optimization completed!")
        print(f"📊 Selected threshold: {result}")
        
        # ตรวจสอบว่าได้ threshold ที่สมเหตุสมผล
        if 0.1 <= result <= 0.9:
            print(f"✅ Threshold is reasonable: {result}")
            return True
        else:
            print(f"⚠️ Threshold seems unusual: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_backtest_with_profit():
    """
    ทดสอบ Enhanced Backtest กับ Profit ที่สร้างขึ้น
    """
    print(f"\n🧪 Testing Enhanced Backtest with created Profit")
    print("-" * 60)
    
    try:
        from python_LightGBM_16_Signal import (
            find_optimal_threshold_enhanced_backtest,
            analyze_market_conditions_for_threshold
        )
        
        # สร้างข้อมูลทดสอบ
        val_df, features = create_challenging_test_data()
        model, scaler = create_biased_model(features)
        
        # เพิ่ม Profit
        from python_LightGBM_16_Signal import create_profit_from_target
        val_df = create_profit_from_target(val_df, "GOLD")
        
        # วิเคราะห์ market conditions
        market_analysis = analyze_market_conditions_for_threshold(val_df, "GOLD")
        
        print(f"\n📊 Testing Enhanced Backtest:")
        
        # ทดสอบ Enhanced Backtest
        result = find_optimal_threshold_enhanced_backtest(
            model=model,
            scaler=scaler,
            val_df=val_df,
            model_features=features,
            scenario_name="trend_following",
            market_analysis=market_analysis,
            symbol="GOLD"
        )
        
        print(f"✅ Enhanced Backtest completed!")
        print(f"📊 Result: {result}")
        
        # ตรวจสอบผลลัพธ์
        if result.get('status') == 'success':
            print(f"✅ Enhanced Backtest successful with threshold: {result['best_threshold']}")
            return True
        else:
            print(f"⚠️ Enhanced Backtest status: {result.get('status', 'unknown')}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_comprehensive_test():
    """
    รันการทดสอบแบบครอบคลุม
    """
    print("🧪 Threshold Issues Fix - Comprehensive Test")
    print("=" * 80)
    
    # ตั้งค่า USE_MULTI_MODEL_ARCHITECTURE สำหรับการทดสอบ
    import python_LightGBM_16_Signal
    python_LightGBM_16_Signal.USE_MULTI_MODEL_ARCHITECTURE = True
    
    tests = [
        ("Create Profit Function", test_create_profit_function),
        ("Flexible Threshold Criteria", test_flexible_threshold_criteria),
        ("Enhanced Backtest with Profit", test_enhanced_backtest_with_profit)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        try:
            success = test_func()
            results[test_name] = "✅ PASSED" if success else "❌ FAILED"
        except Exception as e:
            results[test_name] = f"❌ ERROR: {str(e)}"
    
    # สรุปผลการทดสอบ
    print(f"\n📊 Test Results Summary")
    print("=" * 80)
    
    for test_name, result in results.items():
        print(f"{test_name}: {result}")
    
    all_passed = all("✅ PASSED" in result for result in results.values())
    
    if all_passed:
        print(f"\n🎉 All tests passed! The threshold issues have been fixed.")
        print(f"\n💡 Key Improvements:")
        print(f"   ✅ Flexible threshold criteria (min 20 predictions for Multi-Model)")
        print(f"   ✅ Extended threshold range (0.1-0.9 with 0.02 steps)")
        print(f"   ✅ Automatic Profit generation from Target")
        print(f"   ✅ Enhanced Backtest now works with generated Profit")
        print(f"   ✅ Graceful handling of low positive prediction scenarios")
    else:
        print(f"\n⚠️ Some tests failed. Please check the implementation.")
    
    return results

if __name__ == "__main__":
    results = run_comprehensive_test()
    
    print(f"\n📝 Test completed.")
    print(f"💡 The threshold system should now handle challenging scenarios gracefully!")
    print(f"🎯 Ready for production use with improved robustness.")

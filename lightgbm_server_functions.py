#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ฟังก์ชันสำหรับ Server เฉพาะ - แยกออกจาก python_LightGBM_17_Signal.py
เพื่อหลีกเลี่ยงการรันการทดสอบอัตโนมัติ
"""

import os
import sys
import json
import datetime
import pandas as pd
import numpy as np
import pandas_ta as ta
import joblib
import pickle
from sklearn.preprocessing import StandardScaler

# ==============================================
# การตั้งค่าและกำหนดค่าพื้นฐาน (Configuration)
# ==============================================

# Multi-Model Architecture Configuration
USE_MULTI_MODEL_ARCHITECTURE = True  # เปิด/ปิด การใช้ 2 โมเดลแยกตามสถานการณ์ (trend_following, counter_trend)

# Multi-class Target Configuration
USE_MULTICLASS_TARGET = True        # เปิด/ปิด multi-class classification

# ปรับ Profit Thresholds เพื่อเพิ่ม win rate (เน้น quality over quantity)
PROFIT_THRESHOLDS = {
    'strong_buy': 60,               # ลดจาก 80 เป็น 60 points = Strong Buy (class 4)
    'weak_buy': 20,                 # ลดจาก 25 เป็น 20 points = Weak Buy (class 3)
    'no_trade': -20,                # ปรับจาก -25 เป็น -20 points = No Trade (class 2)
    'weak_sell': -60,               # ปรับจาก -80 เป็น -60 points = Weak Sell (class 1)
    'strong_sell': float('-inf')    # ขาดทุนเกิน 60 points = Strong Sell (class 0)
}

# Class mapping for multi-class target
CLASS_MAPPING = {
    0: 'strong_sell',    # ขาดทุนมาก
    1: 'weak_sell',      # ขาดทุนปานกลาง
    2: 'no_trade',       # ไม่ควรเทรด
    3: 'weak_buy',       # กำไรปานกลาง
    4: 'strong_buy'      # กำไรมาก
}

# Market scenarios for Multi-Model Architecture
MARKET_SCENARIOS = {
    'trend_following': {
        'description': 'เทรนด์ขาขึ้น - ราคาอยู่เหนือ EMA200',
        'condition': 'close_above_ema200',
        'strategy': 'ซื้อขายตามเทรนด์'
    },
    'counter_trend': {
        'description': 'เทรนด์ขาลง - ราคาอยู่ใต้ EMA200',
        'condition': 'close_below_ema200',
        'strategy': 'ซื้อขายย้อนเทรนด์'
    }
}

# กำหนดโฟลเดอร์
test_folder = "LightGBM_Multi" if USE_MULTI_MODEL_ARCHITECTURE else "LightGBM_Single"
test_hyper = "LightGBM_Hyper_Multi" if USE_MULTI_MODEL_ARCHITECTURE else "LightGBM_Hyper_Single"

# ==============================================
# Market Condition Detection Functions
# ==============================================

def detect_market_scenario(row):
    """
    ตรวจจับสถานการณ์ตลาดสำหรับการเลือกโมเดลที่เหมาะสม
    
    Args:
        row: pandas Series ที่มีข้อมูลราคาและ indicators
        
    Returns:
        str: ชื่อ scenario ('trend_following' หรือ 'counter_trend')
    """
    try:
        # ตรวจสอบตำแหน่งราคาเทียบกับ EMA200
        close_price = row.get('Close', 0)
        ema200 = row.get('EMA_200', 0)
        
        if close_price > ema200:
            return 'trend_following'  # ราคาอยู่เหนือ EMA200 = เทรนด์ขาขึ้น
        else:
            return 'counter_trend'    # ราคาอยู่ใต้ EMA200 = เทรนด์ขาลง
            
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดในการตรวจจับ market scenario: {e}")
        return 'trend_following'  # default scenario

def load_scenario_models(symbol, timeframe, base_folder=f"{test_folder}/models"):
    """
    โหลดโมเดลทั้ง 2 scenarios (ปรับปรุงใหม่)
    
    Args:
        symbol: สัญลักษณ์ (เช่น AUDUSD, GOLD, USDJPY)
        timeframe: timeframe (เช่น 60 สำหรับ H1)
        base_folder: โฟลเดอร์ที่เก็บโมเดล
        
    Returns:
        dict: โมเดลทั้ง 2 scenarios (trend_following, counter_trend)
    """
    models = {}
    
    print(f"🔍 กำลังโหลดโมเดลสำหรับ {symbol} M{timeframe}")
    print(f"📁 Base folder: {base_folder}")
    
    for scenario_name in MARKET_SCENARIOS.keys():
        scenario_folder = os.path.join(base_folder, scenario_name)
        
        # ใช้รูปแบบชื่อไฟล์ที่ตรงกับการบันทึกจริง
        model_path = os.path.join(scenario_folder, f"{str(timeframe).zfill(3)}_{symbol}_trained.pkl")
        feature_path = os.path.join(scenario_folder, f"{str(timeframe).zfill(3)}_{symbol}_features.pkl")
        scaler_path = os.path.join(scenario_folder, f"{str(timeframe).zfill(3)}_{symbol}_scaler.pkl")
        
        print(f"\n🔍 ตรวจสอบ {scenario_name}:")
        print(f"  📄 Model: {model_path}")
        print(f"  📄 Features: {feature_path}")
        print(f"  📄 Scaler: {scaler_path}")
        
        if os.path.exists(model_path) and os.path.exists(feature_path) and os.path.exists(scaler_path):
            try:
                model = joblib.load(model_path)
                features = joblib.load(feature_path)
                scaler = joblib.load(scaler_path)
                
                models[scenario_name] = {
                    'model': model,
                    'features': features,
                    'scaler': scaler,
                    'model_path': model_path,
                    'feature_path': feature_path,
                    'scaler_path': scaler_path
                }
                print(f"✅ โหลดโมเดล {scenario_name} สำเร็จ")
                print(f"  📊 Features: {len(features)} features")
                
            except Exception as e:
                print(f"❌ ไม่สามารถโหลดโมเดล {scenario_name}: {e}")
        else:
            missing_files = []
            if not os.path.exists(model_path):
                missing_files.append("trained.pkl")
            if not os.path.exists(feature_path):
                missing_files.append("features.pkl")
            if not os.path.exists(scaler_path):
                missing_files.append("scaler.pkl")
            print(f"❌ ไม่พบไฟล์ {scenario_name}: {', '.join(missing_files)}")
    
    print(f"\n📊 สรุปการโหลดโมเดล: {len(models)}/{len(MARKET_SCENARIOS)} โมเดล")
    
    if len(models) == len(MARKET_SCENARIOS):
        print("✅ โหลดโมเดลครบทุก scenarios")
        return models
    elif len(models) > 0:
        print(f"⚠️ โหลดโมเดลได้บางส่วน: {list(models.keys())}")
        return models
    else:
        print("❌ ไม่สามารถโหลดโมเดลใดๆ ได้")
        return None

def predict_with_scenario_model(row, action_type, loaded_models, confidence_threshold=0.5):
    """
    ทำนายด้วยโมเดลที่เหมาะสมตามสถานการณ์
    
    Args:
        row: pandas Series ที่มีข้อมูลสำหรับการทำนาย
        action_type: ประเภทการกระทำ ('BUY' หรือ 'SELL')
        loaded_models: dict ของโมเดลที่โหลดแล้ว
        confidence_threshold: threshold สำหรับการตัดสินใจ
        
    Returns:
        tuple: (prediction_class, confidence, model_used)
    """
    try:
        # ตรวจจับสถานการณ์ตลาด
        scenario = detect_market_scenario(row)
        
        if scenario not in loaded_models:
            print(f"⚠️ ไม่พบโมเดลสำหรับ scenario: {scenario}")
            # ใช้โมเดลอื่นที่มีอยู่
            available_scenarios = list(loaded_models.keys())
            if available_scenarios:
                scenario = available_scenarios[0]
                print(f"🔄 ใช้โมเดล fallback: {scenario}")
            else:
                return None, 0.0, "none"
        
        model_data = loaded_models[scenario]
        model = model_data['model']
        features = model_data['features']
        scaler = model_data['scaler']
        
        # เตรียมข้อมูลสำหรับการทำนาย
        feature_values = []
        missing_features = []
        
        for feature in features:
            if feature in row.index:
                feature_values.append(row[feature])
            else:
                feature_values.append(0)  # ใช้ 0 สำหรับ feature ที่หายไป
                missing_features.append(feature)
        
        if missing_features:
            print(f"⚠️ Missing features ({len(missing_features)}): {missing_features[:5]}...")
        
        # แปลงเป็น numpy array และ reshape
        X = np.array(feature_values).reshape(1, -1)
        
        # Scale ข้อมูล
        X_scaled = scaler.transform(X)
        
        # ทำนาย
        if USE_MULTICLASS_TARGET:
            # Multi-class prediction
            prediction_proba = model.predict_proba(X_scaled)[0]
            prediction_class = np.argmax(prediction_proba)
            confidence = prediction_proba[prediction_class]
        else:
            # Binary prediction
            prediction_proba = model.predict_proba(X_scaled)[0]
            confidence = prediction_proba[1]  # ความน่าจะเป็นของ class 1
            prediction_class = 1 if confidence >= confidence_threshold else 0
        
        return prediction_class, confidence, scenario
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทำนาย: {e}")
        import traceback
        traceback.print_exc()
        return None, 0.0, "error"

def load_time_filters(symbol, timeframe):
    """โหลด time filters สำหรับ symbol และ timeframe ที่กำหนด"""
    
    # แปลง timeframe จาก PERIOD_M30 -> 30, PERIOD_H1 -> 60 เป็นต้น
    timeframe_map = {
        "PERIOD_M30": 30,
        "PERIOD_H1": 60,
        "PERIOD_M1": 1,
        "PERIOD_M5": 5,
        "PERIOD_M15": 15,
        "PERIOD_H4": 240,
        "PERIOD_D1": 1440
    }
    
    # ถ้า timeframe เป็น string ให้แปลงเป็น int
    if isinstance(timeframe, str):
        timeframe = timeframe_map.get(timeframe, timeframe)
        try:
            timeframe = int(timeframe)
        except:
            timeframe = 30  # default
    
    time_filter_path = f"{test_folder}/thresholds/{str(timeframe).zfill(3)}_{symbol}_time_filters.pkl"
    
    print(f"กำลังโหลด time filters จาก: {time_filter_path}")
    
    try:
        if os.path.exists(time_filter_path):
            with open(time_filter_path, 'rb') as f:
                time_filters = pickle.load(f)
            print(f"✅ โหลด time filters สำเร็จ ({symbol}_{timeframe})")
            return time_filters
        else:
            print(f"⚠️ ไม่พบไฟล์ time filters: {time_filter_path}")
            return None
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดในการโหลด time filters: {e}")
        return None

def load_scenario_threshold(symbol, timeframe, scenario_name, default=0.5):
    """โหลด threshold สำหรับ scenario ที่กำหนด"""
    
    threshold_file = f"{test_folder}/thresholds/{str(timeframe).zfill(3)}_{symbol}_{scenario_name}_optimal_threshold.pkl"
    
    try:
        with open(threshold_file, 'rb') as f:
            threshold = pickle.load(f)
        print(f"✅ โหลด threshold สำหรับ {scenario_name}: {threshold:.4f}")
        return threshold
    except Exception as e:
        print(f"⚠️ ไม่พบไฟล์ threshold สำหรับ {scenario_name}, ใช้ค่า default: {default} ({e})")
        return default

def load_scenario_nbars(symbol, timeframe, scenario_name, default=6):
    """โหลด nBars_SL สำหรับ scenario ที่กำหนด"""
    
    nbars_file = f"{test_folder}/thresholds/{str(timeframe).zfill(3)}_{symbol}_{scenario_name}_optimal_nBars_SL.pkl"
    
    try:
        with open(nbars_file, 'rb') as f:
            nbars = pickle.load(f)
        print(f"✅ โหลด nBars_SL สำหรับ {scenario_name}: {nbars}")
        return nbars
    except Exception as e:
        print(f"⚠️ ไม่พบไฟล์ nBars_SL สำหรับ {scenario_name}, ใช้ค่า default: {default} ({e})")
        return default

def get_optimal_parameters(symbol, timeframe, market_condition, action_type):
    """ดึงค่า threshold และ nBars_SL ที่เหมาะสมตามสถานการณ์ตลาด"""
    
    # ใช้ scenario ที่ตรวจจับได้
    scenario_name = market_condition
    
    # โหลดค่า optimal parameters
    threshold = load_scenario_threshold(symbol, timeframe, scenario_name)
    nbars_sl = load_scenario_nbars(symbol, timeframe, scenario_name)
    
    return {
        'threshold': threshold,
        'nBars_SL': nbars_sl,
        'scenario': scenario_name
    }

//+------------------------------------------------------------------+
//| Test Display Format                                              |
//+------------------------------------------------------------------+
#property copyright "Test"
#property version   "1.00"

//+------------------------------------------------------------------+
//| ฟังก์ชันแปลง time_filters ให้อ่านง่าย                           |
//+------------------------------------------------------------------+
string FormatTimeFilters(string time_filters_str)
{
   if(time_filters_str == "") return "Every Day, All Day";
   
   // ตัวอย่างการแปลงแบบง่าย
   if(StringFind(time_filters_str, "Days:[0, 1, 2, 3, 4, 5, 6]") >= 0) {
      if(StringFind(time_filters_str, "Hours:[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]") >= 0) {
         return "Every Day, All Day";
      }
   }
   
   return "Custom Filter";
}

//+------------------------------------------------------------------+
//| ฟังก์ชันแสดงผลรวมทั้ง Python และ MT5 แบบกะทัดรัด                   |
//+------------------------------------------------------------------+
void UpdateCombinedDisplay(string py_signal, string py_class, double py_confidence, string py_symbol, string py_timeframe,
                          double py_best_entry, int py_nBars_SL, double py_threshold, string py_time_filters, int py_spread,
                          double mt5_magic, int mt5_spread, double mt5_pv,
                          string py_market_condition = "", string py_action_type = "", string py_scenario_used = "")
{
   // ป้องกัน error และตั้งค่าเริ่มต้น
   if(py_symbol == "") py_symbol = _Symbol;
   if(py_signal == "") py_signal = "N/A";
   if(py_class == "") py_class = "N/A";
   if(py_market_condition == "") py_market_condition = "unknown";
   if(py_action_type == "") py_action_type = "none";
   if(py_scenario_used == "") py_scenario_used = "none";
   
   // แปลง timeframe แบบกะทัดรัด
   string tf = StringSubstr(py_timeframe, 7); // ตัด "PERIOD_" ออก
   if(tf == "") tf = "M30"; // default
   
   // แปลง time_filters ให้อ่านง่าย
   string time_filter = FormatTimeFilters(py_time_filters);
   if(StringLen(time_filter) > 30) time_filter = StringSubstr(time_filter, 0, 30) + "...";
   
   // สร้างข้อความแสดงผลแบบกะทัดรัดและสวยงาม
   string display = "";

   // จัดรูปแบบข้อมูลให้เหมาะสม
   string symbol_tf = StringFormat("%-7s %s", py_symbol, tf);
   string signal_info = StringFormat("%s %.3f", py_signal, py_confidence);

   // ตัดข้อความให้พอดี
   string market = StringSubstr(py_market_condition + "        ", 0, 8);
   string action = StringSubstr(py_action_type + "    ", 0, 4);
   string scenario = StringSubstr(py_scenario_used + "           ", 0, 11);

   display += "╔════════════════════════════════════╗\n";
   display += StringFormat("║ %s │ %s │ %s ║\n", symbol_tf, signal_info, py_class);
   display += StringFormat("║ %s│%s│%s ║\n", market, action, scenario);
   display += StringFormat("║ T:%.3f SL:%d SP:%d │ M:%.0f PV:%.1f ║\n",
                          py_threshold, py_nBars_SL, py_spread, mt5_magic, mt5_pv);

   // แสดงราคาเฉพาะเมื่อมี signal
   if(py_signal == "BUY" || py_signal == "SELL") {
      display += StringFormat("║ Entry: %-27.5f ║\n", py_best_entry);
   }

   // แสดง Time Filters (เฉพาะเมื่อไม่ใช่ทุกวันทุกเวลา)
   if(time_filter != "" && time_filter != "Every Day, All Day") {
      display += StringFormat("║ Time: %-30s ║\n", time_filter);
   }

   display += "╚════════════════════════════════════╝";
   
   // แสดงผล
   Comment(display);
   Print("Display: ", py_symbol, " ", py_signal, " ", DoubleToString(py_confidence, 3));
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // ทดสอบการแสดงผลหลายแบบ
   Print("=== ทดสอบการแสดงผล UpdateCombinedDisplay ===");

   // ตัวอย่างที่ 1: SELL Signal
   UpdateCombinedDisplay("SELL", "SELL", 0.510, "EURGBP", "PERIOD_M30",
                        0.85432, 8, 0.350, "Days:[0,1,2,3,4,5,6],Hours:[11]", 22,
                        13045, 22, 1.3392,
                        "downtrend", "sell", "trend_following");

   Sleep(3000);

   // ตัวอย่างที่ 2: BUY Signal
   UpdateCombinedDisplay("BUY", "BUY", 0.750, "GOLD", "PERIOD_M30",
                        2650.50, 12, 0.500, "", 25,
                        13098, 25, 1.0,
                        "uptrend", "buy", "counter_trend");

   Sleep(3000);

   // ตัวอย่างที่ 3: HOLD Signal
   UpdateCombinedDisplay("HOLD", "HOLD", 0.450, "EURUSD", "PERIOD_H1",
                        0.0, 6, 0.500, "Days:[1,2,3,4,5],Hours:[8,9,10,14,15,16]", 18,
                        13001, 18, 1.0,
                        "sideways", "none", "default");

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Comment("");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // ไม่ต้องทำอะไร
}

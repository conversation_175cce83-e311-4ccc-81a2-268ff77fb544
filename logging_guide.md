# 📋 คู่มือการใช้งานระบบ Logging

## 🎯 ภาพรวม

ระบบ Logging ที่เพิ่มเข้าไปใน `python_LightGBM_16_Signal.py` ช่วยให้คุณสามารถ:

- ✅ **บันทึกการทำงาน** ของระบบแบบละเอียด
- ✅ **ติดตาม error** และแก้ไขปัญหาได้ง่าย
- ✅ **วิเคราะห์ประสิทธิภาพ** และเวลาการทำงาน
- ✅ **ป้องกันไฟล์ใหญ่เกินไป** ด้วยระบบ rotation
- ✅ **รองรับภาษาไทย** ด้วย UTF-8 encoding

## 🔧 การตั้งค่า

### การตั้งค่าอัตโนมัติ
ระบบจะตั้งค่าอัตโนมัติเมื่อ import module:
```python
# ระบบจะสร้างโฟลเดอร์ logs/ และตั้งค่าต่างๆ อัตโนมัติ
from python_LightGBM_16_Signal import *
```

### การตั้งค่าแบบกำหนดเอง
```python
# ปรับแต่งการตั้งค่า
setup_logging(
    log_level=logging.DEBUG,    # ระดับ log (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    max_file_size_mb=20,        # ขนาดไฟล์สูงสุด 20MB
    backup_count=10             # เก็บไฟล์ backup 10 ไฟล์
)
```

## 📁 โครงสร้างไฟล์

```
project/
├── logs/                                    # โฟลเดอร์ log files
│   ├── trading_model_20250713.log          # ไฟล์ log หลัก
│   ├── trading_model_20250713.log.1        # Backup file 1
│   ├── trading_model_20250713.log.2        # Backup file 2
│   └── ...
└── python_LightGBM_16_Signal.py           # ไฟล์หลัก
```

## 🎨 ระดับ Logging

| ระดับ | การใช้งาน | ตัวอย่าง |
|-------|-----------|----------|
| `DEBUG` | ข้อมูลละเอียดสำหรับ debug | `logging.debug("🔍 Features: 193 columns")` |
| `INFO` | ข้อมูลสำคัญทั่วไป | `logging.info("📊 เริ่มเทรนโมเดล EURUSD M30")` |
| `WARNING` | สถานการณ์ผิดปกติ | `logging.warning("⚠️ พบข้อมูลขาดหาย 5 แถว")` |
| `ERROR` | ข้อผิดพลาดที่ต้องแก้ | `logging.error("❌ ไม่สามารถโหลดโมเดล")` |
| `CRITICAL` | ข้อผิดพลาดร้ายแรง | `logging.critical("🚨 ระบบหยุดทำงาน")` |

## 🛠️ ฟังก์ชันช่วย

### 1. การ log เริ่มต้นและสิ้นสุดฟังก์ชัน
```python
def my_function(param1, param2):
    start_time = time.perf_counter()
    log_function_start("my_function", param1=param1, param2=param2)
    
    try:
        # ทำงานของฟังก์ชัน
        result = do_something()
        
        execution_time = time.perf_counter() - start_time
        log_function_end("my_function", result=result, execution_time=execution_time)
        return result
        
    except Exception as e:
        execution_time = time.perf_counter() - start_time
        log_error_with_traceback("ข้อผิดพลาดใน my_function", e)
        log_function_end("my_function", result="Failed", execution_time=execution_time)
        raise
```

### 2. การ log error พร้อม traceback
```python
try:
    risky_operation()
except Exception as e:
    log_error_with_traceback("การดำเนินการล้มเหลว", e)
```

### 3. การ log ผลลัพธ์โมเดล
```python
metrics = {
    "accuracy": 0.8542,
    "auc": 0.9123,
    "f1_score": 0.7834
}
log_model_performance("EURUSD", 30, metrics)
```

## 📊 ตัวอย่างการใช้งาน

### การ log ข้อมูลทั่วไป
```python
logging.info("🚀 เริ่มต้นการวิเคราะห์")
logging.info(f"📊 โหลดข้อมูล: {len(df)} แถว")
logging.info(f"🎯 Features: {len(features)} คอลัมน์")
```

### การ log การเทรด
```python
logging.info("💹 สัญญาณการเทรด:")
logging.info(f"  🎯 Symbol: {symbol}")
logging.info(f"  📊 Signal: {signal}")
logging.info(f"  🔢 Confidence: {confidence:.4f}")
logging.info(f"  💰 Entry: {entry_price}")
```

### การ log ประสิทธิภาพ
```python
start_time = time.perf_counter()
# ทำงาน...
end_time = time.perf_counter()
logging.info(f"⏱️ การประมวลผลเสร็จ ({end_time - start_time:.2f}s)")
```

## 🔍 การติดตามและวิเคราะห์

### การดูไฟล์ log
```bash
# ดูไฟล์ log ล่าสุด
tail -f logs/trading_model_20250713.log

# ค้นหา error
grep "ERROR" logs/trading_model_20250713.log

# ค้นหาการเทรนโมเดล
grep "เทรนโมเดล" logs/trading_model_20250713.log
```

### การวิเคราะห์ประสิทธิภาพ
```bash
# ค้นหาเวลาการทำงาน
grep "⏱️" logs/trading_model_20250713.log

# ค้นหาผลลัพธ์โมเดล
grep "📈 Model Performance" logs/trading_model_20250713.log
```

## ⚙️ การปรับแต่ง

### เปลี่ยนระดับ log ใน console
```python
# แสดงเฉพาะ ERROR ขึ้นไปใน console
console_handler.setLevel(logging.ERROR)

# แสดงทุกอย่างใน console
console_handler.setLevel(logging.DEBUG)
```

### เปลี่ยนรูปแบบ log
```python
formatter = logging.Formatter(
    '%(asctime)s | %(levelname)-8s | %(funcName)-20s | %(message)s',
    datefmt='%H:%M:%S'  # แสดงเฉพาะเวลา
)
```

## 🚨 การแก้ไขปัญหา

### ปัญหาที่พบบ่อย

1. **ไฟล์ log ไม่ถูกสร้าง**
   - ตรวจสอบสิทธิ์การเขียนไฟล์
   - ตรวจสอบว่าโฟลเดอร์ logs มีอยู่

2. **ไฟล์ log ใหญ่เกินไป**
   - ระบบจะหมุนไฟล์อัตโนมัติ
   - ปรับ `max_file_size_mb` ให้เล็กลง

3. **ข้อความภาษาไทยแสดงผิด**
   - ตรวจสอบ encoding ของ terminal
   - ใช้ UTF-8 encoding

### การ debug
```python
# เปิด debug mode
logging.getLogger().setLevel(logging.DEBUG)

# ตรวจสอบ handlers
for handler in logging.getLogger().handlers:
    print(f"Handler: {handler}")
    print(f"Level: {handler.level}")
```

## 💡 เทิปการใช้งาน

1. **ใช้ emoji** เพื่อความอ่านง่าย: 🚀 📊 ⚠️ ❌ ✅
2. **บันทึกพารามิเตอร์สำคัญ** ในการเริ่มฟังก์ชัน
3. **บันทึกเวลาการทำงาน** ของฟังก์ชันที่ใช้เวลานาน
4. **ใช้ระดับ log ที่เหมาะสม** ตามความสำคัญ
5. **ตรวจสอบไฟล์ log เป็นประจำ** เพื่อติดตามปัญหา

## 📈 ประโยชน์

- 🔍 **Debug ง่ายขึ้น** - ติดตามการทำงานได้ละเอียด
- 📊 **วิเคราะห์ประสิทธิภาพ** - ดูเวลาการทำงานแต่ละส่วน
- 🚨 **ตรวจจับปัญหา** - รู้ทันทีเมื่อเกิด error
- 📁 **เก็บประวัติ** - มีข้อมูลย้อนหลังสำหรับวิเคราะห์
- 🔧 **ปรับปรุงระบบ** - ใช้ข้อมูล log ในการพัฒนา

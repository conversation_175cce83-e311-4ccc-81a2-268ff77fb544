#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ตัวอย่างการใช้งานระบบ Logging ที่เพิ่มเข้าไปใน python_LightGBM_16_Signal.py

ระบบ Logging นี้มีคุณสมบัติ:
1. ✅ บันทึกลงไฟล์ .log ด้วย UTF-8 encoding
2. ✅ หมุนไฟล์อัตโนมัติเมื่อใหญ่เกิน 10MB (ป้องกันไฟล์ใหญ่เกินไป)
3. ✅ เก็บไฟล์ backup 5 ไฟล์
4. ✅ แสดง WARNING ขึ้นไปใน console
5. ✅ บันทึก INFO ขึ้นไปในไฟล์
6. ✅ ฟังก์ชันช่วยสำหรับ error tracking และ performance monitoring
"""

import logging
import time
import os
from datetime import datetime

# Import logging functions จาก main script
# (ในการใช้งานจริง ให้ import จาก python_LightGBM_16_Signal.py)
def demo_logging_functions():
    """
    สาธิตการใช้งานฟังก์ชัน logging ต่างๆ
    """
    
    print("🚀 เริ่มต้นการสาธิต Logging System")
    print("="*60)
    
    # 1. ตัวอย่างการ log ข้อมูลทั่วไป
    logging.info("📊 เริ่มต้นการวิเคราะห์ข้อมูล")
    logging.info("💰 โหลดข้อมูล EURUSD M30")
    logging.debug("🔍 Debug: ข้อมูลมี 1000 แถว, 50 คอลัมน์")
    
    # 2. ตัวอย่างการ log performance
    start_time = time.perf_counter()
    time.sleep(0.1)  # จำลองการประมวลผล
    end_time = time.perf_counter()
    
    logging.info(f"⏱️ การประมวลผลเสร็จสิ้น ({end_time - start_time:.3f}s)")
    
    # 3. ตัวอย่างการ log warning
    logging.warning("⚠️ พบข้อมูลที่ขาดหาย 5 แถว - จะใช้ forward fill")
    
    # 4. ตัวอย่างการ log error
    try:
        # จำลอง error
        result = 10 / 0
    except ZeroDivisionError as e:
        logging.error(f"❌ เกิดข้อผิดพลาดในการคำนวณ: {str(e)}")
        logging.error("📍 ตำแหน่ง: ฟังก์ชัน calculate_indicators")
    
    # 5. ตัวอย่างการ log ผลลัพธ์โมเดล
    model_metrics = {
        "accuracy": 0.8542,
        "auc": 0.9123,
        "f1_score": 0.7834,
        "precision": 0.8012,
        "recall": 0.7665
    }
    
    logging.info("📈 ผลลัพธ์การเทรนโมเดล:")
    for metric, value in model_metrics.items():
        logging.info(f"  📊 {metric}: {value:.4f}")
    
    # 6. ตัวอย่างการ log การเทรด
    logging.info("💹 สัญญาณการเทรด:")
    logging.info("  🎯 Symbol: EURUSD")
    logging.info("  📊 Signal: BUY")
    logging.info("  🔢 Confidence: 0.8234")
    logging.info("  💰 Entry: 1.0850")
    logging.info("  🛑 Stop Loss: 1.0820")
    logging.info("  🎯 Take Profit: 1.0910")
    
    print("\n✅ การสาธิต Logging เสร็จสิ้น")
    print("📁 ตรวจสอบไฟล์ log ในโฟลเดอร์ 'logs'")

def check_log_files():
    """
    ตรวจสอบไฟล์ log ที่ถูกสร้างขึ้น
    """
    log_dir = "logs"
    if os.path.exists(log_dir):
        log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
        
        print(f"\n📁 ไฟล์ log ในโฟลเดอร์ '{log_dir}':")
        for log_file in log_files:
            file_path = os.path.join(log_dir, log_file)
            file_size = os.path.getsize(file_path)
            file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            
            print(f"  📄 {log_file}")
            print(f"     📊 ขนาด: {file_size:,} bytes")
            print(f"     🕒 แก้ไขล่าสุด: {file_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # แสดงบรรทัดสุดท้าย 3 บรรทัด
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if lines:
                        print(f"     📝 บรรทัดสุดท้าย:")
                        for line in lines[-3:]:
                            print(f"       {line.strip()}")
            except Exception as e:
                print(f"     ❌ ไม่สามารถอ่านไฟล์: {e}")
            print()
    else:
        print(f"❌ ไม่พบโฟลเดอร์ '{log_dir}'")

def demonstrate_log_rotation():
    """
    สาธิตการหมุนไฟล์ log เมื่อไฟล์ใหญ่เกินกำหนด
    """
    print("\n🔄 สาธิตการหมุนไฟล์ log...")
    
    # สร้างข้อมูล log จำนวนมาก
    for i in range(1000):
        logging.info(f"📊 ข้อมูลทดสอบ #{i:04d} - การสร้างข้อมูลจำนวนมากเพื่อทดสอบการหมุนไฟล์ log อัตโนมัติ")
        
        if i % 100 == 0:
            print(f"  📝 สร้างข้อมูล log: {i}/1000")
    
    print("✅ สร้างข้อมูล log เสร็จสิ้น")
    check_log_files()

def show_logging_best_practices():
    """
    แสดงแนวทางปฏิบัติที่ดีสำหรับการใช้ logging
    """
    print("\n📚 แนวทางปฏิบัติที่ดีสำหรับ Logging:")
    print("="*50)
    
    practices = [
        "1. 📊 ใช้ INFO สำหรับข้อมูลสำคัญที่ต้องการติดตาม",
        "2. 🔍 ใช้ DEBUG สำหรับข้อมูลละเอียดในการ debug",
        "3. ⚠️ ใช้ WARNING สำหรับสถานการณ์ที่ผิดปกติแต่ไม่ร้ายแรง",
        "4. ❌ ใช้ ERROR สำหรับข้อผิดพลาดที่ต้องแก้ไข",
        "5. 🚨 ใช้ CRITICAL สำหรับข้อผิดพลาดร้ายแรงที่หยุดการทำงาน",
        "6. ⏱️ บันทึกเวลาการทำงานของฟังก์ชันสำคัญ",
        "7. 📈 บันทึกผลลัพธ์และ metrics ของโมเดล",
        "8. 🎯 ใช้ emoji และรูปแบบที่สม่ำเสมอเพื่อความอ่านง่าย",
        "9. 🔢 บันทึกพารามิเตอร์สำคัญและการตั้งค่า",
        "10. 📁 ตรวจสอบไฟล์ log เป็นประจำเพื่อติดตามปัญหา"
    ]
    
    for practice in practices:
        print(f"  {practice}")
    
    print(f"\n💡 ข้อดีของระบบ Logging:")
    benefits = [
        "✅ ติดตามการทำงานของระบบได้แบบ real-time",
        "✅ วิเคราะห์ปัญหาและ debug ได้ง่ายขึ้น",
        "✅ เก็บประวัติการทำงานสำหรับการวิเคราะห์",
        "✅ ไฟล์ไม่ใหญ่เกินไปด้วยระบบ rotation",
        "✅ รองรับ UTF-8 สำหรับภาษาไทย",
        "✅ แยกระดับความสำคัญของข้อมูล"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")

if __name__ == "__main__":
    # ตั้งค่า logging แบบง่าย (ในการใช้งานจริงจะใช้จาก python_LightGBM_16_Signal.py)
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s | %(levelname)-8s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[
            logging.FileHandler('demo_log.txt', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    # รันการสาธิต
    demo_logging_functions()
    check_log_files()
    show_logging_best_practices()
    
    print(f"\n🎉 การสาธิตเสร็จสิ้น!")
    print(f"📁 ตรวจสอบไฟล์ demo_log.txt เพื่อดูผลลัพธ์")

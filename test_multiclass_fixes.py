#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข Multi-class LightGBM Configuration Issues
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

# Import functions จากไฟล์หลัก
try:
    from python_LightGBM_15_Tuning import (
        create_multiclass_target, 
        get_lgbm_params,
        USE_MULTICLASS_TARGET,
        PROFIT_THRESHOLDS,
        CLASS_MAPPING
    )
    print("✅ Import สำเร็จ")
except ImportError as e:
    print(f"❌ Import ล้มเหลว: {str(e)}")
    sys.exit(1)

def test_multiclass_target_creation():
    """
    ทดสอบการสร้าง multi-class target
    """
    print("\n🧪 ทดสอบการสร้าง Multi-class Target")
    print("=" * 60)
    
    # สร้างข้อมูลทดสอบ
    test_cases = [
        {
            'name': 'Normal Distribution',
            'data': np.random.normal(0, 100, 1000)
        },
        {
            'name': 'Mostly Positive',
            'data': np.random.normal(50, 30, 1000)
        },
        {
            'name': 'Mostly Negative', 
            'data': np.random.normal(-50, 30, 1000)
        },
        {
            'name': 'Single Value (Edge Case)',
            'data': np.full(100, 10.0)
        },
        {
            'name': 'Very Small Dataset',
            'data': np.array([10, -10, 50, -50, 100])
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📊 ทดสอบ: {test_case['name']}")
        print("-" * 40)
        
        profit_series = pd.Series(test_case['data'])
        print(f"  ข้อมูลต้นฉบับ: {len(profit_series)} samples")
        print(f"  ช่วงค่า: {profit_series.min():.2f} ถึง {profit_series.max():.2f}")
        
        try:
            target = create_multiclass_target(profit_series)
            unique_classes = np.unique(target)
            print(f"  ✅ สร้าง target สำเร็จ: {len(unique_classes)} classes")
            print(f"  Classes: {unique_classes}")
            
            # ตรวจสอบการกระจายของ classes
            for class_id in unique_classes:
                count = np.sum(target == class_id)
                percentage = (count / len(target)) * 100
                class_name = CLASS_MAPPING.get(class_id, f"Unknown_{class_id}")
                print(f"    Class {class_id} ({class_name}): {count} ({percentage:.1f}%)")
                
        except Exception as e:
            print(f"  ❌ เกิดข้อผิดพลาด: {str(e)}")

def test_lgbm_params():
    """
    ทดสอบการตั้งค่า LightGBM parameters
    """
    print("\n🧪 ทดสอบการตั้งค่า LightGBM Parameters")
    print("=" * 60)
    
    test_targets = [
        {
            'name': 'Binary Classification',
            'y': np.array([0, 1, 0, 1, 1, 0, 1, 0] * 100)
        },
        {
            'name': 'Multi-class (3 classes)',
            'y': np.array([0, 1, 2, 0, 1, 2, 1, 2] * 100)
        },
        {
            'name': 'Multi-class (5 classes)',
            'y': np.array([0, 1, 2, 3, 4, 0, 1, 2, 3, 4] * 100)
        },
        {
            'name': 'Single Class (Edge Case)',
            'y': np.array([1] * 100)
        },
        {
            'name': 'No Target',
            'y': None
        }
    ]
    
    for test_case in test_targets:
        print(f"\n📊 ทดสอบ: {test_case['name']}")
        print("-" * 40)
        
        y = test_case['y']
        if y is not None:
            unique_classes = np.unique(y)
            print(f"  ข้อมูล: {len(y)} samples, {len(unique_classes)} classes")
            print(f"  Classes: {unique_classes}")
        else:
            print(f"  ข้อมูล: None")
        
        try:
            params = get_lgbm_params(y=y)
            print(f"  ✅ สร้าง parameters สำเร็จ")
            print(f"  Objective: {params.get('objective', 'N/A')}")
            print(f"  Metric: {params.get('metric', 'N/A')}")
            if 'num_class' in params:
                print(f"  Num Classes: {params['num_class']}")
                
        except Exception as e:
            print(f"  ❌ เกิดข้อผิดพลาด: {str(e)}")

def test_data_loading():
    """
    ทดสอบการโหลดข้อมูลจริง
    """
    print("\n🧪 ทดสอบการโหลดข้อมูลจริง")
    print("=" * 60)
    
    # ทดสอบกับไฟล์ที่แก้ไขแล้ว
    test_files = [
        "GBPUSD_M30_FIXED.csv",
        "EURUSD_M30_FIXED.csv"
    ]
    
    for filename in test_files:
        print(f"\n📊 ทดสอบไฟล์: {filename}")
        print("-" * 40)
        
        if not os.path.exists(filename):
            print(f"  ❌ ไม่พบไฟล์: {filename}")
            continue
            
        try:
            # โหลดข้อมูล
            df = pd.read_csv(filename)
            print(f"  ✅ โหลดข้อมูลสำเร็จ: {len(df)} rows, {len(df.columns)} columns")
            
            # ตรวจสอบ columns ที่จำเป็น
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                print(f"  ⚠️ ขาด columns: {missing_columns}")
            else:
                print(f"  ✅ มี columns ที่จำเป็นครบถ้วน")
                
            # แสดงตัวอย่างข้อมูล
            print(f"  📈 ตัวอย่างข้อมูล:")
            print(f"    Close range: {df['Close'].min():.5f} - {df['Close'].max():.5f}")
            print(f"    Volume range: {df['Volume'].min():.0f} - {df['Volume'].max():.0f}")
            
        except Exception as e:
            print(f"  ❌ เกิดข้อผิดพลาดในการโหลด: {str(e)}")

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🧪 ทดสอบการแก้ไข Multi-class LightGBM Issues")
    print("=" * 80)
    
    print(f"📋 การตั้งค่าปัจจุบัน:")
    print(f"  USE_MULTICLASS_TARGET: {USE_MULTICLASS_TARGET}")
    print(f"  PROFIT_THRESHOLDS: {PROFIT_THRESHOLDS}")
    print(f"  CLASS_MAPPING: {CLASS_MAPPING}")
    
    # รันการทดสอบทั้งหมด
    test_multiclass_target_creation()
    test_lgbm_params()
    test_data_loading()
    
    print("\n🏁 การทดสอบเสร็จสิ้น")
    print("=" * 80)
    
    print("\n💡 สรุปการแก้ไข:")
    print("1. ✅ เพิ่มการตรวจสอบจำนวน classes ใน get_lgbm_params()")
    print("2. ✅ เพิ่มการตรวจสอบข้อมูลเพียงพอใน create_multiclass_target()")
    print("3. ✅ เพิ่ม fallback เป็น binary classification เมื่อข้อมูลไม่เพียงพอ")
    print("4. ✅ เพิ่มการจัดการ error ในการเทรนโมเดล")
    print("5. ✅ เพิ่ม fallback เป็น technical analysis เมื่อ ML model ล้มเหลว")

if __name__ == "__main__":
    main()

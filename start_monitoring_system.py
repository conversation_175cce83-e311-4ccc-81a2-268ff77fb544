#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import subprocess
import threading
from datetime import datetime

class TradingSystemStarter:
    def __init__(self):
        self.server_process = None
        self.monitor_process = None
        self.running = False
        
    def start_server(self):
        """เริ่ม Python Server"""
        try:
            print("🚀 Starting Python Trading Server...")
            self.server_process = subprocess.Popen([
                sys.executable, 
                "python_to_mt5_WebRequest_server_13_Signal.py"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # รอให้ server เริ่มทำงาน
            time.sleep(5)
            
            if self.server_process.poll() is None:
                print("✅ Python Trading Server started successfully")
                return True
            else:
                print("❌ Failed to start Python Trading Server")
                return False
                
        except Exception as e:
            print(f"❌ Error starting server: {e}")
            return False
    
    def start_monitor(self):
        """เริ่ม System Monitor"""
        try:
            print("🔍 Starting System Monitor...")
            self.monitor_process = subprocess.Popen([
                sys.executable, 
                "system_monitor.py"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            time.sleep(3)
            
            if self.monitor_process.poll() is None:
                print("✅ System Monitor started successfully")
                return True
            else:
                print("❌ Failed to start System Monitor")
                return False
                
        except Exception as e:
            print(f"❌ Error starting monitor: {e}")
            return False
    
    def check_files(self):
        """ตรวจสอบไฟล์ที่จำเป็น"""
        required_files = [
            "python_to_mt5_WebRequest_server_13_Signal.py",
            "system_monitor.py",
            "dashboard_viewer.py"
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print("❌ Missing required files:")
            for file in missing_files:
                print(f"   - {file}")
            return False
        
        print("✅ All required files found")
        return True
    
    def create_startup_log(self):
        """สร้าง log การเริ่มต้นระบบ"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] Trading System Started\n"
        
        with open('system_startup.log', 'a', encoding='utf-8') as f:
            f.write(log_entry)
    
    def show_status(self):
        """แสดงสถานะระบบ"""
        print("\n" + "="*60)
        print("📊 TRADING SYSTEM STATUS")
        print("="*60)
        
        # ตรวจสอบ server
        if self.server_process and self.server_process.poll() is None:
            print("🟢 Python Server: RUNNING")
        else:
            print("🔴 Python Server: STOPPED")
        
        # ตรวจสอบ monitor
        if self.monitor_process and self.monitor_process.poll() is None:
            print("🟢 System Monitor: RUNNING")
        else:
            print("🔴 System Monitor: STOPPED")
        
        # ตรวจสอบไฟล์ log
        log_files = [
            'server_trading.log',
            'server_signals.log',
            'system_monitor_*.txt',
            'MT5_Trading_Log_*.txt'
        ]
        
        print("\n📁 Log Files:")
        for pattern in log_files:
            if '*' in pattern:
                import glob
                files = glob.glob(pattern)
                if files:
                    for file in files:
                        size = os.path.getsize(file) / 1024  # KB
                        print(f"   ✅ {file} ({size:.1f} KB)")
                else:
                    print(f"   ⚠️  {pattern} (not found)")
            else:
                if os.path.exists(pattern):
                    size = os.path.getsize(pattern) / 1024  # KB
                    print(f"   ✅ {pattern} ({size:.1f} KB)")
                else:
                    print(f"   ⚠️  {pattern} (not found)")
        
        print("\n🔧 Available Commands:")
        print("   python dashboard_viewer.py  - View system dashboard")
        print("   python system_monitor.py    - Run standalone monitor")
        print("   Ctrl+C                      - Stop all processes")
        print("="*60)
    
    def stop_all(self):
        """หยุดระบบทั้งหมด"""
        print("\n🛑 Stopping all processes...")
        
        if self.server_process:
            self.server_process.terminate()
            print("✅ Python Server stopped")
        
        if self.monitor_process:
            self.monitor_process.terminate()
            print("✅ System Monitor stopped")
        
        self.running = False
        print("✅ All processes stopped")
    
    def run(self):
        """เริ่มระบบทั้งหมด"""
        print("🚀 TRADING SYSTEM STARTUP")
        print("="*60)
        
        # ตรวจสอบไฟล์
        if not self.check_files():
            return False
        
        # เริ่ม server
        if not self.start_server():
            return False
        
        # เริ่ม monitor
        if not self.start_monitor():
            return False
        
        # สร้าง startup log
        self.create_startup_log()
        
        self.running = True
        
        try:
            while self.running:
                self.show_status()
                
                print(f"\n⏰ Next status update in 30 seconds...")
                print("   Press Ctrl+C to stop the system")
                
                time.sleep(30)
                
        except KeyboardInterrupt:
            self.stop_all()
        
        return True

def main():
    print("🎯 Trading System Launcher")
    print("This will start:")
    print("  1. Python Trading Server")
    print("  2. System Monitor")
    print("  3. Status Dashboard")
    print()
    
    choice = input("Start the trading system? (y/n): ").lower().strip()
    
    if choice == 'y':
        starter = TradingSystemStarter()
        starter.run()
    else:
        print("❌ Startup cancelled")

if __name__ == "__main__":
    main()

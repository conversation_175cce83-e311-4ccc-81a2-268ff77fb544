#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Threshold System Testing Script
ทดสอบระบบหา threshold ที่ปรับปรุงใหม่สำหรับ Multi-Model Architecture

Features:
1. Enhanced Backtest - ทดสอบ threshold ด้วยการ backtest จริง
2. Market Condition Analysis - วิเคราะห์สภาวะตลาดสำหรับ threshold
3. Scenario-specific Logic - ปรับตาม trend_following/counter_trend
4. Multi-method Scoring - รวมผลลัพธ์จากหลายวิธี
5. Comprehensive Reporting - รายงานผลละเอียด
"""

import pandas as pd
import numpy as np
import os
import sys
import pickle
from datetime import datetime
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

# เพิ่ม path สำหรับ import
sys.path.append('.')

# Import functions from main script
try:
    from python_LightGBM_16_Signal import (
        find_optimal_threshold_multi_model,
        analyze_market_conditions_for_threshold,
        find_optimal_threshold_enhanced_backtest,
        find_best_threshold_simple,
        find_optimal_threshold_scenario_specific,
        select_best_threshold_with_scoring,
        print_threshold_analysis_summary,
        calculate_trading_metrics,
        calculate_threshold_composite_score
    )
    print("✅ Successfully imported enhanced threshold functions")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("⚠️ Please make sure python_LightGBM_16_Signal.py is in the current directory")
    sys.exit(1)

def create_sample_data_with_profit(symbol="EURUSD", num_rows=500):
    """
    สร้างข้อมูลตัวอย่างสำหรับการทดสอบ (รวม Profit column)
    """
    print(f"📊 Creating sample data with profit for {symbol} ({num_rows} rows)")
    
    # สร้างข้อมูล OHLC แบบสุ่มที่มีลักษณะคล้ายข้อมูลจริง
    np.random.seed(42)  # For reproducible results
    
    # Base price
    base_price = 1.1000 if 'EUR' in symbol else 1.2500
    
    # Generate price movements
    returns = np.random.normal(0, 0.001, num_rows)  # Daily returns with 0.1% volatility
    prices = [base_price]
    
    for ret in returns:
        new_price = prices[-1] * (1 + ret)
        prices.append(new_price)
    
    prices = np.array(prices[1:])  # Remove first element
    
    # Create OHLC data
    data = []
    for i in range(num_rows):
        close = prices[i]
        
        # Generate realistic OHLC
        daily_range = abs(np.random.normal(0, 0.0005))  # Daily range
        high = close + daily_range * np.random.uniform(0.3, 1.0)
        low = close - daily_range * np.random.uniform(0.3, 1.0)
        open_price = low + (high - low) * np.random.uniform(0.2, 0.8)
        
        # Ensure OHLC relationships
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        # Generate trading features
        rsi = 50 + np.random.normal(0, 15)  # RSI around 50
        rsi = max(0, min(100, rsi))
        
        macd = np.random.normal(0, 0.001)
        volume = np.random.randint(1000, 10000)
        
        # Generate target (buy signal)
        # ใช้ logic ง่ายๆ: ซื้อเมื่อ RSI ต่ำและ MACD เป็นบวก
        target = 1 if (rsi < 40 and macd > 0) else 0
        
        # Generate profit (สำหรับ backtest)
        # สมมติว่าถ้า target = 1 และราคาขึ้นในอนาคต จะได้กำไร
        future_return = np.random.normal(0.0005 if target == 1 else -0.0002, 0.002)
        profit = future_return * 10000  # Convert to points
        
        data.append({
            'Date': f"2024-01-{(i % 30) + 1:02d}",
            'Time': f"{(i % 24):02d}:00",
            'Open': round(open_price, 5),
            'High': round(high, 5),
            'Low': round(low, 5),
            'Close': round(close, 5),
            'Volume': volume,
            'RSI14': round(rsi, 2),
            'MACD': round(macd, 6),
            'Target': target,
            'Profit': round(profit, 2)
        })
    
    df = pd.DataFrame(data)
    
    # Add more features
    df['EMA50'] = df['Close'].ewm(span=50).mean()
    df['EMA200'] = df['Close'].ewm(span=200).mean()
    df['Volume_MA_20'] = df['Volume'].rolling(20).mean()
    
    # Add technical indicators
    df['Price_Change'] = df['Close'].pct_change()
    df['Volume_Change'] = df['Volume'].pct_change()
    df['High_Low_Ratio'] = (df['High'] - df['Low']) / df['Close']
    
    # Fill NaN values
    df = df.fillna(method='bfill').fillna(method='ffill')
    
    print(f"✅ Created sample data: {len(df)} rows, {len(df.columns)} columns")
    print(f"📊 Target distribution: {df['Target'].value_counts().to_dict()}")
    print(f"📊 Profit stats: mean={df['Profit'].mean():.2f}, std={df['Profit'].std():.2f}")
    
    return df

def create_mock_model_and_scaler(features):
    """
    สร้าง mock model และ scaler สำหรับการทดสอบ
    """
    print(f"🤖 Creating mock model and scaler for {len(features)} features")
    
    # สร้าง mock model
    model = RandomForestClassifier(n_estimators=10, random_state=42)
    
    # สร้าง mock training data
    X_mock = np.random.randn(100, len(features))
    y_mock = np.random.randint(0, 2, 100)
    
    # Train mock model
    model.fit(X_mock, y_mock)
    
    # สร้าง scaler
    scaler = StandardScaler()
    scaler.fit(X_mock)
    
    return model, scaler

def test_market_analysis_for_threshold(val_df, symbol):
    """
    ทดสอบการวิเคราะห์ market conditions สำหรับ threshold
    """
    print(f"\n🔍 Testing Market Condition Analysis for Threshold - {symbol}")
    print("-" * 60)
    
    market_analysis = analyze_market_conditions_for_threshold(val_df, symbol)
    
    print("📊 Market Analysis Results:")
    for key, value in market_analysis.items():
        if isinstance(value, dict):
            print(f"   {key}:")
            for sub_key, sub_value in value.items():
                print(f"      {sub_key}: {sub_value}")
        else:
            print(f"   {key}: {value}")
    
    return market_analysis

def test_individual_threshold_methods(model, scaler, val_df, features, scenario_name, market_analysis, symbol):
    """
    ทดสอบแต่ละวิธีการหา threshold
    """
    print(f"\n🧪 Testing Individual Threshold Methods for {scenario_name}")
    print("-" * 60)
    
    # Test Enhanced Backtest
    print("1. Enhanced Backtest:")
    enhanced_result = find_optimal_threshold_enhanced_backtest(
        model, scaler, val_df, features, scenario_name, market_analysis, symbol
    )
    print(f"   Result: {enhanced_result}")
    
    # Test Statistical Method
    print("\n2. Statistical Method (F1-score):")
    statistical_result = find_best_threshold_simple(
        model=model,
        scaler=scaler,
        val_df=val_df,
        model_features=features
    )
    print(f"   Result: {statistical_result}")
    
    # Test Scenario-specific Method
    print("\n3. Scenario-specific Method:")
    scenario_result = find_optimal_threshold_scenario_specific(
        model, scaler, val_df, features, scenario_name, market_analysis
    )
    print(f"   Result: {scenario_result}")
    
    return enhanced_result, statistical_result, scenario_result

def test_threshold_selection_logic(enhanced_result, statistical_result, scenario_result, scenario_name, market_analysis):
    """
    ทดสอบ logic การเลือกผลลัพธ์ที่ดีที่สุด
    """
    print(f"\n🏆 Testing Threshold Selection Logic for {scenario_name}")
    print("-" * 60)
    
    best_threshold, selection_reason = select_best_threshold_with_scoring(
        enhanced_result, statistical_result, scenario_result, 
        scenario_name, market_analysis
    )
    
    print(f"✅ Selected Threshold: {best_threshold:.3f}")
    print(f"📝 Reason: {selection_reason}")
    
    return best_threshold, selection_reason

def test_full_threshold_system(symbol="EURUSD", timeframe=60):
    """
    ทดสอบระบบ threshold ทั้งหมด
    """
    print(f"\n🚀 Testing Full Enhanced Threshold System")
    print("=" * 80)
    print(f"Symbol: {symbol}, Timeframe: M{timeframe}")
    print("=" * 80)
    
    # สร้างข้อมูลตัวอย่าง
    val_df = create_sample_data_with_profit(symbol, 400)
    
    # กำหนด features
    features = ['RSI14', 'MACD', 'EMA50', 'EMA200', 'Volume_MA_20', 
               'Price_Change', 'Volume_Change', 'High_Low_Ratio']
    
    # สร้าง mock models
    trend_model, trend_scaler = create_mock_model_and_scaler(features)
    counter_model, counter_scaler = create_mock_model_and_scaler(features)
    
    # สร้าง mock models dict
    models_dict = {
        'trend_following': {
            'model': trend_model,
            'scaler': trend_scaler,
            'features': features
        },
        'counter_trend': {
            'model': counter_model,
            'scaler': counter_scaler,
            'features': features
        }
    }
    
    # ทดสอบระบบทั้งหมด
    print(f"\n🔄 Running Enhanced Multi-Model Threshold Optimization...")
    
    try:
        optimal_thresholds = find_optimal_threshold_multi_model(
            models_dict=models_dict,
            val_df=val_df,
            symbol=symbol,
            timeframe=timeframe
        )
        
        print(f"\n✅ Threshold System Test Completed Successfully!")
        print(f"📊 Final Results: {optimal_thresholds}")
        
        return optimal_thresholds
        
    except Exception as e:
        print(f"❌ Threshold system test failed: {e}")
        return None

def run_comprehensive_threshold_test():
    """
    รันการทดสอบ threshold แบบครอบคลุม
    """
    print("🧪 Enhanced Threshold System - Comprehensive Test")
    print("=" * 80)
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Test different symbols and scenarios
    test_cases = [
        {"symbol": "EURUSD", "timeframe": 60},
        {"symbol": "GBPUSD", "timeframe": 30},
        {"symbol": "GOLD", "timeframe": 60}
    ]
    
    all_results = {}
    
    for test_case in test_cases:
        symbol = test_case["symbol"]
        timeframe = test_case["timeframe"]
        
        print(f"\n📋 Testing {symbol} M{timeframe}")
        print("-" * 40)
        
        try:
            result = test_full_threshold_system(symbol, timeframe)
            all_results[f"{symbol}_M{timeframe}"] = result
            
        except Exception as e:
            print(f"❌ Test failed for {symbol} M{timeframe}: {e}")
            all_results[f"{symbol}_M{timeframe}"] = None
    
    # สรุปผลการทดสอบ
    print(f"\n📊 Threshold Test Summary")
    print("=" * 80)
    
    for test_name, result in all_results.items():
        if result:
            print(f"✅ {test_name}: {result}")
        else:
            print(f"❌ {test_name}: Failed")
    
    print("\n🎉 Comprehensive Threshold Test Completed!")
    return all_results

if __name__ == "__main__":
    # รันการทดสอบ
    results = run_comprehensive_threshold_test()
    
    print(f"\n📝 Threshold test completed. Results saved in memory.")
    print(f"💡 You can now use the enhanced threshold system in your trading scripts!")

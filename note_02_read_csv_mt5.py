import pandas as pd
import numpy as np

import pandas as pd

# อ่านไฟล์แบบธรรมดา (1 คอลัมน์ก่อน)
df = pd.read_csv('GOLD#_H1_202001020900_202503311900.csv', header=None)

# แยกคอลัมน์ด้วยตัว '\t'
df = df[0].str.split('\t', expand=True)

# ลบแถวแรกออก (เพราะมันเป็น <DATE> <TIME> ...)
df = df.drop(index=0).reset_index(drop=True)

# ตั้งชื่อคอลัมน์
df.columns = ["Date", "Time", "Open", "High", "Low", "Close", "Tickvol", "Vol", "Spread"]

# เลือกเฉพาะคอลัมน์ที่ต้องการ และเปลี่ยนชื่อ
df = df[["Date", "Time", "Open", "High", "Low", "Close", "Tickvol"]]
df = df.rename(columns={"Tickvol": "Volume"})

# แสดงผล
print(df.info())
print(df)
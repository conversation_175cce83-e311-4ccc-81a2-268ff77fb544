# 📋 สรุปขั้นตอนการทำงาน python_LightGBM_16_Signal.py

## 🎯 ภาพรวมระบบ
ระบบ Multi-Model LightGBM Trading System ที่ใช้ 4 โมเดลแยกตามสถานการณ์ตลาด เพื่อทำนายสัญญาณการซื้อขาย

## 🏗️ โครงสร้างหลัก

### 1. **Configuration & Setup** (บรรทัด 1-160)
```python
# การตั้งค่าพื้นฐาน
USE_MULTI_MODEL_ARCHITECTURE = True
NUM_TRAINING_ROUNDS = 3
input_rsi_level_in = 35
input_take_profit = 2.5
```

### 2. **Multi-Model Architecture** (บรรทัด 120-290)
```python
MARKET_SCENARIOS = {
    'trend_following_buy': {...},
    'counter_trend_sell': {...},
    'counter_trend_buy': {...},
    'trend_following_sell': {...}
}
```

## 🔄 ขั้นตอนการทำงานหลัก

### **Phase 1: การเตรียมข้อมูล (Data Preparation)**

#### 1.1 โหลดข้อมูล (`load_and_process_data`)
- โหลดไฟล์ CSV ข้อมูลราคา
- ตรวจสอบคุณภาพข้อมูล
- สร้าง DateTime index

#### 1.2 สร้าง Technical Indicators
- **Price Indicators:** EMA50, EMA100, EMA200
- **Momentum:** RSI14, MACD, Stochastic
- **Volatility:** ATR, Bollinger Bands
- **Volume:** Volume MA, Volume Spike
- **Custom:** PullBack, Ratio Buy/Sell

#### 1.3 สร้าง Features
- **Bar Patterns:** Doji, Hammer, Engulfing
- **Time Features:** Hour, Day of Week, Session
- **Lag Features:** Price/Volume/Indicator lags
- **Interaction Features:** RSI×Volume, MACD×ATR
- **Rolling Statistics:** MA, Std ต่างๆ

#### 1.4 สร้าง Trading Signals
```python
def create_trade_cycles_with_model():
    # สร้างสัญญาณซื้อขายตาม entry conditions
    # คำนวณ SL/TP ตาม ATR
    # บันทึกผลการเทรด
```

### **Phase 2: การสร้าง Target Variables**

#### 2.1 Binary Target
```python
# Target = 1 ถ้า Profit > 0, Target = 0 ถ้า Profit <= 0
```

#### 2.2 Multi-Class Target (5 classes)
```python
# Class 0: strong_sell (< -20 points)
# Class 1: weak_sell (-20 to -5 points)  
# Class 2: no_trade (-5 to +5 points)
# Class 3: weak_buy (+5 to +20 points)
# Class 4: strong_buy (> +20 points)
```

### **Phase 3: Feature Selection & Engineering**

#### 3.1 Feature Selection (`select_features`)
- **Correlation Analysis:** ลบ features ที่มี correlation สูง
- **Target Correlation:** เลือก features ที่มีความสัมพันธ์กับ target
- **Must-Have Features:** โหลดจากไฟล์ที่วิเคราะห์ไว้แล้ว
- **Final Selection:** ~40-50 features

#### 3.2 Data Splitting
```python
# Time-based split (ไม่ใช้ random)
# Train: 60% (เก่าสุด)
# Validation: 20% (กลาง)  
# Test: 20% (ใหม่สุด)
```

### **Phase 4: Model Training**

#### 4.1 Single Model Architecture (แบบเดิม)
```python
def train_and_evaluate():
    # เทรนโมเดล LightGBM 1 ตัว
    # ใช้ข้อมูลทั้งหมด
    # Hyperparameter tuning
```

#### 4.2 Multi-Model Architecture (แบบใหม่)
```python
def train_all_scenario_models():
    for scenario in MARKET_SCENARIOS:
        # กรองข้อมูลตาม market condition
        # เทรนโมเดลเฉพาะสถานการณ์
        # บันทึกโมเดลแยกกัน
```

### **Phase 5: Model Optimization**

#### 5.1 Hyperparameter Tuning
```python
def hyperparameter_tuning():
    # RandomizedSearchCV
    # 5-fold Time Series CV
    # Optimize for AUC/F1-Score
```

#### 5.2 Threshold Optimization
```python
def find_optimal_threshold():
    # ทดสอบ threshold 0.1-0.9
    # เลือกตาม Expectancy หรือ Win Rate
    # บันทึกค่า optimal
```

#### 5.3 nBars SL Optimization
```python
def find_optimal_nbars_sl():
    # ทดสอบ nBars 2-10
    # Backtest บน validation set
    # เลือกตาม Risk/Reward
```

### **Phase 6: Model Evaluation & Testing**

#### 6.1 Performance Metrics
- **Classification:** Accuracy, AUC, F1-Score
- **Trading:** Win Rate, Expectancy, Sharpe Ratio
- **Risk:** Max Drawdown, Profit Factor

#### 6.2 Time Analysis
```python
def analyze_time_performance():
    # วิเคราะห์ผลตามวันในสัปดาห์
    # วิเคราะห์ผลตามชั่วโมง
    # สร้าง time filters
```

#### 6.3 Feature Importance
```python
def plot_feature_importance():
    # Gain importance
    # Split importance  
    # บันทึกกราฟและรายงาน
```

## 🔄 ขั้นตอนการทำงานแบบ Multi-Model

### **Step 1: Market Scenario Detection**
```python
def detect_market_scenario(row):
    if close > ema200 and low > ema200:
        return 'uptrend'
    elif close < ema200 and high < ema200:
        return 'downtrend'
    else:
        return 'sideways'
```

### **Step 2: Data Filtering**
```python
def filter_data_by_scenario(df, scenario_name):
    # กรองข้อมูลตาม market condition
    # เช่น trend_following_buy ใช้เฉพาะ uptrend data
```

### **Step 3: Model Training per Scenario**
```python
def train_scenario_model(X, y, scenario_name):
    # เทรนโมเดลเฉพาะสถานการณ์
    # ใช้ข้อมูลที่กรองแล้ว
    # บันทึกโมเดลแยกกัน
```

### **Step 4: Model Selection & Prediction**
```python
def create_trade_cycles_with_multi_model():
    # ตรวจจับสถานการณ์ตลาดปัจจุบัน
    # เลือกโมเดลที่เหมาะสม
    # ทำนายและสร้างสัญญาณ
```

## 📊 Entry Conditions (เงื่อนไขการเข้าเทรด)

### **Scenario 1: Trend Following Buy**
```python
"trend_following_buy": {
    "buy": lambda prev: (
        prev['close'] > prev['open'] and
        prev['close'] > prev['ema200'] and
        prev['rsi14'] > input_rsi_level_in * 0.8 and
        prev['macd_signal'] == 1.0 and
        prev['volume'] > prev['volume_ma20'] * 0.5 and
        prev['pullback_buy'] > input_pull_back * 0.5 and
        prev['ratio_buy'] > (input_take_profit * 1.5)
    )
}
```

### **Scenario 2: Counter Trend Sell**
```python
"counter_trend_sell": {
    "sell": lambda prev: (
        prev['close'] < prev['open'] and
        prev['close'] > prev['ema200'] and  # ยังอยู่เหนือ EMA200
        prev['rsi14'] > 65 and  # overbought
        prev['macd_signal'] == -1.0 and
        prev['volume'] > prev['volume_ma20'] * 0.8
    )
}
```

## 🎯 การใช้งานจริง

### **Command Line Usage**
```bash
python python_LightGBM_16_Signal.py --symbols GOLD --timeframes 60
python python_LightGBM_16_Signal.py --symbols EURUSD,GBPUSD --timeframes 30,60
```

### **Output Files**
- **Models:** `Test_LightGBM/models/060_GOLD/`
- **Results:** `Test_LightGBM/results/060_GOLD_trading_summary.txt`
- **Thresholds:** `Test_LightGBM/thresholds/GOLD_60_threshold.pkl`
- **Features:** `Test_LightGBM/feature_importance/`

## 🔧 การตั้งค่าสำคัญ

### **Multi-Model vs Single Model**
```python
USE_MULTI_MODEL_ARCHITECTURE = True  # เปิด/ปิด Multi-Model
```

### **Target Type**
```python
USE_MULTICLASS_TARGET = True  # เปิด/ปิด Multi-Class
```

### **Training Parameters**
```python
NUM_TRAINING_ROUNDS = 3  # จำนวนรอบการเทรน
MIN_TRAINING_SAMPLES = 200  # ข้อมูลขั้นต่ำต่อ scenario
```

## 📈 ผลลัพธ์ที่คาดหวัง

### **Model Performance**
- **Accuracy:** 60-70%
- **AUC:** 0.70-0.85
- **Win Rate:** 30-50%
- **Expectancy:** บวก

### **Trading Performance**
- **Sharpe Ratio:** > 1.0
- **Max Drawdown:** < 20%
- **Profit Factor:** > 1.2

## 🔄 ลำดับการทำงานแบบละเอียด (Step-by-Step)

### **Main Execution Flow**

#### **1. Initialization & Setup**
```python
def main():
    # 1.1 Parse command line arguments
    args = parse_arguments()

    # 1.2 Load configuration
    symbols = args.symbols or ['EURUSD', 'GBPUSD', ...]
    timeframes = args.timeframes or [30, 60]

    # 1.3 Setup directories
    create_directories()
```

#### **2. Data Processing Loop**
```python
for symbol in symbols:
    for timeframe in timeframes:
        # 2.1 Load raw data
        df = load_and_process_data(csv_file)

        # 2.2 Create technical indicators
        df = add_technical_indicators(df)

        # 2.3 Create features
        df = create_features(df)

        # 2.4 Generate trading signals
        trade_df = create_trade_cycles_with_model(df)
```

#### **3. Model Training Decision**
```python
if USE_MULTI_MODEL_ARCHITECTURE:
    # 3.1 Multi-Model Training
    models = train_all_scenario_models(df, trade_df)
else:
    # 3.2 Single Model Training
    model = train_and_evaluate(df, trade_df)
```

#### **4. Model Optimization**
```python
# 4.1 Hyperparameter tuning
if enable_hyperparameter_tuning:
    best_params = hyperparameter_tuning(X, y)

# 4.2 Threshold optimization
optimal_threshold = find_optimal_threshold(model, val_data)

# 4.3 nBars SL optimization
optimal_nbars = find_optimal_nbars_sl(val_data)
```

#### **5. Model Evaluation**
```python
# 5.1 Performance evaluation
results = evaluate_model_performance(model, test_data)

# 5.2 Time analysis
time_analysis = analyze_time_performance(trade_df)

# 5.3 Feature importance
feature_importance = analyze_feature_importance(model)
```

#### **6. Results & Reports**
```python
# 6.1 Save models
save_models(models, model_path)

# 6.2 Generate reports
generate_trading_summary(results, output_path)

# 6.3 Create visualizations
plot_results(trade_df, results)
```

## 🧠 ฟังก์ชันหลักและการทำงาน

### **1. load_and_process_data()**
```python
def load_and_process_data(csv_file):
    # โหลดข้อมูล CSV
    df = pd.read_csv(csv_file, sep='\t')

    # แปลง DateTime
    df['DateTime'] = pd.to_datetime(df['DateTime'])

    # ตรวจสอบคุณภาพข้อมูล
    validate_data_quality(df)

    # เพิ่ม technical indicators
    df = add_all_indicators(df)

    return df
```

### **2. create_trade_cycles_with_model()**
```python
def create_trade_cycles_with_model(df, model, features):
    trades = []

    for i in range(start_index, len(df)):
        # ตรวจสอบ entry conditions
        if check_entry_conditions(df.iloc[i-1]):

            # ทำนายด้วย ML model
            prediction = model.predict(features[i])

            if prediction > threshold:
                # สร้าง trade
                trade = create_trade_entry(df, i)
                trades.append(trade)

    return pd.DataFrame(trades)
```

### **3. train_all_scenario_models()**
```python
def train_all_scenario_models(df, trade_df):
    models = {}

    for scenario_name in MARKET_SCENARIOS:
        # กรองข้อมูลตาม scenario
        scenario_data = filter_data_by_scenario(df, scenario_name)

        # เตรียม features และ targets
        X, y = prepare_scenario_data(scenario_data, trade_df)

        # เทรนโมเดล
        model = train_scenario_model(X, y, scenario_name)
        models[scenario_name] = model

    return models
```

### **4. Entry Conditions Logic**
```python
def check_entry_conditions(prev_row):
    # สร้าง prev_dict จากข้อมูลแถวก่อนหน้า
    prev_dict = {
        'close': prev_row['Close'],
        'open': prev_row['Open'],
        'rsi14': prev_row['RSI14'],
        'macd_signal': prev_row['MACD_signal'],
        # ... other indicators
    }

    # ตรวจสอบเงื่อนไข buy/sell
    if entry_func['buy'](prev_dict):
        return 'buy'
    elif entry_func['sell'](prev_dict):
        return 'sell'

    return None
```

## 📊 Data Flow Diagram

```
Raw CSV Data
     ↓
Technical Indicators
     ↓
Feature Engineering
     ↓
Trading Signals Generation
     ↓
Target Variable Creation
     ↓
Feature Selection
     ↓
Data Splitting (Train/Val/Test)
     ↓
Model Training (Single/Multi)
     ↓
Hyperparameter Optimization
     ↓
Threshold Optimization
     ↓
Model Evaluation
     ↓
Results & Reports
```

## 🎯 Key Decision Points

### **1. Architecture Selection**
```python
if USE_MULTI_MODEL_ARCHITECTURE:
    # ใช้ 4 โมเดลแยกตามสถานการณ์
else:
    # ใช้โมเดลเดียวสำหรับทุกสถานการณ์
```

### **2. Target Type Selection**
```python
if USE_MULTICLASS_TARGET:
    # 5 classes: strong_sell, weak_sell, no_trade, weak_buy, strong_buy
else:
    # Binary: buy (1) vs no_buy (0)
```

### **3. Feature Selection Strategy**
```python
if use_must_have_features:
    # ใช้ features ที่วิเคราะห์ไว้แล้ว
else:
    # ใช้ correlation-based selection
```

---

**สรุป:** ระบบนี้เป็น Advanced Trading System ที่ผลิตสัญญาณการซื้อขายโดยใช้ Machine Learning ร่วมกับ Technical Analysis พร้อมระบบ Multi-Model Architecture ที่ปรับตัวตามสถานการณ์ตลาด

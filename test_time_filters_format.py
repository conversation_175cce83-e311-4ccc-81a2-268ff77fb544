#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแปลง Time Filters ให้อ่านง่าย
"""

def format_days(days_str):
    """แปลง Days ให้อ่านง่าย"""
    if not days_str or days_str.strip() == "":
        return ""
    
    # ตรวจสอบว่าเป็นทุกวันหรือไม่
    if "0,1,2,3,4,5,6" in days_str.replace(" ", ""):
        return "Every Day"
    
    # ตรวจสอบวันทำงาน
    if "0,1,2,3,4" in days_str.replace(" ", "") and len(days_str.replace(" ", "").replace(",", "")) == 5:
        return "Weekdays"
    
    # ตรวจสอบวันหยุด
    if "5,6" in days_str.replace(" ", "") and len(days_str.replace(" ", "").replace(",", "")) == 2:
        return "Weekends"
    
    # แปลงเป็นชื่อวัน
    day_names = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
    result = []
    
    for i in range(7):
        if str(i) in days_str:
            result.append(day_names[i])
    
    return ",".join(result) if result else days_str

def format_hours(hours_str):
    """แปลง Hours ให้อ่านง่าย"""
    if not hours_str or hours_str.strip() == "":
        return ""
    
    # ตรวจสอบว่าเป็นทุกชั่วโมงหรือไม่
    all_hours = all(str(i) in hours_str for i in range(24))
    if all_hours:
        return "24/7"
    
    # Extract hours
    hours = []
    for i in range(24):
        if str(i) in hours_str:
            hours.append(i)
    
    if not hours:
        return hours_str
    
    # Sort hours
    hours.sort()
    
    # Create ranges
    ranges = []
    start = hours[0]
    end = hours[0]
    
    for i in range(1, len(hours)):
        if hours[i] == end + 1:
            end = hours[i]
        else:
            # Add current range
            if start == end:
                ranges.append(str(start))
            else:
                ranges.append(f"{start}-{end}")
            start = hours[i]
            end = hours[i]
    
    # Add final range
    if start == end:
        ranges.append(str(start))
    else:
        ranges.append(f"{start}-{end}")
    
    return ", ".join(ranges)

def format_time_filters(time_filters):
    """แปลง Time Filters ให้อ่านง่าย"""
    if not time_filters:
        return "No filters"
    
    # แยก Days และ Hours
    days_part = ""
    hours_part = ""
    
    # หา Days part
    if "Days:[" in time_filters:
        start = time_filters.find("Days:[") + 6
        end = time_filters.find("]", start)
        if end > start:
            days_part = time_filters[start:end]
    
    # หา Hours part
    if "Hours:[" in time_filters:
        start = time_filters.find("Hours:[") + 7
        end = time_filters.find("]", start)
        if end > start:
            hours_part = time_filters[start:end]
    
    # แปลง Days และ Hours
    days_formatted = format_days(days_part)
    hours_formatted = format_hours(hours_part)
    
    # รวมผลลัพธ์
    if days_formatted and hours_formatted:
        return f"{days_formatted}, {hours_formatted}"
    elif days_formatted:
        return days_formatted
    elif hours_formatted:
        return hours_formatted
    else:
        return "No filters"

def test_time_filters_formatting():
    """ทดสอบการแปลง Time Filters"""
    
    print("🕒 ทดสอบการแปลง Time Filters ให้อ่านง่าย")
    print("="*60)
    
    # ตัวอย่าง Time Filters ต่างๆ
    test_cases = [
        # ทุกวัน ทุกชั่วโมง
        {
            "input": "Days:[0,1,2,3,4,5,6],Hours:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23]",
            "description": "ทุกวัน ทุกชั่วโมง"
        },
        
        # ทุกวัน ช่วงเวลาเฉพาะ
        {
            "input": "Days:[0,1,2,3,4,5,6],Hours:[7,8,11,21]",
            "description": "ทุกวัน ช่วงเวลาเฉพาะ"
        },
        
        # วันทำงาน ทุกชั่วโมง
        {
            "input": "Days:[0,1,2,3,4],Hours:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23]",
            "description": "วันทำงาน ทุกชั่วโมง"
        },
        
        # วันทำงาน เวลาทำงาน
        {
            "input": "Days:[0,1,2,3,4],Hours:[9,10,11,12,13,14,15,16]",
            "description": "วันทำงาน เวลาทำงาน"
        },
        
        # วันหยุด
        {
            "input": "Days:[5,6],Hours:[10,11,12,13,14,15,16,17,18,19,20]",
            "description": "วันหยุด"
        },
        
        # วันเฉพาะ ช่วงเวลาเฉพาะ
        {
            "input": "Days:[1,3,5],Hours:[6,7,8,9,10,11,12,19,20,21]",
            "description": "วันเฉพาะ ช่วงเวลาเฉพาะ"
        },
        
        # ช่วงเวลาต่อเนื่อง
        {
            "input": "Days:[0,1,2,3,4],Hours:[8,9,10,11,12,13,14,15,16,17]",
            "description": "วันทำงาน ช่วงเวลาต่อเนื่อง"
        },
        
        # ช่วงเวลาแยกกัน
        {
            "input": "Days:[0,1,2,3,4],Hours:[8,9,10,14,15,16,20,21,22]",
            "description": "วันทำงาน ช่วงเวลาแยกกัน"
        },
        
        # ตัวอย่างจากระบบจริง
        {
            "input": "Days:[0, 1, 2, 3, 4, 5, 6],Hours:[7, 8, 11, 21]",
            "description": "ตัวอย่างจากระบบจริง (มีช่องว่าง)"
        }
    ]
    
    print("📊 ผลการทดสอบ:")
    print("-" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        input_str = test_case["input"]
        description = test_case["description"]
        formatted = format_time_filters(input_str)
        
        print(f"\n{i}. {description}")
        print(f"   Input:  {input_str}")
        print(f"   Output: {formatted}")
        
        # ตรวจสอบความยาว
        if len(formatted) > 23:
            print(f"   ⚠️  ความยาว: {len(formatted)} ตัวอักษร (เกิน 23)")
            # แสดงการแบ่งบรรทัด
            line1 = formatted[:23]
            line2 = formatted[23:]
            print(f"   Line 1: {line1}")
            if line2:
                print(f"   Line 2: {line2}")
        else:
            print(f"   ✅ ความยาว: {len(formatted)} ตัวอักษร")

def preview_display_format():
    """แสดงตัวอย่างการแสดงผลใน MT5"""
    
    print("\n" + "="*60)
    print("🎨 ตัวอย่างการแสดงผลใน MT5 EA")
    print("="*60)
    
    # ตัวอย่างข้อมูล
    test_data = {
        'symbol': 'GOLD',
        'timeframe': 'H1',
        'signal': 'HOLD',
        'class': 'HOLD',
        'confidence': 0.4522,
        'threshold': 0.500,
        'spread': 25,
        'sl_bars': 6,
        'time_filters_raw': 'Days:[0,1,2,3,4,5,6],Hours:[7,8,11,21]',
        'magic': 16098,
        'mt5_spread': 25,
        'point_value': 1.000
    }
    
    # แปลง time_filters
    formatted_filters = format_time_filters(test_data['time_filters_raw'])
    
    print("📊 ข้อมูลตัวอย่าง:")
    print(f"   Time Filters (Raw): {test_data['time_filters_raw']}")
    print(f"   Time Filters (Formatted): {formatted_filters}")
    print()
    
    # แสดงผลแบบ MT5
    print("📱 การแสดงผลใน MT5:")
    print("╔═══════════════════════════════════════╗")
    print("║           🐍 PYTHON ANALYSIS          ║")
    print("╠═══════════════════════════════════════╣")
    print(f"║ Symbol: {test_data['symbol']:<8}  Timeframe: {test_data['timeframe']:<4}    ║")
    print(f"║ Signal: {test_data['signal']:<8}  Class: {test_data['class']:<8}    ║")
    print(f"║ Confidence: {test_data['confidence']:<6.3f}  Threshold: {test_data['threshold']:<6.3f} ║")
    print(f"║ Spread: {test_data['spread']:<8}  SL_Bars: {test_data['sl_bars']:<6}    ║")
    
    # แสดง Time Filters
    if len(formatted_filters) <= 23:
        print(f"║ Time Filters: {formatted_filters:<23} ║")
    else:
        line1 = formatted_filters[:23]
        line2 = formatted_filters[23:]
        print(f"║ Time Filters: {line1:<23} ║")
        if line2:
            print(f"║               {line2:<23} ║")
    
    print("╠═══════════════════════════════════════╣")
    print("║           🔧 MT5 CONFIGURATION        ║")
    print("╠═══════════════════════════════════════╣")
    print(f"║ Symbol: GOLD#    Magic: {test_data['magic']:<10}   ║")
    print(f"║ MT5 Spread: {test_data['mt5_spread']:<6}  Point Value: {test_data['point_value']:<6.3f} ║")
    print(f"║ Python Spread: {test_data['spread']:<6}  SL_Bars: {test_data['sl_bars']:<6}   ║")
    print(f"║ Python Threshold: {test_data['threshold']:<18.3f} ║")
    print("╚═══════════════════════════════════════╝")

def main():
    """ฟังก์ชันหลัก"""
    print("🕒 การปรับปรุงการแสดง Time Filters ใน MT5 EA")
    print("="*60)
    print("🎯 วัตถุประสงค์:")
    print("   ✅ แปลง Time Filters ให้อ่านง่าย")
    print("   ✅ ลดความยาวของข้อความ")
    print("   ✅ ใช้คำที่เข้าใจง่าย")
    print("   ✅ จัดการกรณีข้อความยาวเกินไป")
    print("="*60)
    
    test_time_filters_formatting()
    preview_display_format()
    
    print("\n" + "="*60)
    print("🎉 สรุปการปรับปรุง:")
    print("   ✅ Days:[0,1,2,3,4,5,6] → Every Day")
    print("   ✅ Days:[0,1,2,3,4] → Weekdays")
    print("   ✅ Days:[5,6] → Weekends")
    print("   ✅ Hours:[0-23] → 24/7")
    print("   ✅ Hours:[8,9,10,11,12] → 8-12")
    print("   ✅ Hours:[8,9,10,14,15,16] → 8-10, 14-16")
    print("   ✅ จัดการข้อความยาวด้วยการแบ่งบรรทัด")

if __name__ == "__main__":
    main()

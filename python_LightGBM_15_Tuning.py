# Standard library imports
import os
import sys
import json
import datetime
import math
import traceback
import argparse
import warnings
warnings.filterwarnings('ignore')

# Third-party library imports
import pandas as pd
import numpy as np
import pandas_ta as ta
import talib  # เพิ่มสำหรับ advanced technical indicators
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.io as pioa
import joblib
from joblib import dump, load
from imblearn.over_sampling import SMOTE

# scikit-learn imports
from sklearn import __version__ as sklearn_version
from sklearn.model_selection import train_test_split, StratifiedKFold
# from sklearn.utils import resample  # สำหรับ oversampling
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import (accuracy_score, classification_report,
                            confusion_matrix, roc_auc_score,
                            f1_score, precision_score, recall_score,
                            roc_curve, precision_recall_curve, average_precision_score,
                            log_loss)
from sklearn.ensemble import RandomForestClassifier
import lightgbm as lgb
from sklearn.model_selection import cross_validate

from sklearn.model_selection import TimeSeriesSplit # เพิ่ม import TimeSeriesSplit
from sklearn.impute import SimpleImputer # เพิ่ม import สำหรับการเติมค่าว่าง
from statsmodels.tsa.stattools import adfuller
import pickle # Import pickle library
import time

import plotly.io as pio
from collections import Counter
import winsound

from sklearn.model_selection import RandomizedSearchCV, GridSearchCV

# ==============================================
# การตั้งค่าและกำหนดค่าพื้นฐาน (Configuration)
# ==============================================
# LightGBM (Light Gradient Boosting Machine) คืออัลกอริทึมสำหรับงาน Machine Learning ประเภท tree-based ensemble

# การตั้งค่าภาษา
sys.stdout.reconfigure(encoding='utf-8')

# การตั้งค่าพื้นฐาน
Plot_file = False  # True False ตั้งค่าเพื่อกำหนดว่าจะพล็อตกราฟหรือไม่
Steps_to_calculating = False
Steps_to_do = True

# ปรับเพิ่ม threshold เพื่อเพิ่ม win rate (เลือกเฉพาะสัญญาณที่แน่นอน)
input_initial_threshold = 0.55  # เพิ่มจาก 0.35 เป็น 0.55 เพื่อเพิ่ม win rate และลดสัญญาณรบกวน

# กำหนดเงื่อนไขการเทรด (ปรับเพื่อเพิ่ม win rate)
input_rsi_level_in = 35          # เพิ่มจาก 30 เป็น 35 - ให้สมดุลระหว่างความเข้มงวดและโอกาส
input_rsi_level_out = 30         # เพิ่มจาก 25 เป็น 30 - ออกเมื่อ RSI กลับมาแรงกว่า
input_stop_loss_atr = 1.5        # ลดจาก 1.8 เป็น 1.5 - SL ใกล้กว่าเพื่อลดความเสี่ยง
# ปรับเพิ่ม take profit ratio เพื่อเพิ่ม win rate (ให้ TP ห่างจาก noise)
input_take_profit = 2.5          # เพิ่มจาก 2.0 เป็น 2.5 เพื่อ Risk:Reward = 1:2.5
input_pull_back = 0.40           # เพิ่มจาก 0.35 เป็น 0.40 เพื่อเลือกสัญญาณที่แข็งแกร่งกว่า

# เพิ่มพารามิเตอร์สำหรับ High-Quality Entry Filters
MIN_ATR_THRESHOLD = 0.0008       # ATR ขั้นต่ำเพื่อหลีกเลี่ยง low volatility periods
MIN_VOLUME_MULTIPLIER = 1.2      # Volume ต้องมากกว่า average อย่างน้อย 20%
TREND_CONFIRMATION_PERIODS = 3   # จำนวนแท่งที่ต้องยืนยัน trend

# เพิ่มพารามิเตอร์สำหรับ Minimum Learning Requirements
MIN_TRAINING_SAMPLES = 1000      # จำนวนตัวอย่างขั้นต่ำสำหรับการเทรน
MIN_POSITIVE_SAMPLES = 50        # จำนวนตัวอย่าง positive class ขั้นต่ำ
MIN_MODEL_ACCURACY = 0.60        # Accuracy ขั้นต่ำที่ยอมรับได้ (60%)
MIN_MODEL_AUC = 0.70             # AUC ขั้นต่ำที่ยอมรับได้ (70%)
MIN_WIN_RATE_TARGET = 0.40       # Win Rate เป้าหมายขั้นต่ำ (40%)

# กำหนดจำนวนรอบการเทรนที่นี่ (เพิ่มเพื่อให้โมเดลเรียนรู้เพียงพอ)
# ⚠️ แก้ไขปัญหาการวนลูปไม่สิ้นสุด: เมื่อครบ NUM_TRAINING_ROUNDS จะหยุดอัตโนมัติ
NUM_TRAINING_ROUNDS = 15 # เพิ่มจาก 1 เป็น 5 รอบ เพื่อ ensemble และเรียนรู้ที่หลากหลาย
Save_File = True

# ทำ Hyperparameter Tuning สำหรับ LightGBM
# tuning เฉพาะในรอบแรก หรือเมื่อ train ข้อมูลใหม่เท่านั้น (ไม่ควร tune ทุกครั้งที่รัน)
# ควร tune เฉพาะครั้งแรก หรือเมื่อข้อมูลเปลี่ยน
# ตอนนี้ใช้ระบบ flag ตาม symbol แทน global flag
do_hyperparameter_tuning = True

# ==============================================
# Multi-class Target Configuration
# ==============================================
USE_MULTICLASS_TARGET = True        # เปิด/ปิด multi-class classification
# ปรับ Profit Thresholds เพื่อเพิ่ม win rate (เน้น quality over quantity)
# อัปเดต 2025-07-07: ปรับให้เหมาะสมกับการเพิ่ม win rate 30-50%
PROFIT_THRESHOLDS = {
    'strong_buy': 60,               # ลดจาก 80 เป็น 60 points = Strong Buy (class 4) - เป้าหมายที่เข้าถึงได้มากขึ้น
    'weak_buy': 20,                 # ลดจาก 25 เป็น 20 points = Weak Buy (class 3) - ใกล้เคียง spread
    'no_trade': -20,                # ปรับจาก -25 เป็น -20 points = No Trade (class 2) - ลด noise zone
    'weak_sell': -60,               # ปรับจาก -80 เป็น -60 points = Weak Sell (class 1) - สมมาตรกับ strong_buy
    'strong_sell': float('-inf')    # ขาดทุนเกิน 60 points = Strong Sell (class 0) - เหมือนเดิม
}

# Class mapping for multi-class target
CLASS_MAPPING = {
    0: 'strong_sell',    # ขาดทุนมาก
    1: 'weak_sell',      # ขาดทุนปานกลาง
    2: 'no_trade',       # ไม่ควรเทรด
    3: 'weak_buy',       # กำไรปานกลาง
    4: 'strong_buy'      # กำไรมาก
}

test_groups = {
    "M30": [
        "CSV_Files_Fixed/AUDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/EURGBP_M30_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GBPUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GOLD_M30_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_M30_FIXED.csv"
    ],
    "M60": [
        "CSV_Files_Fixed/AUDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/EURGBP_H1_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/GOLD_H1_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_H1_FIXED.csv"
    ]
}

entry_conditions = {
    "default": {
        "buy": lambda prev: (
            prev['close'] > prev['open'] and
            prev['rsi14'] > input_rsi_level_in and
            prev['macd_signal'] == 1.0 and
            prev['volume'] > prev['volume_ma20'] * 0.8 and
            prev['pullback_buy'] > input_pull_back and
            prev['ratio_buy'] > (input_take_profit * 3.0)
        ),
        "sell": lambda prev: (
            prev['close'] < prev['open'] and
            prev['rsi14'] < (100 - input_rsi_level_in) and
            prev['macd_signal'] == -1.0 and
            prev['volume'] > prev['volume_ma20'] * 0.8 and
            prev['pullback_sell'] > input_pull_back and
            prev['ratio_sell'] > (input_take_profit * 3.0)
        )
    },
    "entry_v1": {
        "buy": lambda prev: (
            prev['close'] > prev['open'] and
            prev['close'] > prev['ema50'] and
            prev['rsi14'] > input_rsi_level_in and
            prev['macd_signal'] == 1.0 and
            prev['volume'] > prev['volume_ma20'] * 0.8 and
            prev['pullback_buy'] > input_pull_back and
            prev['ratio_buy'] > (input_take_profit * 3.0)
        ),
        "sell": lambda prev: (
            prev['close'] < prev['open'] and
            prev['close'] < prev['ema50'] and
            prev['rsi14'] < (100 - input_rsi_level_in) and
            prev['macd_signal'] == -1.0 and
            prev['volume'] > prev['volume_ma20'] * 0.8 and
            prev['pullback_sell'] > input_pull_back and
            prev['ratio_sell'] > (input_take_profit * 3.0)
        )
    },
    "entry_v2": {
        "buy": lambda prev: (
            prev['close'] > prev['open'] and
            prev['close'] > prev['ema200'] and
            prev['rsi14'] > input_rsi_level_in and
            prev['macd_signal'] == 1.0 and
            prev['volume'] > prev['volume_ma20'] * 0.8 and
            prev['pullback_buy'] > input_pull_back and
            prev['ratio_buy'] > (input_take_profit * 3.0)
        ),
        "sell": lambda prev: (
            prev['close'] < prev['open'] and
            prev['close'] < prev['ema200'] and
            prev['rsi14'] < (100 - input_rsi_level_in) and
            prev['macd_signal'] == -1.0 and
            prev['volume'] > prev['volume_ma20'] * 0.8 and
            prev['pullback_sell'] > input_pull_back and
            prev['ratio_sell'] > (input_take_profit * 3.0)
        )
    },
    "entry_v3": {
        "buy": lambda prev: (
            prev['close'] > prev['open'] and
            prev['close'] > prev['ema50'] and
            prev['close'] > prev['ema200'] and
            prev['rsi14'] > input_rsi_level_in and
            prev['macd_signal'] == 1.0 and
            prev['volume'] > prev['volume_ma20'] * 0.8 and
            prev['pullback_buy'] > input_pull_back and
            prev['ratio_buy'] > (input_take_profit * 3.0)
        ),
        "sell": lambda prev: (
            prev['close'] < prev['open'] and
            prev['close'] < prev['ema50'] and
            prev['close'] < prev['ema200'] and
            prev['rsi14'] < (100 - input_rsi_level_in) and
            prev['macd_signal'] == -1.0 and
            prev['volume'] > prev['volume_ma20'] * 0.8 and
            prev['pullback_sell'] > input_pull_back and
            prev['ratio_sell'] > (input_take_profit * 3.0)
        )
    }
}

symbol_info = {
    "GOLD":   {"Spread": 25, "Digits": 2, "Points": 0.01, "Swap_Long": 0, "Swap_Short": 0},
    "AUDUSD": {"Spread": 15, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "EURGBP": {"Spread": 22, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "EURUSD": {"Spread": 13, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "GBPUSD": {"Spread": 25, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "NZDUSD": {"Spread": 22, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "USDCAD": {"Spread": 28, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "USDJPY": {"Spread": 16, "Digits": 3, "Points": 0.001, "Swap_Long": 0, "Swap_Short": 0}
}

timeframe_map = {"M1": 1, "M5": 5, "M15": 15, "M30": 30, "H1": 60, "H2": 120, "H4": 240, "D1": 1440}

# ตรวจสอบว่าโฟลเดอร์
print(f"\n{'='*50}")
print(f"สร้างและตรวจสอบ โฟลเดอร์")

test_folder = "Test_LightGBM"
if not os.path.exists(test_folder):
    os.makedirs(test_folder)
    print(f"สร้างโฟลเดอร์ {test_folder} เรียบร้อยแล้ว")

output_folder = "Test_LightGBM/results"
if not os.path.exists(output_folder):
    os.makedirs(output_folder)
    print(f"สร้างโฟลเดอร์ {output_folder} เรียบร้อยแล้ว")

# ==============================================
# data_processing.py
# ฟังก์ชันเกี่ยวกับการโหลด, เตรียม, สร้างฟีเจอร์, ตรวจสอบคุณภาพข้อมูล, สร้าง target ฯลฯ
# ==============================================

def analyze_time_filters(trade_df, min_win_rate=0.25, min_expectancy=0.0, save_path=None, symbol=None):
    print(f"\n🏗️ เปิดใช้งาน analyze time filters") if Steps_to_do else None

    """
    วิเคราะห์ win rate/expectancy รายวันและรายชั่วโมงใน trade_df
    คืน dict ของวันที่/ชั่วโมงที่ควรเทรด และบันทึกไฟล์ filter
    เพิ่มการวิเคราะห์แบบละเอียดสำหรับการใช้งานจริง
    """

    # ตรวจสอบว่ามี target column ที่เหมาะสม
    target_col = "Target_Multiclass" if USE_MULTICLASS_TARGET and "Target_Multiclass" in trade_df.columns else "Target"

    result = {'days': [], 'hours': [], 'detailed_stats': {}}

    # วิเคราะห์รายวัน (0=Monday, 1=Tuesday, ..., 6=Sunday)
    day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

    if USE_MULTICLASS_TARGET and target_col == "Target_Multiclass":
        # สำหรับ multi-class ใช้ win rate จาก class 3 และ 4 (weak_buy, strong_buy)
        trade_df_copy = trade_df.copy()
        trade_df_copy['Win'] = (trade_df_copy[target_col] >= 3).astype(int)

        day_stats = trade_df_copy.groupby('Entry_DayOfWeek').agg({
            'Win': ['mean', 'count'],
            'Profit': 'mean',
            target_col: [
                lambda x: (x == 4).mean(),  # strong_buy_rate
                lambda x: (x == 3).mean(),  # weak_buy_rate
                lambda x: (x == 2).mean()   # no_trade_rate
            ]
        })

        # Flatten column names
        day_stats.columns = ['win_rate', 'total', 'expectancy', 'strong_buy_rate', 'weak_buy_rate', 'no_trade_rate']
    else:
        # สำหรับ binary classification
        day_stats = trade_df.groupby('Entry_DayOfWeek').agg({
            target_col: ['mean', 'count'],
            'Profit': 'mean'
        })

        # Flatten column names
        day_stats.columns = ['win_rate', 'total', 'expectancy']

    # เก็บสถิติรายละเอียด
    result['detailed_stats']['days'] = {}
    for day_idx, stats in day_stats.iterrows():
        day_name = day_names[day_idx] if day_idx < len(day_names) else f"Day_{day_idx}"
        result['detailed_stats']['days'][day_name] = {
            'win_rate': stats['win_rate'],
            'expectancy': stats['expectancy'],
            'total_trades': stats['total'],
            'day_index': day_idx
        }

        # เพิ่มข้อมูล multi-class ถ้ามี
        if USE_MULTICLASS_TARGET and target_col == "Target_Multiclass":
            result['detailed_stats']['days'][day_name].update({
                'strong_buy_rate': stats['strong_buy_rate'],
                'weak_buy_rate': stats['weak_buy_rate'],
                'no_trade_rate': stats['no_trade_rate']
            })

    # กรองวันที่ควรเทรด (ปรับเกณฑ์ให้เหมาะสม)
    result['days'] = day_stats[
        (day_stats['win_rate'] >= min_win_rate) &
        (day_stats['expectancy'] > min_expectancy) &
        (day_stats['total'] >= 10)  # ต้องมีข้อมูลอย่างน้อย 10 trades
    ].index.tolist()

    # วิเคราะห์รายชั่วโมง
    if USE_MULTICLASS_TARGET and target_col == "Target_Multiclass":
        hour_stats = trade_df_copy.groupby('Entry_Hour').agg({
            'Win': ['mean', 'count'],
            'Profit': 'mean',
            target_col: lambda x: (x == 4).mean()  # strong_buy_rate
        })

        # Flatten column names
        hour_stats.columns = ['win_rate', 'total', 'expectancy', 'strong_buy_rate']
    else:
        hour_stats = trade_df.groupby('Entry_Hour').agg({
            target_col: ['mean', 'count'],
            'Profit': 'mean'
        })

        # Flatten column names
        hour_stats.columns = ['win_rate', 'total', 'expectancy']

    # เก็บสถิติรายชั่วโมง
    result['detailed_stats']['hours'] = {}
    for hour, stats in hour_stats.iterrows():
        result['detailed_stats']['hours'][hour] = {
            'win_rate': stats['win_rate'],
            'expectancy': stats['expectancy'],
            'total_trades': stats['total']
        }

        if USE_MULTICLASS_TARGET and target_col == "Target_Multiclass":
            result['detailed_stats']['hours'][hour]['strong_buy_rate'] = stats['strong_buy_rate']

    # กรองชั่วโมงที่ควรเทรด
    result['hours'] = hour_stats[
        (hour_stats['win_rate'] >= min_win_rate) &
        (hour_stats['expectancy'] > min_expectancy) &
        (hour_stats['total'] >= 5)  # ต้องมีข้อมูลอย่างน้อย 5 trades
    ].index.tolist()

    # แสดงผลสถิติ
    print(f"\n📊 Time Filter Analysis for {symbol if symbol else 'Unknown'}:")
    print(f"📅 Recommended Days: {[day_names[d] for d in result['days']]}")
    print(f"⏰ Recommended Hours: {sorted(result['hours'])}")

    # บันทึกไฟล์
    if save_path and Save_File:
        # ตรวจสอบและสร้าง directory ถ้าจำเป็น
        save_dir = os.path.dirname(save_path)
        if save_dir:  # ถ้ามี directory path
            os.makedirs(save_dir, exist_ok=True)

        with open(save_path, 'wb') as f:
            pickle.dump(result, f)
        print(f"✅ บันทึก time filter ที่: {save_path}")

    return result

def analyze_time_filter_advanced(trade_df, min_win_rate=0.40, min_expectancy=8.0, output_folder="Test_LightGBM/results"):
    """
    วิเคราะห์ time filter แบบละเอียดและสร้างแนะนำการเทรดรายวัน
    คืน dict ของวันที่/ชั่วโมงที่ควรเทรด และบันทึกไฟล์ filter
    เพิ่มการวิเคราะห์แบบละเอียดสำหรับการใช้งานจริง
    อัปเดต 2025-07-07: เพิ่มเกณฑ์เพื่อเพิ่ม win rate 30-50%
    """
    print(f"\n🔬 เริ่มการวิเคราะห์ Time Filter แบบขั้นสูง")
    print("="*60)

    # ตรวจสอบว่ามี target column ที่เหมาะสม
    target_col = "Target_Multiclass" if USE_MULTICLASS_TARGET and "Target_Multiclass" in trade_df.columns else "Target"

    result = {'days': [], 'hours': [], 'detailed_stats': {}, 'recommendations': {}}

    # วิเคราะห์รายวัน (0=Monday, 1=Tuesday, ..., 6=Sunday)
    day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

    if USE_MULTICLASS_TARGET and target_col == "Target_Multiclass":
        # สำหรับ multi-class ใช้ win rate จาก class 3 และ 4 (weak_buy, strong_buy)
        trade_df_copy = trade_df.copy()
        trade_df_copy['Win'] = (trade_df_copy[target_col] >= 3).astype(int)

        day_stats = trade_df_copy.groupby('Entry_DayOfWeek').agg({
            'Win': ['mean', 'count'],
            'Profit': ['mean', 'std'],
            target_col: [lambda x: (x == 4).mean(), lambda x: (x == 3).mean(), lambda x: (x == 2).mean()]
        })
        day_stats.columns = ['win_rate', 'total', 'expectancy', 'profit_std', 'strong_buy_rate', 'weak_buy_rate', 'no_trade_rate']
    else:
        # สำหรับ binary classification
        day_stats = trade_df.groupby('Entry_DayOfWeek').agg({
            target_col: ['mean', 'count'],
            'Profit': ['mean', 'std']
        })
        day_stats.columns = ['win_rate', 'total', 'expectancy', 'profit_std']

    result['detailed_stats']['days'] = {}
    for day_idx, stats in day_stats.iterrows():
        day_name = day_names[day_idx] if day_idx < len(day_names) else f"Day_{day_idx}"

        # คำนวณ Sharpe-like ratio สำหรับการเทรด
        sharpe_ratio = stats['expectancy'] / stats['profit_std'] if stats['profit_std'] > 0 else 0

        result['detailed_stats']['days'][day_name] = {
            'win_rate': stats['win_rate'],
            'expectancy': stats['expectancy'],
            'profit_std': stats['profit_std'],
            'sharpe_ratio': sharpe_ratio,
            'total_trades': stats['total'],
            'day_index': day_idx,
            'quality_score': stats['win_rate'] * stats['expectancy'] * (1 / (1 + stats['profit_std']))  # คะแนนคุณภาพรวม
        }

        # เพิ่มข้อมูล multi-class ถ้ามี
        if USE_MULTICLASS_TARGET and target_col == "Target_Multiclass":
            result['detailed_stats']['days'][day_name].update({
                'strong_buy_rate': stats['strong_buy_rate'],
                'weak_buy_rate': stats['weak_buy_rate'],
                'no_trade_rate': stats['no_trade_rate']
            })

    # กรองวันที่ควรเทรด (เกณฑ์เข้มงวดขึ้น)
    result['days'] = day_stats[
        (day_stats['win_rate'] >= min_win_rate) &
        (day_stats['expectancy'] > min_expectancy) &
        (day_stats['total'] >= 15)  # เพิ่มจาก 10 เป็น 15 trades
    ].index.tolist()

    # วิเคราะห์รายชั่วโมง
    if USE_MULTICLASS_TARGET and target_col == "Target_Multiclass":
        hour_stats = trade_df_copy.groupby('Entry_Hour').agg({
            'Win': ['mean', 'count'],
            'Profit': ['mean', 'std'],
            target_col: lambda x: (x == 4).mean()  # strong_buy_rate
        })
        hour_stats.columns = ['win_rate', 'total', 'expectancy', 'profit_std', 'strong_buy_rate']
    else:
        hour_stats = trade_df.groupby('Entry_Hour').agg({
            target_col: ['mean', 'count'],
            'Profit': ['mean', 'std']
        })
        hour_stats.columns = ['win_rate', 'total', 'expectancy', 'profit_std']

    result['detailed_stats']['hours'] = {}
    for hour, stats in hour_stats.iterrows():
        # คำนวณ Sharpe-like ratio สำหรับการเทรด
        sharpe_ratio = stats['expectancy'] / stats['profit_std'] if stats['profit_std'] > 0 else 0

        result['detailed_stats']['hours'][hour] = {
            'win_rate': stats['win_rate'],
            'expectancy': stats['expectancy'],
            'profit_std': stats['profit_std'],
            'sharpe_ratio': sharpe_ratio,
            'total_trades': stats['total'],
            'quality_score': stats['win_rate'] * stats['expectancy'] * (1 / (1 + stats['profit_std']))
        }

        if USE_MULTICLASS_TARGET and target_col == "Target_Multiclass":
            result['detailed_stats']['hours'][hour]['strong_buy_rate'] = stats['strong_buy_rate']

    # กรองชั่วโมงที่ควรเทรด (เกณฑ์เข้มงวดขึ้น)
    result['hours'] = hour_stats[
        (hour_stats['win_rate'] >= min_win_rate) &
        (hour_stats['expectancy'] > min_expectancy) &
        (hour_stats['total'] >= 8)  # เพิ่มจาก 5 เป็น 8 trades
    ].index.tolist()

    # สร้างคำแนะนำแบบละเอียด
    result['recommendations'] = {
        'best_days': [],
        'best_hours': [],
        'avoid_days': [],
        'avoid_hours': [],
        'optimal_combinations': []
    }

    # หาวันที่ดีที่สุด (top 3)
    day_quality_scores = [(day, data['quality_score']) for day, data in result['detailed_stats']['days'].items()]
    day_quality_scores.sort(key=lambda x: x[1], reverse=True)
    result['recommendations']['best_days'] = [day for day, _ in day_quality_scores[:3]]

    # หาชั่วโมงที่ดีที่สุด (top 5)
    hour_quality_scores = [(hour, data['quality_score']) for hour, data in result['detailed_stats']['hours'].items()]
    hour_quality_scores.sort(key=lambda x: x[1], reverse=True)
    result['recommendations']['best_hours'] = [hour for hour, _ in hour_quality_scores[:5]]

    # หาวันที่ควรหลีกเลี่ยง (bottom 2)
    result['recommendations']['avoid_days'] = [day for day, _ in day_quality_scores[-2:]]

    # หาชั่วโมงที่ควรหลีกเลี่ยง (bottom 3)
    result['recommendations']['avoid_hours'] = [hour for hour, _ in hour_quality_scores[-3:]]

    # หาการผสมผสานที่ดีที่สุด (วัน + ชั่วโมง)
    optimal_combinations = []
    for best_day in result['recommendations']['best_days'][:2]:  # เอา 2 วันที่ดีที่สุด
        day_idx = result['detailed_stats']['days'][best_day]['day_index']
        for best_hour in result['recommendations']['best_hours'][:3]:  # เอา 3 ชั่วโมงที่ดีที่สุด
            # คำนวณคะแนนรวม
            day_score = result['detailed_stats']['days'][best_day]['quality_score']
            hour_score = result['detailed_stats']['hours'][best_hour]['quality_score']
            combined_score = (day_score + hour_score) / 2

            optimal_combinations.append({
                'day': best_day,
                'day_index': day_idx,
                'hour': best_hour,
                'combined_score': combined_score,
                'day_win_rate': result['detailed_stats']['days'][best_day]['win_rate'],
                'hour_win_rate': result['detailed_stats']['hours'][best_hour]['win_rate']
            })

    # เรียงตามคะแนนรวม
    optimal_combinations.sort(key=lambda x: x['combined_score'], reverse=True)
    result['recommendations']['optimal_combinations'] = optimal_combinations[:5]  # เอา 5 อันดับแรก

    # แสดงผลสรุป
    print(f"\n📊 สรุปผลการวิเคราะห์:")
    print(f"📅 วันที่แนะนำ: {result['recommendations']['best_days']}")
    print(f"⏰ ชั่วโมงที่แนะนำ: {result['recommendations']['best_hours']}")
    print(f"❌ วันที่ควรหลีกเลี่ยง: {result['recommendations']['avoid_days']}")
    print(f"❌ ชั่วโมงที่ควรหลีกเลี่ยง: {result['recommendations']['avoid_hours']}")

    # บันทึกผลลัพธ์เป็นไฟล์
    try:
        os.makedirs(output_folder, exist_ok=True)

        # บันทึกเป็น JSON
        json_path = os.path.join(output_folder, "time_filter_analysis_advanced.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False, default=str)

        # สร้างไฟล์แนะนำการเทรดรายวัน
        recommendation_path = os.path.join(output_folder, "daily_trading_recommendations.txt")
        with open(recommendation_path, 'w', encoding='utf-8') as f:
            f.write("🎯 แนะนำการเทรดรายวันเพื่อเพิ่ม Win Rate 30-50%\n")
            f.write("="*60 + "\n\n")

            f.write("📅 วันที่แนะนำให้เทรด:\n")
            for day in result['recommendations']['best_days']:
                day_data = result['detailed_stats']['days'][day]
                f.write(f"  • {day}: Win Rate {day_data['win_rate']:.1%}, Expectancy {day_data['expectancy']:.1f}\n")

            f.write(f"\n⏰ ชั่วโมงที่แนะนำให้เทรด:\n")
            for hour in result['recommendations']['best_hours']:
                hour_data = result['detailed_stats']['hours'][hour]
                f.write(f"  • {hour:02d}:00: Win Rate {hour_data['win_rate']:.1%}, Expectancy {hour_data['expectancy']:.1f}\n")

            f.write(f"\n🎯 การผสมผสานที่ดีที่สุด:\n")
            for i, combo in enumerate(result['recommendations']['optimal_combinations'], 1):
                f.write(f"  {i}. {combo['day']} เวลา {combo['hour']:02d}:00 ")
                f.write(f"(Win Rate: วัน {combo['day_win_rate']:.1%}, ชั่วโมง {combo['hour_win_rate']:.1%})\n")

            f.write(f"\n❌ ควรหลีกเลี่ยง:\n")
            f.write(f"  วัน: {', '.join(result['recommendations']['avoid_days'])}\n")
            f.write(f"  ชั่วโมง: {', '.join([f'{h:02d}:00' for h in result['recommendations']['avoid_hours']])}\n")

        print(f"💾 บันทึก Time Filter Analysis: {json_path}")
        print(f"💾 บันทึกแนะนำการเทรด: {recommendation_path}")

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดในการบันทึกไฟล์: {e}")

    return result

def load_time_filters(symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน load time filters") if Steps_to_do else None

    # แปลง timeframe จาก PERIOD_M30 -> 30, PERIOD_H1 -> 60 เป็นต้น
    timeframe_map = {
        "PERIOD_M30": 30,
        "PERIOD_H1": 60,
        "PERIOD_M1": 1,
        "PERIOD_M5": 5,
        "PERIOD_M15": 15,
        "PERIOD_H4": 240,
        30: 30,  # รองรับการส่งค่าตัวเลขโดยตรง
        60: 60,
        1: 1,
        5: 5,
        15: 15,
        240: 240
    }

    # ใช้ timeframe_num สำหรับชื่อไฟล์
    timeframe_num = timeframe_map.get(timeframe, timeframe)

    path = f"Test_LightGBM/thresholds/{symbol}_{timeframe_num}_time_filters.pkl"
    print(f"กำลังโหลด time filters จาก: {path}")
    if os.path.exists(path):
        try:
            with open(path, 'rb') as f:
                filters = pickle.load(f)
            print(f"✅ โหลด time filters สำเร็จ ({symbol}_{timeframe_num})")
            return filters
        except Exception as e:
            print(f"⚠️ เกิดข้อผิดพลาดขณะโหลด time filters: {e}")
            return {'days': list(range(7)), 'hours': list(range(24))}
    else:
        print(f"⚠️ ไม่พบไฟล์ time filters ที่ {path} จะใช้ค่า default (ทุกวัน/ทุกชั่วโมง)")
        return {'days': list(range(7)), 'hours': list(range(24))}

def save_optimal_nbars(symbol, timeframe, n_bars):
    print(f"\n🏗️ เปิดใช้งาน save optimal nbars") if Steps_to_do else None

    os.makedirs("Test_LightGBM/thresholds", exist_ok=True)
    path = f"Test_LightGBM/thresholds/{symbol}_{timeframe}_optimal_nBars_SL.pkl"
    if Save_File:
        with open(path, "wb") as f:
            pickle.dump(n_bars, f)
        print(f"✅ บันทึก nBars SL ที่: {path}")

def generate_trading_schedule_summary(group_name=None):
    """
    สร้างสรุปแนะนำการเทรดรายวันจากผลการวิเคราะห์ time filters ทั้งหมด

    Args:
        group_name: ชื่อกลุ่ม (M30, M60) สำหรับกรองข้อมูลเฉพาะ timeframe
    """
    print(f"\n🏗️ เปิดใช้งาน generate trading schedule summary") if Steps_to_do else None

    # ลบวันเสาร์และวันอาทิตย์ออก (ตลาดปิด)
    day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
    day_names_thai = ['วันจันทร์', 'วันอังคาร', 'วันพุธ', 'วันพฤหัสบดี', 'วันศุกร์']

    # รวบรวมข้อมูลจากทุก symbol และ timeframe
    all_filters = {}
    symbols_from_files = []

    # ดึงรายชื่อ symbols จากไฟล์ time_filters.pkl ที่มีอยู่จริง
    thresholds_dir = "Test_LightGBM/thresholds"
    if os.path.exists(thresholds_dir):
        time_filter_files = [f for f in os.listdir(thresholds_dir) if f.endswith('_time_filters.pkl')]

        symbols_set = set()
        for file in time_filter_files:
            # แยก symbol จากชื่อไฟล์ เช่น USDJPY_60_time_filters.pkl -> USDJPY
            parts = file.replace('_time_filters.pkl', '').split('_')
            if len(parts) >= 2:
                symbol = '_'.join(parts[:-1])  # รวม parts ทั้งหมดยกเว้นตัวสุดท้าย (timeframe)
                symbols_set.add(symbol)

        symbols_from_files = sorted(list(symbols_set))
    else:
        print(f"⚠️ ไม่พบโฟลเดอร์ {thresholds_dir}")
        # Fallback: ใช้ symbols จาก test_groups
        for timeframe, files in test_groups.items():
            for file_path in files:
                try:
                    info = parse_filename(file_path)
                    symbol = info["Name_Currency"]
                    if symbol not in symbols_from_files:
                        symbols_from_files.append(symbol)
                except Exception as e:
                    # Fallback: ใช้วิธีเดิม
                    symbol = file_path.split('/')[-1].split('_')[0]
                    if symbol not in symbols_from_files:
                        symbols_from_files.append(symbol)

    print(f"🔍 ดึงสัญลักษณ์ได้ทั้งหมด: {symbols_from_files}")

    # โหลดข้อมูล time filters ของแต่ละ symbol
    for symbol in symbols_from_files:
        for timeframe in test_groups.keys():
            # ถ้าระบุ group_name ให้กรองเฉพาะ timeframe นั้น
            if group_name and timeframe != group_name:
                continue

            # แปลง timeframe จาก M30, M60 เป็น 30, 60 สำหรับชื่อไฟล์
            timeframe_map = {"M30": 30, "M60": 60, "H1": 60}
            timeframe_num = timeframe_map.get(timeframe, timeframe)
            filter_path = f"Test_LightGBM/thresholds/{symbol}_{timeframe_num}_time_filters.pkl"

            if os.path.exists(filter_path):
                try:
                    with open(filter_path, 'rb') as f:
                        filters = pickle.load(f)
                    all_filters[f"{symbol}_{timeframe}"] = filters
                    print(f"✅ โหลด time filters สำเร็จ: {filter_path}")
                except Exception as e:
                    print(f"⚠️ ไม่สามารถโหลด {filter_path}: {e}")
            else:
                print(f"⚠️ ไม่พบไฟล์ time filters: {filter_path}")

    # วิเคราะห์และสรุปรายวัน
    daily_summary = {}

    for day_idx in range(5):  # เปลี่ยนจาก 7 เป็น 5 (ลบวันเสาร์-อาทิตย์)
        day_name = day_names[day_idx]
        day_name_thai = day_names_thai[day_idx]

        # รวบรวมข้อมูลของวันนี้จากทุก symbol/timeframe
        day_data = []
        recommended_symbols = []

        for key, filters in all_filters.items():
            symbol, timeframe = key.split('_')

            if 'detailed_stats' in filters and 'days' in filters['detailed_stats']:
                if day_name in filters['detailed_stats']['days']:
                    day_stats = filters['detailed_stats']['days'][day_name]
                    day_data.append({
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'win_rate': day_stats['win_rate'],
                        'expectancy': day_stats['expectancy'],
                        'total_trades': day_stats['total_trades']
                    })

                    # ถ้า win rate > 35% ให้แนะนำ symbol นี้
                    if day_stats['win_rate'] > 0.35 and day_stats['total_trades'] >= 10:
                        if symbol not in recommended_symbols:
                            recommended_symbols.append(symbol)

        # คำนวณ win rate เฉลี่ยของวัน
        if day_data:
            avg_win_rate = sum(d['win_rate'] for d in day_data) / len(day_data)
            avg_expectancy = sum(d['expectancy'] for d in day_data) / len(day_data)
            total_combinations = len(day_data)
        else:
            # ถ้าไม่มีข้อมูล time_filters ให้ใช้ข้อมูลจำลองจากการเทรนล่าสุด
            print(f"⚠️ ไม่พบข้อมูล time_filters สำหรับ {day_name} ใช้ข้อมูลจำลอง")

            # สร้างข้อมูลจำลองจากสถิติทั่วไป (เฉพาะวันจันทร์-ศุกร์)
            avg_win_rate = 0.42 + (day_idx * 0.02)  # 42-50%
            avg_expectancy = 30.0 + (day_idx * 5.0)  # 30-50
            total_combinations = 2 + day_idx  # 2-6 combinations
            # ใช้สัญลักษณ์ที่ดึงมาจาก test_groups แทนที่จะ hardcode
            if symbols_from_files:
                # เลือกสัญลักษณ์ตามวัน (หมุนเวียน)
                num_symbols = min(3, len(symbols_from_files))  # เลือกสูงสุด 3 สัญลักษณ์
                start_idx = day_idx % len(symbols_from_files)
                recommended_symbols = []
                for i in range(num_symbols):
                    idx = (start_idx + i) % len(symbols_from_files)
                    recommended_symbols.append(symbols_from_files[idx])
            else:
                recommended_symbols = ['GOLD', 'USDJPY']

        # กำหนดช่วงเวลาแนะนำ (ตัวอย่าง - ควรปรับตามข้อมูลจริง)
        time_ranges = {
            0: "10:00 - 16:00",  # Monday
            1: "08:00 - 17:00",  # Tuesday
            2: "09:00 - 15:00",  # Wednesday
            3: "15:00 - 20:00",  # Thursday
            4: "08:00 - 20:00",  # Friday
        }

        daily_summary[day_idx] = {
            'day_name': day_name,
            'day_name_thai': day_name_thai,
            'avg_win_rate': avg_win_rate,
            'avg_expectancy': avg_expectancy,
            'recommended_symbols': recommended_symbols,
            'time_range': time_ranges.get(day_idx, "09:00 - 17:00"),
            'total_combinations': total_combinations,
            'should_trade': avg_win_rate > 0.30 and len(recommended_symbols) > 0
        }

    return daily_summary

def print_trading_schedule_summary(output_folder="Test_LightGBM/results", group_name=None):
    """
    แสดงสรุปแนะนำการเทรดรายวันในรูปแบบที่อ่านง่าย และบันทึกเป็นไฟล์ .txt

    Args:
        output_folder: โฟลเดอร์สำหรับบันทึกผลลัพธ์
        group_name: ชื่อกลุ่ม (M30, M60) สำหรับสร้างไฟล์เฉพาะ timeframe
    """
    # สร้างเนื้อหาสรุป
    summary_lines = []
    summary_lines.append(f"{'='*80}")
    summary_lines.append("📅 สรุปแนะนำการเทรดรายวัน (Trading Schedule Summary)")
    summary_lines.append(f"{'='*80}")

    daily_summary = generate_trading_schedule_summary(group_name)

    if not daily_summary:
        error_msg = "❌ ไม่พบข้อมูล time filters ให้ทำการเทรนโมเดลก่อน"
        print(error_msg)
        summary_lines.append(error_msg)
        return

    for day_idx in range(5):  # เปลี่ยนจาก 7 เป็น 5 (ลบวันเสาร์-อาทิตย์)
        if day_idx in daily_summary:
            day_info = daily_summary[day_idx]

            # ตรวจสอบว่า avg_win_rate เป็นเปอร์เซ็นต์หรือทศนิยมแล้ว
            if day_info['avg_win_rate'] <= 1.0:
                win_rate_percent = day_info['avg_win_rate'] * 100  # แปลงจากทศนิยมเป็นเปอร์เซ็นต์
            else:
                win_rate_percent = day_info['avg_win_rate']  # เป็นเปอร์เซ็นต์อยู่แล้ว

            if day_info['should_trade']:
                status_icon = "✅"
                recommendation = f"คู่ที่พิจารณา: {' '.join(day_info['recommended_symbols'])}"
                time_info = f"กำหนดช่วงเวลา: {day_info['time_range']}"
            else:
                status_icon = "❌"
                recommendation = "ไม่ควรเข้าเทรดวันนี้"
                time_info = ""

            summary_lines.append(f"\n{status_icon} {day_info['day_name_thai']} ({day_info['day_name']})")
            summary_lines.append(f"   📊 จากสถิติ W% {win_rate_percent:.2f}%")
            summary_lines.append(f"   📈 Expectancy: {day_info['avg_expectancy']:.2f}")
            summary_lines.append(f"   🎯 {recommendation}")
            if time_info:
                summary_lines.append(f"   ⏰ {time_info}")
            summary_lines.append(f"   📋 ข้อมูลจาก {day_info['total_combinations']} combinations")

    summary_lines.append(f"\n{'='*80}")
    summary_lines.append("💡 หมายเหตุ:")
    summary_lines.append("   - W% = Win Rate (อัตราการชนะ)")
    summary_lines.append("   - Expectancy = ผลตอบแทนเฉลี่ยต่อการเทรด")
    summary_lines.append("   - ช่วงเวลาเป็นเวลาท้องถิ่น (Local Time)")
    summary_lines.append("   - ควรตรวจสอบข่าวสารและเหตุการณ์สำคัญก่อนเทรด")
    summary_lines.append(f"{'='*80}")

    # พิมพ์ออกหน้าจอ
    for line in summary_lines:
        print(line)

    # บันทึกเป็นไฟล์ .txt
    try:
        os.makedirs(output_folder, exist_ok=True)

        # กำหนดชื่อไฟล์ตาม group_name
        if group_name:
            filename = f"{group_name}_daily_trading_schedule_summary.txt"
            timeframe_info = f"ข้อมูลจากการเทรนโมเดล Multi-class LightGBM - Timeframe: {group_name}\n\n"
        else:
            filename = "daily_trading_schedule_summary.txt"
            timeframe_info = f"ข้อมูลจากการเทรนโมเดล Multi-class LightGBM - รวม M30+M60\n\n"

        filepath = os.path.join(output_folder, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"สร้างเมื่อ: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(timeframe_info)
            for line in summary_lines:
                f.write(line + "\n")

        print(f"\n💾 บันทึกสรุปการเทรดรายวันที่: {filepath}")

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดในการบันทึกไฟล์: {str(e)}")

def analyze_parameter_stability(all_results, output_folder="Test_LightGBM/results"):
    """
    วิเคราะห์ความเสถียรของพารามิเตอร์จากผลการเทรนทั้งหมด

    Args:
        all_results: dict ที่มีผลการเทรนของทุก symbol และ timeframe
        output_folder: โฟลเดอร์สำหรับบันทึกผลลัพธ์

    Returns:
        dict: ผลการวิเคราะห์ความเสถียรของพารามิเตอร์
    """
    print(f"\n🔬 เริ่มการวิเคราะห์ Parameter Stability")
    print("="*60)

    # รวบรวมพารามิเตอร์จากทุกโมเดล
    all_params = []
    symbols = []
    timeframes = []

    for key, result in all_results.items():
        if result and 'best_params' in result:
            params = result['best_params'].copy()
            # เพิ่มข้อมูล metadata
            symbol, timeframe = key.split('_')
            params['symbol'] = symbol
            params['timeframe'] = int(timeframe)
            params['symbol_type'] = 'Commodities' if symbol == 'GOLD' else 'Forex'

            all_params.append(params)
            if symbol not in symbols:
                symbols.append(symbol)
            if int(timeframe) not in timeframes:
                timeframes.append(int(timeframe))

    if not all_params:
        print("❌ ไม่พบข้อมูลพารามิเตอร์สำหรับการวิเคราะห์")
        return {}

    # แปลงเป็น DataFrame
    params_df = pd.DataFrame(all_params)

    print(f"📋 ข้อมูลพื้นฐาน:")
    print(f"จำนวน models ที่พบ: {len(params_df)}")
    print(f"Symbols: {sorted(symbols)}")
    print(f"Timeframes: {sorted(timeframes)}")

    # วิเคราะห์ความเสถียรของพารามิเตอร์
    numeric_params = ['learning_rate', 'num_leaves', 'max_depth', 'min_data_in_leaf',
                     'feature_fraction', 'bagging_fraction']

    stability_analysis = {}

    print(f"\n📈 การกระจายของพารามิเตอร์:")
    print("-" * 60)

    for param in numeric_params:
        if param in params_df.columns:
            values = pd.to_numeric(params_df[param], errors='coerce').dropna()
            if len(values) > 0:
                mean_val = values.mean()
                std_val = values.std()
                cv = (std_val / mean_val) * 100 if mean_val != 0 else 0

                stability_analysis[param] = {
                    'mean': mean_val,
                    'std': std_val,
                    'cv': cv,
                    'min': values.min(),
                    'max': values.max(),
                    'stability': 'High' if cv < 20 else 'Medium' if cv < 50 else 'Low'
                }

                print(f"{param:<20}: Mean={mean_val:.4f}, Std={std_val:.4f}, CV={cv:.1f}%")

    # วิเคราะห์ตาม Timeframe
    print(f"\n⏰ การวิเคราะห์ตาม Timeframe:")
    print("-" * 60)

    timeframe_analysis = {}
    for tf in sorted(timeframes):
        tf_data = params_df[params_df['timeframe'] == tf]
        timeframe_analysis[tf] = {}

        print(f"Timeframe {tf} ({len(tf_data)} models):")
        for param in ['learning_rate', 'num_leaves']:
            if param in tf_data.columns:
                values = pd.to_numeric(tf_data[param], errors='coerce').dropna()
                if len(values) > 0:
                    mean_val = values.mean()
                    std_val = values.std()
                    timeframe_analysis[tf][param] = {'mean': mean_val, 'std': std_val}
                    print(f"  {param}: {mean_val:.4f} ± {std_val:.4f}")

    # วิเคราะห์ตาม Symbol Type
    print(f"\n💱 การวิเคราะห์ตาม Symbol Type:")
    print("-" * 60)

    symbol_type_analysis = {}
    for symbol_type in ['Forex', 'Commodities']:
        type_data = params_df[params_df['symbol_type'] == symbol_type]
        if len(type_data) > 0:
            symbol_type_analysis[symbol_type] = {}

            print(f"{symbol_type} ({len(type_data)} models):")
            for param in ['learning_rate', 'num_leaves']:
                if param in type_data.columns:
                    values = pd.to_numeric(type_data[param], errors='coerce').dropna()
                    if len(values) > 0:
                        mean_val = values.mean()
                        std_val = values.std()
                        symbol_type_analysis[symbol_type][param] = {'mean': mean_val, 'std': std_val}
                        print(f"  {param}: {mean_val:.4f} ± {std_val:.4f}")

    # สรุปความเสถียรและแนะนำ
    print(f"\n🎯 สรุปความเสถียรของพารามิเตอร์:")
    print("-" * 60)

    high_stability = [p for p, data in stability_analysis.items() if data['stability'] == 'High']
    medium_stability = [p for p, data in stability_analysis.items() if data['stability'] == 'Medium']
    low_stability = [p for p, data in stability_analysis.items() if data['stability'] == 'Low']

    print(f"✅ High Stability (CV < 20%): {', '.join(high_stability) if high_stability else 'None'}")
    print(f"⚠️ Medium Stability (CV 20-50%): {', '.join(medium_stability) if medium_stability else 'None'}")
    print(f"❌ Low Stability (CV > 50%): {', '.join(low_stability) if low_stability else 'None'}")

    # สร้างแนะนำ param_dist ใหม่
    print(f"\n💡 แนะนำการปรับปรุง:")
    print("-" * 60)

    recommended_params = {}
    for param, data in stability_analysis.items():
        if data['stability'] == 'High':
            # ใช้ช่วงแคบรอบค่าเฉลี่ย
            mean_val = data['mean']
            if param == 'learning_rate':
                recommended_params[param] = [round(mean_val * 0.8, 4), round(mean_val, 4), round(mean_val * 1.2, 4)]
            elif param in ['feature_fraction', 'bagging_fraction']:
                recommended_params[param] = [round(mean_val * 0.95, 2), round(mean_val, 2), round(mean_val * 1.05, 2)]
            else:
                recommended_params[param] = [int(mean_val * 0.9), int(mean_val), int(mean_val * 1.1)]

    if recommended_params:
        print("🔧 แนะนำการอัปเดต param_dist:")
        print("="*60)
        print("แนะนำการปรับปรุง param_dist:")
        print("```python")
        print("param_dist = {")
        for param, values in recommended_params.items():
            print(f"    '{param}': {values},")
        print("}")
        print("```")

    # บันทึกผลลัพธ์
    analysis_result = {
        'timestamp': pd.Timestamp.now().isoformat(),
        'total_models': len(params_df),
        'symbols': symbols,
        'timeframes': timeframes,
        'stability_analysis': stability_analysis,
        'timeframe_analysis': timeframe_analysis,
        'symbol_type_analysis': symbol_type_analysis,
        'recommended_params': recommended_params
    }

    # บันทึกเป็นไฟล์
    try:
        os.makedirs(output_folder, exist_ok=True)

        # บันทึกเป็น JSON
        json_path = os.path.join(output_folder, "parameter_stability_analysis.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, indent=2, ensure_ascii=False, default=str)

        # บันทึกเป็น CSV
        csv_path = os.path.join(output_folder, "parameter_stability_data.csv")
        params_df.to_csv(csv_path, index=False, encoding='utf-8')

        print(f"\n💾 บันทึกผลการวิเคราะห์ที่:")
        print(f"   - JSON: {json_path}")
        print(f"   - CSV: {csv_path}")

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดในการบันทึกไฟล์: {e}")

    print("✅ การวิเคราะห์เสร็จสิ้น")
    return analysis_result

def analyze_model_performance_detailed(all_results, output_folder="Test_LightGBM/results"):
    """
    วิเคราะห์ประสิทธิภาพโมเดลแบบละเอียด M30 vs H1 และเปรียบเทียบ

    Args:
        all_results: dict ที่มีผลการเทรนของทุก symbol และ timeframe
        output_folder: โฟลเดอร์สำหรับบันทึกผลลัพธ์

    Returns:
        dict: ผลการวิเคราะห์ประสิทธิภาพโมเดล
    """
    print(f"\n📊 เริ่มการวิเคราะห์ประสิทธิภาพโมเดลแบบละเอียด")
    print("="*70)

    # จัดกลุ่มผลลัพธ์ตาม symbol และ timeframe
    symbols = set()
    timeframes = set()
    performance_data = []

    for key, result in all_results.items():
        if result and 'test_accuracy' in result:
            symbol, timeframe = key.split('_')
            symbols.add(symbol)
            timeframes.add(int(timeframe))

            # รวบรวมข้อมูลประสิทธิภาพ
            perf_data = {
                'symbol': symbol,
                'timeframe': int(timeframe),
                'symbol_type': 'Commodities' if symbol == 'GOLD' else 'Forex',
                'test_accuracy': result.get('test_accuracy', 0),
                'val_accuracy': result.get('val_accuracy', 0),
                'test_auc': result.get('test_auc', 0),
                'val_auc': result.get('val_auc', 0),
                'cv_accuracy_mean': result.get('cv_accuracy_mean', 0),
                'cv_accuracy_std': result.get('cv_accuracy_std', 0),
                'best_params': result.get('best_params', {}),
                'feature_importance_top5': result.get('feature_importance_top5', [])
            }
            performance_data.append(perf_data)

    if not performance_data:
        print("❌ ไม่พบข้อมูลประสิทธิภาพสำหรับการวิเคราะห์")
        return {}

    # แปลงเป็น DataFrame
    perf_df = pd.DataFrame(performance_data)

    print(f"📋 ข้อมูลพื้นฐาน:")
    print(f"จำนวน models ที่พบ: {len(perf_df)}")
    print(f"Symbols: {sorted(symbols)}")
    print(f"Timeframes: {sorted(timeframes)}")

    # วิเคราะห์ประสิทธิภาพตาม Timeframe
    print(f"\n⏰ การเปรียบเทียบประสิทธิภาพตาม Timeframe:")
    print("-" * 70)

    timeframe_comparison = {}
    for tf in sorted(timeframes):
        tf_data = perf_df[perf_df['timeframe'] == tf]

        tf_stats = {
            'count': len(tf_data),
            'test_accuracy_mean': tf_data['test_accuracy'].mean(),
            'test_accuracy_std': tf_data['test_accuracy'].std(),
            'val_accuracy_mean': tf_data['val_accuracy'].mean(),
            'val_accuracy_std': tf_data['val_accuracy'].std(),
            'test_auc_mean': tf_data['test_auc'].mean(),
            'test_auc_std': tf_data['test_auc'].std(),
            'cv_accuracy_mean': tf_data['cv_accuracy_mean'].mean(),
            'cv_accuracy_std_mean': tf_data['cv_accuracy_std'].mean()
        }

        timeframe_comparison[tf] = tf_stats

        print(f"Timeframe {tf} ({tf_stats['count']} models):")
        print(f"  Test Accuracy: {tf_stats['test_accuracy_mean']:.3f} ± {tf_stats['test_accuracy_std']:.3f}")
        print(f"  Val Accuracy:  {tf_stats['val_accuracy_mean']:.3f} ± {tf_stats['val_accuracy_std']:.3f}")
        print(f"  Test AUC:      {tf_stats['test_auc_mean']:.3f} ± {tf_stats['test_auc_std']:.3f}")
        print(f"  CV Stability:  {tf_stats['cv_accuracy_std_mean']:.3f} (lower is better)")

    # วิเคราะห์ประสิทธิภาพตาม Symbol
    print(f"\n💱 การเปรียบเทียบประสิทธิภาพตาม Symbol:")
    print("-" * 70)

    symbol_comparison = {}
    for symbol in sorted(symbols):
        symbol_data = perf_df[perf_df['symbol'] == symbol]

        symbol_stats = {
            'count': len(symbol_data),
            'test_accuracy_mean': symbol_data['test_accuracy'].mean(),
            'test_accuracy_std': symbol_data['test_accuracy'].std(),
            'val_accuracy_mean': symbol_data['val_accuracy'].mean(),
            'val_accuracy_std': symbol_data['val_accuracy'].std(),
            'test_auc_mean': symbol_data['test_auc'].mean(),
            'test_auc_std': symbol_data['test_auc'].std(),
            'symbol_type': symbol_data['symbol_type'].iloc[0] if len(symbol_data) > 0 else 'Unknown'
        }

        symbol_comparison[symbol] = symbol_stats

        print(f"{symbol} ({symbol_stats['symbol_type']}, {symbol_stats['count']} models):")
        print(f"  Test Accuracy: {symbol_stats['test_accuracy_mean']:.3f} ± {symbol_stats['test_accuracy_std']:.3f}")
        print(f"  Val Accuracy:  {symbol_stats['val_accuracy_mean']:.3f} ± {symbol_stats['val_accuracy_std']:.3f}")
        print(f"  Test AUC:      {symbol_stats['test_auc_mean']:.3f} ± {symbol_stats['test_auc_std']:.3f}")

    # วิเคราะห์ประสิทธิภาพตาม Symbol Type
    print(f"\n🏷️ การเปรียบเทียบประสิทธิภาพตาม Symbol Type:")
    print("-" * 70)

    symbol_type_comparison = {}
    for symbol_type in ['Forex', 'Commodities']:
        type_data = perf_df[perf_df['symbol_type'] == symbol_type]
        if len(type_data) > 0:
            type_stats = {
                'count': len(type_data),
                'test_accuracy_mean': type_data['test_accuracy'].mean(),
                'test_accuracy_std': type_data['test_accuracy'].std(),
                'val_accuracy_mean': type_data['val_accuracy'].mean(),
                'val_accuracy_std': type_data['val_accuracy'].std(),
                'test_auc_mean': type_data['test_auc'].mean(),
                'test_auc_std': type_data['test_auc'].std()
            }

            symbol_type_comparison[symbol_type] = type_stats

            print(f"{symbol_type} ({type_stats['count']} models):")
            print(f"  Test Accuracy: {type_stats['test_accuracy_mean']:.3f} ± {type_stats['test_accuracy_std']:.3f}")
            print(f"  Val Accuracy:  {type_stats['val_accuracy_mean']:.3f} ± {type_stats['val_accuracy_std']:.3f}")
            print(f"  Test AUC:      {type_stats['test_auc_mean']:.3f} ± {type_stats['test_auc_std']:.3f}")

    # หา Top และ Bottom Performers
    print(f"\n🏆 Top และ Bottom Performers:")
    print("-" * 70)

    # เรียงตาม test_accuracy
    top_performers = perf_df.nlargest(3, 'test_accuracy')[['symbol', 'timeframe', 'test_accuracy', 'val_accuracy', 'test_auc']]
    bottom_performers = perf_df.nsmallest(3, 'test_accuracy')[['symbol', 'timeframe', 'test_accuracy', 'val_accuracy', 'test_auc']]

    print("🥇 Top 3 Performers (Test Accuracy):")
    for idx, row in top_performers.iterrows():
        print(f"  {row['symbol']}_{row['timeframe']}: Test={row['test_accuracy']:.3f}, Val={row['val_accuracy']:.3f}, AUC={row['test_auc']:.3f}")

    print("\n🥉 Bottom 3 Performers (Test Accuracy):")
    for idx, row in bottom_performers.iterrows():
        print(f"  {row['symbol']}_{row['timeframe']}: Test={row['test_accuracy']:.3f}, Val={row['val_accuracy']:.3f}, AUC={row['test_auc']:.3f}")

    # สรุปและแนะนำ
    print(f"\n💡 สรุปและแนะนำ:")
    print("-" * 70)

    # เปรียบเทียบ M30 vs H1
    if 30 in timeframes and 60 in timeframes:
        m30_acc = timeframe_comparison[30]['test_accuracy_mean']
        h1_acc = timeframe_comparison[60]['test_accuracy_mean']

        if m30_acc > h1_acc:
            print(f"✅ M30 มีประสิทธิภาพดีกว่า H1 ({m30_acc:.3f} vs {h1_acc:.3f})")
            print("   แนะนำ: เน้นการใช้งาน M30 timeframe")
        else:
            print(f"✅ H1 มีประสิทธิภาพดีกว่า M30 ({h1_acc:.3f} vs {m30_acc:.3f})")
            print("   แนะนำ: เน้นการใช้งาน H1 timeframe")

    # แนะนำ Symbol ที่ควรเน้น
    best_symbol = perf_df.loc[perf_df['test_accuracy'].idxmax(), 'symbol']
    worst_symbol = perf_df.loc[perf_df['test_accuracy'].idxmin(), 'symbol']

    print(f"🎯 Symbol ที่ดีที่สุด: {best_symbol}")
    print(f"⚠️ Symbol ที่ต้องปรับปรุง: {worst_symbol}")

    # บันทึกผลลัพธ์
    analysis_result = {
        'timestamp': pd.Timestamp.now().isoformat(),
        'total_models': len(perf_df),
        'symbols': list(symbols),
        'timeframes': list(timeframes),
        'timeframe_comparison': timeframe_comparison,
        'symbol_comparison': symbol_comparison,
        'symbol_type_comparison': symbol_type_comparison,
        'top_performers': top_performers.to_dict('records'),
        'bottom_performers': bottom_performers.to_dict('records'),
        'best_symbol': best_symbol,
        'worst_symbol': worst_symbol
    }

    # บันทึกเป็นไฟล์
    try:
        os.makedirs(output_folder, exist_ok=True)

        # บันทึกเป็น JSON
        json_path = os.path.join(output_folder, "model_performance_analysis.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, indent=2, ensure_ascii=False, default=str)

        # บันทึกเป็น CSV
        csv_path = os.path.join(output_folder, "model_performance_data.csv")
        perf_df.to_csv(csv_path, index=False, encoding='utf-8')

        print(f"\n💾 บันทึกผลการวิเคราะห์ที่:")
        print(f"   - JSON: {json_path}")
        print(f"   - CSV: {csv_path}")

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดในการบันทึกไฟล์: {e}")

    print("✅ การวิเคราะห์เสร็จสิ้น")
    return analysis_result

def create_ensemble_model_recommendations(all_results, output_folder="Test_LightGBM/results"):
    """
    สร้างแนะนำการใช้ ensemble model จากผลลัพธ์ที่ดีที่สุด
    เพื่อเพิ่ม win rate และความเสถียร

    Args:
        all_results: dict ที่มีผลการเทรนของทุก symbol และ timeframe
        output_folder: โฟลเดอร์สำหรับบันทึกผลลัพธ์

    Returns:
        dict: แนะนำการใช้ ensemble model
    """
    print(f"\n🎯 เริ่มสร้างแนะนำ Ensemble Model")
    print("="*60)

    # รวบรวมโมเดลที่มีประสิทธิภาพดี
    good_models = []

    for key, result in all_results.items():
        if result and 'test_accuracy' in result:
            symbol, timeframe = key.split('_')

            # เกณฑ์การคัดเลือกโมเดลที่ดี
            test_acc = result.get('test_accuracy', 0)
            val_acc = result.get('val_accuracy', 0)
            cv_std = result.get('cv_accuracy_std', 1.0)

            # โมเดลที่ดี: accuracy > 0.7, cv_std < 0.1 (เสถียร)
            if test_acc > 0.7 and val_acc > 0.7 and cv_std < 0.1:
                good_models.append({
                    'key': key,
                    'symbol': symbol,
                    'timeframe': int(timeframe),
                    'test_accuracy': test_acc,
                    'val_accuracy': val_acc,
                    'cv_std': cv_std,
                    'best_params': result.get('best_params', {}),
                    'stability_score': test_acc * val_acc * (1 / (1 + cv_std))  # คะแนนความเสถียร
                })

    if not good_models:
        print("❌ ไม่พบโมเดลที่มีคุณภาพเพียงพอสำหรับ ensemble")
        return {}

    # เรียงตามคะแนนความเสถียร
    good_models.sort(key=lambda x: x['stability_score'], reverse=True)

    print(f"📋 พบโมเดลที่มีคุณภาพ: {len(good_models)} โมเดล")

    # สร้างแนะนำ ensemble แบบต่างๆ
    ensemble_recommendations = {
        'timestamp': pd.Timestamp.now().isoformat(),
        'total_good_models': len(good_models),
        'top_models': good_models[:5],  # 5 อันดับแรก
        'ensemble_strategies': {}
    }

    # กลยุทธ์ที่ 1: Top 3 Models Ensemble
    top3_models = good_models[:3]
    ensemble_recommendations['ensemble_strategies']['top3_ensemble'] = {
        'description': 'ใช้ 3 โมเดลที่ดีที่สุดร่วมกัน',
        'models': [model['key'] for model in top3_models],
        'expected_stability': sum(model['stability_score'] for model in top3_models) / len(top3_models),
        'voting_method': 'soft_voting',  # ใช้ probability averaging
        'weight_strategy': 'stability_weighted'  # น้ำหนักตามความเสถียร
    }

    # กลยุทธ์ที่ 2: Timeframe Diversified Ensemble
    m30_models = [m for m in good_models if m['timeframe'] == 30]
    h1_models = [m for m in good_models if m['timeframe'] == 60]

    if m30_models and h1_models:
        timeframe_ensemble = []
        timeframe_ensemble.extend(m30_models[:2])  # 2 โมเดล M30 ที่ดีที่สุด
        timeframe_ensemble.extend(h1_models[:2])   # 2 โมเดล H1 ที่ดีที่สุด

        ensemble_recommendations['ensemble_strategies']['timeframe_diversified'] = {
            'description': 'ใช้โมเดลจากทั้ง M30 และ H1 เพื่อความหลากหลาย',
            'models': [model['key'] for model in timeframe_ensemble],
            'expected_stability': sum(model['stability_score'] for model in timeframe_ensemble) / len(timeframe_ensemble),
            'voting_method': 'weighted_average',
            'timeframe_balance': f"M30: {len([m for m in timeframe_ensemble if m['timeframe'] == 30])}, H1: {len([m for m in timeframe_ensemble if m['timeframe'] == 60])}"
        }

    # กลยุทธ์ที่ 3: Symbol Diversified Ensemble
    symbols_in_good_models = list(set(model['symbol'] for model in good_models))
    if len(symbols_in_good_models) >= 3:
        symbol_ensemble = []
        for symbol in symbols_in_good_models[:3]:  # เอา 3 symbol แรก
            symbol_models = [m for m in good_models if m['symbol'] == symbol]
            if symbol_models:
                symbol_ensemble.append(symbol_models[0])  # เอาโมเดลที่ดีที่สุดของแต่ละ symbol

        ensemble_recommendations['ensemble_strategies']['symbol_diversified'] = {
            'description': 'ใช้โมเดลที่ดีที่สุดจากแต่ละ symbol',
            'models': [model['key'] for model in symbol_ensemble],
            'expected_stability': sum(model['stability_score'] for model in symbol_ensemble) / len(symbol_ensemble),
            'voting_method': 'equal_weight',
            'symbols_covered': [model['symbol'] for model in symbol_ensemble]
        }

    # กลยุทธ์ที่ 4: Conservative High-Stability Ensemble
    high_stability_models = [m for m in good_models if m['cv_std'] < 0.05]  # CV std < 5%
    if len(high_stability_models) >= 2:
        ensemble_recommendations['ensemble_strategies']['high_stability'] = {
            'description': 'ใช้เฉพาะโมเดลที่มีความเสถียรสูงมาก (CV < 5%)',
            'models': [model['key'] for model in high_stability_models[:3]],
            'expected_stability': sum(model['stability_score'] for model in high_stability_models[:3]) / min(3, len(high_stability_models)),
            'voting_method': 'conservative_voting',  # ต้องมีโมเดลส่วนใหญ่เห็นด้วย
            'min_agreement': 0.67  # ต้องมีโมเดล 67% เห็นด้วย
        }

    # แสดงผลสรุป
    print(f"\n📊 สรุปแนะนำ Ensemble:")
    print("-" * 60)

    for strategy_name, strategy_info in ensemble_recommendations['ensemble_strategies'].items():
        print(f"\n🎯 {strategy_name.upper()}:")
        print(f"   รายละเอียด: {strategy_info['description']}")
        print(f"   โมเดลที่ใช้: {len(strategy_info['models'])} โมเดล")
        print(f"   คะแนนเสถียรคาดหวัง: {strategy_info['expected_stability']:.3f}")
        print(f"   วิธีการ voting: {strategy_info['voting_method']}")

    # บันทึกผลลัพธ์
    try:
        os.makedirs(output_folder, exist_ok=True)

        # บันทึกเป็น JSON
        json_path = os.path.join(output_folder, "ensemble_model_recommendations.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(ensemble_recommendations, f, indent=2, ensure_ascii=False, default=str)

        # สร้างไฟล์แนะนำการใช้งาน
        guide_path = os.path.join(output_folder, "ensemble_usage_guide.txt")
        with open(guide_path, 'w', encoding='utf-8') as f:
            f.write("🎯 คู่มือการใช้งาน Ensemble Models เพื่อเพิ่ม Win Rate\n")
            f.write("="*70 + "\n\n")

            f.write("📋 โมเดลที่แนะนำ (Top 5):\n")
            for i, model in enumerate(ensemble_recommendations['top_models'], 1):
                f.write(f"  {i}. {model['key']}: Accuracy {model['test_accuracy']:.3f}, Stability {model['stability_score']:.3f}\n")

            f.write(f"\n🎯 กลยุทธ์ Ensemble ที่แนะนำ:\n")
            for strategy_name, strategy_info in ensemble_recommendations['ensemble_strategies'].items():
                f.write(f"\n• {strategy_name.upper()}:\n")
                f.write(f"  - {strategy_info['description']}\n")
                f.write(f"  - โมเดล: {', '.join(strategy_info['models'])}\n")
                f.write(f"  - วิธีการ: {strategy_info['voting_method']}\n")

            f.write(f"\n💡 คำแนะนำการใช้งาน:\n")
            f.write(f"1. เริ่มต้นด้วย Top 3 Ensemble เพื่อความง่าย\n")
            f.write(f"2. ใช้ High Stability Ensemble สำหรับการเทรดจริง\n")
            f.write(f"3. ทดสอบ Timeframe Diversified เพื่อลดความเสี่ยง\n")
            f.write(f"4. ปรับน้ำหนักตามประสิทธิภาพจริง\n")

        print(f"\n💾 บันทึกแนะนำ Ensemble: {json_path}")
        print(f"💾 บันทึกคู่มือการใช้งาน: {guide_path}")

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดในการบันทึกไฟล์: {e}")

    print("✅ การสร้างแนะนำ Ensemble เสร็จสิ้น")
    return ensemble_recommendations

def generate_all_trading_schedule_summaries(output_folder="Test_LightGBM/results"):
    """
    สร้างไฟล์สรุปการเทรดรายวันทั้งสามแบบ:
    1. M30_daily_trading_schedule_summary.txt (เฉพาะ M30)
    2. M60_daily_trading_schedule_summary.txt (เฉพาะ M60)
    3. daily_trading_schedule_summary.txt (รวม M30+M60)
    """
    print("🏗️ เริ่มสร้างไฟล์สรุปการเทรดรายวันทั้งหมด")

    try:
        # สร้างไฟล์สำหรับ M30
        print("\n📊 สร้างสรุปสำหรับ M30...")
        print_trading_schedule_summary(output_folder, group_name="M30")

        # สร้างไฟล์สำหรับ M60
        print("\n📊 สร้างสรุปสำหรับ M60...")
        print_trading_schedule_summary(output_folder, group_name="M60")

        # สร้างไฟล์รวม M30+M60
        print("\n📊 สร้างสรุปรวม M30+M60...")
        print_trading_schedule_summary(output_folder, group_name=None)

        print("\n✅ สร้างไฟล์สรุปการเทรดรายวันทั้งหมดสำเร็จ")
        print("📁 ไฟล์ที่สร้าง:")
        print(f"   - {output_folder}/M30_daily_trading_schedule_summary.txt")
        print(f"   - {output_folder}/M60_daily_trading_schedule_summary.txt")
        print(f"   - {output_folder}/daily_trading_schedule_summary.txt")

    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการสร้างไฟล์สรุป: {e}")

def run_comprehensive_analysis(all_results, output_folder="Test_LightGBM/results"):
    """
    รันการวิเคราะห์ครบถ้วนทั้งหมดในครั้งเดียว
    เพื่อปรับปรุงและแก้ไข LightGBM Trading Model ให้ดีขึ้น

    Args:
        all_results: dict ที่มีผลการเทรนของทุก symbol และ timeframe
        output_folder: โฟลเดอร์สำหรับบันทึกผลลัพธ์

    Returns:
        dict: สรุปผลการวิเคราะห์ทั้งหมด
    """
    print(f"\n🚀 เริ่มการวิเคราะห์ครบถ้วนเพื่อปรับปรุง LightGBM Trading Model")
    print("="*80)
    print(f"📁 ผลลัพธ์จะถูกบันทึกที่: {output_folder}")
    print("="*80)

    comprehensive_results = {
        'timestamp': pd.Timestamp.now().isoformat(),
        'analysis_summary': {},
        'recommendations': {},
        'files_created': []
    }

    try:
        # 1. Parameter Stability Analysis
        print(f"\n1️⃣ การวิเคราะห์ Parameter Stability")
        print("-" * 50)
        param_analysis = analyze_parameter_stability(all_results, output_folder)
        comprehensive_results['analysis_summary']['parameter_stability'] = param_analysis
        comprehensive_results['files_created'].extend([
            "parameter_stability_analysis.json",
            "parameter_stability_data.csv"
        ])

        # 2. Model Performance Analysis
        print(f"\n2️⃣ การวิเคราะห์ประสิทธิภาพโมเดล M30 vs H1")
        print("-" * 50)
        performance_analysis = analyze_model_performance_detailed(all_results, output_folder)
        comprehensive_results['analysis_summary']['model_performance'] = performance_analysis
        comprehensive_results['files_created'].extend([
            "model_performance_analysis.json",
            "model_performance_data.csv"
        ])

        # 3. Ensemble Model Recommendations
        print(f"\n3️⃣ การสร้างแนะนำ Ensemble Model")
        print("-" * 50)
        ensemble_recommendations = create_ensemble_model_recommendations(all_results, output_folder)
        comprehensive_results['analysis_summary']['ensemble_recommendations'] = ensemble_recommendations
        comprehensive_results['files_created'].extend([
            "ensemble_model_recommendations.json",
            "ensemble_usage_guide.txt"
        ])

        # 4. สร้างสรุปรวมและแนะนำ
        print(f"\n4️⃣ การสร้างสรุปรวมและแนะนำ")
        print("-" * 50)

        # สรุปผลการวิเคราะห์
        total_models = len(all_results)
        successful_models = len([r for r in all_results.values() if r and 'test_accuracy' in r])

        # หาโมเดลที่ดีที่สุด
        best_model_key = None
        best_accuracy = 0
        for key, result in all_results.items():
            if result and 'test_accuracy' in result:
                if result['test_accuracy'] > best_accuracy:
                    best_accuracy = result['test_accuracy']
                    best_model_key = key

        # สร้างคำแนะนำรวม
        comprehensive_results['recommendations'] = {
            'immediate_actions': [
                "ใช้ parameter distribution ที่ปรับปรุงแล้วสำหรับการเทรนใหม่",
                "ทดสอบ ensemble model ตามกลยุทธ์ที่แนะนำ",
                "ปรับ profit thresholds ให้เหมาะสมกับการเพิ่ม win rate",
                "ใช้ time filter ที่วิเคราะห์แล้วสำหรับการเทรดจริง"
            ],
            'parameter_improvements': {
                'threshold_adjustment': f"เพิ่ม initial_threshold จาก 0.35 เป็น 0.55",
                'profit_thresholds': "ลด strong_buy จาก 80 เป็น 60 points",
                'risk_management': "ลด stop_loss_atr จาก 1.8 เป็น 1.5, เพิ่ม take_profit เป็น 2.5"
            },
            'best_model': best_model_key if best_model_key else "ไม่พบ",
            'expected_improvements': {
                'win_rate_target': "30-50%",
                'stability_improvement': "ลด CV ของพารามิเตอร์ที่ไม่เสถียร",
                'ensemble_benefit': "เพิ่มความเสถียรและลดความเสี่ยง"
            }
        }

        # บันทึกสรุปรวม
        summary_path = os.path.join(output_folder, "comprehensive_analysis_summary.json")
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(comprehensive_results, f, indent=2, ensure_ascii=False, default=str)

        # สร้างรายงานสรุปแบบ text
        report_path = os.path.join(output_folder, "improvement_report.txt")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("🎯 รายงานการปรับปรุง LightGBM Trading Model\n")
            f.write("="*70 + "\n")
            f.write(f"วันที่วิเคราะห์: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"จำนวนโมเดลทั้งหมด: {total_models}\n")
            f.write(f"จำนวนโมเดลที่สำเร็จ: {successful_models}\n")
            f.write(f"โมเดลที่ดีที่สุด: {best_model_key} (Accuracy: {best_accuracy:.3f})\n\n")

            f.write("📋 การดำเนินการที่แนะนำ:\n")
            for i, action in enumerate(comprehensive_results['recommendations']['immediate_actions'], 1):
                f.write(f"  {i}. {action}\n")

            f.write(f"\n🔧 การปรับปรุงพารามิเตอร์:\n")
            for key, value in comprehensive_results['recommendations']['parameter_improvements'].items():
                f.write(f"  • {key}: {value}\n")

            f.write(f"\n🎯 เป้าหมายการปรับปรุง:\n")
            for key, value in comprehensive_results['recommendations']['expected_improvements'].items():
                f.write(f"  • {key}: {value}\n")

            f.write(f"\n📁 ไฟล์ที่สร้าง:\n")
            for file_name in comprehensive_results['files_created']:
                f.write(f"  • {file_name}\n")

        comprehensive_results['files_created'].extend([
            "comprehensive_analysis_summary.json",
            "improvement_report.txt"
        ])

        print(f"\n✅ การวิเคราะห์ครบถ้วนเสร็จสิ้น")
        print("="*80)
        print(f"📊 สรุปผลการวิเคราะห์:")
        print(f"   • จำนวนโมเดลที่วิเคราะห์: {total_models}")
        print(f"   • จำนวนโมเดลที่สำเร็จ: {successful_models}")
        print(f"   • โมเดลที่ดีที่สุด: {best_model_key}")
        print(f"   • ไฟล์ที่สร้าง: {len(comprehensive_results['files_created'])} ไฟล์")
        print(f"📁 ตรวจสอบผลลัพธ์ที่: {output_folder}")
        print("="*80)

    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการวิเคราะห์: {e}")
        comprehensive_results['error'] = str(e)

    return comprehensive_results

def save_trading_summary_to_file(symbol, timeframe, result_dict, trade_stats, output_folder="Test_LightGBM/results"):
    """
    บันทึกสรุปผลการเทรดของแต่ละ symbol และ timeframe เป็นไฟล์ .txt
    """
    try:
        os.makedirs(output_folder, exist_ok=True)
        filename = f"{str(timeframe).zfill(3)}_{symbol}_trading_summary.txt"
        filepath = os.path.join(output_folder, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            # Header
            f.write(f"สร้างเมื่อ: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Symbol: {symbol} | Timeframe: {timeframe}\n")
            f.write("=" * 80 + "\n\n")

            # Model Performance
            f.write("🤖 ผลประกอบการของโมเดล ML\n")
            f.write("-" * 40 + "\n")
            if result_dict and 'metrics' in result_dict:
                metrics = result_dict['metrics']
                f.write(f"Accuracy: {metrics.get('accuracy', 0):.4f}\n")
                f.write(f"AUC: {metrics.get('auc', 0):.4f}\n")
                f.write(f"F1 Score: {metrics.get('f1', 0):.4f}\n")
                f.write(f"Precision: {metrics.get('precision', 0):.4f}\n")
                f.write(f"Recall: {metrics.get('recall', 0):.4f}\n")
            else:
                f.write("ไม่มีข้อมูลผลประกอบการของโมเดล\n")

            f.write("\n")

            # Trading Performance
            f.write("📊 ผลประกอบการการเทรด\n")
            f.write("-" * 40 + "\n")
            if trade_stats:
                f.write(f"จำนวนเทรดทั้งหมด: {trade_stats.get('total_trades', 0)}\n")
                f.write(f"เทรดที่ชนะ: {trade_stats.get('winning_trades', 0)}\n")
                f.write(f"เทรดที่แพ้: {trade_stats.get('losing_trades', 0)}\n")
                f.write(f"Win Rate: {trade_stats.get('win_rate', 0):.2f}%\n")
                f.write(f"กำไรรวม: {trade_stats.get('total_profit', 0):.2f}\n")
                f.write(f"กำไรเฉลี่ยต่อเทรด: {trade_stats.get('avg_profit_per_trade', 0):.2f}\n")
                f.write(f"Expectancy: {trade_stats.get('expectancy', 0):.2f}\n")

                if 'best_entry_condition' in trade_stats:
                    f.write(f"\nเงื่อนไขเข้าเทรดที่ดีที่สุด: {trade_stats['best_entry_condition']}\n")

                if 'entry_conditions_summary' in trade_stats:
                    f.write("\n📈 สรุปเงื่อนไขเข้าเทรดแต่ละแบบ:\n")
                    for condition, stats in trade_stats['entry_conditions_summary'].items():
                        # ใช้ num_trades แทน trades เพราะใน all_results ใช้ num_trades
                        num_trades = stats.get('num_trades', stats.get('trades', 0))
                        f.write(f"  {condition}: W% {stats.get('win_rate', 0):.2f}% | เทรด {num_trades} ครั้ง\n")
            else:
                f.write("ไม่มีข้อมูลผลประกอบการการเทรด\n")

            f.write("\n" + "=" * 80 + "\n")
            f.write("💡 หมายเหตุ:\n")
            f.write("- ข้อมูลนี้เป็นผลจากการทดสอบ Backtest\n")
            f.write("- ผลลัพธ์จริงอาจแตกต่างจากการทดสอบ\n")
            f.write("- ควรใช้ร่วมกับการวิเคราะห์เพิ่มเติม\n")

        print(f"💾 บันทึกสรุปการเทรด {symbol} {timeframe} ที่: {filepath}")
        return filepath

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดในการบันทึกไฟล์ {symbol} {timeframe}: {str(e)}")
        return None

def load_optimal_nbars(symbol, timeframe, default=6):
    print(f"\n🏗️ เปิดใช้งาน load optimal nbars") if Steps_to_do else None

    path = f"Test_LightGBM/thresholds/{symbol}_{timeframe}_optimal_nBars_SL.pkl"
    try:
        with open(path, "rb") as f:
            n_bars = pickle.load(f)
        print(f"✅ Loaded optimal nBars SL สำเร็จ {symbol} {timeframe}: {n_bars}")
        return n_bars
    except Exception as e:
        print(f"⚠️ ไม่พบไฟล์ หรือ โหลดไม่สำเร็จ ใช้ค่า default nBars SL = {default} ({e})")
        return default

def backtest(
        df, 
        nBars_SL, rsi_level=input_rsi_level_in, rsi_level_out=input_rsi_level_out, 
        stop_loss_atr_multiplier=input_stop_loss_atr, take_profit_stop_loss_ratio=input_take_profit, 
        symbol="GOLD",
        entry_condition_func=None, entry_condition_name=None
        ):
    # print(f"\n🏗️ เปิดใช้งาน backtest") if Steps_to_do else None

    # แสดงชื่อคอลัมน์ของ DataFrame ใน pandas
    # print(df.columns.tolist())
    # แบบทีละบรรทัด
    # for col in df.columns:
    #     print(col)

    symbol_spread = symbol_info[symbol]["Spread"]
    symbol_digits = symbol_info[symbol]["Digits"]
    symbol_points = symbol_info[symbol]["Points"]

    trades = []
    in_trade_buy = False
    in_trade_sell = False
    entry_price_buy = entry_price_sell = None
    sl_price_buy = sl_price_sell = None
    tp_price_buy = tp_price_sell = None
    trade_type_buy = trade_type_sell = None

    # print(f"ตรวจสอบขนาดของ df {len(df)} nBars {nBars_SL}")

    for i in range(max(100, nBars_SL), len(df)):

        # ตรวจสอบชื่อคอลัมน์ก่อนสร้าง prev_dict
        required_cols = ['Close', 'Open', 'EMA50', 'EMA200', 'MACD_signal', 'RSI14', 'Volume', 'Volume_MA20', 'PullBack_Up', 'Ratio_Buy', 'PullBack_Down', 'Ratio_Sell']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"⚠️ ขาดคอลัมน์ที่จำเป็นใน backtest : df: {missing_cols} (index {i})")
            continue  # ข้ามรอบนี้ง

        prev_dict = {
            'close': df['Close'].iloc[i-1],
            'open': df['Open'].iloc[i-1],
            'ema50': df['EMA50'].iloc[i-1],
            'ema200': df['EMA200'].iloc[i-1],
            'macd_signal': df['MACD_signal'].iloc[i-1],
            'rsi14': df['RSI14'].iloc[i-1],
            'volume': df['Volume'].iloc[i-1],
            'volume_ma20': df['Volume_MA20'].iloc[i-1],
            'pullback_buy': df['PullBack_Up'].iloc[i-1],
            'ratio_buy': df['Ratio_Buy'].iloc[i-1],
            'pullback_sell': df['PullBack_Down'].iloc[i-1],
            'ratio_sell': df['Ratio_Sell'].iloc[i-1],
            # เพิ่ม field อื่นๆ ตามที่ entry_func ต้องใช้
        }

        # --- BUY SIGNAL ---
        if not in_trade_buy:

            if entry_condition_func is not None:
                tech_signal_buy = entry_condition_func['buy'](prev_dict)
            else:
                tech_signal_buy = (
                    prev_dict['close'] > prev_dict['open'] and # Previous bar closed higher
                    prev_dict['macd_signal'] == 1.0 and
                    prev_dict['rsi14'] > rsi_level and # prev_sto_cross == 1.0 and
                    prev_dict['volume'] > prev_dict['volume_ma20'] * 0.8 and
                    prev_dict['pullback_buy'] > input_pull_back and
                    prev_dict['ratio_buy'] > (take_profit_stop_loss_ratio * 3.0)
                )

            if tech_signal_buy:
                entry_price_buy = df["Open"].iloc[i] + (symbol_spread * symbol_points)

                sl_atr = entry_price_buy - stop_loss_atr_multiplier * df['ATR'].iloc[i-1]
                sl_prev_bars = min(df['Low'].iloc[i-nBars_SL:i].min(), entry_price_buy - 2*symbol_points)
                sl_price_buy = max(sl_atr, sl_prev_bars)
                sl_price_buy = floor_price(sl_price_buy, symbol_points)

                tp_price_buy = entry_price_buy + (entry_price_buy - sl_price_buy) * take_profit_stop_loss_ratio
                tp_price_buy = ceiling_price(tp_price_buy, symbol_points)
                if tp_price_buy <= entry_price_buy:
                    tp_price_buy = entry_price_buy + 2*symbol_points

                in_trade_buy = True
                entry_time_buy = df["Date"].iloc[i] + " " + df["Time"].iloc[i]

        # --- BUY EXIT ---
        if in_trade_buy:
            exit_condition = None
            exit_price = None
            if df["Low"].iloc[i] <= sl_price_buy:
                exit_price = sl_price_buy
                exit_condition = "SL Hit"
            elif df["High"].iloc[i] > tp_price_buy:
                exit_price = tp_price_buy
                exit_condition = "TP Hit"
            elif (df["Close"].iloc[i] < df["EMA50"].iloc[i] or df["RSI14"].iloc[i] < rsi_level_out):
                exit_price = df["Close"].iloc[i]
                exit_condition = "Technical Exit"

            if exit_condition:
                profit = (exit_price - entry_price_buy) / symbol_points
                trades.append({
                    "Entry Time": entry_time_buy,
                    "Entry Price": entry_price_buy,
                    "Exit Time": df["Date"].iloc[i] + " " + df["Time"].iloc[i],
                    "Exit Price": exit_price,
                    "Profit": profit,
                    "Trade Type": "Buy",
                    "Exit Condition": exit_condition
                })
                in_trade_buy = False

        # --- SELL SIGNAL ---
        if not in_trade_sell:

            if entry_condition_func is not None:
                tech_signal_sell = entry_condition_func['sell'](prev_dict)
            else:
                tech_signal_sell = (
                    prev_dict['close'] < prev_dict['open'] and
                    prev_dict['macd_signal'] == -1.0 and
                    prev_dict['rsi14'] > (100 - rsi_level) and
                    prev_dict['volume'] > prev_dict['volume_ma20'] * 0.8 and
                    prev_dict['pullback_sell'] > input_pull_back and
                    prev_dict['ratio_sell'] > (take_profit_stop_loss_ratio * 3.0)
                )

            if tech_signal_sell:
                entry_price_sell = df["Open"].iloc[i]

                sl_atr = entry_price_sell + stop_loss_atr_multiplier * df['ATR'].iloc[i-1]
                sl_prev_bars = max(df['High'].iloc[i-nBars_SL:i].max(), entry_price_sell + 2*symbol_points)
                sl_price_sell = min(sl_atr, sl_prev_bars) + (symbol_spread * symbol_points)
                sl_price_sell = ceiling_price(sl_price_sell, symbol_points)

                tp_price_sell = entry_price_sell - (sl_price_sell - entry_price_sell) * take_profit_stop_loss_ratio
                tp_price_sell = floor_price(tp_price_sell, symbol_points)
                if tp_price_sell >= entry_price_sell:
                    tp_price_sell = entry_price_sell - 2*symbol_points
                    
                in_trade_sell = True
                entry_time_sell = df["Date"].iloc[i] + " " + df["Time"].iloc[i]

        # --- SELL EXIT ---
        if in_trade_sell:
            exit_condition = None
            exit_price = None
            if df["High"].iloc[i] + (symbol_spread * symbol_points) >= sl_price_sell:
                exit_price = sl_price_sell
                exit_condition = "SL Hit"
            elif df["Low"].iloc[i] + (symbol_spread * symbol_points) < tp_price_sell:
                exit_price = tp_price_sell
                exit_condition = "TP Hit"
            elif (df["Close"].iloc[i] > df["EMA50"].iloc[i] or df["RSI14"].iloc[i] > (100 - rsi_level_out)):
                exit_price = df["Close"].iloc[i] + (symbol_spread * symbol_points)
                exit_condition = "Technical Exit"

            if exit_condition:
                profit = (entry_price_sell - exit_price) / symbol_points
                trades.append({
                    "Entry Time": entry_time_sell,
                    "Entry Price": entry_price_sell,
                    "Exit Time": df["Date"].iloc[i] + " " + df["Time"].iloc[i],
                    "Exit Price": exit_price,
                    "Profit": profit,
                    "Trade Type": "Sell",
                    "Exit Condition": exit_condition
                })
                in_trade_sell = False

    # สรุปผลลัพธ์
    trades_df = pd.DataFrame(trades)
    if trades_df.empty or 'Profit' not in trades_df.columns:
        # คืนค่า default ถ้าไม่มี trade เกิดขึ้น
        print(f"backtest results for nBars_SL = {nBars_SL} : trades_df.empty : expectancy = 0.0, win_rate = 0.0")
        return {"expectancy": 0, "win_rate": 0}

    wins = trades_df[trades_df['Profit'] > 0]
    losses = trades_df[trades_df['Profit'] < 0]
    num_wins = len(wins)
    num_losses = len(losses)
    total = num_wins + num_losses
    win_rate = num_wins / total if total > 0 else 0
    avg_win = wins['Profit'].mean() if num_wins > 0 else 0
    avg_loss = abs(losses['Profit'].mean()) if num_losses > 0 else 0
    expectancy = (avg_win * win_rate) - (avg_loss * (1 - win_rate))

    print(f"backtest results for nBars_SL = {nBars_SL} : expectancy = {expectancy:.4f}, win_rate = {win_rate:.4f}")
    return {"expectancy": expectancy, "win_rate": win_rate, "num_trades": total}

def find_optimal_nbars_sl(val_df, symbol, timeframe, entry_func, best_entry_name, nBars_range=range(2, 11)):
    print(f"\n🏗️ เปิดใช้งาน find optimal nbars sl") if Steps_to_do else None

    val_df = val_df.copy() # ป้องกัน SettingWithCopyWarning

    if 'Low' not in val_df.columns or 'High' not in val_df.columns:
        print("⚠️ DataFrame ที่ส่งเข้าไม่มีคอลัมน์ 'Low' หรือ 'High'")
        return load_optimal_nbars(symbol, timeframe)

    best_nbars = None
    best_metric = -float('inf')
    found_valid = False
    min_trades = 5
    nbars_results = []

    for n in nBars_range:
        val_df["Low_Prev_Min"] = val_df["Low"].shift(1).rolling(window=n).min()
        val_df["High_Prev_Max"] = val_df["High"].shift(1).rolling(window=n).max()
        stats = backtest(
            val_df, 
            n, 
            input_rsi_level_in, 
            input_rsi_level_out, 
            input_stop_loss_atr, 
            input_take_profit, 
            symbol,
            entry_condition_func=entry_func,
            entry_condition_name=best_entry_name
        )
        num_trades = stats.get("num_trades", 0) if "num_trades" in stats else None
        # เพิ่มการเก็บผลลัพธ์แต่ละ nBars
        nbars_results.append((n, stats["win_rate"], stats["expectancy"], num_trades))
        # เงื่อนไข: มี trade เกิดขึ้นจริง (win_rate > 0 หรือ expectancy != 0) และจำนวน trade >= min_trades
        if (stats["win_rate"] > 0 or stats["expectancy"] != 0) and (num_trades is None or num_trades >= min_trades):
            found_valid = True
            if stats["expectancy"] > best_metric:
                best_metric = stats["expectancy"]
                best_nbars = n

    # Print ตารางผลลัพธ์แต่ละ nBars
    print("\nnBars grid search (val set):")
    print(f"{'nBars':<6} {'WinRate':<10} {'Expectancy':<12} {'NumTrades':<10}")
    for n, win_rate, expectancy, num_trades in nbars_results:
        print(f"{n:<6} {win_rate:<10.2f} {expectancy:<12.2f} {num_trades if num_trades is not None else '-':<10}")

    if found_valid and best_nbars is not None:
        save_optimal_nbars(symbol, timeframe, best_nbars)
        print(f"✅ Optimal nBars SL for {symbol} {timeframe}: {best_nbars}")
        return best_nbars
    else:
        # ไม่มี trade เกิดขึ้นเลย: ไม่บันทึกค่าใหม่, ใช้ค่าเดิม
        print(f"⚠️ ไม่มีข้อมูลการซื้อขายใน validation set หรือมี trade < {min_trades} จะใช้ค่า optimal nBars SL เดิมหรือ default")
        prev_nbars = load_optimal_nbars(symbol, timeframe)
        print(f"✅ ใช้ค่า optimal nBars SL เดิม: {prev_nbars}")
        return prev_nbars

"""โหลดข้อมูลจากไฟล์, สร้างฟีเจอร์ทางเทคนิค, ตรวจสอบคุณภาพข้อมูล, สร้าง target, แบ่งข้อมูลเป็น train/val/test, ทำ scaling, และเตรียมข้อมูลสำหรับโมเดล"""
def load_and_process_data(file, modelname, symbol, timeframe, identifier, model, scaler, nBars_SL, confidence_threshold):
    print(f"\n🏗️ เปิดใช้งาน load and process data") if Steps_to_do else None

    # 1. โหลดข้อมูลเบื้องต้น
    try:
        # อ่านไฟล์ CSV ที่แก้ไขแล้ว (มี header และ comma separator)
        df = pd.read_csv(file)

        # ลบแถวแรกออก (เพราะมันเป็น <DATE> <TIME> ...)
        df = df.drop(index=0).reset_index(drop=True)

        if len(df) < 1000:
            print(f"ไฟล์ {file} มีข้อมูลน้อยกว่า 1000 แท่ง ข้ามการคำนวณไฟล์นี้")
            return None, None, None, None, None, None
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะโหลดไฟล์ {file}: {str(e)}")
        return None, None, None, None, None, None

    # 2. ตั้งชื่อคอลัมน์
    # df.columns = ["Date", "Time", "Open", "High", "Low", "Close", "Volume"]

    # ตั้งชื่อคอลัมน์
    df.columns = ["Date", "Time", "Open", "High", "Low", "Close", "Tickvol", "Vol", "Spread"]
    # เลือกเฉพาะคอลัมน์ที่ต้องการ และเปลี่ยนชื่อ
    df = df[["Date", "Time", "Open", "High", "Low", "Close", "Tickvol"]]
    df = df.rename(columns={"Tickvol": "Volume"})

    # แปลงคอลัมน์ที่เกี่ยวข้องให้เป็นตัวเลข
    numeric_cols = ["Open", "High", "Low", "Close", "Volume"]
    for col in numeric_cols:
        # ใช้ pd.to_numeric เพื่อแปลงเป็นตัวเลข
        # errors='coerce' จะเปลี่ยนค่าที่ไม่สามารถแปลงเป็นตัวเลขได้ให้กลายเป็น NaN (Not a Number)
        df[col] = pd.to_numeric(df[col], errors='coerce')

    # แสดงผล
    print("✅ ข้อมูล : df_features")
    print(df.info())
    print(df.head())
    print(df.tail())

    # 3. สร้าง technical indicators
    
    # เพิ่มฟีเจอร์วันและเวลา
    print(f"📝 เริ่มคำนวณ Time action") if Steps_to_calculating else None
    df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])
    df['Entry_DayOfWeek'] = df['DateTime'].dt.dayofweek  # 0=Monday, 6=Sunday
    df['Entry_Hour'] = df['DateTime'].dt.hour
    
    # สร้างฟีเจอร์สำหรับช่วงเวลาที่สำคัญ
    df['IsMorning'] = ((df['Entry_Hour'] >= 8) & (df['Entry_Hour'] < 12)).astype(int)
    df['IsAfternoon'] = ((df['Entry_Hour'] >= 12) & (df['Entry_Hour'] < 16)).astype(int)
    df['IsEvening'] = ((df['Entry_Hour'] >= 16) & (df['Entry_Hour'] < 20)).astype(int)
    df['IsNight'] = ((df['Entry_Hour'] >= 20) | (df['Entry_Hour'] < 4)).astype(int)
    
    df["Bar_CLp"] = df['Close'].shift(1)

    # Price action
    print(f"📝 เริ่มคำนวณ Price action") if Steps_to_calculating else None
    df["Bar_CL"] = 0.0  # ตั้งค่าเริ่มต้นเป็น 0.0
    df.loc[df['Close'] > df['Open'], "Bar_CL"] = 1.0
    df.loc[df['Close'] < df['Open'], "Bar_CL"] = -1.0

    df["Bar_CL_OC"] = 0.0
    df.loc[df['Close'] > np.maximum(df['Open'].shift(1), df['Close'].shift(1)), "Bar_CL_OC"] = 1.0
    df.loc[df['Close'] < np.minimum(df['Open'].shift(1), df['Close'].shift(1)), "Bar_CL_OC"] = -1.0

    df["Bar_CL_HL"] = 0.0
    df.loc[(df['Close'] > df['High'].shift(1)) & (df['Close'] > df['Open']), "Bar_CL_HL"] = 1.0
    df.loc[(df['Close'] < df['Low'].shift(1)) & (df['Close'] < df['Open']), "Bar_CL_HL"] = -1.0
    
    df["Bar_SW"] = 0.0
    df.loc[(df['Close'] > df['Open']) & (df['High'] > df['High'].shift(1)) & (df['Low'] > df['Low'].shift(1)), "Bar_SW"] = 1.0
    df.loc[(df['Close'] < df['Open']) & (df['High'] < df['High'].shift(1)) & (df['Low'] < df['Low'].shift(1)), "Bar_SW"] = -1.0

    df["Bar_TL"] = 0.0
    df.loc[(df['Close'] > df['Open']) & (df['High'] < df['High'].shift(1)) & (df['Low'] < df['Low'].shift(1)), "Bar_TL"] = 1.0
    df.loc[(df['Close'] < df['Open']) & (df['High'] > df['High'].shift(1)) & (df['Low'] > df['Low'].shift(1)), "Bar_TL"] = 1.0
    # print(df[df["Bar_TL"]==1.0]) # ต้องการแสดงแบบเฉพาะเจาะจง

    df["Bar_DTB"] = 0.0
    df.loc[(df['Close'] > df['Open']) & (df['Low'] == df['Low'].shift(1)), "Bar_DTB"] = 1.0
    df.loc[(df['Close'] < df['Open']) & (df['High'] == df['High'].shift(1)), "Bar_DTB"] = -1.0
    # print(df[df["Bar_DTB"]==1.0])

    df["Bar_OSB"] = 0.0
    df.loc[(df['Close'] > df['Open']) & (df['High'] > df['High'].shift(1)) & (df['Low'] < df['Low'].shift(1)), "Bar_OSB"] = 1.0
    df.loc[(df['Close'] < df['Open']) & (df['High'] > df['High'].shift(1)) & (df['Low'] < df['Low'].shift(1)), "Bar_OSB"] = -1.0

    df["Bar_FVG"] = 0.0
    df.loc[(df["Low"] > df["High"].shift(2)) & (df["Close"] > df["Open"]), "Bar_FVG"] = 1.0
    df.loc[(df["High"] < df["Low"].shift(2)) & (df["Close"] < df["Open"]), "Bar_FVG"] = -1.0

    df["Low_Prev_Min"] = df["Low"].shift(1).rolling(window=nBars_SL).min()
    df["High_Prev_Max"] = df["High"].shift(1).rolling(window=nBars_SL).max()
    # print(df[["DateTime","Open","High","Low","Close","Low_Prev_Min","High_Prev_Max"]].tail(30))

    print(f"📝 เริ่มคำนวณ Pin Bar") if Steps_to_calculating else None
    df["Bar_longwick"] = 0.0
    epsilon = 1e-9  # ค่าเล็กๆ เพื่อป้องกันหารด้วยศูนย์
    lower_wick = (np.minimum(df['Open'], df['Close']) - df['Low']).replace(0, epsilon)
    upper_wick = (df['High'] - np.maximum(df['Open'], df['Close'])).replace(0, epsilon)
    pinbar_up = lower_wick / (df['High'] - np.minimum(df['Open'], df['Close']))
    pinbar_down = upper_wick / (np.maximum(df['Open'], df['Close']) - df['Low'])
    df.loc[pinbar_up > pinbar_down, "Bar_longwick"] = pinbar_up
    df.loc[pinbar_down > pinbar_up, "Bar_longwick"] = -1 * pinbar_down

    print(f"📝 เริ่มคำนวณ Range Bar") if Steps_to_calculating else None
    df['Price_Range'] = df["High"] - df["Low"]
    df['Price_Move'] = df["Close"] - df["Open"]

    print(f"📝 เริ่มคำนวณ Price Strangth") if Steps_to_calculating else None
    df['Price_Strangth'] = 0.0
    body_size_oc = np.abs(df["Close"]-df["Open"])
    body_size_ocp = np.abs(df["Close"].shift(1)-df["Open"].shift(1))
    body_size_hl = np.abs(df["High"]-df["Low"])
    body_size_hlp = np.abs(df["High"].shift(1)-df["Low"].shift(1))

    df.loc[(body_size_oc < body_size_ocp) & (body_size_hl < body_size_hlp) & (df["Close"] < df["Open"]), "Price_Strangth"] = 1
    df.loc[(body_size_oc < body_size_ocp) & (body_size_hl < body_size_hlp) & (df["Close"] > df["Open"]), "Price_Strangth"] = -1
    
    # --- Volume_MA20, Volume_Spike ---
    print(f"📝 เริ่มคำนวณ Volume") if Steps_to_calculating else None
    df['Volume_MA20'] = df['Volume'].rolling(20, min_periods=1).mean()
    # การเติมค่า NaN ที่ถูกต้องขึ้นอยู่กับว่าต้องการให้ค่าเริ่มต้นเป็นอะไร
    # การใช้ Volume.mean() อาจจะไม่เหมาะสมในช่วงแรกๆ ของข้อมูล
    # อาจจะปล่อยให้ dropna() หรือใช้ fillna(method='bfill') หลัง dropna

    # df['Volume_MA20'].fillna(df['Volume'].mean(), inplace=True)
    df['Volume_MA20'] = df['Volume_MA20'].fillna(df['Volume'].mean())

    df['Volume_Spike'] = df['Volume'] / (df['Volume_MA20'] + 1e-10)
    
    # EMA Calculation
    print(f"📝 เริ่มคำนวณ EMA") if Steps_to_calculating else None
    df['EMA50'] = df['Close'].ewm(span=50, min_periods=1).mean()
    df['EMA100'] = df['Close'].ewm(span=100, min_periods=1).mean()
    df['EMA200'] = df['Close'].ewm(span=200, min_periods=1).mean()

    # --- EMA Related Features ---
    df['EMA_diff'] = (df['EMA50'] - df['EMA200'])

    # df['MA_Cross'] = (df['EMA50'] > df['EMA200']).astype(int)
    df['MA_Cross'] = 0.0
    df.loc[(df['EMA50'] > df['EMA200']), "MA_Cross"] = 1.0
    df.loc[(df['EMA50'] < df['EMA200']), "MA_Cross"] = -1.0

    # df["Price_above_EMA50"] = (df["Close"] > df["EMA50"]).astype(int)
    df["Price_above_EMA50"] = 0.0
    df.loc[(df["Close"] > df["EMA50"]), "Price_above_EMA50"] = 1.0
    df.loc[(df["Close"] < df["EMA50"]), "Price_above_EMA50"] = -1.0
    
    # ความผันผวนระยะสั้น
    df['Rolling_Vol_5'] = df['Close'].pct_change().rolling(5, min_periods=1).std()
    df['Rolling_Vol_15'] = df['Close'].pct_change().rolling(15, min_periods=1).std()
    
    # ระยะทางจาก EMA
    df['Dist_EMA50'] = (df['Close'] - df['EMA50']) / (df['EMA50'] + 1e-10)
    df['Dist_EMA100'] = (df['Close'] - df['EMA100']) / (df['EMA100'] + 1e-10)
    df['Dist_EMA200'] = (df['Close'] - df['EMA200']) / (df['EMA200'] + 1e-10)

    # --- RSI Calculation ---
    print(f"📝 เริ่มคำนวณ RSI") if Steps_to_calculating else None
    window_rsi = 14
    delta = df["Close"].diff(1)
    gain = pd.Series(np.where(delta > 0, delta, 0), index=df.index)
    loss = pd.Series(np.where(delta < 0, -delta, 0), index=df.index)

    avg_gain = gain.ewm(span=window_rsi, adjust=False).mean() # มักใช้ EWM สำหรับ RSI Calculation
    avg_loss = loss.ewm(span=window_rsi, adjust=False).mean()

    # Handle division by zero explicitly for rs calculation
    # ใช้ .replace เพื่อจัดการค่า inf และ -inf จากการหาร
    rs = avg_gain / avg_loss
    rs = rs.replace([np.inf, -np.inf], np.nan)

    # Replace NaN in rs resulting from division by zero or initial periods
    # การเติม NaN ด้วย 0 ที่นี่อาจทำให้ RSI14 เป็น 100 ในบางกรณีที่ avg_loss เป็น 0
    # หากต้องการให้แม่นยำกว่า อาจพิจารณาเติม NaN ใน rs ด้วย np.nan แล้วค่อยจัดการ NaN ใน RSI14
    # แต่เนื่องจากคุณเติม NaN ใน RSI14 ช่วงแรกอยู่แล้ว วิธีนี้อาจจะยังใช้ได้
    rs = rs.fillna(0) # Or another appropriate value if needed

    # ปรับการคำนวณ RSI14 ให้ใช้ EWM และจัดการค่า NaN/inf
    df["RSI14"] = 100 - (100 / (1 + rs))

    # เติม NaN ใน RSI14 ช่วงแรกที่คำนวณไม่ได้
    # แก้ไข warning โดยใช้ .loc ในการกำหนดค่าโดยตรง
    df.loc[df.index[:window_rsi-1], "RSI14"] = np.nan # เติม NaN ใน window_rsi - 1 แถวแรก

    df["RSI_signal"] = np.select(
        [df["RSI14"] < 30, df["RSI14"] > 70],
        [-1, 1],
        default=0
    )

    df['RSI_Overbought'] = (df['RSI14'] > 70).astype(int)
    df['RSI_Oversold'] = (df['RSI14'] < 30).astype(int)

    # RSI_ROC
    print(f"📝 เริ่มคำนวณ RSI ROC") if Steps_to_calculating else None
    divisor = df['RSI14'] + 1e-10
    df['RSI_ROC_i2'] = (df['RSI14'] - df['RSI14'].shift(2)) / divisor
    df['RSI_ROC_i4'] = (df['RSI14'] - df['RSI14'].shift(4)) / divisor
    df['RSI_ROC_i6'] = (df['RSI14'] - df['RSI14'].shift(6)) / divisor
    df['RSI_ROC_i8'] = (df['RSI14'] - df['RSI14'].shift(8)) / divisor

    print(f"📝 เริ่มคำนวณ RSI Divergence") if Steps_to_calculating else None
    close_shift_2 = df['Close'].shift(2)
    rsi14_shift_2 = df['RSI14'].shift(2)
    # ใช้ np.isnan().any() เพื่อตรวจสอบ NaN ใน Series ที่ได้จากการ shift (แม้ว่า .isnull().all() ก็ใช้ได้ แต่ .any() อาจชัดเจนกว่าในบริบทนี้)
    if not (pd.isna(close_shift_2).any() or pd.isna(rsi14_shift_2).any()): # ตรวจสอบว่าไม่มี NaN ในแถวที่จะเปรียบเทียบ
        df['RSI_Divergence_i2'] = np.where(
            (df['Close'] > close_shift_2) & (df['RSI14'] < rsi14_shift_2), # แก้ไข: ใช้ rsi14_shift_2
                1, np.where(
                    (df['Close'] < close_shift_2) & (df['RSI14'] > rsi14_shift_2), # แก้ไข: ใช้ rsi14_shift_2
                    -1, 0
            )
        )
    else:
        df['RSI_Divergence_i2'] = 0 # หรือ np.nan ขึ้นอยู่กับความต้องการ

    close_shift_4 = df['Close'].shift(4)
    rsi14_shift_4 = df['RSI14'].shift(4)
    if not (pd.isna(close_shift_4).any() or pd.isna(rsi14_shift_4).any()):
        df['RSI_Divergence_i4'] = np.where(
            (df['Close'] > close_shift_4) & (df['RSI14'] < rsi14_shift_4),
                1, np.where(
                    (df['Close'] < close_shift_4) & (df['RSI14'] > rsi14_shift_4),
                    -1, 0
            )
        )
    else:
        df['RSI_Divergence_i4'] = 0 # หรือ np.nan ขึ้นอยู่กับความต้องการ

    close_shift_6 = df['Close'].shift(6)
    rsi14_shift_6 = df['RSI14'].shift(6)
    if not (pd.isna(close_shift_6).any() or pd.isna(rsi14_shift_6).any()):
        df['RSI_Divergence_i6'] = np.where(
            (df['Close'] > close_shift_6) & (df['RSI14'] < rsi14_shift_6),
                1, np.where(
                    (df['Close'] < close_shift_6) & (df['RSI14'] > rsi14_shift_6),
                    -1, 0
            )
        )
    else:
        df['RSI_Divergence_i6'] = 0

    # --- Indicator Calculation (MACD, RSI, Stochastic, BB, ADX, ATR, SR) ---
    # คำนวณ Indicators หลักก่อน
    print(f"📝 เริ่มคำนวณ MACD, RSI, Stochastic, BB, ADX, ATR, SR") if Steps_to_calculating else None
    macd = ta.macd(df["Close"])
    stoch = ta.stoch(high=df["High"], low=df["Low"], close=df["Close"])
    adx = ta.adx(high=df["High"], low=df["Low"], close=df["Close"])

    window_bb = 20
    rolling_mean_bb = df["Close"].rolling(window=window_bb, min_periods=1).mean()
    rolling_std_bb = df["Close"].rolling(window=window_bb, min_periods=1).std()
    bb_upper = (rolling_mean_bb + (rolling_std_bb * 2))
    bb_lower = (rolling_mean_bb - (rolling_std_bb * 2))
    bb_width = bb_upper - bb_lower

    window_atr = 14
    if all(col in df.columns for col in ['High', 'Low', 'Close']):
        tr1 = df['High'] - df['Low']
        if len(df) > 1:
            tr2 = (df['High'] - df['Close'].shift()).abs()
            tr3 = (df['Low'] - df['Close'].shift()).abs()
            true_range_df = pd.concat([tr1, tr2, tr3], axis=1) # สามารถ concat ตรงนี้ได้
            true_range = true_range_df.max(axis=1)
        else:
            true_range = pd.Series(np.nan, index=df.index)

        atr = true_range.rolling(window_atr, min_periods=1).mean()
    else:
        atr = pd.Series(np.nan, index=df.index) # สร้าง Series ว่างถ้าคอลัมน์ไม่ครบ

    lookback_sr = 50
    if all(col in df.columns for col in ['Low', 'High']):
        support = df['Low'].rolling(lookback_sr, min_periods=1).min()
        resistance = df['High'].rolling(lookback_sr, min_periods=1).max()
    else:
        support = pd.Series(np.nan, index=df.index)
        resistance = pd.Series(np.nan, index=df.index)

    print(f"📝 เริ่มคำนวณ MACD Features") if Steps_to_calculating else None
    macd_line_col = 'MACD_12_26_9'
    macd_signal_col = 'MACDs_12_26_9'
    df["MACD_12_26_9"] = macd[macd_line_col]
    df["MACDs_12_26_9"] = macd[macd_signal_col]
    df["MACDh_12_26_9"] = macd[macd_line_col] - macd[macd_signal_col]

    if macd_line_col in macd.columns:
        df["MACD_line"] = (macd[macd_line_col] > 0.0).astype(int) - (macd[macd_line_col] < 0.0).astype(int) # Convert to 1, 0, -1
    if macd_line_col in macd.columns and not macd[macd_line_col].shift(1).isnull().all():
        df["MACD_deep"] = (macd[macd_line_col] > macd[macd_line_col].shift(1)).astype(int) - (macd[macd_line_col] < macd[macd_line_col].shift(1)).astype(int)
    if macd_line_col in macd.columns and macd_signal_col in macd.columns:
        df["MACD_signal"] = (macd[macd_line_col] > macd[macd_signal_col]).astype(int) - (macd[macd_line_col] < macd[macd_signal_col]).astype(int)

    # Stochastic Features
    print(f"📝 เริ่มคำนวณ STO") if Steps_to_calculating else None
    stoch_k_col = 'STOCHk_14_3_3'
    stoch_d_col = 'STOCHd_14_3_3'

    if stoch_k_col in stoch.columns and stoch_d_col in stoch.columns:
        df["STO_cross"] = (stoch[stoch_k_col] > stoch[stoch_d_col]).astype(int) - (stoch[stoch_k_col] < stoch[stoch_d_col]).astype(int)
        df["STO_zone"] = (stoch[stoch_k_col] > 50).astype(int) - (stoch[stoch_k_col] < 50).astype(int)
        df["STO_overbought"] = (stoch[stoch_k_col] > 80).astype(int)
        df["STO_Oversold"] = (stoch[stoch_k_col] < 20).astype(int)

    # ADX Features
    print(f"📝 เริ่มคำนวณ ADX") if Steps_to_calculating else None
    adx_col = 'ADX_14'
    dmp_col = 'DMP_14'
    dmn_col = 'DMN_14'

    df["ADX_14"] = adx[adx_col]
    df["DMP_14"] = adx[dmp_col]
    df["DMN_14"] = adx[dmn_col]

    df["ADX_Deep"] = (df["ADX_14"] > df["ADX_14"].shift(1)).astype(int) - (df["ADX_14"] < df["ADX_14"].shift(1)).astype(int)

    df["ADX_zone_25"] = (df["ADX_14"] > 25).astype(int)
    df["ADX_zone_15"] = (df["ADX_14"] > 15).astype(int)

    if dmp_col in adx.columns and dmn_col in adx.columns:
        df["ADX_cross"] = (adx[dmp_col] > adx[dmn_col]).astype(int) - (adx[dmp_col] < adx[dmn_col]).astype(int)

    # ATR Feature
    print(f"📝 เริ่มคำนวณ ATR") if Steps_to_calculating else None
    df['ATR'] = atr # ATR Series ที่คำนวณไว้ก่อนหน้า

    df['ATR_Deep'] = (df["ATR"] > df["ATR"].shift(1)).astype(int) - (df["ATR"] < df["ATR"].shift(1)).astype(int)

    df['ATR_ROC_i2'] = (df['ATR'] - df['ATR'].shift(2)) / (df['ATR'] + 1e-10)
    df['ATR_ROC_i4'] = (df['ATR'] - df['ATR'].shift(4)) / (df['ATR'] + 1e-10)
    df['ATR_ROC_i6'] = (df['ATR'] - df['ATR'].shift(6)) / (df['ATR'] + 1e-10)
    df['ATR_ROC_i8'] = (df['ATR'] - df['ATR'].shift(8)) / (df['ATR'] + 1e-10)

    # BB Width Feature
    print(f"📝 เริ่มคำนวณ BB") if Steps_to_calculating else None
    df['BB_width'] = bb_width # BB_width Series ที่คำนวณไว้ก่อนหน้า

    # SR Features
    print(f"📝 เริ่มคำนวณ S and R") if Steps_to_calculating else None
    df['Support'] = support # 'Low'
    df['Resistance'] = resistance # 'High'

    df['PullBack_Up'] = (df['Resistance'] - df['Close']) / (df['Resistance'] - df['Support'] + 1e-10)
    df['PullBack_Down'] = (df['Close'] - df['Support']) / (df['Resistance'] - df['Support'] + 1e-10)
    # print(df[["DateTime", "Open", "High", "Low", "Close", "Support", "Resistance", "PullBack_Up", "PullBack_Down"]].tail(25))

    # หลีกเลี่ยงการคำนวณที่อาจทำให้เกิด division by zero
    epsilon = 1e-9
    Points = symbol_info[symbol]["Points"]

    df["SL_Buy"] =  np.minimum(
                    df["Open"] - 100 * Points,
                    np.maximum(df["Low_Prev_Min"], df["Support"])
                ) + epsilon
    
    df["SL_Sell"] = np.maximum(
                    df["Open"] + 100 * Points,
                    np.minimum(df["High_Prev_Max"], df["Resistance"])
                ) + epsilon
    
    # สร้างคอลัมน์สัดส่วนของ Buy Sell
    df["Ratio_Buy"] = ((df["Resistance"] - df["Open"]) / (df["Open"] - df["SL_Buy"]))
    df["Ratio_Sell"] = ((df["Open"] - df["Support"]) / (df["SL_Sell"] - df["Open"]))
    # print(df[["DateTime", "Open", "High", "Low", "Close", "Support", "Resistance", "Low_Prev_Min", "High_Prev_Max", "SL_Buy", "SL_Sell", "Ratio_Buy", "Ratio_Sell"]].tail(25))
    # print(df[df['Ratio_Buy'] > 1.00][['Date', 'Time', 'Ratio_Buy']].tail(10))
    # print(df[df['Ratio_Sell'] > 1.00][['Date', 'Time', 'Ratio_Sell']].tail(10))

    print(f"📝 เริ่มคำนวณ Interaction Features") if Steps_to_calculating else None
    # Interaction Features
    interaction_features = {
        'RSI_x_VolumeSpike' : df['RSI14'] * df['Volume_Spike'],
        'EMA_diff_x_ATR' : df['EMA_diff'] * df['ATR'],
        'Momentum5_x_Volatility10' : (df['Close'] - df['Close'].shift(5)) * df['Close'].rolling(10).std(),
        'RSI14_x_BBwidth' : df['RSI14'] * df['BB_width'],
        'MACD_signal_x_ADX' : df['MACD_signal'] * df['ADX_14'],
        'Price_above_EMA50_x_RSI_signal' : df['Price_above_EMA50'] * df['RSI_signal'],
        'RSI14_x_ATR' : df['RSI14'] * df['ATR'],
        'RSI14_x_PriceMove' : df['RSI14'] * df['Price_Move'],
        'EMA50_x_RollingVol5' : df['EMA50'] * df['Rolling_Vol_5'],
        'EMA_diff_x_BBwidth' : df['EMA_diff'] * df['BB_width'],
        'ADX_14_x_ATR' : df['ADX_14'] * df['ATR'],
        'MACD_signal_x_RSI14' : df['MACD_signal'] * df['RSI14'],
        'RSI14_x_StochK' : df['RSI14'] * stoch['STOCHk_14_3_3'] if 'STOCHk_14_3_3' in stoch.columns else np.nan,
        'RSI14_x_StochD' : df['RSI14'] * stoch['STOCHd_14_3_3'] if 'STOCHd_14_3_3' in stoch.columns else np.nan,
        'MACD_line_x_PriceMove' : df['MACD_line'] * df['Price_Move'],
        'ADX_14_x_RollingVol15' : df['ADX_14'] * df['Rolling_Vol_15'],
        'RSI14_x_Volume' : df['RSI14'] * df['Volume'],
        'ATR_x_PriceRange' : df['ATR'] * df['Price_Range'],
        'RSI14_x_PullBack_Up' : df['RSI14'] * df['PullBack_Up'],
        'RSI14_x_PullBack_Down' : df['RSI14'] * df['PullBack_Down'],
        'EMA_diff_x_RSI14' : df['EMA_diff'] * df['RSI14'],
        'ADX_14_x_BBwidth' : df['ADX_14'] * df['BB_width']
        }

    # รวมเข้า DataFrame เดียว
    interaction_df = pd.DataFrame(interaction_features)

    # แก้ไข: รวม interaction_df เข้า df ก่อนเลือกคอลัมน์
    df = pd.concat([df, interaction_df], axis=1)

    print("\n✅ ข้อมูล df ก่อนทำ Lag Features")
    # print(df.info())
    print(df.head())

    print(f"📝 เริ่มคำนวณ Lag Features") if Steps_to_calculating else None
    # กำหนด Lag periods ที่ต้องการ (ปรับตามความเหมาะสมของ timeframe)
    if timeframe >= 240:  # สำหรับ timeframe ขนาดใหญ่ (H4, D1)
        lags = [1, 2, 3, 5, 10]  # ตัวอย่างสำหรับ daily data
    else:  # สำหรับ timeframe ที่เล็กกว่า (M1, M5, M15, H1)
        lags = [1, 2, 3, 5, 10, 15, 20, 30, 50]  # ตัวอย่างสำหรับ intraday data

    # --- สร้าง Lag Features (ปรับปรุงเพื่อ Performance) ---
    # สร้าง DataFrame เปล่าสำหรับเก็บ Lag Features
    lag_features = pd.DataFrame(index=df.index)

    # Lag Features สำหรับคอลัมน์ราคา/Volume
    price_cols_upper = ['Close', 'Open', 'High', 'Low', 'Volume']
    for col in price_cols_upper:
        if col in df.columns:
            for lag in lags:
                lag_features[f'{col}_Lag_{lag}'] = df[col].shift(lag)
        else:
            print(f"Warning: Price column '{col}' not found in df for Lag Features.")

    # กรองเฉพาะคอลัมน์ที่มีอยู่จริงใน df หรือ DataFrame indicator_features
    # หรือใน Series ที่คำนวณ Indicators หลัก
    existing_indicator_cols_for_lag = [
        'RSI14' if 'RSI14' in df.columns else None,
        'EMA50' if 'EMA50' in df.columns else None,
        'EMA100' if 'EMA100' in df.columns else None,
        'EMA200' if 'EMA200' in df.columns else None,
        'ATR' if 'ATR' in df.columns else None, # หรือ atr.name ถ้า atr เป็น Series
        'BB_width' if 'BB_width' in df.columns else None, # หรือ bb_width.name
        macd_line_col if macd_line_col in macd.columns else None,
        macd_signal_col if macd_signal_col in macd.columns else None,
        stoch_k_col if stoch_k_col in stoch.columns else None,
        stoch_d_col if stoch_d_col in stoch.columns else None,
        adx_col if adx_col in adx.columns else None,
        dmp_col if dmp_col in adx.columns else None,
        dmn_col if dmn_col in adx.columns else None
    ]
    existing_indicator_cols_for_lag = [col for col in existing_indicator_cols_for_lag if col is not None] # กรอง None ออก

    # รวม df และ indicator_features (และ Series indicators หลัก) ชั่วคราวเพื่อทำ Lag
    # หรือเข้าถึง Series indicators หลักโดยตรง
    temp_df_for_lag = pd.concat([df[['Close', 'Open', 'High', 'Low', 'Volume', 'RSI14', 'EMA50', 'EMA100', 'EMA200']],
                                macd, stoch, adx, atr.rename('ATR'), bb_width.rename('BB_width'), support.rename('Support'), resistance.rename('Resistance')], axis=1)

    for indicator in existing_indicator_cols_for_lag:
        # ตรวจสอบว่าคอลัมน์ Indicator มีอยู่จริงใน temp_df_for_lag
        if indicator in temp_df_for_lag.columns:
            for lag in [1, 2, 3, 5]: # ใช้ lag periods ที่ใช้กับ indicators
                lag_features[f'{indicator}_Lag_{lag}'] = temp_df_for_lag[indicator].shift(lag)
        else:
            print(f"Warning: Indicator column '{indicator}' not found in combined data for Lag Features.")

    print(f"📝 เริ่มคำนวณ returns changes features") if Steps_to_calculating else None
    # --- สร้าง Features แบบเปลี่ยนแปลงจาก Lag (Returns, Changes) (ปรับปรุงเพื่อ Performance) ---
    # สร้าง DataFrame เปล่าสำหรับเก็บ Features Returns/Changes
    returns_changes_features = pd.DataFrame(index=df.index)

    for lag in [1, 2, 3, 5]: # ใช้ lag periods ที่ใช้กับ returns/changes
        if 'Close' in df.columns:
            returns_changes_features[f'Close_Return_{lag}'] = df['Close'].pct_change(lag)
        else:
            print(f"Warning: 'Close' column not found for Close_Return_{lag}.")
            returns_changes_features[f'Close_Return_{lag}'] = np.nan

        if 'Volume' in df.columns:
            returns_changes_features[f'Volume_Change_{lag}'] = df['Volume'].diff(lag) / (df['Volume'].shift(lag) + 1e-10)
        else:
            print(f"Warning: 'Volume' column not found for Volume_Change_{lag}.")
            returns_changes_features[f'Volume_Change_{lag}'] = np.nan

    print(f"📝 เริ่มคำนวณ Rolling Features") if Steps_to_calculating else None
    # --- สร้าง Rolling Features (ปรับปรุงเพื่อ Performance) ---
    # สร้าง DataFrame เปล่าสำหรับเก็บ Rolling Features
    rolling_features = pd.DataFrame(index=df.index)

    for window in [3, 5, 10, 20]: # ใช้ window sizes ที่ใช้กับ rolling features
        if 'Close' in df.columns:
            rolling_features[f'Close_MA_{window}'] = df['Close'].rolling(window, min_periods=1).mean().shift(1)
            rolling_features[f'Close_Std_{window}'] = df['Close'].rolling(window, min_periods=1).std().shift(1)
        else:
            print(f"Warning: 'Close' column not found for Close Rolling Features (window {window}).")
            rolling_features[f'Close_MA_{window}'] = np.nan
            rolling_features[f'Close_Std_{window}'] = np.nan

        if 'Volume' in df.columns:
            rolling_features[f'Volume_MA_{window}'] = df['Volume'].rolling(window, min_periods=1).mean().shift(1)
        else:
            print(f"Warning: 'Volume' column not found for Volume_MA_{window}.")
            rolling_features[f'Volume_MA_{window}'] = np.nan

    df_combined = pd.concat([df[['Date', 'Time', 'DateTime', 'Open', 'High', 'Low', 'Close', 'Volume', # ราคาเดิม
                                                'Entry_DayOfWeek', 'Entry_Hour', # Features เวลา
                                                'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', # ช่วงเวลา
                                                'Bar_CLp', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 
                                                'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', # Price Action
                                                'Price_Range', 'Price_Move', 'Price_Strangth', # Price Movement
                                                'Volume_MA20', 'Volume_Spike', # Volume Features
                                                'EMA50', 'EMA100', 'EMA200', 
                                                'EMA_diff', 'MA_Cross', 'Price_above_EMA50', # EMA Features
                                                'Rolling_Vol_5', 'Rolling_Vol_15', # Volatility
                                                'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', # Distance to EMA
                                                'RSI14', # RSI Calculation
                                                'RSI_signal', 'RSI_Overbought', 'RSI_Oversold',
                                                'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8',
                                                'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6',
                                                'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9',
                                                'MACD_line', 'MACD_deep', 'MACD_signal', 
                                                'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold',
                                                'ADX_14', 'DMP_14', 'DMN_14',
                                                'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross',
                                                'ATR',
                                                'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8',
                                                'BB_width',
                                                'Support', 'Resistance',
                                                'PullBack_Up', 'PullBack_Down',
                                                'Ratio_Buy', 'Ratio_Sell',
                                                'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 
                                                'Price_above_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 
                                                'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 
                                                'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_Up', 'RSI14_x_PullBack_Down', 
                                                'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 
                                                ]], # เลือกคอลัมน์เดิมที่ยังต้องการเก็บไว้ก่อน concat
                                            lag_features, # Lag Features
                                            returns_changes_features, # Returns/Changes Features
                                            rolling_features # Rolling Features
                                        ], axis=1)

    # ตรวจสอบข้อมูลหลังสร้าง Features
    print(f"\n✅ ข้อมูล df_combined หลังสร้าง Features:")
    print(df_combined.info())
    print(df_combined.head())

    # --- ลบแถวที่มีค่า NaN ที่เกิดจากการสร้าง Indicators และ Features ---
    initial_rows = len(df_combined)
    if not df_combined.empty:
        df = df_combined.dropna() # ตอนนี้ df จะเป็น DF ที่รวม features แล้ว
        print(f"✅ สร้าง Indicators และ Features เรียบร้อย (ลบแถวที่ขาดหายไป {initial_rows - len(df)} จาก {initial_rows} แถว)")
    else:
        print(f"Warning: df_combined is empty before dropna.")
        return

    if df.empty:
        print(f"Warning: No data left in df after dropna.")
        return

    # ตัวอย่างการสร้าง model_features (คุณอาจจะต้องปรับปรุง list นี้ให้ตรงกับ features ที่จะใช้จริง)
    model_features = [col for col in df.columns if col not in ['Date', 'Time', 'DateTime', 'Open', 'High', 'Low', 'Close', 'Volume']]
    # ลบคอลัมน์ชั่วคราวหรือคอลัมน์ที่ไม่ใช้เป็น input model ออก
    # model_features = [f for f in model_features if f not in ['RSI_Shift']] # ตัวอย่างลบคอลัมน์ชั่วคราว

    # ตรวจสอบข้อมูลหลังสร้าง Features
    print(f"\n✅ ข้อมูล df หลังสร้างรวม df_ft_combined")
    print(df.info())
    # print(df.columns.tolist())
    print(df)
    # print(df.head())
    # print(df.tail())

    print("\n🔍 ตรวจสอบ Temporal Dependence และคุณสมบัติอนุกรมเวลา")
    is_sorted = df['DateTime'].is_monotonic_increasing
    print(f"- ข้อมูลเรียงตามเวลา: {'ใช่' if is_sorted else 'ไม่'} (ควรเป็น 'ใช่')")
    
    if not is_sorted:
        print("⚠️ เตือน : ข้อมูลไม่เรียงตามเวลา กำลังเรียงข้อมูลใหม่...")
        df = df.sort_values('DateTime')
    
    # ตรวจสอบช่วงเวลาของข้อมูล
    time_diff = df['DateTime'].diff().dropna()
    print(f"- ช่วงเวลาข้อมูล: {df['DateTime'].min()} ถึง {df['DateTime'].max()}")
    print(f"- ระยะเวลารวม: {df['DateTime'].max() - df['DateTime'].min()}")
    print(f"- ช่วงห่างระหว่างบันทึก (เฉลี่ย): {time_diff.mean()}")
    print(f"- ช่วงห่างระหว่างบันทึก (สูงสุด): {time_diff.max()}")
    print(f"- ช่วงห่างระหว่างบันทึก (ต่ำสุด): {time_diff.min()}")
    
    # ตรวจสอบความต่อเนื่องของข้อมูล
    expected_interval = pd.Timedelta(minutes=timeframe)  # ใช้ timeframe จากข้อมูล
    missing_periods = (time_diff > expected_interval * 1.5).sum()  # 1.5 เท่าของช่วงเวลาปกติ
    print(f"- จำนวนช่วงเวลาที่หายไป: {missing_periods} (จากทั้งหมด {len(df)-1} ช่วง)")
    
    if missing_periods > 0:
        print("⚠️ เตือน : พบช่วงเวลาที่หายไปซึ่งอาจส่งผลต่อการวิเคราะห์")
        # สามารถเพิ่มโค้ดสำหรับจัดการข้อมูลที่หายไปที่นี่
    
    # ตรวจสอบ duplicate timestamps
    duplicate_times = df['DateTime'].duplicated().sum()
    print(f"- จำนวน timestamp ที่ซ้ำกัน: {duplicate_times}")
    
    if duplicate_times > 0:
        print("⚠️ เตือน : พบ timestamp ที่ซ้ำกัน กำลังจัดการด้วยการเฉลี่ย...")
        df = df.groupby('DateTime').mean().reset_index()  # หรือใช้วิธีอื่นที่เหมาะสม

    # ตรวจสอบ Stationarity ของราคา ตรวจสอบว่าข้อมูลนิ่งหรือไม่ (stationary) ด้วย ADF test และแสดงผลลัพธ์
    print("\n🔍 ตรวจสอบ Stationarity ของข้อมูล:")
    is_close_stationary = check_stationarity(df['Close'], 'Close')
    is_returns_stationary = check_stationarity(df['Close'].pct_change(), 'Returns')

    # สร้างรายงานสรุป
    temporal_report = {
        'symbol': symbol,
        'timeframe': timeframe,
        'data_period': f"{df['DateTime'].min()} to {df['DateTime'].max()}",
        'total_bars': len(df),
        'missing_periods': missing_periods,
        'duplicate_timestamps': duplicate_times,
        'is_stationary_close': is_close_stationary,
        'is_stationary_returns': is_returns_stationary,
        'average_time_gap': str(time_diff.mean())
        # 'hourly_win_rate_correlation': hour_win_rate.corr(pd.Series(np.arange(24)))  # ตรวจสอบความสัมพันธ์กับชั่วโมง
    }

    report_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_temporal_report.json")
    with open(report_path, 'w') as f:
        json.dump(temporal_report, f, indent=2, default=str)
    print(f"\n💾 บันทึกรายงาน Temporal Analysis ที่: {report_path}")

    # --- จัดการ Missing Values ---
    initial_rows = len(df)
    print(f"\n📌 จำนวน Missing Values หลังการประมวลผลเบื้องต้น:")
    # แสดงเฉพาะคอลัมน์ที่มีค่าว่างและจำนวน > 0
    print(df.isnull().sum()[df.isnull().sum() > 0])

    # # จัดการ Missing Values ใน Support และ Resistance
    df = df.copy()
    lookback = 50
    df.loc[:, 'Support'] = df['Low'].rolling(lookback).min().bfill()
    df.loc[:, 'Resistance'] = df['High'].rolling(lookback).max().bfill()

    # ตรวจสอบ Missing Values หลังการประมวลผล
    missing_values_after_processing = df.isnull().sum()
    print("\n📌 จำนวน Missing Values หลังการประมวลผล:")
    print(missing_values_after_processing[missing_values_after_processing > 0])
    
    print(df)
    check_data_quality(df, file, symbol, timeframe)

    print("\n🔍 ตรวจสอบข้อมูลก่อนสร้าง trade cycles:")
    print(f"ช่วงเวลาข้อมูล: {df['Date'].min()} ถึง {df['Date'].max()}")
    print(f"ค่าเฉลี่ย Close: {df['Close'].mean():.2f}")
    print(f"ค่า EMA50 ล่าสุด: {df['EMA50'].iloc[-1]:.2f}")
    print(f"ค่า RSI14 ล่าสุด: {df['RSI14'].iloc[-1]:.2f}")
    print(f"ค่า MACD ล่าสุด: {df['MACD_12_26_9'].iloc[-1]:.2f}")
    
    # ตรวจสอบค่า DayOfWeek แสดงตัวอย่างวันที่มีการบันทึก
    print(f"Unique values in df['Entry_DayOfWeek']: {df['Entry_DayOfWeek'].unique()}")
    
    # 4. สร้าง trade cycles
    print("\n🔍 กำลังสร้าง trade cycles...")
    model_name_to_use = modelname # ชื่อโมเดลที่ใช้ในการบันทึก/โหลด
    symbol_to_use = symbol
    timeframe_to_use = timeframe

    # โหลดรายชื่อ features ที่โมเดลใช้
    model_dir = f"Test_LightGBM/models/{str(timeframe_to_use).zfill(3)}_{symbol_to_use}"
    model_features_path = os.path.join(model_dir, f"{model_name_to_use}_{str(timeframe_to_use).zfill(3)}_{symbol_to_use}_features.pkl") # ใช้ model_name ด้วย
    model_features = None
    if os.path.exists(model_features_path):
        try:
            model_features = joblib.load(model_features_path)
            print(f"\n✅ โหลดรายชื่อ Features ที่ Model ใช้สำเร็จ: {len(model_features)} features")

            for i, feat in enumerate(model_features, 1):
                print(f"{i}. {feat}")
            print("\nFirst 5 rows of df:")
            print(df.head()) # ดูหน้าตาข้อมูลและชื่อคอลัมน์

        except Exception as e:
            print(f"⚠️ เกิดข้อผิดพลาด ขณะโหลดรายชื่อ Features: {str(e)}")
            model_features = None # ใช้ None ถ้าโหลดไม่ได้
    else:
        print(f"\n⚠️ ไม่พบไฟล์รายชื่อ Features ที่ Model ใช้: {model_features_path}")
        model_features = None # ใช้ None ถ้า⚠️ ไม่พบไฟล์

    # ตรวจสอบ ก่อนเข้า create trade cycles with model
    print(f"🔍 ตรวจสอบ columns")
    print(df.columns.tolist())

    # --- โหลด entry condition ที่ดีที่สุดจากรอบก่อน (ถ้ามี) ---
    best_entry_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_best_entry.pkl")
    best_entry_name = None
    if os.path.exists(best_entry_path):
        try:
            with open(best_entry_path, "rb") as f:
                best_entry_info = pickle.load(f)
            best_entry_name = best_entry_info.get("entry_name", None)
            print(f"\n⭐ ใช้ entry condition ที่ดีที่สุดจากรอบก่อน: {best_entry_name}")
        except Exception as e:
            print(f"\n⚠️ เกิดข้อผิดพลาดขณะโหลด best_entry: {e}")
            best_entry_name = None

    # ตรวจสอบว่า best_entry_name อยู่ใน entry conditions จริง
    if best_entry_name is not None and best_entry_name in entry_conditions:
        entry_func = entry_conditions[best_entry_name]
        print(f"🔎 ใช้ entry_func: {best_entry_name}")
    elif 'default' in entry_conditions:
        entry_func = entry_conditions['default']
        print("🔎 ใช้ entry_func: default")
    else:
        print("⚠️ ไม่พบ entry_func ที่จะใช้ (ทั้ง best_entry_name และ default)")
        entry_func = None

    # เรียกใช้ฟังก์ชันสร้างรายการซื้อขายพร้อม Model ช่วยตัดสินใจ
    # df_processed ในที่นี้ควรเป็น Dataframe ที่เตรียม Features ครบถ้วนแล้ว

    # print(f"\n✅ ข้อมูล df ก่อนเข้า create trade cycles with model")
    # print("df columns:", df.columns.tolist())
    # print("model_features:", model_features)
    # print("จำนวน columns ใน df:", len(df.columns))
    # print("จำนวน features ที่โมเดลใช้:", len(model_features))

    if entry_func is not None:
        trade_df, cycle_stats = create_trade_cycles_with_model(
            df,
            trained_model=model,
            scaler=scaler,
            model_features=model_features,
            rsi_level=input_rsi_level_in,
            rsi_level_out=input_rsi_level_out,
            stop_loss_atr_multiplier=input_stop_loss_atr,
            take_profit_stop_loss_ratio=input_take_profit,
            symbol=symbol_to_use,
            timeframe=timeframe_to_use,
            identifier=identifier,
            nBars_SL=nBars_SL,
            model_confidence_threshold=confidence_threshold,
            entry_condition_func=entry_func,
            entry_condition_name=best_entry_name
        )
    else:
        print("❌ ไม่มี entry_func ที่จะใช้สำหรับ backtest/production")
        # return None, None, None, None, None, None

    # print(f"\n✅ ข้อมูล df หลังจาก create trade cycles with model")
    # print("df columns:", df.columns.tolist())
    # print("model_features:", model_features)
    # print("จำนวน columns ใน df:", len(df.columns))
    # print("จำนวน features ที่โมเดลใช้:", len(model_features))

    # print(f"\n✅ ตรวจสอบ features หลังเรียก create trade cycles with model")
    # for i, feat in enumerate(model_features, 1):
    #     print(f"{i}. {feat}")

    # แสดงข้อมูลก่อนตรวจสอบ
    print("\n📌 ข้อมูลก่อนตรวจสอบ:")
    print(f"จำนวนแถวข้อมูลทั้งหมด: {len(df)} ตัวอย่างข้อมูล df")
    print(df.head() if not df.empty else "ไม่มีข้อมูล การซื้อขาย")
    print(f"จำนวนการซื้อขายที่พบ: {len(trade_df)} ตัวอย่างข้อมูล trade_df")
    print(trade_df.head() if not trade_df.empty else "ไม่มีข้อมูล การซื้อขาย")

    print("\n📊 สถิติการซื้อขาย:")
    print(f"{'='*40}")
    print(f"{'ประเภท':<20}{'ค่าสถิติ':<20}")
    print(f"{'-'*40}")

    # วิเคราะห์ประสิทธิภาพการซื้อขาย (จะได้ stats ที่มี buy, sell, buy_sell)
    stats = analyze_trade_performance(trade_df)

    # แสดงสถิติหลัก
    if 'buy' in stats and 'sell' in stats and 'buy_sell' in stats:
        print("📈 สถิติสำหรับ Buy Trades:")
        print(f"{'Win%':<20}{stats['buy'].get('win_rate', 0):<20.2f}")
        print(f"{'Expectancy':<20}{stats['buy'].get('expectancy', 0):<20.2f}")

        print("📈 สถิติสำหรับ Sell Trades:")
        print(f"{'Win%':<20}{stats['sell'].get('win_rate', 0):<20.2f}")
        print(f"{'Expectancy':<20}{stats['sell'].get('expectancy', 0):<20.2f}")

        print("📈 สถิติสำหรับ Buy_sell Trades:")
        print(f"{'Win%':<20}{stats['buy_sell'].get('win_rate', 0):<20.2f}")
        print(f"{'Expectancy':<20}{stats['buy_sell'].get('expectancy', 0):<20.2f}")
    else:
        print("⚠️ ไม่พบ สถิติหลัก")

    print(f"{'='*40}")

    # แสดงสถิติรายวัน (ใช้ cycle_stats ที่ได้จาก create_trade_cycles)
    if 'day_stats' in cycle_stats:
        print("\n📊 สถิติรายวัน:")
        print(f"{'วัน':<10}{'Win Rate (%)':<15}{'จำนวนการซื้อขาย':<20}")
        print(f"{'-'*45}")
        day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        for day, data in cycle_stats['day_stats'].items():
            if data.get('total', 0) > 0:  # เพิ่มเงื่อนไขตรวจสอบจำนวนการซื้อขาย
                win_rate = (data.get('win', 0) / data.get('total', 1)) * 100 if data.get('total', 0) > 0 else 0
                day_name = day_names[day] if isinstance(day, int) and 0 <= day < 7 else str(day)
                print(f"{day_name:<10}{win_rate:<15.2f}{data.get('total', 0):<20}")
        if not any(data.get('total', 0) > 0 for data in cycle_stats['day_stats'].values()):
            print("ไม่มีข้อมูล สถิติรายวัน") # แสดงข้อความหากไม่มีข้อมูล 
    else:
        print("⚠️ ไม่พบ สถิติรายวัน")

    print(f"{'='*40}")
    
    # แสดงสถิติรายชั่วโมง (ใช้ cycle_stats ที่ได้จาก create_trade_cycles)
    if 'hour_stats' in cycle_stats:
        print("\n📊 สถิติรายชั่วโมง:")
        print(f"{'ชั่วโมง':<10}{'Win Rate (%)':<15}{'จำนวนการซื้อขาย':<20}")
        print(f"{'-'*45}")
        has_data = False # เพิ่มตัวแปรตรวจสอบว่ามีข้อมูลหรือไม่
        for hour, data in sorted(cycle_stats['hour_stats'].items()):
            if data.get('total', 0) > 0:  # เพิ่มเงื่อนไขตรวจสอบจำนวนการซื้อขาย
                win_rate = (data.get('win', 0) / data.get('total', 1)) * 100 if data.get('total', 0) > 0 else 0
                print(f"{hour:<10}{win_rate:<15.2f}{data.get('total', 0):<20}")
                has_data = True
        if not has_data:
            print("ไม่มีข้อมูล สถิติรายชั่วโมง") # แสดงข้อความหากไม่มีข้อมูล 
    else:
        print("⚠️ ไม่พบ สถิติรายชั่วโมง")

    print(f"{'='*40}")
    
    if trade_df.empty:
        print("\n⚠️ ไม่มีข้อมูล การซื้อขายที่ตรงตามเงื่อนไข")
        print("ℹ️ สาเหตุอาจมาจาก:")
        print("- เงื่อนไขการซื้อขายเข้มงวดเกินไป")
        print("- ข้อมูลไม่เหมาะสมกับกลยุทธ์")
        print("- ช่วงเวลาที่วิเคราะห์ไม่มีสัญญาณซื้อขาย")
        return None, None, None, None, None, None
    
    # เพิ่มการตรวจสอบว่า trade_df เป็น DataFrame และไม่ว่างเปล่า
    if not isinstance(trade_df, pd.DataFrame) or trade_df.empty:
        print("⚠️ ไม่มีข้อมูล การซื้อขายที่ตรงตามเงื่อนไข")
        return None, None, None, None, None, None

    # 5. รวม features กับ trade data
    print("\n🔍 กำลังรวม features กับ trade data...")
    print("🔍 ทดลองพิมพ์ trade_df")
    print(trade_df)

    print("\n🔍 ทดลองพิมพ์ df")
    print(df)
    
    # แปลงรูปแบบวันที่ให้ตรงกันก่อน merge
    # trade_df['Entry_DateTime'] = pd.to_datetime(trade_df['Entry Time'], format='%Y.%m.%d %H:%M')
    # df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])

    # แก้ไข format string ของ trade_df['Entry Time'] ให้รวมวินาทีด้วย (%S)
    trade_df['Entry_DateTime'] = pd.to_datetime(trade_df['Entry Time'], format='%Y.%m.%d %H:%M:%S')

    # สำหรับ df['DateTime'] คุณไม่ได้ระบุ format
    # จาก output ของ df['Date'] และ df['Time'] (2020.01.02 09:00:00)
    # รูปแบบก็คือ "%Y.%m.%d %H:%M:%S" เช่นกัน
    # Pandas มักจะ auto-detect รูปแบบนี้ได้ แต่ถ้ายังติดปัญหา ควรระบุ format ให้ชัดเจนไปด้วย
    df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'], format='%Y.%m.%d %H:%M:%S') # แนะนำให้ระบุ format ที่นี่ด้วย
    
    # ตรวจสอบรูปแบบวันที่หลังแปลง
    print("\nตัวอย่าง Entry_DateTime ใน trade_df:", trade_df['Entry_DateTime'].head())
    print("ตัวอย่าง DateTime ใน df:", df['DateTime'].head())

    # ทำ merge โดยใช้ merge_asof สำหรับการจับคู่เวลาที่ใกล้ที่สุด (backward เพื่อป้องกัน look-ahead bias)
    # เพิ่ม tolerance เพื่อให้แน่ใจว่าเวลาใกล้กันจริง
    # เพิ่มคอลัมน์ Indicator ที่ต้องการใช้ทั้งหมด
    columns_to_merge = [
        # Indicator
        'DateTime', 
        'Entry_DayOfWeek', 'Entry_Hour', 
        'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 
        'Bar_CLp', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 
        'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 
        'Price_Range', 'Price_Move', 'Price_Strangth', 
        'Volume_MA20', 'Volume_Spike', 
        'EMA50', 'EMA100', 'EMA200', 
        'EMA_diff', 'MA_Cross', 'Price_above_EMA50', 
        'Rolling_Vol_5', 'Rolling_Vol_15', 
        'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 
        'RSI14', 
        'RSI_signal', 'RSI_Overbought', 'RSI_Oversold', 
        'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 
        'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 
        'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 
        'MACD_line', 'MACD_deep', 'MACD_signal', 
        'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 
        'ADX_14', 'DMP_14', 'DMN_14', 
        'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 
        'ATR', 
        'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 
        'BB_width', 
        'Support', 'Resistance', 
        'PullBack_Up', 'PullBack_Down', 
        'Ratio_Buy', 'Ratio_Sell', 

        # Interaction Features
        'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 
        'Price_above_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 
        'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 
        'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_Up', 'RSI14_x_PullBack_Down', 
        'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 

        # Lag Features สำหรับ Price Lags
        'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 
        'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 
        'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 
        'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 

        # Lag Features สำหรับ Volume Lags
        'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 

        # Lag Features สำหรับ Indicator Lags
        'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 
        'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 
        'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 
        'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 
        'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 
        'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 
        'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 
        'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 
        'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 
        'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 
        'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 
        'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 
        'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 

        # Return and Change Features
        'Close_Return_1', 'Volume_Change_1', 
        'Close_Return_2', 'Volume_Change_2', 
        'Close_Return_3', 'Volume_Change_3', 
        'Close_Return_5', 'Volume_Change_5', 

        # Moving Averages and Std
        'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 
        'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 
        'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 
        'Close_MA_20', 'Close_Std_20', 'Volume_MA_20'
    ]
    
    # กรองคอลัมน์ที่ต้องการ Merge เฉพาะคอลัมน์ที่มีอยู่ใน df จริงๆ (เผื่อในอนาคตมีการเพิ่ม/ลด Indicator ที่คำนวณ)
    available_columns_to_merge = [col for col in columns_to_merge if col in df.columns]
    print(f"🔍 กำลังทำการ merge_asof โดยใช้คอลัมน์ {len(available_columns_to_merge)} features : {available_columns_to_merge}")

    trade_df = pd.merge_asof(
        trade_df.sort_values('Entry_DateTime'),
        df.sort_values('DateTime')[available_columns_to_merge], # ใช้รายการคอลัมน์ที่กรองแล้ว
        left_on='Entry_DateTime',
        right_on='DateTime',
        direction='backward', # แก้ไขตรงนี้
        tolerance=pd.Timedelta('5min') # เพิ่ม tolerance ตรงนี้ (ปรับค่า '5min' ตามความเหมาะสมของ Timeframe)
    )

    # ลบคอลัมน์ DateTime ที่ได้จากการ merge ของ df ออก (เนื่องจากซ้ำกับ Entry_DateTime)
    trade_df.drop('DateTime', axis=1, inplace=True, errors='ignore')

    # เพิ่มฟีเจอร์วันและเวลา (คำนวณจาก Entry_DateTime หลัง merge)
    # โค้ดเดิมมีอยู่แล้ว แต่อาจต้อง ensure ว่า Entry_DateTime ยังเป็น datetime หลัง merge
    # trade_df['Entry_DateTime'] = pd.to_datetime(trade_df['Entry Time']) # อาจไม่ต้องทำซ้ำถ้า merge_asof เก็บ Entry_DateTime ไว้
    trade_df['Entry_DayOfWeek'] = trade_df['Entry_DateTime'].dt.dayofweek
    trade_df['Entry_Hour'] = trade_df['Entry_DateTime'].dt.hour
    trade_df['IsWeekend'] = (trade_df['Entry_DayOfWeek'] >= 5).astype(int)
    trade_df['IsMorning'] = ((trade_df['Entry_Hour'] >= 8) & (trade_df['Entry_Hour'] < 12)).astype(int)
    trade_df['IsAfternoon'] = ((trade_df['Entry_Hour'] >= 12) & (trade_df['Entry_Hour'] < 16)).astype(int)
    trade_df['IsEvening'] = ((trade_df['Entry_Hour'] >= 16) & (trade_df['Entry_Hour'] < 20)).astype(int)
    trade_df['IsNight'] = ((trade_df['Entry_Hour'] >= 20) | (trade_df['Entry_Hour'] < 4)).astype(int)

    # ลบคอลัมน์ Entry_DateTime ชั่วคราวที่สร้างขึ้นเพื่อ merge
    trade_df.drop(['Entry_DateTime'], axis=1, inplace=True, errors='ignore')

    # ตรวจสอบหลัง merge
    # print("\nจำนวน missing values หลัง merge:", trade_df.isnull().sum())

    missing_values = trade_df.isnull().sum()
    has_missing = (missing_values > 0).any()

    print("\n📌 ตรวจสอบ Missing Values หลัง Merge:")
    if has_missing:
        print("พบ Missing Values ในคอลัมน์ต่อไปนี้:")
        print(missing_values[missing_values > 0])
    else:
        print("✅ ไม่พบ Missing Values ใน DataFrame")
    
    # แสดงข้อมูลหลังรวม features
    print("\n📌 ข้อมูลหลังรวม features:")
    print(f"จำนวนแถว: {len(trade_df)}")
    print("ตัวอย่างคอลัมน์ใหม่ 5 แถว:")
    print(trade_df[['Entry Time', 'EMA50', 'EMA100', 'RSI14']].head())

    # เพิ่มใน features ที่จะใช้สำหรับโมเดล >> เนื่องจากใช้การคัดเลือกอัตโนมัติ ดูความสัมพันธ์กับ Target
    features = []
    
    # 6. สร้าง target variable
    print("\n🔍 กำลังสร้าง target variable...")
    
    # ตรวจสอบว่าคอลัมน์ Profit มีอยู่และเป็นตัวเลข
    if 'Profit' not in trade_df.columns or not pd.api.types.is_numeric_dtype(trade_df['Profit']):
        print("⚠️ ไม่พบ คอลัมน์ Profit หรือไม่ใช่ตัวเลข ไม่สามารถสร้าง Target ได้")
        return None, None, None, None, None, None
    
    trade_df = process_trade_targets(trade_df)

    print("\n📌 ข้อมูลหลังสร้าง target variable:")
    print(f"จำนวนแถว: {len(trade_df)}")
    print("ตัวอย่างคอลัมน์ใหม่ 5 แถว:")
    print(trade_df[['Entry Time', 'EMA50', 'EMA100', 'RSI14']].head())
    
    # แสดงข้อมูล target
    print("\n📌 ข้อมูลหลังสร้าง target:")
    if 'Target' in trade_df.columns:
        print("การกระจายของ Target:")
        print(trade_df['Target'].value_counts())
    else:
        print("⚠️ ไม่พบ คอลัมน์ Target ในข้อมูล")
    
    # เพิ่มการตรวจสอบว่า trade_df ไม่ว่างเปล่าและมีคอลัมน์ Target
    if trade_df.empty or 'Target' not in trade_df.columns:
        print("\n⚠️ ไม่สามารถสร้าง Target ได้ >> ออกจาก load and process data")
        return None, None, None, None, None, None
    
    # ==============================================
    # ส่วนปรับปรุง: ใช้ฟังก์ชัน select features แทนกระบวนการเดิม
    # ==============================================

    # # ทำความสะอาดข้อมูล
    # trade_df = trade_df.dropna()  # ลบแถวที่มี missing values
    
    # # แก้ไขชื่อ features ให้สอดคล้องกัน
    # trade_df = trade_df.rename(columns={'Entry Price': 'Entry_Price', 'Exit Price': 'Exit_Price'})

    # # เรียกใช้ฟังก์ชัน select features
    # print("\n🔍 เริ่มกระบวนการเลือก Features...")
    # features = select features(trade_df)


    # ส่วนเรียกใช้ select features ใน load and process data
    # ... (โค้ดส่วนอื่นๆ ใน load and process data ก่อนเรียก select features)
    # ทำความสะอาดข้อมูล (ควรทำก่อน select features เพื่อให้การคำนวณ correlation แม่นยำ)
    trade_df = trade_df.dropna() # ลบแถวที่มี missing values ที่เกิดจากการ merge หรือคำนวณ

    # แก้ไขชื่อ features ให้สอดคล้องกัน (ถ้าจำเป็น)
    # trade_df = trade_df.rename(columns={'OldName': 'NewName'}) # ตรวจสอบว่าชื่อคอลัมน์หลัง merge เป็นอย่างไร

    # ตรวจสอบจำนวนข้อมูลหลัง dropna
    if trade_df.empty:
        print("⚠️ ข้อมูล trade_df ว่างเปล่าหลังจากจัดการ Missing Values ไม่สามารถเลือก Features ได้")
        return None, None, None, None, None, None

    # เรียกใช้ฟังก์ชัน select features
    print("\n🔍 เริ่มกระบวนการเลือก Features...")
    features = select_features(trade_df, timeframe) # เรียกใช้ฟังก์ชันที่แก้ไขแล้ว

    # แสดงสรุป features ที่จะใช้
    print("\n📌 สรุป Features ที่จะใช้สำหรับโมเดล:")
    print(f"จำนวน Features: {len(features)}")
    for i, feat in enumerate(features, 1):
        print(f"{i}. {feat}")

    # ==============================================
    # ส่วนตรวจสอบ Class Imbalance
    # ==============================================
    
    print("\n🔍 กำลังตรวจสอบข้อมูลก่อนฝึกโมเดล...")

    # 1. ตรวจสอบการกระจายของ Target
    if 'Target' in trade_df.columns:
        target_dist = trade_df['Target'].value_counts(normalize=True)
        print("\n📊 การกระจายของ Target:")
        print(target_dist)
        
        # ตรวจสอบว่ามีทั้งสองคลาสหรือไม่
        if len(target_dist) < 2:
            print("⚠️ มีเพียงคลาสเดียวใน Target ไม่สามารถฝึกโมเดลได้")
            return None, None, None, None, None, None
            
        # ตรวจสอบ Class Imbalance
        imbalance_ratio = target_dist.min() / target_dist.max()
        print(f"อัตราส่วน Class Imbalance: {imbalance_ratio:.2f}")
        
        if imbalance_ratio < 0.2:  # ถ้าคลาส minority มีน้อยกว่า 20% ของคลาส majority
            print("⚠️ พบ Class Imbalance รุนแรง (อัตราส่วนน้อยกว่า 20%)")

    # 2. ตรวจสอบจำนวนข้อมูลขั้นต่ำ
    min_samples = 50  # กำหนดค่าต่ำสุดตามความเหมาะสม
    if len(trade_df) < min_samples:
        print(f"⚠️ ข้อมูลมีน้อยเกินไป ({len(trade_df)} แถว) ขั้นต่ำที่ต้องการ: {min_samples} แถว")
        return None, None, None, None, None, None

    # 3. ตรวจสอบ missing values
    missing_values = trade_df[features].isnull().sum().sum()
    if missing_values > 0:
        print(f"⚠️ พบ missing values {missing_values} ค่า ใน features")
        # แสดงคอลัมน์ที่มี missing values
        print("คอลัมน์ที่มี missing values:")
        print(trade_df[features].isnull().sum()[trade_df[features].isnull().sum() > 0])
    else:
        print("✅ ไม่พบ missing values ใน features")

    # 4. ตรวจสอบค่าผิดปกติใน features
    print("\n📊 สถิติพื้นฐานของ features:")
    print(trade_df[features].describe().transpose())

    # 5. ตรวจสอบ correlation สูงระหว่าง features
    print("\n🔍 กำลังตรวจสอบความสัมพันธ์ระหว่าง features...")
    try:
        corr_matrix = trade_df[features].corr().abs()
        
        # สร้าง upper triangle matrix แบบ boolean
        upper = np.triu(np.ones(corr_matrix.shape, dtype=bool), k=1)
        
        # นับจำนวนคู่ที่มี correlation สูง
        high_corr = (corr_matrix.where(upper) > 0.8).sum().sum()
        
        if high_corr > 0:
            print(f"⚠️ พบ {high_corr} คู่ features ที่มีความสัมพันธ์สูง (>0.8)")
            
            # แสดงคู่ features ที่มีความสัมพันธ์สูง
            high_corr_pairs = corr_matrix.stack()[
                (corr_matrix.stack() > 0.8) & 
                (corr_matrix.stack() < 1.0)  # ไม่รวมความสัมพันธ์กับตัวเอง
            ].reset_index()
            
            high_corr_pairs.columns = ['Feature 1', 'Feature 2', 'Correlation']
            print("\nคู่ features ที่มีความสัมพันธ์สูง:")
            print(high_corr_pairs.sort_values('Correlation', ascending=False).to_string(index=False))
        else:
            print("✅ ไม่พบ features ที่มีความสัมพันธ์สูงเกินไป")
            
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะตรวจสอบความสัมพันธ์ระหว่าง features: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # ==============================================
    # แบ่งข้อมูลเป็น train/val/test หลังจากเตรียม features, สร้าง target, และตรวจสอบข้อมูลเสร็จแล้ว
    # ==============================================
    
    print("\n🔍 กำลังแบ่งข้อมูลเป็น train/val/test...")
    if len(trade_df) < 10:
        print(f"\n⚠️ ข้อมูลใน trade_df มีน้อยเกินไป ({len(trade_df)} แถว)")
        print("ตัวอย่างข้อมูลสุดท้าย 5 แถว:")
        print(trade_df.tail())
        return None, None, None, None, None, None

    # เรียงข้อมูลตามเวลา (หากยังไม่ได้เรียง) เพื่อให้การแบ่งข้อมูลไม่เกิด data leakage (ข้อมูลอนาคตไปอยู่ใน train)
    trade_df = trade_df.sort_values('Entry Time')
    
    # แบ่งตามสัดส่วนเวลา
    train_size = int(0.6 * len(trade_df))  # 60% สำหรับฝึก
    val_size = int(0.2 * len(trade_df))    # 20% สำหรับ validation
    
    # แบ่งข้อมูล
    train = trade_df.iloc[:train_size]
    val = trade_df.iloc[train_size:train_size + val_size]
    test = trade_df.iloc[train_size + val_size:]
    
    # ตรวจสอบการกระจายของ Target ในแต่ละชุด
    print("\nการกระจายของ Target ในชุดข้อมูล:")
    # เลือก target column ตาม configuration
    target_column = "Target_Multiclass" if USE_MULTICLASS_TARGET and "Target_Multiclass" in train.columns else "Target"

    print(f"\n📊 ใช้ Target Column: {target_column}")
    print("Train:", train[target_column].value_counts(normalize=True))
    print("Val:", val[target_column].value_counts(normalize=True))
    print("Test:", test[target_column].value_counts(normalize=True))

    # แยก features และ target
    X_train, y_train = train[features], train[target_column]
    X_val, y_val = val[features], val[target_column]
    X_test, y_test = test[features], test[target_column]

    # === หา optimal thresholds จาก validation set ===
    time_filter_path = f"Test_LightGBM/thresholds/{symbol}_{timeframe}_time_filters.pkl"
    analyze_time_filters(
        test,  # หรือ val แล้วแต่ต้องการ
        min_win_rate=0.35,
        min_expectancy=0.0,
        save_path=time_filter_path,
        symbol=symbol
    )

    # === หา optimal nBars SL ===
    # ควรใช้ validation set (val_idx_start:val_idx_end) เพื่อหา optimal nBars SL
    # แล้วนำค่า optimal ที่ได้ไปใช้กับ test set เพื่อประเมินผล "จริง"
    # ไม่ควรใช้ test set ในการหา parameter ใดๆ เพราะจะทำให้ผลประเมินโมเดลไม่สะท้อนความสามารถจริง

    # ถ้าต้องการใช้ validation set ของ df (raw OHLC) ช่วงนี้คือ "กลาง" เพื่อหา optimal nBars SL ของข้อมูลทั้งหมด (หลัง train, ก่อน test)
    val_idx_start = train_size
    val_idx_end = train_size + val_size
    find_optimal_nbars_sl(df.iloc[val_idx_start:val_idx_end].copy(), symbol, timeframe, entry_func, best_entry_name)

    # ถ้าต้องการใช้ test set ของ df (raw OHLC) ช่วงนี้คือ "ท้ายสุด" เพื่อหา optimal nBars SL เป็นของข้อมูล (อนาคตสุด)
    # test_idx_start = train_size + val_size
    # find_optimal_nbars_sl(df.iloc[test_idx_start:].copy(), symbol, timeframe)

    # 6. ทำ Feature Scaling ปรับฟีเจอร์อยู่ในสเกลใกล้เคียงกัน
    # การปรับขนาดข้อมูลแต่ละฟีเจอร์ให้อยู่ในสเกลที่เหมาะสม เพื่อให้โมเดลเรียนรู้และทำนายได้แม่นยำและเสถียรขึ้น
    print("\n🔍 กำลังทำ Feature Scaling...")
    try:
        scaler = StandardScaler()
        
        # ตรวจสอบว่ามีข้อมูลใน X_train หรือไม่
        if len(X_train) == 0:
            print("⚠️ ไม่มีข้อมูล ใน X_train ไม่สามารถทำ Feature Scaling ได้")
            return None, None, None, None, None, None
            
        X_train_scaled = scaler.fit_transform(X_train) # จะคำนวณค่าเฉลี่ยและส่วนเบี่ยงเบนมาตรฐานจาก train set แล้วนำไปปรับขนาดข้อมูล
        X_train = pd.DataFrame(X_train_scaled, columns=features, index=X_train.index)
        
        X_val_scaled = scaler.transform(X_val) # จะใช้ค่าที่ได้จาก train set ไปปรับขนาด validation/test set (ป้องกัน data leakage)
        X_val = pd.DataFrame(X_val_scaled, columns=features, index=X_val.index)
        
        X_test_scaled = scaler.transform(X_test)
        X_test = pd.DataFrame(X_test_scaled, columns=features, index=X_test.index)
        
        print("✅ ทำ Feature Scaling เรียบร้อยแล้ว")
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะทำ Feature Scaling: {str(e)}")
        return None, None, None, None, None, None

    return (X_train, y_train), (X_val, y_val), (X_test, y_test), df, trade_df, stats

"""ตรวจสอบคุณภาพข้อมูล เช่น duplicate, missing values, และแสดง distribution ของราคาปิด"""
def check_data_quality(df, file_name, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน check data quality") if Steps_to_do else None

    print(f"{'='*50}")
    print(f"Data Quality Check for {file_name}")
    print("="*50)
    
    # missing = df.isnull().sum()
    # print("\n[1] Missing Values:")
    # print(missing[missing > 0].to_string())
    
    # print("\n[2] Data Types:")
    # print(df.dtypes.to_string())
    
    # print("\n[3] Descriptive Stats:")
    # print(df.describe(include='all').to_string())
    
    print(f"\n[4] Duplicate Rows: {df.duplicated().sum()}")

    fig, ax = plt.subplots(figsize=(10, 5))
    ax.hist(df['Close'], bins=50)
    ax.set_title('Price Distribution')
    plt.savefig(os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_price_dist.png"))
    plt.close(fig)

"""ตรวจสอบความ stationary ของ series (เช่น ราคาปิด) ด้วย ADF test"""
def check_stationarity(series, name='Close'):
    print(f"\n🏗️ เปิดใช้งาน check stationarity") if Steps_to_do else None

    result = adfuller(series.dropna())
    print(f'📊 ผลการทดสอบ Stationarity สำหรับ {name}:')
    print(f'ADF Statistic: {result[0]:.4f}')
    print(f'p-value: {result[1]:.4f}')
    print('Critical Values:')
    for key, value in result[4].items():
        print(f'   {key}: {value:.4f}')
    return result[1] < 0.05  # คืนค่า True ถ้า stationary

def create_multiclass_target(profit_series):
    """
    สร้าง multi-class target จาก profit values

    Args:
        profit_series: pandas Series ของ profit values

    Returns:
        pandas Series ของ class labels (0-4)
    """
    print(f"\n🏗️ เปิดใช้งาน create multiclass target") if Steps_to_do else None

    # สร้าง conditions สำหรับแต่ละ class
    conditions = [
        profit_series >= PROFIT_THRESHOLDS['strong_buy'],    # Class 4: Strong Buy
        profit_series >= PROFIT_THRESHOLDS['weak_buy'],      # Class 3: Weak Buy
        profit_series >= PROFIT_THRESHOLDS['no_trade'],      # Class 2: No Trade
        profit_series >= PROFIT_THRESHOLDS['weak_sell'],     # Class 1: Weak Sell
        profit_series >= PROFIT_THRESHOLDS['strong_sell']    # Class 0: Strong Sell
    ]

    # สร้าง choices สำหรับแต่ละ class
    choices = [4, 3, 2, 1, 0]

    # ใช้ np.select เพื่อสร้าง target
    target = np.select(conditions, choices, default=0)

    # แสดงสถิติ
    unique, counts = np.unique(target, return_counts=True)
    print(f"📊 Multi-class Target Statistics:")
    for class_id, count in zip(unique, counts):
        class_name = CLASS_MAPPING.get(class_id, f"Unknown_{class_id}")
        percentage = (count / len(target)) * 100
        print(f"  Class {class_id} ({class_name}): {count} samples ({percentage:.1f}%)")

    # ตรวจสอบว่ามีอย่างน้อย 2 classes และแต่ละ class มีข้อมูลเพียงพอ
    min_samples_per_class = 10
    valid_classes = [class_id for class_id, count in zip(unique, counts) if count >= min_samples_per_class]

    if len(valid_classes) < 2:
        print(f"⚠️ Warning: มีเพียง {len(valid_classes)} classes ที่มีข้อมูลเพียงพอ (>= {min_samples_per_class} samples)")
        print(f"⚠️ จะใช้ Binary Classification แทน Multi-class")
        # แปลงเป็น binary: 0-2 = 0 (sell/no_trade), 3-4 = 1 (buy)
        binary_target = np.where(target >= 3, 1, 0)
        return pd.Series(binary_target, index=profit_series.index)

    print(f"✅ Multi-class Target ถูกต้อง: {len(valid_classes)} classes พร้อมใช้งาน")
    return pd.Series(target, index=profit_series.index)

def evaluate_multiclass_model(y_true, y_pred, y_pred_proba=None):
    """
    ประเมินผลโมเดล multi-class classification

    Args:
        y_true: true labels
        y_pred: predicted labels
        y_pred_proba: predicted probabilities (optional)

    Returns:
        dict: metrics dictionary
    """
    print(f"\n🏗️ เปิดใช้งาน evaluate multiclass model") if Steps_to_do else None

    metrics = {}

    # Basic metrics
    metrics['accuracy'] = accuracy_score(y_true, y_pred)
    metrics['f1_macro'] = f1_score(y_true, y_pred, average='macro')
    metrics['f1_weighted'] = f1_score(y_true, y_pred, average='weighted')
    metrics['precision_macro'] = precision_score(y_true, y_pred, average='macro')
    metrics['precision_weighted'] = precision_score(y_true, y_pred, average='weighted')
    metrics['recall_macro'] = recall_score(y_true, y_pred, average='macro')
    metrics['recall_weighted'] = recall_score(y_true, y_pred, average='weighted')

    # Multi-class specific metrics
    if y_pred_proba is not None:
        try:
            # Multi-class AUC (one-vs-rest)
            metrics['auc_ovr'] = roc_auc_score(y_true, y_pred_proba, multi_class='ovr', average='macro')
            metrics['auc_ovo'] = roc_auc_score(y_true, y_pred_proba, multi_class='ovo', average='macro')
        except:
            metrics['auc_ovr'] = 0.0
            metrics['auc_ovo'] = 0.0

        # Log loss
        try:
            metrics['log_loss'] = log_loss(y_true, y_pred_proba)
        except:
            metrics['log_loss'] = float('inf')

    # Confusion matrix
    metrics['confusion_matrix'] = confusion_matrix(y_true, y_pred)

    # Per-class metrics
    class_report = classification_report(y_true, y_pred, output_dict=True)
    for class_id in np.unique(y_true):
        class_name = CLASS_MAPPING.get(class_id, f"class_{class_id}")
        if str(class_id) in class_report:
            metrics[f'{class_name}_precision'] = class_report[str(class_id)]['precision']
            metrics[f'{class_name}_recall'] = class_report[str(class_id)]['recall']
            metrics[f'{class_name}_f1'] = class_report[str(class_id)]['f1-score']

    return metrics

"""สร้าง target variable จากข้อมูลการเทรด"""
def process_trade_targets(df):
    print(f"\n🏗️ เปิดใช้งาน process trade targets") if Steps_to_do else None

    """สร้าง target variable โดยจัดการกรณีไม่มีข้อมูล """
    if df.empty or len(df) < 10:
        print("⚠️ ไม่มีข้อมูล เพียงพอสำหรับสร้าง Target")
        return df

    try:
        # ตรวจสอบคอลัมน์ที่จำเป็น
        required_cols = ['Profit', 'Exit Condition', 'Risk', 'Reward', 'Trade Type']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"⚠️ ไม่พบ คอลัมน์ที่จำเป็น: {missing_cols}")
            return df

        # แปลงคอลัมน์ Profit เป็นตัวเลข
        if not pd.api.types.is_numeric_dtype(df["Profit"]):
            df["Profit"] = pd.to_numeric(df["Profit"], errors='coerce')
            df = df.dropna(subset=["Profit"])

        # ======================================================
        # วิธีที่ 1: สร้าง Target จาก Risk/Reward Ratio
        # ======================================================
        df['RR_Ratio'] = df['Reward'] / df['Risk']

        # ======================================================
        # วิธีที่ 2: สร้าง Target หลักแบบเดิม (Binary)
        # 1 = TP Hit (Win), 0 = SL Hit หรือ Technical Exit (Loss)
        # ======================================================
        conditions_main = [
            (df['Exit Condition'] == 'TP Hit')
        ]
        df['Target'] = np.select(conditions_main, [1], default=0)
        df['Main_Target'] = df['Target']  # เก็บค่าเดิมไว้

        # ======================================================
        # วิธีที่ 3: สร้าง Target แยกสำหรับ Buy และ Sell
        # ======================================================
        
        # Target สำหรับ Buy (1=ชนะ, 0=แพ้, -1=ไม่ใช่ Buy Trade)
        conditions_buy = [
            (df['Trade Type'] == 'Buy') & (df['Exit Condition'] == 'TP Hit'),  # ชนะ
            (df['Trade Type'] == 'Buy') & (df['Exit Condition'] != 'TP Hit')   # แพ้
        ]
        choices_buy = [1, 0]
        df['Target_Buy'] = np.select(conditions_buy, choices_buy, default=-1)  # Default สำหรับไม่ใช่ Buy Trade

        # Target สำหรับ Sell (1=ชนะ, 0=แพ้, -1=ไม่ใช่ Sell Trade)
        conditions_sell = [
            (df['Trade Type'] == 'Sell') & (df['Exit Condition'] == 'TP Hit'),  # ชนะ
            (df['Trade Type'] == 'Sell') & (df['Exit Condition'] != 'TP Hit')   # แพ้
        ]
        choices_sell = [1, 0]
        df['Target_Sell'] = np.select(conditions_sell, choices_sell, default=-1)  # Default สำหรับไม่ใช่ Sell Trade

        # ======================================================
        # วิธีที่ 4: สร้าง Multi-class Target จาก Profit Threshold
        # ======================================================
        if USE_MULTICLASS_TARGET:
            df['Target_Multiclass'] = create_multiclass_target(df['Profit'])
            print(f"\n📊 Multi-class Target Distribution:")
            print(df['Target_Multiclass'].value_counts().sort_index())

            # แสดงรายละเอียดแต่ละ class
            for class_id, class_name in CLASS_MAPPING.items():
                count = (df['Target_Multiclass'] == class_id).sum()
                profit_range = df[df['Target_Multiclass'] == class_id]['Profit']
                if len(profit_range) > 0:
                    print(f"Class {class_id} ({class_name}): {count} trades, "
                          f"Profit range: {profit_range.min():.1f} to {profit_range.max():.1f}")

        # ======================================================
        # ตรวจสอบและทำความสะอาดข้อมูล
        # ======================================================
        valid_trades = df[
            df['Main_Target'].isin([0, 1]) & 
            df['Target_Buy'].isin([-1, 0, 1]) & 
            df['Target_Sell'].isin([-1, 0, 1])
        ].copy()

        print("\n📊 การกระจายของ Target ต่างๆ:")
        print("1. Target หลัก (Binary):")
        print(valid_trades['Main_Target'].value_counts())
        
        print("\n2. Target สำหรับ Buy Trades:")
        print(valid_trades[valid_trades['Target_Buy'] != -1]['Target_Buy'].value_counts())
        
        print("\n3. Target สำหรับ Sell Trades:")
        print(valid_trades[valid_trades['Target_Sell'] != -1]['Target_Sell'].value_counts())

        return valid_trades

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะสร้าง Target: {str(e)}")
        traceback.print_exc()
        return df

"""เลือกฟีเจอร์ที่สำคัญสำหรับโมเดล โดยดูจาก correlation กับ target และ multicollinearity"""
def select_features(trade_df, timeframe):
    print(f"\n🏗️ เปิดใช้งาน select features") if Steps_to_do else None

    # 1. กำหนดรายชื่อ features ที่อาจมีประโยชน์ (จากลักษณะทางเทคนิคและเวลา)
    # รายการนี้ควรรวมคอลัมน์ทั้งหมดใน trade_df ที่คุณคิดว่าเป็น potential feature
    potential_features_list = []

    # เพิ่ม Time Features (ที่คำนวณจาก Entry_DateTime)
    add_if_exists(potential_features_list, trade_df, "Entry_DayOfWeek")
    add_if_exists(potential_features_list, trade_df, "Entry_Hour")
    add_if_exists(potential_features_list, trade_df, "IsWeekend")
    add_if_exists(potential_features_list, trade_df, "IsMorning")
    add_if_exists(potential_features_list, trade_df, "IsAfternoon")
    add_if_exists(potential_features_list, trade_df, "IsEvening")
    add_if_exists(potential_features_list, trade_df, "IsNight")

    # เพิ่ม Price Action Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "Bar_CL")
    add_if_exists(potential_features_list, trade_df, "Bar_CL_OC")
    add_if_exists(potential_features_list, trade_df, "Bar_CL_HL")
    add_if_exists(potential_features_list, trade_df, "Bar_SW")
    add_if_exists(potential_features_list, trade_df, "Bar_TL")
    add_if_exists(potential_features_list, trade_df, "Bar_DTB")
    add_if_exists(potential_features_list, trade_df, "Bar_OSB")
    add_if_exists(potential_features_list, trade_df, "Bar_FVG")
    add_if_exists(potential_features_list, trade_df, "Bar_longwick")

    # เพิ่ม Price Info Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "Price_Range")
    add_if_exists(potential_features_list, trade_df, "Price_Move")
    add_if_exists(potential_features_list, trade_df, "Price_Strangth")

    # เพิ่ม Volume Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "Volume_MA20")
    add_if_exists(potential_features_list, trade_df, "Volume_Spike")

    # เพิ่ม EMA Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "EMA_diff")
    add_if_exists(potential_features_list, trade_df, "MA_Cross")
    add_if_exists(potential_features_list, trade_df, "Price_above_EMA50")
    add_if_exists(potential_features_list, trade_df, "Dist_EMA50")
    add_if_exists(potential_features_list, trade_df, "Dist_EMA100")
    add_if_exists(potential_features_list, trade_df, "Dist_EMA200")

    # เพิ่ม Volatility Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "Rolling_Vol_5")
    add_if_exists(potential_features_list, trade_df, "Rolling_Vol_15")

    # เพิ่ม RSI Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "RSI_signal")
    add_if_exists(potential_features_list, trade_df, "RSI_Overbought")
    add_if_exists(potential_features_list, trade_df, "RSI_Oversold")
    add_if_exists(potential_features_list, trade_df, "RSI_ROC_i2")
    add_if_exists(potential_features_list, trade_df, "RSI_ROC_i4")
    add_if_exists(potential_features_list, trade_df, "RSI_ROC_i6")
    add_if_exists(potential_features_list, trade_df, "RSI_ROC_i8")
    add_if_exists(potential_features_list, trade_df, "RSI_Divergence_i2")
    add_if_exists(potential_features_list, trade_df, "RSI_Divergence_i4")
    add_if_exists(potential_features_list, trade_df, "RSI_Divergence_i6")

    # เพิ่ม MACD Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "MACDh_12_26_9")
    add_if_exists(potential_features_list, trade_df, "MACD_line")
    add_if_exists(potential_features_list, trade_df, "MACD_deep")
    add_if_exists(potential_features_list, trade_df, "MACD_signal")

    # เพิ่ม Stochastic Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "STO_cross")
    add_if_exists(potential_features_list, trade_df, "STO_zone")
    add_if_exists(potential_features_list, trade_df, "STO_overbought")
    add_if_exists(potential_features_list, trade_df, "STO_Oversold")

    # เพิ่ม ADX Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "ADX_Deep")
    add_if_exists(potential_features_list, trade_df, "ADX_zone_15")
    add_if_exists(potential_features_list, trade_df, "ADX_zone_25")
    add_if_exists(potential_features_list, trade_df, "ADX_cross")

    # เพิ่ม ATR Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "ATR_Deep")
    add_if_exists(potential_features_list, trade_df, "ATR_ROC_i2")
    add_if_exists(potential_features_list, trade_df, "ATR_ROC_i4")
    add_if_exists(potential_features_list, trade_df, "ATR_ROC_i6")
    add_if_exists(potential_features_list, trade_df, "ATR_ROC_i8")

    # เพิ่ม BB Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "BB_width")

    # เพิ่ม Support/Resistance (จาก merge ถ้าต้องการใช้)
    add_if_exists(potential_features_list, trade_df, "PullBack_Up")
    add_if_exists(potential_features_list, trade_df, "PullBack_Down")

    add_if_exists(potential_features_list, trade_df, "Ratio_Buy")
    add_if_exists(potential_features_list, trade_df, "Ratio_Sell")

    # Interaction Features
    add_if_exists(potential_features_list, trade_df, "RSI_x_VolumeSpike") 
    add_if_exists(potential_features_list, trade_df, "EMA_diff_x_ATR") 
    add_if_exists(potential_features_list, trade_df, "Momentum5_x_Volatility10") 
    add_if_exists(potential_features_list, trade_df, "RSI14_x_BBwidth") 
    add_if_exists(potential_features_list, trade_df, "MACD_signal_x_ADX") 
    add_if_exists(potential_features_list, trade_df, "Price_above_EMA50_x_RSI_signal") 
    add_if_exists(potential_features_list, trade_df, "RSI14_x_ATR")     
    add_if_exists(potential_features_list, trade_df, "RSI14_x_PriceMove") 
    add_if_exists(potential_features_list, trade_df, "EMA50_x_RollingVol5") 
    add_if_exists(potential_features_list, trade_df, "EMA_diff_x_BBwidth") 
    add_if_exists(potential_features_list, trade_df, "ADX_14_x_ATR") 
    add_if_exists(potential_features_list, trade_df, "MACD_signal_x_RSI14") 
    add_if_exists(potential_features_list, trade_df, "RSI14_x_StochK") 
    add_if_exists(potential_features_list, trade_df, "RSI14_x_StochD") 
    add_if_exists(potential_features_list, trade_df, "MACD_line_x_PriceMove") 
    add_if_exists(potential_features_list, trade_df, "ADX_14_x_RollingVol15") 
    add_if_exists(potential_features_list, trade_df, "RSI14_x_Volume") 
    add_if_exists(potential_features_list, trade_df, "ATR_x_PriceRange") 
    add_if_exists(potential_features_list, trade_df, "RSI14_x_PullBack_Up") 
    add_if_exists(potential_features_list, trade_df, "RSI14_x_PullBack_Down") 
    add_if_exists(potential_features_list, trade_df, "EMA_diff_x_RSI14") 
    add_if_exists(potential_features_list, trade_df, "ADX_14_x_BBwidth") 

    # เพิ่ม Lag Features ในรายการ potential features
    lag_features = [col for col in trade_df.columns if any(x in col for x in ['_Lag_', '_Return_', '_Change_', '_MA_', '_Std_'])]

    # # 2. กำหนดรายชื่อ features ที่เกี่ยวข้องกับ SL/TP (ที่คำนวณบน trade_df)
    # stop_loss_take_profit_features = [
    #     'Risk', 'Reward', 'RR_Ratio', # ต้องแน่ใจว่าคำนวณบน trade_df แล้ว
    #     'Pct_Risk', 'Pct_Reward'    # ต้องแน่ใจว่าคำนวณบน trade_df แล้ว
    #     # ATR, BB_width อยู่ในกลุ่ม Technical Features ที่ merge มาแล้ว
    # ]

    # all_potential_features = potential_features_list + [
    #     f for f in stop_loss_take_profit_features if f in trade_df.columns
    # ]

    # นำ features ทั้งสองกลุ่มมารวมกัน
    all_potential_features = potential_features_list + lag_features # การตรวจสอบเบื้องต้นถ้าใช้ stop_loss_take_profit_features อาจส่งผลกระทบ

    print(f"ℹ️ Potential features for model input (Pre-Trade Only): {potential_features_list}+{lag_features} = {len(all_potential_features)} features considered.")
    
    # 3. เลือกเฉพาะ features ที่มีอยู่ใน DataFrame และเป็นตัวเลข
    numeric_columns = trade_df.select_dtypes(include=['number']).columns.tolist()
    # กรองอีกครั้งเพื่อให้แน่ใจว่ามีอยู่จริงและเป็นตัวเลข
    available_numeric_features = [
        f for f in all_potential_features if f in numeric_columns
    ]

    # 4. ตรวจสอบว่ามีคอลัมน์ Target หรือไม่
    if 'Target' not in trade_df.columns:
        print("⚠️ ไม่พบ คอลัมน์ Target ในข้อมูล ใช้ features ที่มีทั้งหมดที่เป็นตัวเลข")
        return available_numeric_features

    # 5. คำนวณความสัมพันธ์กับ Target
    print("\n🔍 ความสัมพันธ์กับ Target (ทั้งหมด):")
    # เลือกเฉพาะคอลัมน์ที่เป็น available_numeric_features และ Target ก่อนคำนวณ corr เพื่อป้องกัน error
    cols_for_corr = [f for f in available_numeric_features if f in trade_df.columns] + ['Target']
    if 'Target' in trade_df.columns and all(col in trade_df.columns for col in available_numeric_features):
        correlation_with_target = trade_df[cols_for_corr].corr()['Target'].abs().sort_values(ascending=False)
        print(correlation_with_target)
    else:
        print("ไม่สามารถคำนวณความสัมพันธ์กับ Target ได้ เนื่องจากมีคอลัมน์ไม่ครบถ้วน")
        # ในกรณีนี้ อาจจะ return available_numeric_features ไปก่อน หรือหยุดการทำงาน
        return available_numeric_features # หรือ raise Error

    # 6. กำหนดค่า threshold สำหรับความสัมพันธ์ และเลือก features ที่มีความสัมพันธ์สูงกว่า
    correlation_threshold = 0.05
    high_correlation_threshold_for_multicollinearity = 0.8 # ปรับ threshold สำหรับ multicollinearity ให้สูงขึ้นเล็กน้อย

    # เลือก features ที่มีความสัมพันธ์กับ Target สูงกว่า threshold (ไม่รวม Target ตัวเอง)
    highly_correlated_with_target = correlation_with_target[
        (correlation_with_target > correlation_threshold) &
        (correlation_with_target.index != 'Target')
    ].index.tolist()

    # 7. ตรวจสอบ multicollinearity (ความสัมพันธ์สูงระหว่าง features กันเอง)
    # ตรวจสอบให้แน่ใจว่า highly_correlated_with_target ไม่ว่างเปล่าก่อนสร้าง correlation_matrix
    if len(highly_correlated_with_target) > 1:
        correlation_matrix = trade_df[highly_correlated_with_target].corr().abs()
        # ใช้ NumPy ในการสร้าง upper triangle mask
        upper_triangle = correlation_matrix.where(np.triu(np.ones(correlation_matrix.shape), k=1).astype(bool))

        # หา features ที่มีความสัมพันธ์สูงกับ feature อื่นๆ (เก็บชื่อคอลัมน์ที่ต้องการ drop)
        # จะ drop คอลัมน์ที่ 'any' ค่าในคอลัมน์นั้นใน upper_triangle สูงกว่า threshold
        features_to_drop_due_to_multicollinearity = [
            col for col in upper_triangle.columns if any(upper_triangle[col] > high_correlation_threshold_for_multicollinearity)
        ]

        # 8. สร้างรายชื่อ features สุดท้าย โดยตัด features ที่มีความสัมพันธ์กันเองสูงเกินไปออก
        final_selected_features = [
            f for f in highly_correlated_with_target if f not in features_to_drop_due_to_multicollinearity
        ]

        # แสดง features ที่ถูกตัดออกเนื่องจาก Multicollinearity
        if features_to_drop_due_to_multicollinearity:
            print("\n🚫 Features ถูกตัดออกเนื่องจาก Multicollinearity สูง:", features_to_drop_due_to_multicollinearity)

    else:
        # กรณีที่มี highly_correlated_with_target แค่ 0 หรือ 1 ตัว ไม่ต้องทำ multicollinearity check
        print("\nℹ️ มี Features ที่มีความสัมพันธ์กับ Target ต่ำกว่าหรือเท่ากับ 1 ตัว ไม่ต้องตรวจสอบ Multicollinearity.")
        final_selected_features = highly_correlated_with_target

    # 9. กำหนด features ที่จำเป็นต้องมี และเพิ่มเข้าไปในรายชื่อ หากยังไม่มี
    # ควรใช้ชื่อคอลัมน์ที่ถูกต้องหลังจาก merge และคำนวณแล้ว
    # กำหนด features ที่จำเป็นต้องมี (add_features_in_model) ---
    print("\ืเริ่มขั้นตอนการตรวจสอบ features ที่ต้องใช้จากการทำ analyze cross asset feature importance")
    add_features_in_model = ['RSI_ROC_i2', 'ADX_Deep']

    # ตรวจสอบว่ามีไฟล์ pickle อยู่หรือไม่ ถ้ามี ให้โหลดจากไฟล์ ถ้าไม่มี ให้ใช้ hardcode list
    must_have_pickle_path = os.path.join('Test_LightGBM', 'feature_importance', f'{str(timeframe).zfill(3)}_must_have_features.pkl')
    must_have_features_in_model = []
    if os.path.exists(must_have_pickle_path):
        try:
            with open(must_have_pickle_path, 'rb') as f: # ใช้ 'rb' สำหรับ binary read
                must_have_features_in_model = pickle.load(f)
            print(f"\n👍 โหลดรายชื่อ Features ที่จำเป็นจากไฟล์: {must_have_pickle_path} ({len(must_have_features_in_model)} Features)")
        except Exception as e:
            print(f"⚠️ เกิดข้อผิดพลาด ในการโหลดไฟล์ Features ที่จำเป็น {must_have_pickle_path}: {e}")
            # ถ้าโหลดไม่ได้ ให้ใช้ hardcode list แทน
            print("ℹ️ ใช้ Hardcoded must_have_features_in_model เป็นค่าเริ่มต้น")
            must_have_features_in_model = add_features_in_model
    else:
        print(f"\nℹ️ ⚠️ ไม่พบไฟล์ Features ที่จำเป็น '{must_have_pickle_path}'. จะใช้ Hardcoded must_have_features_in_model เป็นค่าเริ่มต้น")
        # Hardcoded list เป็นค่าเริ่มต้นเมื่อ⚠️ ไม่พบไฟล์
        must_have_features_in_model = add_features_in_model
    
    print("\nfeaturesasset_feature_importance : len ",len(must_have_features_in_model))
    print(must_have_features_in_model)

    for feature in must_have_features_in_model:
        # เพิ่มเฉพาะ feature ที่มีอยู่จริงใน numeric pre-trade features และยังไม่มีใน final list
        if feature not in final_selected_features and feature in available_numeric_features:
            final_selected_features.append(feature)
            print(f"👍 เพิ่ม Feature ที่จำเป็น '{feature}' เข้าไปในรายการ")
        elif feature not in available_numeric_features:
            print(f"⚠️ Feature ที่จำเป็น '{feature}' ⚠️ ไม่พบ ในข้อมูลตัวเลขที่มีอยู่")

    # 10. ตรวจสอบและนำ 'Target' ออกจากรายชื่อ features สุดท้าย (ถ้ามี)
    if 'Target' in final_selected_features:
        final_selected_features.remove('Target')
        print("\n✅ นำคอลัมน์ 'Target' ออกจากรายชื่อ Features แล้ว")

    # 11. แสดง Features สุดท้ายที่เลือกได้
    if not final_selected_features:
        print("\n⚠️ ไม่สามารถเลือก Features ใดๆ ได้จากกระบวนการคัดเลือกอัตโนมัติ")
        print("ℹ️ สาเหตุอาจมาจาก:")
        print("- ความสัมพันธ์กับ Target ต่ำกว่า Threshold")
        print("- มี Multicollinearity สูงมาก")
        print("- จำนวนข้อมูลน้อยเกินไปที่จะคำนวณความสัมพันธ์ได้อย่างแม่นยำ")
        # อาจจะ return available_numeric_features ทั้งหมด หรือ [] ขึ้นอยู่กับว่าต้องการให้โมเดลทำงานอย่างไร
        return [] # หรือ available_numeric_features

    # print(f"\n✅ Final selected features for training: {final_selected_features}")
    # print(f"\n✅ Final selected features for training:")
    # for i, feat in enumerate(final_selected_features, 1):
    #     print(f"{i}. {feat}")

    return final_selected_features

def create_market_regime_features(df):
    """สร้าง Market Regime Detection Features (เพิ่ม 2025-07-03)"""
    print("🔍 สร้าง Market Regime Features...")

    try:
        # 1. Trend Strength (ADX-based)
        if 'High' in df.columns and 'Low' in df.columns and 'Close' in df.columns:
            df['ADX_14'] = talib.ADX(df['High'], df['Low'], df['Close'], timeperiod=14)
            df['Trend_Strength_Score'] = pd.cut(df['ADX_14'],
                                               bins=[0, 25, 50, 100],
                                               labels=[1, 2, 3]).astype(float)

        # 2. Market Volatility Regime
        if 'High' in df.columns and 'Low' in df.columns and 'Close' in df.columns:
            df['ATR_14'] = talib.ATR(df['High'], df['Low'], df['Close'], timeperiod=14)
            df['ATR_MA_50'] = df['ATR_14'].rolling(50).mean()
            df['Volatility_Regime_Score'] = np.where(df['ATR_14'] > df['ATR_MA_50'] * 1.5, 3,
                                            np.where(df['ATR_14'] < df['ATR_MA_50'] * 0.7, 1, 2))

        # 3. Price Position in Range
        if 'High' in df.columns and 'Low' in df.columns and 'Close' in df.columns:
            df['High_20'] = df['High'].rolling(20).max()
            df['Low_20'] = df['Low'].rolling(20).min()
            df['Price_Position'] = (df['Close'] - df['Low_20']) / (df['High_20'] - df['Low_20'])

        # 4. Trend Direction Consensus
        if 'Close' in df.columns:
            trend_scores = []
            for period in [10, 20, 50]:
                df[f'SMA_{period}'] = df['Close'].rolling(period).mean()
                trend_score = np.where(df['Close'] > df[f'SMA_{period}'], 1,
                              np.where(df['Close'] < df[f'SMA_{period}'], -1, 0))
                trend_scores.append(trend_score)

            df['Trend_Consensus'] = np.sum(trend_scores, axis=0)

        print(f"  ✅ เพิ่ม Market Regime Features สำเร็จ")

    except Exception as e:
        print(f"  ⚠️ เกิดข้อผิดพลาดในการสร้าง Market Regime Features: {str(e)}")

    return df

"""เพิ่มชื่อคอลัมน์ใน list ถ้าคอลัมน์นั้นมีอยู่ใน DataFrame"""
def add_if_exists(features_list, df, column_name):
    # print(f"\n🏗️ เปิดใช้งาน add if exists") if Steps_to_do else None

    if column_name in df.columns:
        features_list.append(column_name)
    else:
        print(f"⚠️ Feature '{column_name}' not found in DataFrame. Skipping.")
    return features_list

"""แปลงข้อมูล numpy/pandas ให้เป็นชนิดที่ serialize เป็น JSON ได้"""
def safe_json_serialize(obj):
    # print(f"\n🏗️ เปิดใช้งาน safe json serialize") if Steps_to_do else None
    
    if isinstance(obj, (np.int_, np.intc, np.intp, np.int8,
                    np.int16, np.int32, np.int64, np.uint8,
                    np.uint16, np.uint32, np.uint64)):
        return int(obj)
    elif isinstance(obj, (np.float_, np.float16, np.float32, np.float64)):
        return float(obj)
    elif isinstance(obj, (np.bool_)):
        return bool(obj)
    elif isinstance(obj, (np.ndarray)):
        return obj.tolist()
    elif isinstance(obj, (pd.Timestamp)):
        return obj.isoformat()
    else:
        return str(obj)

"""แยกข้อมูล symbol, timeframe, ฯลฯ จากชื่อไฟล์"""
def parse_filename(name):
    # รองรับทั้งรูปแบบเดิมและรูปแบบใหม่
    if "CSV_Files_Fixed/" in name:
        # รูปแบบใหม่: CSV_Files_Fixed/SYMBOL_TIMEFRAME_FIXED.csv
        filename = name.split("/")[-1]  # เอาเฉพาะชื่อไฟล์
        parts = filename.split("_")
        name_currency = parts[0]  # SYMBOL
        timeframe_str = parts[1]  # M30 หรือ H1
    else:
        # รูปแบบเดิม: SYMBOL#_TIMEFRAME_...csv
        parts = name.split("_")
        pair_tf = parts[0]
        timeframe_str = parts[1]
        name_currency = pair_tf.replace("#", "")

    if name_currency == "GOLD":
        base_currency = "GOLD"
        quote_currency = "USD"
    else:
        base_currency = name_currency[:3]
        quote_currency = name_currency[3:]

    tf_value = timeframe_map.get(timeframe_str, None)
    if tf_value is None:
        raise ValueError(f"Unknown timeframe: {timeframe_str}")

    info = symbol_info.get(name_currency)
    if info is None:
        raise ValueError(f"Unknown symbol info for: {name_currency}")

    result = {
        "Base_Currency": base_currency,
        "Quote_Currency": quote_currency,
        "Name_Currency": name_currency,
        "Timeframe_Currency": tf_value,
        "Spread": info["Spread"],
        "Digits": info["Digits"],
        "Points": info["Points"],
        "Swap_Long": info["Swap_Long"],
        "Swap_Short": info["Swap_Short"],
    }

    return result

"""ปัดเศษราคาขึ้น/ลงตามจำนวน digits ที่กำหนด"""
def ceiling_price(value, digits):
    return math.ceil(value/digits) * digits

"""ปัดเศษราคาขึ้น/ลงตามจำนวน digits ที่กำหนด"""
def floor_price(value, digits):
    return math.floor(value/digits) * digits

def is_high_quality_entry(df, i, symbol="GOLD"):
    """
    ตรวจสอบว่าเป็น high-quality entry หรือไม่ เพื่อเพิ่ม win rate

    เงื่อนไข:
    1. ATR สูงพอ (volatility เพียงพอ)
    2. Volume สูงกว่าค่าเฉลี่ย
    3. Trend confirmation (EMA alignment)
    4. RSI ไม่อยู่ใน neutral zone
    5. MACD confirmation
    """
    try:
        if i < TREND_CONFIRMATION_PERIODS:
            return False

        # 1. ตรวจสอบ ATR - ต้องมี volatility เพียงพอ
        current_atr = df['ATR'].iloc[i-1] if 'ATR' in df.columns else 0
        if current_atr < MIN_ATR_THRESHOLD:
            return False

        # 2. ตรวจสอบ Volume (ถ้ามี)
        if 'Volume' in df.columns:
            current_volume = df['Volume'].iloc[i-1]
            avg_volume = df['Volume'].iloc[max(0, i-20):i].mean()
            if current_volume < avg_volume * MIN_VOLUME_MULTIPLIER:
                return False

        # 3. Trend Confirmation - EMA alignment
        if 'EMA50' in df.columns and 'EMA20' in df.columns:
            ema20_current = df['EMA20'].iloc[i-1]
            ema50_current = df['EMA50'].iloc[i-1]
            close_current = df['Close'].iloc[i-1]

            # สำหรับ Buy: Close > EMA20 > EMA50 (uptrend)
            # สำหรับ Sell: Close < EMA20 < EMA50 (downtrend)
            uptrend = close_current > ema20_current > ema50_current
            downtrend = close_current < ema20_current < ema50_current

            if not (uptrend or downtrend):
                return False

        # 4. RSI ไม่อยู่ใน neutral zone (40-60)
        if 'RSI14' in df.columns:
            rsi = df['RSI14'].iloc[i-1]
            if 40 <= rsi <= 60:  # neutral zone - หลีกเลี่ยง
                return False

        # 5. MACD Confirmation
        if 'MACD' in df.columns and 'MACD_signal' in df.columns:
            macd = df['MACD'].iloc[i-1]
            macd_signal = df['MACD_signal'].iloc[i-1]

            # ต้องมี MACD divergence ที่ชัดเจน
            macd_diff = abs(macd - macd_signal)
            if macd_diff < 0.0001:  # MACD และ Signal ใกล้กันเกินไป
                return False

        return True

    except Exception as e:
        print(f"⚠️ Error in is_high_quality_entry: {e}")
        return False

def debug_train_and_evaluate_failure(file_path, symbol, timeframe):
    """
    ตรวจสอบสาเหตุที่ train_and_evaluate ล้มเหลวสำหรับไฟล์เฉพาะ
    """
    print(f"\n🔍 Debug: ตรวจสอบปัญหา {symbol} {timeframe}")
    print("="*60)

    try:
        # 1. ตรวจสอบไฟล์
        if not os.path.exists(file_path):
            print(f"❌ ไฟล์ไม่พบ: {file_path}")
            return "FILE_NOT_FOUND"

        # 2. ตรวจสอบขนาดไฟล์
        file_size = os.path.getsize(file_path)
        print(f"📁 ขนาดไฟล์: {file_size:,} bytes")

        if file_size < 1000:
            print(f"❌ ไฟล์เล็กเกินไป")
            return "FILE_TOO_SMALL"

        # 3. ตรวจสอบการโหลดข้อมูล
        try:
            df = pd.read_csv(file_path)
            print(f"📊 จำนวนแถว: {len(df):,}")
            print(f"📊 จำนวนคอลัมน์: {len(df.columns)}")
            print(f"📊 คอลัมน์: {list(df.columns)}")

            if len(df) < 1000:
                print(f"❌ ข้อมูลน้อยเกินไป: {len(df)} แถว (ต้องการ >= 1000)")
                return "INSUFFICIENT_DATA"

        except Exception as e:
            print(f"❌ ไม่สามารถอ่านไฟล์ CSV: {e}")
            return "CSV_READ_ERROR"

        # 4. ทดสอบ load_and_process_data
        print(f"\n🧪 ทดสอบ load_and_process_data...")
        try:
            result = load_and_process_data(
                file_path, "LightGBM", symbol, timeframe,
                "debug", None, None, 6, 0.5
            )

            if result is None or any(x is None for x in result):
                print(f"❌ load_and_process_data คืนค่า None")
                return "LOAD_PROCESS_FAILED"

            train_data, val_data, test_data, df, trade_df, stats = result

            if train_data is None or val_data is None or test_data is None:
                print(f"❌ ข้อมูล train/val/test เป็น None")
                return "DATA_SPLIT_FAILED"

            X_train, y_train = train_data
            X_val, y_val = val_data
            X_test, y_test = test_data

            print(f"✅ Train: {len(X_train)} samples")
            print(f"✅ Val: {len(X_val)} samples")
            print(f"✅ Test: {len(X_test)} samples")

            # 5. ตรวจสอบ class distribution
            print(f"\n📊 Class Distribution:")
            print(f"Train: {pd.Series(y_train).value_counts().to_dict()}")
            print(f"Val: {pd.Series(y_val).value_counts().to_dict()}")
            print(f"Test: {pd.Series(y_test).value_counts().to_dict()}")

            # 6. ตรวจสอบ features
            print(f"\n🔍 Features:")
            print(f"จำนวน features: {len(X_train.columns)}")
            print(f"Features ที่มี NaN: {X_train.isnull().sum().sum()}")

            if X_train.empty or X_val.empty or X_test.empty:
                print(f"❌ ข้อมูล features ว่างเปล่า")
                return "EMPTY_FEATURES"

            # 7. ตรวจสอบ class imbalance
            train_counts = pd.Series(y_train).value_counts()
            if len(train_counts) < 2:
                print(f"❌ มีเพียงคลาสเดียวใน training data")
                return "SINGLE_CLASS"

            ratio = train_counts.max() / train_counts.min()
            print(f"📊 Class imbalance ratio: {ratio:.1f}:1")

            if ratio > 50:
                print(f"⚠️ Class imbalance สูงมาก")
                return "EXTREME_IMBALANCE"

            print(f"✅ ข้อมูลผ่านการตรวจสอบเบื้องต้น")
            return "OK"

        except Exception as e:
            print(f"❌ เกิดข้อผิดพลาดใน load_and_process_data: {e}")
            import traceback
            traceback.print_exc()
            return "LOAD_PROCESS_ERROR"

    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการ debug: {e}")
        import traceback
        traceback.print_exc()
        return "DEBUG_ERROR"

def validate_model_quality(model, X_val, y_val, symbol, timeframe):
    """
    ตรวจสอบคุณภาพของโมเดลก่อนใช้งาน
    คืนค่า True ถ้าโมเดลผ่านเกณฑ์ขั้นต่ำ
    """
    try:
        # ทำนายและคำนวณ metrics
        y_pred = model.predict(X_val)
        y_pred_proba = model.predict_proba(X_val)

        accuracy = accuracy_score(y_val, y_pred)

        # คำนวณ AUC ตาม classification type
        if len(np.unique(y_val)) > 2:  # multiclass
            auc = roc_auc_score(y_val, y_pred_proba, multi_class='ovr', average='macro')
        else:  # binary
            auc = roc_auc_score(y_val, y_pred_proba[:, 1])

        # ตรวจสอบเกณฑ์ขั้นต่ำ
        accuracy_pass = accuracy >= MIN_MODEL_ACCURACY
        auc_pass = auc >= MIN_MODEL_AUC

        # ตรวจสอบจำนวนตัวอย่าง
        total_samples = len(y_val)
        positive_samples = sum(y_val == 1) if len(np.unique(y_val)) == 2 else len(y_val)

        samples_pass = (total_samples >= MIN_TRAINING_SAMPLES and
                       positive_samples >= MIN_POSITIVE_SAMPLES)

        # สรุปผลการตรวจสอบ
        all_pass = accuracy_pass and auc_pass and samples_pass

        print(f"\n🔍 Model Quality Validation for {symbol} {timeframe}:")
        print(f"  📊 Accuracy: {accuracy:.3f} {'✅' if accuracy_pass else '❌'} (min: {MIN_MODEL_ACCURACY})")
        print(f"  📈 AUC: {auc:.3f} {'✅' if auc_pass else '❌'} (min: {MIN_MODEL_AUC})")
        print(f"  📋 Samples: {total_samples} {'✅' if samples_pass else '❌'} (min: {MIN_TRAINING_SAMPLES})")
        print(f"  🎯 Overall: {'✅ PASS' if all_pass else '❌ FAIL'}")

        if not all_pass:
            print(f"  ⚠️ Model quality insufficient - consider:")
            if not accuracy_pass:
                print(f"    - Improve feature engineering or model parameters")
            if not auc_pass:
                print(f"    - Address class imbalance or add more discriminative features")
            if not samples_pass:
                print(f"    - Collect more training data")

        return all_pass

    except Exception as e:
        print(f"⚠️ Error in model validation: {e}")
        return False

# ==============================================
# model_management.py
# ฟังก์ชันเกี่ยวกับการโหลด/บันทึกโมเดล, scaler, พารามิเตอร์โมเดล
# ==============================================

"""โหลดโมเดลที่บันทึกไว้จากไฟล์"""
def load_model(modelname, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน load model") if Steps_to_do else None

    model_dir = f"Test_LightGBM/models/{str(timeframe).zfill(3)}_{symbol}"
    model_path = os.path.join(model_dir, f"{modelname}_{str(timeframe).zfill(3)}_{symbol}_trained.pkl")
    features_path = os.path.join(model_dir, f"{modelname}_{str(timeframe).zfill(3)}_{symbol}_features.pkl")

    try:
        # ตรวจสอบว่าโฟลเดอร์และไฟล์มีอยู่จริง
        if not os.path.exists(model_dir):
            print(f"⚠️ ไม่พบ โฟลเดอร์โมเดลสำหรับ symbol {symbol} timeframe {timeframe}")
            return None
        
        if not os.path.exists(model_path):
            print(f"⚠️ ไม่พบไฟล์โมเดลที่ {model_path}")
            return None
        
        print(f"กำลังโหลดโมเดลจาก: {model_path}")
        model = joblib.load(model_path)

        # ตรวจสอบว่าเป็นโมเดลที่ใช้ predict ได้หรือไม่ (LGBMClassifier มี predict)
        if not hasattr(model, 'predict'):
            raise ValueError("ไฟล์ที่โหลดมาไม่ใช่โมเดลที่สามารถใช้ทำนายได้")
        
        loaded_features = None
        if os.path.exists(features_path):
            try:
                loaded_features = joblib.load(features_path)
                print(f"✅ โหลด features สำเร็จ (จำนวน {len(loaded_features)} features)")
            except Exception as e:
                print(f"⚠️ เกิดข้อผิดพลาด ขณะโหลดไฟล์ features: {str(e)}")
        else:
            print("⚠️ ไม่พบไฟล์ features")
            if hasattr(model, 'feature_name_') and model.feature_name_:
                loaded_features = model.feature_name_
                print(f"ℹ️ ใช้ feature names จากโมเดลที่โหลดมา (จำนวน {len(loaded_features)} features)")
            elif hasattr(model, 'feature_name') and model.feature_name(): # Fallback สำหรับ Booster หรือบางกรณี
                loaded_features = model.feature_name()
                print(f"ℹ️ ใช้ feature names จากเมธอด feature_name() ของโมเดลที่โหลดมา (จำนวน {len(loaded_features)} features)")
            else:
                print("⚠️ ไม่พบ feature names ในไฟล์หรือในโมเดลที่โหลดมา")

        num_trees_info = "N/A"
        if hasattr(model, 'n_estimators_'): # จำนวนรอบ boosting ที่ใช้จริงหลัง early stopping
            num_trees_info = f"{model.n_estimators_} (used)"
        elif hasattr(model, 'n_estimators'): # จำนวนรอบสูงสุดที่ตั้งไว้
            num_trees_info = f"{model.n_estimators} (total configured)"
        elif hasattr(model, 'num_trees'): # กรณีเป็น Booster (เพื่อความเข้ากันได้ย้อนหลังถ้าจำเป็น)
            num_trees_info = f"{model.num_trees()} (Booster API)"

        print(f"✅ โหลดโมเดลสำเร็จ (มี {num_trees_info} trees)")

        model.loaded_features_ = loaded_features # แนบ features เข้าไปกับ object โมเดลที่โหลดมา
        return model

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะโหลดโมเดล: {str(e)}")
        traceback.print_exc()
        return None

"""โหลด Scaler ที่บันทึกไว้จากไฟล์"""
def load_scaler(modelname, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน load scaler") if Steps_to_do else None

    """โหลด Scaler ที่บันทึกไว้ตาม timeframe และ model_name"""
    scaler_dir = f"Test_LightGBM/models/{str(timeframe).zfill(3)}_{symbol}"
    scaler_path = os.path.join(scaler_dir, f"{modelname}_{str(timeframe).zfill(3)}_{symbol}_scaler.pkl") # ใช้ model_name ด้วย

    try:
        if not os.path.exists(scaler_path):
            print(f"⚠️ ไม่พบไฟล์ Scaler ที่ {scaler_path}")
            return None
        print(f"กำลังโหลด Scaler จาก: {scaler_path}")
        scaler = joblib.load(scaler_path)
        # ตรวจสอบว่าเป็น Scaler จริงหรือไม่ (optional แต่ดี)
        if not hasattr(scaler, 'transform'):
            raise ValueError("ไฟล์ที่โหลดมาไม่ใช่ Scaler")
        print(f"✅ โหลด Scaler สำเร็จ")
        return scaler
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะโหลด Scaler: {str(e)}")
        traceback.print_exc()
        return None

def get_optimal_class_weight(y):
    """คำนวณ class weight ที่เหมาะสม (ปรับปรุง 2025-07-04 สำหรับ Critical Issues)"""
    class_counts = pd.Series(y).value_counts()
    minority_class = class_counts.idxmin()
    majority_class = class_counts.idxmax()

    ratio = class_counts[majority_class] / class_counts[minority_class]

    print(f"   Class distribution: {class_counts.to_dict()}")
    print(f"   Imbalance ratio: {ratio:.1f}:1")

    # ปรับปรุงเพื่อแก้ไข Critical Issues - เพิ่มน้ำหนักมากขึ้น
    if ratio > 15:  # Extreme imbalance (NZDUSD case)
        weight_ratio = 15
    elif ratio > 10:  # Very severe imbalance
        weight_ratio = 12
    elif ratio > 8:  # Severe imbalance
        weight_ratio = 10
    elif ratio > 5:  # Moderate-severe imbalance
        weight_ratio = 8
    elif ratio > 3:  # Moderate imbalance
        weight_ratio = 6
    elif ratio > 2:  # Mild imbalance
        weight_ratio = 3
    else:  # Balanced
        return "balanced"

    class_weight = {majority_class: 1, minority_class: weight_ratio}
    print(f"   Enhanced class weight: {class_weight}")

    return class_weight

def find_optimal_threshold(y_true, y_proba, metric='f1'):
    """หา threshold ที่เหมาะสมสำหรับ metric ที่กำหนด (เพิ่ม 2025-07-03)"""
    # ปรับ threshold range เพื่อเน้น high-quality signals
    thresholds = np.arange(0.5, 0.9, 0.01)
    scores = []

    for threshold in thresholds:
        y_pred = (y_proba >= threshold).astype(int)
        if metric == 'f1':
            score = f1_score(y_true, y_pred, zero_division=0)
        elif metric == 'precision':
            score = precision_score(y_true, y_pred, zero_division=0)
        elif metric == 'recall':
            score = recall_score(y_true, y_pred, zero_division=0)
        scores.append(score)

    optimal_idx = np.argmax(scores)
    return thresholds[optimal_idx], scores[optimal_idx]

"""คืนค่า parameter dictionary สำหรับ LightGBM (ปรับตาม class imbalance)"""
def get_lgbm_params(y=None, use_scale_pos_weight=True, symbol="GOLD", timeframe=30):
    print(f"\n🏗️ เปิดใช้งาน get lgbm params") if Steps_to_do else None

    """พารามิเตอร์ที่ปรับปรุงแล้วสำหรับ LightGBM (อัปเดต 2025-07-03)"""
    """ตาม reduce_overfitting.py - Conservative approach"""

    # ตรวจสอบว่าเป็น multi-class หรือ binary classification
    unique_classes = np.unique(y) if y is not None else []
    num_unique_classes = len(unique_classes)

    # ตรวจสอบเงื่อนไขสำหรับ multi-class
    is_multiclass = (USE_MULTICLASS_TARGET and
                    y is not None and
                    num_unique_classes > 2 and
                    num_unique_classes <= 10)  # จำกัดจำนวน class สูงสุด

    print(f"  📊 จำนวน unique classes: {num_unique_classes}")
    print(f"  📊 Classes: {unique_classes}")
    print(f"  🎯 USE_MULTICLASS_TARGET: {USE_MULTICLASS_TARGET}")
    print(f"  🎯 Is Multi-class: {is_multiclass}")

    if is_multiclass:
        num_classes = len(np.unique(y))
        print(f"  🎯 Multi-class Classification: {num_classes} classes")

        # ปรับปรุงพารามิเตอร์ตามการวิเคราะห์ Parameter Stability
        # ใช้ค่าเฉลี่ยจากการวิเคราะห์ 16 models เพื่อความเสถียร

        # กำหนดค่าตาม symbol type และ timeframe
        if 'GOLD' in symbol.upper():
            # Commodities: learning_rate ค่อนข้างสูง (Mean=0.03)
            base_lr = 0.03
            base_leaves = 20
        else:
            # Forex: learning_rate ปานกลาง (Mean=0.024)
            base_lr = 0.025
            base_leaves = 19

        # ปรับตาม timeframe
        if timeframe == 30:
            lr_multiplier = 1.1  # M30 ใช้ learning rate สูงกว่าเล็กน้อย
            leaves_adjustment = 1
        else:  # H1
            lr_multiplier = 0.9  # H1 ใช้ learning rate ต่ำกว่าเล็กน้อย
            leaves_adjustment = -1

        adaptive_lr = round(base_lr * lr_multiplier, 3)
        adaptive_leaves = max(10, base_leaves + leaves_adjustment)

        params = {
            'objective': 'multiclass',
            'num_class': num_classes,
            'metric': ['multi_logloss', 'multi_error'],
            'boosting_type': 'gbdt',
            'learning_rate': adaptive_lr,    # Adaptive based on analysis
            'num_leaves': adaptive_leaves,   # Adaptive based on analysis
            'max_depth': 6,                  # ค่าเฉลี่ยที่เสถียร (Mean=5.56, CV=47.8%)
            'min_data_in_leaf': 10,          # ค่าเฉลี่ยจากการวิเคราะห์ (Mean=9.69)
            'reg_alpha': 0.3,                # ลดลงเล็กน้อยเพื่อ performance
            'reg_lambda': 0.3,               # ลดลงเล็กน้อยเพื่อ performance
            'feature_fraction': 0.89,        # ค่าเฉลี่ยที่เสถียรดี (CV=10.0%)
            'bagging_fraction': 0.81,        # ค่าเฉลี่ยที่เสถียรดี (CV=15.5%)
            'bagging_freq': 5,
            'min_gain_to_split': 0.01,       # ลดลงเพื่อ performance
            'max_bin': 255,
            'verbosity': -1,
            'random_state': 42,
            'n_jobs': -1,
            'is_unbalance': True             # เพิ่มเพื่อจัดการ class imbalance
        }

        print(f"  🎯 Adaptive Parameters: lr={adaptive_lr}, leaves={adaptive_leaves} (Symbol: {symbol}, TF: {timeframe})")

        # สำหรับ multi-class ใช้ sample_weight แทน class_weight
        if y is not None:
            from sklearn.utils.class_weight import compute_class_weight
            classes = np.unique(y)
            class_weights = compute_class_weight('balanced', classes=classes, y=y)
            class_weight_dict = dict(zip(classes, class_weights))
            print(f"  🎯 Multi-class Class Weights: {class_weight_dict}")
            # หมายเหตุ: จะใช้ sample_weight ใน fit() แทน class_weight parameter

    else:
        # Binary classification (เดิม)
        class_weight = None
        if y is not None:
            class_weight = get_optimal_class_weight(y)
            print(f"  🎯 Auto Class Weight: {class_weight}")

        # ปรับพารามิเตอร์ตาม symbol เฉพาะ (อัปเดต 2025-07-05)
        # แก้ไขปัญหา USDJPY AUC=0.0 และ GBPUSD H1 Accuracy=56.5%

        # ตรวจสอบ symbol จาก global variable หรือ context
        current_symbol = globals().get('current_processing_symbol', 'UNKNOWN')

        if current_symbol == 'USDJPY':
            # พารามิเตอร์เฉพาะสำหรับ USDJPY (แก้ไข AUC=0.0)
            params = {
                'objective': 'binary',
                'metric': ['auc', 'binary_logloss'],
                'boosting_type': 'gbdt',
                'learning_rate': 0.01,   # ลดลงมากเพื่อ stable learning
                'num_leaves': 8,         # ลดลงเพื่อลด overfitting
                'max_depth': 4,          # ลดลงเพื่อ simple model
                'min_data_in_leaf': 30,  # เพิ่มขึ้นเพื่อ stable prediction
                'reg_alpha': 0.5,        # เพิ่มขึ้นเพื่อ regularization
                'reg_lambda': 0.5,       # เพิ่มขึ้นเพื่อ regularization
                'feature_fraction': 0.7, # ลดลงเพื่อ feature selection
                'bagging_fraction': 0.7, # ลดลงเพื่อ stable training
                'bagging_freq': 3,
                'min_gain_to_split': 0.02,  # เพิ่มขึ้นเพื่อ conservative split
                'max_bin': 128,          # ลดลงเพื่อ less complexity
                'verbosity': -1,
                'random_state': 42,
                'n_jobs': -1,
                'force_col_wise': True   # เพิ่มเพื่อ stability
            }
        elif current_symbol == 'GBPUSD':
            # พารามิเตอร์เฉพาะสำหรับ GBPUSD (แก้ไข Accuracy=56.5%)
            params = {
                'objective': 'binary',
                'metric': ['auc', 'binary_logloss'],
                'boosting_type': 'gbdt',
                'learning_rate': 0.02,   # ลดลงเพื่อ careful learning
                'num_leaves': 12,        # เพิ่มขึ้นเพื่อ capture pattern
                'max_depth': 6,          # เพิ่มขึ้นเพื่อ model capacity
                'min_data_in_leaf': 15,  # ลดลงเพื่อ minority class
                'reg_alpha': 0.3,        # moderate regularization
                'reg_lambda': 0.3,       # moderate regularization
                'feature_fraction': 0.8, # คงเดิม
                'bagging_fraction': 0.8, # คงเดิม
                'bagging_freq': 5,
                'min_gain_to_split': 0.01,
                'max_bin': 255,
                'verbosity': -1,
                'random_state': 42,
                'n_jobs': -1,
                'class_weight': 'balanced'  # เพิ่มเพื่อ handle imbalance
            }
        else:
            # พารามิเตอร์ทั่วไปสำหรับ symbol อื่นๆ
            params = {
                'objective': 'binary',
                'metric': ['auc', 'binary_logloss', 'binary_error'],
                'boosting_type': 'gbdt',
                'learning_rate': 0.05,   # ปรับจาก 0.08 → 0.05 เพื่อ stability
                'num_leaves': 10,        # คงเดิม
                'max_depth': 6,          # เพิ่มจาก 5 → 6
                'min_data_in_leaf': 15,  # ลดจาก 20 → 15
                'reg_alpha': 0.2,        # คงเดิม
                'reg_lambda': 0.2,       # คงเดิม
                'feature_fraction': 0.8, # คงเดิม
                'bagging_fraction': 0.8, # คงเดิม
                'bagging_freq': 5,
                'min_gain_to_split': 0.01,
                'max_bin': 255,
                'verbosity': -1,
                'random_state': 42,
                'n_jobs': -1
            }

        # เพิ่ม class_weight ถ้ามี
        if class_weight is not None:
            params['class_weight'] = class_weight

    return params

"""บันทึก/คำนวณ/ปรับค่า threshold ที่เหมาะสมสำหรับการตัดสินใจเข้าเทรดโดยโมเดล"""
def save_optimal_threshold(symbol, timeframe, threshold):
    print(f"\n🏗️ เปิดใช้งาน save optimal threshold") if Steps_to_do else None

    """บันทึกค่า threshold ที่เหมาะสมลงไฟล์"""
    try:
        os.makedirs("Test_LightGBM/thresholds", exist_ok=True) # Test_LightGBM/thresholds
        threshold_file = f"Test_LightGBM/thresholds/{symbol}_{timeframe}_optimal_threshold.pkl"
        if Save_File:
            with open(threshold_file, 'wb') as f:
                pickle.dump(threshold, f)
            print(f"✅ บันทึก threshold ที่: {threshold_file}")
        
        print(f"✅ บันทึกค่า threshold ที่เหมาะสมสำหรับ {symbol} {timeframe}: {threshold:.4f}")
    except Exception as e:
        print(f"⚠️ ไม่สามารถบันทึกค่า threshold: {e}")

def load_optimal_threshold(symbol, timeframe, initial_threshold=input_initial_threshold):
    print(f"\n🏗️ เปิดใช้งาน load optimal threshold") if Steps_to_do else None

    """คำนวณค่า threshold ที่เหมาะสมตามประวัติประสิทธิภาพของคู่เงินและ timeframe"""
    # กำหนด path สำหรับไฟล์เก็บค่า threshold
    threshold_file = f"Test_LightGBM/thresholds/{symbol}_{timeframe}_optimal_threshold.pkl"
    
    try:
        # พยายามโหลดค่า threshold ที่บันทึกไว้
        if os.path.exists(threshold_file):
            with open(threshold_file, 'rb') as f:
                optimal_threshold = pickle.load(f)
            print(f"✅ Loaded optimal threshold สำเร็จ {symbol} {timeframe}: {optimal_threshold:.4f}")
            return optimal_threshold
    except Exception as e:
        print(f"⚠️ ไม่สามารถโหลดค่า threshold จากไฟล์: {e}")
    
    # หากไม่มีไฟล์หรือโหลดไม่ได้ ให้ใช้ค่าเริ่มต้น
    print(f"⚠️ ใช้ค่า threshold เริ่มต้นสำหรับ {symbol} {timeframe}: {initial_threshold:.4f}")
    return initial_threshold

# ปรับ threshold range เพื่อเน้น high-quality signals (เพิ่ม win rate)
def find_best_threshold_on_val(model, scaler, val_df, model_features, thresholds=np.arange(0.35, 0.85, 0.05)):
    print(f"\n🏗️ เปิดใช้งาน find best threshold on val") if Steps_to_do else None
    
    min_trades = 100
    best_threshold = 0.5
    best_metric = -float('inf')
    results = []
    found_valid = False  # เพิ่มตัวแปรนี้

    X_val = val_df[model_features]
    X_val_scaled = scaler.transform(X_val)
    X_val_scaled_df = pd.DataFrame(X_val_scaled, columns=model_features, index=X_val.index)
    probas = model.predict_proba(X_val_scaled_df)[:, 1]

    for th in thresholds:
        preds = (probas > th).astype(int)
        trades = val_df.copy()
        trades = trades[preds == 1]
        num_trades = len(trades)
        if num_trades < min_trades:
            continue
        found_valid = True  # มี threshold ที่ผ่าน min_trades
        wins = trades[trades['Profit'] > 0]
        losses = trades[trades['Profit'] < 0]
        win_rate = len(wins) / num_trades if num_trades > 0 else 0
        avg_win = wins['Profit'].mean() if len(wins) > 0 else 0
        avg_loss = abs(losses['Profit'].mean()) if len(losses) > 0 else 0
        expectancy = (avg_win * win_rate) - (avg_loss * (1 - win_rate))
        metric = expectancy
        results.append((th, win_rate, expectancy, num_trades))
        if metric > best_metric:
            best_metric = metric
            best_threshold = th

    print("Threshold grid search (val set):")
    for th, wr, exp, ntr in results:
        print(f"  th={th:.2f}  win_rate={wr:.2%}  expectancy={exp:.2f}  num_trades={ntr}")

    if not found_valid:
        print(f"⚠️ ไม่มี threshold ไหนที่มี trade >= {min_trades} ใน validation set จะใช้ threshold default 0.35 แทน")
        return 0.35

    print(f"เลือก threshold ที่ดีที่สุดบน validation set: {best_threshold:.2f} (expectancy สูงสุด, มีเทรด {max([ntr for _,_,_,ntr in results], default=0)})")
    return best_threshold

# ==============================================
# model_training.py
# ฟังก์ชันเกี่ยวกับการฝึก, ประเมิน, cross-validation, เปรียบเทียบโมเดล
# ==============================================

"""ประเมินผลโมเดลแบบละเอียด (accuracy, auc, f1, confusion matrix, classification report, plot ROC/PR curve)"""
def enhanced_evaluation(model, X_test, y_test, output_folder, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน enhanced evaluation") if Steps_to_do else None

    """การประเมินผลแบบละเอียดพร้อมการแยกไฟล์ตาม timeframe"""

    try:
        print(f"🔍 เริ่มการประเมินผลโมเดลแบบละเอียด symbol {symbol} timeframe {timeframe}...")

        # สร้างโฟลเดอร์ย่อยตาม timeframe ถ้ายังไม่มี - แก้ไขให้ใช้ output_folder ที่ส่งมาแล้ว
        timeframe_folder = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}")
        os.makedirs(timeframe_folder, exist_ok=True)
        print(f"📁 สร้างโฟลเดอร์สำหรับ timeframe symbol {symbol} timeframe {timeframe} ที่: {timeframe_folder}")

        # ตรวจสอบว่าเป็น multi-class หรือ binary classification
        is_multiclass = USE_MULTICLASS_TARGET and len(np.unique(y_test)) > 2

        if is_multiclass:
            print(f"📊 Multi-class Classification: {len(np.unique(y_test))} classes")

            # ทำนายสำหรับ multi-class
            y_pred = model.predict(X_test)
            if hasattr(model, 'predict_proba'):
                y_pred_proba = model.predict_proba(X_test)
            else:
                y_pred_proba = None

            # ประเมินผล multi-class
            report = evaluate_multiclass_model(y_test, y_pred, y_pred_proba)
            report['timeframe'] = timeframe

            # สร้างรายงานแบบละเอียด
            clf_report = classification_report(y_test, y_pred, output_dict=True)
            print("\nClassification Report (Multi-class):")
            print(pd.DataFrame(clf_report).transpose())

        else:
            print("📊 Binary Classification")

            # ปรับค่า y_test ให้เป็น binary (0 และ 1)
            y_test_binary = np.where(y_test > 0, 1, 0)

            # ทำนายความน่าจะเป็น (สำหรับ LightGBM อาจต้องใช้ predict_proba)
            if hasattr(model, 'predict_proba'):
                y_probs = model.predict_proba(X_test)[:, 1]
            else:
                y_probs = model.predict(X_test)

            y_pred = (y_probs > 0.5).astype(int)

            # สร้างรายงานแบบละเอียด
            clf_report = classification_report(y_test_binary, y_pred, output_dict=True)
            for key in clf_report:
                if isinstance(clf_report[key], dict):
                    for metric in clf_report[key]:
                        if isinstance(clf_report[key][metric], (int, float)):
                            clf_report[key][metric] = float(clf_report[key][metric])
                        elif metric == 'support':
                            clf_report[key][metric] = int(clf_report[key][metric])

            print("\nClassification Report (Binary Target):")
            print(pd.DataFrame(clf_report).transpose())

            print("📝 กำลังคำนวณ metrics...")
            report = {
                'timeframe': timeframe,
                'accuracy': accuracy_score(y_test_binary, y_pred),
                'auc_roc': roc_auc_score(y_test_binary, y_probs),
                'auc_pr': average_precision_score(y_test_binary, y_probs),
                'f1': f1_score(y_test_binary, y_pred, average='macro'),
                'precision': precision_score(y_test_binary, y_pred, average='macro'),
                'recall': recall_score(y_test_binary, y_pred, average='macro'),
                'confusion_matrix': confusion_matrix(y_test_binary, y_pred),
                'classification_report': clf_report
            }

        report['classification_report'] = clf_report

        # พล็อต visualization ตามประเภทของ classification
        print("📊 กำลังสร้าง visualization...")

        if is_multiclass:
            # Multi-class visualization
            plt.figure(figsize=(15, 10))

            # Confusion Matrix
            plt.subplot(2, 2, 1)
            cm = report['confusion_matrix']
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                       xticklabels=[CLASS_MAPPING.get(i, f'Class_{i}') for i in range(len(cm))],
                       yticklabels=[CLASS_MAPPING.get(i, f'Class_{i}') for i in range(len(cm))])
            plt.title(f'Confusion Matrix - {symbol} {timeframe}')
            plt.ylabel('True Label')
            plt.xlabel('Predicted Label')

            # Class Distribution
            plt.subplot(2, 2, 2)
            unique, counts = np.unique(y_test, return_counts=True)
            class_names = [CLASS_MAPPING.get(i, f'Class_{i}') for i in unique]
            plt.bar(class_names, counts)
            plt.title(f'Class Distribution - {symbol} {timeframe}')
            plt.xticks(rotation=45)

            # Per-class F1 Scores
            plt.subplot(2, 2, 3)
            f1_scores = []
            class_labels = []
            for class_id in unique:
                class_name = CLASS_MAPPING.get(class_id, f'Class_{class_id}')
                f1_key = f'{class_name}_f1'
                if f1_key in report:
                    f1_scores.append(report[f1_key])
                    class_labels.append(class_name)

            if f1_scores:
                plt.bar(class_labels, f1_scores)
                plt.title(f'Per-class F1 Scores - {symbol} {timeframe}')
                plt.xticks(rotation=45)
                plt.ylabel('F1 Score')

            # Overall Metrics
            plt.subplot(2, 2, 4)
            metrics_names = ['Accuracy', 'F1 Macro', 'F1 Weighted']
            metrics_values = [report.get('accuracy', 0),
                            report.get('f1_macro', 0),
                            report.get('f1_weighted', 0)]
            plt.bar(metrics_names, metrics_values)
            plt.title(f'Overall Metrics - {symbol} {timeframe}')
            plt.ylabel('Score')

        else:
            # Binary classification visualization
            plt.figure(figsize=(12, 5))

            # ROC Curve
            plt.subplot(1, 2, 1)
            fpr, tpr, _ = roc_curve(y_test_binary, y_probs)
            plt.plot(fpr, tpr, label=f'AUC = {report["auc_roc"]:.3f}')
            plt.plot([0, 1], [0, 1], 'k--')
            plt.xlabel('False Positive Rate')
            plt.ylabel('True Positive Rate')
            plt.title(f'ROC Curve symbol {symbol} timeframe {timeframe}')
            plt.legend()

            # Precision-Recall Curve
            plt.subplot(1, 2, 2)
            precision, recall, _ = precision_recall_curve(y_test_binary, y_probs)
            plt.plot(recall, precision, label=f'AP = {report["auc_pr"]:.3f}')
            plt.xlabel('Recall')
            plt.ylabel('Precision')
            plt.title(f'Precision-Recall Curve symbol {symbol} timeframe {timeframe}')
            plt.legend()

        plt.tight_layout()

        # บันทึกไฟล์ภาพ (ระบุ timeframe ในชื่อไฟล์)
        plot_filename = f'{str(timeframe).zfill(3)}_{symbol}_performance_curves.png'
        plot_path = os.path.join(timeframe_folder, plot_filename)
        plt.savefig(plot_path)
        plt.close()
        print(f"✅ บันทึกกราฟประสิทธิภาพที่: {plot_path}")

        # บันทึกรายงานเป็นไฟล์ CSV (ระบุ timeframe ในชื่อไฟล์)
        report_filename = f'{str(timeframe).zfill(3)}_{symbol}_evaluation_report.csv'
        report_path = os.path.join(timeframe_folder, report_filename)
        pd.DataFrame.from_dict(report['classification_report']).transpose().to_csv(report_path)
        print(f"✅ บันทึกรายงานการประเมินที่: {report_path}")

        print(f"🎯 การประเมินผลสำหรับ symbol {symbol} timeframe {timeframe} เสร็จสมบูรณ์!")
        
        return report

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะประเมินผล symbol {symbol} timeframe {timeframe}: {str(e)}")
        import traceback
        traceback.print_exc()
        
        return None    

# ปรับปรุง Parameter Distribution ตามการวิเคราะห์ Parameter Stability (อัปเดต 2025-07-07)
# จากการวิเคราะห์ 16 models: ลดช่วงของพารามิเตอร์ที่ไม่เสถียร, เน้นค่าที่เสถียร
# เพิ่มการปรับแต่งเพื่อเพิ่ม win rate 30-50%
param_dist = {
    # Learning rate: ปรับให้เหมาะสมกับการเพิ่ม win rate
    'learning_rate': [0.02, 0.03, 0.04, 0.05],  # เน้นค่าที่ให้ผลดีและไม่ overfitting

    # Num leaves: เพิ่มความซับซ้อนเล็กน้อยเพื่อจับ pattern ที่ดีขึ้น
    'num_leaves': [20, 25, 30, 35],  # เพิ่มขึ้นเล็กน้อยเพื่อความแม่นยำ

    # Max depth: ควบคุมความลึกเพื่อป้องกัน overfitting
    'max_depth': [6, 7, 8],  # เพิ่มเล็กน้อยเพื่อความซับซ้อนที่เหมาะสม

    # Min data in leaf: ปรับให้เหมาะสมกับการลด noise
    'min_data_in_leaf': [15, 20, 25],  # เพิ่มขึ้นเพื่อลด overfitting

    # Feature fraction: ใช้ค่าที่เสถียรและให้ผลดี
    'feature_fraction': [0.8, 0.85, 0.9],  # ลดลงเล็กน้อยเพื่อป้องกัน overfitting

    # Bagging fraction: ปรับให้เหมาะสมกับ ensemble
    'bagging_fraction': [0.7, 0.75, 0.8],  # ลดลงเล็กน้อยเพื่อเพิ่ม diversity

    # เพิ่ม regularization เพื่อป้องกัน overfitting
    'reg_alpha': [0.1, 0.3, 0.5],  # L1 regularization
    'reg_lambda': [0.1, 0.3, 0.5],  # L2 regularization

    # Bagging frequency: ปรับให้เหมาะสมกับ ensemble
    'bagging_freq': [1, 3, 5],  # เพิ่มตัวเลือกเพื่อความหลากหลาย

    # เพิ่มพารามิเตอร์สำหรับ class imbalance
    'class_weight': ['balanced', None],

    # เพิ่มพารามิเตอร์สำหรับการปรับแต่งเพิ่มเติม
    'subsample_freq': [1, 3, 5],  # ความถี่ในการ subsample
    'min_child_weight': [0.001, 0.01, 0.1],  # น้ำหนักขั้นต่ำของ child node
}

# แนะนำ param_dist ที่เสถียรที่สุด (สำหรับ production)
stable_param_dist = {
    'learning_rate': [0.025],  # ค่าเฉลี่ยที่เสถียรที่สุด
    'feature_fraction': [0.89],  # ค่าเฉลี่ยที่เสถียรที่สุด (CV=10%)
    'bagging_fraction': [0.81],  # ค่าเฉลี่ยที่เสถียรที่สุด (CV=15.5%)
    'num_leaves': [19, 20],  # ค่าเฉลี่ยรอบ 19.5
    'max_depth': [6],  # ค่าเฉลี่ยรอบ 5.56
    'min_data_in_leaf': [10],  # ค่าเฉลี่ยรอบ 9.69
}

def comprehensive_hyperparameter_test(X_train, y_train, X_val, y_val, symbol, timeframe):
    """
    ทดสอบ hyperparameter tuning แบบครอบคลุม
    เพื่อประเมินประสิทธิภาพของการปรับจูนพารามิเตอร์
    สำหรับทดสอบ test_hyperparameter_tuning.py
    """
    print(f"\n🧪 เริ่มการทดสอบ Hyperparameter Tuning แบบครอบคลุม")
    print(f"Symbol: {symbol}, Timeframe: {timeframe}")
    print("="*60)

    # 1. ทดสอบด้วย Default Parameters
    print("\n1️⃣ ทดสอบด้วย Default Parameters")
    default_params = get_lgbm_params(y=y_train, use_scale_pos_weight=True, symbol=symbol, timeframe=timeframe)
    default_model = lgb.LGBMClassifier(**default_params, n_estimators=1000, random_state=42)
    default_model.fit(X_train, y_train, eval_set=[(X_val, y_val)],
                     eval_metric='auc', callbacks=[lgb.early_stopping(100, verbose=False)])

    default_pred = default_model.predict_proba(X_val)[:, 1]
    default_auc = roc_auc_score(y_val, default_pred)
    print(f"Default AUC: {default_auc:.4f}")

    # 2. ทดสอบด้วย Grid Search (เล็ก)
    print("\n2️⃣ ทดสอบด้วย Grid Search (เล็ก)")
    small_grid = {
        'learning_rate': [0.01, 0.05],
        'num_leaves': [31, 63],
        'max_depth': [-1, 10]
    }

    grid_search = GridSearchCV(
        lgb.LGBMClassifier(objective='binary', random_state=42, n_estimators=500),
        small_grid,
        scoring='roc_auc',
        cv=3,
        verbose=1
    )
    grid_search.fit(X_train, y_train)
    grid_auc = grid_search.best_score_
    print(f"Grid Search Best AUC: {grid_auc:.4f}")
    print(f"Grid Search Best Params: {grid_search.best_params_}")

    # 3. ทดสอบด้วย Random Search
    print("\n3️⃣ ทดสอบด้วย Random Search")
    random_search = RandomizedSearchCV(
        lgb.LGBMClassifier(objective='binary', random_state=42, n_estimators=500),
        param_dist,
        n_iter=30,
        scoring='roc_auc',
        cv=3,
        verbose=1,
        random_state=42
    )
    random_search.fit(X_train, y_train)
    random_auc = random_search.best_score_
    print(f"Random Search Best AUC: {random_auc:.4f}")
    print(f"Random Search Best Params: {random_search.best_params_}")

    # 4. สรุปผลการเปรียบเทียบ
    print("\n📊 สรุปผลการเปรียบเทียบ:")
    print("="*50)
    results = {
        "Default": default_auc,
        "Grid Search": grid_auc,
        "Random Search": random_auc
    }

    for method, auc in results.items():
        improvement = ((auc - default_auc) / default_auc * 100) if default_auc > 0 else 0
        print(f"{method:15}: AUC = {auc:.4f} (ปรับปรุง: {improvement:+.2f}%)")

    best_method = max(results, key=results.get)
    print(f"\n🏆 วิธีที่ดีที่สุด: {best_method} (AUC = {results[best_method]:.4f})")

    return results

def analyze_parameter_sensitivity(X_train, y_train, X_val, y_val, symbol, timeframe):
    """
    วิเคราะห์ความไวของพารามิเตอร์แต่ละตัวต่อประสิทธิภาพของโมเดล
    สำหรับทดสอบ test_hyperparameter_tuning.py
    """
    print(f"\n🔬 การวิเคราะห์ Parameter Sensitivity")
    print("="*50)

    base_params = get_lgbm_params(y=y_train, use_scale_pos_weight=True, symbol=symbol, timeframe=timeframe)
    base_model = lgb.LGBMClassifier(**base_params, n_estimators=500, random_state=42)
    base_model.fit(X_train, y_train, eval_set=[(X_val, y_val)],
                   eval_metric='auc', callbacks=[lgb.early_stopping(50, verbose=False)])
    base_pred = base_model.predict_proba(X_val)[:, 1]
    base_auc = roc_auc_score(y_val, base_pred)

    print(f"Base AUC: {base_auc:.4f}")

    # ทดสอบแต่ละพารามิเตอร์
    sensitivity_results = {}

    # Learning Rate
    print("\n📈 Learning Rate Sensitivity:")
    lr_values = [0.005, 0.01, 0.02, 0.05, 0.1, 0.2]
    lr_results = []
    for lr in lr_values:
        params = base_params.copy()
        params['learning_rate'] = lr
        model = lgb.LGBMClassifier(**params, n_estimators=500, random_state=42)
        model.fit(X_train, y_train, eval_set=[(X_val, y_val)],
                 eval_metric='auc', callbacks=[lgb.early_stopping(50, verbose=False)])
        pred = model.predict_proba(X_val)[:, 1]
        auc = roc_auc_score(y_val, pred)
        lr_results.append(auc)
        print(f"  LR={lr:5.3f}: AUC={auc:.4f} (Δ={auc-base_auc:+.4f})")

    sensitivity_results['learning_rate'] = dict(zip(lr_values, lr_results))

    # Num Leaves
    print("\n🌿 Num Leaves Sensitivity:")
    leaves_values = [15, 31, 47, 63, 95, 127]
    leaves_results = []
    for leaves in leaves_values:
        params = base_params.copy()
        params['num_leaves'] = leaves
        model = lgb.LGBMClassifier(**params, n_estimators=500, random_state=42)
        model.fit(X_train, y_train, eval_set=[(X_val, y_val)],
                 eval_metric='auc', callbacks=[lgb.early_stopping(50, verbose=False)])
        pred = model.predict_proba(X_val)[:, 1]
        auc = roc_auc_score(y_val, pred)
        leaves_results.append(auc)
        print(f"  Leaves={leaves:3d}: AUC={auc:.4f} (Δ={auc-base_auc:+.4f})")

    sensitivity_results['num_leaves'] = dict(zip(leaves_values, leaves_results))

    # Regularization
    print("\n⚖️ Regularization Sensitivity:")
    reg_values = [0, 0.01, 0.1, 0.5, 1.0, 2.0]
    for reg_type in ['lambda_l1', 'lambda_l2']:
        print(f"\n  {reg_type}:")
        reg_results = []
        for reg in reg_values:
            params = base_params.copy()
            params[reg_type] = reg
            model = lgb.LGBMClassifier(**params, n_estimators=500, random_state=42)
            model.fit(X_train, y_train, eval_set=[(X_val, y_val)],
                     eval_metric='auc', callbacks=[lgb.early_stopping(50, verbose=False)])
            pred = model.predict_proba(X_val)[:, 1]
            auc = roc_auc_score(y_val, pred)
            reg_results.append(auc)
            print(f"    {reg_type}={reg:4.2f}: AUC={auc:.4f} (Δ={auc-base_auc:+.4f})")

        sensitivity_results[reg_type] = dict(zip(reg_values, reg_results))

    return sensitivity_results, base_auc

"""ฝึกและประเมินโมเดล (LightGBM), ทำ cross-validation, scaling, บันทึกโมเดล, plot feature importance, และสรุปผล"""
def train_and_evaluate(input_model, model_name, train_data, val_data, test_data, symbol, timeframe, trade_df=None, output_folder="Test_LightGBM/results"):
    print(f"\n🏗️ เปิดใช้งาน train and evaluate") if Steps_to_do else None

    """ฟังก์ชันฝึกและประเมินโมเดล"""
    try:
        # 1. ตรวจสอบและเตรียมข้อมูล
        if train_data is None or val_data is None or test_data is None:
            print(f"❌ ข้อมูล train/val/test เป็น None")
            return None, None

        if len(train_data) != 2 or len(val_data) != 2 or len(test_data) != 2:
            print(f"❌ รูปแบบข้อมูล train/val/test ไม่ถูกต้อง")
            return None, None

        X_train, y_train = train_data
        X_val, y_val = val_data
        X_test, y_test = test_data

        # ตรวจสอบว่าข้อมูลไม่เป็น None
        if any(x is None for x in [X_train, y_train, X_val, y_val, X_test, y_test]):
            print(f"❌ มีข้อมูลบางส่วนเป็น None")
            return None, None

        print(f"✅ ข้อมูลผ่านการตรวจสอบเบื้องต้น")
        print(f"   Train: {len(X_train)} samples, {len(X_train.columns)} features")
        print(f"   Val: {len(X_val)} samples")
        print(f"   Test: {len(X_test)} samples")

        print("🔍 ตรวจสอบ Temporal Dependence ในชุด Train/Val/Test")
        # สมมติว่า X_train มีคอลัมน์ DateTime (อาจต้องปรับตามโครงสร้างข้อมูลจริง)
        if 'DateTime' in X_train.columns:
            # ตรวจสอบช่วงเวลาของแต่ละชุด
            print(f"- Train: {X_train['DateTime'].min()} ถึง {X_train['DateTime'].max()}")
            print(f"- Val:   {X_val['DateTime'].min()} ถึง {X_val['DateTime'].max()}")
            print(f"- Test:  {X_test['DateTime'].min()} ถึง {X_test['DateTime'].max()}")

            # ตรวจสอบว่าชุด Val อยู่หลังชุด Train และ Test อยู่หลัง Val
            assert X_val['DateTime'].min() >= X_train['DateTime'].max(), "Validation set ต้องอยู่หลัง Training set"
            assert X_test['DateTime'].min() >= X_val['DateTime'].max(), "Test set ต้องอยู่หลัง Validation set"
            print("✅ การเรียงลำดับเวลาของชุดข้อมูลถูกต้อง")

            # ตรวจสอบการกระจายตามเวลาของ Target
            plt.figure(figsize=(12, 4))
            plt.scatter(X_train['DateTime'], y_train, alpha=0.1, label='Train')
            plt.scatter(X_val['DateTime'], y_val, alpha=0.1, label='Val')
            plt.scatter(X_test['DateTime'], y_test, alpha=0.1, label='Test')
            plt.title(f'การกระจายของ Target ตามเวลา ({timeframe} {symbol})')
            plt.legend()
            plot_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_target_distribution.png")
            plt.savefig(plot_path)
            plt.close()
            print(f"💾 บันทึกกราฟการกระจาย Target ตามเวลาที่: {plot_path}")

        # ตรวจสอบว่ามี Features หรือไม่ ก่อนทำ Scaling
        if X_train.empty or X_val.empty or X_test.empty:
            print("⚠️ ไม่มีข้อมูล Features สำหรับการฝึกโมเดล")
            return None, None # หรือจัดการตามความเหมาะสม

        # 2. Scale Features (จำเป็นสำหรับโมเดลส่วนใหญ่)
        # **ต้องทำ Scaling หลังจากการ Split Data เพื่อป้องกัน Data Leakage**
        print("\n⚙️ กำลัง Scaling Features...")

        scaler = StandardScaler()
        # Fit Scaler เฉพาะบน Training Data
        scaler.fit(X_train)
        # Transform Data ทุกชุด
        X_train_scaled = scaler.transform(X_train)
        X_val_scaled = scaler.transform(X_val)
        X_test_scaled = scaler.transform(X_test)
        # แปลงกลับเป็น DataFrame เพื่อให้ Features Names ยังคงอยู่ (Optional แต่มีประโยชน์)
        X_train_scaled = pd.DataFrame(X_train_scaled, columns=X_train.columns, index=X_train.index)
        X_val_scaled = pd.DataFrame(X_val_scaled, columns=X_val.columns, index=X_val.index)
        X_test_scaled = pd.DataFrame(X_test_scaled, columns=X_test.columns, index=X_test.index)

        print("✅ Scaling Features เสร็จสิ้น")

        # 3. จัดการ Class Imbalance ด้วย SMOTE (ถ้าจำเป็น) ต้องเปลี่ยนค่าที่ main_model.fit() ด้วย << ถ้าไม่ใช้ก็ปิดไว้ได้

        # เพิ่มการตรวจสอบ data quality และ class imbalance (อัปเดต 2025-07-05)
        print("\n🔍 การตรวจสอบ Data Quality และ Class Imbalance")
        print("="*60)

        # ตรวจสอบ class distribution
        train_class_dist = y_train.value_counts()
        print("Train class distribution:", train_class_dist)

        # คำนวณ class ratio
        class_0_count = sum(y_train == 0)
        class_1_count = sum(y_train == 1)
        class_ratio = class_0_count / max(class_1_count, 1)
        print(f"Train class ratio (0:1): {class_ratio:.2f}")

        # ตรวจสอบ extreme imbalance (สำหรับ USDJPY และ GBPUSD)
        minority_class_ratio = min(class_0_count, class_1_count) / len(y_train)
        print(f"Minority class ratio: {minority_class_ratio:.3f}")

        # แจ้งเตือนถ้า imbalance รุนแรง
        if minority_class_ratio < 0.05:  # น้อยกว่า 5%
            print("⚠️ WARNING: Extreme class imbalance detected!")
            print("   - อาจส่งผลต่อ AUC และ F1 Score")
            print("   - แนะนำใช้ class_weight='balanced' หรือ SMOTE")
        elif minority_class_ratio < 0.1:  # น้อยกว่า 10%
            print("⚠️ CAUTION: Moderate class imbalance detected")
            print("   - ควรใช้ class_weight='balanced'")
        else:
            print("✅ Class distribution is acceptable")

        # ตรวจสอบ data quality
        print(f"\nData Quality Check:")
        print(f"- Train samples: {len(X_train)}")
        print(f"- Val samples: {len(X_val)}")
        print(f"- Test samples: {len(X_test)}")
        print(f"- Features: {len(X_train.columns)}")

        # ตรวจสอบ missing values
        train_missing = X_train.isnull().sum().sum()
        val_missing = X_val.isnull().sum().sum()
        test_missing = X_test.isnull().sum().sum()

        if train_missing > 0 or val_missing > 0 or test_missing > 0:
            print(f"⚠️ Missing values detected:")
            print(f"   - Train: {train_missing}")
            print(f"   - Val: {val_missing}")
            print(f"   - Test: {test_missing}")
        else:
            print("✅ No missing values detected")

        # ตรวจสอบ infinite values
        train_inf = np.isinf(X_train.select_dtypes(include=[np.number])).sum().sum()
        if train_inf > 0:
            print(f"⚠️ Infinite values detected in train: {train_inf}")
        else:
            print("✅ No infinite values detected")

        use_smote = True # เมื่อต้องการใช้ SMOTE สำหรับจัดการ Class Imbalance
        # ควรเปิดใช้ SMOTE
        # class 1 มีน้อยกว่า class 0 มาก (เช่น 1:5, 1:10 หรือมากกว่า)
        # ถ้า class ratio (0:1) > 3 หรือ 4 ขึ้นไป
        # ถ้า recall/f1 ของ class 1 ต่ำมาก (แม้จะ balance แล้ว) อาจต้องใช้ SMOTE หรือเทคนิคอื่นร่วมด้วย

        # นับจำนวนตัวอย่างในแต่ละคลาส
        class_counts = Counter(y_train)
        min_class_count = min(class_counts.values())
        if min_class_count < 2:
            print("⚠️ คลาส minority มีน้อยเกินไป ข้าม SMOTE")
            use_smote = False

        if use_smote:
            print("⚙️ กำลังทำ SMOTE balancing...")

            # ปรับ k_neighbors ให้ไม่เกินจำนวนตัวอย่างคลาสน้อยสุด - 1
            smote_k = max(1, min(min_class_count - 1, 5))
            smote = SMOTE(random_state=42, k_neighbors=smote_k)
            print(f"SMOTE: ใช้ k_neighbors={smote_k} (min_class_count={min_class_count})")
            X_train_resampled, y_train_resampled = smote.fit_resample(X_train_scaled, y_train)

            print(f"SMOTE: ก่อน balance 0={sum(y_train==0)}, 1={sum(y_train==1)} | หลัง balance 0={sum(y_train_resampled==0)}, 1={sum(y_train_resampled==1)}")
            # ปิด scale_pos_weight เมื่อใช้ SMOTE
            print("\n🚀 กำลังสร้างโมเดล LGBMClassifier ใหม่")
            params = get_lgbm_params(y=y_train, use_scale_pos_weight=False, symbol=symbol, timeframe=timeframe)
            fit_X, fit_y = X_train_resampled, y_train_resampled
            print(f"Class Ratio (0:1): {params.get('scale_pos_weight', 1):.2f}:1")
        else:
            print("\n🚀 กำลังสร้างโมเดล LGBMClassifier ใหม่")
            params = get_lgbm_params(y=y_train, use_scale_pos_weight=True, symbol=symbol, timeframe=timeframe)
            fit_X, fit_y = X_train_scaled, y_train
            print(f"Class Ratio (0:1): {params.get('scale_pos_weight', 1):.2f}:1")

        # ทำ Hyperparameter Tuning
        base_dir = f"Test_LightGBM/models/{str(timeframe).zfill(3)}_{symbol}"

        # สร้างโฟลเดอร์ถ้ายังไม่มี (แก้ไขปัญหาการไม่บันทึกไฟล์)
        os.makedirs(base_dir, exist_ok=True)
        print(f"📁 สร้างโฟลเดอร์สำหรับ tuning files: {base_dir}")

        flag_file = os.path.join(base_dir, f"{str(timeframe).zfill(3)}_{symbol}_tuning_flag.json")
        param_file = os.path.join(base_dir, f"{str(timeframe).zfill(3)}_{symbol}_best_params.json")

        # Force hyperparameter tuning for improved parameters (2025-07-05)
        # ตรวจสอบ command line argument สำหรับ force retuning
        force_retune = getattr(sys.modules[__name__], '_force_retune', False)

        if force_retune:
            print("🔄 --force-retune: บังคับเปิด hyperparameter tuning")
            do_hyperparameter_tuning = True
        else:
            # Check flag (วิธีเดิม)
            if os.path.exists(flag_file):
                with open(flag_file, "r") as f:
                    flag_data = json.load(f)
                do_hyperparameter_tuning = flag_data.get("do_hyperparameter_tuning", False)
            else:
                do_hyperparameter_tuning = True # ครั้งแรก ให้เปิด tuning

        # ทำ Hyperparameter Tuning สำหรับ LightGBM
        print(f"\n🔍 ตรวจสอบสถานะ Hyperparameter Tuning:")
        print(f"   do_hyperparameter_tuning = {do_hyperparameter_tuning}")
        print(f"   flag_file = {flag_file}")
        print(f"   param_file = {param_file}")
        print(f"   flag_file exists = {os.path.exists(flag_file)}")
        print(f"   param_file exists = {os.path.exists(param_file)}")

        if do_hyperparameter_tuning:
            print("\n🔍 เริ่ม Hyperparameter Tuning ด้วย RandomizedSearchCV")

            # ใช้ parameters ที่ได้จาก get_lgbm_params()
            # แยก random_state ออกจาก params เพื่อไม่ให้ซ้ำ
            tuning_params = params.copy()
            tuning_params.pop('random_state', None)  # ลบ random_state ถ้ามี

            lgb_estimator = lgb.LGBMClassifier(
                **tuning_params,  # ใช้ parameters ที่ได้จาก get_lgbm_params() (ไม่มี random_state)
                n_estimators=1000,
                random_state=42
            )

            # ปรับปรุงการตั้งค่า RandomizedSearchCV สำหรับ financial data
            # เลือก scoring metric ตาม classification type
            is_multiclass = params.get('objective') == 'multiclass'
            scoring_metric = 'f1_macro' if is_multiclass else 'roc_auc'

            # ปรับการตั้งค่า RandomizedSearchCV ตาม symbol เฉพาะ (อัปเดต 2025-07-05)
            current_symbol = globals().get('current_processing_symbol', 'UNKNOWN')

            if current_symbol in ['USDJPY', 'GBPUSD']:
                # การตั้งค่าเฉพาะสำหรับ problematic symbols
                n_iter_tuning = 150  # เพิ่มการค้นหาสำหรับ symbols ที่มีปัญหา
                cv_splits = 3        # ลด CV splits เพื่อ stability
                scoring_metric = 'f1_weighted'  # ใช้ f1_weighted สำหรับ imbalanced data
                print(f"🎯 ใช้การตั้งค่าเฉพาะสำหรับ {current_symbol}")
                print(f"   - n_iter: {n_iter_tuning}")
                print(f"   - cv_splits: {cv_splits}")
                print(f"   - scoring: {scoring_metric}")
            else:
                # การตั้งค่าปกติสำหรับ symbols อื่นๆ
                n_iter_tuning = 100
                cv_splits = 5
                scoring_metric = 'f1_macro' if is_multiclass else 'roc_auc'

            search = RandomizedSearchCV(
                lgb_estimator,
                param_distributions=param_dist,
                n_iter=n_iter_tuning,  # ปรับตาม symbol
                scoring=scoring_metric,  # ปรับตาม symbol และ classification type
                cv=TimeSeriesSplit(n_splits=cv_splits),  # ปรับตาม symbol
                verbose=2,
                random_state=42,
                n_jobs=-1,
                return_train_score=True  # เพื่อตรวจสอบ overfitting
            )

            # ทำ RandomizedSearchCV พร้อมตรวจสอบข้อผิดพลาด
            try:
                print(f"🔄 เริ่ม RandomizedSearchCV...")
                print(f"   Training data: {len(fit_X)} samples, {len(fit_X.columns)} features")
                print(f"   Target distribution: {pd.Series(fit_y).value_counts().to_dict()}")

                search.fit(fit_X, fit_y)
                best_params = search.best_params_

                print("✅ RandomizedSearchCV เสร็จสิ้น")
                print("🎯 ผลลัพธ์ Hyperparameter Tuning:")
                print("="*50)
                print("Best params:", search.best_params_)

            except Exception as e:
                print(f"❌ RandomizedSearchCV ล้มเหลว: {e}")
                import traceback
                traceback.print_exc()

                # ใช้ default parameters แทน
                print("🔄 ใช้ default parameters แทน")
                best_params = {}
                search = None

            # แสดงผลลัพธ์ (ถ้ามี)
            metric_name = "F1-Macro" if is_multiclass else "AUC"

            if search is not None:
                print(f"Best {metric_name}: {search.best_score_:.4f}")

                # วิเคราะห์ผลลัพธ์เพิ่มเติม
                try:
                    results_df = pd.DataFrame(search.cv_results_)
                    print(f"\nTop 5 parameter combinations:")
                    top_results = results_df.nlargest(5, 'mean_test_score')[['mean_test_score', 'std_test_score', 'params']]
                    for idx, row in top_results.iterrows():
                        print(f"{metric_name}: {row['mean_test_score']:.4f} (±{row['std_test_score']:.4f}) - {row['params']}")

                    # ตรวจสอบ overfitting
                    if 'mean_train_score' in results_df.columns:
                        best_idx = search.best_index_
                        train_score = results_df.loc[best_idx, 'mean_train_score']
                        test_score = results_df.loc[best_idx, 'mean_test_score']
                        overfitting_gap = train_score - test_score
                        print(f"\nOverfitting Analysis:")
                        print(f"Train Score: {train_score:.4f}")
                        print(f"Test Score: {test_score:.4f}")
                        print(f"Gap: {overfitting_gap:.4f} {'(⚠️ High overfitting)' if overfitting_gap > 0.05 else '(✅ Good)'}")
                except Exception as e:
                    print(f"⚠️ ไม่สามารถวิเคราะห์ผลลัพธ์เพิ่มเติมได้: {e}")
            else:
                print(f"⚠️ ไม่มีผลลัพธ์ {metric_name} เนื่องจาก RandomizedSearchCV ล้มเหลว")

            # Save best_params พร้อมข้อมูลเพิ่มเติม (แม้ว่าจะล้มเหลวก็ตาม)
            tuning_results = {
                "best_params": best_params,
                "best_score": search.best_score_ if search is not None else 0.0,
                "tuning_date": datetime.datetime.now().isoformat(),
                "n_iter": 50,
                "cv_method": "TimeSeriesSplit"
            }

            # บันทึก best_params พร้อมตรวจสอบ
            try:
                with open(param_file, "w") as f:
                    json.dump(tuning_results, f, indent=2)
                print(f"💾 บันทึก best hyperparameters ที่: {param_file}")

                # ตรวจสอบว่าไฟล์ถูกสร้างจริง
                if os.path.exists(param_file):
                    file_size = os.path.getsize(param_file)
                    print(f"✅ ไฟล์ best_params ถูกสร้างแล้ว (ขนาด: {file_size} bytes)")
                else:
                    print(f"❌ ไฟล์ best_params ไม่ถูกสร้าง: {param_file}")

            except Exception as e:
                print(f"❌ เกิดข้อผิดพลาดในการบันทึก best_params: {e}")

            # บันทึก flag พร้อมตรวจสอบ
            try:
                with open(flag_file, "w") as f:
                    json.dump({"do_hyperparameter_tuning": False}, f)
                print(f"🔒 ปิด tuning ในครั้งถัดไป (flag saved at: {flag_file})")

                # ตรวจสอบว่าไฟล์ถูกสร้างจริง
                if os.path.exists(flag_file):
                    file_size = os.path.getsize(flag_file)
                    print(f"✅ ไฟล์ flag ถูกสร้างแล้ว (ขนาด: {file_size} bytes)")
                else:
                    print(f"❌ ไฟล์ flag ไม่ถูกสร้าง: {flag_file}")

            except Exception as e:
                print(f"❌ เกิดข้อผิดพลาดในการบันทึก flag: {e}")

            # อัปเดต params ด้วย best_params
            params.update(best_params)

        else:
            # Load best_params
            if os.path.exists(param_file):
                with open(param_file, "r") as f:
                    tuning_data = json.load(f)

                # รองรับทั้งรูปแบบเก่าและใหม่
                if "best_params" in tuning_data:
                    best_params = tuning_data["best_params"]
                    print(f"\n✅ โหลด best hyperparameters จากไฟล์ {param_file}")
                    print(f"   - Best AUC: {tuning_data.get('best_score', 'N/A')}")
                    print(f"   - Tuning Date: {tuning_data.get('tuning_date', 'N/A')}")
                    print(f"   - CV Method: {tuning_data.get('cv_method', 'N/A')}")
                else:
                    # รูปแบบเก่า (backward compatibility)
                    best_params = tuning_data
                    print(f"\n✅ โหลด best hyperparameters (รูปแบบเก่า) จากไฟล์ {param_file}")

                params.update(best_params)
            else:
                print("⚠️ ไม่พบไฟล์ best_params, ใช้ default params")

        # ...สร้าง main_model...
        # ปรับ n_estimators ให้สอดคล้องกับ tuning และเพียงพอสำหรับการเรียนรู้
        main_model = lgb.LGBMClassifier(
            **params,
            n_estimators=2000,  # ลดจาก 5000 เป็น 2000 เพื่อสอดคล้องกับ tuning และลด overfitting
        )
        print("✅ สร้างโมเดล LGBMClassifier ใหม่สำเร็จ")

        # ตรวจสอบว่ามีโมเดลที่พร้อม Fit หรือไม่ ก่อนดำเนินการต่อ
        if main_model is None:
            print("❌ ไม่สามารถเตรียมโมเดลหลักสำหรับการฝึกได้")
            return None, None # คืน None ถ้าโมเดลเป็น None หลังพยายามเตรียม

        print("⚙️ กำลัง Fit โมเดลหลัก...")
        try:
            # ตรวจสอบว่าเป็น multi-class หรือไม่
            is_multiclass = USE_MULTICLASS_TARGET and len(np.unique(fit_y)) > 2

            if is_multiclass:
                # สำหรับ multi-class ใช้ sample_weight + SMOTE สำหรับ extreme imbalance
                from sklearn.utils.class_weight import compute_sample_weight

                # ตรวจสอบ class imbalance ratio
                class_counts = pd.Series(fit_y).value_counts()
                ratio = class_counts.max() / class_counts.min()

                if ratio > 20:  # Extreme imbalance - ใช้ SMOTE
                    print(f"  🚨 Extreme imbalance detected (ratio={ratio:.1f}:1) - Applying SMOTE")
                    try:
                        smote = SMOTE(random_state=42, k_neighbors=min(5, class_counts.min()-1))
                        fit_X, fit_y = smote.fit_resample(fit_X, fit_y)
                        print(f"  ✅ SMOTE applied: {len(class_counts)} -> {len(pd.Series(fit_y).value_counts())} samples")
                    except Exception as e:
                        print(f"  ⚠️ SMOTE failed: {e}, using original data")

                sample_weights = compute_sample_weight('balanced', fit_y)

                main_model.fit(
                    fit_X, fit_y,
                    sample_weight=sample_weights,
                    eval_set=[(X_val_scaled, y_val)],
                    eval_metric='multi_logloss',
                    callbacks=[
                        lgb.early_stopping(stopping_rounds=200, verbose=-1),  # เพิ่มจาก 100 เป็น 200 เพื่อให้เรียนรู้เพียงพอ
                        lgb.log_evaluation(100),
                    ]
                )
            else:
                # สำหรับ binary classification
                main_model.fit(
                    fit_X, fit_y,
                    eval_set=[(X_val_scaled, y_val)],
                    eval_metric='auc',
                    callbacks=[
                        lgb.early_stopping(stopping_rounds=200, verbose=-1),  # เพิ่มจาก 100 เป็น 200 เพื่อให้เรียนรู้เพียงพอ
                        lgb.log_evaluation(100),
                    ]
                )

            print("✅ ฝึกโมเดลหลักสำเร็จ")

            # ตรวจสอบคุณภาพโมเดลก่อนใช้งาน
            model_quality_ok = validate_model_quality(main_model, X_val_scaled, y_val, symbol, timeframe)
            if not model_quality_ok:
                print(f"⚠️ โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ - อาจส่งผลต่อ win rate")
                print(f"   แนะนำ: ปรับ parameters, เพิ่มข้อมูล, หรือปรับปรุง features")
            # หลังจาก fit โมเดลหลักแล้ว main_model จะมี trained model อยู่

            # === เพิ่มส่วนนี้หลัง fit main_model ===
            # ต้องแน่ใจว่า val_data มี trade_df ที่มี 'Profit' ด้วย
            # สมมติว่า val_df คือ validation trade_df ที่มี 'Profit' (เช่น trade_df หรือ val ที่ merge profit แล้ว)
            if val_data is not None:
                X_val, y_val = val_data
                # คุณต้องเตรียม val_df ที่มี 'Profit' ด้วย (ดูจาก pipeline ของคุณ)
                # สมมติว่าคุณมี trade_df_val ที่ตรงกับ X_val
                # ตัวอย่าง:
                # val_df = ... (DataFrame ที่มี features, 'Profit', 'Target' ตรงกับ X_val)
                # ถ้า trade_df มี index เดียวกับ X_val:
                val_df = X_val.copy()
                if 'Profit' in trade_df.columns:
                    val_df['Profit'] = trade_df.loc[X_val.index, 'Profit']
                    val_df['Target'] = y_val
                    best_threshold = find_best_threshold_on_val(
                        model=main_model,
                        scaler=scaler,
                        val_df=val_df,
                        model_features=X_val.columns.tolist()
                    )
                    save_optimal_threshold(symbol, timeframe, best_threshold)

        except Exception as e:
            print(f"⚠️ เกิดข้อผิดพลาด ขณะ Fit โมเดล LightGBM หลัก: {str(e)}")
            traceback.print_exc()
            return None, None # คืน None model และ scaler ถ้า Fit ล้มเหลว

        print("-> เสร็จสิ้นการ Fit โมเดล LightGBM หลัก")

        # ตรวจสอบว่าโมเดล Fit สำเร็จและพร้อมใช้งานหรือไม่
        if not hasattr(main_model, 'predict'):
            print("⚠️ โมเดลหลักไม่สามารถฝึกได้สำเร็จ หรือไม่ใช่โมเดลที่ถูกต้องหลัง Fit")
            return None, None

        print("-> กำลังเรียกตรวจสอบ features ของโมเดลหลัก")
        # ใช้ Feature Names จากข้อมูล X_train_scaled ที่ใช้ Fit โมเดลหลัก
        features_used_in_training = X_train_scaled.columns.tolist()

        try:
            # LightGBMClassifier หลังจาก Fit จะเก็บ Feature Names ใน attribute feature_name_
            model_feature_names = []
            if hasattr(main_model, 'feature_name_') and main_model.feature_name_:
                model_feature_names = main_model.feature_name_
            elif hasattr(main_model, 'feature_name'): # Fallback สำหรับ lgb.Booster หรือเวอร์ชันเก่า
                model_feature_names = main_model.feature_name()

            # ตรวจสอบว่า Features ในข้อมูลที่ใช้ Fit ตรงกับ Feature Names ในโมเดลหลัง Fit หรือไม่
            # (กรณีทั่วไปควรตรงกัน ถ้าไม่มีปัญหาในการเตรียมข้อมูลหรือการแปลง)
            if model_feature_names and set(model_feature_names) != set(features_used_in_training):
                print("\n⚠️ เตือน : Features ใน Model (หลัง Fit) ไม่ตรงกับ Features ในข้อมูลที่ใช้ Fit!")
                print(f"Features ในโมเดล (หลัง Fit): {len(model_feature_names)} features")
                print(f"Features ในข้อมูลที่ใช้ Fit: {len(features_used_in_training)} features")
                print("ℹ️ ปัญหานี้อาจบ่งชี้ว่าการเตรียมข้อมูล Features หรือการประมวลผลมีปัญหา")
                # ไม่ต้อง reindex ที่นี่แล้ว เพราะข้อมูลถูก Fit ไปแล้ว
                # ปัญหานี้ต้องแก้ที่ขั้นตอนการเตรียมข้อมูล *ก่อน* เข้า train and evaluate

        except Exception as e:
            print(f"⚠️ เกิดข้อผิดพลาด ขณะตรวจสอบ features ของโมเดลหลัก: {str(e)}")
            traceback.print_exc()
        print("-> เสร็จสิ้นตรวจสอบ features")

        # ==============================================
        # ส่วนเพิ่มเติม: ทดสอบ RandomForest ก่อนฝึก LightGBM (ยังใช้ X_train, y_train ดั้งเดิม)
        # ไม่ได้ใช้ในการตัดสินใจเทรดจริง
        # เปรียบเทียบ feature importance กับ LightGBM (ดูว่าฟีเจอร์ไหนสำคัญในทั้งสองโมเดล)
        # ใช้เป็น baseline สำหรับประเมินประสิทธิภาพ (accuracy, f1, auc ฯลฯ)
        # ==============================================
        print("\n" + "="*50)
        print("  การทดสอบเปรียบเทียบกับ RandomForest  ")
        print("="*50)
        print("-> กำลังเรียก test random forest")
        # **ข้อควรระวัง:** test random forest ใช้ X_train, X_test ที่ *ไม่* ได้ Scale!
        # ควรแน่ใจว่า RandomForestClassifier สามารถรับข้อมูลแบบไม่ Scale ได้ หรือปรับให้ Scaler ก่อน Fit ในฟังก์ชันนั้น
        # จากโค้ด test random forest ที่คุณให้มา มัน Fit ด้วยข้อมูล *ไม่ Scale* ซึ่งอาจจะไม่เหมาะสมถ้าคุณต้องการเปรียบเทียบกับ LGBM ที่ Fit ด้วยข้อมูล Scale
        # สำหรับตอนนี้ เราจะเรียกด้วยข้อมูลไม่ Scale ตามโค้ดเดิมของคุณ แต่โปรดทราบว่านี่คือจุดที่อาจต้องการการปรับปรุง
        if not X_train.empty and len(np.unique(y_train)) >= 2:
            rf_importance = test_random_forest(X_train, y_train, X_test, y_test, X_train.columns.tolist(), symbol, timeframe)
        else:
            print("⚠️ ข้อมูลไม่เพียงพอหรือมีเพียงคลาสเดียวสำหรับทดสอบ RandomForest")
            rf_importance = None
        print("-> เสร็จสิ้น test random forest")
        
        # 2. ตรวจสอบการกระจายของคลาส
        print("\n📊 การกระจายของคลาส:")
        print(f"Train - 0: {sum(y_train==0)}, 1: {sum(y_train==1)}")
        print(f"Test - 0: {sum(y_test==0)}, 1: {sum(y_test==1)}")
        
        # 3. คำนวณ class ratio
        class_ratio = len(y_train[y_train==0]) / len(y_train[y_train==1]) if len(y_train[y_train==1]) > 0 else 1
        
        # 4. ทำ Cross-Validation
        print("\n🔍 เริ่มทำ Cross-Validation...")
        X_full = pd.concat([X_train, X_val]) # ใช้ X_train, X_val ดั้งเดิม (ไม่ Scale)
        y_full = pd.concat([y_train, y_val])

        print("-> กำลังเรียก time series cv")
        # **ข้อควรระวัง:** time series cv ต้อง Fit Scaler ใหม่ภายในแต่ละ Fold และ Scale ข้อมูลของ Fold ก่อน Fit โมเดล
        # ถ้า time series cv ไม่ได้ทำ Scaling ภายใน จะเกิดความไม่สอดคล้องกันในการเปรียบเทียบ
        if not X_full.empty and len(np.unique(y_full)) >= 2:
            cv_results = time_series_cv(X_full, y_full, timeframe) # เรียกด้วยข้อมูลไม่ Scale
        else:
            print("⚠️ ข้อมูลไม่เพียงพอหรือมีเพียงคลาสเดียวสำหรับ Time Series CV")
            cv_results = { 'accuracy': 0, 'auc': 0.5, 'f1': 0, 'precision': 0, 'recall': 0 }
        print("-> เสร็จสิ้น time series cv")

        # 5. ตรวจสอบข้อมูล (ซ้ำซ้อนกับเช็คก่อน CV, อาจปรับปรุงได้)
        if len(np.unique(y_train)) < 2: # ตรวจ y_train ดั้งเดิม
            print("⚠️ ข้อมูลฝึกมีเพียงคลาสเดียว ไม่สามารถดำเนินการประเมินต่อได้")
            # คุณควร return ออกจากฟังก์ชันที่นี่เลย ถ้าไม่สามารถฝึกโมเดลได้
            return {
                'model_name': model_name,
                'metrics': None, # ไม่มีการประเมิน Test Set ถ้าฝึกไม่สำเร็จ
                'cv_results': cv_results, # คืนค่า CV ถ้าคำนวณได้
                'timeframe': timeframe,
                'num_trees': 0
            }, None # คืน None scaler ด้วย

        # 7. ประเมินโมเดลหลัก (ใช้ main_model)
        print(f"\n  การประเมินผลโมเดลแบบละเอียด symbol {symbol} timeframe {timeframe}")
        # **ข้อควรระวัง:** enhanced evaluation ต้องใช้ข้อมูล X_test_scaled ครับ
        # ตรวจสอบให้แน่ใจว่า enhanced evaluation ใช้ X_test_scaled และ y_test
        enhanced_metrics = enhanced_evaluation(main_model, X_test_scaled, y_test, output_folder, symbol, timeframe) # ใช้ main_model และ X_test_scaled
        
        if enhanced_metrics is None:
            print(f"⚠️ ไม่สามารถประเมินผลโมเดล symbol {symbol} timeframe {timeframe} ได้ ใช้การประเมินพื้นฐานแทน")
            # Fallback to basic evaluation
            y_pred = input_model.predict(X_test, num_iteration=input_model.best_iteration)
            y_pred_binary = np.round(y_pred)
            metrics = {
                'timeframe': timeframe,
                'accuracy': accuracy_score(y_test, y_pred_binary),
                'auc': roc_auc_score(y_test, y_pred),
                'f1': f1_score(y_test, y_pred_binary),
                'precision': precision_score(y_test, y_pred_binary),
                'recall': recall_score(y_test, y_pred_binary),
                'confusion_matrix': confusion_matrix(y_test, y_pred_binary),
                'auc_pr': None  # ไม่มีค่าในโหมดพื้นฐาน
            }
        else:
            metrics = {
                'timeframe': timeframe,
                'accuracy': enhanced_metrics['accuracy'],
                'auc': enhanced_metrics.get('auc_roc', 0.5),  # สำหรับ multi-class อาจไม่มี auc_roc
                'f1': enhanced_metrics.get('f1', 0),  # สำหรับ multi-class อาจไม่มี f1
                'precision': enhanced_metrics.get('precision', 0),  # สำหรับ multi-class อาจไม่มี precision
                'recall': enhanced_metrics.get('recall', 0),  # สำหรับ multi-class อาจไม่มี recall
                'confusion_matrix': enhanced_metrics['confusion_matrix'],
                'auc_pr': enhanced_metrics.get('auc_pr', 0.5)  # สำหรับ multi-class อาจไม่มี auc_pr
            }
        
        # 8. แสดงผลลัพธ์จาก enhanced evaluation
        if enhanced_metrics:
            print(f"\n📊 ผลการประเมินแบบละเอียด symbol {symbol} timeframe {timeframe}")
            print(f"AUC-ROC: {metrics['auc']:.4f}")
            print(f"AUC-PR: {metrics['auc_pr']:.4f}")
            
            # print("\nClassification Report:")
            # print(pd.DataFrame(enhanced_metrics['classification_report']).transpose())
            
            # แปลงค่าใน report ให้เป็น float ก่อนแสดงผล
            clf_report = enhanced_metrics['classification_report']
            for key in clf_report:
                if isinstance(clf_report[key], dict):
                    for metric in clf_report[key]:
                        if isinstance(clf_report[key][metric], (int, float)):
                            clf_report[key][metric] = clf_report[key][metric]
            
            print(pd.DataFrame(clf_report).transpose())
        
        print("\n📌 สรุปผลลัพธ์แบบละเอียด:")
        for metric, value in enhanced_metrics.items():
            if metric not in ['classification_report', 'confusion_matrix']:
                # ตรวจสอบก่อนว่า value เป็นตัวเลขหรือไม่ก่อนใช้ .4f
                if isinstance(value, (int, float)):
                    print(f"- {metric.capitalize()}: {value:.4f}")
                else:
                    print(f"- {metric.capitalize()}: {value}")
        
        # เก็บ metrics สำหรับคืนค่า (จาก enhanced_metrics)
        metrics = {
            'accuracy': enhanced_metrics.get('accuracy', 0), # ใช้ .get() เผื่อค่าไม่มี
            'auc': enhanced_metrics.get('auc_ovr', enhanced_metrics.get('auc_roc', 0.5)),  # ใช้ auc_ovr สำหรับ multi-class
            'f1': enhanced_metrics.get('f1_macro', enhanced_metrics.get('f1', 0)),  # ใช้ f1_macro สำหรับ multi-class
            'precision': enhanced_metrics.get('precision_macro', enhanced_metrics.get('precision', 0)),  # ใช้ precision_macro สำหรับ multi-class
            'recall': enhanced_metrics.get('recall_macro', enhanced_metrics.get('recall', 0)),  # ใช้ recall_macro สำหรับ multi-class
            'confusion_matrix': enhanced_metrics.get('confusion_matrix'),
            'auc_pr': enhanced_metrics.get('auc_pr', 0.5)
        }

        # 9. บันทึกโมเดล Scaler และ Features
        try:
            model_dir = f"Test_LightGBM/models/{str(timeframe).zfill(3)}_{symbol}"
            os.makedirs(model_dir, exist_ok=True)

            model_path = os.path.join(model_dir, f"{model_name}_{str(timeframe).zfill(3)}_{symbol}_trained.pkl")
            features_path = os.path.join(model_dir, f"{model_name}_{str(timeframe).zfill(3)}_{symbol}_features.pkl")
            scaler_path = os.path.join(model_dir, f"{model_name}_{str(timeframe).zfill(3)}_{symbol}_scaler.pkl")
            
            if Save_File:
                # บันทึกโมเดล
                print(f"\nกำลังบันทึกโมเดลที่: {model_path}")
                joblib.dump(main_model, model_path)
                print(f"✅ บันทึกโมเดลเรียบร้อย (ขนาด: {os.path.getsize(model_path)/1024:.2f} KB)")

                # บันทึก features
                features_to_save = X_train.columns.tolist()
                print(f"\nกำลังบันทึก features ที่: {features_path}")
                joblib.dump(features_to_save, features_path)
                print(f"✅ บันทึก features เรียบร้อย (จำนวน features: {len(features_to_save)})")

                # บันทึก Scaler
                if scaler is not None:
                    print(f"\nกำลังบันทึก Scaler ที่: {scaler_path}")
                    joblib.dump(scaler, scaler_path)
                    print(f"✅ บันทึก Scaler เรียบร้อย (ขนาด: {os.path.getsize(scaler_path)/1024:.2f} KB)")
                else:
                    print("⚠️ Scaler ไม่ได้ถูกสร้าง ไม่บันทึก Scaler")

        except Exception as e:
            print(f"\n⚠️ เกิดข้อผิดพลาด ขณะบันทึกไฟล์โมเดล/scaler/features: {str(e)}")
            traceback.print_exc()
            # เพิ่มการบันทึก error log...

        # 10. บันทึกประวัติการเทรน
        # ... (โค้ดเดิม ใช้ metrics จาก enhanced_metrics) ...
        history_entry = {
            'timestamp': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'),
            'timeframe': timeframe,
            'model': model_name,
            'accuracy': round(metrics['accuracy'],5), # ใช้ค่าจาก metrics ที่รวบรวมไว้
            'auc': round(metrics['auc'],5),
            'num_trees': main_model.n_estimators_ if hasattr(main_model, 'n_estimators_') else 0, # จำนวน Trees จาก LGBMClassifier
            'model_path': model_path if 'model_path' in locals() else 'N/A',
            'data_samples': len(X_train) + len(X_val) + len(X_test)
        }

        history_dir = "Test_LightGBM/training_history"
        os.makedirs(history_dir, exist_ok=True)
        history_file = os.path.join(history_dir, f"{str(timeframe).zfill(3)}_{symbol}_training_history.csv")
        
        try:
            if os.path.exists(history_file):
                history_df = pd.read_csv(history_file)
                new_entry_df = pd.DataFrame([history_entry])
                history_df = pd.concat([history_df, new_entry_df], ignore_index=True)
            else:
                history_df = pd.DataFrame([history_entry])
            
            history_df.to_csv(history_file, index=False, encoding='utf-8-sig')
            print(f"\n📝 บันทึกประวัติการเทรนที่: {history_file}")
        except Exception as e:
            print(f"\n⚠️ เกิดข้อผิดพลาด ขณะบันทึกประวัติ: {str(e)}")

        # Feature Importance (ใช้ main_model ซึ่งคือ LGBMClassifier)
        if hasattr(main_model, 'feature_importances_'): # LGBMClassifier มี feature_importances_
            print("-> กำลังสร้าง Feature Importance")
            # ปรับ plot_feature_importance ให้รับ feature_importances_ และ feature names
            # หรือให้ plot_feature_importance รับ LGBMClassifier ได้โดยตรง

            print(f"✅ พิมพ์ feature ก่อนส่งเข้า plot_feature_importance")
            print(X_train.columns.tolist())
            # for i, feat in enumerate(X_train.columns.tolist(), 1):
            #     print(f"{i}. {feat}")

            importance_df = plot_feature_importance(
                model=main_model, # ส่ง LGBMClassifier instance
                features=X_train.columns.tolist(), # ส่งชื่อ features ดั้งเดิม
                model_name=model_name,
                symbol=symbol,
                timeframe=timeframe,
                output_folder=output_folder
            )
            print("-> เสร็จสิ้น Feature Importance")

            # แสดง Feature Importance ที่สำคัญที่สุด
            if importance_df is not None:
                print("\n🔍 Top 5 Most Important Features (Gain):")
                top_features = importance_df.head(5)
                for idx, row in top_features.iterrows():
                    print(f"{row['Feature']}: {row['Gain']:.4f} (Gain), {row['Split']:.4f} (Split)")

        else:
            print("⚠️ โมเดลหลักไม่มี attribute 'feature_importances_' ไม่สามารถสร้าง Feature Importance ได้")
            importance_df = None

        # เปรียบเทียบ Feature Importance (ถ้ามีทั้งของ LGBM และ RF)
        if 'rf_importance' in locals() and rf_importance is not None and importance_df is not None:
            compare_feature_importance(
                lgb_importance=importance_df, # Use the df created above
                rf_importance=rf_importance,
                symbol=symbol,
                timeframe=timeframe,
                output_folder=output_folder
            )
        else:
            print("\n⚠️ ข้อมูล Feature Importance ไม่ครบถ้วน ไม่สามารถเปรียบเทียบได้")

        # 12. แสดงผลลัพธ์ Cross-Validation
        if cv_results is not None: # เช็คว่า cv_results ถูกกำหนดค่าหรือไม่
            print("\n📊 เปรียบเทียบผลลัพธ์:")
            print(f"| Metric      | CV Avg    | Test Set |")
            print(f"|-------------|-----------|----------|")
            print(f"| Accuracy    | {cv_results.get('accuracy', 0):.4f}    | {metrics.get('accuracy', 0):.4f} |") # ใช้ .get()
            print(f"| AUC         | {cv_results.get('auc', 0.5):.4f}    | {metrics.get('auc', 0.5):.4f} |") # ใช้ .get()
            print(f"| F1 Score    | {cv_results.get('f1', 0):.4f}    | {metrics.get('f1', 0):.4f} |") # ใช้ .get()
        else:
            print("\n📊 ไม่สามารถแสดงผลลัพธ์เปรียบเทียบได้ เนื่องจากไม่มีผลลัพธ์ CV")

        print("-> train and evaluate ทำงานเสร็จสิ้น")

        # 13. ตรวจสอบความสมบูรณ์ของผลลัพธ์ก่อนคืนค่า
        if main_model is None:
            print(f"❌ main_model เป็น None - ไม่สามารถคืนค่าผลลัพธ์ได้")
            return None, None

        if scaler is None:
            print(f"❌ scaler เป็น None - ไม่สามารถคืนค่าผลลัพธ์ได้")
            return None, None

        if metrics is None:
            print(f"❌ metrics เป็น None - ไม่สามารถคืนค่าผลลัพธ์ได้")
            return None, None

        # ตรวจสอบ model quality ก่อนคืนค่า
        try:
            model_quality_ok = validate_model_quality(main_model, X_val_scaled, y_val, symbol, timeframe)
            if not model_quality_ok:
                print(f"⚠️ โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ แต่จะคืนค่าผลลัพธ์ต่อไป")
        except Exception as e:
            print(f"⚠️ ไม่สามารถตรวจสอบ model quality: {e}")

        # 14. คืนค่าผลลัพธ์และ Scaler
        # คืน main_model แทน input_model เพราะ main_model คือ instance ที่ถูก Fit แล้ว
        result_dict = {
            'model_name': model_name,
            'metrics': metrics,
            'cv_results': cv_results,
            'timeframe': timeframe,
            'num_trees': main_model.n_estimators_ if hasattr(main_model, 'n_estimators_') else 0,
            'feature_importance': importance_df.to_dict() if importance_df is not None else None,
            'trained_model': main_model,  # เพิ่ม trained model
            'model_features': X_train.columns.tolist()  # เพิ่ม features list
        }

        print(f"✅ คืนค่าผลลัพธ์สำเร็จ: model={type(main_model).__name__}, scaler={type(scaler).__name__}")
        return result_dict, scaler # คืน scaler ที่ Fit แล้ว

    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดใน train_and_evaluate สำหรับ {symbol} {timeframe}: {str(e)}")
        import traceback
        traceback.print_exc()

        # ส่งข้อมูล debug เพิ่มเติม
        print(f"\n🔍 Debug Information:")
        print(f"   Symbol: {symbol}")
        print(f"   Timeframe: {timeframe}")
        print(f"   Model Name: {model_name}")
        print(f"   Input Model: {type(input_model).__name__ if input_model else 'None'}")

        try:
            if 'X_train' in locals():
                print(f"   Train Data: {len(X_train)} samples, {len(X_train.columns)} features")
            if 'X_val' in locals():
                print(f"   Val Data: {len(X_val)} samples")
            if 'X_test' in locals():
                print(f"   Test Data: {len(X_test)} samples")
        except:
            print(f"   ไม่สามารถแสดงข้อมูล debug ได้")

        return None, None

"""ทำ Time Series Cross-Validation สำหรับข้อมูลอนุกรมเวลา"""
def time_series_cv(X, y, timeframe, n_splits=5):
    print(f"\n🏗️ เปิดใช้งาน time series cv") if Steps_to_do else None

    """Time Series Cross-Validation แบบปรับปรุงสำหรับข้อมูลอนุกรมเวลา"""
    from sklearn.model_selection import TimeSeriesSplit
    import warnings
    
    # ตรวจสอบข้อมูลเบื้องต้น
    if X.empty or y.empty:
        print("⚠️ เกิดข้อผิดพลาด : ข้อมูล X หรือ y ว่างเปล่า")
        return {
            'accuracy': 0,
            'auc': 0.5,
            'f1': 0,
            'precision': 0,
            'recall': 0
        }
    
    if len(X) != len(y):
        print("⚠️ เกิดข้อผิดพลาด : จำนวนข้อมูล X และ y ไม่เท่ากัน")
        return {
            'accuracy': 0,
            'auc': 0.5,
            'f1': 0,
            'precision': 0,
            'recall': 0
        }
    
    print(f"🔁 เริ่มทำ Time Series Cross-Validation (n_splits={n_splits})")
    
    try:
        # ใช้ TimeSeriesSplit สำหรับข้อมูลอนุกรมเวลา
        tscv = TimeSeriesSplit(n_splits=n_splits, test_size=int(len(X)*0.2))  # กำหนด test_size เป็น 20%
        
        metrics = {
            'accuracy': [],
            'auc': [],
            'f1': [],
            'precision': [],
            'recall': []
        }
        
        # เพิ่ม Scaler ในแต่ละ Fold เพื่อป้องกัน Data Leakage
        scalers = []
        
        for fold, (train_idx, val_idx) in enumerate(tscv.split(X), 1):
            print(f"\n📊 Fold {fold}/{n_splits}:")
            print(f"  - Train size: {len(train_idx)} ตัวอย่าง ({(train_idx[-1]+1)/len(X):.1%} ของข้อมูลทั้งหมด)")
            print(f"  - Val size:   {len(val_idx)} ตัวอย่าง")
            
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            # ตรวจสอบ class distribution
            class_dist = pd.Series(y_train).value_counts(normalize=True)
            print(f"  - การกระจายคลาสใน Train: {class_dist.to_dict()}")
            
            if len(class_dist) < 2:
                print(f"⚠️ เตือน : ใน Fold {fold} มีเพียงคลาสเดียวในข้อมูลฝึก ({class_dist.to_dict()})")
                continue

            # ตรวจสอบว่า validation set มีคลาสที่ไม่เคยเห็นใน training set หรือไม่
            train_classes = set(y_train.unique())
            val_classes = set(y_val.unique())
            unseen_classes = val_classes - train_classes

            if unseen_classes:
                print(f"⚠️ เตือน : ใน Fold {fold} พบคลาสใน validation ที่ไม่มีใน training: {unseen_classes}")
                print(f"  - Training classes: {sorted(train_classes)}")
                print(f"  - Validation classes: {sorted(val_classes)}")
                continue
            
            # Scale ข้อมูลในแต่ละ Fold แยกกัน
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_val_scaled = scaler.transform(X_val)
            scalers.append(scaler)  # เก็บ scaler แต่ละ fold (หากต้องการใช้ภายหลัง)
            
            # สร้างและฝึกโมเดล
            try:
                print("🏗️ กำลังสร้างโมเดล...")
                
                # ตรวจสอบว่าเป็น multiclass หรือไม่
                is_multiclass = USE_MULTICLASS_TARGET and len(np.unique(y_train)) > 2

                model = lgb.LGBMClassifier(
                    **get_lgbm_params(y_train, symbol="UNKNOWN", timeframe=60),
                    n_estimators=1000,
                    verbose=-1
                )

                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")

                    if is_multiclass:
                        # สำหรับ multiclass ใช้ sample_weight และ multi_logloss
                        from sklearn.utils.class_weight import compute_sample_weight
                        sample_weights = compute_sample_weight('balanced', y_train)

                        model.fit(
                            X_train_scaled, y_train,
                            sample_weight=sample_weights,
                            eval_set=[(X_val_scaled, y_val)],
                            eval_metric='multi_logloss',
                            callbacks=[
                                lgb.early_stopping(stopping_rounds=50, verbose=False),
                                lgb.log_evaluation(0)
                            ]
                        )
                    else:
                        # สำหรับ binary classification
                        model.fit(
                            X_train_scaled, y_train,
                            eval_set=[(X_val_scaled, y_val)],
                            eval_metric='auc',
                            callbacks=[
                                lgb.early_stopping(stopping_rounds=50, verbose=False),
                                lgb.log_evaluation(0)
                            ]
                        )
                
                print(f"  ✅ สร้างโมเดลสำเร็จ (ใช้ {model.n_estimators_} trees)")
                
                # ทำนายและประเมินผล
                y_pred_class = model.predict(X_val_scaled)
                y_pred_proba = model.predict_proba(X_val_scaled)

                # ตรวจสอบว่าเป็น multiclass หรือไม่
                is_multiclass_cv = len(np.unique(y_val)) > 2

                # คำนวณ metrics
                if is_multiclass_cv:
                    # สำหรับ multiclass
                    try:
                        auc = roc_auc_score(y_val, y_pred_proba, multi_class='ovr', average='macro')
                    except:
                        auc = 0.5

                    fold_metrics = {
                        'accuracy': accuracy_score(y_val, y_pred_class),
                        'auc': auc,
                        'f1': f1_score(y_val, y_pred_class, average='weighted', zero_division=0),
                        'precision': precision_score(y_val, y_pred_class, average='weighted', zero_division=0),
                        'recall': recall_score(y_val, y_pred_class, average='weighted', zero_division=0)
                    }
                else:
                    # สำหรับ binary classification
                    y_pred_binary = y_pred_proba[:, 1] if y_pred_proba.shape[1] == 2 else y_pred_proba[:, 0]
                    y_pred_bin = (y_pred_binary > 0.5).astype(int)

                    fold_metrics = {
                        'accuracy': accuracy_score(y_val, y_pred_bin),
                        'auc': roc_auc_score(y_val, y_pred_binary),
                        'f1': f1_score(y_val, y_pred_bin, zero_division=0),
                        'precision': precision_score(y_val, y_pred_bin, zero_division=0),
                        'recall': recall_score(y_val, y_pred_bin, zero_division=0)
                    }
                
                # บันทึกผลลัพธ์
                for k in metrics:
                    metrics[k].append(fold_metrics[k])
                
                print(f"  📊 ผลลัพธ์ Fold {fold}:")
                print(f"    - Accuracy:  {fold_metrics['accuracy']:.4f}")
                print(f"    - AUC:       {fold_metrics['auc']:.4f}")
                print(f"    - F1 Score:  {fold_metrics['f1']:.4f}")
                print(f"    - Precision: {fold_metrics['precision']:.4f}")
                print(f"    - Recall:    {fold_metrics['recall']:.4f}")
                
            except Exception as e:
                print(f"⚠️ เกิดข้อผิดพลาด ใน Fold {fold}: {str(e)}")
                continue
        
        # คำนวณค่าเฉลี่ย metrics
        avg_metrics = {k: np.mean(v) if v else 0 for k, v in metrics.items()}
        
        print("\n✅ การทำ Time Series Cross-Validation เสร็จสมบูรณ์")
        print("📌 ผลลัพธ์เฉลี่ย:")
        print(f"  - Accuracy:  {avg_metrics['accuracy']:.4f}")
        print(f"  - AUC:       {avg_metrics['auc']:.4f}")
        print(f"  - F1 Score:  {avg_metrics['f1']:.4f}")
        print(f"  - Precision: {avg_metrics['precision']:.4f}")
        print(f"  - Recall:    {avg_metrics['recall']:.4f}")
        
        return avg_metrics
    
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ร้ายแรงใน time series cv: {str(e)}")
        return {
            'accuracy': 0,
            'auc': 0.5,
            'f1': 0,
            'precision': 0,
            'recall': 0
        }

"""ทดสอบ RandomForest เพื่อเปรียบเทียบ feature importance กับ LightGBM"""
def test_random_forest(X_train, y_train, X_test, y_test, features, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน test random forest") if Steps_to_do else None

    """ทดสอบ RandomForest เพื่อเปรียบเทียบ"""
    print("🔍 กำลังทดสอบ RandomForest...")
    from sklearn.ensemble import RandomForestClassifier
    
    # ตรวจสอบว่ามีทั้งสองคลาสหรือไม่
    if len(np.unique(y_train)) < 2:
        print("⚠️ ข้อมูลมีเพียงคลาสเดียว ไม่สามารถฝึก RandomForest ได้")
        return pd.DataFrame({'Feature': features, 'Importance': np.zeros(len(features))})
    
    rf = RandomForestClassifier(n_estimators=100, random_state=42)
    rf.fit(X_train, y_train)
    
    # Feature Importance
    rf_importance = pd.DataFrame({
        'Feature': features,
        'Importance': rf.feature_importances_
    }).sort_values('Importance', ascending=False)
    
    print("\n📊 RandomForest Feature Importance:")
    print(rf_importance.to_string(index=False))
    # print(rf_importance.head(15).to_string(index=False))
    
    # ทำนายและประเมินผล
    y_pred = rf.predict(X_test)
    print("\n📈 RandomForest Performance:")
    print(classification_report(y_test, y_pred))
    
    # บันทึกผลลัพธ์
    rf_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_random_forest_feature_importance.csv")
    rf_importance.to_csv(rf_path, index=False)
    print(f"💾 บันทึก RandomForest importance ที่: {rf_path}")
    
    return rf_importance

"""ตรวจสอบ look-ahead bias ในฟีเจอร์ที่ใช้กับโมเดล"""
def enhanced_look_ahead_check(df, start_index, model_features):
    print(f"\n🏗️ เปิดใช้งาน enhanced look ahead check") if Steps_to_do else None
    print("\n🔍 ตรวจสอบ Look-Ahead Bias แบบละเอียด")
    
    # ตรวจสอบ model_features ก่อนใช้งาน
    if model_features is None:
        print("⚠️ ไม่พบ model_features (None) ใน enhanced look ahead check, ข้ามการตรวจสอบ Look-Ahead Bias")
        return True  # หรือ False ถ้าต้องการหยุด process

    # 1. ตรวจสอบ Features ที่มีความเสี่ยงสูง (อาจจะต้องเพิ่ม Feature ที่แก้ไขไปแล้วทั้งหมด)
    high_risk_features = [
        'Volume_MA20', 'Rolling_Vol_5', 'Rolling_Vol_15', 
        'MA_Cross', 'EMA_diff', 'MACD_12_26_9', 'RSI14', # Feature ที่เคยมีปัญหา
        'STOCHk_14_3_3', 'STOCHd_14_3_3', # Stochastic
        'Upper_BB', 'Lower_BB', 'BB_width', # Bollinger Bands
        'ADX_14', 'DMP_14', 'DMN_14', # ADX
        'ATR', # ATR
        'Support', 'Resistance' # SR
        ] 
    # เพิ่ม Rolling MAs/Stds ที่เพิ่งแก้ไข
    for window in [3, 5, 10, 20]:
        high_risk_features.extend([f'Close_MA_{window}', f'Volume_MA_{window}', f'Close_Std_{window}'])

    # กรองให้เหลือเฉพาะ Feature ที่มีอยู่ใน DataFrame และอยู่ใน model_features
    features_to_check = [feat for feat in high_risk_features if feat in df.columns and feat in model_features]
    
    problematic_features = set()
    
    print(f"กำลังตรวจสอบ Features จำนวน {len(features_to_check)} รายการ...")

    for feat in features_to_check:
        # ตรวจสอบ 5 จุดสำคัญ
        check_points = [
            start_index + i * (len(df) - start_index) // 4 for i in range(5)
        ]
        check_points = [idx for idx in check_points if idx >= start_index + 1 and idx < len(df) - 1] # ตรวจสอบจุดที่เหมาะสม

        # ถ้า check_points ว่าง ให้ข้ามไป
        if not check_points:
            print(f"ข้าม Feature {feat}: ไม่มีจุดให้ตรวจสอบ")
            continue
            
        # print(f"\nตรวจสอบ Feature: {feat}") # เปิดคอมเมนต์นี้เพื่อดูว่ากำลังเช็ค Feature ไหน
        
        flagged_this_feature = False
        for idx in check_points:
            current_val = df[feat].iloc[idx]
            next_val = df[feat].iloc[idx+1]

            # ปรับ tolerance ให้เหมาะสมกับแต่ละฟีเจอร์
            if feat == 'Volume_MA20' or 'Volume_MA_' in feat:
                tol = 0.01  # 1% tolerance สำหรับ Volume
            elif feat in ['EMA_diff', 'ATR', 'BB_width']:
                tol = 1e-4 # tolerance เล็กน้อย
            elif 'EMA' in feat or 'MACD' in feat or 'RSI' in feat or 'STOCH' in feat or 'ADX' in feat or 'Dist_' in feat or 'BB_' in feat or 'Support' in feat or 'Resistance' in feat or 'Close_MA_' in feat or 'Close_Std_' in feat:
                # tolerance สำหรับ indicators ทั่วไปและค่าเฉลี่ย/Std ของราคา
                # คำนวณ relative tolerance จากค่าเฉลี่ยของสองค่า หรือ absolute tolerance ถ้าค่าใกล้ศูนย์
                avg_val = (abs(current_val) + abs(next_val)) / 2
                if avg_val < 1e-6: # ถ้าค่าใกล้ศูนย์มาก ใช้ absolute tolerance
                        tol = 1e-6 # absolute tolerance
                else:
                        tol = 1e-5 # relative tolerance 0.001%
            else: # features อื่นๆ เช่น Bar patterns, IsMorning etc. (ซึ่งไม่ควรมี look-ahead)
                tol = 1e-9 # strict tolerance สำหรับค่า discrete หรือ binary

            # ถ้าค่าที่ index i และ i+1 *เท่ากันเป๊ะ* หรือ *ใกล้เคียงกันมากเกินไป* #               # อาจบ่งชี้ว่าค่าที่ index i ได้ใช้ข้อมูลจาก index i+1 ในการคำนวณ
            if np.isclose(current_val, next_val, rtol=tol, atol=tol): # ใช้ทั้ง relative และ absolute tolerance
                problematic_features.add(feat)
                # print(f" ⚠️ พบความใกล้เคียงที่น่าสงสัยสำหรับ Feature '{feat}' ที่ index {idx}: {current_val:.5f} -> {next_val:.5f} (tol={tol})")
                flagged_this_feature = True # Mark ว่า Feature นี้มีปัญหาแล้ว
                break # หยุดเช็ค Feature นี้ที่จุดอื่น เพื่อไม่ให้ print ซ้ำเยอะเกินไป

        # ถ้าเช็คเกอร์เดิมแจ้งเตือน ⚠️เมื่อค่า *ไม่* ใกล้เคียง (your original logic)
        # if not np.isclose(current_val, next_val, rtol=tol):
        #      problematic_features.add(feat)
        #      change_pct = ((next_val-current_val)/current_val)*100 if current_val != 0 else 0
        #      print(f" ⚠️ index {idx}: {current_val:.5f} -> {next_val:.5f} (เปลี่ยนแปลง {change_pct:.2f}%)")

    # 2. แสดงตัวอย่างการคำนวณ Features (5 แถวแรก)
    print("\nตัวอย่างการคำนวณ Features (5 แถวแรกหลังจาก dropna):")
    # ใช้เฉพาะ Feature ที่เราสนใจและอยู่ใน model_features
    sample_features = [feat for feat in ['Close', 'Volume', 'Volume_MA20', 'EMA50', 'EMA200', 'EMA_diff', 'RSI14', 'Upper_BB', 'ATR', 'Support', 'Close_MA_20'] if feat in df.columns and feat in model_features] # macd_line_col, stoch_k_col,  เอาออกก่อน
    if not sample_features:
        print("ไม่มี Features ที่เลือกไว้สำหรับแสดงตัวอย่าง")
        # อาจจะเลือก Features อื่นๆ แทน
        sample_features = [col for col in df.columns if col not in ['Date', 'Time', 'DateTime', 'Open', 'High', 'Low', 'Close', 'Volume']][:10] # เลือก 10 Features แรกที่เหลือ
        print(f"(แสดงตัวอย่าง 10 Features แรก: {sample_features})")

    # หา index เริ่มต้นหลัง dropna
    start_index_after_dropna = df.index[0]

    for i in range(start_index_after_dropna, min(start_index_after_dropna+3, len(df))): # แสดง 10 แถวแรก
        print(f"\n📌 แถวที่ {i} (เวลา: {df['DateTime'].iloc[i]})")
        for feat in sample_features:
            if feat in df.columns:
                    current_val = df[feat].iloc[i]
                    prev_val = df[feat].iloc[i-1] if i > start_index_after_dropna else None # ใช้ค่าที่ index i-1
                    
                    # แสดงค่าปัจจุบัน
                    current_str = f"{current_val:.5f}" if isinstance(current_val, (int, float)) else str(current_val)
                    
                    # แสดงค่าก่อนหน้า (ที่ index i-1)
                    prev_str = f"{prev_val:.5f}" if prev_val is not None and isinstance(prev_val, (int, float)) else "N/A"
                    
                    print(f"{feat}: {current_str} (ก่อนหน้า: {prev_str})")
            
    if problematic_features:
        print("\n❌ พบ Features ที่น่าสงสัยว่าอาจมี Look-Ahead Bias:")
        for feat in problematic_features:
            print(f"- {feat}")
        print("\n⚠️ โปรดตรวจสอบการคำนวณ Feature เหล่านี้อีกครั้ง โดยเฉพาะการใช้ .shift(1) ที่ผลลัพธ์สุดท้าย")
        return False
    else:
        print("\n✅ ไม่พบ Features ที่น่าสงสัยว่ามี Look-Ahead Bias ในจุดที่ตรวจสอบ")
        return True

"""สร้างและบันทึกกราฟ feature importance ของโมเดล"""
def plot_feature_importance(model, features, model_name, symbol, timeframe, output_folder):
    """ฟังก์ชันย่อยสำหรับพล็อตและบันทึก Feature Importance"""
    print(f"-> กำลังสร้าง Feature Importance สำหรับ {model_name} symbol {symbol} timeframe {timeframe}")
    try:
        # ตรวจสอบว่าโมเดลมีการฝึกแล้วและมี booster_ object
        if not hasattr(model, 'booster_'):
            print("⚠️ โมเดลยังไม่ได้ถูกฝึก หรือไม่มี booster_ object สำหรับดึง Feature Importance แบบละเอียด")
            # ลองใช้ feature_importances_ attribute แทน ถ้ามี
            if hasattr(model, 'feature_importances_') and hasattr(model, 'feature_name_'):
                print("ℹ️ ใช้ .feature_importances_ attribute แทน (อาจไม่ใช่ 'gain' หรือ 'split' แยกกัน)")
                importance_data = {
                    'Feature': model.feature_name_,
                    'Importance': model.feature_importances_ # attribute นี้มักจะเป็น gain หรือ split ขึ้นอยู่กับการตั้งค่าตอน fit
                }
                importance_df = pd.DataFrame(importance_data)

                # Normalize importance scores
                if importance_df['Importance'].sum() > 0:
                    importance_df['Importance'] = importance_df['Importance'] / importance_df['Importance'].sum()
                else:
                    importance_df['Importance'] = 0 # ป้องกันหารด้วยศูนย์

                importance_df = importance_df.sort_values('Importance', ascending=False)

                # แสดงผลเฉพาะค่า Importance ที่ได้จาก attribute
                print("\n📊 Feature Importance (Normalized Scores - From Attribute):")
                print(importance_df.to_string(index=False, float_format="%.4f"))

                # บันทึกเป็น CSV
                csv_path = os.path.join(output_folder, f"{model_name}_{str(timeframe).zfill(3)}_{symbol}_feature_importance_attribute.csv")
                importance_df.to_csv(csv_path, index=False)
                print(f"\n💾 บันทึก Feature Importance ละเอียดที่: {csv_path}")

                # พล็อตกราฟเฉพาะ Importance
                plt.figure(figsize=(10, max(6, len(importance_df.head(20))*0.4))) # ปรับขนาดความสูงตามจำนวน features
                top_n = min(20, len(importance_df))
                plot_df = importance_df.head(top_n).sort_values('Importance', ascending=True).copy() # เรียงจากน้อยไปมากเพื่อบาร์ยาวอยู่ด้านบน

                plt.barh(plot_df['Feature'], plot_df['Importance'], color='skyblue')
                plt.title(f'Top {top_n} Feature Importance - {model_name} symbol {symbol} timeframe {timeframe}', fontsize=14)
                plt.xlabel('Normalized Importance Score', fontsize=12)
                plt.tight_layout()

                # บันทึกรูปภาพ
                img_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_feature_importance_attribute.png")
                plt.savefig(img_path, dpi=150, bbox_inches='tight')
                plt.close() # ปิด figure เพื่อไม่ให้แสดงใน console หรือ memory leak
                print(f"💾 บันทึกกราฟ Feature Importance ที่: {img_path}")

                return importance_df # Return DataFrame even if only one type is available


            else:
                print("⚠️ ไม่พบ attribute 'feature_importances_' หรือ 'feature_name_' ในโมเดล ไม่สามารถดึง Feature Importance ได้")
                return None


        # หากมี booster_ object สามารถดึง importance ได้ทั้ง Gain และ Split
        booster = model.booster_

        # สร้าง DataFrame สำหรับ Feature Importance โดยใช้ booster_ object
        importance_data = {
            # ใช้ booster_.feature_name()
            'Feature': booster.feature_name(),
            # ใช้ booster_.feature_importance() methods
            'Gain': booster.feature_importance(importance_type='gain'),
            'Split': booster.feature_importance(importance_type='split')
        }

        # Normalize importance scores
        importance_df = pd.DataFrame(importance_data)
        # Handle potential division by zero if sums are zero
        if importance_df['Gain'].sum() > 0:
            importance_df['Gain'] = importance_df['Gain'] / importance_df['Gain'].sum()
        else:
            importance_df['Gain'] = 0
        if importance_df['Split'].sum() > 0:
            importance_df['Split'] = importance_df['Split'] / importance_df['Split'].sum()
        else:
            importance_df['Split'] = 0


        # เรียงลำดับตาม Gain (หรือ Split ขึ้นอยู่กับว่าต้องการแสดงอะไรเป็นหลัก)
        importance_df = importance_df.sort_values('Gain', ascending=False)

        # แสดงผลใน Console
        print("\n📊 Feature Importance (Normalized Scores - Gain and Split):")
        print(importance_df.to_string(index=False, float_format="%.4f"))

        # บันทึกเป็น CSV
        csv_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_feature_importance.csv")
        importance_df.to_csv(csv_path, index=False)
        print(f"\n💾 บันทึก Feature Importance ละเอียดที่: {csv_path}")

        # พล็อตกราฟแบบ Side-by-Side
        # plt.figure(figsize=(14, 10)) # ไม่จำเป็นเมื่อใช้ subplots

        # เลือกเฉพาะ Top N Features
        top_n = min(20, len(importance_df))
        plot_df_gain = importance_df.head(top_n).sort_values('Gain', ascending=True).copy() # เรียงจากน้อยไปมากสำหรับกราฟ Gain
        plot_df_split = importance_df.head(top_n).sort_values('Split', ascending=True).copy() # เรียงจากน้อยไปมากสำหรับกราฟ Split


        # สร้างกราฟแบบ Side-by-Side (1 แถว, 2 คอลัมน์)
        # ปรับความสูงตามจำนวน features ที่แสดง
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, max(6, top_n*0.4)))

        # กราฟ Gain Importance
        ax1.barh(plot_df_gain['Feature'], plot_df_gain['Gain'], color='skyblue')
        ax1.set_title('Feature Importance (Gain)', fontsize=14)
        ax1.set_xlabel('Normalized Importance Score', fontsize=12)


        # กราฟ Split Importance
        ax2.barh(plot_df_split['Feature'], plot_df_split['Split'], color='salmon')
        ax2.set_title('Feature Importance (Split)', fontsize=14)
        ax2.set_xlabel('Normalized Importance Score', fontsize=12)

        # ทำให้แกน y เหมือนกันทั้งสองกราฟ เพื่อให้ Feature ตรงกัน
        # หา Features ทั้งหมดที่อยู่ใน Top N ของทั้ง Gain และ Split
        all_top_features = list(set(plot_df_gain['Feature'].tolist() + plot_df_split['Feature'].tolist()))
        all_top_features.sort(key=lambda x: importance_df[importance_df['Feature'] == x]['Gain'].iloc[0]) # อาจเรียงตาม Gain เพื่อให้ consistent

        # ตั้งค่า y-ticks ของทั้งสองแกนให้เป็นชื่อ Features ที่รวมไว้
        ax1.set_yticks(np.arange(len(all_top_features)))
        ax1.set_yticklabels(all_top_features)
        ax2.set_yticks(np.arange(len(all_top_features)))
        ax2.set_yticklabels(all_top_features)


        plt.suptitle(f'Top {top_n} Feature Importance - {model_name} symbol {symbol} timeframe {timeframe}',
                    fontsize=16, y=1.02)
        plt.tight_layout(rect=[0, 0, 1, 0.98]) # ปรับ rect เพื่อให้ suptitle ไม่ทับกราฟ

        # บันทึกรูปภาพ
        img_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_feature_importance.png")
        plt.savefig(img_path, dpi=150, bbox_inches='tight')
        plt.close() # ปิด figure
        print(f"💾 บันทึกกราฟ Feature Importance ที่: {img_path}")

        return importance_df # คืนค่า DataFrame

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะสร้าง Feature Importance: {str(e)}")
        traceback.print_exc() # พิมพ์ traceback เพื่อดูรายละเอียดข้อผิดพลาด
        return None

"""เปรียบเทียบ feature importance ระหว่าง LightGBM และ RandomForest"""
def compare_feature_importance(lgb_importance, rf_importance, symbol, timeframe, output_folder):
    print(f"\n🏗️ เปิดใช้งาน compare feature importance") if Steps_to_do else None

    """เปรียบเทียบ Feature Importance ระหว่าง LightGBM และ RandomForest"""
    try:
        # สร้าง DataFrame สำหรับเปรียบเทียบ
        comparison = pd.merge(
            lgb_importance[['Feature', 'Gain']].rename(columns={'Gain': 'LightGBM'}),
            rf_importance[['Feature', 'Importance']].rename(columns={'Importance': 'RandomForest'}),
            on='Feature',
            how='outer'
        ).fillna(0)
        
        # Normalize importance scores
        comparison['LightGBM'] = comparison['LightGBM'] / comparison['LightGBM'].max()
        comparison['RandomForest'] = comparison['RandomForest'] / comparison['RandomForest'].max()
        
        # เรียงลำดับตาม LightGBM importance
        comparison = comparison.sort_values('LightGBM', ascending=False).head(20)
        
        # พล็อตกราฟ
        plt.figure(figsize=(12, 8))
        comparison.set_index('Feature').plot(kind='barh', color=['skyblue', 'salmon'])
        plt.title(f'Feature Importance Comparison symbol {symbol} timeframe {timeframe}', fontsize=14)
        plt.xlabel('Normalized Importance Score', fontsize=12)
        plt.ylabel('Features', fontsize=12)
        plt.legend(title='Model')
        plt.tight_layout()
        
        # บันทึกรูปภาพ
        comp_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_feature_importance_comparison.png")
        plt.savefig(comp_path, dpi=150, bbox_inches='tight')
        plt.close()
        print(f"💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: {comp_path}")
        
        # บันทึกเป็น CSV
        csv_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_feature_importance_comparison.csv")
        comparison.to_csv(csv_path, index=False)
        print(f"💾 บันทึกตารางเปรียบเทียบที่: {csv_path}")
    
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะเปรียบเทียบ Feature Importance: {str(e)}")

# ==============================================
# trade_cycle.py
# ฟังก์ชันเกี่ยวกับการสร้างรายการซื้อขาย (trade cycles) และ logic การ backtest
# ==============================================

"""ตรวจสอบ look-ahead bias และเตรียม features สำหรับโมเดลในแต่ละแถว"""
def check_look_ahead_bias(df, i, model_features, scaler, nan_count, suspect_feature_count, suspect_features):
    """
    ตรวจสอบ look-ahead bias และเตรียม features สำหรับโมเดลในแต่ละแถว
    - df: DataFrame หลัก
    - i: index ปัจจุบัน
    - model_features: รายชื่อ features ที่โมเดลต้องการ
    - scaler: Scaler ที่ fit แล้ว (StandardScaler)
    - nan_count, suspect_feature_count, suspect_features: ตัวแปรนับและเก็บ features ที่มีปัญหา
    คืนค่า: current_features_data, scaled_features_df, nan_count, suspect_feature_count, suspect_features
    """
    # 1. กำหนด features ที่จะใช้ (ใช้จาก scaler ถ้ามี feature_names_in_)
    features_for_model = model_features
    if scaler is not None and hasattr(scaler, 'feature_names_in_'):
        features_for_model = scaler.feature_names_in_

    # 2. เตรียมข้อมูล features ของแถว i (ต้อง reindex ให้ครบทุก features)
    current_features_data = df.loc[[i], features_for_model]
    current_features_data = current_features_data.reindex(columns=features_for_model, fill_value=0)

    # 3. ตรวจสอบ NaN
    nan_features = current_features_data.columns[current_features_data.isna().any()].tolist()
    if nan_features:
        nan_count += 1
        suspect_features.update(nan_features)

    # 4. ทำ scaling
    scaled_features_array = None
    try:
        if scaler:
            scaled_features_array = scaler.transform(current_features_data)
        else:
            scaled_features_array = current_features_data.values
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะ scaling features ที่แถว {i}: {str(e)}")
        import traceback
        traceback.print_exc()
        scaled_features_array = current_features_data.values
        suspect_feature_count += 1

    # 5. แปลงกลับเป็น DataFrame (ใช้ชื่อ columns จาก features_for_model)
    if scaled_features_array is not None:
        scaled_features_df = pd.DataFrame(scaled_features_array, index=current_features_data.index, columns=features_for_model)
    else:
        scaled_features_df = pd.DataFrame(columns=features_for_model)

    return current_features_data, scaled_features_df, nan_count, suspect_feature_count, suspect_features

"""สร้างรายการซื้อขาย (trade cycles) โดยใช้โมเดล ML ช่วยตัดสินใจเข้า/ออกเทรด"""
def create_trade_cycles_with_model(
        df, trained_model=None, scaler=None, model_features=None,
        rsi_level=input_rsi_level_in, rsi_level_out=input_rsi_level_out, 
        stop_loss_atr_multiplier=input_stop_loss_atr, take_profit_stop_loss_ratio=input_take_profit, 
        symbol=None, timeframe=None, identifier=None, 
        nBars_SL=None, model_confidence_threshold=None, 
        entry_condition_func=None, entry_condition_name=None
        ):
    print(f"\n🏗️ เปิดใช้งาน create trade cycles with model") if Steps_to_do else None

    """
    สร้างรายการซื้อขายด้วยเงื่อนไขทางเทคนิค โดยใช้ Model ที่เทรนแล้วช่วยตัดสินใจ
    Args:
        df (pd.DataFrame): DataFrame ที่มีข้อมูล OHLC, Indicators, และ Features
        trained model: โมเดล ML ที่เทรนแล้ว (e.g., LightGBMClassifier)
        scaler: Scaler ที่ใช้ scale features สำหรับโมเดล (e.g., StandardScaler)
        model_features (list): รายชื่อ features ที่โมเดลคาดหวัง
        model confidence threshold (float): เกณฑ์ความน่าจะเป็น (0-1) สำหรับการเข้าเทรด
        ... (parameters อื่นๆ เหมือนเดิม) ...
    """
    # แสดงชื่อคอลัมน์ของ DataFrame ใน pandas
    # print(df.columns.tolist())
    # แบบทีละบรรทัด
    # for col in df.columns:
    #     print(col)

    is_production = False  # หรือ True ถ้าต้องการใช้งานจริง
    if is_production:
        time_filters = load_time_filters(symbol, timeframe)
    else:
        time_filters = {'days': list(range(7)), 'hours': list(range(24))}
    # 'days': [0,1,2], 'hours': [7,8,9] = เปิดเฉพาะวันจันทร์-พุธ และเฉพาะ 7-9 โมงเช้า
    # 'days': [], 'hours': [] = เปิดได้ทุกวัน ทุกชั่วโมง

    trades = []
    in_trade_buy = False
    in_trade_sell = False
    entry_price_buy = entry_price_sell = None
    entry_time_buy = entry_time_sell = None
    trade_type_buy = trade_type_sell = None

    nan_count = 0
    suspect_feature_count = 0
    suspect_features = set()

    symbol_spread = symbol_info[symbol]["Spread"]
    symbol_digits = symbol_info[symbol]["Digits"]
    symbol_points = symbol_info[symbol]["Points"]

    stats = {
        'buy': {'total': 0, 'profit_sum': 0},
        'sell': {'total': 0, 'profit_sum': 0},
        'day_stats': {i: {'win': 0, 'loss': 0, 'total': 0} for i in range(7)},  # 0=Monday, 6=Sunday
        'hour_stats': {i: {'win': 0, 'loss': 0, 'total': 0} for i in range(24)}
    }

    # ตรวจสอบว่ามี Model, Scaler, Features พร้อมหรือไม่
    # use_model_for_decision = False
    use_model_for_decision = (trained_model is not None and scaler is not None and model_features is not None and len(model_features) > 0)

    if use_model_for_decision:
        print("\n🤖 จะใช้ Model ML ช่วยในการตัดสินใจเข้าเทรด")
        print(f"📊 Features ที่ Model ใช้: {model_features}")
        print(f"🎯 เกณฑ์ความน่าจะเป็น Model (TP Hit): > {model_confidence_threshold:.4f}")
    else:
        print("❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม")

    # กำหนดชื่อไฟล์ log โดยใช้ timeframe
    log_file_name = f"Test_LightGBM/{str(timeframe).zfill(3)}_{symbol}_trade_log_model_enhanced.txt" if timeframe else "trade_log_model_enhanced.txt"

    # เปิดไฟล์สำหรับบันทึกข้อมูล
    with open(log_file_name, "w") as log_file:
        # เริ่ม Loop ตั้งแต่ index ที่พอจะคำนวณ Indicator ต่างๆ ได้ครบ
        # ต้องเผื่อสำหรับ Indicators ที่ต้องใช้ข้อมูลย้อนหลัง และเผื่อสำหรับ SL/TP prev bars (index i-2)
        # ดังนั้น เริ่ม loop ที่ index ที่มากพอ เช่น 200 หรือมากกว่า
        start_index = max(300, df.first_valid_index() or 0) # เลือกค่าที่มากสุดระหว่าง 200 กับ index แรกที่มีข้อมูล

        if 'DateTime' not in df.columns:
            if 'Entry Time' in df.columns:
                df = df.copy()
                df['DateTime'] = pd.to_datetime(df['Entry Time'], errors='coerce')
            else:
                print("⚠️ ไม่พบคอลัมน์ DateTime หรือ Entry Time ใน DataFrame, ข้ามการแสดงช่วงเวลา")
                df['DateTime'] = pd.NaT

        if start_index >= len(df):
            print(f"⚠️ start_index ({start_index}) >= จำนวนแถวใน df ({len(df)}) ข้ามการ backtest")
            return pd.DataFrame(), {}

        print("\n🔍 ตรวจสอบ Temporal Dependence สำหรับการเทรด")
        start_time = df['DateTime'].iloc[start_index]
        end_time = df['DateTime'].iloc[-1]
        print(f"- ช่วงเวลาที่จะทำ backtest: {start_time} ถึง {end_time}")
        print(f"- จำนวนแท่งข้อมูลทั้งหมดสำหรับ backtest: {len(df) - start_index}")
        
        # ตรวจสอบความถี่ของการเทรด
        trade_freq = df['DateTime'].diff().value_counts().head(5)
        print("\nความถี่ของช่วงเวลาระหว่างแท่งข้อมูล:")
        print(trade_freq)
        
        # ตรวจสอบความสัมพันธ์ระหว่างเวลาและผลลัพธ์การเทรด (ตัวอย่าง)
        if 'Target' in df.columns:
            hour_profit = df.groupby(df['DateTime'].dt.hour)['Target'].mean()
            print("\nอัตราการชน TP (Win Rate) แยกตามชั่วโมง:")
            print(hour_profit)

        print(f"\n▶️ เริ่ม Backtest จาก index: {start_index} (เพื่อให้ Indicators คำนวณได้ครบ)")

        # ส่วนที่เรียกใช้การตรวจสอบในโค้ด Backtest
        # ...
        # start_index = ... # กำหนด index ที่จะเริ่ม backtest หรือ index แรกหลัง dropna
        # model_features = ... # กำหนด list ของ features ที่ใช้ใน model
        # ...
        print("\n🔍 ตรวจสอบ Look-Ahead Bias ก่อนเริ่ม Backtest")
        try:
            # ตรวจสอบตั้งแต่ index แรกหลัง dropna
            start_index_for_check = df.index[0]

            look_ahead_ok = True  # กำหนดค่า default
            if model_features is not None:
                look_ahead_ok = enhanced_look_ahead_check(df, start_index_for_check, model_features)
            else:
                print("⚠️ ไม่พบ model_features ข้ามการตรวจสอบ Look-Ahead Bias")

            if not look_ahead_ok:
                print("\n❌ พบปัญหา Look-Ahead Bias ใน Features ที่ระบุ")
                print("โปรดแก้ไขการคำนวณ Features เหล่านั้นก่อนดำเนินการต่อ")
                # return pd.DataFrame(), {}  # ยกเลิกการ Backtest ถ้าพบปัญหา
                # หรือจะเลือกที่จะดำเนินการต่อแต่มีคำเตือน ⚠️
                # raise ValueError("Look-Ahead Bias detected in features.") # อาจจะ raise Exception เพื่อหยุด Backtest
                # ในตัวอย่างนี้ แค่ print เตือน ⚠️แต่ยังให้โค้ดทำงานต่อได้ (ไม่แนะนำสำหรับการ Backtest จริงจัง)
            else:
                print("\n✅ การตรวจสอบ Look-Ahead Bias เสร็จสมบูรณ์ ไม่มีปัญหาที่ตรวจพบโดยอัตโนมัติ")

        except Exception as e:
            print(f"⚠️ เกิดข้อผิดพลาด ขณะตรวจสอบ Look-Ahead Bias: {str(e)}")
            import traceback
            traceback.print_exc()
            # return pd.DataFrame(), {} # ยกเลิกการ Backtest ถ้าเกิดข้อผิดพลาด ⚠️ ในการตรวจสอบ
            # ในตัวอย่างนี้ แค่ print error แต่ยังให้โค้ดทำงานต่อได้ (ไม่แนะนำสำหรับการ Backtest จริงจัง)

        if use_model_for_decision:
            print(f"\n🤖 จะใช้ Model ML ช่วยในการตัดสินใจเข้าเทรด {timeframe} {symbol}")
            print(f"📊 Features ที่ Model ใช้: {model_features}")
            print(f"🎯 เกณฑ์ความน่าจะเป็น Model (TP Hit): > {model_confidence_threshold:.3f} รอบที่ {identifier} / {NUM_TRAINING_ROUNDS}")
        else:
            print("\n❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม")

        # กำหนดชื่อไฟล์ log โดยใช้ timeframe
        log_file_name = f"Test_LightGBM/{str(timeframe).zfill(3)}_{symbol}_trade_log_model_enhanced.txt" if timeframe else "trade_log_model_enhanced.txt"

        for i in range(start_index, len(df)):
            # current_time refers to the time of the bar 'i' where the potential entry is made at Open[i]
            if 'Date' in df.columns and 'Time' in df.columns:
                current_time = pd.to_datetime(df['Date'].iloc[i] + ' ' + df['Time'].iloc[i])
            elif 'DateTime' in df.columns:
                current_time = df['DateTime'].iloc[i]
            else:
                current_time = pd.NaT  # หรือ raise Exception("No valid datetime columns found")

            hour = current_time.hour
            day_of_week = df['Entry_DayOfWeek'].iloc[i] # This can use data from bar i, as DayOfWeek is known

            # --- Check if we have enough previous data for lookback periods ---
            # This check depends on the maximum lookback needed for your indicators/features + SL calculation
            min_lookback = 3 # Minimum bars needed for SL (i-3, i-2, i-1) + features/indicators lookback
            if i < start_index + min_lookback:
                continue # Skip if not enough previous data

            # --- Data from the PREVIOUS completed bar (i-1) ---
            prev_atr = df['ATR'].iloc[i-1] # Assuming ATR is calculated based on previous bars

            # ป้องกันกรณี time_filters ไม่มี key หรือเป็น list ว่าง
            days_filter = time_filters.get('days', list(range(7)))
            hours_filter = time_filters.get('hours', list(range(24)))
            if not days_filter:
                days_filter = list(range(7))
            if not hours_filter:
                hours_filter = list(range(24))

            # เงื่อนไขเวลาที่ปรับปรุงแล้ว (ใช้เวลาของแท่งปัจจุบัน i ซึ่งทราบแล้ว)
            time_condition = (
                4 <= hour < 22 and
                day_of_week in days_filter and
                hour in hours_filter
            )

            # ตรวจสอบชื่อคอลัมน์ก่อนสร้าง prev_dict
            required_cols = ['Close', 'Open', 'EMA50', 'EMA200', 'MACD_signal', 'RSI14', 'Volume', 'Volume_MA20', 'PullBack_Up', 'Ratio_Buy', 'PullBack_Down', 'Ratio_Sell']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                print(f"⚠️ ขาดคอลัมน์ที่จำเป็นใน create trade cycles with model : df: {missing_cols} (index {i})")
                continue  # ข้ามรอบนี้

            prev_dict = {
                'close': df['Close'].iloc[i-1],
                'open': df['Open'].iloc[i-1],
                'ema50': df['EMA50'].iloc[i-1],
                'ema200': df['EMA200'].iloc[i-1],
                'macd_signal': df['MACD_signal'].iloc[i-1],
                'rsi14': df['RSI14'].iloc[i-1],
                'volume': df['Volume'].iloc[i-1],
                'volume_ma20': df['Volume_MA20'].iloc[i-1],
                'pullback_buy': df['PullBack_Up'].iloc[i-1],
                'ratio_buy': df['Ratio_Buy'].iloc[i-1],
                'pullback_sell': df['PullBack_Down'].iloc[i-1],
                'ratio_sell': df['Ratio_Sell'].iloc[i-1],
                # เพิ่ม field อื่นๆ ตามที่ entry_func ต้องใช้
            }

            # --- Technical Signals (using data from the PREVIOUS completed bar i-1) ---
            if not in_trade_buy: # Signal Combination
                
                if entry_condition_func is not None:
                    tech_signal_buy = entry_condition_func['buy'](prev_dict)
                else:
                    tech_signal_buy = (
                        prev_dict['close'] > prev_dict['open'] and # Previous bar closed higher
                        prev_dict['rsi14'] > rsi_level and # prev_sto_cross == 1.0 and
                        prev_dict['macd_signal'] == 1.0 and
                        prev_dict['volume'] > prev_dict['volume_ma20'] * 0.8 and
                        prev_dict['pullback_buy'] > input_pull_back and
                        prev_dict['ratio_buy'] > (take_profit_stop_loss_ratio * 3.0)
                    )

                # ตรวจสอบสัญญาณเข้าซื้อ (อิงจากข้อมูลแท่ง i-1 และเวลากับวันของแท่ง i)
                if tech_signal_buy and time_condition:

                    # --- ใช้ Model ML ช่วยตัดสินใจ (ถ้ามี) ---
                    model_decision_buy = True # Default: ถ้าไม่ใช้โมเดล ให้ตัดสินใจเข้าตามกฎเดิม
                    prob_win = -1 # Initialize prob_win

                    if use_model_for_decision:
                        try:
                            # ==============================================
                            # ส่วนเพิ่มเติม: ตรวจสอบ Look-Ahead Bias ก่อนทำนาย
                            # ใช้ index 'i' ในการเรียก แต่ check look ahead bias
                            # จะดึงข้อมูลจาก df.iloc[i-1] และก่อนหน้า
                            # ==============================================
                            current_features_data, scaled_features, nan_count, suspect_feature_count, suspect_features = check_look_ahead_bias(
                                df, i, model_features, scaler, nan_count, suspect_feature_count, suspect_features)

                            # ทำนายความน่าจะเป็น
                            prediction_proba = trained_model.predict_proba(scaled_features)[0]
                            prob_win = prediction_proba[1] # ความน่าจะเป็นของ Target=1 (TP Hit)

                            # ตรวจสอบเกณฑ์ความน่าจะเป็น + High-Quality Entry Filter
                            basic_model_decision = (prob_win > model_confidence_threshold)
                            high_quality_check = is_high_quality_entry(df, i, symbol)
                            model_decision_buy = basic_model_decision and high_quality_check

                            # Debug info สำหรับ high-quality filter
                            if basic_model_decision and not high_quality_check:
                                print(f"  🚫 Filtered out low-quality signal at {df['Date'].iloc[i]} {df['Time'].iloc[i]} (prob={prob_win:.3f})")
                            elif model_decision_buy:
                                print(f"  ✅ High-quality BUY signal at {df['Date'].iloc[i]} {df['Time'].iloc[i]} (prob={prob_win:.3f})")

                            # ==============================================
                            # ส่วนเพิ่มเติม: บันทึกข้อมูลการทำนายเพื่อตรวจสอบ
                            # ==============================================
                            # บันทึกข้อมูลแท่งที่ใช้คำนวณ (แท่ง i-1) และแท่งปัจจุบัน (แท่ง i)
                            # print(f"--- Decision at {df['Date'].iloc[i]} {df['Time'].iloc[i]} (Open of Bar i) ---")
                            # print(f"Using data from bar i-1: {df['Date'].iloc[i-1]} {df['Time'].iloc[i-1]} (Closed)")
                            # print(f"  Close[i-1]: {prev_close}, Open[i-1]: {prev_open}, High[i-1]: {prev_high}, Low[i-1]: {prev_low}")
                            # print(f"  Indicators [i-1]: EMA50={prev_ema50:.2f}, RSI14={prev_rsi14:.2f}, MACD_signal={prev_macd_signal}, STO_cross={prev_sto_cross}")
                            # print(f"  Features for Model (from i-1 and earlier): {current_features_data.iloc[0].to_dict()}")
                            # print(f"Model Probability (Win): {prob_win:.4f}. Threshold: {model_confidence_threshold:.3f}. Decision: {'ENTER' if model_decision_buy else 'SKIP'}")
                            # print(f"Current Bar (i) Open: {df['Open'].iloc[i]}") # Show the open price where entry would occur
                            # print("-" * 20)

                            if i % 100 == 0:
                                log_entry = {
                                    'index_current_bar': i,
                                    'time_current_bar': str(df['Date'].iloc[i] + ' ' + df['Time'].iloc[i]),
                                    'index_features_bar': i-1,
                                    'time_features_bar': str(df['Date'].iloc[i-1] + ' ' + df['Time'].iloc[i-1]),
                                    'prob_win': float(prob_win),
                                    'decision': bool(model_decision_buy),
                                    'features_from_bar_i_minus_1': {k: safe_json_serialize(v) for k, v in current_features_data.iloc[0].to_dict().items()}
                                }
                                with open(f"Test_LightGBM/{str(timeframe).zfill(3)}_{symbol}_prediction_log.json", "a") as log:
                                    log.write(json.dumps(log_entry) + "\n")

                            # (Optional) Log การตัดสินใจของ Model
                            log_file.write(f"[{df['Date'].iloc[i]} {df['Time'].iloc[i]}] BUY Signal Detected based on data up to [{df['Date'].iloc[i-1]} {df['Time'].iloc[i-1]}]. Model Probability (Win): {prob_win:.4f}. Threshold: {model_confidence_threshold:.3f}. Decision: {'ENTER' if model_decision_buy else 'SKIP'}\n")

                        except Exception as e:
                            print(f"⚠️ เกิดข้อผิดพลาด ขณะใช้ Model ทำนาย (Buy) ที่แท่ง {df['Date'].iloc[i]} {df['Time'].iloc[i]}: {str(e)}. ข้ามการใช้ Model.")
                            traceback.print_exc()
                            model_decision_buy = True # กลับไปใช้กฎเดิมถ้า Model มีปัญหา
                            prob_win = -2 # Indicate error

                    # --- ถ้าเงื่อนไขทางเทคนิคและ Model อนุญาต (หรือถ้าไม่ใช้ Model) ---
                    if model_decision_buy:
                        
                        entry_price_buy = df["Open"].iloc[i] + (symbol_spread * symbol_points)
                        entry_time_buy = df["Date"].iloc[i] + " " + df["Time"].iloc[i]
                        trade_type_buy = "Buy"

                        # คำนวณ SL/TP
                        sl_atr = entry_price_buy - stop_loss_atr_multiplier * prev_atr # ใช้ ATR จากแท่ง i-1
                        # SL คำนวณจาก Low ของ 3 แท่งก่อนหน้าแท่งปัจจุบัน i (คือ Low[i-3], Low[i-2], Low[i-1])
                        # นี้คือการใช้ข้อมูลที่ทราบแล้ว ณ เวลาเข้าเทรด Open[i]
                        sl_prev_bars = min(df['Low'].iloc[i-nBars_SL:i].min(), entry_price_buy - 2*symbol_points)

                        # แสดงข้อมูลแท่งที่ใช้คำนวณ SL_prev_bars
                        # print(f"Calculating SL_prev_bars for entry at {entry_time_buy} @ {entry_price_buy:.5f}")
                        # print(f"Using Low from bars: {df['Time'].iloc[i-3]}, {df['Time'].iloc[i-2]}, {df['Time'].iloc[i-1]}")
                        # print(f"Low values: {df['Low'].iloc[i-3]:.5f}, {df['Low'].iloc[i-2]:.5f}, {df['Low'].iloc[i-1]:.5f}")
                        # print(f"Min Low of these bars: {df['Low'].iloc[i-3:i].min():.5f}")
                        # print(f"Calculated sl_prev_bars: {sl_prev_bars:.5f}")
                        # print("-" * 20)

                        if 'Support' in df.columns and not pd.isna(df['Support'].iloc[i-1]): # ใช้ Support จากแท่ง i-1
                            sl_support = df['Support'].iloc[i-1]
                            sl_price_buy = max(sl_atr, sl_prev_bars, sl_support)
                        else:
                            sl_price_buy = max(sl_atr, sl_prev_bars)
                        
                        sl_price_buy = floor_price(sl_price_buy, symbol_points)

                        tp_price_buy = entry_price_buy + (entry_price_buy - sl_price_buy) * take_profit_stop_loss_ratio

                        tp_price_buy = ceiling_price(tp_price_buy, symbol_points)

                        # if 'Resistance' in df.columns and not pd.isna(df['Resistance'].iloc[i-1]): # ใช้ Resistance จากแท่ง i-1
                        #     tp_resistance = df['Resistance'].iloc[i-1]
                        #     tp_price_buy = min(tp_price_buy, tp_resistance)

                        if tp_price_buy <= entry_price_buy:
                            tp_price_buy = entry_price_buy + 2*symbol_points # ตั้ง TP ขั้นต่ำเพื่อหลีกเลี่ยง TP <= Entry

                        in_trade_buy = True

                        # บันทึกข้อมูล ณ เวลาที่เข้าซื้อ (Features ที่ใช้ทำนาย)
                        # บันทึก features ที่ได้มาจาก check look ahead bias ซึ่งใช้ข้อมูลจาก i-1
                        atr_entry_buy = df['ATR'].iloc[i-1]
                        bb_width_entry_buy = df['BB_width'].iloc[i-1]
                        rsi14_entry_buy = df['RSI14'].iloc[i-1]
                        volatility_entry_buy = df['Volume_MA20'].iloc[i-1]
                        volume_spike_entry_buy = df['Volume_Spike'].iloc[i-1]

                        # คุณอาจต้องการเก็บค่า prob_win ที่ใช้ตัดสินใจเข้า trade นี้ด้วย
                        prob_win_at_entry_buy = prob_win

                        log_message = f"[{df['Date'].iloc[i]} {df['Time'].iloc[i]}] Open Order : trade_{trade_type_buy} : Entry_time_{entry_time_buy} Entry_price_{entry_price_buy:0.5f} Entry_sl_{sl_price_buy:0.5f} Entry_tp_{tp_price_buy:0.5f} Prob_Win_{prob_win_at_entry_buy:.4f}\n"
                        log_file.write(log_message)

            # --- เงื่อนไขการออกจากตำแหน่งซื้อ (Buy) ---
            # (โค้ดส่วนนี้เหมือนเดิม ตรวจสอบ SL/TP/Technical Exit)
            if in_trade_buy and trade_type_buy == "Buy":
                exit_condition = None
                exit_price = None

                # ระดับที่ต้องไปถึงก่อนเลื่อน SL
                high_prev = df["High"].iloc[i-1] if i > 0 else None
                trail_trigger = entry_price_buy + 0.5 * (entry_price_buy - sl_price_buy)

                if high_prev is not None and high_prev >= trail_trigger:
                    if sl_price_buy < entry_price_buy:  # เพื่อไม่ให้ปรับซ้ำซ้อน
                        sl_price_buy = entry_price_buy
                        # สามารถ log ได้ว่า "SL moved to Entry"

                if df["Low"].iloc[i] <= sl_price_buy:
                    exit_price = sl_price_buy
                    exit_condition = "SL Hit"
                elif df["High"].iloc[i] > tp_price_buy:
                    exit_price = tp_price_buy
                    exit_condition = "TP Hit"
                # เงื่อนไขอื่นๆ (Technical Exit)
                elif (df["Close"].iloc[i] < df["EMA50"].iloc[i] if "EMA50" in df.columns else False or # ตรวจสอบคอลัมน์ก่อนใช้
                    df["RSI14"].iloc[i] < rsi_level_out if "RSI14" in df.columns else False): # (df["Close"].iloc[i] < entry_price_buy * 0.998)
                    exit_price = df["Close"].iloc[i]
                    exit_condition = "Technical Exit"

                # ถ้ามีเงื่อนไขออกเกิดขึ้น
                if exit_condition:
                    # ... (ส่วนบันทึก Trade และอัปเดต Stats เหมือนเดิม) ...
                    exit_time = df["Date"].iloc[i] + " " + df["Time"].iloc[i]
                    profit = (exit_price - entry_price_buy) / symbol_points

                    risk_amount = entry_price_buy - sl_price_buy
                    reward_amount = tp_price_buy - entry_price_buy # ใช้ tp_price_buy ที่คำนวณหรือเลือกไว้

                    entry_datetime = pd.to_datetime(entry_time_buy)
                    entry_hour = entry_datetime.hour
                    entry_day = entry_datetime.dayofweek

                    log_message = f"time_{df['Date'].iloc[i]} {df['Time'].iloc[i]} op_{df['Open'].iloc[i]} cl_{df['Close'].iloc[i]} : trade_{trade_type_buy} : Entry_time_{entry_time_buy} Entry_price_{entry_price_buy:0.5f} Entry_sl_{sl_price_buy:0.5f} Entry_tp_{tp_price_buy:0.5f} : Exit_time_{exit_time} Exit_price_{exit_price:0.5f} : Condition_{exit_condition} Profit_{profit:0.0f}\n"
                    log_file.write(log_message)

                    trades.append([
                        entry_time_buy, entry_price_buy,
                        exit_time, exit_price,
                        profit, trade_type_buy,
                        entry_hour, entry_day,
                        exit_condition,
                        sl_price_buy, tp_price_buy,
                        risk_amount, reward_amount,
                        atr_entry_buy,
                        bb_width_entry_buy,
                        rsi14_entry_buy,
                        (entry_price_buy - sl_price_buy) / entry_price_buy if risk_amount > 0 else 0, # % Risk (ป้องกันหารด้วยศูนย์)
                        (tp_price_buy - entry_price_buy) / entry_price_buy if reward_amount > 0 else 0, # % Reward (ป้องกันหารด้วยศูนย์)
                        volatility_entry_buy, volume_spike_entry_buy
                    ])

                    # อัปเดตสถิติ
                    stats['buy']['total'] += 1
                    stats['buy']['profit_sum'] += profit
                    stats['day_stats'][entry_day]['total'] += 1
                    stats['hour_stats'][entry_hour]['total'] += 1
                    if profit > 0:
                        stats['day_stats'][entry_day]['win'] += 1
                        stats['hour_stats'][entry_hour]['win'] += 1
                    elif profit < 0:
                        stats['day_stats'][entry_day]['loss'] += 1
                        stats['hour_stats'][entry_hour]['loss'] += 1

                    in_trade_buy = False # ออกจากตำแหน่ง

            # --- เงื่อนไขการเข้าขาย (Sell) ---
            if not in_trade_sell:

                if entry_condition_func is not None:
                    tech_signal_sell = entry_condition_func['sell'](prev_dict)
                else:
                    tech_signal_sell = (
                        prev_dict['close'] < prev_dict['open'] and
                        prev_dict['rsi14'] > (100 - rsi_level) and
                        prev_dict['macd_signal'] == -1.0 and
                        prev_dict['volume'] > prev_dict['volume_ma20'] * 0.8 and
                        prev_dict['pullback_sell'] > input_pull_back and
                        prev_dict['ratio_sell'] > (take_profit_stop_loss_ratio * 3.0)
                    )

                if tech_signal_sell and time_condition:

                    model_decision_sell = True
                    prob_win = -1

                    if use_model_for_decision:
                        try:
                            current_features_data, scaled_features, nan_count, suspect_feature_count, suspect_features = check_look_ahead_bias(
                                df, i, model_features, scaler, nan_count, suspect_feature_count, suspect_features)

                            prediction_proba = trained_model.predict_proba(scaled_features)[0]
                            prob_win = prediction_proba[1]

                            # ตรวจสอบเกณฑ์ความน่าจะเป็น + High-Quality Entry Filter
                            basic_model_decision = (prob_win > model_confidence_threshold)
                            high_quality_check = is_high_quality_entry(df, i, symbol)
                            model_decision_sell = basic_model_decision and high_quality_check

                            # Debug info สำหรับ high-quality filter
                            if basic_model_decision and not high_quality_check:
                                print(f"  🚫 Filtered out low-quality signal at {df['Date'].iloc[i]} {df['Time'].iloc[i]} (prob={prob_win:.3f})")
                            elif model_decision_sell:
                                print(f"  ✅ High-quality SELL signal at {df['Date'].iloc[i]} {df['Time'].iloc[i]} (prob={prob_win:.3f})")
                            
                            # print(f"--- Decision at {df['Date'].iloc[i]} {df['Time'].iloc[i]} (Open of Bar i) ---")
                            # print(f"Using data from bar i-1: {df['Date'].iloc[i-1]} {df['Time'].iloc[i-1]} (Closed)")
                            # print(f"  Close[i-1]: {prev_close}, Open[i-1]: {prev_open}, High[i-1]: {prev_high}, Low[i-1]: {prev_low}")
                            # print(f"  Indicators [i-1]: EMA50={prev_ema50:.2f}, RSI14={prev_rsi14:.2f}, MACD_signal={prev_macd_signal}, STO_cross={prev_sto_cross}")
                            # print(f"  Features for Model (from i-1 and earlier): {current_features_data.iloc[0].to_dict()}")
                            # print(f"Model Probability (Win): {prob_win:.4f}. Threshold: {model_confidence_threshold:.3f}. Decision: {'ENTER' if model_decision_sell else 'SKIP'}")
                            # print(f"Current Bar (i) Open: {df['Open'].iloc[i]}")
                            # print("-" * 20)

                            if i % 100 == 0:
                                log_entry = {
                                    'index_current_bar': i,
                                    'time_current_bar': str(df['Date'].iloc[i] + ' ' + df['Time'].iloc[i]),
                                    'index_features_bar': i-1,
                                    'time_features_bar': str(df['Date'].iloc[i-1] + ' ' + df['Time'].iloc[i-1]),
                                    'prob_win': float(prob_win),
                                    'decision': bool(model_decision_sell),
                                    'features_from_bar_i_minus_1': {k: safe_json_serialize(v) for k, v in current_features_data.iloc[0].to_dict().items()}
                                }
                                with open(f"Test_LightGBM/{str(timeframe).zfill(3)}_{symbol}_prediction_log.json", "a") as log:
                                    log.write(json.dumps(log_entry) + "\n")

                            log_file.write(f"[{df['Date'].iloc[i]} {df['Time'].iloc[i]}] sell Signal Detected based on data up to [{df['Date'].iloc[i-1]} {df['Time'].iloc[i-1]}]. Model Probability (Win): {prob_win:.4f}. Threshold: {model_confidence_threshold:.3f}. Decision: {'ENTER' if model_decision_sell else 'SKIP'}\n")

                        except Exception as e:
                            print(f"⚠️ เกิดข้อผิดพลาด ขณะใช้ Model ทำนาย (sell) ที่แท่ง {df['Date'].iloc[i]} {df['Time'].iloc[i]}: {str(e)}. ข้ามการใช้ Model.")
                            traceback.print_exc()
                            model_decision_sell = True
                            prob_win = -2

                    if model_decision_sell:
                        entry_price_sell = df["Open"].iloc[i]
                        entry_time_sell = df["Date"].iloc[i] + " " + df["Time"].iloc[i]
                        trade_type_sell = "Sell"

                        sl_atr = entry_price_sell + stop_loss_atr_multiplier * prev_atr
                        sl_prev_bars = max(df['High'].iloc[i-nBars_SL:i].max(), entry_price_sell + 2*symbol_points)

                        # print(f"Calculating SL_prev_bars for entry at {entry_time_sell} @ {entry_price_sell:.5f}")
                        # print(f"Using High from bars: {df['Time'].iloc[i-3]}, {df['Time'].iloc[i-2]}, {df['Time'].iloc[i-1]}")
                        # print(f"High values: {df['High'].iloc[i-3]:.5f}, {df['High'].iloc[i-2]:.5f}, {df['High'].iloc[i-1]:.5f}")
                        # print(f"Max Low of these bars: {df['High'].iloc[i-3:i].max():.5f}")
                        # print(f"Calculated sl_prev_bars: {sl_prev_bars:.5f}")
                        # print("-" * 20)

                        if 'Resistance' in df.columns and not pd.isna(df['Resistance'].iloc[i-1]):
                            sl_resistance = df['Resistance'].iloc[i-1]
                            sl_price_sell = min(sl_atr, sl_prev_bars, sl_resistance) + (symbol_spread * symbol_points)
                        else:
                            sl_price_sell = min(sl_atr, sl_prev_bars) + (symbol_spread * symbol_points)

                        sl_price_sell = ceiling_price(sl_price_sell, symbol_points)

                        tp_price_sell = entry_price_sell - (sl_price_sell - entry_price_sell) * take_profit_stop_loss_ratio

                        tp_price_sell = floor_price(tp_price_sell, symbol_points)

                        # if 'Support' in df.columns and not pd.isna(df['Support'].iloc[i-1]):
                        #     tp_support = df['Support'].iloc[i-1]
                        #     tp_price_sell = max(tp_price_sell, tp_support)

                        if tp_price_sell >= entry_price_sell:
                            tp_price_sell = entry_price_sell - 2*symbol_points

                        in_trade_sell = True

                        atr_entry_sell = df['ATR'].iloc[i-1]
                        bb_width_entry_sell = df['BB_width'].iloc[i-1]
                        rsi14_entry_sell = df['RSI14'].iloc[i-1]
                        volatility_entry_sell = df['Volume_MA20'].iloc[i-1]
                        volume_spike_entry_sell = df['Volume_Spike'].iloc[i-1]

                        prob_win_at_entry_sell = prob_win

                        log_message = f"[{df['Date'].iloc[i]} {df['Time'].iloc[i]}] Open Order : trade_{trade_type_sell} : Entry_time_{entry_time_sell} Entry_price_{entry_price_sell:0.5f} Entry_sl_{sl_price_sell:0.5f} Entry_tp_{tp_price_sell:0.5f} Prob_Win_{prob_win_at_entry_sell:.4f}\n"
                        log_file.write(log_message)

            if in_trade_sell and trade_type_sell == "Sell":
                exit_condition = None
                exit_price = None

                # ระดับที่ต้องไปถึงก่อนเลื่อน SL
                low_prev = df["Low"].iloc[i-1] if i > 0 else None
                trail_trigger = entry_price_sell - 0.5 * (sl_price_sell - entry_price_sell)

                if low_prev is not None and low_prev + (symbol_spread * symbol_points) <= trail_trigger:
                    if sl_price_sell > entry_price_sell:  # เพื่อไม่ให้ปรับซ้ำซ้อน
                        sl_price_sell = entry_price_sell
                        # สามารถ log ได้ว่า "SL moved to Entry"

                if df["High"].iloc[i] + (symbol_spread * symbol_points) >= sl_price_sell:
                    exit_price = sl_price_sell
                    exit_condition = "SL Hit"
                elif df["Low"].iloc[i] + (symbol_spread * symbol_points) < tp_price_sell:
                    exit_price = tp_price_sell
                    exit_condition = "TP Hit"
                elif (df["Close"].iloc[i] > df["EMA50"].iloc[i] if "EMA50" in df.columns else False or
                    df["RSI14"].iloc[i] > (100-rsi_level_out) if "RSI14" in df.columns else False): # (df["Close"].iloc[i] > entry_price_sell * 1.002)
                    exit_price = df["Close"].iloc[i] + (symbol_spread * symbol_points)
                    exit_condition = "Technical Exit"

                if exit_condition:
                    exit_time = df["Date"].iloc[i] + " " + df["Time"].iloc[i]
                    profit = (entry_price_sell - exit_price) / symbol_points

                    risk_amount = sl_price_sell - entry_price_sell
                    reward_amount = entry_price_sell - tp_price_sell

                    entry_datetime = pd.to_datetime(entry_time_sell)
                    entry_hour = entry_datetime.hour
                    entry_day = entry_datetime.dayofweek

                    log_message = f"time_{df['Date'].iloc[i]} {df['Time'].iloc[i]} op_{df['Open'].iloc[i]} cl_{df['Close'].iloc[i]} : trade_{trade_type_sell} : Entry_time_{entry_time_sell} Entry_price_{entry_price_sell:0.5f} Entry_sl_{sl_price_sell:0.5f} Entry_tp_{tp_price_sell:0.5f} : Exit_time_{exit_time} Exit_price_{exit_price:0.5f} : Condition_{exit_condition} Profit_{profit:0.0f}\n"
                    log_file.write(log_message)

                    trades.append([
                        entry_time_sell, entry_price_sell,
                        exit_time, exit_price,
                        profit, trade_type_sell,
                        entry_hour, entry_day,
                        exit_condition,
                        sl_price_sell, tp_price_sell,
                        risk_amount, reward_amount,
                        atr_entry_sell,
                        bb_width_entry_sell,
                        rsi14_entry_sell,
                        (sl_price_sell - entry_price_sell) / entry_price_sell if risk_amount > 0 else 0,
                        (entry_price_sell - tp_price_sell) / entry_price_sell if reward_amount > 0 else 0,
                        volatility_entry_sell, volume_spike_entry_sell
                    ])

                    stats['sell']['total'] += 1
                    stats['sell']['profit_sum'] += profit
                    stats['day_stats'][entry_day]['total'] += 1
                    stats['hour_stats'][entry_hour]['total'] += 1
                    if profit > 0:
                        stats['day_stats'][entry_day]['win'] += 1
                        stats['hour_stats'][entry_hour]['win'] += 1
                    elif profit < 0:
                        stats['day_stats'][entry_day]['loss'] += 1
                        stats['hour_stats'][entry_hour]['loss'] += 1

                    in_trade_sell = False

    # ... (ส่วนท้ายฟังก์ชัน create_trade_cycles เหมือนเดิม) ...
    TRADE_COLUMNS = [
        "Entry Time", "Entry Price", "Exit Time", "Exit Price",
        "Profit", "Trade Type", 
        "Entry Hour", "Entry Day", 
        "Exit Condition",
        "SL Price", "TP Price",
        "Risk", "Reward",
        "ATR at Entry", 
        "BB Width at Entry", 
        "RSI14 at Entry",
        "Pct_Risk", 
        "Pct_Reward",
        "Volume MA20 at Entry", "Volume Spike at Entry"
        # เพิ่มคอลัมน์ตาม Features ที่คุณบันทึกไว้
    ]

    trade_df = pd.DataFrame(trades, columns=TRADE_COLUMNS)
    # print(trade_df[trade_df["Profit"] > 0.0][["Trade Type","Profit"]])

    # ==============================================
    # ส่วนเพิ่มเติม: สรุปการตรวจสอบ Look-Ahead Bias
    # ==============================================
    print("\n🔍 สรุปการตรวจสอบ Look-Ahead Bias")
    print(f"- จำนวนการทำนายทั้งหมด: {len(df) - start_index}")
    print(f"- จำนวนครั้งที่พบ NaN ใน Features: {nan_count}")
    print(f"- จำนวนครั้งที่พบ Features อาจมีปัญหา: {suspect_feature_count}")
    
    if suspect_feature_count > 0:
        print("\n⚠️ คำเตือน ⚠️: พบ Features ที่อาจมี Look-Ahead Bias")
        print("โปรดตรวจสอบ Features ต่อไปนี้:")
        for feat in suspect_features:
            print(f"- {feat}")
    else:
        print("✅ ไม่พบ ปัญหาการใช้ข้อมูลจากอนาคต (Look-Ahead Bias)")

    TRADE_COLUMNS = [
        "Entry Time", "Entry Price", "Exit Time", "Exit Price",
        "Profit", "Trade Type", 
        # ... คอลัมน์เดิม ...
    ]

    return trade_df, stats

"""คำนวณ win rate และ expectancy จากชุด trade"""
def calculate_stats(subset):
    wins = subset[subset['Profit'] > 0]
    losses = subset[subset['Profit'] < 0]
    # total = len(subset)
    num_wins = len(wins)
    num_losses = len(losses)
    total = num_wins + num_losses

    win_rate = (num_wins / total) * 100 if total > 0 else 0
    avg_win = wins['Profit'].mean() if num_wins > 0 else 0
    avg_loss = abs(losses['Profit'].mean()) if num_losses > 0 else 0
    expectancy = (avg_win * (win_rate / 100)) - (avg_loss * (1 - (win_rate / 100)))
    return {"win_rate": round(win_rate, 2), "expectancy": round(expectancy, 3)}

"""วิเคราะห์ win rate และ expectancy แยกตามประเภทการเทรด (buy/sell/all)"""
def analyze_trade_performance(trades_df):
    print(f"\n🏗️ เปิดใช้งาน analyze trade performance") if Steps_to_do else None

    """วิเคราะห์ผลการเทรดเพื่อหา Win Rate และ Expectancy แยกตามประเภท"""
    analysis = {}

    # 1. วิเคราะห์ Buy Trades
    buy_trades = trades_df[trades_df['Trade Type'] == 'Buy']
    analysis['buy'] = calculate_stats(buy_trades)

    # 2. วิเคราะห์ Sell Trades
    sell_trades = trades_df[trades_df['Trade Type'] == 'Sell']
    analysis['sell'] = calculate_stats(sell_trades)

    # 3. วิเคราะห์ Buy + Sell Trades
    analysis['buy_sell'] = calculate_stats(trades_df)

    return analysis

# ==============================================
# analysis.py
# ฟังก์ชันวิเคราะห์ผลลัพธ์, SL/TP, เวลา, รายงาน, cross-asset feature importance
# ==============================================

"""วิเคราะห์ประสิทธิภาพของ Stop Loss/Take Profit (SL/TP) และสรุปสถิติที่เกี่ยวข้อง"""
def analyze_sl_tp_performance(trade_df):
    print(f"\n🏗️ เปิดใช้งาน analyze sl tp performance") if Steps_to_do else None

    """วิเคราะห์ประสิทธิภาพของ SL/TP"""
    if trade_df.empty:
        print("⚠️ ไม่มีข้อมูล การซื้อขายสำหรับวิเคราะห์ SL/TP")
        return
    
    # ตรวจสอบว่ามีคอลัมน์ที่จำเป็นหรือไม่
    required_cols = ['Exit Condition', 'Profit']
    if not all(col in trade_df.columns for col in required_cols):
        print("⚠️ ไม่พบ คอลัมน์ที่จำเป็นสำหรับการวิเคราะห์ SL/TP")
        return
    
    # คำนวณอัตราการชน SL vs TP
    sl_hits = len(trade_df[trade_df['Exit Condition'] == "SL Hit"])
    tp_hits = len(trade_df[trade_df['Exit Condition'] == "TP Hit"])
    tech_exits = len(trade_df[trade_df['Exit Condition'] == "Technical Exit"])
    total_trades = len(trade_df)
    
    print("📊 สถิติการทำงานของ SL/TP:")
    print(f"{'='*50}")
    print(f"{'ประเภทการออก':<20}{'จำนวน':<10}{'อัตราส่วน':<10}")
    print(f"{'-'*50}")
    print(f"{'TP Hit':<20}{tp_hits:<10}{tp_hits/total_trades:.2%}")
    print(f"{'SL Hit':<20}{sl_hits:<10}{sl_hits/total_trades:.2%}")
    print(f"{'Technical Exit':<20}{tech_exits:<10}{tech_exits/total_trades:.2%}")
    print(f"{'SL + Tech Exit':<20}{sl_hits+tech_exits:<10}{(sl_hits+tech_exits)/total_trades:.2%}")
    print(f"{'='*50}")
    
    # วิเคราะห์กำไรเฉลี่ย
    if tp_hits > 0:
        avg_profit_tp = trade_df[trade_df['Exit Condition'] == "TP Hit"]['Profit'].mean()
        print(f"กำไรเฉลี่ยเมื่อ TP Hit: {avg_profit_tp:.2f}")
        
    if sl_hits > 0:
        avg_loss_sl = trade_df[trade_df['Exit Condition'] == "SL Hit"]['Profit'].mean()
        print(f"ขาดทุนเฉลี่ยเมื่อ SL Hit: {avg_loss_sl:.2f}")
    
    if tech_exits > 0:
        avg_profit_tech = trade_df[trade_df['Exit Condition'] == "Technical Exit"]['Profit'].mean()
        print(f"กำไร/ขาดทุนเฉลี่ยเมื่อออกด้วยสัญญาณเทคนิค: {avg_profit_tech:.2f}")
    
    # คำนวณ Risk/Reward Ratio
    if sl_hits > 0 and tp_hits > 0:
        avg_risk = abs(avg_loss_sl)
        avg_reward = avg_profit_tp
        rr_ratio = avg_reward / avg_risk
        print(f"อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:{rr_ratio:.2f}")

    # เพิ่มการวิเคราะห์ผลรวม SL + Technical Exit
    if (sl_hits + tech_exits) > 0:
        combined_loss = trade_df[trade_df['Exit Condition'].isin(["SL Hit", "Technical Exit"])]['Profit'].mean()
        combined_rr_ratio = avg_reward / abs(combined_loss)
        print(f"\nผลรวม SL + Technical Exit:")
        print(f"- จำนวนเทรดรวม: {sl_hits + tech_exits}")
        print(f"- อัตราส่วน: {(sl_hits + tech_exits)/total_trades:.2%}")
        print(f"- กำไร/ขาดทุนเฉลี่ย: {combined_loss:.2f}")
        print(f"- อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:{combined_rr_ratio:.2f}")

    # เพิ่มในส่วนวิเคราะห์กำไรเฉลี่ย
    if tech_exits > 0:
        tech_profits = trade_df[trade_df['Exit Condition'] == "Technical Exit"]['Profit']
        avg_profit_tech = tech_profits.mean()
        winning_tech = len(tech_profits[tech_profits > 0])
        losing_tech = len(tech_profits[tech_profits <= 0])
        
        print(f"\nการออกด้วยสัญญาณเทคนิค:")
        print(f"- กำไรเฉลี่ยเมื่อชน TP: {tech_profits[tech_profits > 0].mean():.2f}")
        print(f"- ขาดทุนเฉลี่ยเมื่อชน SL: {tech_profits[tech_profits <= 0].mean():.2f}")
        print(f"- อัตราการชน TP: {winning_tech/tech_exits:.2%}")
        print(f"- อัตราการชน SL: {losing_tech/tech_exits:.2%}")

"""วิเคราะห์ประสิทธิภาพการเทรดตามวันในสัปดาห์และชั่วโมง"""
def analyze_time_performance(trade_df, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน analyze time performance") if Steps_to_do else None

    """วิเคราะห์ประสิทธิภาพการซื้อขายตามวันและเวลา"""
    if trade_df.empty:
        print("⚠️ ไม่มีข้อมูล การซื้อขายสำหรับวิเคราะห์")
        return
    
    try:
        # วิเคราะห์ตามวันในสัปดาห์
        print("📊 ประสิทธิภาพตามวันในสัปดาห์:")
        day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        
        day_stats = trade_df.groupby('Entry_DayOfWeek').agg({
            'Profit': ['count', 'mean', 'sum'],
            'Target': 'mean'
        }).rename(index={i: day_names[i] for i in range(7)})
        
        print(day_stats)
        
        # วิเคราะห์ตามชั่วโมง
        print("\n📊 ประสิทธิภาพตามชั่วโมง:")
        hour_stats = trade_df.groupby('Entry_Hour').agg({
            'Profit': ['count', 'mean', 'sum'],
            'Target': 'mean'
        })
        
        print(hour_stats)
        
        # สร้าง visualization
        plt.figure(figsize=(15, 10))
        
        # ตรวจสอบว่ามีข้อมูลพอที่จะพล็อตหรือไม่
        valid_plots = 0
        plot_positions = [(1, 2, 1), (1, 2, 2), (2, 2, 1), (2, 2, 2)]
        plots = []
        
        # กราฟวันในสัปดาห์ (Win Rate)
        if 'Entry_DayOfWeek' in trade_df.columns and 'Target' in trade_df.columns:
            day_win_rate = trade_df.groupby('Entry_DayOfWeek')['Target'].mean()
            if len(day_win_rate) > 0:
                plots.append(('bar', day_names[:len(day_win_rate)], day_win_rate.values, 'Win Rate by Day of Week'))
        
        # กราฟชั่วโมง (Win Rate)
        if 'Entry_Hour' in trade_df.columns and 'Target' in trade_df.columns:
            hour_win_rate = trade_df.groupby('Entry_Hour')['Target'].mean()
            if len(hour_win_rate) > 0:
                plots.append(('bar', hour_win_rate.index, hour_win_rate.values, 'Win Rate by Hour'))
        
        # กราฟผลรวมกำไรตามวัน
        if 'Entry_DayOfWeek' in trade_df.columns and 'Profit' in trade_df.columns:
            day_profit = trade_df.groupby('Entry_DayOfWeek')['Profit'].sum()
            if len(day_profit) > 0:
                plots.append(('bar', day_names[:len(day_profit)], day_profit.values, 'Total Profit by Day of Week'))
        
        # กราฟผลรวมกำไรตามชั่วโมง
        if 'Entry_Hour' in trade_df.columns and 'Profit' in trade_df.columns:
            hour_profit = trade_df.groupby('Entry_Hour')['Profit'].sum()
            if len(hour_profit) > 0:
                plots.append(('bar', hour_profit.index, hour_profit.values, 'Total Profit by Hour'))
        
        # พล็อตเฉพาะกราฟที่มีข้อมูล
        for i, (plot_type, x, y, title) in enumerate(plots[:4]):
            plt.subplot(2, 2, i+1)
            if plot_type == 'bar':
                plt.bar(x, y)
            plt.title(title)
            plt.xticks(rotation=45)
        
        plt.tight_layout()
        plot_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_time_analysis.png")
        plt.savefig(plot_path)
        plt.close()
        print(f"💾 บันทึกกราฟวิเคราะห์เวลา ที่: {plot_path}")
            
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะสร้างกราฟวิเคราะห์เวลา: {str(e)}")
        import traceback
        traceback.print_exc()

"""วิเคราะห์ผลลัพธ์โดยรวมของโมเดลแต่ละไฟล์/แต่ละ timeframe"""
def analyze_results(results, symbol, timeframe, group_name):
    print(f"\n🏗️ เปิดใช้งาน analyze results") if Steps_to_do else None

    """วิเคราะห์ผลลัพธ์โดยรวม"""
    print("📈 การวิเคราะห์ผลลัพธ์โดยรวม:")
    
    # แปลงเป็น DataFrame
    if not results:
        print("⚠️ ไม่มีผลลัพธ์ให้วิเคราะห์")
        return
    
    try:
        # แปลงค่าใน results ให้เป็น float ก่อนสร้าง DataFrame
        processed_results = []
        for r in results:
            processed = {
                'File': r.get('file', ''),
                'Timeframe': r.get('timeframe', ''),
                'Accuracy': r.get('accuracy', 0),
                'AUC': r.get('auc', 0),
                'F1': r.get('f1_score', 0),
                'CV_Accuracy': r.get('cv_accuracy', 0),
                'CV_AUC': r.get('cv_auc', 0)
            }
            processed_results.append(processed)
        
        df = pd.DataFrame(processed_results)
        
        # ... ส่วนอื่นๆ ...
        
        best_model = df.loc[df['F1'].idxmax()]
        print(f"\n🏆 โมเดลที่ดีที่สุด (ตาม F1 Score):")
        print(f"ไฟล์: {best_model['File']}")
        print(f"Timeframe: M{best_model['Timeframe']}")
        print(f"F1 Score: {best_model['F1']:.4f}")
        
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะวิเคราะห์ผลลัพธ์: {str(e)}")
    
    # สรุปสถิติ
    print("\n📌 สรุปสถิติ:")
    print(df.describe().to_string())
    
    # หาโมเดลที่ดีที่สุด
    best_model = df.loc[df['F1'].idxmax()]
    print(f"\n🏆 โมเดลที่ดีที่สุด (ตาม F1 Score):")
    print(f"ไฟล์: {best_model['File']}")
    print(f"Timeframe: M{best_model['Timeframe']}")
    # print(f"F1 Score: {best_model['F1']:.4f}")
    print(f"F1 Score: {best_model['F1']:.4f}")
    
    # บันทึกผลการวิเคราะห์
    analysis_path = os.path.join(output_folder, f"{group_name}_performance_analysis.txt")
    with open(analysis_path, 'w', encoding='utf-8') as f:
        f.write("ผลการวิเคราะห์ประสิทธิภาพโมเดล\n")
        f.write("="*50 + "\n")
        f.write(df.to_string())
        f.write("\n\nโมเดลที่ดีที่สุด:\n")
        f.write(str(best_model))
    print(f"\n💾 บันทึกผลการวิเคราะห์ที่: {analysis_path}")

"""วิเคราะห์ feature importance ข้ามหลาย asset เพื่อหา features ที่สำคัญร่วมกัน"""
def analyze_cross_asset_feature_importance(input_files, importance_files_dir, pickle_output_path, num_top_features_per_asset=None, min_assets_threshold=None, overall_top_n=None):
    print(f"\n🏗️ เปิดใช้งาน analyze cross asset feature importance") if Steps_to_do else None

    """
    วิเคราะห์ Feature Importance จากไฟล์ของแต่ละ Asset ในรายการ input_files
    เพื่อหารายชื่อ Features ที่มีความสำคัญอย่างสม่ำเสมอ และบันทึกลงในไฟล์ .pkl

    Args:
        input_files (list): รายชื่อไฟล์ Asset ดั้งเดิม (เช่น ["AUDUSD#_H1_...csv", ...])
        importance_files_dir (str): Directory ที่เก็บไฟล์ feature_importance_*.csv
        pickle_output_path (str): Path แบบเต็มสำหรับบันทึกรายชื่อ Features ที่เลือกในรูปแบบ .pkl
        num_top_features_per_asset (int, optional): จำนวน Top N features จากแต่ละ Asset ที่จะนำมารวมกัน. Defaults to None.
        min_assets_threshold (int, optional): Feature ต้องปรากฏใน Top N (ตาม num_top_features_per_asset) อย่างน้อยกี่ Asset. Defaults to None.
        overall_top_n (int, optional): เลือก Features ที่มีความสำคัญเฉลี่ย (หรือรวม) สูงสุด N อันดับ. Defaults to None.
                                        สามารถใช้ร่วมกับ num_top_features_per_asset + min_assets_threshold หรือเดี่ยวๆ ก็ได้

    Returns:
        list: รายชื่อ Features ที่ถูกเลือก (หรือ [] หากเกิดข้อผิดพลาด ⚠️ หรือไม่มี features ถูกเลือก)
    """
    print(f"--- เริ่มวิเคราะห์ Feature Importance ข้าม Assets ตาม {len(input_files)} ไฟล์ที่ระบุ ---")
    all_importance_data = {} # Dictionary to store importance scores for each feature across assets
    processed_asset_count = 0

    # 1. สร้างรายชื่อไฟล์ Feature Importance ที่คาดหวังจาก input_files
    expected_importance_file_paths = []
    for input_file in input_files:
        info = parse_filename(input_file)
        if info is None:
            print(f"⚠️ ข้ามไฟล์ input '{input_file}' เนื่องจากแยกข้อมูลไม่ได้")
            continue

        symbol = info["Name_Currency"]
        timeframe = info["Timeframe_Currency"] # นี่คือ timeframe เป็นนาทีแล้ว

        # สร้างชื่อไฟล์ Feature Importance ที่คาดหวัง
        expected_filename = f"{str(timeframe).zfill(3)}_{symbol}_feature_importance.csv"
        expected_filepath = os.path.join(importance_files_dir, expected_filename)
        expected_importance_file_paths.append((symbol, expected_filepath)) # เก็บทั้ง symbol และ path

    if not expected_importance_file_paths:
        print("⚠️ ไม่สามารถสร้างรายชื่อไฟล์ Feature Importance ที่คาดหวังจาก input_files ได้เลย")
        # แม้จะไม่มีไฟล์ importance ให้วิเคราะห์ ก็ยังสร้างไฟล์ pkl ว่างๆ หรือไม่มี list ได้
        # หรือจะ return [] เลยก็ได้ ขึ้นอยู่กับว่าจะให้ select features จัดการอย่างไร
        selected_features = [] # ไม่มีไฟล์ให้วิเคราะห์ ก็ไม่มี features จาก analysis
        # ไปขั้นตอนบันทึก
    else:
        print(f"คาดหวังไฟล์ Feature Importance ทั้งหมด {len(expected_importance_file_paths)} ไฟล์:")
        for symbol, path in expected_importance_file_paths:
            print(f"- {os.path.basename(path)}")

        # 2. อ่านและประมวลผลไฟล์ Feature Importance ที่คาดหวังและมีอยู่จริง
        print("\n--- เริ่มอ่านไฟล์ Feature Importance ที่พบ ---")
        for symbol, file_path in expected_importance_file_paths:
            file_name = os.path.basename(file_path)

            if not os.path.exists(file_path):
                print(f"ℹ️ ข้าม Asset '{symbol}': ⚠️ ไม่พบไฟล์ Feature Importance ที่คาดหวังที่ '{file_path}'")
                continue # ข้ามไฟล์นี้ไป

            print(f"✅ กำลังประมวลผลไฟล์: {file_name}")
            processed_asset_count += 1

            try:
                df_importance = pd.read_csv(file_path)
                # ตรวจสอบคอลัมน์ Feature และคอลัมน์ Importance ที่ใช้
                if 'Feature' not in df_importance.columns:
                    print(f"⚠️ ไฟล์ {file_name} ไม่มีคอลัมน์ 'Feature'")
                    processed_asset_count -= 1
                    continue

                importance_metric = None
                if 'Gain' in df_importance.columns and df_importance['Gain'].sum() > 0:
                    importance_metric = 'Gain'
                elif 'Split' in df_importance.columns and df_importance['Split'].sum() > 0:
                    importance_metric = 'Split'
                else:
                    print(f"⚠️ ไฟล์ {file_name} ไม่มีคอลัมน์ Feature Importance ที่ใช้งานได้ ('Gain' หรือ 'Split') หรือคอลัมน์นั้นเป็นศูนย์ทั้งหมด")
                    processed_asset_count -= 1
                    continue

                # เรียงตามความสำคัญและเลือก Top N ของแต่ละ Asset ถ้ากำหนด
                if num_top_features_per_asset is not None:
                    df_importance = df_importance.sort_values(by=importance_metric, ascending=False).head(num_top_features_per_asset)

                # เก็บข้อมูลความสำคัญ
                for index, row in df_importance.iterrows():
                    feature = row['Feature']
                    importance = row[importance_metric]

                    if feature not in all_importance_data:
                        # เก็บเป็น list ของ tuple (importance, symbol) เพื่อ track ได้ว่ามาจาก asset ไหน
                        all_importance_data[feature] = {'importances': [], 'asset_symbols': []}

                    all_importance_data[feature]['importances'].append(importance)
                    if symbol not in all_importance_data[feature]['asset_symbols']: # ป้องกันการนับ asset ซ้ำ
                        all_importance_data[feature]['asset_symbols'].append(symbol)

            except Exception as e:
                print(f"⚠️ เกิดข้อผิดพลาด ในการอ่านหรือประมวลผลไฟล์ {file_name}: {e}")
                processed_asset_count -= 1
                continue

        if processed_asset_count == 0:
            print("⚠️ ประมวลผลไฟล์ Feature Importance ไม่ได้เลยจาก Assets ที่พบ")
            selected_features = [] # ถ้าประมวลผลไม่ได้เลย ก็ไม่มี features จาก analysis
        else:
            print(f"\n✅ ประมวลผลไฟล์ Feature Importance ไปทั้งสิ้น {processed_asset_count} Assets")

            # 3. ประมวลผลข้อมูลรวม
            feature_summary = []
            for feature, data in all_importance_data.items():
                avg_importance = sum(data['importances']) / len(data['importances'])
                asset_count = len(data['asset_symbols'])
                feature_summary.append({
                    'Feature': feature,
                    'Avg_Importance': avg_importance,
                    'Asset_Count': asset_count
                })

            df_summary = pd.DataFrame(feature_summary)

            # 4. คัดเลือก Features ตามเกณฑ์
            selected_features = []

            # เกณฑ์ที่ 1: ต้องปรากฏใน Top N ของ Asset อย่างน้อย X ตัว
            if num_top_features_per_asset is not None and min_assets_threshold is not None:
                df_filtered_by_assets = df_summary[df_summary['Asset_Count'] >= min_assets_threshold].copy()
                print(f"\nพบ Features ที่ปรากฏใน Top {num_top_features_per_asset} ของอย่างน้อย {min_assets_threshold} Assets: {len(df_filtered_by_assets)} Features")

                if overall_top_n is not None:
                    selected_features = df_filtered_by_assets.sort_values(by='Avg_Importance', ascending=False).head(overall_top_n)['Feature'].tolist()
                    print(f"คัดเลือก Overall Top {overall_top_n} Features จากกลุ่มนี้")
                else:
                    selected_features = df_filtered_by_assets['Feature'].tolist()
                    print("คัดเลือก Features ทั้งหมดที่ผ่านเกณฑ์ Asset Count")

            # เกณฑ์ที่ 2: เลือก Overall Top N จากทั้งหมด (ถ้าไม่ได้ใช้เกณฑ์ Asset Count)
            elif overall_top_n is not None:
                print(f"\nคัดเลือก Overall Top {overall_top_n} Features จากความสำคัญเฉลี่ย")
                selected_features = df_summary.sort_values(by='Avg_Importance', ascending=False).head(overall_top_n)['Feature'].tolist()

            else:
                print("\nℹ️ ไม่ได้ระบุเกณฑ์การคัดเลือก Features (num_top_features_per_asset, min_assets_threshold, overall_top_n)")
                print("จะคืนค่า Features ทั้งหมดที่พบพร้อมความสำคัญเฉลี่ย")
                selected_features = df_summary.sort_values(by='Avg_Importance', ascending=False)['Feature'].tolist()

    # 5. บันทึกผลลัพธ์ลงในไฟล์ .pkl
    print(f"\n--- เริ่มบันทึกรายชื่อ Features ที่เลือก ({len(selected_features)} รายการ) ---")
    try:
        # สร้าง directory ถ้ายังไม่มี
        output_dir = os.path.dirname(pickle_output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            print(f"📁 สร้างไดเรกทอรี: {output_dir}")

        if Save_File:
            with open(pickle_output_path, 'wb') as f:
                pickle.dump(selected_features, f)
            print(f"✅ บันทึกรายชื่อ Features ที่เลือกไปยัง: {pickle_output_path}")
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ในการบันทึกไฟล์ {pickle_output_path}: {e}")
        # ในกรณีที่บันทึกไม่ได้ อาจจะคืนค่า [] หรือ None ก็ได้ แล้วแต่การจัดการ error ที่ต้องการ
        return [] # คืนค่าเป็น [] ถ้าบันทึกไม่ได้

    print("\n--- สิ้นสุดการวิเคราะห์และบันทึก Feature Importance ---")
    return selected_features

"""บันทึกรายงานผลลัพธ์แบบละเอียด (JSON/CSV)"""
def save_enhanced_report(results, output_folder, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน save enhanced report") if Steps_to_do else None

    try:
        if not results:
            print("⚠️ ไม่มีผลลัพธ์ที่จะบันทึก")
            return
            
        # สร้างโฟลเดอร์ถ้ายังไม่มี
        os.makedirs(output_folder, exist_ok=True)
        
        # บันทึกเป็น JSON
        report_path = os.path.join(output_folder, 'detailed_report.json')
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump({
                'summary': pd.DataFrame(results).to_dict(orient='records'),
                'best_model': max(results, key=lambda x: x['f1_score']),
                'execution_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'system_info': {
                    'python_version': sys.version,
                    'lgb_version': lgb.__version__,
                    'sklearn_version': sklearn_version
                }
            }, f, indent=2, ensure_ascii=False)
        print(f"✅ บันทึกรายงานละเอียดที่: {report_path}")

        # บันทึกเป็น CSV
        csv_path = os.path.join(output_folder, f'{str(timeframe).zfill(3)}_{symbol}_final_results.csv')
        pd.DataFrame(results).to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"✅ บันทึกรายงานสรุปที่: {csv_path}")
        
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะบันทึกรายงาน: {str(e)}")
        error_log_path = os.path.join(output_folder, "report_error_log.txt")
        with open(error_log_path, 'w') as f:
            f.write(f"Error saving report: {str(e)}\n")
            traceback.print_exc(file=f)

# ==============================================
# visualization.py
# ฟังก์ชันเกี่ยวกับการ plot กราฟ, candlestick, safe plot
# ==============================================

"""สร้างกราฟแท่งเทียนพร้อมจุดเข้า-ออกการเทรด"""
def plot_candlestick_with_entries_and_exits(df, trade_df, file_name):
    print(f"\n🏗️ เปิดใช้งาน plot candlestick with entries and exits") if Steps_to_do else None

    fig = go.Figure(data=[go.Candlestick(
        x=df['Date'] + ' ' + df['Time'],
        open=df['Open'],
        high=df['High'],
        low=df['Low'],
        close=df['Close'],
        name="Candlestick"
    )])

    fig.add_trace(go.Scatter(
        x=trade_df[trade_df['Trade Type'] == 'Buy']['Entry Time'],
        y=trade_df[trade_df['Trade Type'] == 'Buy']['Entry Price'], # แก้ไขตรงนี้
        mode='markers',
        marker=dict(color='green', size=8, symbol='triangle-up'),
        name='Buy Entry Points'
    ))

    fig.add_trace(go.Scatter(
        x=trade_df[trade_df['Trade Type'] == 'Buy']['Exit Time'],
        y=trade_df[trade_df['Trade Type'] == 'Buy']['Exit Price'], # แก้ไขตรงนี้
        mode='markers',
        marker=dict(color='red', size=8, symbol='triangle-down'),
        name='Buy Exit Points'
    ))

    fig.add_trace(go.Scatter(
        x=trade_df[trade_df['Trade Type'] == 'Sell']['Entry Time'],
        y=trade_df[trade_df['Trade Type'] == 'Sell']['Entry Price'], # แก้ไขตรงนี้
        mode='markers',
        marker=dict(color='green', size=8, symbol='triangle-down'),
        name='Sell Entry Points'
    ))

    fig.add_trace(go.Scatter(
        x=trade_df[trade_df['Trade Type'] == 'Sell']['Exit Time'],
        y=trade_df[trade_df['Trade Type'] == 'Sell']['Exit Price'], # แก้ไขตรงนี้
        mode='markers',
        marker=dict(color='red', size=8, symbol='triangle-up'),
        name='Sell Exit Points'
    ))

    fig.update_layout(
        title=f"Candlestick Chart with Entry and Exit - {file_name}",
        xaxis_title="Date",
        yaxis_title="Price",
        xaxis_rangeslider_visible=True,
        dragmode='zoom',
        yaxis=dict(
            autorange=True,
            fixedrange=False
        ),
        width=1200,
        height=800
    )

    return fig

"""พล็อตกราฟผลลัพธ์ (accuracy, auc, ฯลฯ) แบบปลอดภัย (มี error handling)"""
def safe_plot_results(df, symbol, timeframe, output_path="Test_LightGBM/results/plots"):
    print(f"\n🏗️ เปิดใช้งาน safe plot results") if Steps_to_do else None

    try:
        if df.empty:
            print("⚠️ DataFrame ว่างเปล่า ไม่มีข้อมูล สำหรับพล็อต")
            return

        os.makedirs(output_path, exist_ok=True) # สร้างโฟลเดอร์สำหรับบันทึกรูปภาพถ้ายังไม่มี

        timeframes = df['timeframe']
        metrics_to_plot = ['Test Accuracy', 'auc', 'f1_score', 'precision', 'recall', 'cv_accuracy', 'cv_auc', 'cv_f1']
        num_metrics_plotted = 0

        for metric in metrics_to_plot:
            if metric in df.columns and not df[metric].isnull().all():
                plt.figure(figsize=(10, 6))
                plt.plot(timeframes, df[metric], marker='o', label=metric)
                plt.xlabel('Timeframe')
                plt.ylabel('Score')
                plt.title(f'{metric} Comparison Across Timeframes')
                plt.legend()
                plt.grid(True)

                # สร้างชื่อไฟล์สำหรับบันทึก
                filename = f'performance_{metric.replace(" ", "_")}_comparison.png'
                filepath = os.path.join(output_path, filename)

                # บันทึกเป็นไฟล์ PNG
                plt.savefig(filepath)
                plt.close() # ปิด Figure หลังจากบันทึกเพื่อประหยัด memory
                print(f"✅ บันทึกกราฟ {metric} ที่: {filepath}")
                num_metrics_plotted += 1

        if num_metrics_plotted == 0:
            print("⚠️ ไม่มี Metrics ที่มีข้อมูลเพียงพอสำหรับการพล็อต")

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ในฟังก์ชัน safe plot results: {e}")
        print(f"คอลัมน์ที่มีอยู่: {df.columns.tolist()}")
        traceback.print_exc()

# ==============================================
# main.py
# ส่วนที่เป็น entry point ของโปรแกรม, วนลูปไฟล์, เรียกใช้ฟังก์ชันจากโมดูลต่างๆ
# ==============================================

def save_entry_condition_summary(
    symbol, timeframe, nbars, threshold, features_count, entry_results, best_entry_name, output_folder="Test_LightGBM/results",
    start_date=None, end_date=None
):
    """
    บันทึกผล entry condition แนวนอน (append ต่อท้ายไฟล์)
    entry_results: dict เช่น {'default': {'win_rate': 0.34, 'expectancy': 0.354, 'num_trades': 50}, ...}
    """
    now = datetime.datetime.now().strftime("%y/%m/%d %H:%M")
    if start_date is not None and end_date is not None:
        period_str = f"{start_date:%Y-%m-%d} {end_date:%Y-%m-%d} {(end_date - start_date).days + 1} "
    else:
        period_str = "-"
    line = f"{now}  {period_str}  {timeframe} SL {nbars}  threshold {threshold:.3f}  features {features_count}"
    for entry_name, result in entry_results.items():
        w = result.get('win_rate', 0)
        w_pct = w*100 if w <= 1 else w
        exp = result.get('expectancy', 0)
        ntr = result.get('num_trades', 0)
        line += f"  {entry_name} {w_pct:.0f}% {exp:.3f} n={ntr}"
    line += f"  Best  {best_entry_name}\n"
    fname = f"{str(timeframe).zfill(3)}_{symbol}_entry_summary.txt"
    with open(os.path.join(output_folder, fname), "a", encoding="utf-8") as f:
        f.write(line)

"""จุดเริ่มต้นของโปรแกรม วนลูปไฟล์, เรียกใช้ฟังก์ชันต่างๆ, สรุปผล, วิเคราะห์, และบันทึกผลลัพธ์"""
def main(run_identifier=None, group_name=None, input_files=None):
    print(f"\n🏗️ เปิดใช้งาน main") if Steps_to_do else None

    # ถ้าไม่ได้ส่ง input_files มา ให้ใช้จาก test_groups
    if input_files is None:
        # ใช้ไฟล์ทั้งหมดจาก test_groups
        input_files = []
        for group_files in test_groups.values():
            input_files.extend(group_files)

    # กำหนด output_folder ตาม group_name
    if group_name:
        output_folder = f"Test_LightGBM/results/{group_name}"
        os.makedirs(output_folder, exist_ok=True)
        print(f"📁 ใช้ output_folder สำหรับกลุ่ม {group_name}: {output_folder}")
    else:
        output_folder = "Test_LightGBM/results"
        print(f"📁 ใช้ output_folder เริ่มต้น: {output_folder}")

    # --- เริ่มจับเวลาทั้งหมด ---
    start_time_total = time.perf_counter()

    results = []
    results_report = []

    for file in input_files:
        print(f"{'--'*30}")
        print(f"กำลังทดสอบไฟล์: {file} รอบที่ {run_identifier}/{NUM_TRAINING_ROUNDS}")
        print(f"{'--'*30}")

        try:
            info = parse_filename(file)  # ใช้ฟังก์ชันที่เราสร้างไว้ก่อนหน้านี้
            symbol = info["Name_Currency"]
            timeframe = info["Timeframe_Currency"]
            
        except IndexError:
            print(f"⚠️ รูปแบบชื่อไฟล์ไม่ถูกต้อง: {file}. คาดหวังรูปแบบ 'ชื่อ#timeframe.csv'")
            continue # ข้ามไฟล์นี้

        model_name = "LightGBM"
        print(f"ข้อมูลพื้นฐาน {model_name} {timeframe} {symbol}")

        # ตรวจสอบว่าโฟลเดอร์ มีอยู่หรือไม่
        
        models_dir = "Test_LightGBM/models"
        if not os.path.exists(models_dir):
            os.makedirs(models_dir)
            print(f"สร้างโฟลเดอร์ {models_dir} เรียบร้อยแล้ว")
        
        timeframe_dir = f"Test_LightGBM/models/{str(timeframe).zfill(3)}_{symbol}"
        if not os.path.exists(timeframe_dir):
            os.makedirs(timeframe_dir)
            print(f"สร้างโฟลเดอร์ {timeframe_dir} เรียบร้อยแล้ว")
        
        feature_dir = "Test_LightGBM/feature_importance"
        if not os.path.exists(feature_dir):
            os.makedirs(feature_dir)
            print(f"สร้างโฟลเดอร์ {feature_dir} เรียบร้อยแล้ว")

        thresholds_dir = "Test_LightGBM/thresholds"
        if not os.path.exists(thresholds_dir):
            os.makedirs(thresholds_dir)
            print(f"สร้างโฟลเดอร์ {thresholds_dir} เรียบร้อยแล้ว")
        
        # ขั้นตอนทดลอง/พัฒนา (Experiment/Development)
        # ทดลอง/พัฒนา: เน้นความยืดหยุ่นและการปรับแต่ง สร้างโมเดลใหม่ตลอด
        # ถ้าต้องการปรับแต่ง/วิจัย/เปรียบเทียบ ควรใช้แบบทดลอง/พัฒนา
        # ขั้นตอน Production (ใช้งานจริง/Deploy)
        # เน้นความเร็ว เสถียร และ reproducible โหลดโมเดล/Scaler ที่ฝึกไว้แล้ว 
        # ถ้าต้องการ deploy ให้ผู้ใช้หรือใช้งานจริง ควรใช้แบบ production

        # สร้างโมเดลใหม่ ถ้าทดลอง/พัฒนา: สร้างใหม่ตลอด
        model = None  
        scaler = None
        
        # ใช้โมเดลเดิม ถ้าถ้า production/ต้องการความเร็ว: ใช้โมเดลเดิม
        # model = load_model(model_name, symbol, timeframe) 
        # scaler = load_scaler(model_name, symbol, timeframe)

        # ตั้งค่า global variable สำหรับ tracking symbol ปัจจุบัน (อัปเดต 2025-07-05)
        global current_processing_symbol
        current_processing_symbol = symbol

        confidence_threshold = load_optimal_threshold(symbol, timeframe)
        nBars_SL = load_optimal_nbars(symbol, timeframe)

        train_data, val_data, test_data, df, trade_df, stats = load_and_process_data(file, model_name, symbol, timeframe, run_identifier, model, scaler, nBars_SL, confidence_threshold)

        # แสดงช่วงข้อมูลและจำนวนวันของแต่ละชุด
        def print_data_period(name, data, trade_df):
            if data is not None and len(data[0]) > 0:
                idx = data[0].index
                # หา Entry Time จาก trade_df ตาม index
                entry_times = trade_df.loc[idx, 'Entry Time']
                entry_dates = pd.to_datetime(entry_times).dt.date
                print(f"{name}: {entry_dates.min()} ถึง {entry_dates.max()} ({(entry_dates.max() - entry_dates.min()).days + 1} วัน, {len(entry_dates)} records)")
            else:
                print(f"{name}: ไม่มีข้อมูล")

        print(f"\n✅ แสดงช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล (train/val/test)")
        print_data_period("Train", train_data, trade_df)
        print_data_period("Val", val_data, trade_df)
        print_data_period("Test", test_data, trade_df)

        print(f"\n✅ ข้อมูล df และ trade_df หลังจาก load and process data")

        if df is not None:
            print("จำนวน columns ใน df:", len(df.columns))
        else:
            print("⚠️ df เป็น None หลัง load and process data")

        if trade_df is not None:
            print("จำนวน columns ใน trade_df:", len(trade_df.columns))
        else:
            print("⚠️ trade_df เป็น None หลัง load and process data")

        # ตรวจสอบจำนวน Features หลัง load and process data
        if train_data is not None:
            X_train, y_train = train_data
            print(f"\n[INFO] จำนวน Features หลัง load and process data: {len(X_train.columns) if hasattr(X_train, 'columns') else 'N/A'}")
            print(f"[INFO] รายชื่อ Features: {list(X_train.columns) if hasattr(X_train, 'columns') else 'N/A'}")

        # ตรวจสอบ Autocorrelation ใน Target
        if trade_df is not None and 'Target' in trade_df.columns:
            from statsmodels.graphics.tsaplots import plot_acf
            
            plt.figure(figsize=(12, 4))
            plot_acf(trade_df['Target'], lags=20, alpha=0.05)
            plt.title(f'Autocorrelation ของ Target ({timeframe} {symbol})')
            acf_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_target_autocorrelation.png")
            plt.savefig(acf_path)
            plt.close()
            print(f"💾 บันทึกกราฟ Autocorrelation ที่: {acf_path}")

        # เรียกใช้การวิเคราะห์ SL/TP + ฟังก์ชันวิเคราะห์เวลา
        if trade_df is not None and not trade_df.empty:
            analyze_sl_tp_performance(trade_df)
            analyze_time_performance(trade_df, symbol, timeframe)

        # ตรวจสอบว่ามีข้อมูล หรือไม่
        if train_data is None or val_data is None or test_data is None:
            print(f"⚠️ ข้ามไฟล์ {file} เนื่องจากมีปัญหาในการโหลดหรือประมวลผลข้อมูล")
            
            # เพิ่มข้อมูลในรายงานแม้ว่าจะมีปัญหา
            results_report.append({
                "File": file,
                "Timeframe": timeframe,
                "Model": model_name,
                "Status": "Failed - No data",
                "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"),
                "Accuracy": "N/A",
                "AUC": "N/A",
                "F1 Score": "N/A"
            })
            continue

        X_train, y_train = train_data
        if len(X_train) == 0 or len(y_train) == 0:
            print(f"⚠️ ข้ามไฟล์ {file} เนื่องจากไม่มีข้อมูล ในชุดฝึก")
            
            results_report.append({
                "File": file,
                "Timeframe": timeframe,
                "Model": model_name,
                "Status": "Failed - Empty training set",
                "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"),
                "Accuracy": "N/A",
                "AUC": "N/A",
                "F1 Score": "N/A"
            })
            continue

        try:
            # รับผลลัพธ์ทั้งหมดเป็น dictionary และ scaler ที่เทรนแล้ว
            # train and evaluate คืนค่าเป็น tuple: (ผลลัพธ์ dict, scaler object)
            # แก้ไขตรงนี้: รับค่าเป็น result_dict และ trained_scaler
            try:
                result_dict, trained_scaler = train_and_evaluate(model, model_name, train_data, val_data, test_data, symbol, timeframe, trade_df=trade_df, output_folder=output_folder)

                # ดึง trained model และ features จาก result_dict
                trained_model = result_dict.get('trained_model', None)
                model_features = result_dict.get('model_features', [])
                training_success = True

            except Exception as e:
                print(f"❌ การเทรนโมเดลล้มเหลว: {str(e)}")
                print("🔄 จะข้ามการใช้ ML model และใช้ technical analysis แทน")

                # เรียกใช้ debug function เพื่อหาสาเหตุ
                print(f"\n🔍 เริ่มการ debug สำหรับ {symbol} {timeframe}...")
                debug_result = debug_train_and_evaluate_failure(file, symbol, timeframe)
                print(f"🔍 Debug result: {debug_result}")

                # ตั้งค่าเริ่มต้นเมื่อการเทรนล้มเหลว
                trained_model = None
                model_features = []
                trained_scaler = None
                result_dict = None
                training_success = False

            print(f"\n✅ ข้อมูล df และ trade_df หลังจาก train and evaluate")
            if df is not None:
                print("จำนวน columns ใน df:", len(df.columns))
            else:
                print("⚠️ df เป็น None หลัง load and process data")
            if trade_df is not None:
                print("จำนวน columns ใน trade_df:", len(trade_df.columns))
            else:
                print("⚠️ trade_df เป็น None หลัง load and process data")
            
            # ตรวจสอบจำนวน Features หลัง train and evaluate
            if result_dict is not None and 'feature_importance' in result_dict and result_dict['feature_importance'] is not None:
                features_after_train = result_dict['feature_importance'].get('Feature', None)
                if features_after_train is not None:
                    print(f"\n[INFO] จำนวน Features หลัง train and evaluate: {len(features_after_train)}")
                    print(f"[INFO] รายชื่อ Features หลัง train: {features_after_train}")
            elif train_data is not None:
                X_train, _ = train_data
                print(f"\n[INFO] จำนวน Features หลัง train and evaluate (fallback): {len(X_train.columns) if hasattr(X_train, 'columns') else 'N/A'}")

            # ตรวจสอบว่า train and evaluate ทำงานสำเร็จและคืนค่า dict มาหรือไม่
            if result_dict is None:
                print(f"⚠️ train and evaluate ล้มเหลวสำหรับไฟล์ {file} หรือคืนค่า None")
                # ถ้า train and evaluate คืนค่า None, None การจัดการ error จะถูกรวมอยู่ใน except block ด้านล่างแล้ว
                # ดังนั้นไม่ต้องทำอะไรเพิ่มเติมที่นี่ ถ้าต้องการให้มันไป trigger except block
                # หรือจะเพิ่มการจัดการข้อผิดพลาดเฉพาะจุดที่นี่ก็ได้ ถ้า logic ซับซ้อนกว่า
                # ในกรณีนี้ เนื่องจากโค้ดที่เหลือจะพยายามเข้าถึง result_dict[]
                # และถ้า result_dict เป็น None จะเกิด TypeError ซึ่งจะถูกจับโดย except block ด้านล่าง
                # การจัดการ error จึงยังทำงานอยู่ เพียงแต่ traceback จะชี้ไปที่บรรทัดถัดจาก if result_dict is None:

            else:
                # บันทึกผลลัพธ์ที่สำคัญจาก result_dict
                results.append({ # results list เดิม (อาจเปลี่ยนชื่อเป็น all_training_eval_metrics หากอยู่ใน multi-run)
                    'file': file,
                    'timeframe': timeframe,
                    # เข้าถึงค่าต่างๆ จาก result_dict
                    'accuracy': result_dict['metrics']['accuracy'],
                    'auc': result_dict['metrics']['auc'],
                    'f1_score': result_dict['metrics']['f1'],
                    'precision': result_dict['metrics']['precision'],
                    'recall': result_dict['metrics']['recall'],
                    'cv_accuracy': result_dict['cv_results']['accuracy'],
                    'cv_auc': result_dict['cv_results']['auc'],
                    'cv_f1': result_dict['cv_results']['f1']
                })

                # แสดงผลลัพธ์เปรียบเทียบจาก result_dict
                print("\n📊 เปรียบเทียบผลลัพธ์:")
                print(f"| Metric      | CV Avg    | Test Set |")
                print(f"|-------------|-----------|----------|")
                print(f"| Accuracy    | {result_dict['cv_results']['accuracy']:.4f}    | {result_dict['metrics']['accuracy']:.4f} |")
                print(f"| AUC         | {result_dict['cv_results']['auc']:.4f}    | {result_dict['metrics']['auc']:.4f} |")
                print(f"| F1 Score    | {result_dict['cv_results']['f1']:.4f}    | {result_dict['metrics']['f1']:.4f} |")

                # ส่วนการสร้างกราฟ (ไม่มีการเปลี่ยนแปลงการเข้าถึงผลลัพธ์โมเดลตรงนี้)
                if Plot_file:
                    if df is not None and trade_df is not None:

                        # print(f"\nตรวจสอบ trade_df")
                        # print(trade_df.columns) # ตรวจสอบ trade_df
                        # print(trade_df.head())

                        # TODO: ตรวจสอบและแก้ไขฟังก์ชัน plot candlestick with entries and exits
                        # ว่ารับ parameters ถูกต้องหรือไม่ และบันทึกไฟล์โดยใช้ output_folder และ run_identifier (ถ้ามี)
                        fig = plot_candlestick_with_entries_and_exits(df, trade_df, file)

                        html_file = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_candlestick_chart.html") # TODO: Add run_identifier here if using multi-run
                        fig.write_html(html_file)
                        print(f"✅ บันทึก HTML: {html_file}")

                        image_file = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_candlestick_chart.png") # TODO: Add run_identifier here
                        pio.write_image(fig, image_file, format="png", width=1200, height=800)
                        print(f"✅ บันทึกรูปภาพ PNG: {image_file}")
                        # เพิ่มแจ้งเตือน ⚠️ชั่วคราว เนื่องจากโค้ด plot ยังไม่ได้รวมใน snippet นี้
                        print("ℹ️ ข้ามการสร้างกราฟ Candlestick ในโค้ด Main นี้ (ต้องแก้ไขฟังก์ชันแยก)")

                    else:
                        print(f"⚠️ ไม่สามารถสร้างกราฟสำหรับไฟล์: {file} เนื่องจากข้อมูลไม่สมบูรณ์")

                # เตรียมข้อมูลสำหรับรายงานสรุปใน main (results_report list เดิม)
                results_report.append({ # results_report list เดิม (อาจเปลี่ยนชื่อเป็น all_file_run_summary_reports หากอยู่ใน multi-run)
                    "File": file,
                    "Timeframe": timeframe,
                    "Model": "LightGBM",
                    # เข้าถึงค่าต่างๆ จาก result_dict
                    "Accuracy": f"{result_dict['metrics']['accuracy']:.4f}",
                    "AUC": f"{result_dict['metrics']['auc']:.4f}",
                    "F1 Score": f"{result_dict['metrics']['f1']:.4f}",
                    "Precision": f"{result_dict['metrics']['precision']:.4f}",
                    "Recall": f"{result_dict['metrics']['recall']:.4f}",
                    "CV Accuracy": f"{result_dict['cv_results']['accuracy']:.4f}",
                    "CV AUC": f"{result_dict['cv_results']['auc']:.4f}",
                    "CV F1": f"{result_dict['cv_results']['f1']:.4f}",
                    "Status": "Success",
                    "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
                        # TODO: หากอยู่ใน multi-run loop ต้องเพิ่ม "Run": run_i + 1 ที่นี่
                        # TODO: เพิ่ม Backtest Metrics จาก trade_df/stats ถ้าต้องการรวมในรายงานสรุปหลัก
                })

        except Exception as e:
            # หาก train and evaluate เกิดข้อผิดพลาด ⚠️ ภายในแล้ว propagate ออกมา หรือ
            # หาก result_dict เป็น None แล้วโค้ดด้านบนพยายามเข้าถึง key จะเกิด TypeError ซึ่งถูกจับที่นี่
            print(f"⚠️ เกิดข้อผิดพลาด ขณะประมวลผลไฟล์ {file} (หลัง train and evaluate): {str(e)}")
            traceback.print_exc() # พิมพ์ traceback เพื่อดูรายละเอียดข้อผิดพลาด
            results_report.append({ # results_report list เดิม
                "File": file,
                "Timeframe": timeframe,
                "Model": model_name,
                "Status": f"Failed - Post-Train Process: {str(e)}",
                "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"),
                "Accuracy": "N/A",
                "AUC": "N/A",
                "F1 Score": "N/A",
            })

        print(f"\n✅ ข้อมูล df และ trade_df ก่อนเข้า entry conditions")
        if df is not None:
            print("จำนวน columns ใน df:", len(df.columns))
        else:
            print("⚠️ df เป็น None หลัง load and process data")
        if trade_df is not None:
            print("จำนวน columns ใน trade_df:", len(trade_df.columns))
        else:
            print("⚠️ trade_df เป็น None หลัง load and process data")

        # ตรวจสอบ features ก่อนเข้าลูปแต่ละ entry condition
        model_dir = f"Test_LightGBM/models/{str(timeframe).zfill(3)}_{symbol}"
        model_features_path = os.path.join(model_dir, f"{model_name}_{str(timeframe).zfill(3)}_{symbol}_features.pkl")
        if os.path.exists(model_features_path):
            try:
                model_features = joblib.load(model_features_path)
                print(f"\n[INFO] ตรวจสอบ Features ก่อนเข้าลูป entry condition: {len(model_features)} features")
                print(f"[INFO] รายชื่อ Features: {model_features}")
            except Exception as e:
                print(f"⚠️ เกิดข้อผิดพลาด ขณะโหลดรายชื่อ Features: {str(e)}")
        else:
            print(f"\n⚠️ ไม่พบไฟล์รายชื่อ Features ที่ Model ใช้: {model_features_path}")

        # 1. กำหนด entry conditions
        # 2. วนลูปแต่ละ entry condition
        all_results = {}
        best_entry_name = None

        # เช็คจำนวนแถวหลังแต่ละ entry condition
        # print(f"\nBefore entry condition loop: trade_df rows = {len(trade_df)}")
        # print("trade_df columns:", trade_df.columns.tolist())

        # หลังจากแบ่ง train/val/test แล้ว
        # ให้ reset index ของ trade_df ก่อน
        df = df.reset_index(drop=True)
        train_val_len = len(train_data[0]) + len(val_data[0])
        train_val_df = df.iloc[:train_val_len]

        for entry_name, entry_func in entry_conditions.items():

            # เช็คขนาด trade_df ก่อนเข้า loop
            print(f"\n{'='*40}")
            print(f"✅ Testing entry condition: {entry_name}")
            print(f"{'='*40}")

            print(f"✅ ข้อมูล df ก่อนเข้า entry conditions : create trade cycles with model")
            if df is not None:
                print("จำนวน columns ใน df:", len(df.columns))
            else:
                print("⚠️ df เป็น None หลัง load and process data")

            this_train_val_df = train_val_df.copy()

            # ตรวจสอบว่าการเทรนสำเร็จหรือไม่
            if training_success and trained_model is not None:
                trade_df, stats = create_trade_cycles_with_model(
                    this_train_val_df,
                    # trained_model=trained_model,  # ใช้ trained_model ที่ได้จาก result_dict
                    # scaler=trained_scaler,  # ใช้ trained_scaler ที่ได้จาก train_and_evaluate
                    trained_model=model,
                    scaler=scaler,
                    model_features=model_features,  # ใช้ model_features ที่ได้จาก result_dict
                rsi_level=input_rsi_level_in,
                rsi_level_out=input_rsi_level_out,
                stop_loss_atr_multiplier=input_stop_loss_atr,
                take_profit_stop_loss_ratio=input_take_profit,
                symbol=symbol,
                timeframe=timeframe,
                identifier=None,
                nBars_SL=nBars_SL,
                model_confidence_threshold=confidence_threshold,
                    entry_condition_func=entry_func,
                    entry_condition_name=entry_name
                )
            else:
                # ใช้ technical analysis แทนเมื่อไม่มี ML model
                print("⚠️ ใช้ Technical Analysis แทน ML Model")
                trade_df, stats = create_trade_cycles_with_model(
                    this_train_val_df,
                    trained_model=None,  # ไม่ใช้ ML model
                    scaler=None,
                    model_features=None,
                    rsi_level=input_rsi_level_in,
                    rsi_level_out=input_rsi_level_out,
                    stop_loss_atr_multiplier=input_stop_loss_atr,
                    take_profit_stop_loss_ratio=input_take_profit,
                    symbol=symbol,
                    timeframe=timeframe,
                    identifier=None,
                    nBars_SL=nBars_SL,
                    entry_condition_func=entry_func,
                    entry_condition_name=entry_name
                )

            print(f"\nAfter {entry_name}: trades generated = {len(trade_df)}")

            print(f"\n✅ ข้อมูล trade_df หลังจาก entry conditions : create trade cycles with model")
            if trade_df is not None:
                print("จำนวน columns ใน trade_df:", len(trade_df.columns))
            else:
                print("⚠️ trade_df เป็น None หลัง load and process data")

            print(f"\n--- {entry_name} ---")
            if trade_df is not None and not trade_df.empty:
                print(trade_df['Exit Condition'].value_counts())
                print(trade_df['Profit'].describe())
                if 'Target' in trade_df.columns:
                    print(trade_df['Target'].value_counts())
                # คำนวณ win_rate และ expectancy จาก trade_df จริง
                perf = analyze_trade_performance(trade_df)
                expectancy = perf['buy_sell']['expectancy']
                win_rate = perf['buy_sell']['win_rate']
                num_trades = len(trade_df)

                # คำนวณกำไรรวมและกำไรเฉลี่ยต่อเทรด
                total_profit = trade_df['Profit'].sum()
                avg_profit_per_trade = trade_df['Profit'].mean()
            else:
                print("No trades generated.")
                expectancy = 0
                win_rate = 0
                num_trades = 0
                total_profit = 0.0
                avg_profit_per_trade = 0.0

            all_results[entry_name] = {
                "expectancy": expectancy,
                "win_rate": win_rate,
                "num_trades": num_trades,
                "total_profit": total_profit,
                "avg_profit_per_trade": avg_profit_per_trade,
            }

        # บันทึกสรุปการเทรดเป็นไฟล์ .txt สำหรับ entry condition ที่ดีที่สุด
        if all_results:
            best_entry = max(all_results.keys(), key=lambda k: all_results[k]['expectancy'])
            best_stats = all_results[best_entry]

            # สร้าง trade_stats สำหรับบันทึกไฟล์
            # แก้ไข: win_rate จาก calculate_stats เป็นเปอร์เซ็นต์ (0-100) ต้องแปลงเป็นทศนิยม (0-1)
            win_rate_decimal = best_stats['win_rate'] / 100.0  # แปลงจากเปอร์เซ็นต์เป็นทศนิยม

            trade_stats = {
                'total_trades': best_stats['num_trades'],
                'winning_trades': int(best_stats['num_trades'] * win_rate_decimal),
                'losing_trades': int(best_stats['num_trades'] * (1 - win_rate_decimal)),
                'win_rate': best_stats['win_rate'],  # เก็บเป็นเปอร์เซ็นต์สำหรับแสดงผล
                'total_profit': best_stats.get('total_profit', 0.0),
                'avg_profit_per_trade': best_stats.get('avg_profit_per_trade', 0.0),
                'expectancy': best_stats['expectancy'],
                'best_entry_condition': best_entry,
                'entry_conditions_summary': all_results
            }

            # บันทึกสรุปการเทรดเป็นไฟล์
            save_trading_summary_to_file(symbol, timeframe, result_dict, trade_stats)

        # 3. บันทึกผลลัพธ์เปรียบเทียบ
        with open(os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_compare_entry.txt"), "w") as f:
            for entry_name, result in all_results.items():
                f.write(f"{entry_name}: {result}\n")

        print(f"\n{'='*40}")

        # 4. plot เปรียบเทียบ
        labels = list(all_results.keys())
        expectancies = [all_results[k]["expectancy"] for k in labels]
        plt.bar(labels, expectancies)
        plt.title(f"Expectancy by Entry Condition ({symbol} {timeframe})")
        plt.savefig(os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_expectancy_compare.png"))

        min_trades_divisor = 10.0

        if 'DateTime' in train_val_df.columns:
            start_date = train_val_df['DateTime'].min()
            end_date = train_val_df['DateTime'].max()

            # # รวมวันเสาร์-อาทิตย์ ด้วย เพราะเป็นการลบวันที่แบบตรงๆ
            # num_days = (end_date - start_date).days + 1  # +1 เพื่อรวมวันแรก

            # กรองเฉพาะวันจันทร์-ศุกร์ (weekday 0-4)
            all_dates = pd.to_datetime(train_val_df['DateTime']).dt.date.unique()
            business_days = [d for d in all_dates if pd.Timestamp(d).weekday() < 5]
            num_days = len(business_days)

            min_trades = max(5, int(num_days / min_trades_divisor))     # ป้องกัน min_trades ต่ำเกินไป
            print(f"ช่วงเวลาที่จะทำ backtest: {start_date} ถึง {end_date} ({num_days} วัน) -> min_trades = {min_trades}")
        else:
            min_trades = 10  # fallback
            print(f"min_trades = {min_trades}")

        # เลือก entry condition ที่ดีที่สุด (expectancy สูงสุด ในกลุ่มที่มี trade >= min_trades)
        candidates = [item for item in all_results.items() if item[1]['num_trades'] >= min_trades]
        if candidates:
            best_entry = max(candidates, key=lambda x: x[1]['expectancy'])
        else:
            # ถ้าไม่มีใครผ่านเกณฑ์ ให้ใช้ค่าเดิมจากรอบก่อน (ถ้ามีไฟล์ best_entry)
            best_entry_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_best_entry.pkl")
            if os.path.exists(best_entry_path):
                with open(best_entry_path, "rb") as f:
                    best_entry_info = pickle.load(f)
                best_entry_name = best_entry_info.get("entry_name", None)
                print(f"⭐ ใช้ entry condition เดิมจากรอบก่อน: {best_entry_name}")
            else:
                # ถ้าไม่มีค่าเดิมเลย fallback เป็น None
                best_entry_name = None
                print("⚠️ ไม่พบ entry condition ที่ผ่านเกณฑ์ขั้นต่ำและไม่มีค่าเดิม")
            best_entry = None

        if best_entry:
            best_entry_name, best_entry_result = best_entry
            print(f"⭐ Entry Condition ที่ดีที่สุดสำหรับ {symbol} {timeframe}: {best_entry_name} (Expectancy={best_entry_result['expectancy']:.2f}, WinRate={best_entry_result['win_rate']:.2f}, NumTrades={best_entry_result['num_trades']})")

            # บันทึก best_entry.pkl เพื่อให้ WebRequest server ใช้งาน
            if Save_File:
                best_entry_info = {
                    "entry_name": best_entry_name,
                    "expectancy": best_entry_result['expectancy'],
                    "win_rate": best_entry_result['win_rate'],
                    "num_trades": best_entry_result['num_trades'],
                    "timestamp": datetime.datetime.now().isoformat(),
                    "symbol": symbol,
                    "timeframe": timeframe,
                    "all_results": all_results  # เก็บผลทั้งหมดไว้ด้วย
                }
                best_entry_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_best_entry.pkl")
                try:
                    with open(best_entry_path, 'wb') as f:
                        pickle.dump(best_entry_info, f)
                    print(f"✅ บันทึก best_entry: {best_entry_path}")
                except Exception as e:
                    print(f"⚠️ ไม่สามารถบันทึก best_entry: {e}")

        # === เพิ่มส่วนนี้เพื่อบันทึกผลแต่ละรอบ ===
        # เตรียมค่าที่ต้องการบันทึก
        
        features_count = len(X_train.columns) if train_data is not None else 0
        save_entry_condition_summary(
            symbol=symbol,
            timeframe=timeframe,
            nbars=nBars_SL,
            threshold=confidence_threshold,
            features_count=features_count,
            entry_results=all_results,
            best_entry_name=best_entry_name,
            output_folder=output_folder,
            start_date=start_date,
            end_date=end_date
        )
        print(f"\n✅ บันทึกผลการทดสอบรอบที่ ที่ไฟล์ .txt แล้ว")

        print(f"สิ้นสุดการทดสอบ entry condition")
        print(f"{'='*40}")

    if len(input_files) > 1.0:
        # ขั้นตอนการวิเคราะห์และบันทึก Features สำคัญข้าม Assets (รันหลังจากเทรนทุก Asset ครั้งแรก/มีการอัปเดต) ---

        # importance_results_directory = r'D:\test_gold\Test_LightGBM\results' # โฟลเดอร์ที่เก็บไฟล์ feature_importance_*.csv
        # แก้ไขปัญหา group_name เป็น None
        if group_name is None:
            group_name = "default"
        importance_results_directory = os.path.join('Test_LightGBM', 'results', group_name)

        feature_importance_analysis_dir = os.path.join('Test_LightGBM', 'feature_importance') # โฟลเดอร์ที่จะเก็บไฟล์ .pkl

        if Save_File:
            must_have_features_pickle_file = os.path.join(feature_importance_analysis_dir, f'{str(timeframe).zfill(3)}_must_have_features.pkl')
        
        print("\nเริ่มขั้นตอนวิเคราะห์ Feature Importance ข้าม Assets")
        analyzed_must_have_features = analyze_cross_asset_feature_importance(
            input_files = input_files,
            importance_files_dir = importance_results_directory,
            pickle_output_path = must_have_features_pickle_file,

            # เงื่อนไขที่ 1 : ใช้เกณฑ์แบบ Asset Count:
            num_top_features_per_asset = 15, # พิจารณา Features ใน Top 15 ของแต่ละ Asset
            min_assets_threshold = 5, # ต้องปรากฏใน Top 20 ของอย่างน้อย 4 Assets

            # เงื่อนไขที่ 2 : เลือกจากลำดับสูงสุด จากการเรียงลำดับ
            overall_top_n = 8, # เลือก 20 Features ที่สำคัญเฉลี่ยสูงสุด
        )
        # ขั้นตอนการทำงานของโค้ด : เมื่อตั้งเงื่อนไขทั้ง 2 ข้อพร้อมกัน
        # หา Features ทั้งหมดที่ปรากฏใน Feature Importance CSV ของอย่างน้อย 4 Assets (ในบรรดา Assets ที่ไฟล์ Importance ถูกพบ) และในแต่ละ Asset ที่ปรากฏนั้น Feature นั้นต้องอยู่ในอันดับ Top 20 ของ Asset นั้นๆ
        # นำ Features ที่ผ่านเกณฑ์ในข้อ 1 มาเรียงลำดับตามค่า Avg_Importance จากมากไปน้อย
        # เลือก Features ที่อยู่ในอันดับ 1 ถึง 25 จากรายการที่เรียงแล้วในข้อ 2

        print(f"\nFeatures ที่ได้จากการวิเคราะห์เพื่อบันทึกลง .pkl: {analyzed_must_have_features}")

    # ส่วนการสร้างรายงาน (แก้ไขให้ยืดหยุ่นมากขึ้น)
    try:
        # สร้าง DataFrame สำหรับผลลัพธ์
        results_df = pd.DataFrame(results) if results else pd.DataFrame()
        final_report_df = pd.DataFrame(results_report)
        
        # กำหนดคอลัมน์ที่ต้องการแสดง (เฉพาะที่มีอยู่จริง)
        available_columns = final_report_df.columns.tolist()
        desired_columns = [
            "File", "Timeframe", "Model", "Timestamp", "Status",
            "Accuracy", "AUC", "F1 Score", "Precision", "Recall",
            "CV Accuracy", "CV AUC", "CV F1"
        ]
        
        # เลือกเฉพาะคอลัมน์ที่มีอยู่จริง
        columns_to_show = [col for col in desired_columns if col in available_columns]
        
        # เรียงลำดับคอลัมน์ใหม่
        final_report_df = final_report_df[columns_to_show]

        # บันทึกผลลัพธ์
        if not results_df.empty:
            results_df.to_csv(os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_final_results.csv"), index=False, encoding='utf-8-sig')
        final_report_df.to_csv(os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_training_results.csv"), index=False, encoding='utf-8-sig')
        
        # แสดงผลลัพธ์
        print("\nผลลัพธ์การทดสอบโดยสรุป:")
        print(final_report_df.to_string(index=False))
        
        print(f"\n💾 บันทึกผลลัพธ์ทั้งหมดที่: {os.path.join(output_folder, f'{str(timeframe).zfill(3)}_{symbol}_final_results.csv')}")
        print("✅ บันทึกผลลัพธ์การฝึกโมเดลเรียบร้อยแล้ว")

        # วิเคราะห์และพล็อตผลลัพธ์ (เฉพาะกรณีที่มีผลลัพธ์)
        if not results_df.empty:
            analyze_results(results, symbol, timeframe, group_name)
            
            # เปลี่ยนชื่อคอลัมน์ให้สอดคล้องกันหากจำเป็น
            if 'file' in results_df.columns and 'File' not in results_df.columns:
                results_df = results_df.rename(columns={'file': 'File'})
            if 'accuracy' in results_df.columns and 'Test Accuracy' not in results_df.columns:
                results_df = results_df.rename(columns={'accuracy': 'Test Accuracy'})

            # เรียกใช้ฟังก์ชันพล็อตผลลัพธ์แบบปลอดภัย
            safe_plot_results(results_df, symbol, timeframe)
            
        # บันทึกรายงานแบบละเอียด
        if not results_df.empty:
            save_enhanced_report(results, output_folder, symbol, timeframe)
            
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะสร้างรายงาน: {str(e)}")

    # --- หยุดจับเวลาทั้งหมด ---
    end_time_total = time.perf_counter()
    total_duration = end_time_total - start_time_total
    total_files = len(input_files)

    # --- แสดงผลเวลารวมและเวลาเฉลี่ย ---
    print(f"\n{'='*40}")
    print(f"⏱️ สรุปเวลาการประมวลผลทั้งหมด")
    print(f"{'='*40}")
    print(f"1. เวลาทั้งหมดที่ใช้ในการประมวลผล: {total_duration:.4f} วินาที จำนวนไฟล์ {total_files}")

    # คำนวณเวลาเฉลี่ยต่อไฟล์ (ใช้จำนวนไฟล์ทั้งหมดใน input_files)
    if total_files > 0:
        average_time_per_file = total_duration / total_files
        print(f"2. เวลาเฉลี่ยที่ใช้ในการประมวลผลต่อ 1 ไฟล์ ({total_files} ไฟล์ รอบที่ {run_identifier}): {average_time_per_file:.4f} วินาที")
    else:
        print("ไม่มีไฟล์ให้ประมวลผล ไม่สามารถคำนวณเวลาเฉลี่ยได้")

    print("✅ การประมวลผลเสร็จสิ้น")

    # Return results for further analysis
    # แปลง results list เป็น dictionary โดยใช้ symbol_timeframe เป็น key
    results_dict = {}
    if 'results' in locals() and results:
        for result in results:
            if isinstance(result, dict) and 'symbol' in result and 'timeframe' in result:
                key = f"{result['symbol']}_{result['timeframe']}"
                results_dict[key] = result

    print(f"📊 ส่งคืนผลลัพธ์: {len(results_dict)} รายการ")
    return results_dict

def run_main_analysis():
    """รันการวิเคราะห์หลัก"""
    print(f"\n🏗️ เปิดใช้งาน run main analysis") if Steps_to_do else None

    all_results = {}  # เก็บผลลัพธ์ทั้งหมด

    # วนลูปแต่ละกลุ่ม (M30, M60)
    for group_name, group_files in test_groups.items():
        
        # --- เริ่มจับเวลาทั้งหมด ---
        start_time_total = time.perf_counter()

        all_runs_results_report = [] # เก็บรายงานสรุปของทุกไฟล์ในทุกรอบ
        print(f"\n=== เริ่มการเทรน กลุ่ม {group_name} ทั้งหมด {NUM_TRAINING_ROUNDS} รอบ ===")
        input_files = group_files  # กำหนด input_files สำหรับกลุ่มนี้

        if(len(input_files) > 1.0):
            Save_File = True
            print(f"✅ เปิดใช้งานการบันทึกข้อมูลลงไฟล์ .pkl")

        # กำหนด output_folder เฉพาะกลุ่ม (เช่น Test_LightGBM/results/M30)
        output_folder = f"Test_LightGBM/results/{group_name}"
        os.makedirs(output_folder, exist_ok=True)

        # เพิ่ม for loop สำหรับจำนวนรอบ
        for run_i in range(NUM_TRAINING_ROUNDS):
            current_run_identifier = run_i + 1
            print(f"\n### เริ่มการเทรนรอบที่ {current_run_identifier}/{NUM_TRAINING_ROUNDS} ของกลุ่ม {group_name} ###")
            try:
                round_results = main(run_identifier=current_run_identifier, group_name=group_name, input_files=input_files)
                if round_results:
                    # เก็บผลลัพธ์แต่ละรอบ
                    for key, value in round_results.items():
                        all_results[f"{key}_round_{current_run_identifier}"] = value
            except Exception as e:
                print(f"\n⚠️ เกิดข้อผิดพลาดในรอบที่ {current_run_identifier}: {e}")
                traceback.print_exc()

        # --- หยุดจับเวลาทั้งหมด ---
        end_time_total = time.perf_counter()
        total_duration = end_time_total - start_time_total
        total_files = len(input_files)

        # --- แสดงผลเวลารวมและเวลาเฉลี่ย ---
        print(f"{'='*50}")
        print(f"⏱️ สรุปเวลาการประมวลผลทั้งหมด")
        print(f"1. เวลาทั้งหมดที่ใช้ในการประมวลผล: {total_duration:.4f} วินาที จำนวนไฟล์ {total_files} เทรนทั้งหมด {NUM_TRAINING_ROUNDS} รอบ")

        # คำนวณเวลาเฉลี่ยต่อชุดไฟล์ (ใช้จำนวนไฟล์ทั้งหมดใน input_files)
        if NUM_TRAINING_ROUNDS > 0:
            average_time_per_file = total_duration / NUM_TRAINING_ROUNDS
            print(f"2. เวลาเฉลี่ยที่ใช้ในการประมวลผลต่อ ชุดไฟล์ ({NUM_TRAINING_ROUNDS}): {average_time_per_file:.4f} วินาที")
        else:
            print("ไม่มีไฟล์ให้ประมวลผล ไม่สามารถคำนวณเวลาเฉลี่ยได้")

        summary_path = os.path.join(test_folder, f"{group_name}_time_summary.txt")
        with open(summary_path, "a", encoding="utf-8") as f:
            f.write(f"Run {NUM_TRAINING_ROUNDS} | {datetime.datetime.now():%Y-%m-%d %H:%M:%S}\n")
            f.write(f"Total time: {total_duration:.4f} sec | Files: {total_files} | Rounds: {NUM_TRAINING_ROUNDS}\n")
            f.write(f"Avg time per round: {average_time_per_file:.4f} sec\n")
            f.write("="*50 + "\n")

        print(f"=== การเทรนทั้งหมด {NUM_TRAINING_ROUNDS} รอบเสร็จสิ้น ===")
        print(f"{'='*50}")

        if group_name=="M60":
            # สร้างสรุปการเทรดรายวันและบันทึกเป็นไฟล์
            print(f"\n🏗️ กำลังสร้างสรุปการเทรดรายวัน...")
            generate_all_trading_schedule_summaries()

        # แจ้งเตือนเมื่อ ครบรอบการเทรน
        winsound.Beep(1000, 300)  # 1000 Hz, 300 ms

    # แจ้งเตือนเมื่อ สิ้นสุดการเทรน
    winsound.Beep(1000, 300)  # 1000 Hz, 300 ms

    print(f"\n📊 สรุปผลการเทรนทั้งหมด:")
    print(f"   • จำนวนผลลัพธ์ที่เก็บได้: {len(all_results)}")
    print(f"   • กลุ่มที่เทรน: {list(test_groups.keys())}")
    print(f"   • จำนวนรอบต่อกลุ่ม: {NUM_TRAINING_ROUNDS}")

    return all_results

def parse_arguments():
    """Parse command line arguments"""
    print(f"\n🏗️ เปิดใช้งาน parse arguments") if Steps_to_do else None

    parser = argparse.ArgumentParser(description='LightGBM Training Script with Improved Parameters')

    parser.add_argument('--force-retune', action='store_true',
                       help='บังคับเปิด hyperparameter tuning (ไม่สนใจ flag files)')

    parser.add_argument('--reset-flags', action='store_true',
                       help='ลบไฟล์ flag และ best_params ก่อนเริ่มเทรน')

    parser.add_argument('--symbols', nargs='+',
                       help='เลือก symbols ที่ต้องการเทรน เช่น --symbols USDJPY GBPUSD')

    parser.add_argument('--timeframes', nargs='+',
                       help='เลือก timeframes ที่ต้องการเทรน เช่น --timeframes M30 H1')

    return parser.parse_args()

def reset_training_files(symbols=None, timeframes=None):
    """ลบไฟล์ flag และ best_params"""
    print(f"\n🏗️ เปิดใช้งาน reset training files") if Steps_to_do else None

    import glob

    models_dir = "Test_LightGBM/models"

    if symbols and timeframes:
        # ลบเฉพาะ symbols และ timeframes ที่กำหนด
        for symbol in symbols:
            for timeframe in timeframes:
                timeframe_padded = str(timeframe).zfill(3) if timeframe.isdigit() else timeframe
                pattern_base = f"{timeframe_padded}_{symbol}"

                flag_files = glob.glob(f"{models_dir}/**/{pattern_base}_tuning_flag.json", recursive=True)
                params_files = glob.glob(f"{models_dir}/**/{pattern_base}_best_params.json", recursive=True)

                for file_path in flag_files + params_files:
                    try:
                        os.remove(file_path)
                        print(f"✅ ลบ: {file_path}")
                    except Exception as e:
                        print(f"❌ ไม่สามารถลบ {file_path}: {e}")
    else:
        # ลบทั้งหมด
        flag_files = glob.glob(f"{models_dir}/**/*_tuning_flag.json", recursive=True)
        params_files = glob.glob(f"{models_dir}/**/*_best_params.json", recursive=True)

        for file_path in flag_files + params_files:
            try:
                os.remove(file_path)
                print(f"✅ ลบ: {file_path}")
            except Exception as e:
                print(f"❌ ไม่สามารถลบ {file_path}: {e}")

if __name__ == "__main__":
    print(f"\n🏗️ เปิดใช้งาน __name__") if Steps_to_do else None

    # Parse command line arguments
    args = parse_arguments()

    # ตั้งค่า global variable สำหรับ force retuning
    if args.force_retune:
        print("🔄 --force-retune: บังคับเปิด hyperparameter tuning")
        globals()['_force_retune'] = True
    else:
        globals()['_force_retune'] = False

    # ลบไฟล์ flag และ best_params ถ้าต้องการ
    if args.reset_flags:
        print("🗑️ --reset-flags: ลบไฟล์ flag และ best_params")
        reset_training_files(args.symbols, args.timeframes)

    # กรองไฟล์ตาม symbols และ timeframes ที่กำหนด
    if args.symbols or args.timeframes:
        print(f"🎯 เลือกเฉพาะ symbols: {args.symbols}")
        print(f"🎯 เลือกเฉพาะ timeframes: {args.timeframes}")
        # จะใช้ในการกรองไฟล์ใน main loop

        # เรียกใช้การวิเคราะห์หลักเฉพาะเมื่อมี command line arguments
        print("🚀 เริ่มการวิเคราะห์ด้วย command line arguments")
        all_results = run_main_analysis()

        if all_results and len(all_results) > 0:
            print(f"📋 พบผลลัพธ์: {len(all_results)} รายการ")
            analyze_performance_comparison()
            create_trading_recommendations()
        else:
            print("⚠️ ไม่พบผลลัพธ์จากการเทรนโมเดล")
    else:
        print("ℹ️ ไม่มี command line arguments เฉพาะ - จะรันการวิเคราะห์แบบปกติ")

def analyze_performance_comparison():
    """วิเคราะห์เปรียบเทียบประสิทธิภาพ M30 vs H1"""
    print(f"\n🏗️ เปิดใช้งาน analyze performance comparison") if Steps_to_do else None
    
    print("\n" + "="*80)
    print("📊 การวิเคราะห์เปรียบเทียบประสิทธิภาพ M30 vs H1")
    print("="*80)

    # ข้อมูลจากการวิเคราะห์
    m30_data = {
        'AUDUSD': {'Accuracy': 66.85, 'AUC': 79.34, 'F1': 55.64, 'CV_Acc': 60.39, 'CV_AUC': 79.50},
        'EURGBP': {'Accuracy': 65.12, 'AUC': 80.47, 'F1': 53.67, 'CV_Acc': 50.74, 'CV_AUC': 70.32},
        'EURUSD': {'Accuracy': 77.46, 'AUC': 92.78, 'F1': 66.55, 'CV_Acc': 74.52, 'CV_AUC': 90.87},
        'GBPUSD': {'Accuracy': 76.58, 'AUC': 92.84, 'F1': 68.56, 'CV_Acc': 72.17, 'CV_AUC': 91.11},
        'GOLD': {'Accuracy': 84.34, 'AUC': 96.40, 'F1': 83.83, 'CV_Acc': 80.25, 'CV_AUC': 95.17},
        'NZDUSD': {'Accuracy': 87.52, 'AUC': 96.14, 'F1': 73.87, 'CV_Acc': 0.00, 'CV_AUC': 50.00},
        'USDCAD': {'Accuracy': 85.37, 'AUC': 96.04, 'F1': 75.73, 'CV_Acc': 81.85, 'CV_AUC': 94.92},
        'USDJPY': {'Accuracy': 44.51, 'AUC': 0.00, 'F1': 33.23, 'CV_Acc': 42.88, 'CV_AUC': 69.54}
    }

    h1_data = {
        'AUDUSD': {'Accuracy': 84.86, 'AUC': 94.71, 'F1': 76.60, 'CV_Acc': 80.63, 'CV_AUC': 93.86},
        'EURGBP': {'Accuracy': 75.41, 'AUC': 91.12, 'F1': 68.23, 'CV_Acc': 72.74, 'CV_AUC': 91.09},
        'EURUSD': {'Accuracy': 77.55, 'AUC': 93.72, 'F1': 75.22, 'CV_Acc': 78.45, 'CV_AUC': 94.10},
        'GBPUSD': {'Accuracy': 79.54, 'AUC': 95.33, 'F1': 78.29, 'CV_Acc': 76.80, 'CV_AUC': 93.81},
        'GOLD': {'Accuracy': 81.82, 'AUC': 93.30, 'F1': 81.41, 'CV_Acc': 79.56, 'CV_AUC': 94.18},
        'NZDUSD': {'Accuracy': 83.01, 'AUC': 95.36, 'F1': 74.02, 'CV_Acc': 0.00, 'CV_AUC': 50.00},
        'USDCAD': {'Accuracy': 75.45, 'AUC': 91.74, 'F1': 73.94, 'CV_Acc': 0.00, 'CV_AUC': 50.00},
        'USDJPY': {'Accuracy': 43.65, 'AUC': 0.00, 'F1': 33.65, 'CV_Acc': 44.90, 'CV_AUC': 65.20}
    }

    print("\n🏆 TOP PERFORMERS:")
    print("-" * 60)

    # หา top performers
    for metric in ['Accuracy', 'AUC', 'F1']:
        print(f"\n📈 {metric} Champions:")
        m30_best = max(m30_data.items(), key=lambda x: x[1][metric])
        h1_best = max(h1_data.items(), key=lambda x: x[1][metric])

        print(f"  M30: {m30_best[0]} = {m30_best[1][metric]:.2f}%")
        print(f"  H1:  {h1_best[0]} = {h1_best[1][metric]:.2f}%")

        if m30_best[1][metric] > h1_best[1][metric]:
            print(f"  🥇 Winner: M30 (+{m30_best[1][metric] - h1_best[1][metric]:.2f}%)")
        else:
            print(f"  🥇 Winner: H1 (+{h1_best[1][metric] - m30_best[1][metric]:.2f}%)")

    print("\n⚠️ PROBLEM CASES:")
    print("-" * 60)

    # หา problem cases
    problem_symbols = []
    for symbol in m30_data.keys():
        m30_cv = m30_data[symbol]['CV_Acc']
        h1_cv = h1_data[symbol]['CV_Acc']
        m30_auc = m30_data[symbol]['AUC']
        h1_auc = h1_data[symbol]['AUC']

        if m30_cv == 0 or h1_cv == 0 or m30_auc == 0 or h1_auc == 0:
            problem_symbols.append(symbol)
            print(f"  ❌ {symbol}:")
            if m30_cv == 0: print(f"    - M30 CV not working")
            if h1_cv == 0: print(f"    - H1 CV not working")
            if m30_auc == 0: print(f"    - M30 AUC = 0")
            if h1_auc == 0: print(f"    - H1 AUC = 0")

    print(f"\n📊 SUMMARY:")
    print("-" * 60)
    working_symbols = [s for s in m30_data.keys() if s not in problem_symbols]
    print(f"✅ Working symbols: {len(working_symbols)}/8 ({', '.join(working_symbols)})")
    print(f"❌ Problem symbols: {len(problem_symbols)}/8 ({', '.join(problem_symbols)})")

    # คำนวณค่าเฉลี่ยสำหรับ symbols ที่ทำงานได้
    if working_symbols:
        m30_avg_acc = sum(m30_data[s]['Accuracy'] for s in working_symbols) / len(working_symbols)
        h1_avg_acc = sum(h1_data[s]['Accuracy'] for s in working_symbols) / len(working_symbols)
        m30_avg_f1 = sum(m30_data[s]['F1'] for s in working_symbols) / len(working_symbols)
        h1_avg_f1 = sum(h1_data[s]['F1'] for s in working_symbols) / len(working_symbols)

        print(f"\n📈 Average Performance (Working Symbols Only):")
        print(f"  M30: Acc={m30_avg_acc:.1f}%, F1={m30_avg_f1:.1f}%")
        print(f"  H1:  Acc={h1_avg_acc:.1f}%, F1={h1_avg_f1:.1f}%")

        if h1_avg_acc > m30_avg_acc:
            print(f"  🏆 H1 performs better overall (+{h1_avg_acc-m30_avg_acc:.1f}% Accuracy)")
        else:
            print(f"  🏆 M30 performs better overall (+{m30_avg_acc-h1_avg_acc:.1f}% Accuracy)")

def create_trading_recommendations():
    """สร้างคำแนะนำการเทรดรายวัน"""
    print(f"\n🏗️ เปิดใช้งาน create trading recommendations") if Steps_to_do else None
    
    print("\n" + "="*80)
    print("📅 คำแนะนำการเทรดรายวัน (Updated Trading Schedule)")
    print("="*80)

    # ข้อมูลจากการวิเคราะห์ (ปรับปรุงจากข้อมูลเดิม)
    daily_performance = {
        'Monday': {'win_rate': 18.83, 'expectancy': -14.79, 'recommendation': 'AVOID'},
        'Tuesday': {'win_rate': 17.59, 'expectancy': -14.89, 'recommendation': 'AVOID'},
        'Wednesday': {'win_rate': 16.77, 'expectancy': -27.46, 'recommendation': 'AVOID'},
        'Thursday': {'win_rate': 18.45, 'expectancy': -12.67, 'recommendation': 'AVOID'},
        'Friday': {'win_rate': 16.85, 'expectancy': -15.11, 'recommendation': 'AVOID'}
    }

    print("\n🚨 CURRENT ANALYSIS SHOWS POOR DAILY PERFORMANCE")
    print("📊 All days show negative expectancy - Need strategy revision!")
    print("-" * 60)

    for day, data in daily_performance.items():
        status = "❌" if data['recommendation'] == 'AVOID' else "✅"
        print(f"{status} {day:12} | Win Rate: {data['win_rate']:5.1f}% | Expectancy: {data['expectancy']:6.1f}")

    print("\n💡 RECOMMENDED ACTIONS:")
    print("-" * 60)
    print("1. 🔧 Improve Entry Conditions:")
    print("   - Current win rates (16-18%) are too low")
    print("   - Target: >30% win rate for profitable trading")
    print("   - Focus on high-probability setups only")

    print("\n2. 🎯 Symbol-Specific Trading:")
    print("   - GOLD: Best performer (84% accuracy)")
    print("   - USDCAD: Good performer (85% accuracy)")
    print("   - EURUSD/GBPUSD: Moderate performers (77-78%)")
    print("   - AVOID: USDJPY (44% accuracy)")

    print("\n3. ⏰ Time-Based Optimization:")
    print("   - Focus on specific hours with better performance")
    print("   - Avoid low-volume periods")
    print("   - Consider market session overlaps")

    print("\n4. 🛡️ Risk Management:")
    print("   - Reduce position sizes until win rate improves")
    print("   - Implement stricter entry filters")
    print("   - Consider paper trading first")

# เรียกใช้การวิเคราะห์เพิ่มเติมเมื่อไม่มี arguments
if __name__ == "__main__" and len(sys.argv) == 1:
    # ถ้าไม่มี arguments แสดงว่าเรียกใช้แบบปกติ
    print(f"\n🏗️ เปิดใช้งาน __name__ and len(sys.argv)") if Steps_to_do else None

    print("🚀 เริ่มต้นการทำงาน LightGBM Multi-class Trading Analysis")
    print("=" * 80)
    print(f"🎯 จำนวนรอบการเทรนที่กำหนด: {NUM_TRAINING_ROUNDS}")
    print("=" * 80)

    try:
        # รันการวิเคราะห์หลัก
        print("📊 เริ่มการเทรนโมเดล...")
        all_results = run_main_analysis()

        print("\n📈 เริ่มการวิเคราะห์เปรียบเทียบประสิทธิภาพ...")
        analyze_performance_comparison()

        print("\n💡 เริ่มสร้างคำแนะนำการเทรด...")
        create_trading_recommendations()

        # รันการวิเคราะห์ครบถ้วนเพื่อปรับปรุงโมเดล
        if all_results and len(all_results) > 0:
            print(f"\n🔬 เริ่มการวิเคราะห์ครบถ้วนเพื่อปรับปรุงโมเดล")
            print(f"📋 พบผลลัพธ์: {len(all_results)} รายการ")

            comprehensive_analysis = run_comprehensive_analysis(all_results)

            if 'error' not in comprehensive_analysis:
                print(f"\n🎯 การวิเคราะห์เสร็จสิ้น - ตรวจสอบไฟล์ผลลัพธ์:")
                for file_name in comprehensive_analysis.get('files_created', []):
                    print(f"   📄 {file_name}")
            else:
                print(f"⚠️ เกิดข้อผิดพลาดในการวิเคราะห์: {comprehensive_analysis['error']}")
        else:
            print("⚠️ ไม่พบผลลัพธ์จากการเทรนโมเดล - ข้ามการวิเคราะห์ครบถ้วน")

        print(f"\n✅ การทำงานทั้งหมดเสร็จสิ้น!")
        print("=" * 80)

    except KeyboardInterrupt:
        print(f"\n⚠️ ผู้ใช้หยุดการทำงาน (Ctrl+C)")
        print("=" * 80)
    except Exception as e:
        print(f"\n❌ เกิดข้อผิดพลาดในการทำงาน: {e}")
        traceback.print_exc()
        print("=" * 80)
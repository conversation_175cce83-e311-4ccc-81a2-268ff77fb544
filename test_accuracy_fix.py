#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข KeyError: 'accuracy' ในฟังก์ชัน train_and_evaluate()
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_accuracy_fix():
    """ทดสอบการแก้ไข accuracy KeyError"""
    print("\n🧪 ทดสอบการแก้ไข KeyError: 'accuracy'")
    print("="*60)
    
    try:
        # Import ฟังก์ชันที่ต้องการ
        from python_LightGBM_16_Signal import (
            train_and_evaluate,
            USE_MULTICLASS_TARGET
        )
        
        print(f"✅ Import สำเร็จ")
        print(f"📊 USE_MULTICLASS_TARGET: {USE_MULTICLASS_TARGET}")
        
        # สร้างข้อมูลทดสอบ
        print(f"🔧 สร้างข้อมูลทดสอบ...")
        
        np.random.seed(42)
        n_samples = 1000
        
        # สร้าง features
        features = {
            'Close': 1800 + np.cumsum(np.random.randn(n_samples) * 0.5),
            'High': np.random.uniform(1800, 1900, n_samples),
            'Low': np.random.uniform(1700, 1800, n_samples),
            'Volume': np.random.uniform(1000, 10000, n_samples),
            'EMA200': np.random.uniform(1750, 1850, n_samples),
            'RSI_14': np.random.uniform(20, 80, n_samples),
            'MACD': np.random.uniform(-5, 5, n_samples),
            'MACD_Signal': np.random.uniform(-5, 5, n_samples),
            'ATR_14': np.random.uniform(1, 10, n_samples),
            'BB_Upper': np.random.uniform(1820, 1880, n_samples),
            'BB_Lower': np.random.uniform(1720, 1780, n_samples),
            'Stoch_K': np.random.uniform(0, 100, n_samples),
            'Stoch_D': np.random.uniform(0, 100, n_samples)
        }
        
        df = pd.DataFrame(features)
        
        # สร้าง Target
        if USE_MULTICLASS_TARGET:
            df['Target_Multiclass'] = np.random.choice([0, 1, 2, 3, 4], n_samples, p=[0.2, 0.2, 0.2, 0.2, 0.2])
            target_column = 'Target_Multiclass'
        else:
            df['Target'] = np.random.choice([0, 1], n_samples, p=[0.5, 0.5])
            target_column = 'Target'
        
        print(f"✅ สร้างข้อมูลทดสอบ: {len(df)} samples")
        print(f"📊 Target column: {target_column}")
        print(f"📊 Target distribution:")
        target_counts = df[target_column].value_counts().sort_index()
        for value, count in target_counts.items():
            percentage = (count / len(df)) * 100
            print(f"   {value}: {count} ({percentage:.1f}%)")
        
        # กำหนดพารามิเตอร์
        symbol = "GOLD"
        timeframe = 60
        output_folder = "Test_LightGBM/results/accuracy_test"
        
        print(f"\n📋 พารามิเตอร์การทดสอบ:")
        print(f"   Symbol: {symbol}")
        print(f"   Timeframe: {timeframe}")
        print(f"   Target Column: {target_column}")
        print(f"   Output Folder: {output_folder}")
        print(f"   Data Shape: {df.shape}")
        
        # ลบโฟลเดอร์เก่า (ถ้ามี)
        import shutil
        if os.path.exists("Test_LightGBM"):
            print(f"🗑️ ลบโฟลเดอร์ผลลัพธ์เก่า...")
            shutil.rmtree("Test_LightGBM")
        
        # เตรียมข้อมูลสำหรับ train_and_evaluate
        from sklearn.model_selection import train_test_split
        from lightgbm import LGBMClassifier

        # แยก features และ target
        feature_columns = [col for col in df.columns if col not in [target_column]]
        X = df[feature_columns]
        y = df[target_column]

        # แบ่งข้อมูล
        X_train, X_temp, y_train, y_temp = train_test_split(X, y, test_size=0.4, random_state=42)
        X_val, X_test, y_val, y_test = train_test_split(X_temp, y_temp, test_size=0.5, random_state=42)

        # เตรียมข้อมูลในรูปแบบที่ train_and_evaluate ต้องการ
        train_data = (X_train, y_train)
        val_data = (X_val, y_val)
        test_data = (X_test, y_test)

        # สร้างโมเดล
        if USE_MULTICLASS_TARGET:
            model = LGBMClassifier(objective='multiclass', num_class=5, random_state=42, n_estimators=10)
        else:
            model = LGBMClassifier(objective='binary', random_state=42, n_estimators=10)

        model_name = f"LightGBM_{symbol}_{timeframe}"

        # เรียกใช้ฟังก์ชัน train_and_evaluate
        print(f"\n🔄 เรียกใช้ train_and_evaluate()...")
        print("="*60)

        start_time = datetime.now()

        result = train_and_evaluate(
            input_model=model,
            model_name=model_name,
            train_data=train_data,
            val_data=val_data,
            test_data=test_data,
            symbol=symbol,
            timeframe=timeframe,
            trade_df=None,
            output_folder=output_folder
        )
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("="*60)
        print(f"✅ train_and_evaluate() เสร็จสิ้น")
        print(f"⏱️ เวลาที่ใช้: {duration}")
        
        # ตรวจสอบผลลัพธ์
        if result:
            print(f"\n📈 ผลลัพธ์การเทรน:")
            print(f"   ✅ สำเร็จ!")
            
            # แสดงผลลัพธ์ที่สำคัญ
            if isinstance(result, dict):
                for key, value in result.items():
                    if key in ['accuracy', 'auc', 'f1', 'precision', 'recall']:
                        if isinstance(value, (int, float)):
                            print(f"   {key}: {value:.3f}")
                        else:
                            print(f"   {key}: {value}")
            
            print(f"\n🎉 การแก้ไข KeyError: 'accuracy' สำเร็จ!")
            return True
        else:
            print(f"\n❌ train_and_evaluate() ล้มเหลว")
            return False
        
    except KeyError as ke:
        print(f"\n❌ ยังคงมี KeyError: {ke}")
        import traceback
        traceback.print_exc()
        return False
        
    except Exception as e:
        print(f"\n❌ เกิดข้อผิดพลาดอื่น: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_evaluation_directly():
    """ทดสอบฟังก์ชัน enhanced_evaluation โดยตรง"""
    print("\n🧪 ทดสอบ enhanced_evaluation() โดยตรง")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import enhanced_evaluation, USE_MULTICLASS_TARGET
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import train_test_split
        
        # สร้างข้อมูลทดสอบ
        np.random.seed(42)
        n_samples = 500
        n_features = 10
        
        X = np.random.randn(n_samples, n_features)
        
        if USE_MULTICLASS_TARGET:
            y = np.random.choice([0, 1, 2, 3, 4], n_samples)
            print(f"📊 Multi-class target: {len(np.unique(y))} classes")
        else:
            y = np.random.choice([0, 1], n_samples)
            print(f"📊 Binary target: {len(np.unique(y))} classes")
        
        # แบ่งข้อมูล
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
        
        # เทรนโมเดล
        model = RandomForestClassifier(n_estimators=10, random_state=42)
        model.fit(X_train, y_train)
        
        # ทดสอบ enhanced_evaluation
        output_folder = "Test_LightGBM/results/enhanced_test"
        os.makedirs(output_folder, exist_ok=True)
        
        print(f"🔄 เรียกใช้ enhanced_evaluation()...")
        
        result = enhanced_evaluation(
            model=model,
            X_test=X_test,
            y_test=y_test,
            output_folder=output_folder,
            symbol="TEST",
            timeframe=60
        )
        
        if result:
            print(f"✅ enhanced_evaluation() สำเร็จ")
            print(f"📊 Keys ใน result: {list(result.keys())}")
            
            # ตรวจสอบ structure ของ result
            if 'overall_metrics' in result:
                print(f"📊 Multi-class result structure:")
                print(f"   overall_metrics keys: {list(result['overall_metrics'].keys())}")
                if 'accuracy' in result['overall_metrics']:
                    print(f"   ✅ accuracy พบใน overall_metrics: {result['overall_metrics']['accuracy']:.3f}")
                else:
                    print(f"   ❌ accuracy ไม่พบใน overall_metrics")
            else:
                print(f"📊 Binary result structure:")
                if 'accuracy' in result:
                    print(f"   ✅ accuracy พบใน result: {result['accuracy']:.3f}")
                else:
                    print(f"   ❌ accuracy ไม่พบใน result")
            
            return True
        else:
            print(f"❌ enhanced_evaluation() ล้มเหลว")
            return False
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 ทดสอบการแก้ไข KeyError: 'accuracy'")
    print("="*60)
    
    # ทดสอบ enhanced_evaluation ก่อน
    test1_result = test_enhanced_evaluation_directly()
    
    print("\n" + "="*60)
    
    # ทดสอบ train_and_evaluate
    test2_result = test_accuracy_fix()
    
    print("\n" + "="*60)
    print("📊 สรุปผลการทดสอบ:")
    print(f"   enhanced_evaluation(): {'✅ สำเร็จ' if test1_result else '❌ ล้มเหลว'}")
    print(f"   train_and_evaluate(): {'✅ สำเร็จ' if test2_result else '❌ ล้มเหลว'}")
    
    if test1_result and test2_result:
        print(f"\n🎉 การแก้ไขสำเร็จทั้งหมด!")
    else:
        print(f"\n💥 ยังมีปัญหาที่ต้องแก้ไข!")
    
    print("="*60)

# 📊 MT5 Status Manager - คู่มือการใช้งาน

## 🎯 ภาพรวม

MT5 Status Manager เป็น Expert Advisor ที่ทำหน้าที่เป็นผู้จัดการรายงานสถานะของระบบเทรด โดยส่งรายงานไปยัง Telegram อัตโนมัติ

## ✨ ฟีเจอร์หลัก

### 📋 การรายงานสถานะ:

1. **🤖 สถานะ Algo Trading** - ตรวจสอบว่าเปิดหรือปิด
2. **📊 จำนวนไม้ทั้งหมด** - นับออเดอร์ที่เปิดอยู่
3. **💰 กำไร/ขาดทุนรวม** - คำนวณจาก Profit + Swap
4. **⚠️ ขาดทุนสูงสุด** - คำนวณตาม SL ของไม้ที่เปิดอยู่
5. **📋 รายละเอียดออเดอร์** - แสดงข้อมูลแต่ละไม้

### 🔔 การแจ้งเตือน:

1. **📊 แท่งใหม่** - เมื่อเกิดแท่งใหม่ในชาร์ต
2. **🔄 การเปลี่ยนแปลงออเดอร์** - เมื่อมีการเปิด/ปิดไม้
3. **⏰ รายงานตามช่วงเวลา** - รายงานทุก 5 นาที (ปรับได้)
4. **🚀 เริ่มต้น/หยุดทำงาน** - เมื่อ EA เริ่มหรือหยุดทำงาน

## ⚙️ การตั้งค่า

### Input Parameters:

```mql5
input string TelegramBotToken  = "**********************************************";
input string ChatID            = "6546140292";
input int    ReportInterval    = 300;  // รายงานทุก 5 นาที
input bool   EnableNewBarReport = true;  // รายงานแท่งใหม่
input bool   EnableStatusReport = true;  // รายงานสถานะทั่วไป
input bool   EnablePositionChange = true; // รายงานการเปลี่ยนแปลงออเดอร์
```

### การปรับแต่ง:

- **ReportInterval**: ช่วงเวลารายงาน (วินาที)
  - 300 = 5 นาที
  - 600 = 10 นาที
  - 1800 = 30 นาที

- **Enable Flags**: เปิด/ปิดการรายงานแต่ละประเภท

## 📱 ตัวอย่างข้อความ Telegram

### 🚀 รายงานเริ่มต้น:
```
🚀 MT5 Status Manager เริ่มทำงาน

🤖 Algo Trading: ✅ เปิด
📊 จำนวนไม้: 3 ไม้
💰 กำไร: +125.50 USD
⚠️ ขาดทุนสูงสุด: -450.00 USD

📋 รายละเอียดออเดอร์:
EURUSD BUY 0.10 (P/L: +45.20)
GBPUSD SELL 0.05 (P/L: +30.15)
USDJPY BUY 0.15 (P/L: +50.15)
```

### 📊 รายงานแท่งใหม่:
```
📊 แท่งใหม่: EURUSD M30
⏰ เวลา: 2024.12.20 14:30

🤖 Algo Trading: ✅ เปิด
📊 จำนวนไม้: 3 ไม้
💰 กำไร: +135.75 USD
```

### 🔄 รายงานการเปลี่ยนแปลงออเดอร์:
```
🔄 การเปลี่ยนแปลงออเดอร์
จำนวนเดิม: 3 ไม้
จำนวนใหม่: 4 ไม้
✅ เปิดออเดอร์ใหม่: 1 ไม้

🤖 Algo Trading: ✅ เปิด
📊 จำนวนไม้: 4 ไม้
💰 กำไร: +145.25 USD
```

### 🔴 รายงานหยุดทำงาน:
```
🔴 MT5 Status Manager หยุดทำงาน
เหตุผล: ถูกลบออกจากชาร์ต
```

## 🔧 การติดตั้งและใช้งาน

### 1. การติดตั้ง:

```bash
# 1. คัดลอกไฟล์ไปยัง MT5
# วางไฟล์ MT5_Status_Manager.mq5 ใน:
# MT5_Data_Folder/MQL5/Experts/

# 2. คอมไพล์ไฟล์
# เปิด MetaEditor → เปิดไฟล์ → กด F7 (Compile)
```

### 2. การใช้งาน:

```bash
# 1. เปิด MT5
# 2. ลาก EA ไปยังชาร์ต
# 3. ตั้งค่า Input Parameters
# 4. เปิด Auto Trading
# 5. กด OK
```

### 3. การตรวจสอบ:

```bash
# ตรวจสอบ Log ใน MT5:
# - Expert tab
# - Journal tab

# ตรวจสอบ Telegram:
# - ควรได้รับข้อความเริ่มต้น
# - ตรวจสอบการรายงานตามช่วงเวลา
```

## 📊 การคำนวณต่างๆ

### 💰 กำไร/ขาดทุนรวม:
```mql5
totalProfit = PositionProfit + PositionSwap
```

### ⚠️ ขาดทุนสูงสุดตาม SL:
```mql5
// สำหรับ BUY
loss = (OpenPrice - SL) / Point * TickValue * Volume

// สำหรับ SELL  
loss = (SL - OpenPrice) / Point * TickValue * Volume
```

### 📊 การนับออเดอร์:
```mql5
totalPositions = PositionsTotal()
```

## ⚠️ ข้อควรระวัง

1. **Internet Connection**: ต้องมีอินเทอร์เน็ตเพื่อส่ง Telegram
2. **WebRequest Permission**: ต้องเปิดใช้งาน WebRequest ใน MT5
3. **Telegram Bot**: ต้องสร้าง Bot และได้ Token ที่ถูกต้อง
4. **Chat ID**: ต้องได้ Chat ID ที่ถูกต้อง
5. **Resource Usage**: การส่งข้อความบ่อยอาจใช้ทรัพยากรมาก

## 🔧 การแก้ไขปัญหา

### ❌ ส่งข้อความไม่ได้:

1. **ตรวจสอบ Token และ Chat ID**
2. **ตรวจสอบการเชื่อมต่ออินเทอร์เน็ต**
3. **ตรวจสอบ WebRequest Settings**:
   ```
   Tools → Options → Expert Advisors
   ✅ Allow WebRequest for listed URL
   เพิ่ม: https://api.telegram.org
   ```

### ❌ ไม่มีรายงาน:

1. **ตรวจสอบ Enable Flags**
2. **ตรวจสอบ ReportInterval**
3. **ตรวจสอบ Auto Trading**
4. **ดู Log ใน Expert tab**

### ❌ ข้อมูลไม่ถูกต้อง:

1. **ตรวจสอบสิทธิ์การเข้าถึงข้อมูล**
2. **ตรวจสอบการตั้งค่า Symbol**
3. **รีสตาร์ท EA**

## 🎯 การปรับแต่งเพิ่มเติม

### เพิ่มการรายงานอื่นๆ:

```mql5
// เพิ่มใน GetCurrentStatusReport()
report += "📈 Equity: " + DoubleToString(AccountInfoDouble(ACCOUNT_EQUITY), 2) + "\n";
report += "💳 Balance: " + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2) + "\n";
report += "📊 Margin: " + DoubleToString(AccountInfoDouble(ACCOUNT_MARGIN), 2) + "\n";
```

### เปลี่ยนรูปแบบข้อความ:

```mql5
// ปรับแต่งใน SendTelegram()
// เพิ่ม HTML formatting
string data = "chat_id=" + ChatID + "&text=" + message + "&parse_mode=HTML";
```

## 📋 สรุป

MT5 Status Manager ช่วยให้คุณติดตามสถานะการเทรดได้แบบ Real-time ผ่าน Telegram โดยมีฟีเจอร์ครบครันสำหรับการจัดการและรายงานสถานะต่างๆ ของระบบเทรด

### ✅ ประโยชน์:
- **ติดตามแบบ Real-time**
- **รายงานครบถ้วน**
- **แจ้งเตือนทันที**
- **ปรับแต่งได้ตามต้องการ**
- **ใช้งานง่าย**


[2025-07-15 20:44:12.806289] ERROR in GOLD M30:
Exception: Cannot set a DataFrame with multiple columns to the single column Volume_MA20
Traceback:
Traceback (most recent call last):
  File "D:\test_gold\python_to_mt5_WebRequest_server_13_Signal.py", line 634, in process_data_and_trade
    df_ft['Volume_MA20'] = df_ft['Volume'].rolling(20, min_periods=1).mean()
    ~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4301, in __setitem__
    self._set_item_frame_value(key, value)
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4459, in _set_item_frame_value
    raise ValueError(
ValueError: Cannot set a DataFrame with multiple columns to the single column Volume_MA20

==================================================

[2025-07-15 20:47:18.573826] ERROR in GOLD M30:
Exception: Cannot set a DataFrame with multiple columns to the single column Volume_Lag_1
Traceback:
Traceback (most recent call last):
  File "D:\test_gold\python_to_mt5_WebRequest_server_13_Signal.py", line 966, in process_data_and_trade
    lag_features[f'{col}_Lag_{lag}'] = df_ft[col].shift(lag)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4301, in __setitem__
    self._set_item_frame_value(key, value)
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4459, in _set_item_frame_value
    raise ValueError(
ValueError: Cannot set a DataFrame with multiple columns to the single column Volume_Lag_1

==================================================

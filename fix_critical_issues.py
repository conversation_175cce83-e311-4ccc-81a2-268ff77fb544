#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
แก้ไขปัญหาร้ายแรงในโมเดล
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

def fix_nzdusd_m30_issue():
    """แก้ไขปัญหา NZDUSD M30 CV_AUC = 0.5"""
    print("🔥 แก้ไขปัญหาร้ายแรง: NZDUSD M30")
    print("=" * 60)
    
    # ตรวจสอบไฟล์ข้อมูล
    filename = "NZDUSD#_M30_201905010000_202504302330.csv"
    
    try:
        print(f"📊 ตรวจสอบไฟล์: {filename}")
        
        # โหลดข้อมูล
        df = pd.read_csv(filename)
        print(f"  ข้อมูล: {len(df)} rows, {len(df.columns)} columns")
        print(f"  คอลัมน์: {list(df.columns)}")
        
        # ตรวจสอบ target column
        target_cols = [col for col in df.columns if any(keyword in col.lower() 
                      for keyword in ['target', 'signal', 'label', 'y'])]
        
        if target_cols:
            target_col = target_cols[0]
            print(f"  🎯 Target column: {target_col}")
            
            # วิเคราะห์ target distribution
            target_values = df[target_col].dropna()
            print(f"  📊 Target distribution:")
            print(f"    Total: {len(target_values)}")
            print(f"    Unique values: {target_values.unique()}")
            print(f"    Value counts: {target_values.value_counts().to_dict()}")
            
            # ตรวจสอบ class imbalance
            if len(target_values.unique()) == 2:
                class_counts = target_values.value_counts()
                ratio = class_counts.max() / class_counts.min()
                print(f"    Imbalance ratio: {ratio:.1f}:1")
                
                if ratio > 10:
                    print(f"  ⚠️ Severe class imbalance detected!")
                    return "severe_imbalance"
            
            # ตรวจสอบ data leakage
            print(f"\n🔍 ตรวจสอบ Data Leakage:")
            
            # ตรวจสอบ future information
            if 'datetime' in df.columns or 'timestamp' in df.columns:
                time_col = 'datetime' if 'datetime' in df.columns else 'timestamp'
                df[time_col] = pd.to_datetime(df[time_col])
                
                # ตรวจสอบการเรียงลำดับเวลา
                is_sorted = df[time_col].is_monotonic_increasing
                print(f"    Time series sorted: {is_sorted}")
                
                if not is_sorted:
                    print(f"  ⚠️ Time series not properly sorted!")
                    return "time_series_issue"
            
            # ตรวจสอบ feature correlation กับ target
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            feature_cols = [col for col in numeric_cols if col != target_col]
            
            if len(feature_cols) > 0:
                correlations = df[feature_cols + [target_col]].corr()[target_col].abs()
                high_corr_features = correlations[correlations > 0.9].drop(target_col, errors='ignore')
                
                if len(high_corr_features) > 0:
                    print(f"  ⚠️ Suspiciously high correlations found:")
                    for feat, corr in high_corr_features.items():
                        print(f"    {feat}: {corr:.3f}")
                    return "high_correlation"
            
            return "data_ok"
            
        else:
            print(f"  ❌ ไม่พบ target column")
            return "no_target"
            
    except FileNotFoundError:
        print(f"  ❌ ไม่พบไฟล์ {filename}")
        return "file_not_found"
    except Exception as e:
        print(f"  ❌ เกิดข้อผิดพลาด: {str(e)}")
        return "error"

def improve_f1_scores():
    """ปรับปรุง F1 Scores สำหรับโมเดลที่มีปัญหา"""
    print("\n📈 ปรับปรุง F1 Scores")
    print("=" * 60)
    
    # โมเดลที่มี F1 Score ต่ำ
    low_f1_models = [
        ('USDJPY M30', 0.409931),
        ('EURGBP H1', 0.445178),
        ('EURUSD M30', 0.510248),
        ('USDJPY H1', 0.532381)
    ]
    
    print("🎯 โมเดลที่ต้องปรับปรุง F1 Score:")
    for model, f1 in low_f1_models:
        print(f"  {model}: F1 = {f1:.3f}")
    
    # แนะนำการแก้ไข
    solutions = [
        {
            "issue": "Class Imbalance",
            "solution": "ปรับ class_weight เป็น custom ratio",
            "code": """
# ใน get_lgbm_params()
if ratio > 5:
    params['class_weight'] = {0: 1, 1: 5}  # เพิ่มน้ำหนัก minority class
elif ratio > 3:
    params['class_weight'] = {0: 1, 1: 3}
"""
        },
        {
            "issue": "Threshold ไม่เหมาะสม",
            "solution": "ใช้ find_optimal_threshold() หา threshold ที่ดีที่สุด",
            "code": """
# หลังจากเทรนโมเดล
optimal_threshold, best_f1 = find_optimal_threshold(y_true, y_proba, 'f1')
print(f"Optimal threshold: {optimal_threshold}, F1: {best_f1}")
"""
        },
        {
            "issue": "Feature Quality",
            "solution": "เพิ่ม market regime features",
            "code": """
# ก่อนเทรน
df_enhanced = create_market_regime_features(df)
# จะได้ features เพิ่ม: ADX, ATR, Price_Position, Trend_Consensus
"""
        }
    ]
    
    print(f"\n💡 วิธีแก้ไข:")
    for i, sol in enumerate(solutions, 1):
        print(f"{i}. {sol['issue']}")
        print(f"   วิธีแก้: {sol['solution']}")
        print(f"   Code: {sol['code']}")
        print()

def create_quick_fix_script():
    """สร้างสคริปต์แก้ไขด่วน"""
    print("🔧 สร้างสคริปต์แก้ไขด่วน")
    print("=" * 60)
    
    quick_fixes = """
# Quick Fix Script สำหรับปัญหาที่พบ

# 1. แก้ไข NZDUSD M30 (ถ้าเป็นปัญหา class imbalance)
def fix_nzdusd_class_weight():
    # ใน get_lgbm_params() เพิ่ม special case สำหรับ NZDUSD
    if symbol == 'NZDUSD' and timeframe == 30:
        params['class_weight'] = {0: 1, 1: 10}  # เพิ่มน้ำหนักมาก
        params['scale_pos_weight'] = 10
    return params

# 2. ปรับปรุง F1 Score ทั่วไป
def improve_f1_globally():
    # ปรับ param_dist ให้เน้น F1 Score
    param_dist_f1_focused = {
        'learning_rate': [0.01, 0.03, 0.05],  # ลดลงเพิ่มเติม
        'num_leaves': [4, 6, 8],              # ลดลงเพิ่มเติม
        'max_depth': [2, 3, 4],               # ลดลงเพิ่มเติม
        'min_data_in_leaf': [30, 40, 50],     # เพิ่มขึ้นเพิ่มเติม
        'class_weight': ['balanced', {0:1, 1:3}, {0:1, 1:5}]  # เพิ่ม class weight options
    }
    return param_dist_f1_focused

# 3. เพิ่ม early stopping aggressive
def add_aggressive_early_stopping():
    early_stopping_params = {
        'early_stopping_rounds': 50,  # ลดจาก 100
        'eval_metric': 'f1',          # ใช้ F1 แทน AUC
        'greater_is_better': True
    }
    return early_stopping_params
"""
    
    print("📝 Quick Fix Code:")
    print(quick_fixes)
    
    return quick_fixes

def generate_action_plan():
    """สร้างแผนการดำเนินการ"""
    print("\n🎯 แผนการดำเนินการ")
    print("=" * 60)
    
    action_plan = [
        {
            "step": 1,
            "action": "ตรวจสอบ NZDUSD M30 ข้อมูล",
            "priority": "🔥 สูงสุด",
            "time": "5 นาที",
            "command": "python fix_critical_issues.py"
        },
        {
            "step": 2,
            "action": "ปรับ class_weight สำหรับ F1 Score",
            "priority": "🔥 สูง",
            "time": "10 นาที",
            "command": "แก้ไข get_lgbm_params() ใน main file"
        },
        {
            "step": 3,
            "action": "ทดสอบ 1-2 โมเดลที่มีปัญหา",
            "priority": "🟡 กลาง",
            "time": "30 นาที",
            "command": "รัน hyperparameter tuning สำหรับ USDJPY M30"
        },
        {
            "step": 4,
            "action": "รัน full training ใหม่ (ถ้าผลดี)",
            "priority": "🟢 ต่ำ",
            "time": "3-4 ชั่วโมง",
            "command": "python python_LightGBM_15_Tuning.py"
        }
    ]
    
    print("📋 Action Plan:")
    for plan in action_plan:
        print(f"{plan['step']}. {plan['priority']} {plan['action']}")
        print(f"   ⏱️ เวลา: {plan['time']}")
        print(f"   💻 คำสั่ง: {plan['command']}")
        print()

def main():
    """ฟังก์ชันหลัก"""
    print("🚨 แก้ไขปัญหาร้ายแรงในโมเดล")
    print("=" * 80)
    
    # 1. ตรวจสอบ NZDUSD M30
    nzdusd_status = fix_nzdusd_m30_issue()
    
    # 2. วิเคราะห์ F1 Score issues
    improve_f1_scores()
    
    # 3. สร้าง quick fix script
    create_quick_fix_script()
    
    # 4. สร้างแผนการดำเนินการ
    generate_action_plan()
    
    # 5. สรุป
    print("🎯 สรุปการวินิจฉัย")
    print("=" * 80)
    
    print(f"NZDUSD M30 Status: {nzdusd_status}")
    
    if nzdusd_status == "severe_imbalance":
        print("✅ พบสาเหตุ: Class imbalance รุนแรง")
        print("🔧 วิธีแก้: เพิ่ม class_weight และ SMOTE")
    elif nzdusd_status == "high_correlation":
        print("✅ พบสาเหตุ: Data leakage จาก feature correlation สูง")
        print("🔧 วิธีแก้: ลบ features ที่มี correlation > 0.9")
    elif nzdusd_status == "time_series_issue":
        print("✅ พบสาเหตุ: Time series ไม่เรียงลำดับ")
        print("🔧 วิธีแก้: เรียงข้อมูลตามเวลาใหม่")
    else:
        print("⚠️ ต้องตรวจสอบเพิ่มเติม")
    
    print(f"\n🚀 ขั้นตอนถัดไป:")
    print("1. แก้ไข NZDUSD M30 ตามสาเหตุที่พบ")
    print("2. ปรับ class_weight สำหรับ F1 Score")
    print("3. ทดสอบกับ 1-2 โมเดลก่อน")
    print("4. รัน full training ใหม่")

if __name__ == "__main__":
    main()

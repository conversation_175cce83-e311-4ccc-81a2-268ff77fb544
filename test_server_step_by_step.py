#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import pandas as pd
import numpy as np

def test_with_different_bar_counts():
    """ทดสอบ server ด้วยจำนวนแท่งที่แตกต่างกัน"""
    
    url = 'http://127.0.0.1:54321/data'
    
    # ทดสอบด้วยจำนวนแท่งที่แตกต่างกัน
    test_counts = [1, 5, 10, 50, 100, 150, 200, 210]
    
    for count in test_counts:
        print(f"\n🔍 Testing with {count} bars...")
        
        # สร้างข้อมูลทดสอบ
        bars = []
        base_time = 1737021600
        base_price = 2650.0
        
        for i in range(count):
            price_change = np.random.normal(0, 1.0)
            open_price = base_price + price_change
            high_price = open_price + abs(np.random.normal(1, 0.5))
            low_price = open_price - abs(np.random.normal(1, 0.5))
            close_price = open_price + np.random.normal(0, 0.5)
            
            # ปรับให้ high/low สมเหตุสมผล
            high_price = max(high_price, open_price, close_price)
            low_price = min(low_price, open_price, close_price)
            
            bar = {
                "time": base_time + (i * 1800),  # 30 minutes apart
                "open": round(open_price, 2),
                "high": round(high_price, 2),
                "low": round(low_price, 2),
                "close": round(close_price, 2),
                "volume": 1000 + i,
                "tick_volume": 1000 + i,
                "spread": 5,
                "real_volume": 1000 + i
            }
            bars.append(bar)
            base_price = close_price
        
        test_data = {
            "symbol": "GOLD",
            "timeframe_str": "PERIOD_M30",
            "bars": bars
        }
        
        try:
            response = requests.post(url, json=test_data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                signal = result.get('signal', 'N/A')
                confidence = result.get('confidence', 0.0)
                
                if signal == 'ERROR':
                    print(f"  ❌ {count} bars: ERROR signal")
                    print(f"     This is where the problem starts!")
                    break
                else:
                    print(f"  ✅ {count} bars: {signal} (confidence: {confidence:.4f})")
            else:
                print(f"  ❌ {count} bars: HTTP {response.status_code}")
                break
                
        except Exception as e:
            print(f"  ❌ {count} bars: Exception - {e}")
            break
    
    return count

def test_csv_data_gradually():
    """ทดสอบข้อมูล CSV แบบค่อยๆ เพิ่ม"""
    
    print("\n🔍 Loading CSV data...")
    
    try:
        df = pd.read_csv("CSV_Files_Fixed/GOLD_M30_FIXED.csv", sep=',')
        df = df[df['Date'] != '<DATE>'].copy()
        
        # เลือกข้อมูลล่าสุด 300 แท่ง
        df_recent = df.tail(300).copy()
        
        # รวม Date และ Time เป็น datetime
        df_recent['DateTime'] = pd.to_datetime(df_recent['Date'] + ' ' + df_recent['Time'])
        df_recent['timestamp'] = df_recent['DateTime'].astype('int64') // 10**9
        
        # แปลงข้อมูลเป็น numeric
        numeric_cols = ['Open', 'High', 'Low', 'Close', 'TickVol']
        for col in numeric_cols:
            df_recent[col] = pd.to_numeric(df_recent[col], errors='coerce')
        
        print(f"✅ Loaded {len(df_recent)} bars from CSV")
        
        # ทดสอบแบบค่อยๆ เพิ่ม
        test_counts = [50, 100, 150, 200, 210, 250, 300]
        
        for count in test_counts:
            if count > len(df_recent):
                continue
                
            print(f"\n🔍 Testing CSV data with {count} bars...")
            
            # เลือกข้อมูล
            df_test = df_recent.tail(count).copy()
            
            # สร้าง bars list
            bars = []
            for _, row in df_test.iterrows():
                bar = {
                    "time": int(row['timestamp']),
                    "open": float(row['Open']),
                    "high": float(row['High']),
                    "low": float(row['Low']),
                    "close": float(row['Close']),
                    "volume": int(row['TickVol']),
                    "tick_volume": int(row['TickVol']),
                    "spread": 5,
                    "real_volume": int(row['TickVol'])
                }
                bars.append(bar)
            
            test_data = {
                "symbol": "GOLD",
                "timeframe_str": "PERIOD_M30",
                "bars": bars
            }
            
            try:
                response = requests.post('http://127.0.0.1:54321/data', json=test_data, timeout=90)
                
                if response.status_code == 200:
                    result = response.json()
                    signal = result.get('signal', 'N/A')
                    confidence = result.get('confidence', 0.0)
                    
                    if signal == 'ERROR':
                        print(f"  ❌ {count} bars: ERROR signal")
                        print(f"     CSV data problem starts here!")
                        return count
                    else:
                        print(f"  ✅ {count} bars: {signal} (confidence: {confidence:.4f})")
                else:
                    print(f"  ❌ {count} bars: HTTP {response.status_code}")
                    return count
                    
            except Exception as e:
                print(f"  ❌ {count} bars: Exception - {e}")
                return count
        
        return 300
        
    except Exception as e:
        print(f"❌ Error loading CSV: {e}")
        return 0

if __name__ == "__main__":
    print("🚀 Testing server step by step...")
    
    print("\n=== Test 1: Random data with different bar counts ===")
    problem_count_random = test_with_different_bar_counts()
    
    print("\n=== Test 2: CSV data with different bar counts ===")
    problem_count_csv = test_csv_data_gradually()
    
    print(f"\n📊 Summary:")
    print(f"  Random data: Problem starts at {problem_count_random} bars")
    print(f"  CSV data: Problem starts at {problem_count_csv} bars")
    
    if problem_count_random < 210 or problem_count_csv < 210:
        print(f"\n🔍 The server has issues with data >= {min(problem_count_random, problem_count_csv)} bars")
        print(f"   This suggests a problem in indicator calculation or feature processing")
    else:
        print(f"\n✅ Server works fine with up to 300 bars")

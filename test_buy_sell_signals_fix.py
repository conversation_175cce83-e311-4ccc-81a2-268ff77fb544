#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขปัญหา Buy/Sell signals ใน multiclass classification
"""

import os
import sys
import pandas as pd
import numpy as np

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_multiclass_signal_logic():
    """ทดสอบ logic การตัดสินใจ Buy/Sell ใน multiclass"""
    
    print("🧪 ทดสอบ Multiclass Signal Logic")
    print("="*60)
    
    try:
        from python_LightGBM_16_Signal import (
            USE_MULTICLASS_TARGET, 
            CLASS_MAPPING, 
            PROFIT_THRESHOLDS,
            predict_with_scenario_model
        )
        
        print(f"📊 USE_MULTICLASS_TARGET: {USE_MULTICLASS_TARGET}")
        print(f"📊 CLASS_MAPPING: {CLASS_MAPPING}")
        print(f"📊 PROFIT_THRESHOLDS: {PROFIT_THRESHOLDS}")
        
        # สร้าง mock probabilities สำหรับทดสอบ
        test_cases = [
            {
                'name': 'Strong Buy Signal',
                'probabilities': [0.05, 0.05, 0.10, 0.20, 0.60],  # Class 4 สูงสุด
                'expected_buy': True,
                'expected_sell': False
            },
            {
                'name': 'Weak Buy Signal', 
                'probabilities': [0.05, 0.05, 0.20, 0.60, 0.10],  # Class 3 สูงสุด
                'expected_buy': True,
                'expected_sell': False
            },
            {
                'name': 'Strong Sell Signal',
                'probabilities': [0.60, 0.20, 0.10, 0.05, 0.05],  # Class 0 สูงสุด
                'expected_buy': False,
                'expected_sell': True
            },
            {
                'name': 'Weak Sell Signal',
                'probabilities': [0.20, 0.60, 0.10, 0.05, 0.05],  # Class 1 สูงสุด
                'expected_buy': False,
                'expected_sell': True
            },
            {
                'name': 'No Trade Signal',
                'probabilities': [0.05, 0.05, 0.80, 0.05, 0.05],  # Class 2 สูงสุด
                'expected_buy': False,
                'expected_sell': False
            }
        ]
        
        for test_case in test_cases:
            print(f"\n🎯 ทดสอบ: {test_case['name']}")
            probs = test_case['probabilities']
            
            # คำนวณ buy และ sell probabilities
            buy_prob = probs[3] + probs[4]  # weak_buy + strong_buy
            sell_prob = probs[0] + probs[1]  # strong_sell + weak_sell
            no_trade_prob = probs[2]  # no_trade
            
            print(f"   📊 Probabilities: {probs}")
            print(f"   📈 Buy prob (3+4): {buy_prob:.3f}")
            print(f"   📉 Sell prob (0+1): {sell_prob:.3f}")
            print(f"   ⏸️ No trade prob (2): {no_trade_prob:.3f}")
            
            # ทดสอบกับ threshold ต่างๆ
            thresholds = [0.3, 0.5, 0.7]
            
            for threshold in thresholds:
                buy_signal = buy_prob > threshold
                sell_signal = sell_prob > threshold
                
                print(f"   🎯 Threshold {threshold}: Buy={buy_signal}, Sell={sell_signal}")
                
                # ตรวจสอบว่าผลลัพธ์ตรงกับที่คาดหวังหรือไม่
                if threshold == 0.5:  # ใช้ threshold มาตรฐาน
                    buy_correct = buy_signal == test_case['expected_buy']
                    sell_correct = sell_signal == test_case['expected_sell']
                    
                    if buy_correct and sell_correct:
                        print(f"      ✅ ผลลัพธ์ถูกต้อง")
                    else:
                        print(f"      ❌ ผลลัพธ์ไม่ถูกต้อง - Expected: Buy={test_case['expected_buy']}, Sell={test_case['expected_sell']}")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def test_signal_generation_in_backtest():
    """ทดสอบการสร้าง signals ในระบบ backtest"""
    
    print(f"\n🧪 ทดสอบการสร้าง Signals ในระบบ Backtest")
    print("="*60)
    
    try:
        # ตรวจสอบไฟล์ trade_log ที่มีอยู่
        from python_LightGBM_16_Signal import test_folder
        
        # หาไฟล์ trade_log ทั้งหมด
        trade_log_files = []
        if os.path.exists(test_folder):
            for file in os.listdir(test_folder):
                if file.endswith('_trade_log_model_enhanced.txt'):
                    trade_log_files.append(os.path.join(test_folder, file))
        
        print(f"📁 พบไฟล์ trade_log: {len(trade_log_files)} ไฟล์")
        
        for log_file in trade_log_files[:3]:  # ตรวจสอบแค่ 3 ไฟล์แรก
            print(f"\n📄 วิเคราะห์ไฟล์: {os.path.basename(log_file)}")
            
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # นับจำนวน Buy และ Sell signals
                buy_count = content.count('BUY')
                sell_count = content.count('SELL')
                total_lines = len(content.split('\n'))
                
                print(f"   📊 Total lines: {total_lines}")
                print(f"   📈 BUY signals: {buy_count}")
                print(f"   📉 SELL signals: {sell_count}")
                print(f"   📊 Buy/Sell ratio: {buy_count/(sell_count+1):.2f}")
                
                if sell_count == 0:
                    print(f"   ⚠️ ไม่มี SELL signals เลย!")
                elif buy_count == 0:
                    print(f"   ⚠️ ไม่มี BUY signals เลย!")
                else:
                    print(f"   ✅ มีทั้ง BUY และ SELL signals")
                
                # แสดงตัวอย่าง signals
                lines = content.split('\n')
                signal_lines = [line for line in lines if 'BUY' in line or 'SELL' in line]
                
                if signal_lines:
                    print(f"   📄 ตัวอย่าง signals (5 อันแรก):")
                    for i, line in enumerate(signal_lines[:5]):
                        print(f"      {i+1}: {line.strip()}")
                
            except Exception as e:
                print(f"   ❌ ไม่สามารถอ่านไฟล์: {e}")
        
        if not trade_log_files:
            print("⚠️ ไม่พบไฟล์ trade_log ให้วิเคราะห์")
            print("💡 ลองรัน python_LightGBM_16_Signal.py ก่อนเพื่อสร้างไฟล์ trade_log")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def create_test_trade_log():
    """สร้างไฟล์ trade_log ทดสอบเพื่อดูรูปแบบ"""
    
    print(f"\n🏗️ สร้างไฟล์ trade_log ทดสอบ")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import test_folder
        
        # สร้างข้อมูลทดสอบ
        test_signals = [
            "2023-01-01 10:00:00 - BUY signal detected (prob=0.75)",
            "2023-01-01 11:00:00 - SELL signal detected (prob=0.68)",
            "2023-01-01 12:00:00 - BUY signal detected (prob=0.82)",
            "2023-01-01 13:00:00 - No signal (prob=0.45)",
            "2023-01-01 14:00:00 - SELL signal detected (prob=0.71)",
        ]
        
        test_file = os.path.join(test_folder, "test_trade_log_model_enhanced.txt")
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("Test Trade Log - Multiclass Signals\n")
            f.write("="*50 + "\n\n")
            
            for signal in test_signals:
                f.write(signal + "\n")
        
        print(f"✅ สร้างไฟล์ทดสอบ: {test_file}")
        
        # ทดสอบการอ่านไฟล์
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        buy_count = content.count('BUY')
        sell_count = content.count('SELL')
        
        print(f"📊 ไฟล์ทดสอบ: BUY={buy_count}, SELL={sell_count}")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🏗️ Test Buy/Sell Signals Fix")
    print("="*50)
    
    # ทดสอบ logic การตัดสินใจ multiclass
    test_multiclass_signal_logic()
    
    # ทดสอบการสร้าง signals ในระบบ backtest
    test_signal_generation_in_backtest()
    
    # สร้างไฟล์ทดสอบ
    create_test_trade_log()
    
    print(f"\n✅ การทดสอบเสร็จสิ้น")
    print(f"\n📋 สรุปการแก้ไข:")
    print(f"   ✅ แก้ไข Buy signals: ใช้ prob[3] + prob[4] (weak_buy + strong_buy)")
    print(f"   ✅ แก้ไข Sell signals: ใช้ prob[0] + prob[1] (strong_sell + weak_sell)")
    print(f"   ✅ เพิ่ม debug messages สำหรับ multiclass probabilities")
    print(f"   ✅ รองรับทั้ง binary และ multiclass classification")
    
    print(f"\n💡 แนะนำการทดสอบต่อไป:")
    print(f"   1. รัน python_LightGBM_16_Signal.py เพื่อสร้าง trade_log ใหม่")
    print(f"   2. ตรวจสอบไฟล์ trade_log ว่ามีทั้ง BUY และ SELL signals")
    print(f"   3. วิเคราะห์ความสมดุลของ signals")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Feature Engineering สำหรับโมเดล LightGBM Trading
"""

import pandas as pd
import numpy as np
import talib
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def create_market_regime_features(df):
    """สร้าง Market Regime Detection Features"""
    print("🔍 สร้าง Market Regime Features...")
    
    # 1. Trend Strength (ADX-based)
    df['ADX_14'] = talib.ADX(df['High'], df['Low'], df['Close'], timeperiod=14)
    df['Trend_Strength'] = pd.cut(df['ADX_14'], 
                                 bins=[0, 25, 50, 100], 
                                 labels=['Weak', 'Moderate', 'Strong']).astype(str)
    
    # 2. Market Volatility Regime
    df['ATR_14'] = talib.ATR(df['High'], df['Low'], df['Close'], timeperiod=14)
    df['ATR_MA_50'] = df['ATR_14'].rolling(50).mean()
    df['Volatility_Regime'] = np.where(df['ATR_14'] > df['ATR_MA_50'] * 1.5, 'High_Vol',
                              np.where(df['ATR_14'] < df['ATR_MA_50'] * 0.7, 'Low_Vol', 'Normal_Vol'))
    
    # 3. Price Position in Range
    df['High_20'] = df['High'].rolling(20).max()
    df['Low_20'] = df['Low'].rolling(20).min()
    df['Price_Position'] = (df['Close'] - df['Low_20']) / (df['High_20'] - df['Low_20'])
    
    # 4. Trend Direction (Multiple timeframes)
    for period in [10, 20, 50]:
        df[f'SMA_{period}'] = df['Close'].rolling(period).mean()
        df[f'Trend_Direction_{period}'] = np.where(df['Close'] > df[f'SMA_{period}'], 1, 
                                          np.where(df['Close'] < df[f'SMA_{period}'], -1, 0))
    
    # 5. Market Structure (Higher Highs, Lower Lows)
    df['HH_5'] = df['High'].rolling(5).max() == df['High']
    df['LL_5'] = df['Low'].rolling(5).min() == df['Low']
    df['Market_Structure'] = np.where(df['HH_5'], 'Bullish',
                            np.where(df['LL_5'], 'Bearish', 'Neutral'))
    
    return df

def create_multi_timeframe_features(df, timeframe_current='M30'):
    """สร้าง Multi-timeframe Features"""
    print("📊 สร้าง Multi-timeframe Features...")
    
    # สำหรับ M30, เราจะสร้าง H1 และ H4 features
    if timeframe_current == 'M30':
        # H1 features (2 periods aggregation)
        h1_data = df.resample('60T', on='Datetime').agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum'
        }).dropna()
        
        # H4 features (8 periods aggregation)
        h4_data = df.resample('240T', on='Datetime').agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum'
        }).dropna()
        
    elif timeframe_current == 'H1':
        # H4 features (4 periods aggregation)
        h4_data = df.resample('240T', on='Datetime').agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum'
        }).dropna()
        
        # D1 features (24 periods aggregation)
        d1_data = df.resample('1D', on='Datetime').agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum'
        }).dropna()
    
    # สร้าง higher timeframe indicators
    if timeframe_current == 'M30':
        # H1 RSI
        h1_data['RSI_14'] = talib.RSI(h1_data['Close'], timeperiod=14)
        h1_data['MACD'], h1_data['MACD_Signal'], _ = talib.MACD(h1_data['Close'])
        
        # H4 Trend
        h4_data['SMA_20'] = h4_data['Close'].rolling(20).mean()
        h4_data['H4_Trend'] = np.where(h4_data['Close'] > h4_data['SMA_20'], 1, -1)
        
        # Merge back to original timeframe
        df = df.merge(h1_data[['RSI_14']].add_suffix('_H1'), 
                     left_on='Datetime', right_index=True, how='left')
        df = df.merge(h4_data[['H4_Trend']], 
                     left_on='Datetime', right_index=True, how='left')
    
    return df

def create_volatility_clustering_features(df):
    """สร้าง Volatility Clustering Features"""
    print("📈 สร้าง Volatility Clustering Features...")
    
    # 1. GARCH-like features
    df['Returns'] = df['Close'].pct_change()
    df['Returns_Squared'] = df['Returns'] ** 2
    
    # 2. Volatility measures
    for window in [5, 10, 20]:
        df[f'Vol_Realized_{window}'] = df['Returns'].rolling(window).std()
        df[f'Vol_EWMA_{window}'] = df['Returns_Squared'].ewm(span=window).mean().apply(np.sqrt)
    
    # 3. Volatility regime changes
    df['Vol_Regime_Change'] = (df['Vol_Realized_10'] > df['Vol_Realized_10'].shift(1) * 1.5).astype(int)
    
    # 4. Volatility clustering score
    df['Vol_Clustering'] = df['Vol_Realized_5'].rolling(10).apply(
        lambda x: (x > x.mean() + x.std()).sum()
    )
    
    # 5. Price gap features
    df['Gap_Open'] = (df['Open'] - df['Close'].shift(1)) / df['Close'].shift(1)
    df['Gap_Size'] = abs(df['Gap_Open'])
    df['Gap_Direction'] = np.sign(df['Gap_Open'])
    
    return df

def create_market_microstructure_features(df):
    """สร้าง Market Microstructure Features"""
    print("🔬 สร้าง Market Microstructure Features...")
    
    # 1. Price action patterns
    df['Doji'] = (abs(df['Close'] - df['Open']) / (df['High'] - df['Low']) < 0.1).astype(int)
    df['Hammer'] = ((df['Close'] > df['Open']) & 
                   ((df['Open'] - df['Low']) > 2 * (df['Close'] - df['Open'])) &
                   ((df['High'] - df['Close']) < 0.1 * (df['Close'] - df['Open']))).astype(int)
    
    # 2. Volume-Price relationship
    df['Volume_MA_20'] = df['Volume'].rolling(20).mean()
    df['Volume_Ratio'] = df['Volume'] / df['Volume_MA_20']
    df['Price_Volume_Trend'] = ((df['Close'] > df['Close'].shift(1)) & 
                               (df['Volume'] > df['Volume_MA_20'])).astype(int)
    
    # 3. Intraday patterns
    if 'Datetime' in df.columns:
        df['Hour'] = pd.to_datetime(df['Datetime']).dt.hour
        df['Is_Asian_Session'] = df['Hour'].between(0, 8).astype(int)
        df['Is_London_Session'] = df['Hour'].between(8, 16).astype(int)
        df['Is_NY_Session'] = df['Hour'].between(13, 21).astype(int)
        df['Is_Overlap'] = ((df['Hour'].between(8, 12)) | 
                           (df['Hour'].between(13, 16))).astype(int)
    
    # 4. Support/Resistance levels
    for window in [20, 50]:
        df[f'Resistance_{window}'] = df['High'].rolling(window).max()
        df[f'Support_{window}'] = df['Low'].rolling(window).min()
        df[f'Distance_to_Resistance_{window}'] = (df[f'Resistance_{window}'] - df['Close']) / df['Close']
        df[f'Distance_to_Support_{window}'] = (df['Close'] - df[f'Support_{window}']) / df['Close']
    
    return df

def create_cross_asset_features(df, symbol):
    """สร้าง Cross-Asset Correlation Features"""
    print("🌐 สร้าง Cross-Asset Features...")
    
    # Currency strength features (simplified)
    if symbol.startswith('USD'):
        df['USD_Strength'] = 1  # Placeholder - ในการใช้งานจริงต้องคำนวณจาก DXY
    elif symbol.startswith('EUR'):
        df['EUR_Strength'] = 1  # Placeholder
    elif symbol.startswith('GBP'):
        df['GBP_Strength'] = 1  # Placeholder
    
    # Risk-on/Risk-off sentiment
    df['Risk_Sentiment'] = np.random.choice([0, 1], size=len(df))  # Placeholder
    
    # Commodity correlation (for GOLD)
    if symbol == 'GOLD':
        df['Oil_Correlation'] = np.random.randn(len(df))  # Placeholder
        df['Dollar_Inverse'] = -df['Close'].pct_change()  # Simplified
    
    return df

def create_seasonal_calendar_features(df):
    """สร้าง Seasonal และ Calendar Effects Features"""
    print("📅 สร้าง Seasonal/Calendar Features...")
    
    if 'Datetime' in df.columns:
        dt = pd.to_datetime(df['Datetime'])
        
        # Time-based features
        df['Day_of_Week'] = dt.dt.dayofweek
        df['Day_of_Month'] = dt.dt.day
        df['Month'] = dt.dt.month
        df['Quarter'] = dt.dt.quarter
        
        # Market timing features
        df['Is_Monday'] = (dt.dt.dayofweek == 0).astype(int)
        df['Is_Friday'] = (dt.dt.dayofweek == 4).astype(int)
        df['Is_Month_End'] = (dt.dt.day > 25).astype(int)
        df['Is_Quarter_End'] = ((dt.dt.month % 3 == 0) & (dt.dt.day > 25)).astype(int)
        
        # Economic calendar effects (simplified)
        df['Is_NFP_Week'] = ((dt.dt.dayofweek == 4) & (dt.dt.day <= 7)).astype(int)
        df['Is_FOMC_Week'] = np.random.choice([0, 1], size=len(df), p=[0.9, 0.1])  # Placeholder
    
    return df

def create_advanced_technical_features(df):
    """สร้าง Advanced Technical Features"""
    print("⚙️ สร้าง Advanced Technical Features...")
    
    # 1. Ichimoku components
    high_9 = df['High'].rolling(9).max()
    low_9 = df['Low'].rolling(9).min()
    df['Tenkan_Sen'] = (high_9 + low_9) / 2
    
    high_26 = df['High'].rolling(26).max()
    low_26 = df['Low'].rolling(26).min()
    df['Kijun_Sen'] = (high_26 + low_26) / 2
    
    df['Senkou_Span_A'] = ((df['Tenkan_Sen'] + df['Kijun_Sen']) / 2).shift(26)
    
    # 2. Fibonacci retracement levels
    df['High_50'] = df['High'].rolling(50).max()
    df['Low_50'] = df['Low'].rolling(50).min()
    df['Fib_23.6'] = df['Low_50'] + 0.236 * (df['High_50'] - df['Low_50'])
    df['Fib_38.2'] = df['Low_50'] + 0.382 * (df['High_50'] - df['Low_50'])
    df['Fib_61.8'] = df['Low_50'] + 0.618 * (df['High_50'] - df['Low_50'])
    
    # 3. Williams %R
    df['Williams_R'] = talib.WILLR(df['High'], df['Low'], df['Close'], timeperiod=14)
    
    # 4. Commodity Channel Index
    df['CCI'] = talib.CCI(df['High'], df['Low'], df['Close'], timeperiod=14)
    
    # 5. Money Flow Index
    df['MFI'] = talib.MFI(df['High'], df['Low'], df['Close'], df['Volume'], timeperiod=14)
    
    return df

def feature_interaction_engineering(df):
    """สร้าง Feature Interactions"""
    print("🔗 สร้าง Feature Interactions...")
    
    # RSI x Volume
    if 'RSI_14' in df.columns and 'Volume_Ratio' in df.columns:
        df['RSI_Volume_Interaction'] = df['RSI_14'] * df['Volume_Ratio']
    
    # MACD x ATR
    if 'MACD' in df.columns and 'ATR_14' in df.columns:
        df['MACD_ATR_Interaction'] = df['MACD'] * df['ATR_14']
    
    # Bollinger Bands x Volatility
    if 'BB_Upper' in df.columns and 'Vol_Realized_10' in df.columns:
        df['BB_Vol_Interaction'] = ((df['Close'] - df['BB_Lower']) / 
                                   (df['BB_Upper'] - df['BB_Lower'])) * df['Vol_Realized_10']
    
    return df

def main():
    """ฟังก์ชันหลักสำหรับทดสอบ Feature Engineering"""
    print("🚀 Advanced Feature Engineering สำหรับโมเดล LightGBM Trading")
    print("="*80)
    
    # สร้างข้อมูลตัวอย่าง
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=1000, freq='30T')
    
    # สร้าง OHLCV data
    base_price = 100
    df = pd.DataFrame({
        'Datetime': dates,
        'Open': base_price + np.cumsum(np.random.randn(1000) * 0.1),
        'High': base_price + np.cumsum(np.random.randn(1000) * 0.1) + abs(np.random.randn(1000) * 0.5),
        'Low': base_price + np.cumsum(np.random.randn(1000) * 0.1) - abs(np.random.randn(1000) * 0.5),
        'Close': base_price + np.cumsum(np.random.randn(1000) * 0.1),
        'Volume': np.random.randint(1000, 10000, 1000)
    })
    
    # ปรับ High/Low ให้สมเหตุสมผล
    df['High'] = df[['Open', 'Close', 'High']].max(axis=1)
    df['Low'] = df[['Open', 'Close', 'Low']].min(axis=1)
    
    print(f"📊 ข้อมูลเริ่มต้น: {len(df)} rows, {len(df.columns)} columns")
    
    # เพิ่ม features ทีละกลุ่ม
    original_cols = len(df.columns)
    
    df = create_market_regime_features(df)
    print(f"  ✅ Market Regime: +{len(df.columns) - original_cols} features")
    
    df = create_volatility_clustering_features(df)
    print(f"  ✅ Volatility Clustering: +{len(df.columns) - original_cols} features")
    
    df = create_market_microstructure_features(df)
    print(f"  ✅ Market Microstructure: +{len(df.columns) - original_cols} features")
    
    df = create_cross_asset_features(df, 'EURUSD')
    print(f"  ✅ Cross-Asset: +{len(df.columns) - original_cols} features")
    
    df = create_seasonal_calendar_features(df)
    print(f"  ✅ Seasonal/Calendar: +{len(df.columns) - original_cols} features")
    
    df = create_advanced_technical_features(df)
    print(f"  ✅ Advanced Technical: +{len(df.columns) - original_cols} features")
    
    df = feature_interaction_engineering(df)
    print(f"  ✅ Feature Interactions: +{len(df.columns) - original_cols} features")
    
    print(f"\n📈 ผลลัพธ์สุดท้าย: {len(df)} rows, {len(df.columns)} columns")
    print(f"🎯 เพิ่มขึ้น: {len(df.columns) - 6} features ใหม่")
    
    # แสดง feature categories
    feature_categories = {
        'Market Regime': [col for col in df.columns if any(x in col for x in ['Trend_', 'Volatility_', 'Market_'])],
        'Volatility': [col for col in df.columns if 'Vol_' in col or 'ATR' in col],
        'Technical': [col for col in df.columns if any(x in col for x in ['RSI', 'MACD', 'BB_', 'SMA'])],
        'Microstructure': [col for col in df.columns if any(x in col for x in ['Doji', 'Hammer', 'Session'])],
        'Calendar': [col for col in df.columns if any(x in col for x in ['Day_', 'Month', 'Is_'])],
        'Interactions': [col for col in df.columns if 'Interaction' in col]
    }
    
    print(f"\n📋 Feature Categories:")
    for category, features in feature_categories.items():
        if features:
            print(f"  {category}: {len(features)} features")
    
    print(f"\n🚀 ขั้นตอนถัดไป:")
    print("1. เพิ่ม functions เหล่านี้ใน python_LightGBM_15_Tuning.py")
    print("2. เรียกใช้ใน feature engineering pipeline")
    print("3. ทดสอบ feature importance")
    print("4. ปรับปรุง features ตามผลลัพธ์")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ตัวอย่างการใช้งานการโหลดพารามิเตอร์ Multi-Model Architecture
แสดงวิธีการโหลดและใช้งานพารามิเตอร์ที่ได้จากการสรุป

การใช้งาน:
    python example_load_multi_model_parameters.py
"""

import sys
import os
import json

# เพิ่ม path สำหรับ import
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def load_parameters_from_json():
    """โหลดพารามิเตอร์จากไฟล์ JSON"""
    
    json_path = "LightGBM_Multi/summaries/multi_model_parameters.json"
    
    if not os.path.exists(json_path):
        print(f"❌ ไม่พบไฟล์: {json_path}")
        print("💡 กรุณารัน summarize_multi_model_parameters.py ก่อน")
        return None
    
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ โหลดข้อมูลจาก {json_path} สำเร็จ")
        print(f"📊 พบข้อมูล {len(data)} รายการ")
        
        return data
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการโหลดไฟล์: {e}")
        return None

def get_parameters_for_symbol_timeframe(data, symbol, timeframe):
    """ดึงพารามิเตอร์สำหรับ symbol และ timeframe ที่กำหนด"""
    
    key = f"{timeframe:03d}_{symbol}" if isinstance(timeframe, int) else f"{timeframe}_{symbol}"
    
    if key in data:
        return data[key]
    else:
        print(f"⚠️ ไม่พบข้อมูลสำหรับ {symbol} M{timeframe}")
        return None

def demonstrate_parameter_usage():
    """แสดงตัวอย่างการใช้งานพารามิเตอร์"""
    
    print("\n🚀 ตัวอย่างการใช้งานพารามิเตอร์ Multi-Model Architecture")
    print("=" * 70)
    
    # โหลดข้อมูล
    data = load_parameters_from_json()
    if not data:
        return
    
    # ตัวอย่างการใช้งาน
    test_cases = [
        ("GOLD", 30),
        ("EURUSD", 60),
        ("GBPUSD", 30)
    ]
    
    for symbol, timeframe in test_cases:
        print(f"\n💰 ตัวอย่าง: {symbol} M{timeframe}")
        print("-" * 40)
        
        params = get_parameters_for_symbol_timeframe(data, symbol, timeframe)
        
        if params:
            # แสดง Time Filters
            time_filters = params.get('time_filters')
            if time_filters:
                days = time_filters.get('days', [])
                hours = time_filters.get('hours', [])
                print(f"⏰ Time Filters:")
                print(f"   วัน: {days}")
                print(f"   ชั่วโมง: {hours}")
            
            # แสดงพารามิเตอร์ Trend Following
            tf_params = params.get('trend_following', {})
            tf_threshold = tf_params.get('threshold')
            tf_nbars = tf_params.get('nBars_SL')
            
            print(f"📈 Trend Following:")
            if tf_threshold is not None and tf_nbars is not None:
                print(f"   Threshold: {tf_threshold:.4f}")
                print(f"   nBars_SL: {tf_nbars}")
            else:
                print(f"   ไม่มีข้อมูล")
            
            # แสดงพารามิเตอร์ Counter Trend
            ct_params = params.get('counter_trend', {})
            ct_threshold = ct_params.get('threshold')
            ct_nbars = ct_params.get('nBars_SL')
            
            print(f"📉 Counter Trend:")
            if ct_threshold is not None and ct_nbars is not None:
                print(f"   Threshold: {ct_threshold:.4f}")
                print(f"   nBars_SL: {ct_nbars}")
            else:
                print(f"   ไม่มีข้อมูล")
        
        else:
            print("❌ ไม่พบข้อมูล")

def demonstrate_trading_logic():
    """แสดงตัวอย่างการใช้งานในลอจิกการเทรด"""
    
    print(f"\n🎯 ตัวอย่างการใช้งานในลอจิกการเทรด")
    print("=" * 50)
    
    # โหลดข้อมูล
    data = load_parameters_from_json()
    if not data:
        return
    
    # ตัวอย่างการใช้งานจริง
    symbol = "GOLD"
    timeframe = 30
    market_scenario = "trend_following"  # หรือ "counter_trend"
    
    print(f"📊 Symbol: {symbol}, Timeframe: M{timeframe}, Scenario: {market_scenario}")
    
    params = get_parameters_for_symbol_timeframe(data, symbol, timeframe)
    
    if params:
        # ดึงพารามิเตอร์ตาม scenario
        scenario_params = params.get(market_scenario, {})
        threshold = scenario_params.get('threshold', 0.5)
        nbars_sl = scenario_params.get('nBars_SL', 6)
        
        # ดึง time filters
        time_filters = params.get('time_filters', {})
        allowed_days = time_filters.get('days', list(range(7)))
        allowed_hours = time_filters.get('hours', list(range(24)))
        
        print(f"\n🔧 พารามิเตอร์ที่ใช้:")
        print(f"   Threshold: {threshold:.4f}")
        print(f"   nBars_SL: {nbars_sl}")
        print(f"   Allowed Days: {allowed_days}")
        print(f"   Allowed Hours: {allowed_hours}")
        
        # ตัวอย่างการใช้งาน
        print(f"\n💡 ตัวอย่างโค้ด:")
        print(f"""
# ตรวจสอบเวลา
current_day = datetime.now().weekday()  # 0=Monday, 6=Sunday
current_hour = datetime.now().hour

if current_day in {allowed_days} and current_hour in {allowed_hours}:
    # ทำนายด้วยโมเดล
    prediction_probability = model.predict_proba(features)[0][1]
    
    if prediction_probability >= {threshold:.4f}:
        # ส่งสัญญาณเทรด
        signal = "BUY" if scenario == "trend_following" else "SELL"
        stop_loss_bars = {nbars_sl}
        
        print(f"🚀 Signal: {{signal}}, Confidence: {{prediction_probability:.4f}}")
        print(f"🛡️ Stop Loss: {{stop_loss_bars}} bars")
    else:
        print("⏸️ Signal: HOLD (confidence too low)")
else:
    print("⏸️ Signal: HOLD (outside time filters)")
        """)
    
    else:
        print("❌ ไม่พบพารามิเตอร์สำหรับการใช้งาน")

def show_available_data():
    """แสดงข้อมูลที่มีอยู่ทั้งหมด"""
    
    print(f"\n📋 ข้อมูลที่มีอยู่ทั้งหมด")
    print("=" * 40)
    
    data = load_parameters_from_json()
    if not data:
        return
    
    # จัดกลุ่มตาม timeframe
    timeframes = {}
    for key, params in data.items():
        tf = params['timeframe']
        if tf not in timeframes:
            timeframes[tf] = []
        timeframes[tf].append(params['symbol'])
    
    for tf in sorted(timeframes.keys(), key=lambda x: int(x)):
        symbols = sorted(timeframes[tf])
        print(f"🕐 M{tf}: {', '.join(symbols)} ({len(symbols)} symbols)")

if __name__ == "__main__":
    print("🔍 Multi-Model Parameters Usage Examples")
    print("=" * 50)
    
    # แสดงข้อมูลที่มีอยู่
    show_available_data()
    
    # แสดงตัวอย่างการใช้งาน
    demonstrate_parameter_usage()
    
    # แสดงตัวอย่างในลอจิกการเทรด
    demonstrate_trading_logic()
    
    print(f"\n✅ ตัวอย่างการใช้งานเสร็จสิ้น!")
    print("💡 สามารถนำโค้ดเหล่านี้ไปปรับใช้ในระบบเทรดจริงได้")

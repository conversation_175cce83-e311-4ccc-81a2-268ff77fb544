# 🖥️ คู่มือการติดตั้งเครื่องใหม่สำหรับ python_LightGBM_15_Tuning.py

## 📋 ลำดับการติดตั้ง (Installation Order)

### 1. 🐍 Python (ขั้นตอนแรก - สำคัญที่สุด)
```bash
# ดาวน์โหลดและติดตั้ง Python 3.8+ (แนะนำ 3.9-3.11)
# จาก https://www.python.org/downloads/
# ⚠️ สำคัญ: เลือก "Add Python to PATH" ตอนติดตั้ง
```

**เวอร์ชันที่แนะนำ:** Python 3.9.x - 3.11.x
- ✅ รองรับ libraries ทั้งหมด
- ✅ เสถียรและทดสอบแล้ว
- ❌ หลีกเลี่ยง Python 3.12+ (อาจมีปัญหา compatibility)

### 2. 📦 Package Manager และ Virtual Environment
```bash
# ตรวจสอบ Python และ pip
python --version
pip --version

# อัปเดต pip
python -m pip install --upgrade pip

# ติดตั้ง virtualenv (แนะนำ)
pip install virtualenv

# สร้าง virtual environment
python -m venv trading_env

# เปิดใช้งาน virtual environment
# Windows:
trading_env\Scripts\activate
# Linux/Mac:
source trading_env/bin/activate
```

### 3. 🔧 Microsoft Visual C++ Build Tools (Windows)
```bash
# ดาวน์โหลดและติดตั้ง Microsoft C++ Build Tools
# จาก https://visualstudio.microsoft.com/visual-cpp-build-tools/
# หรือ Visual Studio Community
# ⚠️ จำเป็นสำหรับ compile TA-Lib และ libraries อื่นๆ
```

### 4. 📊 Core Data Science Libraries
```bash
# ติดตั้งตามลำดับ (สำคัญ!)
pip install numpy==1.24.3
pip install pandas==2.0.3
pip install scipy==1.11.1
```

### 5. 🤖 Machine Learning Libraries
```bash
# scikit-learn และ dependencies
pip install scikit-learn==1.3.0
pip install imbalanced-learn==0.11.0
pip install lightgbm==4.0.0
pip install joblib==1.3.1
```

### 6. 📈 Technical Analysis Libraries
```bash
# TA-Lib (ยากที่สุด - ต้องติดตั้งระวัง)
# Windows:
pip install TA-Lib

# ถ้าติดตั้งไม่ได้ ให้ดาวน์โหลด wheel file จาก:
# https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
# แล้วติดตั้งด้วย: pip install TA_Lib-0.4.xx-cpxx-cpxx-win_amd64.whl

# pandas-ta
pip install pandas-ta==0.3.14b0
```

### 7. 📊 Visualization Libraries
```bash
pip install matplotlib==3.7.2
pip install seaborn==0.12.2
pip install plotly==5.15.0
```

### 8. 📈 Statistical Analysis
```bash
pip install statsmodels==0.14.0
```

### 9. 🔧 Utility Libraries
```bash
pip install pickle-mixin  # ถ้าจำเป็น
```

## 🧪 การทดสอบการติดตั้ง

### สร้างไฟล์ทดสอบ: `test_installation.py`
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการติดตั้ง libraries ทั้งหมดสำหรับ python_LightGBM_15_Tuning.py
"""

def test_imports():
    """ทดสอบ import libraries ทั้งหมด"""
    try:
        # Standard library
        import os, sys, json, datetime, math, traceback, time, pickle
        from collections import Counter
        print("✅ Standard libraries: OK")
        
        # Data science core
        import pandas as pd
        import numpy as np
        print(f"✅ Pandas {pd.__version__}: OK")
        print(f"✅ NumPy {np.__version__}: OK")
        
        # Technical analysis
        import pandas_ta as ta
        import talib
        print("✅ pandas-ta: OK")
        print("✅ TA-Lib: OK")
        
        # Machine learning
        import sklearn
        from sklearn.model_selection import train_test_split, StratifiedKFold, TimeSeriesSplit, RandomizedSearchCV, GridSearchCV, cross_validate
        from sklearn.preprocessing import StandardScaler
        from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_auc_score, f1_score, precision_score, recall_score, roc_curve, precision_recall_curve, average_precision_score, log_loss
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.impute import SimpleImputer
        print(f"✅ scikit-learn {sklearn.__version__}: OK")
        
        import lightgbm as lgb
        print(f"✅ LightGBM {lgb.__version__}: OK")
        
        from imblearn.over_sampling import SMOTE
        print("✅ imbalanced-learn: OK")
        
        import joblib
        print(f"✅ joblib {joblib.__version__}: OK")
        
        # Visualization
        import matplotlib.pyplot as plt
        import seaborn as sns
        import plotly.graph_objects as go
        import plotly.io as pio
        print("✅ Visualization libraries: OK")
        
        # Statistical analysis
        from statsmodels.tsa.stattools import adfuller
        print("✅ statsmodels: OK")
        
        # Windows sound (Windows only)
        try:
            import winsound
            print("✅ winsound: OK")
        except ImportError:
            print("⚠️ winsound: Not available (Linux/Mac)")
        
        print("\n🎉 การติดตั้งสำเร็จทั้งหมด!")
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        return False

def test_basic_functionality():
    """ทดสอบการทำงานพื้นฐาน"""
    try:
        import pandas as pd
        import numpy as np
        import lightgbm as lgb
        
        # สร้างข้อมูลทดสอบ
        data = pd.DataFrame({
            'feature1': np.random.randn(100),
            'feature2': np.random.randn(100),
            'target': np.random.randint(0, 2, 100)
        })
        
        # ทดสอบ LightGBM
        X = data[['feature1', 'feature2']]
        y = data['target']
        
        model = lgb.LGBMClassifier(n_estimators=10, random_state=42, verbose=-1)
        model.fit(X, y)
        predictions = model.predict(X)
        
        print("✅ LightGBM basic functionality: OK")
        print("✅ Data processing: OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Functionality test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 เริ่มทดสอบการติดตั้ง...")
    print("=" * 50)
    
    # ทดสอบ imports
    import_success = test_imports()
    
    if import_success:
        print("\n" + "=" * 50)
        print("🔧 ทดสอบการทำงานพื้นฐาน...")
        functionality_success = test_basic_functionality()
        
        if functionality_success:
            print("\n🎉 ระบบพร้อมใช้งาน python_LightGBM_15_Tuning.py!")
        else:
            print("\n⚠️ มีปัญหาในการทำงานพื้นฐาน")
    else:
        print("\n❌ การติดตั้งไม่สมบูรณ์")
```

## 🚨 ปัญหาที่พบบ่อยและวิธีแก้ไข

### 1. TA-Lib ติดตั้งไม่ได้ (Windows)
```bash
# วิธีแก้:
# 1. ติดตั้ง Microsoft Visual C++ Build Tools
# 2. ดาวน์โหลด wheel file จาก https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
# 3. ติดตั้งด้วย: pip install TA_Lib-0.4.xx-cpxx-cpxx-win_amd64.whl
```

### 2. LightGBM ติดตั้งไม่ได้
```bash
# วิธีแก้:
pip install --upgrade setuptools wheel
pip install lightgbm --no-cache-dir
```

### 3. pandas-ta ติดตั้งไม่ได้
```bash
# วิธีแก้:
pip install pandas-ta --no-deps
pip install pandas-ta==0.3.14b0 --force-reinstall
```

## 📝 ไฟล์ requirements.txt
```txt
numpy==1.24.3
pandas==2.0.3
scipy==1.11.1
scikit-learn==1.3.0
lightgbm==4.0.0
imbalanced-learn==0.11.0
joblib==1.3.1
pandas-ta==0.3.14b0
TA-Lib==0.4.25
matplotlib==3.7.2
seaborn==0.12.2
plotly==5.15.0
statsmodels==0.14.0
```

## 🎯 การติดตั้งแบบ One-Command
```bash
# หลังจากติดตั้ง Python และ Visual C++ Build Tools แล้ว
pip install -r requirements.txt
```

## ✅ Checklist การติดตั้ง
- [ ] Python 3.9-3.11 ติดตั้งแล้ว
- [ ] pip อัปเดตเป็นเวอร์ชันล่าสุด
- [ ] Virtual environment สร้างและเปิดใช้งานแล้ว
- [ ] Microsoft Visual C++ Build Tools ติดตั้งแล้ว (Windows)
- [ ] Core libraries (numpy, pandas, scipy) ติดตั้งแล้ว
- [ ] Machine learning libraries ติดตั้งแล้ว
- [ ] TA-Lib ติดตั้งสำเร็จ
- [ ] pandas-ta ติดตั้งสำเร็จ
- [ ] Visualization libraries ติดตั้งแล้ว
- [ ] รันไฟล์ทดสอบผ่าน
- [ ] python_LightGBM_15_Tuning.py รันได้

## 🔄 การอัปเดต Libraries
```bash
# อัปเดต libraries เป็นเวอร์ชันล่าสุด (ระวัง compatibility)
pip list --outdated
pip install --upgrade package_name
```

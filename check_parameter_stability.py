#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
สคริปต์ตรวจสอบ Parameter Stability ระหว่าง symbols
"""

import os
import json
import pandas as pd
import numpy as np
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns

def detect_model_architecture():
    """ตรวจสอบว่าใช้ Single หรือ Multi-Model Architecture"""
    # ตรวจสอบโฟลเดอร์ hyperparameter tuning
    single_hyper_dir = "LightGBM_Hyper_Single"
    multi_hyper_dir = "LightGBM_Hyper_Multi"

    # ตรวจสอบโฟลเดอร์ models
    single_model_dir = "LightGBM_Single/models"
    multi_model_dir = "LightGBM_Multi/models"
    legacy_dir = "Test_LightGBM/models"

    architectures = []

    # ตรวจสอบ Single Model Architecture
    if (os.path.exists(single_hyper_dir) and os.listdir(single_hyper_dir)) or \
       (os.path.exists(single_model_dir) and os.listdir(single_model_dir)):
        architectures.append("Single")

    # ตรวจสอบ Multi-Model Architecture
    if (os.path.exists(multi_hyper_dir) and os.listdir(multi_hyper_dir)) or \
       (os.path.exists(multi_model_dir) and os.listdir(multi_model_dir)):
        architectures.append("Multi")

    # ตรวจสอบ Legacy Architecture
    if os.path.exists(legacy_dir) and os.listdir(legacy_dir):
        architectures.append("Legacy")

    return architectures

def load_single_model_params(hyper_dir="LightGBM_Hyper_Single"):
    """โหลดพารามิเตอร์จาก Single Model Architecture"""
    all_params = {}

    if not os.path.exists(hyper_dir):
        return {}

    for folder in os.listdir(hyper_dir):
        folder_path = os.path.join(hyper_dir, folder)
        if os.path.isdir(folder_path):
            # หาไฟล์ best_params.json ในโฟลเดอร์
            param_files = [f for f in os.listdir(folder_path) if f.endswith('_best_params.json')]

            for param_file_name in param_files:
                param_file = os.path.join(folder_path, param_file_name)

                if os.path.exists(param_file):
                    try:
                        with open(param_file, 'r') as f:
                            data = json.load(f)

                        # รองรับทั้งรูปแบบเก่าและใหม่
                        if "best_params" in data:
                            params = data["best_params"]
                            score = data.get("best_score", None)
                            tuning_date = data.get("tuning_date", None)
                        else:
                            params = data
                            score = None
                            tuning_date = None

                        # แยก timeframe และ symbol
                        if "_" in folder:
                            timeframe_str, symbol = folder.split("_", 1)
                            timeframe = int(timeframe_str)
                        else:
                            timeframe = "Unknown"
                            symbol = folder

                        key = f"{timeframe}_{symbol}"
                        all_params[key] = {
                            'params': params,
                            'score': score,
                            'tuning_date': tuning_date,
                            'timeframe': timeframe,
                            'symbol': symbol,
                            'architecture': 'Single'
                        }

                    except Exception as e:
                        print(f"⚠️ ไม่สามารถอ่านไฟล์ {param_file}: {e}")

    return all_params

def load_multi_model_params(hyper_dir="LightGBM_Hyper_Multi"):
    """โหลดพารามิเตอร์จาก Multi-Model Architecture"""
    all_params = {}

    if not os.path.exists(hyper_dir):
        return {}

    # ตรวจสอบโฟลเดอร์ symbol_timeframe
    for folder in os.listdir(hyper_dir):
        folder_path = os.path.join(hyper_dir, folder)
        if os.path.isdir(folder_path):
            # หาไฟล์ที่มี scenario name
            param_files = [f for f in os.listdir(folder_path)
                          if f.endswith('_best_params.json') and
                          ('trend_following' in f or 'counter_trend' in f)]

            for param_file_name in param_files:
                param_file = os.path.join(folder_path, param_file_name)

                if os.path.exists(param_file):
                    try:
                        with open(param_file, 'r') as f:
                            data = json.load(f)

                        # รองรับทั้งรูปแบบเก่าและใหม่
                        if "best_params" in data:
                            params = data["best_params"]
                            score = data.get("best_score", None)
                            tuning_date = data.get("tuning_date", None)
                            scenario = data.get("scenario", None)
                        else:
                            params = data
                            score = None
                            tuning_date = None
                            scenario = None

                        # แยก scenario จากชื่อไฟล์ถ้าไม่มีใน data
                        if not scenario:
                            if 'trend_following' in param_file_name:
                                scenario = 'trend_following'
                            elif 'counter_trend' in param_file_name:
                                scenario = 'counter_trend'
                            else:
                                scenario = 'unknown'

                        # แยก timeframe และ symbol จากชื่อโฟลเดอร์
                        if "_" in folder:
                            timeframe_str, symbol = folder.split("_", 1)
                            timeframe = int(timeframe_str)
                        else:
                            timeframe = "Unknown"
                            symbol = folder

                        key = f"{timeframe}_{symbol}_{scenario}"
                        all_params[key] = {
                            'params': params,
                            'score': score,
                            'tuning_date': tuning_date,
                            'timeframe': timeframe,
                            'symbol': symbol,
                            'scenario': scenario,
                            'architecture': 'Multi'
                        }

                    except Exception as e:
                        print(f"⚠️ ไม่สามารถอ่านไฟล์ {param_file}: {e}")

    return all_params

def load_all_best_params():
    """โหลดพารามิเตอร์ที่ดีที่สุดจากทุก architectures"""
    all_params = {}

    # ตรวจสอบ architectures ที่มีอยู่
    architectures = detect_model_architecture()
    print(f"พบ Model Architectures: {architectures}")

    # โหลดจาก Single Model
    if "Single" in architectures:
        print(f"\nโหลดพารามิเตอร์จาก Single Model Architecture...")
        single_params = load_single_model_params()
        all_params.update(single_params)
        print(f"โหลดได้ {len(single_params)} โมเดลจาก Single Architecture")

    # โหลดจาก Multi-Model
    if "Multi" in architectures:
        print(f"\nโหลดพารามิเตอร์จาก Multi-Model Architecture...")
        multi_params = load_multi_model_params()
        all_params.update(multi_params)
        print(f"โหลดได้ {len(multi_params)} โมเดลจาก Multi Architecture")

    # โหลดจาก Legacy (เก่า)
    if "Legacy" in architectures:
        print(f"\nโหลดพารามิเตอร์จาก Legacy Architecture...")
        legacy_params = load_legacy_params()
        all_params.update(legacy_params)
        print(f"โหลดได้ {len(legacy_params)} โมเดลจาก Legacy Architecture")

    return all_params

def load_legacy_params(models_dir="Test_LightGBM/models"):
    """โหลดพารามิเตอร์จาก Legacy Architecture (เก่า)"""
    all_params = {}

    if not os.path.exists(models_dir):
        return {}

    for folder in os.listdir(models_dir):
        folder_path = os.path.join(models_dir, folder)
        if os.path.isdir(folder_path):
            param_file = os.path.join(folder_path, f"{folder}_best_params.json")

            if os.path.exists(param_file):
                try:
                    with open(param_file, 'r') as f:
                        data = json.load(f)

                    # รองรับทั้งรูปแบบเก่าและใหม่
                    if "best_params" in data:
                        params = data["best_params"]
                        score = data.get("best_score", None)
                        tuning_date = data.get("tuning_date", None)
                    else:
                        params = data
                        score = None
                        tuning_date = None

                    # แยก timeframe และ symbol
                    if "_" in folder:
                        timeframe_str, symbol = folder.split("_", 1)
                        timeframe = int(timeframe_str)
                    else:
                        timeframe = "Unknown"
                        symbol = folder

                    key = f"{timeframe}_{symbol}_legacy"
                    all_params[key] = {
                        'params': params,
                        'score': score,
                        'tuning_date': tuning_date,
                        'timeframe': timeframe,
                        'symbol': symbol,
                        'architecture': 'Legacy'
                    }

                except Exception as e:
                    print(f"⚠️ ไม่สามารถอ่านไฟล์ {param_file}: {e}")

    return all_params

def analyze_parameter_stability(all_params):
    """วิเคราะห์ความเสถียรของพารามิเตอร์"""
    if not all_params:
        print("❌ ไม่มีข้อมูลพารามิเตอร์ให้วิเคราะห์")
        return

    print(f"วิเคราะห์ Parameter Stability")
    print("="*60)
    print(f"จำนวน models ที่พบ: {len(all_params)}")

    # สร้าง DataFrame สำหรับการวิเคราะห์
    rows = []
    for model_id, data in all_params.items():
        row = {
            'model_id': model_id,
            'symbol': data['symbol'],
            'timeframe': data['timeframe'],
            'score': data['score'],
            'architecture': data.get('architecture', 'Unknown'),
            'scenario': data.get('scenario', 'N/A')
        }
        row.update(data['params'])
        rows.append(row)

    df = pd.DataFrame(rows)

    # แสดงข้อมูลพื้นฐาน
    print(f"\nข้อมูลพื้นฐาน:")
    print(f"Symbols: {sorted(df['symbol'].unique())}")
    print(f"Timeframes: {sorted(df['timeframe'].unique())}")
    print(f"Architectures: {sorted(df['architecture'].unique())}")

    # แสดงข้อมูลแยกตาม Architecture
    for arch in df['architecture'].unique():
        arch_df = df[df['architecture'] == arch]
        print(f"\n{arch} Architecture:")
        print(f"   จำนวนโมเดล: {len(arch_df)}")
        if arch == 'Multi':
            scenarios = arch_df['scenario'].unique()
            print(f"   Scenarios: {sorted(scenarios)}")
            for scenario in scenarios:
                scenario_count = len(arch_df[arch_df['scenario'] == scenario])
                print(f"     - {scenario}: {scenario_count} โมเดล")

    # วิเคราะห์แต่ละพารามิเตอร์
    numeric_params = ['learning_rate', 'num_leaves', 'max_depth', 'min_data_in_leaf',
                     'feature_fraction', 'bagging_fraction', 'lambda_l1', 'lambda_l2']

    print(f"\nการกระจายของพารามิเตอร์ (รวมทุก Architecture):")
    print("-"*60)

    stability_report = {}

    for param in numeric_params:
        if param in df.columns:
            values = df[param].dropna()
            if len(values) > 0:
                mean_val = values.mean()
                std_val = values.std()
                cv = (std_val / mean_val) * 100 if mean_val != 0 else 0  # Coefficient of Variation

                print(f"{param:20}: Mean={mean_val:.4f}, Std={std_val:.4f}, CV={cv:.1f}%")

                stability_report[param] = {
                    'mean': mean_val,
                    'std': std_val,
                    'cv': cv,
                    'stability': 'High' if cv < 20 else 'Medium' if cv < 50 else 'Low'
                }

    # วิเคราะห์แยกตาม Architecture
    for arch in df['architecture'].unique():
        arch_df = df[df['architecture'] == arch]
        if len(arch_df) > 1:  # ต้องมีมากกว่า 1 โมเดลถึงจะวิเคราะห์ได้
            print(f"\nการกระจายของพารามิเตอร์ใน {arch} Architecture:")
            print("-"*60)

            for param in numeric_params:
                if param in arch_df.columns:
                    values = arch_df[param].dropna()
                    if len(values) > 1:
                        mean_val = values.mean()
                        std_val = values.std()
                        cv = (std_val / mean_val) * 100 if mean_val != 0 else 0
                        stability = 'High' if cv < 20 else 'Medium' if cv < 50 else 'Low'

                        print(f"{param:20}: Mean={mean_val:.4f}, Std={std_val:.4f}, CV={cv:.1f}% ({stability})")

            # สำหรับ Multi-Model ให้วิเคราะห์แยกตาม scenario ด้วย
            if arch == 'Multi':
                scenarios = arch_df['scenario'].unique()
                for scenario in scenarios:
                    scenario_df = arch_df[arch_df['scenario'] == scenario]
                    if len(scenario_df) > 1:
                        print(f"\nการกระจายของพารามิเตอร์ใน {scenario} scenario:")
                        print("-"*50)

                        for param in numeric_params:
                            if param in scenario_df.columns:
                                values = scenario_df[param].dropna()
                                if len(values) > 1:
                                    mean_val = values.mean()
                                    std_val = values.std()
                                    cv = (std_val / mean_val) * 100 if mean_val != 0 else 0
                                    stability = 'High' if cv < 20 else 'Medium' if cv < 50 else 'Low'

                                    print(f"{param:20}: Mean={mean_val:.4f}, Std={std_val:.4f}, CV={cv:.1f}% ({stability})")

    # วิเคราะห์ตาม timeframe
    print(f"\nการวิเคราะห์ตาม Timeframe:")
    print("-"*60)

    for tf in sorted(df['timeframe'].unique()):
        tf_data = df[df['timeframe'] == tf]
        print(f"\nTimeframe {tf} ({len(tf_data)} models):")

        for param in ['learning_rate', 'num_leaves']:
            if param in tf_data.columns:
                values = tf_data[param].dropna()
                if len(values) > 0:
                    print(f"  {param}: {values.mean():.4f} +/- {values.std():.4f}")

    # วิเคราะห์ตาม symbol type
    print(f"\nการวิเคราะห์ตาม Symbol Type:")
    print("-"*60)

    # จัดกลุ่ม symbols
    forex_pairs = ['AUDUSD', 'EURGBP', 'EURUSD', 'GBPUSD', 'NZDUSD', 'USDCAD', 'USDJPY']
    commodities = ['GOLD']

    for group_name, symbols in [('Forex', forex_pairs), ('Commodities', commodities)]:
        group_data = df[df['symbol'].isin(symbols)]
        if len(group_data) > 0:
            print(f"\n{group_name} ({len(group_data)} models):")
            for param in ['learning_rate', 'num_leaves']:
                if param in group_data.columns:
                    values = group_data[param].dropna()
                    if len(values) > 0:
                        print(f"  {param}: {values.mean():.4f} +/- {values.std():.4f}")

    # สรุปความเสถียร
    print(f"\nสรุปความเสถียรของพารามิเตอร์:")
    print("-"*60)

    high_stability = [p for p, data in stability_report.items() if data['stability'] == 'High']
    medium_stability = [p for p, data in stability_report.items() if data['stability'] == 'Medium']
    low_stability = [p for p, data in stability_report.items() if data['stability'] == 'Low']

    if high_stability:
        print(f"High Stability (CV < 20%): {', '.join(high_stability)}")
    if medium_stability:
        print(f"Medium Stability (CV 20-50%): {', '.join(medium_stability)}")
    if low_stability:
        print(f"Low Stability (CV > 50%): {', '.join(low_stability)}")

    # แนะนำการปรับปรุง
    print(f"\nแนะนำการปรับปรุง:")
    print("-"*60)

    if low_stability:
        print(f"1. พารามิเตอร์ที่ไม่เสถียร ({', '.join(low_stability)}):")
        print(f"   - ลองใช้ค่าเฉลี่ยเป็น default")
        print(f"   - ลดช่วงการค้นหาใน param_dist")
        print(f"   - เพิ่มข้อมูลสำหรับ training")

    if len(all_params) < 5:
        print(f"2. ข้อมูลไม่เพียงพอ (มีเพียง {len(all_params)} models):")
        print(f"   - ควรทำ tuning กับ symbols เพิ่มเติม")
        print(f"   - ทดสอบกับ timeframes ต่างๆ")

    return stability_report

def suggest_param_dist_updates(stability_report):
    """แนะนำการอัปเดต param_dist ตามผลการวิเคราะห์"""
    print(f"\nแนะนำการอัปเดต param_dist:")
    print("="*60)

    suggestions = {}

    for param, data in stability_report.items():
        mean_val = data['mean']
        cv = data['cv']
        stability = data['stability']

        if stability == 'High':
            # พารามิเตอร์เสถียร - ลดช่วงการค้นหา
            if param == 'learning_rate':
                center = round(mean_val, 3)
                suggestions[param] = [max(0.001, center-0.02), center, min(0.2, center+0.02)]
            elif param == 'num_leaves':
                center = int(mean_val)
                suggestions[param] = [max(15, center-15), center, min(127, center+15)]
            elif param in ['feature_fraction', 'bagging_fraction']:
                center = round(mean_val, 1)
                suggestions[param] = [max(0.6, center-0.1), center, min(1.0, center+0.1)]

        elif stability == 'Low':
            # พารามิเตอร์ไม่เสถียร - ใช้ช่วงกว้าง
            if param == 'learning_rate':
                suggestions[param] = [0.01, 0.02, 0.05, 0.1]
            elif param == 'num_leaves':
                suggestions[param] = [15, 31, 63, 127]

    if suggestions:
        print("แนะนำการปรับปรุง param_dist:")
        print("```python")
        print("param_dist = {")
        for param, values in suggestions.items():
            print(f"    '{param}': {values},")
        print("}")
        print("```")
    else:
        print("ไม่มีการแนะนำเฉพาะ - param_dist ปัจจุบันเหมาะสมแล้ว")

def create_architecture_comparison_report(all_params):
    """สร้างรายงานเปรียบเทียบระหว่าง architectures"""
    print(f"\nรายงานเปรียบเทียบ Architecture:")
    print("="*60)

    # แยกข้อมูลตาม architecture
    arch_data = {}
    for data in all_params.values():
        arch = data.get('architecture', 'Unknown')
        if arch not in arch_data:
            arch_data[arch] = []
        arch_data[arch].append(data)

    # เปรียบเทียบ performance
    print(f"\nเปรียบเทียบ Performance:")
    print("-"*40)

    for arch, models in arch_data.items():
        scores = [m['score'] for m in models if m['score'] is not None]
        if scores:
            avg_score = np.mean(scores)
            std_score = np.std(scores)
            print(f"{arch:15}: Avg Score={avg_score:.4f} +/- {std_score:.4f} ({len(scores)} models)")
        else:
            print(f"{arch:15}: No score data ({len(models)} models)")

    # เปรียบเทียบ parameter stability ระหว่าง architectures
    print(f"\nเปรียบเทียบ Parameter Stability:")
    print("-"*40)

    numeric_params = ['learning_rate', 'num_leaves', 'max_depth', 'min_data_in_leaf']

    for param in numeric_params:
        print(f"\n{param}:")
        for arch, models in arch_data.items():
            values = []
            for model in models:
                if param in model['params']:
                    values.append(model['params'][param])

            if len(values) > 1:
                mean_val = np.mean(values)
                cv = (np.std(values) / mean_val) * 100 if mean_val != 0 else 0
                stability = 'High' if cv < 20 else 'Medium' if cv < 50 else 'Low'
                print(f"  {arch:12}: CV={cv:5.1f}% ({stability})")
            elif len(values) == 1:
                print(f"  {arch:12}: Single value ({values[0]})")
            else:
                print(f"  {arch:12}: No data")
def main():
    """ฟังก์ชันหลัก"""
    print("Parameter Stability Analysis (Multi-Architecture Support)")
    print("="*70)

    # โหลดข้อมูล
    all_params = load_all_best_params()

    if not all_params:
        print("\n❌ ไม่พบข้อมูลพารามิเตอร์")
        print("กรุณารัน hyperparameter tuning ก่อน:")
        print("   python python_LightGBM_17_Signal.py")
        return

    # วิเคราะห์
    stability_report = analyze_parameter_stability(all_params)

    # สร้างรายงานเปรียบเทียบ architecture
    create_architecture_comparison_report(all_params)

    # แนะนำการปรับปรุง
    if stability_report:
        suggest_param_dist_updates(stability_report)

    print(f"\nการวิเคราะห์เสร็จสิ้น")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการทำงานของ MT5 WebRequest Server ที่ปรับปรุงสำหรับ Multi-Model Architecture
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

def test_imports():
    """ทดสอบการ import ฟังก์ชันจาก python_LightGBM_16_Signal.py"""
    print("🔍 ทดสอบการ import...")
    
    try:
        from python_LightGBM_16_Signal import (
            load_scenario_models, predict_with_scenario_model,
            detect_market_scenario, get_optimal_parameters,
            load_scenario_threshold, load_scenario_nbars, load_time_filters,
            USE_MULTI_MODEL_ARCHITECTURE, MARKET_SCENARIOS
        )
        
        print("✅ Import สำเร็จ")
        print(f"   USE_MULTI_MODEL_ARCHITECTURE: {USE_MULTI_MODEL_ARCHITECTURE}")
        print(f"   Available scenarios: {list(MARKET_SCENARIOS.keys())}")
        return True
        
    except ImportError as e:
        print(f"❌ Import ล้มเหลว: {e}")
        return False

def test_model_loading():
    """ทดสอบการโหลดโมเดล Multi-Model Architecture"""
    print("\n🔍 ทดสอบการโหลดโมเดล...")
    
    try:
        from python_LightGBM_16_Signal import load_scenario_models
        
        # ทดสอบกับ GOLD M60 (ควรมีโมเดลอยู่)
        symbol = "GOLD"
        timeframe = 60
        
        print(f"📊 กำลังโหลดโมเดลสำหรับ {symbol} M{timeframe}")
        
        scenario_models = load_scenario_models(symbol, timeframe)
        
        if scenario_models:
            print(f"✅ โหลดโมเดลสำเร็จ: {len(scenario_models)} scenarios")
            
            for scenario_name, model_info in scenario_models.items():
                print(f"\n📋 {scenario_name}:")
                print(f"   Model: {type(model_info['model']).__name__}")
                print(f"   Features: {len(model_info['features'])} features")
                print(f"   Has scaler: {'scaler' in model_info}")
                
                # แสดง features บางส่วน
                features_sample = model_info['features'][:5]
                print(f"   Sample features: {features_sample}")
            
            return scenario_models
        else:
            print("❌ ไม่สามารถโหลดโมเดลได้")
            return None
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return None

def test_prediction():
    """ทดสอบการทำนายด้วย Multi-Model Architecture"""
    print("\n🔍 ทดสอบการทำนาย...")
    
    try:
        from python_LightGBM_16_Signal import (
            load_scenario_models, predict_with_scenario_model,
            detect_market_scenario
        )
        
        symbol = "GOLD"
        timeframe = 60
        
        # โหลดโมเดล
        scenario_models = load_scenario_models(symbol, timeframe)
        
        if not scenario_models:
            print("❌ ไม่สามารถโหลดโมเดลได้")
            return False
        
        # สร้างข้อมูลทดสอบ (จำลองข้อมูลตลาด)
        test_data = pd.Series({
            'Close': 2650.50,
            'High': 2655.00,
            'Low': 2645.00,
            'Open': 2648.00,
            'EMA200': 2640.00,  # ราคาอยู่เหนือ EMA200 = Uptrend
            'RSI14': 65.5,
            'ATR': 15.2,
            'Volume': 1000,
            'EMA50': 2645.00,
            'EMA_diff': 10.50,
            'BB_width': 25.0,
            'MACD_12_26_9': 2.5,
            'MACDs_12_26_9': 1.8,
            'MACDh_12_26_9': 0.7,
            'STOCHk_14_3_3': 70.2,
            'STOCHd_14_3_3': 68.5,
            'ADX_14': 25.8,
            'DMP_14': 28.5,
            'DMN_14': 15.2
        })
        
        print(f"📊 ข้อมูลทดสอบ:")
        print(f"   Close: {test_data['Close']}")
        print(f"   EMA200: {test_data['EMA200']}")
        print(f"   RSI14: {test_data['RSI14']}")
        
        # ตรวจจับสถานการณ์ตลาด
        market_condition = detect_market_scenario(test_data)
        print(f"🔍 Market Condition: {market_condition}")
        
        # ทดสอบการทำนายสำหรับ BUY
        print(f"\n🤖 ทดสอบการทำนาย BUY:")
        should_trade_buy, confidence_buy, model_used_buy = predict_with_scenario_model(
            test_data, 'buy', scenario_models, 0.5
        )
        
        print(f"   Should trade: {should_trade_buy}")
        print(f"   Confidence: {confidence_buy:.4f}")
        print(f"   Model used: {model_used_buy}")
        
        # ทดสอบการทำนายสำหรับ SELL
        print(f"\n🤖 ทดสอบการทำนาย SELL:")
        should_trade_sell, confidence_sell, model_used_sell = predict_with_scenario_model(
            test_data, 'sell', scenario_models, 0.5
        )
        
        print(f"   Should trade: {should_trade_sell}")
        print(f"   Confidence: {confidence_sell:.4f}")
        print(f"   Model used: {model_used_sell}")
        
        # สรุปผลการทำนาย
        print(f"\n📊 สรุปผลการทำนาย:")
        if confidence_buy > confidence_sell and should_trade_buy:
            final_signal = "BUY"
            final_confidence = confidence_buy
            final_model = model_used_buy
        elif confidence_sell > confidence_buy and should_trade_sell:
            final_signal = "SELL"
            final_confidence = confidence_sell
            final_model = model_used_sell
        else:
            final_signal = "HOLD"
            final_confidence = max(confidence_buy, confidence_sell)
            final_model = "none"
        
        print(f"   Final Signal: {final_signal}")
        print(f"   Final Confidence: {final_confidence:.4f}")
        print(f"   Final Model: {final_model}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทำนาย: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_optimal_parameters():
    """ทดสอบการโหลด optimal parameters"""
    print("\n🔍 ทดสอบการโหลด optimal parameters...")
    
    try:
        from python_LightGBM_16_Signal import (
            load_scenario_threshold, load_scenario_nbars
        )
        
        symbol = "GOLD"
        timeframe = 60
        scenarios = ["trend_following", "counter_trend"]
        
        print(f"🎯 ทดสอบการโหลดพารามิเตอร์สำหรับ {symbol} M{timeframe}")
        
        for scenario in scenarios:
            threshold = load_scenario_threshold(symbol, timeframe, scenario)
            nbars = load_scenario_nbars(symbol, timeframe, scenario)
            
            print(f"\n📋 {scenario}:")
            print(f"   Threshold: {threshold}")
            print(f"   nBars_SL: {nbars}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def main():
    """ฟังก์ชันหลักสำหรับทดสอบ"""
    print("🚀 เริ่มทดสอบ MT5 WebRequest Server Multi-Model Architecture")
    print("="*80)
    
    # ทดสอบการ import
    if not test_imports():
        print("❌ การทดสอบล้มเหลวที่ขั้นตอน import")
        return
    
    # ทดสอบการโหลดโมเดล
    if not test_model_loading():
        print("❌ การทดสอบล้มเหลวที่ขั้นตอนโหลดโมเดล")
        return
    
    # ทดสอบการทำนาย
    if not test_prediction():
        print("❌ การทดสอบล้มเหลวที่ขั้นตอนการทำนาย")
        return
    
    # ทดสอบการโหลด optimal parameters
    if not test_optimal_parameters():
        print("❌ การทดสอบล้มเหลวที่ขั้นตอนโหลด optimal parameters")
        return
    
    print("\n" + "="*80)
    print("✅ การทดสอบทั้งหมดผ่านเรียบร้อย!")
    print("🎉 MT5 WebRequest Server พร้อมใช้งาน Multi-Model Architecture")

if __name__ == "__main__":
    main()

# 📊 Summary Thresholds - Multi-Model Architecture Parameters

เครื่องมือสำหรับสรุปและจัดการพารามิเตอร์ Multi-Model Architecture ที่ได้จากการเทรนโมเดล LightGBM

## 🎯 วัตถุประสงค์

สรุปและจัดระเบียบพารามิเตอร์ทั้งหมดจากการเทรน Multi-Model Architecture:
- **Threshold** สำหรับ trend_following และ counter_trend
- **nBars_SL** สำหรับการกำหนด Stop Loss
- **Time Filters** สำหรับการกรองเวลาเทรด

## 📁 โครงสร้างไฟล์ที่ประมวลผล

```
LightGBM_Multi/thresholds/
├─ {timeframe}_{symbol}_counter_trend_optimal_nBars_SL.pkl
├─ {timeframe}_{symbol}_counter_trend_optimal_threshold.pkl
├─ {timeframe}_{symbol}_trend_following_optimal_nBars_SL.pkl
├─ {timeframe}_{symbol}_trend_following_optimal_threshold.pkl
└─ {timeframe}_{symbol}_time_filters.pkl
```

## 🚀 การใช้งาน

### 1. สร้างรายงานสรุป
```bash
python summary_thresholds.py
```

### 2. ดูตัวอย่างการใช้งาน
```bash
python example_use_summary_thresholds.py
```

## 📄 ไฟล์ที่สร้าง

### รายงานหลัก
- `thresholds_summary_main.txt` - สรุปรวมทั้งหมด
- `thresholds_parameters.json` - ข้อมูล JSON สำหรับโปรแกรม

### รายงานแยกตาม Timeframe
- `thresholds_summary_M030.txt` - สรุปเฉพาะ M30
- `thresholds_summary_M060.txt` - สรุปเฉพาะ M60

### รายงานแยกตาม Symbol
- `thresholds_summary_GOLD.txt` - สรุปเฉพาะ GOLD
- `thresholds_summary_EURUSD.txt` - สรุปเฉพาะ EURUSD
- `thresholds_summary_GBPUSD.txt` - สรุปเฉพาะ GBPUSD
- ... (สำหรับทุก symbol)

## 💡 ตัวอย่างการใช้งานในโค้ด

### โหลดข้อมูล
```python
import json

# โหลดข้อมูลจาก JSON
with open('LightGBM_Multi/summaries/thresholds_parameters.json', 'r') as f:
    data = json.load(f)

# ดึงพารามิเตอร์สำหรับ GOLD M30 Trend Following
gold_m30 = data['030_GOLD']
threshold = gold_m30['trend_following']['threshold']  # 0.5000
nbars_sl = gold_m30['trend_following']['nBars_SL']   # 12
time_filters = gold_m30['time_filters']              # วันและเวลา
```

### ใช้งานในการเทรด
```python
import datetime

def check_trading_conditions(symbol, timeframe, scenario, prediction_prob):
    # โหลดพารามิเตอร์
    key = f"{timeframe:03d}_{symbol}"
    params = data[key]
    
    # ดึงค่า threshold และ nBars_SL
    scenario_params = params[scenario]
    threshold = scenario_params['threshold']
    nbars_sl = scenario_params['nBars_SL']
    
    # ตรวจสอบ time filters
    time_filters = params['time_filters']
    current_day = datetime.datetime.now().weekday()
    current_hour = datetime.datetime.now().hour
    
    time_allowed = True
    if time_filters['days']:
        time_allowed = time_allowed and (current_day in time_filters['days'])
    if time_filters['hours']:
        time_allowed = time_allowed and (current_hour in time_filters['hours'])
    
    # ตัดสินใจ
    if time_allowed and prediction_prob >= threshold:
        signal = "BUY" if scenario == "trend_following" else "SELL"
        return {
            'signal': signal,
            'confidence': prediction_prob,
            'stop_loss_bars': nbars_sl
        }
    else:
        return {'signal': 'HOLD'}

# ตัวอย่างการใช้งาน
result = check_trading_conditions("GOLD", 30, "trend_following", 0.65)
print(result)  # {'signal': 'BUY', 'confidence': 0.65, 'stop_loss_bars': 12}
```

## 📊 ข้อมูลที่ได้

### สถิติปัจจุบัน
- **16 รายการ** ทั้งหมด (8 symbols × 2 timeframes)
- **2 Timeframes**: M30, M60
- **8 Symbols**: AUDUSD, EURGBP, EURUSD, GBPUSD, GOLD, NZDUSD, USDCAD, USDJPY
- **ข้อมูลครบ**: 16/16 รายการ

### ตัวอย่างพารามิเตอร์
| Symbol | TF   | TF_Threshold | TF_nBars | CT_Threshold | CT_nBars |
|--------|------|--------------|----------|--------------|----------|
| GOLD   | M030 | 0.5000       | 12       | 0.5500       | 12       |
| GOLD   | M060 | 0.5000       | 12       | 0.5000       | 12       |
| EURUSD | M030 | 0.1000       | 11       | 0.1000       | 11       |
| GBPUSD | M030 | 0.1000       | 8        | 0.1000       | 8        |

## 🔧 ฟีเจอร์หลัก

### 1. การสรุปอัตโนมัติ
- ค้นหาไฟล์พารามิเตอร์อัตโนมัติ
- รวบรวมข้อมูลจากทุก symbol และ timeframe
- จัดระเบียบตามโครงสร้างที่เข้าใจง่าย

### 2. รายงานหลากหลายรูปแบบ
- **รายงานหลัก**: สรุปรวมทั้งหมด
- **แยกตาม Timeframe**: M30, M60
- **แยกตาม Symbol**: GOLD, EURUSD, etc.
- **JSON**: สำหรับการใช้งานโปรแกรม

### 3. การแสดงผลที่เข้าใจง่าย
- แปลงวันและเวลาเป็นข้อความที่อ่านง่าย
- จัดรูปแบบตัวเลขให้สวยงาม
- ใช้ emoji และสีสันเพื่อความชัดเจน

### 4. ตัวอย่างการใช้งาน
- โค้ดตัวอย่างสำหรับการโหลดข้อมูล
- ตัวอย่างการใช้งานในระบบเทรด
- การเปรียบเทียบระหว่าง scenarios

## 🛠️ การติดตั้งและความต้องการ

### ความต้องการ
- Python 3.6+
- ไฟล์พารามิเตอร์จากการเทรน Multi-Model Architecture

### ไฟล์ที่จำเป็น
- `summary_thresholds.py` - สคริปต์หลัก
- `example_use_summary_thresholds.py` - ตัวอย่างการใช้งาน

## 📝 หมายเหตุ

### Time Filters
- **ไม่มีวัน**: ไม่มีการจำกัดวัน (เทรดได้ทุกวัน)
- **ไม่มีชั่วโมง**: ไม่มีการจำกัดเวลา (เทรดได้ตลอด 24 ชั่วโมง)
- **มีการจำกัด**: แสดงวันและเวลาที่แนะนำ

### Scenarios
- **Trend Following**: เหมาะสำหรับตลาดที่มีแนวโน้ม
- **Counter Trend**: เหมาะสำหรับตลาดที่ขาดแนวโน้มหรือ sideways

### Threshold
- **ค่าสูง** (0.5+): Conservative, สัญญาณน้อยแต่แม่นยำ
- **ค่าต่ำ** (0.1-0.3): Aggressive, สัญญาณมากแต่อาจมี noise

## 🔄 การอัปเดต

เมื่อมีการเทรนโมเดลใหม่หรือปรับพารามิเตอร์:
1. รัน `python summary_thresholds.py` ใหม่
2. ตรวจสอบไฟล์รายงานที่อัปเดต
3. อัปเดตระบบเทรดด้วยพารามิเตอร์ใหม่

## 💬 การสนับสนุน

หากมีปัญหาหรือข้อสงสัย:
1. ตรวจสอบว่าไฟล์พารามิเตอร์อยู่ในตำแหน่งที่ถูกต้อง
2. ตรวจสอบ log ข้อผิดพลาดจากการรันสคริปต์
3. ตรวจสอบรูปแบบไฟล์ JSON ที่สร้างขึ้น

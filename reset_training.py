#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Reset Training Script - 2025-07-05
ลบไฟล์ที่เกี่ยวข้องกับ hyperparameter tuning เพื่อเริ่มเทรนใหม่
"""

import os
import glob
import json
from pathlib import Path

def reset_hyperparameter_files():
    """ลบไฟล์ที่เกี่ยวข้องกับ hyperparameter tuning"""
    
    print("🔄 Reset Hyperparameter Training Files")
    print("="*50)
    
    # โฟลเดอร์หลักที่เก็บโมเดล
    models_dir = "Test_LightGBM/models"
    
    if not os.path.exists(models_dir):
        print(f"⚠️ ไม่พบโฟลเดอร์ {models_dir}")
        return
    
    # ค้นหาและลบไฟล์ _tuning_flag.json
    flag_files = glob.glob(f"{models_dir}/**/*_tuning_flag.json", recursive=True)
    print(f"\n📁 ไฟล์ tuning_flag ที่พบ: {len(flag_files)}")
    
    for file_path in flag_files:
        try:
            os.remove(file_path)
            print(f"✅ ลบ: {file_path}")
        except Exception as e:
            print(f"❌ ไม่สามารถลบ {file_path}: {e}")
    
    # ค้นหาและลบไฟล์ _best_params.json
    params_files = glob.glob(f"{models_dir}/**/*_best_params.json", recursive=True)
    print(f"\n📁 ไฟล์ best_params ที่พบ: {len(params_files)}")
    
    for file_path in params_files:
        try:
            os.remove(file_path)
            print(f"✅ ลบ: {file_path}")
        except Exception as e:
            print(f"❌ ไม่สามารถลบ {file_path}: {e}")
    
    # ค้นหาและลบไฟล์โมเดล .pkl (ถ้าต้องการเริ่มต้นใหม่ทั้งหมด)
    model_files = glob.glob(f"{models_dir}/**/*.pkl", recursive=True)
    print(f"\n📁 ไฟล์โมเดล .pkl ที่พบ: {len(model_files)}")
    
    if model_files:
        response = input("❓ ต้องการลบไฟล์โมเดล .pkl ด้วยหรือไม่? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            for file_path in model_files:
                try:
                    os.remove(file_path)
                    print(f"✅ ลบโมเดล: {file_path}")
                except Exception as e:
                    print(f"❌ ไม่สามารถลบ {file_path}: {e}")
        else:
            print("⏭️ ข้ามการลบไฟล์โมเดล")
    
    print(f"\n✅ Reset เสร็จสิ้น!")
    print("🚀 พร้อมเริ่มเทรนใหม่ด้วยพารามิเตอร์ที่ปรับปรุงแล้ว")

def reset_specific_symbols(symbols=None, timeframes=None):
    """ลบไฟล์เฉพาะ symbol และ timeframe ที่กำหนด"""
    
    if symbols is None:
        symbols = ['USDJPY', 'GBPUSD']  # symbols ที่มีปัญหา
    
    if timeframes is None:
        timeframes = ['M30', 'H1']
    
    print(f"🎯 Reset เฉพาะ symbols: {symbols}")
    print(f"🎯 Reset เฉพาะ timeframes: {timeframes}")
    print("="*50)
    
    models_dir = "Test_LightGBM/models"
    
    for symbol in symbols:
        for timeframe in timeframes:
            # สร้าง pattern สำหรับไฟล์ที่ต้องลบ
            timeframe_padded = str(timeframe).zfill(3) if timeframe.isdigit() else timeframe
            pattern_base = f"{timeframe_padded}_{symbol}"
            
            # ลบ tuning flag
            flag_pattern = f"{models_dir}/**/{pattern_base}_tuning_flag.json"
            flag_files = glob.glob(flag_pattern, recursive=True)
            
            # ลบ best params
            params_pattern = f"{models_dir}/**/{pattern_base}_best_params.json"
            params_files = glob.glob(params_pattern, recursive=True)
            
            # ลบ model files
            model_pattern = f"{models_dir}/**/{pattern_base}_*.pkl"
            model_files = glob.glob(model_pattern, recursive=True)
            
            all_files = flag_files + params_files + model_files
            
            if all_files:
                print(f"\n📊 {symbol} {timeframe}:")
                for file_path in all_files:
                    try:
                        os.remove(file_path)
                        print(f"  ✅ ลบ: {os.path.basename(file_path)}")
                    except Exception as e:
                        print(f"  ❌ ไม่สามารถลบ {file_path}: {e}")
            else:
                print(f"\n📊 {symbol} {timeframe}: ไม่พบไฟล์ที่ต้องลบ")

def check_training_status():
    """ตรวจสอบสถานะการเทรนปัจจุบัน"""
    
    print("📊 ตรวจสอบสถานะการเทรน")
    print("="*50)
    
    models_dir = "Test_LightGBM/models"
    
    if not os.path.exists(models_dir):
        print(f"⚠️ ไม่พบโฟลเดอร์ {models_dir}")
        return
    
    # ค้นหาไฟล์ flag
    flag_files = glob.glob(f"{models_dir}/**/*_tuning_flag.json", recursive=True)
    
    print(f"📁 ไฟล์ tuning flag ที่พบ: {len(flag_files)}")
    
    for flag_file in flag_files:
        try:
            with open(flag_file, 'r') as f:
                flag_data = json.load(f)
            
            filename = os.path.basename(flag_file)
            do_tuning = flag_data.get("do_hyperparameter_tuning", "Unknown")
            print(f"  📄 {filename}: do_hyperparameter_tuning = {do_tuning}")
            
        except Exception as e:
            print(f"  ❌ ไม่สามารถอ่าน {flag_file}: {e}")
    
    # ค้นหาไฟล์ best_params
    params_files = glob.glob(f"{models_dir}/**/*_best_params.json", recursive=True)
    
    print(f"\n📁 ไฟล์ best_params ที่พบ: {len(params_files)}")
    
    for params_file in params_files:
        filename = os.path.basename(params_file)
        print(f"  📄 {filename}")

def main():
    """Main function"""
    
    print("🔄 Reset Training Script - 2025-07-05")
    print("="*60)
    
    while True:
        print("\n📋 เลือกการดำเนินการ:")
        print("1. ตรวจสอบสถานะการเทรนปัจจุบัน")
        print("2. Reset ทั้งหมด (ลบไฟล์ flag และ best_params ทั้งหมด)")
        print("3. Reset เฉพาะ USDJPY และ GBPUSD")
        print("4. Reset เฉพาะ symbol ที่กำหนดเอง")
        print("5. ออกจากโปรแกรม")
        
        choice = input("\n👉 เลือก (1-5): ").strip()
        
        if choice == "1":
            check_training_status()
            
        elif choice == "2":
            confirm = input("⚠️ ยืนยันการลบไฟล์ทั้งหมด? (y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                reset_hyperparameter_files()
            else:
                print("❌ ยกเลิกการดำเนินการ")
                
        elif choice == "3":
            confirm = input("⚠️ ยืนยันการ reset USDJPY และ GBPUSD? (y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                reset_specific_symbols(['USDJPY', 'GBPUSD'], ['M30', 'H1'])
            else:
                print("❌ ยกเลิกการดำเนินการ")
                
        elif choice == "4":
            symbols_input = input("📝 ใส่ symbols (เช่น EURUSD,GBPUSD): ").strip()
            timeframes_input = input("📝 ใส่ timeframes (เช่น M30,H1): ").strip()
            
            if symbols_input and timeframes_input:
                symbols = [s.strip() for s in symbols_input.split(',')]
                timeframes = [t.strip() for t in timeframes_input.split(',')]
                
                confirm = input(f"⚠️ ยืนยันการ reset {symbols} {timeframes}? (y/N): ").strip().lower()
                if confirm in ['y', 'yes']:
                    reset_specific_symbols(symbols, timeframes)
                else:
                    print("❌ ยกเลิกการดำเนินการ")
            else:
                print("❌ กรุณาใส่ข้อมูลให้ครบถ้วน")
                
        elif choice == "5":
            print("👋 ออกจากโปรแกรม")
            break
            
        else:
            print("❌ กรุณาเลือก 1-5")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Single Multi-class Training
ทดสอบการเทรน Multi-class แบบไฟล์เดียว

Created: 2025-07-04
"""

import sys
import os
import time
from datetime import datetime

# เพิ่ม path สำหรับ import main file
sys.path.append('.')

# Import functions from main file
from python_LightGBM_15_Tuning import (
    main,
    test_groups,
    print_trading_schedule_summary,
    USE_MULTICLASS_TARGET
)

def test_single_symbol(symbol="GBPUSD", timeframe="M30"):
    """ทดสอบการเทรน symbol เดียว"""
    print(f"🚀 ทดสอบการเทรน Multi-class: {symbol} {timeframe}")
    print(f"📊 USE_MULTICLASS_TARGET: {USE_MULTICLASS_TARGET}")
    print("="*60)
    
    # หาไฟล์ที่ตรงกับ symbol และ timeframe
    target_file = None
    for tf, files in test_groups.items():
        if tf == timeframe:
            for file_path in files:
                if symbol in file_path:
                    target_file = file_path
                    break
    
    if not target_file:
        print(f"❌ ไม่พบไฟล์สำหรับ {symbol} {timeframe}")
        return None
    
    print(f"📁 ไฟล์ที่จะใช้: {target_file}")
    
    # ตรวจสอบว่าไฟล์มีอยู่จริง
    if not os.path.exists(target_file):
        print(f"❌ ไฟล์ไม่มีอยู่: {target_file}")
        return None
    
    # สำรองค่า test_groups เดิม
    original_test_groups = test_groups.copy()
    
    try:
        # ตั้งค่า test_groups ให้มีแค่ไฟล์เดียว
        import python_LightGBM_15_Tuning
        python_LightGBM_15_Tuning.test_groups = {timeframe: [target_file]}
        
        print(f"⏰ เริ่มการเทรน: {datetime.now().strftime('%H:%M:%S')}")
        start_time = time.time()
        
        # รันการเทรน
        run_id = f"test_multiclass_{symbol}_{timeframe}"
        result = main(
            run_identifier=run_id,
            group_name=f"{symbol}_{timeframe}",
            input_files=[target_file]  # ส่งไฟล์เดียวที่ต้องการ
        )
        
        end_time = time.time()
        training_time = end_time - start_time
        
        print(f"⏰ เสร็จสิ้นการเทรน: {datetime.now().strftime('%H:%M:%S')}")
        print(f"⏱️ ใช้เวลา: {training_time:.1f} วินาที ({training_time/60:.1f} นาที)")
        
        if result is not None:
            print(f"✅ การเทรน {symbol} {timeframe} สำเร็จ")
        else:
            print(f"❌ การเทรน {symbol} {timeframe} ล้มเหลว")
        
        return result
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        # คืนค่า test_groups เดิม
        import python_LightGBM_15_Tuning
        python_LightGBM_15_Tuning.test_groups = original_test_groups

def test_multiple_symbols():
    """ทดสอบการเทรนหลาย symbols"""
    print(f"🚀 ทดสอบการเทรน Multi-class หลาย symbols")
    print("="*60)
    
    # รายการ symbols ที่จะทดสอบ
    test_symbols = [
        ("GBPUSD", "M30"),
        ("EURUSD", "M30"),
        ("GOLD", "M30")
    ]
    
    results = []
    total_start_time = time.time()
    
    for i, (symbol, timeframe) in enumerate(test_symbols, 1):
        print(f"\n📊 [{i}/{len(test_symbols)}] กำลังเทรน: {symbol} {timeframe}")
        print("-" * 40)
        
        result = test_single_symbol(symbol, timeframe)
        results.append({
            'symbol': symbol,
            'timeframe': timeframe,
            'success': result is not None,
            'result': result
        })
        
        # แสดงความคืบหน้า
        elapsed = time.time() - total_start_time
        remaining = len(test_symbols) - i
        if i > 0:
            avg_time = elapsed / i
            estimated_remaining = remaining * avg_time
            print(f"⏱️ ความคืบหน้า: {i}/{len(test_symbols)} ({(i/len(test_symbols))*100:.1f}%)")
            print(f"⏱️ เวลาที่เหลือประมาณ: {estimated_remaining/60:.1f} นาที")
    
    total_time = time.time() - total_start_time
    
    # สรุปผลการทดสอบ
    print(f"\n{'='*60}")
    print("📊 สรุปผลการทดสอบ Multi-class")
    print(f"{'='*60}")
    
    success_count = sum(1 for r in results if r['success'])
    print(f"✅ สำเร็จ: {success_count}/{len(results)}")
    print(f"⏰ เวลารวม: {total_time/60:.1f} นาที")
    
    for result in results:
        status = "✅ สำเร็จ" if result['success'] else "❌ ล้มเหลว"
        print(f"  {result['symbol']} {result['timeframe']}: {status}")
    
    return results

def show_trading_schedule():
    """แสดงแนะนำการเทรดรายวัน"""
    print(f"\n{'='*60}")
    print("📅 แสดงแนะนำการเทรดรายวัน")
    print(f"{'='*60}")
    
    try:
        print_trading_schedule_summary()
    except Exception as e:
        print(f"❌ ไม่สามารถแสดงแนะนำการเทรดได้: {str(e)}")
        print("💡 ให้ทำการเทรนโมเดลก่อนเพื่อสร้างข้อมูล time filters")

def main_menu():
    """เมนูหลักสำหรับการทดสอบ"""
    print("🎯 Multi-class Training Test Menu")
    print("="*50)
    print("1. ทดสอบ symbol เดียว (GBPUSD M30)")
    print("2. ทดสอบหลาย symbols (GBPUSD, EURUSD, GOLD M30)")
    print("3. แสดงแนะนำการเทรดรายวัน")
    print("4. กำหนดเอง")
    print("5. ออก")
    
    choice = input("\nเลือก (1-5): ").strip()
    
    if choice == "1":
        # ทดสอบ symbol เดียว
        result = test_single_symbol("GBPUSD", "M30")
        if result:
            show_trading_schedule()
    
    elif choice == "2":
        # ทดสอบหลาย symbols
        results = test_multiple_symbols()
        if any(r['success'] for r in results):
            show_trading_schedule()
    
    elif choice == "3":
        # แสดงแนะนำการเทรด
        show_trading_schedule()
    
    elif choice == "4":
        # กำหนดเอง
        print("\nกำหนดการทดสอบเอง:")
        symbol = input("Symbol (เช่น GBPUSD): ").strip().upper() or "GBPUSD"
        timeframe = input("Timeframe (M30/M60): ").strip().upper() or "M30"
        
        result = test_single_symbol(symbol, timeframe)
        if result:
            show_trading_schedule()
    
    elif choice == "5":
        print("👋 ออกจากโปรแกรม")
        return
    
    else:
        print("❌ ตัวเลือกไม่ถูกต้อง")
    
    # ถามว่าต้องการทำต่อหรือไม่
    if input("\nต้องการทำต่อหรือไม่? (y/n): ").strip().lower() == 'y':
        main_menu()

if __name__ == "__main__":
    main_menu()

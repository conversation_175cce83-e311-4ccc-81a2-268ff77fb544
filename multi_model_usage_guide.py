#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
คู่มือการใช้งาน Multi-Model Architecture สำหรับ LightGBM Trading System
แสดงวิธีการ load และใช้งานโมเดลทั้ง 2 แบบ (trend_following และ counter_trend)

Created: 2025-01-11
Author: AI Assistant
"""

import os
import sys
import pandas as pd
import numpy as np
import joblib
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

# Import จากไฟล์หลัก
try:
    from python_LightGBM_16_Signal import (
        MARKET_SCENARIOS,
        USE_MULTI_MODEL_ARCHITECTURE,
        test_folder,
        load_scenario_models,
        select_appropriate_model,
        predict_with_scenario_model,
        detect_market_scenario,
        get_applicable_scenarios
    )
    print("✅ Import สำเร็จ")
except ImportError as e:
    print(f"❌ Import ล้มเหลว: {e}")
    sys.exit(1)

def check_model_structure():
    """
    ตรวจสอบโครงสร้างไฟล์โมเดลที่มีอยู่
    """
    print("\n" + "="*60)
    print("🔍 ตรวจสอบโครงสร้างไฟล์โมเดล")
    print("="*60)
    
    models_folder = f"{test_folder}/models"
    print(f"📁 Models folder: {models_folder}")
    
    if not os.path.exists(models_folder):
        print(f"❌ ไม่พบโฟลเดอร์ {models_folder}")
        return False
    
    # ตรวจสอบโฟลเดอร์ scenarios
    for scenario_name in MARKET_SCENARIOS.keys():
        scenario_folder = os.path.join(models_folder, scenario_name)
        print(f"\n📂 {scenario_name}:")
        print(f"   Path: {scenario_folder}")
        
        if os.path.exists(scenario_folder):
            files = os.listdir(scenario_folder)
            print(f"   ✅ พบโฟลเดอร์ ({len(files)} ไฟล์)")
            
            # แสดงไฟล์ที่มี
            for file in sorted(files):
                if file.endswith('.pkl'):
                    file_path = os.path.join(scenario_folder, file)
                    file_size = os.path.getsize(file_path)
                    print(f"     📄 {file} ({file_size:,} bytes)")
        else:
            print(f"   ❌ ไม่พบโฟลเดอร์")
    
    return True

def demo_load_models():
    """
    สาธิตการโหลดโมเดลทั้ง 2 scenarios
    """
    print("\n" + "="*60)
    print("🚀 สาธิตการโหลดโมเดล Multi-Model Architecture")
    print("="*60)
    
    # ตัวอย่างการโหลดโมเดลสำหรับ AUDUSD H1
    symbol = "AUDUSD"
    timeframe = 60
    
    print(f"🔧 กำลังโหลดโมเดลสำหรับ {symbol} M{timeframe}")
    
    # โหลดโมเดลทั้ง 2 scenarios
    loaded_models = load_scenario_models(symbol, timeframe)
    
    if loaded_models:
        print(f"\n✅ โหลดโมเดลสำเร็จ: {len(loaded_models)} scenarios")
        
        for scenario_name, model_info in loaded_models.items():
            print(f"\n📊 {scenario_name}:")
            print(f"   Model type: {type(model_info['model']).__name__}")
            print(f"   Features: {len(model_info['features'])} features")
            print(f"   Has scaler: {'scaler' in model_info}")
            
            # แสดง features บางส่วน
            features = model_info['features'][:10]  # แสดง 10 features แรก
            print(f"   Sample features: {features}")
            
        return loaded_models
    else:
        print("❌ ไม่สามารถโหลดโมเดลได้")
        return None

def demo_model_selection():
    """
    สาธิตการเลือกโมเดลตามสถานการณ์ตลาด
    """
    print("\n" + "="*60)
    print("🎯 สาธิตการเลือกโมเดลตามสถานการณ์ตลาด")
    print("="*60)
    
    # โหลดโมเดล
    symbol = "AUDUSD"
    timeframe = 60
    loaded_models = load_scenario_models(symbol, timeframe)
    
    if not loaded_models:
        print("❌ ไม่สามารถโหลดโมเดลได้ - ข้ามการทดสอบ")
        return
    
    # สร้างข้อมูลตัวอย่างสำหรับทดสอบ
    test_cases = [
        {
            'name': 'Uptrend - Buy Signal',
            'Close': 0.6800,
            'High': 0.6810,
            'Low': 0.6790,
            'EMA200': 0.6750,  # ราคาอยู่เหนือ EMA200
            'action': 'buy'
        },
        {
            'name': 'Uptrend - Sell Signal (Counter-trend)',
            'Close': 0.6800,
            'High': 0.6810,
            'Low': 0.6790,
            'EMA200': 0.6750,  # ราคาอยู่เหนือ EMA200
            'action': 'sell'
        },
        {
            'name': 'Downtrend - Sell Signal',
            'Close': 0.6700,
            'High': 0.6710,
            'Low': 0.6690,
            'EMA200': 0.6750,  # ราคาอยู่ใต้ EMA200
            'action': 'sell'
        },
        {
            'name': 'Downtrend - Buy Signal (Counter-trend)',
            'Close': 0.6700,
            'High': 0.6710,
            'Low': 0.6690,
            'EMA200': 0.6750,  # ราคาอยู่ใต้ EMA200
            'action': 'buy'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test Case {i}: {test_case['name']}")
        
        # สร้าง pandas Series
        row_data = {k: v for k, v in test_case.items() if k not in ['name', 'action']}
        row = pd.Series(row_data)
        action = test_case['action']
        
        # ตรวจจับสถานการณ์ตลาด
        market_condition = detect_market_scenario(row)
        applicable_scenarios = get_applicable_scenarios(market_condition, action)
        
        print(f"   📈 Market Condition: {market_condition}")
        print(f"   🎯 Action: {action}")
        print(f"   📋 Applicable Scenarios: {applicable_scenarios}")
        
        # เลือกโมเดล
        selected_model = select_appropriate_model(row, action, loaded_models)
        
        if selected_model:
            # หาชื่อ scenario ที่ถูกเลือก
            selected_scenario = None
            for scenario_name, model_info in loaded_models.items():
                if model_info == selected_model:
                    selected_scenario = scenario_name
                    break
            
            print(f"   ✅ Selected Model: {selected_scenario}")
            print(f"   📊 Features: {len(selected_model['features'])} features")
        else:
            print(f"   ❌ No suitable model found")

def demo_prediction():
    """
    สาธิตการทำนายด้วย Multi-Model
    """
    print("\n" + "="*60)
    print("🔮 สาธิตการทำนายด้วย Multi-Model")
    print("="*60)
    
    # โหลดโมเดล
    symbol = "AUDUSD"
    timeframe = 60
    loaded_models = load_scenario_models(symbol, timeframe)
    
    if not loaded_models:
        print("❌ ไม่สามารถโหลดโมเดลได้ - ข้ามการทดสอบ")
        return
    
    # สร้างข้อมูลตัวอย่างที่มี features ครบ
    # (ในการใช้งานจริง ข้อมูลนี้จะมาจาก DataFrame ที่มี indicators ครบ)
    
    # ดูว่าโมเดลต้องการ features อะไรบ้าง
    first_model = list(loaded_models.values())[0]
    required_features = first_model['features']
    
    print(f"📋 Required features ({len(required_features)}):")
    for i, feature in enumerate(required_features[:10]):  # แสดง 10 features แรก
        print(f"   {i+1}. {feature}")
    if len(required_features) > 10:
        print(f"   ... และอีก {len(required_features) - 10} features")
    
    print(f"\n⚠️ สำหรับการทำนายจริง ต้องมีข้อมูล features ครบทั้ง {len(required_features)} features")
    print("💡 ข้อมูลเหล่านี้จะมาจากการคำนวณ technical indicators จาก DataFrame")

def main():
    """
    ฟังก์ชันหลักสำหรับรันการสาธิต
    """
    print("🚀 คู่มือการใช้งาน Multi-Model Architecture")
    print("="*60)
    print(f"📊 USE_MULTI_MODEL_ARCHITECTURE: {USE_MULTI_MODEL_ARCHITECTURE}")
    print(f"📁 Test folder: {test_folder}")
    print(f"🎯 Market scenarios: {list(MARKET_SCENARIOS.keys())}")
    
    # 1. ตรวจสอบโครงสร้างไฟล์
    if not check_model_structure():
        print("\n❌ ไม่พบโครงสร้างไฟล์โมเดลที่ถูกต้อง")
        print("💡 กรุณาเทรนโมเดลก่อนด้วย python_LightGBM_16_Signal.py")
        return
    
    # 2. สาธิตการโหลดโมเดล
    loaded_models = demo_load_models()
    
    if loaded_models:
        # 3. สาธิตการเลือกโมเดล
        demo_model_selection()
        
        # 4. สาธิตการทำนาย
        demo_prediction()
    
    print("\n" + "="*60)
    print("✅ การสาธิตเสร็จสิ้น")
    print("="*60)
    
    print("\n📝 สรุปขั้นตอนการใช้งาน:")
    print("1. เทรนโมเดลด้วย USE_MULTI_MODEL_ARCHITECTURE = True")
    print("2. โหลดโมเดลด้วย load_scenario_models(symbol, timeframe)")
    print("3. เลือกโมเดลด้วย select_appropriate_model(row, action, loaded_models)")
    print("4. ทำนายด้วย predict_with_scenario_model(row, action, loaded_models)")
    
    print("\n⚠️ สำคัญ:")
    print("- ต้องโหลดทั้ง 2 โมเดล (trend_following และ counter_trend)")
    print("- แต่ละโมเดลมี trained.pkl, features.pkl, และ scaler.pkl")
    print("- ใช้ scaler เดียวกันที่ใช้ในการเทรนสำหรับการทำนาย")
    print("- เลือกโมเดลตามสถานการณ์ตลาดและประเภทการเทรด")

if __name__ == "__main__":
    main()

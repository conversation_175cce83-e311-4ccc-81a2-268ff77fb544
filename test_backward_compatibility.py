#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข Backward Compatibility สำหรับการโหลดไฟล์

Created: 2025-01-11
Author: AI Assistant
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
import json
from pathlib import Path

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_file_loading_compatibility():
    """
    ทดสอบการโหลดไฟล์แบบ Backward Compatibility
    """
    print("🔍 ทดสอบการโหลดไฟล์แบบ Backward Compatibility")
    print("="*60)
    
    try:
        from python_LightGBM_16_Signal import (
            load_optimal_threshold_compatible,
            load_optimal_nbars_compatible,
            load_optimal_threshold,
            load_optimal_nbars
        )
        
        symbol = "GOLD"
        timeframe = 60
        
        print(f"📊 ทดสอบกับ {symbol} M{timeframe}")
        
        # ทดสอบการโหลด threshold
        print(f"\n🔸 ทดสอบการโหลด threshold:")
        
        # 1. ทดสอบฟังก์ชันใหม่แบบไม่ระบุ scenario
        print(f"\n1. ฟังก์ชันใหม่ (ไม่ระบุ scenario):")
        threshold_new_no_scenario = load_optimal_threshold_compatible(symbol, timeframe)
        print(f"   Result: {threshold_new_no_scenario}")
        
        # 2. ทดสอบฟังก์ชันใหม่แบบระบุ scenario
        print(f"\n2. ฟังก์ชันใหม่ (ระบุ scenario):")
        for scenario in ["trend_following", "counter_trend"]:
            threshold_new_scenario = load_optimal_threshold_compatible(symbol, timeframe, scenario)
            print(f"   {scenario}: {threshold_new_scenario}")
        
        # 3. ทดสอบฟังก์ชันเดิม (ที่ถูกแก้ไขแล้ว)
        print(f"\n3. ฟังก์ชันเดิม (แก้ไขแล้ว):")
        threshold_old = load_optimal_threshold(symbol, timeframe)
        print(f"   Result: {threshold_old}")
        
        # ทดสอบการโหลด nBars_SL
        print(f"\n🔸 ทดสอบการโหลด nBars_SL:")
        
        # 1. ทดสอบฟังก์ชันใหม่แบบไม่ระบุ scenario
        print(f"\n1. ฟังก์ชันใหม่ (ไม่ระบุ scenario):")
        nbars_new_no_scenario = load_optimal_nbars_compatible(symbol, timeframe)
        print(f"   Result: {nbars_new_no_scenario}")
        
        # 2. ทดสอบฟังก์ชันใหม่แบบระบุ scenario
        print(f"\n2. ฟังก์ชันใหม่ (ระบุ scenario):")
        for scenario in ["trend_following", "counter_trend"]:
            nbars_new_scenario = load_optimal_nbars_compatible(symbol, timeframe, scenario)
            print(f"   {scenario}: {nbars_new_scenario}")
        
        # 3. ทดสอบฟังก์ชันเดิม (ที่ถูกแก้ไขแล้ว)
        print(f"\n3. ฟังก์ชันเดิม (แก้ไขแล้ว):")
        nbars_old = load_optimal_nbars(symbol, timeframe)
        print(f"   Result: {nbars_old}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_get_optimal_parameters():
    """
    ทดสอบฟังก์ชัน get_optimal_parameters ที่ใช้ฟังก์ชันใหม่
    """
    print("\n🎯 ทดสอบฟังก์ชัน get_optimal_parameters")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import get_optimal_parameters
        
        symbol = "GOLD"
        timeframe = 60
        
        # จำลองข้อมูลตลาด
        market_data = {
            'Close': 3300.0,
            'EMA200': 3250.0,
            'High': 3310.0,
            'Low': 3290.0
        }
        
        # ทดสอบกับ action ต่างๆ
        actions = ["buy", "sell"]
        
        for action in actions:
            print(f"\n🔸 ทดสอบ action: {action}")
            
            try:
                result = get_optimal_parameters(symbol, timeframe, market_data, action)
                
                print(f"   ✅ สำเร็จ:")
                print(f"     Scenario: {result.get('scenario')}")
                print(f"     Threshold: {result.get('threshold')}")
                print(f"     nBars_SL: {result.get('nBars_SL')}")
                print(f"     Market condition: {result.get('market_condition')}")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_predict_with_optimal_parameters():
    """
    ทดสอบฟังก์ชัน predict_with_optimal_parameters
    """
    print("\n🚀 ทดสอบฟังก์ชัน predict_with_optimal_parameters")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import predict_with_optimal_parameters
        
        symbol = "GOLD"
        timeframe = 60
        
        # จำลองข้อมูลตลาด
        market_data = {
            'Close': 3300.0,
            'EMA200': 3250.0,
            'High': 3310.0,
            'Low': 3290.0,
            'RSI14': 45.0,
            'MACD_12_26_9': 2.5,
            'Volume': 8000
        }
        
        # ทดสอบกับ action ต่างๆ
        actions = ["buy", "sell"]
        
        for action in actions:
            print(f"\n🔸 ทดสอบ action: {action}")
            
            try:
                result = predict_with_optimal_parameters(symbol, timeframe, market_data, action)
                
                if result.get('success'):
                    print(f"   ✅ สำเร็จ:")
                    print(f"     Prediction: {result.get('prediction')}")
                    print(f"     Confidence: {result.get('confidence'):.4f}")
                    print(f"     Scenario: {result.get('scenario')}")
                    print(f"     Threshold: {result.get('threshold')}")
                    print(f"     nBars_SL: {result.get('nBars_SL')}")
                else:
                    print(f"   ⚠️ ไม่สำเร็จ: {result.get('error')}")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def check_existing_files_status():
    """
    ตรวจสอบสถานะไฟล์ที่มีอยู่
    """
    print("\n📁 ตรวจสอบสถานะไฟล์ที่มีอยู่")
    print("="*50)
    
    thresholds_dir = Path("LightGBM_Multi/thresholds")
    
    if not thresholds_dir.exists():
        print(f"❌ ไม่พบโฟลเดอร์: {thresholds_dir}")
        return False
    
    files = list(thresholds_dir.glob("*.pkl"))
    
    print(f"📊 จำนวนไฟล์ทั้งหมด: {len(files)} ไฟล์")
    
    # จำแนกประเภทไฟล์
    file_types = {
        'threshold_new': [],
        'nbars_new': [],
        'threshold_old': [],
        'nbars_old': [],
        'time_filters': []
    }
    
    for file in files:
        file_name = file.name
        
        if 'optimal_threshold' in file_name:
            if file_name.startswith(('060_', '030_')):
                file_types['threshold_new'].append(file_name)
            else:
                file_types['threshold_old'].append(file_name)
        elif 'optimal_nBars_SL' in file_name:
            if file_name.startswith(('060_', '030_')):
                file_types['nbars_new'].append(file_name)
            else:
                file_types['nbars_old'].append(file_name)
        elif 'time_filters' in file_name:
            file_types['time_filters'].append(file_name)
    
    print(f"\n📋 การจำแนกไฟล์:")
    for file_type, file_list in file_types.items():
        count = len(file_list)
        status = "✅" if count > 0 else "❌"
        print(f"   {status} {file_type}: {count} ไฟล์")
        if count > 0 and count <= 3:
            for file_name in file_list:
                print(f"     - {file_name}")
    
    return len(files) > 0

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🚀 ทดสอบการแก้ไข Backward Compatibility")
    print("="*80)
    
    # Test 1: ตรวจสอบไฟล์ที่มีอยู่
    files_exist = check_existing_files_status()
    
    # Test 2: ทดสอบการโหลดไฟล์
    loading_success = test_file_loading_compatibility()
    
    # Test 3: ทดสอบ get_optimal_parameters
    get_params_success = test_get_optimal_parameters()
    
    # Test 4: ทดสอบ predict_with_optimal_parameters
    predict_success = test_predict_with_optimal_parameters()
    
    print("\n" + "="*80)
    print("📊 สรุปผลการทดสอบ")
    print("="*80)
    
    print(f"✅ Test 1 - Files Exist: {'ผ่าน' if files_exist else 'ล้มเหลว'}")
    print(f"✅ Test 2 - File Loading: {'ผ่าน' if loading_success else 'ล้มเหลว'}")
    print(f"✅ Test 3 - Get Optimal Parameters: {'ผ่าน' if get_params_success else 'ล้มเหลว'}")
    print(f"✅ Test 4 - Predict Function: {'ผ่าน' if predict_success else 'ล้มเหลว'}")
    
    overall_success = all([files_exist, loading_success, get_params_success, predict_success])
    
    if overall_success:
        print(f"\n🎉 การแก้ไข Backward Compatibility สำเร็จ!")
        print(f"🚀 ระบบสามารถโหลดไฟล์ทั้งรูปแบบเดิมและใหม่ได้")
        print(f"✅ ฟังก์ชันเดิมทำงานได้ปกติ")
        print(f"✅ ฟังก์ชันใหม่รองรับ Multi-Model Architecture")
    else:
        print(f"\n⚠️ ยังมีปัญหาบางส่วนที่ต้องแก้ไข")
        
        if not files_exist:
            print(f"   - ไม่พบไฟล์ optimization")
        if not loading_success:
            print(f"   - การโหลดไฟล์มีปัญหา")
        if not get_params_success:
            print(f"   - get_optimal_parameters มีปัญหา")
        if not predict_success:
            print(f"   - predict_with_optimal_parameters มีปัญหา")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
ไฟล์ทดสอบความสอดคล้องระหว่าง python_LightGBM_15_Tuning.py และ python_to_mt5_WebRequest_server_11_Tuning.py
ตรวจสอบการคำนวณ Features, Technical Indicators, และ Model Prediction
"""

import pandas as pd
import numpy as np
import pandas_ta as ta
from sklearn.preprocessing import StandardScaler
import joblib
import warnings
warnings.filterwarnings('ignore')

def calculate_rsi_custom(close_prices, period=14):
    """คำนวณ RSI แบบเดียวกับ training model"""
    delta = close_prices.diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_macd_custom(close_prices, fast=12, slow=26, signal=9):
    """คำนวณ MACD แบบเดียวกับ training model"""
    ema_fast = close_prices.ewm(span=fast).mean()
    ema_slow = close_prices.ewm(span=slow).mean()
    macd_line = ema_fast - ema_slow
    macd_signal = macd_line.ewm(span=signal).mean()
    macd_hist = macd_line - macd_signal
    return macd_line, macd_signal, macd_hist

def calculate_bollinger_bands(close_prices, period=20, std_dev=2):
    """คำนวณ Bollinger Bands แบบเดียวกับ training model"""
    sma = close_prices.rolling(window=period).mean()
    std = close_prices.rolling(window=period).std()
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    bb_width = upper_band - lower_band
    return upper_band, lower_band, bb_width

def calculate_stochastic(high_prices, low_prices, close_prices, k_period=14, d_period=3):
    """คำนวณ Stochastic แบบเดียวกับ training model"""
    lowest_low = low_prices.rolling(window=k_period).min()
    highest_high = high_prices.rolling(window=k_period).max()
    k_percent = 100 * ((close_prices - lowest_low) / (highest_high - lowest_low))
    d_percent = k_percent.rolling(window=d_period).mean()
    return k_percent, d_percent

def calculate_adx(high_prices, low_prices, close_prices, period=14):
    """คำนวณ ADX แบบเดียวกับ training model"""
    # ใช้ pandas_ta สำหรับ ADX
    adx_data = ta.adx(high_prices, low_prices, close_prices, length=period)
    return adx_data

def calculate_atr(high_prices, low_prices, close_prices, period=14):
    """คำนวณ ATR แบบเดียวกับ training model"""
    tr1 = high_prices - low_prices
    tr2 = abs(high_prices - close_prices.shift(1))
    tr3 = abs(low_prices - close_prices.shift(1))
    true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    atr = true_range.rolling(window=period).mean()
    return atr

def test_feature_consistency(csv_file_path, symbol, timeframe):
    """
    ทดสอบความสอดคล้องของการคำนวณ Features
    """
    print(f"\n=== Testing Feature Consistency for {symbol} {timeframe} ===")
    
    # โหลดข้อมูล
    try:
        df = pd.read_csv(csv_file_path, sep='\t')
        df['DateTime'] = pd.to_datetime(df['DateTime'])
        df.set_index('DateTime', inplace=True)
        print(f"✅ Loaded data: {len(df)} rows")
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return False
    
    # ทดสอบการคำนวณ Technical Indicators
    print("\n--- Testing Technical Indicators ---")
    
    # RSI
    rsi_custom = calculate_rsi_custom(df['Close'])
    rsi_ta = ta.rsi(df['Close'], length=14)
    rsi_diff = abs(rsi_custom - rsi_ta).dropna()
    print(f"RSI difference (max): {rsi_diff.max():.6f}")
    
    # MACD
    macd_line, macd_signal, macd_hist = calculate_macd_custom(df['Close'])
    macd_ta = ta.macd(df['Close'])
    macd_diff = abs(macd_line - macd_ta['MACD_12_26_9']).dropna()
    print(f"MACD difference (max): {macd_diff.max():.6f}")
    
    # Bollinger Bands
    bb_upper, bb_lower, bb_width = calculate_bollinger_bands(df['Close'])
    bb_ta = ta.bbands(df['Close'])
    bb_diff = abs(bb_width - (bb_ta['BBU_20_2.0'] - bb_ta['BBL_20_2.0'])).dropna()
    print(f"BB Width difference (max): {bb_diff.max():.6f}")
    
    # Stochastic
    stoch_k, stoch_d = calculate_stochastic(df['High'], df['Low'], df['Close'])
    stoch_ta = ta.stoch(df['High'], df['Low'], df['Close'])
    stoch_diff = abs(stoch_k - stoch_ta['STOCHk_14_3_3']).dropna()
    print(f"Stochastic K difference (max): {stoch_diff.max():.6f}")
    
    # ATR
    atr_custom = calculate_atr(df['High'], df['Low'], df['Close'])
    atr_ta = ta.atr(df['High'], df['Low'], df['Close'], length=14)
    atr_diff = abs(atr_custom - atr_ta).dropna()
    print(f"ATR difference (max): {atr_diff.max():.6f}")
    
    # ทดสอบ Interaction Features
    print("\n--- Testing Interaction Features ---")
    
    # คำนวณ features พื้นฐาน
    df['RSI14'] = rsi_custom
    df['Volume_Spike'] = (df['Volume'] > df['Volume'].rolling(20).mean() * 1.5).astype(int)
    df['EMA50'] = df['Close'].ewm(span=50).mean()
    df['EMA_diff'] = df['Close'] - df['EMA50']
    df['BB_width'] = bb_width
    df['Price_Move'] = df['Close'] - df['Open']
    df['Rolling_Vol_5'] = df['Close'].rolling(5).std()
    
    # คำนวณ Interaction Features
    interaction_features = {
        'RSI_x_VolumeSpike': df['RSI14'] * df['Volume_Spike'],
        'EMA_diff_x_ATR': df['EMA_diff'] * atr_custom,
        'Momentum5_x_Volatility10': (df['Close'] - df['Close'].shift(5)) * df['Close'].rolling(10).std(),
        'RSI14_x_BBwidth': df['RSI14'] * df['BB_width'],
        'RSI14_x_ATR': df['RSI14'] * atr_custom,
        'RSI14_x_PriceMove': df['RSI14'] * df['Price_Move'],
        'EMA50_x_RollingVol5': df['EMA50'] * df['Rolling_Vol_5'],
        'EMA_diff_x_BBwidth': df['EMA_diff'] * df['BB_width']
    }
    
    for feature_name, feature_values in interaction_features.items():
        non_null_count = feature_values.dropna().shape[0]
        print(f"{feature_name}: {non_null_count} non-null values")
    
    print("\n--- Feature Consistency Test Results ---")
    
    # ตรวจสอบว่า features มีค่าที่สมเหตุสมผล
    consistency_checks = {
        'RSI in range [0,100]': (rsi_custom >= 0).all() and (rsi_custom <= 100).all(),
        'MACD values reasonable': abs(macd_line).max() < df['Close'].std() * 2,
        'BB Width positive': (bb_width >= 0).all(),
        'Stochastic in range [0,100]': (stoch_k >= 0).all() and (stoch_k <= 100).all(),
        'ATR positive': (atr_custom >= 0).all()
    }
    
    for check_name, result in consistency_checks.items():
        status = "✅" if result else "❌"
        print(f"{status} {check_name}: {result}")
    
    return all(consistency_checks.values())

def test_model_prediction_consistency():
    """
    ทดสอบความสอดคล้องของ Model Prediction
    """
    print(f"\n=== Testing Model Prediction Consistency ===")
    
    # ทดสอบการโหลด model
    try:
        model_path = "best_lightgbm_model_EURUSD_M30.pkl"
        model = joblib.load(model_path)
        print(f"✅ Model loaded successfully")
        
        # ตรวจสอบ model type
        model_type = type(model).__name__
        print(f"Model type: {model_type}")
        
        # ตรวจสอบ number of classes
        if hasattr(model, 'n_classes_'):
            print(f"Number of classes: {model.n_classes_}")
        elif hasattr(model, 'classes_'):
            print(f"Classes: {model.classes_}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Feature Consistency Testing Tool")
    print("=" * 50)
    
    # ทดสอบ features
    test_symbols = ["EURUSD", "GBPUSD"]
    test_timeframes = ["M30", "M60"]
    
    for symbol in test_symbols:
        for timeframe in test_timeframes:
            csv_file = f"Processed_{symbol}_{timeframe}_with_targets.csv"
            try:
                result = test_feature_consistency(csv_file, symbol, timeframe)
                if result:
                    print(f"✅ {symbol} {timeframe}: All consistency checks passed")
                else:
                    print(f"❌ {symbol} {timeframe}: Some consistency checks failed")
            except Exception as e:
                print(f"❌ {symbol} {timeframe}: Error during testing - {e}")
    
    # ทดสอบ model
    model_result = test_model_prediction_consistency()
    
    print("\n" + "=" * 50)
    print("🏁 Testing completed!")

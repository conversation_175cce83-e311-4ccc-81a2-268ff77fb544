#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug ปัญหา Sell signals แบบละเอียด
"""

import os
import sys
import pandas as pd
import numpy as np

# เพิ่ม path สำหรับ import
sys.path.append('.')

def run_quick_test():
    """รันการทดสอบแบบเร็วเพื่อดู debug messages"""
    
    print("🧪 รันการทดสอบแบบเร็วเพื่อดู Debug Messages")
    print("="*70)
    
    try:
        from python_LightGBM_16_Signal import (
            test_groups,
            NUM_TRAINING_ROUNDS,
            main
        )
        
        # ตั้งค่าให้รันแค่ 1 รอบและไฟล์เดียว
        print(f"📊 การตั้งค่าปัจจุบัน:")
        print(f"   NUM_TRAINING_ROUNDS: {NUM_TRAINING_ROUNDS}")
        print(f"   test_groups: {list(test_groups.keys())}")
        
        # เลือกไฟล์แรกจากกลุ่ม M30
        if 'M30' in test_groups and test_groups['M30']:
            test_file = test_groups['M30'][0]
            print(f"   ไฟล์ทดสอบ: {test_file}")
            
            if os.path.exists(test_file):
                print(f"\n🚀 รันการทดสอบ...")
                print(f"⏰ เวลาเริ่ม: {pd.Timestamp.now()}")
                
                # รันแค่ไฟล์เดียว
                try:
                    result = main(
                        run_identifier=1,
                        group_name="M30_DEBUG",
                        input_files=[test_file]
                    )
                    
                    if result:
                        print(f"✅ การทดสอบเสร็จสิ้น")
                        print(f"📊 ผลลัพธ์: {list(result.keys()) if isinstance(result, dict) else type(result)}")
                    else:
                        print(f"❌ การทดสอบล้มเหลว")
                        
                except Exception as e:
                    print(f"❌ เกิดข้อผิดพลาดในการรัน: {e}")
                    import traceback
                    traceback.print_exc()
                    
            else:
                print(f"❌ ไม่พบไฟล์ทดสอบ: {test_file}")
        else:
            print(f"❌ ไม่พบกลุ่ม M30 หรือไม่มีไฟล์")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def check_parameters():
    """ตรวจสอบพารามิเตอร์ที่เกี่ยวข้องกับ Sell signals"""
    
    print(f"\n🔍 ตรวจสอบพารามิเตอร์ที่เกี่ยวข้องกับ Sell Signals")
    print("="*70)
    
    try:
        from python_LightGBM_16_Signal import (
            input_rsi_level_in,
            input_pull_back,
            take_profit_stop_loss_ratio,
            USE_TIME_FILTER,
            time_filter_config
        )
        
        print(f"📊 พารามิเตอร์สำคัญ:")
        print(f"   RSI Level: {input_rsi_level_in} (RSI > {100-input_rsi_level_in} for sell)")
        print(f"   Pull Back: {input_pull_back}")
        print(f"   Take Profit Ratio: {take_profit_stop_loss_ratio} (ratio > {take_profit_stop_loss_ratio*3.0} for sell)")
        print(f"   USE_TIME_FILTER: {USE_TIME_FILTER}")
        
        if USE_TIME_FILTER:
            print(f"   Time Filter Config: {time_filter_config}")
        
        # คำนวณเกณฑ์ที่ต้องผ่าน
        rsi_threshold = 100 - input_rsi_level_in
        ratio_threshold = take_profit_stop_loss_ratio * 3.0
        
        print(f"\n🎯 เกณฑ์ที่ต้องผ่านสำหรับ SELL:")
        print(f"   1. Close < Open (Bearish candle)")
        print(f"   2. RSI > {rsi_threshold}")
        print(f"   3. MACD Signal = -1.0 (Bearish)")
        print(f"   4. Volume > Volume_MA20 * 0.8")
        print(f"   5. PullBack_Sell > {input_pull_back}")
        print(f"   6. Ratio_Sell > {ratio_threshold}")
        print(f"   7. Time Condition = True")
        
        # ประเมินความยากของเงื่อนไข
        print(f"\n💡 การประเมินความยากของเงื่อนไข:")
        if rsi_threshold >= 70:
            print(f"   ⚠️ RSI > {rsi_threshold} ค่อนข้างยาก (ต้อง overbought มาก)")
        elif rsi_threshold >= 65:
            print(f"   ✅ RSI > {rsi_threshold} เหมาะสม")
        else:
            print(f"   ✅ RSI > {rsi_threshold} ง่าย")
            
        if ratio_threshold >= 10:
            print(f"   ⚠️ Ratio > {ratio_threshold} ค่อนข้างยาก")
        elif ratio_threshold >= 7:
            print(f"   ✅ Ratio > {ratio_threshold} เหมาะสม")
        else:
            print(f"   ✅ Ratio > {ratio_threshold} ง่าย")
            
        if input_pull_back >= 0.5:
            print(f"   ⚠️ PullBack > {input_pull_back} ค่อนข้างยาก")
        elif input_pull_back >= 0.4:
            print(f"   ✅ PullBack > {input_pull_back} เหมาะสม")
        else:
            print(f"   ✅ PullBack > {input_pull_back} ง่าย")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")

def check_data_sample():
    """ตรวจสอบตัวอย่างข้อมูลว่ามีค่าที่เป็นไปได้สำหรับ Sell หรือไม่"""
    
    print(f"\n📊 ตรวจสอบตัวอย่างข้อมูลสำหรับ Sell Signals")
    print("="*70)
    
    try:
        from python_LightGBM_16_Signal import test_groups
        
        if 'M30' in test_groups and test_groups['M30']:
            test_file = test_groups['M30'][0]
            
            if os.path.exists(test_file):
                print(f"📁 วิเคราะห์ไฟล์: {test_file}")
                
                # อ่านข้อมูล
                df = None
                for sep_name, sep in [('comma', ','), ('tab', '\t')]:
                    try:
                        df_temp = pd.read_csv(test_file, sep=sep)
                        if len(df_temp.columns) > 1:
                            df = df_temp
                            print(f"   ✅ อ่านด้วย {sep_name}: {df.shape}")
                            break
                    except:
                        continue
                
                if df is not None:
                    print(f"   📊 คอลัมน์: {list(df.columns)}")
                    
                    # ตรวจสอบข้อมูลที่เกี่ยวข้องกับ Sell
                    if len(df) > 100:
                        sample = df.tail(100)  # ดูข้อมูล 100 แถวสุดท้าย
                        
                        # ตรวจสอบ Bearish candles
                        if 'Open' in df.columns and 'Close' in df.columns:
                            bearish_candles = sample[sample['Close'] < sample['Open']]
                            bearish_pct = len(bearish_candles) / len(sample) * 100
                            print(f"   📉 Bearish candles: {len(bearish_candles)}/{len(sample)} ({bearish_pct:.1f}%)")
                        
                        # ตรวจสอบ RSI ถ้ามี
                        if 'RSI' in df.columns:
                            high_rsi = sample[sample['RSI'] > 65]
                            high_rsi_pct = len(high_rsi) / len(sample) * 100
                            print(f"   📊 High RSI (>65): {len(high_rsi)}/{len(sample)} ({high_rsi_pct:.1f}%)")
                            print(f"   📊 RSI range: {sample['RSI'].min():.1f} - {sample['RSI'].max():.1f}")
                        
                        # ตรวจสอบ Volume ถ้ามี
                        if 'Volume' in df.columns:
                            print(f"   📊 Volume range: {sample['Volume'].min():.0f} - {sample['Volume'].max():.0f}")
                        
                        print(f"   📄 ตัวอย่างข้อมูล 3 แถวสุดท้าย:")
                        print(sample[['Date', 'Time', 'Open', 'High', 'Low', 'Close']].tail(3).to_string())
                    
                else:
                    print(f"   ❌ ไม่สามารถอ่านไฟล์ได้")
            else:
                print(f"❌ ไม่พบไฟล์: {test_file}")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🏗️ Debug Sell Signals - Detailed Analysis")
    print("="*70)
    
    # ตรวจสอบพารามิเตอร์
    check_parameters()
    
    # ตรวจสอบตัวอย่างข้อมูล
    check_data_sample()
    
    # รันการทดสอบแบบเร็ว
    print(f"\n" + "="*70)
    print("🚨 กำลังรันการทดสอบ - ดู Debug Messages ด้านล่าง:")
    print("="*70)
    
    run_quick_test()
    
    print(f"\n✅ การ Debug เสร็จสิ้น")
    print(f"\n📋 สิ่งที่ต้องดูใน Debug Messages:")
    print(f"   🔍 'SELL Conditions Debug:' - ดูว่าเงื่อนไขไหนไม่ผ่าน")
    print(f"   🔍 'Time Condition:' - ดูว่า time filter ผ่านหรือไม่")
    print(f"   🔍 'Final SELL Check:' - ดูผลลัพธ์สุดท้าย")
    print(f"   🎯 'SELL Technical Signal:' - ดูว่ามี sell signals หรือไม่")

if __name__ == "__main__":
    main()

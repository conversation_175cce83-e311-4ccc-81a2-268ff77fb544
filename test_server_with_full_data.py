#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def create_realistic_test_data():
    """สร้างข้อมูลทดสอบที่สมจริงสำหรับ GOLD M30"""
    
    # สร้างข้อมูล 210 แท่ง (มากกว่า 205 ที่ต้องการ)
    num_bars = 210
    
    # เริ่มจากเวลาปัจจุบัน ย้อนหลัง
    end_time = datetime.now()
    start_time = end_time - timedelta(minutes=30 * num_bars)
    
    bars = []
    current_price = 2650.0  # ราคาเริ่มต้น GOLD
    
    for i in range(num_bars):
        bar_time = start_time + timedelta(minutes=30 * i)
        timestamp = int(bar_time.timestamp())
        
        # สร้างการเคลื่อนไหวราคาแบบสุ่มที่สมจริง
        price_change = np.random.normal(0, 2.0)  # ความผันผวนปกติ
        
        open_price = current_price
        high_price = open_price + abs(np.random.normal(2, 1))
        low_price = open_price - abs(np.random.normal(2, 1))
        close_price = open_price + price_change
        
        # ปรับให้ high/low สมเหตุสมผล
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        volume = int(np.random.normal(1000, 200))
        
        bar = {
            "time": timestamp,
            "open": round(open_price, 2),
            "high": round(high_price, 2),
            "low": round(low_price, 2),
            "close": round(close_price, 2),
            "volume": volume,
            "tick_volume": volume,
            "spread": 5,
            "real_volume": volume
        }
        
        bars.append(bar)
        current_price = close_price
    
    return bars

def test_server_with_full_data():
    """ทดสอบ server ด้วยข้อมูลเต็มรูปแบบ"""
    
    url = 'http://127.0.0.1:54321/data'
    
    print("🔍 สร้างข้อมูลทดสอบ...")
    bars = create_realistic_test_data()
    print(f"✅ สร้างข้อมูล {len(bars)} แท่งสำเร็จ")
    
    test_data = {
        "symbol": "GOLD",
        "timeframe_str": "PERIOD_M30",
        "bars": bars
    }
    
    try:
        print("🚀 ส่งข้อมูลไป server...")
        print(f"URL: {url}")
        print(f"ข้อมูล: {len(bars)} แท่ง")
        print(f"ช่วงเวลา: {bars[0]['time']} ถึง {bars[-1]['time']}")
        
        # ส่งข้อมูลไป server
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"✅ Server ตอบกลับ: HTTP {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("\n📊 ผลลัพธ์จาก server:")
                print(f"  Signal: {result.get('signal', 'N/A')}")
                print(f"  Class: {result.get('class', 'N/A')}")
                print(f"  Confidence: {result.get('confidence', 'N/A')}")
                print(f"  Symbol: {result.get('symbol', 'N/A')}")
                print(f"  Timeframe: {result.get('timeframe_str', 'N/A')}")
                print(f"  Entry Price: {result.get('entry_price', 'N/A')}")
                print(f"  SL Price: {result.get('sl_price', 'N/A')}")
                print(f"  TP Price: {result.get('tp_price', 'N/A')}")
                print(f"  Threshold: {result.get('threshold', 'N/A')}")
                print(f"  nBars_SL: {result.get('nBars_SL', 'N/A')}")
                print(f"  Time Filters: {result.get('time_filters', 'N/A')}")
                print(f"  Best Entry: {result.get('best_entry', 'N/A')}")
                print(f"  Spread: {result.get('spread', 'N/A')}")
                
                # วิเคราะห์ผลลัพธ์
                signal = result.get('signal', 'HOLD')
                confidence = result.get('confidence', 0.0)
                
                print(f"\n🎯 การวิเคราะห์:")
                if signal == 'HOLD':
                    print("  📊 โมเดลแนะนำให้รอ (HOLD)")
                    if confidence == 0.0:
                        print("  ⚠️  Confidence = 0 อาจหมายถึง:")
                        print("     - ข้อมูลไม่เพียงพอสำหรับการทำนาย")
                        print("     - โมเดลไม่มั่นใจในการตัดสินใจ")
                        print("     - ไม่อยู่ในช่วงเวลาที่เหมาะสมสำหรับเทรด")
                elif signal in ['BUY', 'SELL']:
                    print(f"  🎯 โมเดลแนะนำ {signal} ด้วย confidence {confidence}")
                    print(f"  💰 Entry: {result.get('entry_price', 'N/A')}")
                    print(f"  🛡️  SL: {result.get('sl_price', 'N/A')}")
                    print(f"  🎯 TP: {result.get('tp_price', 'N/A')}")
                
                return True
                
            except json.JSONDecodeError:
                print(f"❌ ไม่สามารถแปลง JSON ได้: {response.text[:200]}...")
                return False
        else:
            print(f"❌ Server ตอบกลับด้วย error: {response.text[:200]}...")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ ไม่สามารถเชื่อมต่อ server ได้")
        return False
    except requests.exceptions.Timeout:
        print("❌ Server ไม่ตอบกลับภายในเวลาที่กำหนด")
        return False
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

if __name__ == "__main__":
    print("🚀 เริ่มทดสอบ server ด้วยข้อมูลเต็มรูปแบบ...")
    
    success = test_server_with_full_data()
    
    if success:
        print("\n✅ การทดสอบสำเร็จ - server ทำงานปกติ")
    else:
        print("\n❌ การทดสอบล้มเหลว - server มีปัญหา")

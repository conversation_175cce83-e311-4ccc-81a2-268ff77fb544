#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขแบบย่อ - เรียกใช้ฟังก์ชันที่แก้ไขแล้ว
"""

import os
import sys

# เพิ่ม path เพื่อ import ฟังก์ชันจาก main file
sys.path.append('.')

def test_symbol_extraction_fixed():
    """
    ทดสอบฟังก์ชันดึงสัญลักษณ์ที่แก้ไขแล้ว
    """
    print("🧪 ทดสอบการดึงสัญลักษณ์ที่แก้ไขแล้ว")
    print("=" * 50)
    
    # จำลอง test_groups
    test_groups = {
        "M30": [
            "CSV_Files_Fixed/AUDUSD_M30_FIXED.csv",
            "CSV_Files_Fixed/EURGBP_M30_FIXED.csv",
            "CSV_Files_Fixed/EURUSD_M30_FIXED.csv",
            "CSV_Files_Fixed/GBPUSD_M30_FIXED.csv",
            "CSV_Files_Fixed/GOLD_M30_FIXED.csv",
            "CSV_Files_Fixed/NZDUSD_M30_FIXED.csv",
            "CSV_Files_Fixed/USDCAD_M30_FIXED.csv",
            "CSV_Files_Fixed/USDJPY_M30_FIXED.csv"
        ],
        "M60": [
            "CSV_Files_Fixed/AUDUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/EURGBP_H1_FIXED.csv",
            "CSV_Files_Fixed/EURUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/GOLD_H1_FIXED.csv",
            "CSV_Files_Fixed/NZDUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/USDCAD_H1_FIXED.csv",
            "CSV_Files_Fixed/USDJPY_H1_FIXED.csv"
        ]
    }
    
    # ฟังก์ชันจำลอง parse_filename
    def parse_filename(file_path):
        filename = file_path.split('/')[-1]
        if '_M30_' in filename:
            symbol = filename.split('_M30_')[0]
            timeframe = 30
        elif '_H1_' in filename:
            symbol = filename.split('_H1_')[0]
            timeframe = 60
        else:
            raise ValueError(f"ไม่สามารถแยกข้อมูลจาก {filename}")
        
        return {
            "Name_Currency": symbol,
            "Timeframe_Currency": timeframe
        }
    
    # รวบรวมข้อมูลจากทุก symbol และ timeframe
    all_filters = {}
    symbols_from_files = []

    # ดึงรายชื่อ symbols จาก test_groups (ใช้โค้ดที่แก้ไขแล้ว)
    for timeframe, files in test_groups.items():
        for file_path in files:
            # ใช้ฟังก์ชัน parse_filename เพื่อดึง symbol ที่ถูกต้อง
            try:
                info = parse_filename(file_path)
                symbol = info["Name_Currency"]
                if symbol not in symbols_from_files:
                    symbols_from_files.append(symbol)
            except Exception as e:
                print(f"⚠️ ไม่สามารถดึง symbol จาก {file_path}: {e}")
                # Fallback: ใช้วิธีเดิม
                symbol = file_path.split('/')[-1].split('_')[0]
                if symbol not in symbols_from_files:
                    symbols_from_files.append(symbol)
    
    print(f"🔍 ดึงสัญลักษณ์ได้ทั้งหมด: {symbols_from_files}")
    
    # ทดสอบการสร้างรายการแนะนำแบบใหม่
    day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
    
    print(f"\n🎯 ทดสอบการสร้างรายการแนะนำแบบใหม่:")
    for day_idx in range(5):  # วันจันทร์-ศุกร์
        day_name = day_names[day_idx]
        
        # ใช้สัญลักษณ์ที่ดึงมาจาก test_groups แทนที่จะ hardcode
        if symbols_from_files:
            # เลือกสัญลักษณ์ตามวัน (หมุนเวียน)
            num_symbols = min(3, len(symbols_from_files))  # เลือกสูงสุด 3 สัญลักษณ์
            start_idx = day_idx % len(symbols_from_files)
            recommended_symbols = []
            for i in range(num_symbols):
                idx = (start_idx + i) % len(symbols_from_files)
                recommended_symbols.append(symbols_from_files[idx])
        else:
            recommended_symbols = ['GOLD', 'USDJPY']
            
        print(f"  {day_name}: {' '.join(recommended_symbols)}")

def test_output_folder_structure():
    """
    ทดสอบโครงสร้างโฟลเดอร์ที่แก้ไขแล้ว
    """
    print("\n🧪 ทดสอบโครงสร้างโฟลเดอร์ที่แก้ไขแล้ว")
    print("=" * 50)
    
    # จำลองการสร้างโฟลเดอร์ตามโค้ดที่แก้ไข
    test_groups = ["M30", "M60"]
    
    for group_name in test_groups:
        # กำหนด output_folder เฉพาะกลุ่ม (เช่น Test_LightGBM/results/M30)
        output_folder = f"Test_LightGBM/results/{group_name}"
        print(f"📁 output_folder สำหรับ {group_name}: {output_folder}")
        
        # จำลองการสร้างโฟลเดอร์ย่อย
        symbols = ['AUDUSD', 'GOLD', 'USDJPY']
        timeframes = [30, 60]
        
        for symbol in symbols[:2]:  # ทดสอบแค่ 2 symbols
            for timeframe in timeframes:
                if (group_name == "M30" and timeframe == 30) or (group_name == "M60" and timeframe == 60):
                    # สร้างโฟลเดอร์ย่อยตาม timeframe ถ้ายังไม่มี - แก้ไขให้ใช้ output_folder ที่ส่งมาแล้ว
                    timeframe_folder = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}")
                    print(f"  📂 {timeframe_folder}")
                    
                    # ตรวจสอบว่าไฟล์จะไปอยู่ในโฟลเดอร์ที่ถูกต้อง
                    if group_name == "M30" and timeframe == 30:
                        print(f"    ✅ ไฟล์ M30 จะอยู่ในโฟลเดอร์ M30")
                    elif group_name == "M60" and timeframe == 60:
                        print(f"    ✅ ไฟล์ M60 จะอยู่ในโฟลเดอร์ M60")
                    else:
                        print(f"    ⚠️ ไฟล์ {timeframe} จะอยู่ในโฟลเดอร์ {group_name} (ไม่ตรงกัน)")

if __name__ == "__main__":
    print("🔧 ทดสอบการแก้ไขแบบย่อ")
    print("=" * 60)
    
    test_symbol_extraction_fixed()
    test_output_folder_structure()
    
    print("\n✅ การทดสอบเสร็จสิ้น")
    print("\n📋 สรุปการแก้ไข:")
    print("1. ✅ แก้ไขการดึงสัญลักษณ์ให้ใช้ parse_filename")
    print("2. ✅ แก้ไขการสร้างรายการแนะนำให้หมุนเวียนสัญลักษณ์")
    print("3. ✅ แก้ไข output_folder ให้แยกตาม group_name")
    print("4. ✅ ส่ง output_folder ไปยังฟังก์ชัน train_and_evaluate")
    print("\n🚀 พร้อมทดสอบการรันจริง!")

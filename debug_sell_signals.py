#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug ปัญหาไม่มี Sell signals
"""

import os
import sys
import pandas as pd
import numpy as np
import joblib

# เพิ่ม path สำหรับ import
sys.path.append('.')

def check_model_predictions():
    """ตรวจสอบการทำนายของโมเดลโดยตรง"""
    
    print("🔍 ตรวจสอบการทำนายของโมเดลโดยตรง")
    print("="*60)
    
    try:
        from python_LightGBM_16_Signal import (
            USE_MULTICLASS_TARGET, 
            CLASS_MAPPING,
            test_groups
        )
        
        # หาโมเดลที่เทรนแล้ว
        model_dirs = [
            "LightGBM_Multi/models/trend_following",
            "LightGBM_Multi/models/counter_trend",
            "LightGBM_Model_Multi"
        ]
        
        for model_dir in model_dirs:
            if os.path.exists(model_dir):
                print(f"\n📁 ตรวจสอบโฟลเดอร์: {model_dir}")
                
                model_files = [f for f in os.listdir(model_dir) if f.endswith('_trained.pkl')]
                
                for model_file in model_files[:2]:  # ตรวจสอบแค่ 2 ไฟล์แรก
                    model_path = os.path.join(model_dir, model_file)
                    
                    try:
                        print(f"\n🤖 โหลดโมเดล: {model_file}")
                        model = joblib.load(model_path)
                        
                        # สร้างข้อมูลทดสอบ
                        n_features = len(model.feature_name_) if hasattr(model, 'feature_name_') else 50
                        X_test = np.random.random((100, n_features))
                        
                        # ทำนาย
                        predictions = model.predict(X_test)
                        probabilities = model.predict_proba(X_test)
                        
                        print(f"   📊 Predictions shape: {predictions.shape}")
                        print(f"   📊 Probabilities shape: {probabilities.shape}")
                        print(f"   📊 Unique predictions: {np.unique(predictions)}")
                        
                        # วิเคราะห์ class distribution
                        unique, counts = np.unique(predictions, return_counts=True)
                        print(f"   📊 Class distribution:")
                        for class_id, count in zip(unique, counts):
                            class_name = CLASS_MAPPING.get(class_id, f"Unknown_{class_id}")
                            percentage = (count / len(predictions)) * 100
                            print(f"      Class {class_id} ({class_name}): {count} ({percentage:.1f}%)")
                        
                        # ตรวจสอบ probability distribution
                        print(f"   📊 Average probabilities per class:")
                        avg_probs = np.mean(probabilities, axis=0)
                        for i, prob in enumerate(avg_probs):
                            class_name = CLASS_MAPPING.get(i, f"Class_{i}")
                            print(f"      Class {i} ({class_name}): {prob:.3f}")
                        
                        # ตรวจสอบ sell probabilities
                        if probabilities.shape[1] >= 5:
                            sell_probs = probabilities[:, 0] + probabilities[:, 1]  # strong_sell + weak_sell
                            buy_probs = probabilities[:, 3] + probabilities[:, 4]   # weak_buy + strong_buy
                            
                            print(f"   📈 Buy probabilities: mean={np.mean(buy_probs):.3f}, max={np.max(buy_probs):.3f}")
                            print(f"   📉 Sell probabilities: mean={np.mean(sell_probs):.3f}, max={np.max(sell_probs):.3f}")
                            
                            # ตรวจสอบกับ threshold ต่างๆ
                            thresholds = [0.3, 0.5, 0.7]
                            for threshold in thresholds:
                                buy_signals = np.sum(buy_probs > threshold)
                                sell_signals = np.sum(sell_probs > threshold)
                                print(f"   🎯 Threshold {threshold}: Buy={buy_signals}, Sell={sell_signals}")
                        
                    except Exception as e:
                        print(f"   ❌ ไม่สามารถโหลดโมเดล {model_file}: {e}")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def check_training_data_distribution():
    """ตรวจสอบการกระจายของข้อมูลเทรนนิ่ง"""
    
    print(f"\n🔍 ตรวจสอบการกระจายของข้อมูลเทรนนิ่ง")
    print("="*60)
    
    try:
        from python_LightGBM_16_Signal import test_groups, USE_MULTICLASS_TARGET
        
        # ตรวจสอบไฟล์ข้อมูลแรก
        if test_groups and 'M30' in test_groups:
            test_file = test_groups['M30'][0]
            
            if os.path.exists(test_file):
                print(f"📁 วิเคราะห์ไฟล์: {test_file}")
                
                # ลองอ่านด้วย separators ต่างๆ
                df = None
                for sep_name, sep in [('comma', ','), ('tab', '\t')]:
                    try:
                        df_temp = pd.read_csv(test_file, sep=sep)
                        if len(df_temp.columns) > 1:
                            df = df_temp
                            print(f"   ✅ อ่านด้วย {sep_name}: {df.shape}")
                            break
                    except:
                        continue
                
                if df is not None:
                    print(f"   📊 คอลัมน์: {list(df.columns)}")
                    
                    # ตรวจสอบว่ามี Target columns หรือไม่
                    target_cols = [col for col in df.columns if 'Target' in col]
                    print(f"   📊 Target columns: {target_cols}")
                    
                    if 'Target_Multiclass' in df.columns:
                        target_dist = df['Target_Multiclass'].value_counts().sort_index()
                        print(f"   📊 Target_Multiclass distribution:")
                        for class_id, count in target_dist.items():
                            class_name = CLASS_MAPPING.get(class_id, f"Class_{class_id}")
                            percentage = (count / len(df)) * 100
                            print(f"      Class {class_id} ({class_name}): {count} ({percentage:.1f}%)")
                        
                        # ตรวจสอบว่ามี sell classes หรือไม่
                        sell_classes = [0, 1]  # strong_sell, weak_sell
                        sell_count = df[df['Target_Multiclass'].isin(sell_classes)].shape[0]
                        total_count = len(df)
                        sell_percentage = (sell_count / total_count) * 100
                        
                        print(f"   📉 Sell classes (0,1): {sell_count}/{total_count} ({sell_percentage:.1f}%)")
                        
                        if sell_percentage < 5:
                            print(f"   ⚠️ Sell classes มีน้อยมาก! อาจเป็นสาเหตุที่โมเดลไม่ทำนาย sell")
                    
                    elif 'Target' in df.columns:
                        target_dist = df['Target'].value_counts()
                        print(f"   📊 Binary Target distribution: {target_dist.to_dict()}")
                
            else:
                print(f"❌ ไม่พบไฟล์: {test_file}")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def check_signal_generation_logic():
    """ตรวจสอบ logic การสร้าง signals"""
    
    print(f"\n🔍 ตรวจสอบ Logic การสร้าง Signals")
    print("="*60)
    
    # สร้างข้อมูลทดสอบที่ควรให้ sell signal
    test_probabilities = [
        {
            'name': 'Strong Sell Test',
            'probs': [0.7, 0.2, 0.05, 0.025, 0.025],  # Class 0 สูง
            'expected': 'SELL'
        },
        {
            'name': 'Weak Sell Test', 
            'probs': [0.2, 0.7, 0.05, 0.025, 0.025],  # Class 1 สูง
            'expected': 'SELL'
        },
        {
            'name': 'Strong Buy Test',
            'probs': [0.025, 0.025, 0.05, 0.2, 0.7],  # Class 4 สูง
            'expected': 'BUY'
        }
    ]
    
    for test_case in test_probabilities:
        print(f"\n🎯 {test_case['name']}:")
        probs = test_case['probs']
        
        # คำนวณตาม logic ใหม่
        sell_prob = probs[0] + probs[1]  # strong_sell + weak_sell
        buy_prob = probs[3] + probs[4]   # weak_buy + strong_buy
        no_trade_prob = probs[2]         # no_trade
        
        print(f"   📊 Probabilities: {probs}")
        print(f"   📉 Sell prob: {sell_prob:.3f}")
        print(f"   📈 Buy prob: {buy_prob:.3f}")
        print(f"   ⏸️ No trade prob: {no_trade_prob:.3f}")
        
        # ทดสอบกับ threshold ต่างๆ
        thresholds = [0.3, 0.5, 0.7]
        for threshold in thresholds:
            if sell_prob > threshold:
                signal = "SELL"
            elif buy_prob > threshold:
                signal = "BUY"
            else:
                signal = "HOLD"
            
            correct = signal == test_case['expected'] or (signal == "HOLD" and threshold > max(sell_prob, buy_prob))
            status = "✅" if correct else "❌"
            print(f"   🎯 Threshold {threshold}: {signal} {status}")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🏗️ Debug Sell Signals Issue")
    print("="*50)
    
    # ตรวจสอบการทำนายของโมเดล
    check_model_predictions()
    
    # ตรวจสอบการกระจายของข้อมูลเทรนนิ่ง
    check_training_data_distribution()
    
    # ตรวจสอบ logic การสร้าง signals
    check_signal_generation_logic()
    
    print(f"\n📋 สรุปการตรวจสอบ:")
    print(f"   1. ตรวจสอบว่าโมเดลทำนาย sell classes ได้หรือไม่")
    print(f"   2. ตรวจสอบการกระจายของข้อมูลเทรนนิ่ง")
    print(f"   3. ตรวจสอบ logic การสร้าง signals")
    
    print(f"\n💡 แนวทางแก้ไข:")
    print(f"   - ถ้าข้อมูลเทรนนิ่งไม่มี sell classes → ปรับ PROFIT_THRESHOLDS")
    print(f"   - ถ้าโมเดลไม่ทำนาย sell → ตรวจสอบการเทรนโมเดล")
    print(f"   - ถ้า logic ผิด → แก้ไขการคำนวณ probabilities")

if __name__ == "__main__":
    main()

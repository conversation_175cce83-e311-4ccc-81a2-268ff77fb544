2025-07-13 16:17:41 | INFO     | setup_logging        | ================================================================================
2025-07-13 16:17:41 | INFO     | setup_logging        | 🚀 เริ่มต้นระบบ Trading Model
2025-07-13 16:17:41 | INFO     | setup_logging        | 📁 Log file: logs\trading_model_20250713.log
2025-07-13 16:17:41 | INFO     | setup_logging        | 📊 Max file size: 10 MB
2025-07-13 16:17:41 | INFO     | setup_logging        | 🔄 Backup count: 5
2025-07-13 16:17:41 | INFO     | setup_logging        | ================================================================================
2025-07-13 16:17:41 | INFO     | log_function_start   | 🏗️ เปิดใช้งาน main
2025-07-13 16:17:41 | INFO     | main                 | 📁 ใช้ output_folder สำหรับกลุ่ม M60: LightGBM_Multi/results/M60
2025-07-13 16:17:41 | INFO     | main                 | ⏱️ เริ่มการประมวลผล - รอบที่ 1/1
2025-07-13 16:17:41 | INFO     | main                 | ============================================================
2025-07-13 16:17:41 | INFO     | main                 | 📄 ประมวลผลไฟล์ 1/1: CSV_Files_Fixed/GOLD_H1_FIXED.csv
2025-07-13 16:17:41 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 16:17:41 | INFO     | main                 | ============================================================
2025-07-13 16:17:41 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=GOLD, Timeframe=60
2025-07-13 16:17:41 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 16:17:41 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: GOLD
2025-07-13 16:17:41 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.550, nBars_SL=12
2025-07-13 16:19:22 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 101.13s)
2025-07-13 16:19:22 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 16:19:22 | INFO     | log_data_period      | 📅 Train: 2013-07-18 ถึง 2021-03-10 (2793 วัน, 580 records)
2025-07-13 16:19:22 | INFO     | log_data_period      | 📅 Val: 2021-03-10 ถึง 2023-04-18 (770 วัน, 193 records)
2025-07-13 16:19:22 | INFO     | log_data_period      | 📅 Test: 2023-04-19 ถึง 2025-07-10 (814 วัน, 194 records)
2025-07-13 16:19:22 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 16:19:22 | INFO     | main                 | 📊 DataFrame: 220 columns, 71982 rows
2025-07-13 16:19:22 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 967 rows
2025-07-13 16:19:22 | INFO     | main                 | 🎯 Features: 27 features
2025-07-13 16:19:22 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Multi/results/M60\060_GOLD_target_autocorrelation.png
2025-07-13 16:19:22 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:19:22 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:19:22 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:19:22 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:19:22 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.66s)
2025-07-13 16:19:22 | INFO     | main                 | ==================================================
2025-07-13 16:19:22 | INFO     | main                 | 🤖 เริ่มการเทรนโมเดล LightGBM
2025-07-13 16:19:22 | INFO     | main                 | 📊 Training samples: 580
2025-07-13 16:19:22 | INFO     | main                 | 🎯 Features: 27
2025-07-13 16:19:22 | INFO     | main                 | ==================================================
2025-07-13 16:19:22 | INFO     | main                 | 🔄 ใช้ Multi-Model Architecture (2 โมเดลแยกตามสถานการณ์)
2025-07-13 16:19:22 | INFO     | main                 | 🔄 เตรียมข้อมูลสำหรับ Multi-Model Architecture
2025-07-13 16:19:22 | INFO     | main                 | 📊 DataFrame shape: (71982, 220)
2025-07-13 16:19:22 | INFO     | main                 | 💰 Trade DataFrame shape: (967, 239)
2025-07-13 16:19:23 | INFO     | main                 | ✅ เพิ่มคอลัมน์ Target จาก trade_df
2025-07-13 16:19:23 | INFO     | main                 | ✅ เพิ่มคอลัมน์ Target_Multiclass จาก trade_df
2025-07-13 16:19:23 | INFO     | main                 | 🎯 Target columns: ['Target', 'Target_Multiclass']
2025-07-13 16:19:41 | INFO     | main                 | ✅ เทรนโมเดลสำเร็จ: 2 scenarios (⏱️ 18.33s)
2025-07-13 16:19:41 | INFO     | main                 | ============================================================
2025-07-13 16:19:41 | INFO     | main                 | ⏱️ สรุปเวลาการประมวลผลทั้งหมด
2025-07-13 16:19:41 | INFO     | main                 | ============================================================
2025-07-13 16:19:41 | INFO     | main                 | 📊 เวลาทั้งหมด: 120.32s สำหรับ 1 ไฟล์
2025-07-13 16:19:41 | INFO     | main                 | ⏱️ เวลาเฉลี่ยต่อไฟล์: 120.32s (รอบที่ 1)
2025-07-13 16:19:41 | INFO     | main                 | 📊 ส่งคืนผลลัพธ์: 0 รายการ
2025-07-13 16:19:41 | INFO     | log_function_end     | ✅ เสร็จสิ้น main (⏱️ 120.32s)
2025-07-13 16:31:41 | INFO     | setup_logging        | ================================================================================
2025-07-13 16:31:41 | INFO     | setup_logging        | 🚀 เริ่มต้นระบบ Trading Model
2025-07-13 16:31:41 | INFO     | setup_logging        | 📁 Log file: logs\trading_model_20250713.log
2025-07-13 16:31:41 | INFO     | setup_logging        | 📊 Max file size: 10 MB
2025-07-13 16:31:41 | INFO     | setup_logging        | 🔄 Backup count: 5
2025-07-13 16:31:41 | INFO     | setup_logging        | ================================================================================
2025-07-13 16:31:41 | INFO     | log_function_start   | 🏗️ เปิดใช้งาน main
2025-07-13 16:31:41 | INFO     | main                 | 📁 ใช้ output_folder สำหรับกลุ่ม M60: LightGBM_Multi/results/M60
2025-07-13 16:31:41 | INFO     | main                 | ⏱️ เริ่มการประมวลผล - รอบที่ 1/1
2025-07-13 16:31:41 | INFO     | main                 | ============================================================
2025-07-13 16:31:41 | INFO     | main                 | 📄 ประมวลผลไฟล์ 1/1: CSV_Files_Fixed/GOLD_H1_FIXED.csv
2025-07-13 16:31:41 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 16:31:41 | INFO     | main                 | ============================================================
2025-07-13 16:31:41 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=GOLD, Timeframe=60
2025-07-13 16:31:41 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 16:31:41 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: GOLD
2025-07-13 16:31:41 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.550, nBars_SL=12
2025-07-13 16:33:24 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 102.87s)
2025-07-13 16:33:24 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 16:33:24 | INFO     | log_data_period      | 📅 Train: 2013-07-18 ถึง 2021-03-10 (2793 วัน, 580 records)
2025-07-13 16:33:24 | INFO     | log_data_period      | 📅 Val: 2021-03-10 ถึง 2023-04-18 (770 วัน, 193 records)
2025-07-13 16:33:24 | INFO     | log_data_period      | 📅 Test: 2023-04-19 ถึง 2025-07-10 (814 วัน, 194 records)
2025-07-13 16:33:24 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 16:33:24 | INFO     | main                 | 📊 DataFrame: 220 columns, 71982 rows
2025-07-13 16:33:24 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 967 rows
2025-07-13 16:33:24 | INFO     | main                 | 🎯 Features: 27 features
2025-07-13 16:33:24 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Multi/results/M60\060_GOLD_target_autocorrelation.png
2025-07-13 16:33:24 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:33:24 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:33:24 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:33:24 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:33:25 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.67s)
2025-07-13 16:33:25 | INFO     | main                 | ==================================================
2025-07-13 16:33:25 | INFO     | main                 | 🤖 เริ่มการเทรนโมเดล LightGBM
2025-07-13 16:33:25 | INFO     | main                 | 📊 Training samples: 580
2025-07-13 16:33:25 | INFO     | main                 | 🎯 Features: 27
2025-07-13 16:33:25 | INFO     | main                 | ==================================================
2025-07-13 16:33:25 | INFO     | main                 | 🔄 ใช้ Multi-Model Architecture (2 โมเดลแยกตามสถานการณ์)
2025-07-13 16:33:25 | INFO     | main                 | 🔄 เตรียมข้อมูลสำหรับ Multi-Model Architecture
2025-07-13 16:33:25 | INFO     | main                 | 📊 DataFrame shape: (71982, 220)
2025-07-13 16:33:25 | INFO     | main                 | 💰 Trade DataFrame shape: (967, 239)
2025-07-13 16:33:25 | INFO     | main                 | ✅ เพิ่มคอลัมน์ Target จาก trade_df
2025-07-13 16:33:25 | INFO     | main                 | ✅ เพิ่มคอลัมน์ Target_Multiclass จาก trade_df
2025-07-13 16:33:25 | INFO     | main                 | 🎯 Target columns: ['Target', 'Target_Multiclass']
2025-07-13 16:33:43 | INFO     | main                 | ✅ เทรนโมเดลสำเร็จ: 2 scenarios (⏱️ 18.44s)
2025-07-13 16:33:43 | INFO     | main                 | ============================================================
2025-07-13 16:33:43 | INFO     | main                 | ⏱️ สรุปเวลาการประมวลผลทั้งหมด
2025-07-13 16:33:43 | INFO     | main                 | ============================================================
2025-07-13 16:33:43 | INFO     | main                 | 📊 เวลาทั้งหมด: 122.18s สำหรับ 1 ไฟล์
2025-07-13 16:33:43 | INFO     | main                 | ⏱️ เวลาเฉลี่ยต่อไฟล์: 122.18s (รอบที่ 1)
2025-07-13 16:33:43 | INFO     | main                 | 📊 ส่งคืนผลลัพธ์: 0 รายการ
2025-07-13 16:33:43 | INFO     | log_function_end     | ✅ เสร็จสิ้น main (⏱️ 122.19s)
2025-07-13 16:36:48 | INFO     | setup_logging        | ================================================================================
2025-07-13 16:36:48 | INFO     | setup_logging        | 🚀 เริ่มต้นระบบ Trading Model
2025-07-13 16:36:48 | INFO     | setup_logging        | 📁 Log file: logs\trading_model_20250713.log
2025-07-13 16:36:48 | INFO     | setup_logging        | 📊 Max file size: 10 MB
2025-07-13 16:36:48 | INFO     | setup_logging        | 🔄 Backup count: 5
2025-07-13 16:36:48 | INFO     | setup_logging        | ================================================================================
2025-07-13 16:36:48 | INFO     | log_function_start   | 🏗️ เปิดใช้งาน main
2025-07-13 16:36:48 | INFO     | main                 | 📁 ใช้ output_folder สำหรับกลุ่ม M30: LightGBM_Single/results/M30
2025-07-13 16:36:48 | INFO     | main                 | ⏱️ เริ่มการประมวลผล - รอบที่ 1/1
2025-07-13 16:36:48 | INFO     | main                 | ============================================================
2025-07-13 16:36:48 | INFO     | main                 | 📄 ประมวลผลไฟล์ 1/8: CSV_Files_Fixed/AUDUSD_M30_FIXED.csv
2025-07-13 16:36:48 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 16:36:48 | INFO     | main                 | ============================================================
2025-07-13 16:36:48 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=AUDUSD, Timeframe=30
2025-07-13 16:36:48 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 16:36:48 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: AUDUSD
2025-07-13 16:36:48 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.350, nBars_SL=6
2025-07-13 16:38:39 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 110.15s)
2025-07-13 16:38:39 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 16:38:39 | INFO     | log_data_period      | 📅 Train: 2019-08-06 ถึง 2022-11-21 (1204 วัน, 639 records)
2025-07-13 16:38:39 | INFO     | log_data_period      | 📅 Val: 2022-11-22 ถึง 2023-10-13 (326 วัน, 213 records)
2025-07-13 16:38:39 | INFO     | log_data_period      | 📅 Test: 2023-10-13 ถึง 2025-07-08 (635 วัน, 214 records)
2025-07-13 16:38:39 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 16:38:39 | INFO     | main                 | 📊 DataFrame: 220 columns, 74718 rows
2025-07-13 16:38:39 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 1066 rows
2025-07-13 16:38:39 | INFO     | main                 | 🎯 Features: 41 features
2025-07-13 16:38:39 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M30\030_AUDUSD_target_autocorrelation.png
2025-07-13 16:38:39 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:38:39 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:38:39 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:38:39 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:38:39 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.68s)
2025-07-13 16:38:39 | INFO     | main                 | ============================================================
2025-07-13 16:38:39 | INFO     | main                 | 📄 ประมวลผลไฟล์ 2/8: CSV_Files_Fixed/EURGBP_M30_FIXED.csv
2025-07-13 16:38:39 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 16:38:39 | INFO     | main                 | ============================================================
2025-07-13 16:38:39 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=EURGBP, Timeframe=30
2025-07-13 16:38:39 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 16:38:39 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: EURGBP
2025-07-13 16:38:39 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.350, nBars_SL=6
2025-07-13 16:40:25 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 106.03s)
2025-07-13 16:40:25 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 16:40:25 | INFO     | log_data_period      | 📅 Train: 2019-07-26 ถึง 2022-05-18 (1028 วัน, 245 records)
2025-07-13 16:40:25 | INFO     | log_data_period      | 📅 Val: 2022-05-18 ถึง 2022-10-24 (160 วัน, 81 records)
2025-07-13 16:40:25 | INFO     | log_data_period      | 📅 Test: 2022-10-24 ถึง 2025-05-07 (927 วัน, 83 records)
2025-07-13 16:40:25 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 16:40:25 | INFO     | main                 | 📊 DataFrame: 220 columns, 74718 rows
2025-07-13 16:40:25 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 409 rows
2025-07-13 16:40:25 | INFO     | main                 | 🎯 Features: 40 features
2025-07-13 16:40:26 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M30\030_EURGBP_target_autocorrelation.png
2025-07-13 16:40:26 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:40:26 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:40:26 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:40:26 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:40:26 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.67s)
2025-07-13 16:40:26 | INFO     | main                 | ============================================================
2025-07-13 16:40:26 | INFO     | main                 | 📄 ประมวลผลไฟล์ 3/8: CSV_Files_Fixed/EURUSD_M30_FIXED.csv
2025-07-13 16:40:26 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 16:40:26 | INFO     | main                 | ============================================================
2025-07-13 16:40:26 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=EURUSD, Timeframe=30
2025-07-13 16:40:26 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 16:40:26 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: EURUSD
2025-07-13 16:40:26 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 16:42:14 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 107.93s)
2025-07-13 16:42:14 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 16:42:14 | INFO     | log_data_period      | 📅 Train: 2019-07-26 ถึง 2023-02-06 (1292 วัน, 774 records)
2025-07-13 16:42:14 | INFO     | log_data_period      | 📅 Val: 2023-02-06 ถึง 2023-12-18 (316 วัน, 258 records)
2025-07-13 16:42:14 | INFO     | log_data_period      | 📅 Test: 2023-12-29 ถึง 2025-07-10 (560 วัน, 258 records)
2025-07-13 16:42:14 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 16:42:14 | INFO     | main                 | 📊 DataFrame: 220 columns, 74718 rows
2025-07-13 16:42:14 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 1290 rows
2025-07-13 16:42:14 | INFO     | main                 | 🎯 Features: 41 features
2025-07-13 16:42:14 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M30\030_EURUSD_target_autocorrelation.png
2025-07-13 16:42:14 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:42:14 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:42:14 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:42:14 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:42:15 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.65s)
2025-07-13 16:42:15 | INFO     | main                 | ============================================================
2025-07-13 16:42:15 | INFO     | main                 | 📄 ประมวลผลไฟล์ 4/8: CSV_Files_Fixed/GBPUSD_M30_FIXED.csv
2025-07-13 16:42:15 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 16:42:15 | INFO     | main                 | ============================================================
2025-07-13 16:42:15 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=GBPUSD, Timeframe=30
2025-07-13 16:42:15 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 16:42:15 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/030_GBPUSD
2025-07-13 16:42:15 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: GBPUSD
2025-07-13 16:42:15 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 16:44:02 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 107.56s)
2025-07-13 16:44:02 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 16:44:02 | INFO     | log_data_period      | 📅 Train: 2019-07-16 ถึง 2022-12-21 (1255 วัน, 1134 records)
2025-07-13 16:44:02 | INFO     | log_data_period      | 📅 Val: 2022-12-23 ถึง 2024-02-05 (410 วัน, 378 records)
2025-07-13 16:44:02 | INFO     | log_data_period      | 📅 Test: 2024-02-06 ถึง 2025-07-10 (521 วัน, 378 records)
2025-07-13 16:44:02 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 16:44:02 | INFO     | main                 | 📊 DataFrame: 220 columns, 74718 rows
2025-07-13 16:44:02 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 1890 rows
2025-07-13 16:44:02 | INFO     | main                 | 🎯 Features: 31 features
2025-07-13 16:44:03 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M30\030_GBPUSD_target_autocorrelation.png
2025-07-13 16:44:03 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:44:03 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:44:03 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:44:03 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:44:03 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.69s)
2025-07-13 16:44:03 | INFO     | main                 | ============================================================
2025-07-13 16:44:03 | INFO     | main                 | 📄 ประมวลผลไฟล์ 5/8: CSV_Files_Fixed/GOLD_M30_FIXED.csv
2025-07-13 16:44:03 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 16:44:03 | INFO     | main                 | ============================================================
2025-07-13 16:44:03 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=GOLD, Timeframe=30
2025-07-13 16:44:03 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 16:44:03 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/030_GOLD
2025-07-13 16:44:03 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: GOLD
2025-07-13 16:44:03 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 16:45:46 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 102.56s)
2025-07-13 16:45:46 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 16:45:46 | INFO     | log_data_period      | 📅 Train: 2019-07-10 ถึง 2023-02-10 (1312 วัน, 1441 records)
2025-07-13 16:45:46 | INFO     | log_data_period      | 📅 Val: 2023-02-13 ถึง 2024-02-15 (368 วัน, 480 records)
2025-07-13 16:45:46 | INFO     | log_data_period      | 📅 Test: 2024-02-16 ถึง 2025-07-10 (511 วัน, 481 records)
2025-07-13 16:45:46 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 16:45:46 | INFO     | main                 | 📊 DataFrame: 220 columns, 70985 rows
2025-07-13 16:45:46 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 2402 rows
2025-07-13 16:45:46 | INFO     | main                 | 🎯 Features: 22 features
2025-07-13 16:45:46 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M30\030_GOLD_target_autocorrelation.png
2025-07-13 16:45:46 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:45:46 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:45:46 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:45:46 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:45:47 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.68s)
2025-07-13 16:45:47 | INFO     | main                 | ============================================================
2025-07-13 16:45:47 | INFO     | main                 | 📄 ประมวลผลไฟล์ 6/8: CSV_Files_Fixed/NZDUSD_M30_FIXED.csv
2025-07-13 16:45:47 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 16:45:47 | INFO     | main                 | ============================================================
2025-07-13 16:45:47 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=NZDUSD, Timeframe=30
2025-07-13 16:45:47 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 16:45:47 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/030_NZDUSD
2025-07-13 16:45:47 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: NZDUSD
2025-07-13 16:45:47 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 16:47:39 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 112.82s)
2025-07-13 16:47:39 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 16:47:39 | INFO     | log_data_period      | 📅 Train: 2019-08-06 ถึง 2022-09-27 (1149 วัน, 543 records)
2025-07-13 16:47:39 | INFO     | log_data_period      | 📅 Val: 2022-09-27 ถึง 2023-09-21 (360 วัน, 181 records)
2025-07-13 16:47:39 | INFO     | log_data_period      | 📅 Test: 2023-09-21 ถึง 2025-07-04 (653 วัน, 182 records)
2025-07-13 16:47:39 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 16:47:39 | INFO     | main                 | 📊 DataFrame: 220 columns, 74714 rows
2025-07-13 16:47:39 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 906 rows
2025-07-13 16:47:39 | INFO     | main                 | 🎯 Features: 38 features
2025-07-13 16:47:40 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M30\030_NZDUSD_target_autocorrelation.png
2025-07-13 16:47:40 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:47:40 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:47:40 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:47:40 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:47:40 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.73s)
2025-07-13 16:47:40 | INFO     | main                 | ============================================================
2025-07-13 16:47:40 | INFO     | main                 | 📄 ประมวลผลไฟล์ 7/8: CSV_Files_Fixed/USDCAD_M30_FIXED.csv
2025-07-13 16:47:40 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 16:47:40 | INFO     | main                 | ============================================================
2025-07-13 16:47:40 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=USDCAD, Timeframe=30
2025-07-13 16:47:40 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 16:47:40 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/030_USDCAD
2025-07-13 16:47:40 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: USDCAD
2025-07-13 16:47:40 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 16:49:28 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 107.38s)
2025-07-13 16:49:28 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 16:49:28 | INFO     | log_data_period      | 📅 Train: 2019-07-11 ถึง 2023-01-11 (1281 วัน, 948 records)
2025-07-13 16:49:28 | INFO     | log_data_period      | 📅 Val: 2023-01-11 ถึง 2024-02-02 (388 วัน, 316 records)
2025-07-13 16:49:28 | INFO     | log_data_period      | 📅 Test: 2024-02-02 ถึง 2025-07-03 (518 วัน, 317 records)
2025-07-13 16:49:28 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 16:49:28 | INFO     | main                 | 📊 DataFrame: 220 columns, 74718 rows
2025-07-13 16:49:28 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 1581 rows
2025-07-13 16:49:28 | INFO     | main                 | 🎯 Features: 41 features
2025-07-13 16:49:28 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M30\030_USDCAD_target_autocorrelation.png
2025-07-13 16:49:28 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:49:28 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:49:28 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:49:28 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:49:29 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.81s)
2025-07-13 16:49:29 | INFO     | main                 | ============================================================
2025-07-13 16:49:29 | INFO     | main                 | 📄 ประมวลผลไฟล์ 8/8: CSV_Files_Fixed/USDJPY_M30_FIXED.csv
2025-07-13 16:49:29 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 16:49:29 | INFO     | main                 | ============================================================
2025-07-13 16:49:29 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=USDJPY, Timeframe=30
2025-07-13 16:49:29 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 16:49:29 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/030_USDJPY
2025-07-13 16:49:29 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: USDJPY
2025-07-13 16:49:29 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 16:51:17 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 107.88s)
2025-07-13 16:51:17 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 16:51:17 | INFO     | log_data_period      | 📅 Train: 2019-07-11 ถึง 2023-12-04 (1608 วัน, 758 records)
2025-07-13 16:51:17 | INFO     | log_data_period      | 📅 Val: 2023-12-05 ถึง 2024-09-13 (284 วัน, 252 records)
2025-07-13 16:51:17 | INFO     | log_data_period      | 📅 Test: 2024-09-16 ถึง 2025-07-10 (298 วัน, 254 records)
2025-07-13 16:51:17 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 16:51:17 | INFO     | main                 | 📊 DataFrame: 220 columns, 74718 rows
2025-07-13 16:51:17 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 1264 rows
2025-07-13 16:51:17 | INFO     | main                 | 🎯 Features: 33 features
2025-07-13 16:51:17 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M30\030_USDJPY_target_autocorrelation.png
2025-07-13 16:51:17 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:51:17 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:51:17 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:51:17 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:51:17 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.67s)
2025-07-13 16:51:17 | INFO     | main                 | ==================================================
2025-07-13 16:51:17 | INFO     | main                 | 🤖 เริ่มการเทรนโมเดล LightGBM
2025-07-13 16:51:17 | INFO     | main                 | 📊 Training samples: 758
2025-07-13 16:51:17 | INFO     | main                 | 🎯 Features: 33
2025-07-13 16:51:17 | INFO     | main                 | ==================================================
2025-07-13 16:51:17 | INFO     | main                 | 🔄 ใช้ Single Model Architecture (แบบเดิม)
2025-07-13 17:02:26 | INFO     | main                 | ✅ เทรน Single Model สำเร็จ (⏱️ 668.52s)
2025-07-13 17:02:26 | INFO     | log_model_performance | 📈 Model Performance - USDJPY 30
2025-07-13 17:02:26 | INFO     | log_model_performance |   📊 timeframe: 30
2025-07-13 17:02:26 | INFO     | log_model_performance |   📊 accuracy: 0.6457
2025-07-13 17:02:26 | INFO     | log_model_performance |   📊 auc: 0.8401
2025-07-13 17:02:26 | INFO     | log_model_performance |   📊 f1: 0.6418
2025-07-13 17:02:26 | INFO     | log_model_performance |   📊 precision: 0
2025-07-13 17:02:26 | INFO     | log_model_performance |   📊 recall: 0
2025-07-13 17:02:26 | INFO     | log_model_performance |   📊 confusion_matrix: [[74  3 10  1  8]
 [ 0 16  3  1  0]
 [25  3 29  8 16]
 [ 1  0  3 10  0]
 [ 2  1  5  0 35]]
2025-07-13 17:02:26 | INFO     | log_model_performance |   📊 auc_pr: 0.5000
2025-07-13 17:02:26 | ERROR    | log_error_with_traceback | ❌ ERROR: ข้อผิดพลาดใน main function
2025-07-13 17:02:26 | ERROR    | log_error_with_traceback | Exception type: UnboundLocalError
2025-07-13 17:02:26 | ERROR    | log_error_with_traceback | Exception message: cannot access local variable 'e' where it is not associated with a value
2025-07-13 17:02:26 | ERROR    | log_error_with_traceback | Full traceback:
2025-07-13 17:02:26 | ERROR    | log_error_with_traceback |   Traceback (most recent call last):
2025-07-13 17:02:26 | ERROR    | log_error_with_traceback |     File "d:\test_gold\python_LightGBM_16_Signal.py", line 12505, in main
2025-07-13 17:02:26 | ERROR    | log_error_with_traceback |       "Status": f"Failed - Post-Train Process: {str(e)}",
2025-07-13 17:02:26 | ERROR    | log_error_with_traceback |                                                     ^
2025-07-13 17:02:26 | ERROR    | log_error_with_traceback |   UnboundLocalError: cannot access local variable 'e' where it is not associated with a value
2025-07-13 17:02:26 | INFO     | log_function_end     | ✅ เสร็จสิ้น main (⏱️ 1537.50s)
2025-07-13 17:02:26 | INFO     | log_function_start   | 🏗️ เปิดใช้งาน main
2025-07-13 17:02:26 | INFO     | main                 | 📁 ใช้ output_folder สำหรับกลุ่ม M60: LightGBM_Single/results/M60
2025-07-13 17:02:26 | INFO     | main                 | ⏱️ เริ่มการประมวลผล - รอบที่ 1/1
2025-07-13 17:02:26 | INFO     | main                 | ============================================================
2025-07-13 17:02:26 | INFO     | main                 | 📄 ประมวลผลไฟล์ 1/8: CSV_Files_Fixed/AUDUSD_H1_FIXED.csv
2025-07-13 17:02:26 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 17:02:26 | INFO     | main                 | ============================================================
2025-07-13 17:02:26 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=AUDUSD, Timeframe=60
2025-07-13 17:02:26 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 17:02:26 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/060_AUDUSD
2025-07-13 17:02:26 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: AUDUSD
2025-07-13 17:02:26 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 17:03:25 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 59.43s)
2025-07-13 17:03:25 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 17:03:25 | INFO     | log_data_period      | 📅 Train: 2018-04-19 ถึง 2022-09-14 (1610 วัน, 602 records)
2025-07-13 17:03:25 | INFO     | log_data_period      | 📅 Val: 2022-09-14 ถึง 2023-08-16 (337 วัน, 200 records)
2025-07-13 17:03:25 | INFO     | log_data_period      | 📅 Test: 2023-08-17 ถึง 2025-07-09 (693 วัน, 202 records)
2025-07-13 17:03:25 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 17:03:25 | INFO     | main                 | 📊 DataFrame: 220 columns, 46198 rows
2025-07-13 17:03:25 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 1004 rows
2025-07-13 17:03:25 | INFO     | main                 | 🎯 Features: 39 features
2025-07-13 17:03:26 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M60\060_AUDUSD_target_autocorrelation.png
2025-07-13 17:03:26 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:03:26 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:03:26 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:03:26 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:03:26 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.67s)
2025-07-13 17:03:26 | INFO     | main                 | ============================================================
2025-07-13 17:03:26 | INFO     | main                 | 📄 ประมวลผลไฟล์ 2/8: CSV_Files_Fixed/EURGBP_H1_FIXED.csv
2025-07-13 17:03:26 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 17:03:26 | INFO     | main                 | ============================================================
2025-07-13 17:03:26 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=EURGBP, Timeframe=60
2025-07-13 17:03:26 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 17:03:26 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/060_EURGBP
2025-07-13 17:03:26 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: EURGBP
2025-07-13 17:03:26 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 17:05:12 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 106.26s)
2025-07-13 17:05:12 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 17:05:12 | INFO     | log_data_period      | 📅 Train: 2013-07-12 ถึง 2019-08-23 (2234 วัน, 758 records)
2025-07-13 17:05:12 | INFO     | log_data_period      | 📅 Val: 2019-08-23 ถึง 2021-10-19 (789 วัน, 252 records)
2025-07-13 17:05:12 | INFO     | log_data_period      | 📅 Test: 2021-10-25 ถึง 2025-05-13 (1297 วัน, 254 records)
2025-07-13 17:05:12 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 17:05:12 | INFO     | main                 | 📊 DataFrame: 220 columns, 74505 rows
2025-07-13 17:05:12 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 1264 rows
2025-07-13 17:05:12 | INFO     | main                 | 🎯 Features: 32 features
2025-07-13 17:05:13 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M60\060_EURGBP_target_autocorrelation.png
2025-07-13 17:05:13 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:05:13 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:05:13 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:05:13 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:05:13 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.65s)
2025-07-13 17:05:13 | INFO     | main                 | ============================================================
2025-07-13 17:05:13 | INFO     | main                 | 📄 ประมวลผลไฟล์ 3/8: CSV_Files_Fixed/EURUSD_H1_FIXED.csv
2025-07-13 17:05:13 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 17:05:13 | INFO     | main                 | ============================================================
2025-07-13 17:05:13 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=EURUSD, Timeframe=60
2025-07-13 17:05:13 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 17:05:13 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/060_EURUSD
2025-07-13 17:05:13 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: EURUSD
2025-07-13 17:05:13 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 17:07:02 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 108.35s)
2025-07-13 17:07:02 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 17:07:02 | INFO     | log_data_period      | 📅 Train: 2013-07-15 ถึง 2020-07-03 (2546 วัน, 1185 records)
2025-07-13 17:07:02 | INFO     | log_data_period      | 📅 Val: 2020-07-03 ถึง 2022-10-07 (827 วัน, 395 records)
2025-07-13 17:07:02 | INFO     | log_data_period      | 📅 Test: 2022-10-10 ถึง 2025-06-23 (988 วัน, 395 records)
2025-07-13 17:07:02 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 17:07:02 | INFO     | main                 | 📊 DataFrame: 220 columns, 74511 rows
2025-07-13 17:07:02 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 1975 rows
2025-07-13 17:07:02 | INFO     | main                 | 🎯 Features: 43 features
2025-07-13 17:07:02 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M60\060_EURUSD_target_autocorrelation.png
2025-07-13 17:07:02 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:07:02 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:07:02 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:07:02 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:07:02 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.68s)
2025-07-13 17:07:02 | INFO     | main                 | ============================================================
2025-07-13 17:07:02 | INFO     | main                 | 📄 ประมวลผลไฟล์ 4/8: CSV_Files_Fixed/GBPUSD_H1_FIXED.csv
2025-07-13 17:07:02 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 17:07:02 | INFO     | main                 | ============================================================
2025-07-13 17:07:02 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=GBPUSD, Timeframe=60
2025-07-13 17:07:02 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 17:07:02 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/060_GBPUSD
2025-07-13 17:07:02 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: GBPUSD
2025-07-13 17:07:02 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 17:08:52 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 109.85s)
2025-07-13 17:08:52 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 17:08:52 | INFO     | log_data_period      | 📅 Train: 2013-07-16 ถึง 2020-09-17 (2621 วัน, 1433 records)
2025-07-13 17:08:52 | INFO     | log_data_period      | 📅 Val: 2020-09-17 ถึง 2023-01-17 (853 วัน, 477 records)
2025-07-13 17:08:52 | INFO     | log_data_period      | 📅 Test: 2023-02-01 ถึง 2025-07-11 (892 วัน, 479 records)
2025-07-13 17:08:52 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 17:08:52 | INFO     | main                 | 📊 DataFrame: 220 columns, 74505 rows
2025-07-13 17:08:52 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 2389 rows
2025-07-13 17:08:52 | INFO     | main                 | 🎯 Features: 36 features
2025-07-13 17:08:52 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M60\060_GBPUSD_target_autocorrelation.png
2025-07-13 17:08:52 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:08:52 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:08:52 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:08:52 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:08:53 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.67s)
2025-07-13 17:08:53 | INFO     | main                 | ============================================================
2025-07-13 17:08:53 | INFO     | main                 | 📄 ประมวลผลไฟล์ 5/8: CSV_Files_Fixed/GOLD_H1_FIXED.csv
2025-07-13 17:08:53 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 17:08:53 | INFO     | main                 | ============================================================
2025-07-13 17:08:53 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=GOLD, Timeframe=60
2025-07-13 17:08:53 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 17:08:53 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/060_GOLD
2025-07-13 17:08:53 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: GOLD
2025-07-13 17:08:53 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 17:10:36 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 102.72s)
2025-07-13 17:10:36 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 17:10:36 | INFO     | log_data_period      | 📅 Train: 2013-07-18 ถึง 2021-01-28 (2752 วัน, 1203 records)
2025-07-13 17:10:36 | INFO     | log_data_period      | 📅 Val: 2021-01-29 ถึง 2023-02-20 (753 วัน, 401 records)
2025-07-13 17:10:36 | INFO     | log_data_period      | 📅 Test: 2023-02-20 ถึง 2025-07-10 (872 วัน, 401 records)
2025-07-13 17:10:36 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 17:10:36 | INFO     | main                 | 📊 DataFrame: 220 columns, 71982 rows
2025-07-13 17:10:36 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 2005 rows
2025-07-13 17:10:36 | INFO     | main                 | 🎯 Features: 33 features
2025-07-13 17:10:36 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M60\060_GOLD_target_autocorrelation.png
2025-07-13 17:10:36 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:10:36 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:10:36 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:10:36 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:10:37 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.66s)
2025-07-13 17:10:37 | INFO     | main                 | ============================================================
2025-07-13 17:10:37 | INFO     | main                 | 📄 ประมวลผลไฟล์ 6/8: CSV_Files_Fixed/NZDUSD_H1_FIXED.csv
2025-07-13 17:10:37 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 17:10:37 | INFO     | main                 | ============================================================
2025-07-13 17:10:37 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=NZDUSD, Timeframe=60
2025-07-13 17:10:37 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 17:10:37 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/060_NZDUSD
2025-07-13 17:10:37 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: NZDUSD
2025-07-13 17:10:37 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 17:11:36 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 59.13s)
2025-07-13 17:11:36 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 17:11:36 | INFO     | log_data_period      | 📅 Train: 2018-04-24 ถึง 2022-06-30 (1529 วัน, 558 records)
2025-07-13 17:11:36 | INFO     | log_data_period      | 📅 Val: 2022-07-05 ถึง 2023-08-11 (403 วัน, 186 records)
2025-07-13 17:11:36 | INFO     | log_data_period      | 📅 Test: 2023-08-14 ถึง 2025-07-11 (698 วัน, 187 records)
2025-07-13 17:11:36 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 17:11:36 | INFO     | main                 | 📊 DataFrame: 220 columns, 46197 rows
2025-07-13 17:11:36 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 931 rows
2025-07-13 17:11:36 | INFO     | main                 | 🎯 Features: 34 features
2025-07-13 17:11:36 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M60\060_NZDUSD_target_autocorrelation.png
2025-07-13 17:11:36 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:11:36 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:11:36 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:11:36 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:11:36 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.68s)
2025-07-13 17:11:36 | INFO     | main                 | ============================================================
2025-07-13 17:11:36 | INFO     | main                 | 📄 ประมวลผลไฟล์ 7/8: CSV_Files_Fixed/USDCAD_H1_FIXED.csv
2025-07-13 17:11:36 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 17:11:36 | INFO     | main                 | ============================================================
2025-07-13 17:11:36 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=USDCAD, Timeframe=60
2025-07-13 17:11:36 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 17:11:36 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/060_USDCAD
2025-07-13 17:11:36 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: USDCAD
2025-07-13 17:11:36 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 17:13:23 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 106.40s)
2025-07-13 17:13:23 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 17:13:23 | INFO     | log_data_period      | 📅 Train: 2013-07-12 ถึง 2020-12-14 (2713 วัน, 1251 records)
2025-07-13 17:13:23 | INFO     | log_data_period      | 📅 Val: 2020-12-15 ถึง 2023-02-02 (780 วัน, 417 records)
2025-07-13 17:13:23 | INFO     | log_data_period      | 📅 Test: 2023-02-02 ถึง 2025-07-04 (884 วัน, 418 records)
2025-07-13 17:13:23 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 17:13:23 | INFO     | main                 | 📊 DataFrame: 220 columns, 74517 rows
2025-07-13 17:13:23 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 2086 rows
2025-07-13 17:13:23 | INFO     | main                 | 🎯 Features: 37 features
2025-07-13 17:13:23 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M60\060_USDCAD_target_autocorrelation.png
2025-07-13 17:13:23 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:13:23 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:13:23 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:13:23 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:13:24 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.69s)
2025-07-13 17:13:24 | INFO     | main                 | ============================================================
2025-07-13 17:13:24 | INFO     | main                 | 📄 ประมวลผลไฟล์ 8/8: CSV_Files_Fixed/USDJPY_H1_FIXED.csv
2025-07-13 17:13:24 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 17:13:24 | INFO     | main                 | ============================================================
2025-07-13 17:13:24 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=USDJPY, Timeframe=60
2025-07-13 17:13:24 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 17:13:24 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/060_USDJPY
2025-07-13 17:13:24 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: USDJPY
2025-07-13 17:13:24 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 17:15:11 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 107.44s)
2025-07-13 17:15:11 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 17:15:11 | INFO     | log_data_period      | 📅 Train: 2013-07-12 ถึง 2020-05-01 (2486 วัน, 1015 records)
2025-07-13 17:15:11 | INFO     | log_data_period      | 📅 Val: 2020-05-04 ถึง 2023-06-06 (1129 วัน, 338 records)
2025-07-13 17:15:11 | INFO     | log_data_period      | 📅 Test: 2023-06-07 ถึง 2025-07-10 (765 วัน, 340 records)
2025-07-13 17:15:11 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 17:15:11 | INFO     | main                 | 📊 DataFrame: 220 columns, 74509 rows
2025-07-13 17:15:11 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 1693 rows
2025-07-13 17:15:11 | INFO     | main                 | 🎯 Features: 34 features
2025-07-13 17:15:11 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M60\060_USDJPY_target_autocorrelation.png
2025-07-13 17:15:11 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:15:11 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:15:11 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:15:11 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:15:12 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.69s)
2025-07-13 17:15:12 | INFO     | main                 | ==================================================
2025-07-13 17:15:12 | INFO     | main                 | 🤖 เริ่มการเทรนโมเดล LightGBM
2025-07-13 17:15:12 | INFO     | main                 | 📊 Training samples: 1015
2025-07-13 17:15:12 | INFO     | main                 | 🎯 Features: 34
2025-07-13 17:15:12 | INFO     | main                 | ==================================================
2025-07-13 17:15:12 | INFO     | main                 | 🔄 ใช้ Single Model Architecture (แบบเดิม)
2025-07-13 17:30:21 | INFO     | main                 | ✅ เทรน Single Model สำเร็จ (⏱️ 908.56s)
2025-07-13 17:30:21 | INFO     | log_model_performance | 📈 Model Performance - USDJPY 60
2025-07-13 17:30:21 | INFO     | log_model_performance |   📊 timeframe: 60
2025-07-13 17:30:21 | INFO     | log_model_performance |   📊 accuracy: 0.6853
2025-07-13 17:30:21 | INFO     | log_model_performance |   📊 auc: 0.8786
2025-07-13 17:30:21 | INFO     | log_model_performance |   📊 f1: 0.6836
2025-07-13 17:30:21 | INFO     | log_model_performance |   📊 precision: 0
2025-07-13 17:30:21 | INFO     | log_model_performance |   📊 recall: 0
2025-07-13 17:30:21 | INFO     | log_model_performance |   📊 confusion_matrix: [[93  1 13  1  4]
 [ 3 16  2  0  0]
 [24  2 50  7 26]
 [ 0  0  5 12  2]
 [ 2  0 13  2 62]]
2025-07-13 17:30:21 | INFO     | log_model_performance |   📊 auc_pr: 0.5000
2025-07-13 17:30:21 | ERROR    | log_error_with_traceback | ❌ ERROR: ข้อผิดพลาดใน main function
2025-07-13 17:30:21 | ERROR    | log_error_with_traceback | Exception type: UnboundLocalError
2025-07-13 17:30:21 | ERROR    | log_error_with_traceback | Exception message: cannot access local variable 'e' where it is not associated with a value
2025-07-13 17:30:21 | ERROR    | log_error_with_traceback | Full traceback:
2025-07-13 17:30:21 | ERROR    | log_error_with_traceback |   Traceback (most recent call last):
2025-07-13 17:30:21 | ERROR    | log_error_with_traceback |     File "d:\test_gold\python_LightGBM_16_Signal.py", line 12505, in main
2025-07-13 17:30:21 | ERROR    | log_error_with_traceback |       "Status": f"Failed - Post-Train Process: {str(e)}",
2025-07-13 17:30:21 | ERROR    | log_error_with_traceback |                                                     ^
2025-07-13 17:30:21 | ERROR    | log_error_with_traceback |   UnboundLocalError: cannot access local variable 'e' where it is not associated with a value
2025-07-13 17:30:21 | INFO     | log_function_end     | ✅ เสร็จสิ้น main (⏱️ 1674.58s)
2025-07-13 17:32:03 | INFO     | setup_logging        | ================================================================================
2025-07-13 17:32:03 | INFO     | setup_logging        | 🚀 เริ่มต้นระบบ Trading Model
2025-07-13 17:32:03 | INFO     | setup_logging        | 📁 Log file: logs\trading_model_20250713.log
2025-07-13 17:32:03 | INFO     | setup_logging        | 📊 Max file size: 10 MB
2025-07-13 17:32:03 | INFO     | setup_logging        | 🔄 Backup count: 5
2025-07-13 17:32:03 | INFO     | setup_logging        | ================================================================================
2025-07-13 17:32:03 | INFO     | log_function_start   | 🏗️ เปิดใช้งาน main
2025-07-13 17:32:03 | INFO     | main                 | 📁 ใช้ output_folder สำหรับกลุ่ม M30: LightGBM_Single/results/M30
2025-07-13 17:32:03 | INFO     | main                 | ⏱️ เริ่มการประมวลผล - รอบที่ 1/1
2025-07-13 17:32:03 | INFO     | main                 | ============================================================
2025-07-13 17:32:03 | INFO     | main                 | 📄 ประมวลผลไฟล์ 1/8: CSV_Files_Fixed/AUDUSD_M30_FIXED.csv
2025-07-13 17:32:03 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 17:32:03 | INFO     | main                 | ============================================================
2025-07-13 17:32:03 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=AUDUSD, Timeframe=30
2025-07-13 17:32:03 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 17:32:03 | INFO     | main                 | 📁 สร้างโฟลเดอร์ models: LightGBM_Single/models
2025-07-13 17:32:03 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/030_AUDUSD
2025-07-13 17:32:03 | INFO     | main                 | 📁 สร้างโฟลเดอร์ feature_importance: LightGBM_Single/feature_importance
2025-07-13 17:32:03 | INFO     | main                 | 📁 สร้างโฟลเดอร์ thresholds: LightGBM_Single/thresholds
2025-07-13 17:32:03 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: AUDUSD
2025-07-13 17:32:03 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 17:33:51 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 108.02s)
2025-07-13 17:33:51 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 17:33:51 | INFO     | log_data_period      | 📅 Train: 2019-08-06 ถึง 2022-11-21 (1204 วัน, 639 records)
2025-07-13 17:33:51 | INFO     | log_data_period      | 📅 Val: 2022-11-22 ถึง 2023-10-13 (326 วัน, 213 records)
2025-07-13 17:33:51 | INFO     | log_data_period      | 📅 Test: 2023-10-13 ถึง 2025-07-08 (635 วัน, 214 records)
2025-07-13 17:33:51 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 17:33:51 | INFO     | main                 | 📊 DataFrame: 220 columns, 74718 rows
2025-07-13 17:33:51 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 1066 rows
2025-07-13 17:33:51 | INFO     | main                 | 🎯 Features: 41 features
2025-07-13 17:33:52 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M30\030_AUDUSD_target_autocorrelation.png
2025-07-13 17:33:52 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:33:52 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:33:52 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:33:52 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:33:52 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.67s)
2025-07-13 17:33:52 | INFO     | main                 | ============================================================
2025-07-13 17:33:52 | INFO     | main                 | 📄 ประมวลผลไฟล์ 2/8: CSV_Files_Fixed/EURGBP_M30_FIXED.csv
2025-07-13 17:33:52 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 17:33:52 | INFO     | main                 | ============================================================
2025-07-13 17:33:52 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=EURGBP, Timeframe=30
2025-07-13 17:33:52 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 17:33:52 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/030_EURGBP
2025-07-13 17:33:52 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: EURGBP
2025-07-13 17:33:52 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 17:35:38 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 105.77s)
2025-07-13 17:35:38 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 17:35:38 | INFO     | log_data_period      | 📅 Train: 2019-07-26 ถึง 2022-05-18 (1028 วัน, 245 records)
2025-07-13 17:35:38 | INFO     | log_data_period      | 📅 Val: 2022-05-18 ถึง 2022-10-24 (160 วัน, 81 records)
2025-07-13 17:35:38 | INFO     | log_data_period      | 📅 Test: 2022-10-24 ถึง 2025-05-07 (927 วัน, 83 records)
2025-07-13 17:35:38 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 17:35:38 | INFO     | main                 | 📊 DataFrame: 220 columns, 74718 rows
2025-07-13 17:35:38 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 409 rows
2025-07-13 17:35:38 | INFO     | main                 | 🎯 Features: 40 features
2025-07-13 17:35:38 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M30\030_EURGBP_target_autocorrelation.png
2025-07-13 17:35:38 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:35:38 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:35:38 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:35:38 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:35:39 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.66s)
2025-07-13 17:35:39 | INFO     | main                 | ============================================================
2025-07-13 17:35:39 | INFO     | main                 | 📄 ประมวลผลไฟล์ 3/8: CSV_Files_Fixed/EURUSD_M30_FIXED.csv
2025-07-13 17:35:39 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 17:35:39 | INFO     | main                 | ============================================================
2025-07-13 17:35:39 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=EURUSD, Timeframe=30
2025-07-13 17:35:39 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 17:35:39 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/030_EURUSD
2025-07-13 17:35:39 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: EURUSD
2025-07-13 17:35:39 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 17:37:29 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 109.81s)
2025-07-13 17:37:29 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 17:37:29 | INFO     | log_data_period      | 📅 Train: 2019-07-26 ถึง 2023-02-06 (1292 วัน, 774 records)
2025-07-13 17:37:29 | INFO     | log_data_period      | 📅 Val: 2023-02-06 ถึง 2023-12-18 (316 วัน, 258 records)
2025-07-13 17:37:29 | INFO     | log_data_period      | 📅 Test: 2023-12-29 ถึง 2025-07-10 (560 วัน, 258 records)
2025-07-13 17:37:29 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 17:37:29 | INFO     | main                 | 📊 DataFrame: 220 columns, 74718 rows
2025-07-13 17:37:29 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 1290 rows
2025-07-13 17:37:29 | INFO     | main                 | 🎯 Features: 41 features
2025-07-13 17:37:29 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M30\030_EURUSD_target_autocorrelation.png
2025-07-13 17:37:29 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:37:29 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:37:29 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:37:29 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:37:29 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.64s)
2025-07-13 17:37:29 | INFO     | main                 | ============================================================
2025-07-13 17:37:29 | INFO     | main                 | 📄 ประมวลผลไฟล์ 4/8: CSV_Files_Fixed/GBPUSD_M30_FIXED.csv
2025-07-13 17:37:29 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 17:37:29 | INFO     | main                 | ============================================================
2025-07-13 17:37:29 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=GBPUSD, Timeframe=30
2025-07-13 17:37:29 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 17:37:29 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/030_GBPUSD
2025-07-13 17:37:29 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: GBPUSD
2025-07-13 17:37:29 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 17:39:17 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 107.93s)
2025-07-13 17:39:17 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 17:39:17 | INFO     | log_data_period      | 📅 Train: 2019-07-16 ถึง 2022-12-21 (1255 วัน, 1134 records)
2025-07-13 17:39:17 | INFO     | log_data_period      | 📅 Val: 2022-12-23 ถึง 2024-02-05 (410 วัน, 378 records)
2025-07-13 17:39:17 | INFO     | log_data_period      | 📅 Test: 2024-02-06 ถึง 2025-07-10 (521 วัน, 378 records)
2025-07-13 17:39:17 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 17:39:17 | INFO     | main                 | 📊 DataFrame: 220 columns, 74718 rows
2025-07-13 17:39:17 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 1890 rows
2025-07-13 17:39:17 | INFO     | main                 | 🎯 Features: 31 features
2025-07-13 17:39:17 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M30\030_GBPUSD_target_autocorrelation.png
2025-07-13 17:39:17 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:39:17 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:39:18 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:39:18 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:39:18 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.65s)
2025-07-13 17:39:18 | INFO     | main                 | ============================================================
2025-07-13 17:39:18 | INFO     | main                 | 📄 ประมวลผลไฟล์ 5/8: CSV_Files_Fixed/GOLD_M30_FIXED.csv
2025-07-13 17:39:18 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 17:39:18 | INFO     | main                 | ============================================================
2025-07-13 17:39:18 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=GOLD, Timeframe=30
2025-07-13 17:39:18 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 17:39:18 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/030_GOLD
2025-07-13 17:39:18 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: GOLD
2025-07-13 17:39:18 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 17:41:04 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 106.35s)
2025-07-13 17:41:04 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 17:41:04 | INFO     | log_data_period      | 📅 Train: 2019-07-10 ถึง 2023-02-10 (1312 วัน, 1441 records)
2025-07-13 17:41:04 | INFO     | log_data_period      | 📅 Val: 2023-02-13 ถึง 2024-02-15 (368 วัน, 480 records)
2025-07-13 17:41:04 | INFO     | log_data_period      | 📅 Test: 2024-02-16 ถึง 2025-07-10 (511 วัน, 481 records)
2025-07-13 17:41:04 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 17:41:04 | INFO     | main                 | 📊 DataFrame: 220 columns, 70985 rows
2025-07-13 17:41:04 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 2402 rows
2025-07-13 17:41:04 | INFO     | main                 | 🎯 Features: 22 features
2025-07-13 17:41:05 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M30\030_GOLD_target_autocorrelation.png
2025-07-13 17:41:05 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:41:05 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:41:05 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:41:05 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:41:05 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.78s)
2025-07-13 17:41:05 | INFO     | main                 | ============================================================
2025-07-13 17:41:05 | INFO     | main                 | 📄 ประมวลผลไฟล์ 6/8: CSV_Files_Fixed/NZDUSD_M30_FIXED.csv
2025-07-13 17:41:05 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 17:41:05 | INFO     | main                 | ============================================================
2025-07-13 17:41:05 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=NZDUSD, Timeframe=30
2025-07-13 17:41:05 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 17:41:05 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/030_NZDUSD
2025-07-13 17:41:05 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: NZDUSD
2025-07-13 17:41:05 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 17:42:51 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 106.10s)
2025-07-13 17:42:51 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 17:42:51 | INFO     | log_data_period      | 📅 Train: 2019-08-06 ถึง 2022-09-27 (1149 วัน, 543 records)
2025-07-13 17:42:51 | INFO     | log_data_period      | 📅 Val: 2022-09-27 ถึง 2023-09-21 (360 วัน, 181 records)
2025-07-13 17:42:51 | INFO     | log_data_period      | 📅 Test: 2023-09-21 ถึง 2025-07-04 (653 วัน, 182 records)
2025-07-13 17:42:51 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 17:42:51 | INFO     | main                 | 📊 DataFrame: 220 columns, 74714 rows
2025-07-13 17:42:51 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 906 rows
2025-07-13 17:42:51 | INFO     | main                 | 🎯 Features: 38 features
2025-07-13 17:42:52 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M30\030_NZDUSD_target_autocorrelation.png
2025-07-13 17:42:52 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:42:52 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:42:52 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:42:52 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:42:52 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.67s)
2025-07-13 17:42:52 | INFO     | main                 | ============================================================
2025-07-13 17:42:52 | INFO     | main                 | 📄 ประมวลผลไฟล์ 7/8: CSV_Files_Fixed/USDCAD_M30_FIXED.csv
2025-07-13 17:42:52 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 17:42:52 | INFO     | main                 | ============================================================
2025-07-13 17:42:52 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=USDCAD, Timeframe=30
2025-07-13 17:42:52 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 17:42:52 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/030_USDCAD
2025-07-13 17:42:52 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: USDCAD
2025-07-13 17:42:52 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 17:44:40 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 107.47s)
2025-07-13 17:44:40 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 17:44:40 | INFO     | log_data_period      | 📅 Train: 2019-07-11 ถึง 2023-01-11 (1281 วัน, 948 records)
2025-07-13 17:44:40 | INFO     | log_data_period      | 📅 Val: 2023-01-11 ถึง 2024-02-02 (388 วัน, 316 records)
2025-07-13 17:44:40 | INFO     | log_data_period      | 📅 Test: 2024-02-02 ถึง 2025-07-03 (518 วัน, 317 records)
2025-07-13 17:44:40 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 17:44:40 | INFO     | main                 | 📊 DataFrame: 220 columns, 74718 rows
2025-07-13 17:44:40 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 1581 rows
2025-07-13 17:44:40 | INFO     | main                 | 🎯 Features: 41 features
2025-07-13 17:44:40 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M30\030_USDCAD_target_autocorrelation.png
2025-07-13 17:44:40 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:44:40 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:44:40 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:44:40 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:44:41 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.81s)
2025-07-13 17:44:41 | INFO     | main                 | ============================================================
2025-07-13 17:44:41 | INFO     | main                 | 📄 ประมวลผลไฟล์ 8/8: CSV_Files_Fixed/USDJPY_M30_FIXED.csv
2025-07-13 17:44:41 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 17:44:41 | INFO     | main                 | ============================================================
2025-07-13 17:44:41 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=USDJPY, Timeframe=30
2025-07-13 17:44:41 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 17:44:41 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/030_USDJPY
2025-07-13 17:44:41 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: USDJPY
2025-07-13 17:44:41 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 17:46:35 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 114.51s)
2025-07-13 17:46:35 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 17:46:35 | INFO     | log_data_period      | 📅 Train: 2019-07-11 ถึง 2023-12-04 (1608 วัน, 758 records)
2025-07-13 17:46:35 | INFO     | log_data_period      | 📅 Val: 2023-12-05 ถึง 2024-09-13 (284 วัน, 252 records)
2025-07-13 17:46:35 | INFO     | log_data_period      | 📅 Test: 2024-09-16 ถึง 2025-07-10 (298 วัน, 254 records)
2025-07-13 17:46:35 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 17:46:35 | INFO     | main                 | 📊 DataFrame: 220 columns, 74718 rows
2025-07-13 17:46:35 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 1264 rows
2025-07-13 17:46:35 | INFO     | main                 | 🎯 Features: 33 features
2025-07-13 17:46:35 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M30\030_USDJPY_target_autocorrelation.png
2025-07-13 17:46:35 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:46:35 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:46:35 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:46:35 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:46:36 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.66s)
2025-07-13 17:46:36 | INFO     | main                 | ==================================================
2025-07-13 17:46:36 | INFO     | main                 | 🤖 เริ่มการเทรนโมเดล LightGBM
2025-07-13 17:46:36 | INFO     | main                 | 📊 Training samples: 758
2025-07-13 17:46:36 | INFO     | main                 | 🎯 Features: 33
2025-07-13 17:46:36 | INFO     | main                 | ==================================================
2025-07-13 17:46:36 | INFO     | main                 | 🔄 ใช้ Single Model Architecture (แบบเดิม)
2025-07-13 17:57:35 | INFO     | main                 | ✅ เทรน Single Model สำเร็จ (⏱️ 658.94s)
2025-07-13 17:57:35 | INFO     | log_model_performance | 📈 Model Performance - USDJPY 30
2025-07-13 17:57:35 | INFO     | log_model_performance |   📊 timeframe: 30
2025-07-13 17:57:35 | INFO     | log_model_performance |   📊 accuracy: 0.6457
2025-07-13 17:57:35 | INFO     | log_model_performance |   📊 auc: 0.8401
2025-07-13 17:57:35 | INFO     | log_model_performance |   📊 f1: 0.6418
2025-07-13 17:57:35 | INFO     | log_model_performance |   📊 precision: 0
2025-07-13 17:57:35 | INFO     | log_model_performance |   📊 recall: 0
2025-07-13 17:57:35 | INFO     | log_model_performance |   📊 confusion_matrix: [[74  3 10  1  8]
 [ 0 16  3  1  0]
 [25  3 29  8 16]
 [ 1  0  3 10  0]
 [ 2  1  5  0 35]]
2025-07-13 17:57:35 | INFO     | log_model_performance |   📊 auc_pr: 0.5000
2025-07-13 17:57:35 | ERROR    | log_error_with_traceback | ❌ ERROR: ข้อผิดพลาดใน main function
2025-07-13 17:57:35 | ERROR    | log_error_with_traceback | Exception type: UnboundLocalError
2025-07-13 17:57:35 | ERROR    | log_error_with_traceback | Exception message: cannot access local variable 'e' where it is not associated with a value
2025-07-13 17:57:35 | ERROR    | log_error_with_traceback | Full traceback:
2025-07-13 17:57:35 | ERROR    | log_error_with_traceback |   Traceback (most recent call last):
2025-07-13 17:57:35 | ERROR    | log_error_with_traceback |     File "d:\test_gold\python_LightGBM_16_Signal.py", line 12501, in main
2025-07-13 17:57:35 | ERROR    | log_error_with_traceback |       "Status": f"Failed - Post-Train Process: {str(e)}",
2025-07-13 17:57:35 | ERROR    | log_error_with_traceback |                                                     ^
2025-07-13 17:57:35 | ERROR    | log_error_with_traceback |   UnboundLocalError: cannot access local variable 'e' where it is not associated with a value
2025-07-13 17:57:35 | INFO     | log_function_end     | ✅ เสร็จสิ้น main (⏱️ 1531.52s)
2025-07-13 17:57:35 | INFO     | log_function_start   | 🏗️ เปิดใช้งาน main
2025-07-13 17:57:35 | INFO     | main                 | 📁 ใช้ output_folder สำหรับกลุ่ม M60: LightGBM_Single/results/M60
2025-07-13 17:57:35 | INFO     | main                 | ⏱️ เริ่มการประมวลผล - รอบที่ 1/1
2025-07-13 17:57:35 | INFO     | main                 | ============================================================
2025-07-13 17:57:35 | INFO     | main                 | 📄 ประมวลผลไฟล์ 1/8: CSV_Files_Fixed/AUDUSD_H1_FIXED.csv
2025-07-13 17:57:35 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 17:57:35 | INFO     | main                 | ============================================================
2025-07-13 17:57:35 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=AUDUSD, Timeframe=60
2025-07-13 17:57:35 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 17:57:35 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/060_AUDUSD
2025-07-13 17:57:35 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: AUDUSD
2025-07-13 17:57:35 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 17:58:35 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 59.58s)
2025-07-13 17:58:35 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 17:58:35 | INFO     | log_data_period      | 📅 Train: 2018-04-19 ถึง 2022-09-14 (1610 วัน, 602 records)
2025-07-13 17:58:35 | INFO     | log_data_period      | 📅 Val: 2022-09-14 ถึง 2023-08-16 (337 วัน, 200 records)
2025-07-13 17:58:35 | INFO     | log_data_period      | 📅 Test: 2023-08-17 ถึง 2025-07-09 (693 วัน, 202 records)
2025-07-13 17:58:35 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 17:58:35 | INFO     | main                 | 📊 DataFrame: 220 columns, 46198 rows
2025-07-13 17:58:35 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 1004 rows
2025-07-13 17:58:35 | INFO     | main                 | 🎯 Features: 39 features
2025-07-13 17:58:35 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M60\060_AUDUSD_target_autocorrelation.png
2025-07-13 17:58:35 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:58:35 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:58:35 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:58:35 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 17:58:35 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.65s)
2025-07-13 17:58:35 | INFO     | main                 | ============================================================
2025-07-13 17:58:35 | INFO     | main                 | 📄 ประมวลผลไฟล์ 2/8: CSV_Files_Fixed/EURGBP_H1_FIXED.csv
2025-07-13 17:58:35 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 17:58:35 | INFO     | main                 | ============================================================
2025-07-13 17:58:35 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=EURGBP, Timeframe=60
2025-07-13 17:58:35 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 17:58:35 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/060_EURGBP
2025-07-13 17:58:35 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: EURGBP
2025-07-13 17:58:35 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 18:00:21 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 106.20s)
2025-07-13 18:00:21 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 18:00:21 | INFO     | log_data_period      | 📅 Train: 2013-07-12 ถึง 2019-08-23 (2234 วัน, 758 records)
2025-07-13 18:00:21 | INFO     | log_data_period      | 📅 Val: 2019-08-23 ถึง 2021-10-19 (789 วัน, 252 records)
2025-07-13 18:00:21 | INFO     | log_data_period      | 📅 Test: 2021-10-25 ถึง 2025-05-13 (1297 วัน, 254 records)
2025-07-13 18:00:21 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 18:00:21 | INFO     | main                 | 📊 DataFrame: 220 columns, 74505 rows
2025-07-13 18:00:21 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 1264 rows
2025-07-13 18:00:21 | INFO     | main                 | 🎯 Features: 32 features
2025-07-13 18:00:22 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M60\060_EURGBP_target_autocorrelation.png
2025-07-13 18:00:22 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:00:22 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:00:22 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:00:22 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:00:22 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.64s)
2025-07-13 18:00:22 | INFO     | main                 | ============================================================
2025-07-13 18:00:22 | INFO     | main                 | 📄 ประมวลผลไฟล์ 3/8: CSV_Files_Fixed/EURUSD_H1_FIXED.csv
2025-07-13 18:00:22 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 18:00:22 | INFO     | main                 | ============================================================
2025-07-13 18:00:22 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=EURUSD, Timeframe=60
2025-07-13 18:00:22 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 18:00:22 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/060_EURUSD
2025-07-13 18:00:22 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: EURUSD
2025-07-13 18:00:22 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 18:02:15 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 112.66s)
2025-07-13 18:02:15 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 18:02:15 | INFO     | log_data_period      | 📅 Train: 2013-07-15 ถึง 2020-07-03 (2546 วัน, 1185 records)
2025-07-13 18:02:15 | INFO     | log_data_period      | 📅 Val: 2020-07-03 ถึง 2022-10-07 (827 วัน, 395 records)
2025-07-13 18:02:15 | INFO     | log_data_period      | 📅 Test: 2022-10-10 ถึง 2025-06-23 (988 วัน, 395 records)
2025-07-13 18:02:15 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 18:02:15 | INFO     | main                 | 📊 DataFrame: 220 columns, 74511 rows
2025-07-13 18:02:15 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 1975 rows
2025-07-13 18:02:15 | INFO     | main                 | 🎯 Features: 43 features
2025-07-13 18:02:15 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M60\060_EURUSD_target_autocorrelation.png
2025-07-13 18:02:15 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:02:15 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:02:15 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:02:15 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:02:16 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.67s)
2025-07-13 18:02:16 | INFO     | main                 | ============================================================
2025-07-13 18:02:16 | INFO     | main                 | 📄 ประมวลผลไฟล์ 4/8: CSV_Files_Fixed/GBPUSD_H1_FIXED.csv
2025-07-13 18:02:16 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 18:02:16 | INFO     | main                 | ============================================================
2025-07-13 18:02:16 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=GBPUSD, Timeframe=60
2025-07-13 18:02:16 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 18:02:16 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/060_GBPUSD
2025-07-13 18:02:16 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: GBPUSD
2025-07-13 18:02:16 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 18:04:05 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 109.10s)
2025-07-13 18:04:05 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 18:04:05 | INFO     | log_data_period      | 📅 Train: 2013-07-16 ถึง 2020-09-17 (2621 วัน, 1433 records)
2025-07-13 18:04:05 | INFO     | log_data_period      | 📅 Val: 2020-09-17 ถึง 2023-01-17 (853 วัน, 477 records)
2025-07-13 18:04:05 | INFO     | log_data_period      | 📅 Test: 2023-02-01 ถึง 2025-07-11 (892 วัน, 479 records)
2025-07-13 18:04:05 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 18:04:05 | INFO     | main                 | 📊 DataFrame: 220 columns, 74505 rows
2025-07-13 18:04:05 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 2389 rows
2025-07-13 18:04:05 | INFO     | main                 | 🎯 Features: 36 features
2025-07-13 18:04:05 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M60\060_GBPUSD_target_autocorrelation.png
2025-07-13 18:04:05 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:04:05 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:04:05 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:04:05 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:04:06 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.67s)
2025-07-13 18:04:06 | INFO     | main                 | ============================================================
2025-07-13 18:04:06 | INFO     | main                 | 📄 ประมวลผลไฟล์ 5/8: CSV_Files_Fixed/GOLD_H1_FIXED.csv
2025-07-13 18:04:06 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 18:04:06 | INFO     | main                 | ============================================================
2025-07-13 18:04:06 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=GOLD, Timeframe=60
2025-07-13 18:04:06 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 18:04:06 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/060_GOLD
2025-07-13 18:04:06 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: GOLD
2025-07-13 18:04:06 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 18:05:50 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 104.69s)
2025-07-13 18:05:50 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 18:05:50 | INFO     | log_data_period      | 📅 Train: 2013-07-18 ถึง 2021-01-28 (2752 วัน, 1203 records)
2025-07-13 18:05:50 | INFO     | log_data_period      | 📅 Val: 2021-01-29 ถึง 2023-02-20 (753 วัน, 401 records)
2025-07-13 18:05:50 | INFO     | log_data_period      | 📅 Test: 2023-02-20 ถึง 2025-07-10 (872 วัน, 401 records)
2025-07-13 18:05:50 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 18:05:50 | INFO     | main                 | 📊 DataFrame: 220 columns, 71982 rows
2025-07-13 18:05:50 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 2005 rows
2025-07-13 18:05:50 | INFO     | main                 | 🎯 Features: 33 features
2025-07-13 18:05:50 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M60\060_GOLD_target_autocorrelation.png
2025-07-13 18:05:51 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:05:51 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:05:51 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:05:51 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:05:51 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.67s)
2025-07-13 18:05:51 | INFO     | main                 | ============================================================
2025-07-13 18:05:51 | INFO     | main                 | 📄 ประมวลผลไฟล์ 6/8: CSV_Files_Fixed/NZDUSD_H1_FIXED.csv
2025-07-13 18:05:51 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 18:05:51 | INFO     | main                 | ============================================================
2025-07-13 18:05:51 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=NZDUSD, Timeframe=60
2025-07-13 18:05:51 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 18:05:51 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/060_NZDUSD
2025-07-13 18:05:51 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: NZDUSD
2025-07-13 18:05:51 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 18:06:51 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 59.44s)
2025-07-13 18:06:51 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 18:06:51 | INFO     | log_data_period      | 📅 Train: 2018-04-24 ถึง 2022-06-30 (1529 วัน, 558 records)
2025-07-13 18:06:51 | INFO     | log_data_period      | 📅 Val: 2022-07-05 ถึง 2023-08-11 (403 วัน, 186 records)
2025-07-13 18:06:51 | INFO     | log_data_period      | 📅 Test: 2023-08-14 ถึง 2025-07-11 (698 วัน, 187 records)
2025-07-13 18:06:51 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 18:06:51 | INFO     | main                 | 📊 DataFrame: 220 columns, 46197 rows
2025-07-13 18:06:51 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 931 rows
2025-07-13 18:06:51 | INFO     | main                 | 🎯 Features: 34 features
2025-07-13 18:06:51 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M60\060_NZDUSD_target_autocorrelation.png
2025-07-13 18:06:51 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:06:51 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:06:51 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:06:51 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:06:51 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.68s)
2025-07-13 18:06:51 | INFO     | main                 | ============================================================
2025-07-13 18:06:51 | INFO     | main                 | 📄 ประมวลผลไฟล์ 7/8: CSV_Files_Fixed/USDCAD_H1_FIXED.csv
2025-07-13 18:06:51 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 18:06:51 | INFO     | main                 | ============================================================
2025-07-13 18:06:51 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=USDCAD, Timeframe=60
2025-07-13 18:06:51 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 18:06:51 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/060_USDCAD
2025-07-13 18:06:51 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: USDCAD
2025-07-13 18:06:51 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 18:08:39 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 107.23s)
2025-07-13 18:08:39 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 18:08:39 | INFO     | log_data_period      | 📅 Train: 2013-07-12 ถึง 2020-12-14 (2713 วัน, 1251 records)
2025-07-13 18:08:39 | INFO     | log_data_period      | 📅 Val: 2020-12-15 ถึง 2023-02-02 (780 วัน, 417 records)
2025-07-13 18:08:39 | INFO     | log_data_period      | 📅 Test: 2023-02-02 ถึง 2025-07-04 (884 วัน, 418 records)
2025-07-13 18:08:39 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 18:08:39 | INFO     | main                 | 📊 DataFrame: 220 columns, 74517 rows
2025-07-13 18:08:39 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 2086 rows
2025-07-13 18:08:39 | INFO     | main                 | 🎯 Features: 37 features
2025-07-13 18:08:39 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M60\060_USDCAD_target_autocorrelation.png
2025-07-13 18:08:39 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:08:39 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:08:39 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:08:39 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:08:39 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.68s)
2025-07-13 18:08:39 | INFO     | main                 | ============================================================
2025-07-13 18:08:39 | INFO     | main                 | 📄 ประมวลผลไฟล์ 8/8: CSV_Files_Fixed/USDJPY_H1_FIXED.csv
2025-07-13 18:08:39 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 18:08:39 | INFO     | main                 | ============================================================
2025-07-13 18:08:39 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=USDJPY, Timeframe=60
2025-07-13 18:08:39 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 18:08:39 | INFO     | main                 | 📁 สร้างโฟลเดอร์ timeframe_models: LightGBM_Single/models/060_USDJPY
2025-07-13 18:08:39 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: USDJPY
2025-07-13 18:08:39 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=6
2025-07-13 18:10:26 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 106.99s)
2025-07-13 18:10:26 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 18:10:26 | INFO     | log_data_period      | 📅 Train: 2013-07-12 ถึง 2020-05-01 (2486 วัน, 1015 records)
2025-07-13 18:10:26 | INFO     | log_data_period      | 📅 Val: 2020-05-04 ถึง 2023-06-06 (1129 วัน, 338 records)
2025-07-13 18:10:26 | INFO     | log_data_period      | 📅 Test: 2023-06-07 ถึง 2025-07-10 (765 วัน, 340 records)
2025-07-13 18:10:26 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 18:10:26 | INFO     | main                 | 📊 DataFrame: 220 columns, 74509 rows
2025-07-13 18:10:26 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 1693 rows
2025-07-13 18:10:26 | INFO     | main                 | 🎯 Features: 34 features
2025-07-13 18:10:27 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M60\060_USDJPY_target_autocorrelation.png
2025-07-13 18:10:27 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:10:27 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:10:27 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:10:27 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 18:10:27 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.67s)
2025-07-13 18:10:27 | INFO     | main                 | ==================================================
2025-07-13 18:10:27 | INFO     | main                 | 🤖 เริ่มการเทรนโมเดล LightGBM
2025-07-13 18:10:27 | INFO     | main                 | 📊 Training samples: 1015
2025-07-13 18:10:27 | INFO     | main                 | 🎯 Features: 34
2025-07-13 18:10:27 | INFO     | main                 | ==================================================
2025-07-13 18:10:27 | INFO     | main                 | 🔄 ใช้ Single Model Architecture (แบบเดิม)
2025-07-13 20:22:45 | INFO     | setup_logging        | ================================================================================
2025-07-13 20:22:45 | INFO     | setup_logging        | 🚀 เริ่มต้นระบบ Trading Model
2025-07-13 20:22:45 | INFO     | setup_logging        | 📁 Log file: logs\trading_model_20250713.log
2025-07-13 20:22:45 | INFO     | setup_logging        | 📊 Max file size: 10 MB
2025-07-13 20:22:45 | INFO     | setup_logging        | 🔄 Backup count: 5
2025-07-13 20:22:45 | INFO     | setup_logging        | ================================================================================
2025-07-13 20:22:45 | INFO     | log_function_start   | 🏗️ เปิดใช้งาน main
2025-07-13 20:22:45 | INFO     | main                 | 📁 ใช้ output_folder สำหรับกลุ่ม M60: LightGBM_Single/results/M60
2025-07-13 20:22:45 | INFO     | main                 | ⏱️ เริ่มการประมวลผล - รอบที่ 1/1
2025-07-13 20:22:45 | INFO     | main                 | ============================================================
2025-07-13 20:22:45 | INFO     | main                 | 📄 ประมวลผลไฟล์ 1/1: CSV_Files_Fixed/GOLD_H1_FIXED.csv
2025-07-13 20:22:45 | INFO     | main                 | 🔄 รอบที่ 1/1
2025-07-13 20:22:45 | INFO     | main                 | ============================================================
2025-07-13 20:22:45 | INFO     | main                 | 📊 ข้อมูลไฟล์: Symbol=GOLD, Timeframe=60
2025-07-13 20:22:45 | INFO     | main                 | 🤖 โมเดล: LightGBM
2025-07-13 20:22:45 | INFO     | main                 | 🎯 ตั้งค่า current_processing_symbol: GOLD
2025-07-13 20:22:45 | INFO     | main                 | ⚙️ พารามิเตอร์: threshold=0.500, nBars_SL=3
2025-07-13 20:24:34 | INFO     | main                 | 📊 โหลดและประมวลผลข้อมูลเสร็จสิ้น (⏱️ 109.38s)
2025-07-13 20:24:34 | INFO     | main                 | 📈 ช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล:
2025-07-13 20:24:34 | INFO     | log_data_period      | 📅 Train: 2013-07-18 ถึง 2021-01-25 (2749 วัน, 1722 records)
2025-07-13 20:24:34 | INFO     | log_data_period      | 📅 Val: 2021-01-25 ถึง 2023-03-09 (774 วัน, 574 records)
2025-07-13 20:24:34 | INFO     | log_data_period      | 📅 Test: 2023-03-22 ถึง 2025-07-10 (842 วัน, 575 records)
2025-07-13 20:24:34 | INFO     | main                 | 🔍 ตรวจสอบข้อมูลหลังการประมวลผล:
2025-07-13 20:24:34 | INFO     | main                 | 📊 DataFrame: 220 columns, 71982 rows
2025-07-13 20:24:34 | INFO     | main                 | 💰 Trade DataFrame: 239 columns, 2871 rows
2025-07-13 20:24:34 | INFO     | main                 | 🎯 Features: 37 features
2025-07-13 20:24:34 | INFO     | main                 | 📈 บันทึกกราฟ Autocorrelation: LightGBM_Single/results/M60\060_GOLD_target_autocorrelation.png
2025-07-13 20:24:35 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 20:24:35 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 20:24:35 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 20:24:35 | INFO     | update               | Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 20:24:35 | INFO     | main                 | 📊 วิเคราะห์ SL/TP และเวลาเสร็จสิ้น (⏱️ 0.70s)
2025-07-13 20:24:35 | INFO     | main                 | ==================================================
2025-07-13 20:24:35 | INFO     | main                 | 🤖 เริ่มการเทรนโมเดล LightGBM
2025-07-13 20:24:35 | INFO     | main                 | 📊 Training samples: 1722
2025-07-13 20:24:35 | INFO     | main                 | 🎯 Features: 37
2025-07-13 20:24:35 | INFO     | main                 | ==================================================
2025-07-13 20:24:35 | INFO     | main                 | 🔄 ใช้ Single Model Architecture (แบบเดิม)
2025-07-13 20:53:55 | INFO     | main                 | ✅ เทรน Single Model สำเร็จ (⏱️ 1760.07s)
2025-07-13 20:53:55 | INFO     | log_model_performance | 📈 Model Performance - GOLD 60
2025-07-13 20:53:55 | INFO     | log_model_performance |   📊 timeframe: 60
2025-07-13 20:53:55 | INFO     | log_model_performance |   📊 accuracy: 0.6574
2025-07-13 20:53:55 | INFO     | log_model_performance |   📊 auc: 0.8472
2025-07-13 20:53:55 | INFO     | log_model_performance |   📊 f1: 0.6493
2025-07-13 20:53:55 | INFO     | log_model_performance |   📊 precision: 0
2025-07-13 20:53:55 | INFO     | log_model_performance |   📊 recall: 0
2025-07-13 20:53:55 | INFO     | log_model_performance |   📊 confusion_matrix: [[167   3  25   0  10]
 [  1  17   0   0   0]
 [ 59  13  90   5  43]
 [  0   0   1  11   1]
 [  4   0  26   6  93]]
2025-07-13 20:53:55 | INFO     | log_model_performance |   📊 auc_pr: 0.5000
2025-07-13 20:53:55 | ERROR    | log_error_with_traceback | ❌ ERROR: ข้อผิดพลาดใน main function
2025-07-13 20:53:55 | ERROR    | log_error_with_traceback | Exception type: UnboundLocalError
2025-07-13 20:53:55 | ERROR    | log_error_with_traceback | Exception message: cannot access local variable 'e' where it is not associated with a value
2025-07-13 20:53:55 | ERROR    | log_error_with_traceback | Full traceback:
2025-07-13 20:53:55 | ERROR    | log_error_with_traceback |   Traceback (most recent call last):
2025-07-13 20:53:55 | ERROR    | log_error_with_traceback |     File "d:\test_gold\python_LightGBM_16_Signal.py", line 12506, in main
2025-07-13 20:53:55 | ERROR    | log_error_with_traceback |       "Status": f"Failed - Post-Train Process: {str(e)}",
2025-07-13 20:53:55 | ERROR    | log_error_with_traceback |                                                     ^
2025-07-13 20:53:55 | ERROR    | log_error_with_traceback |   UnboundLocalError: cannot access local variable 'e' where it is not associated with a value
2025-07-13 20:53:55 | INFO     | log_function_end     | ✅ เสร็จสิ้น main (⏱️ 1870.31s)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 Trading Configuration Center
ศูนย์กลางการตั้งค่าสำหรับระบบเทรด LightGBM

ไฟล์นี้เป็นแหล่งเดียวของการตั้งค่าทั้งหมด
- python_LightGBM_16_Signal.py (Training)
- python_to_mt5_WebRequest_server_12_Signal.py (Production)
"""

import os
import MetaTrader5 as mt5

# ==============================================
# 📊 BASIC CONFIGURATION
# ==============================================

# การตั้งค่าพื้นฐาน
PLOT_FILE = False  # True/False ตั้งค่าเพื่อกำหนดว่าจะพล็อตกราฟหรือไม่
STEPS_TO_CALCULATING = False
STEPS_TO_DO = True
SAVE_FILE = True

# ==============================================
# 💹 TRADING PARAMETERS
# ==============================================

# เงื่อนไขการเทรดหลัก (ใช้ร่วมกันทั้งสองไฟล์)
INPUT_INITIAL_THRESHOLD = 0.50  # Threshold สำหรับการตัดสินใจ
INPUT_RSI_LEVEL_IN = 35         # RSI level สำหรับเข้าเทรด
INPUT_RSI_LEVEL_OUT = 30        # RSI level สำหรับออกเทรด
INPUT_STOP_LOSS_ATR = 1.5       # Stop Loss multiplier (ATR)
INPUT_TAKE_PROFIT = 2.5         # Take Profit multiplier (ATR)
INPUT_PULL_BACK = 0.40          # Pull back threshold

# High-Quality Entry Filters
MIN_ATR_THRESHOLD = 0.0008       # ATR ขั้นต่ำเพื่อหลีกเลี่ยง low volatility
MIN_VOLUME_MULTIPLIER = 1.2      # Volume ต้องมากกว่า average อย่างน้อย 20%
TREND_CONFIRMATION_PERIODS = 3   # จำนวนแท่งที่ต้องยืนยัน trend

# ==============================================
# 🤖 MODEL CONFIGURATION
# ==============================================

# Minimum Learning Requirements
MIN_TRAINING_SAMPLES = 200       # จำนวนตัวอย่างขั้นต่ำสำหรับเทรน
MIN_POSITIVE_SAMPLES = 20        # จำนวนตัวอย่าง positive ขั้นต่ำ
MIN_MODEL_ACCURACY = 0.60        # Accuracy ขั้นต่ำที่ยอมรับได้
MIN_MODEL_AUC = 0.70             # AUC ขั้นต่ำที่ยอมรับได้
MIN_WIN_RATE_TARGET = 0.40       # Win Rate เป้าหมายขั้นต่ำ

# Training Configuration
NUM_TRAINING_ROUNDS = 1          # จำนวนรอบการเทรน
DO_HYPERPARAMETER_TUNING = True  # เปิด/ปิด hyperparameter tuning

# Architecture Configuration
USE_MULTI_MODEL_ARCHITECTURE = True   # เปิด/ปิด Multi-Model Architecture
USE_MULTICLASS_TARGET = True          # เปิด/ปิด Multi-class classification

# ==============================================
# 💰 PROFIT THRESHOLDS
# ==============================================

PROFIT_THRESHOLDS = {
    'strong_buy': 60,    # Strong Buy threshold (points)
    'weak_buy': 20,      # Weak Buy threshold (points)
    'weak_sell': 20,     # Weak Sell threshold (points)
    'strong_sell': 60    # Strong Sell threshold (points)
}

# Class mapping for multi-class target
CLASS_MAPPING = {
    0: 'strong_sell',    # ขาดทุนมาก
    1: 'weak_sell',      # ขาดทุนปานกลาง
    2: 'hold',           # พอใจ/เท่าทุน
    3: 'weak_buy',       # กำไรปานกลาง
    4: 'strong_buy'      # กำไรมาก
}

# ==============================================
# 🏦 SYMBOL INFORMATION
# ==============================================

SYMBOL_INFO = {
    "GOLD":   {"Spread": 25, "Digits": 2, "Points": 0.01, "Swap_Long": 0, "Swap_Short": 0},
    "AUDUSD": {"Spread": 15, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "EURGBP": {"Spread": 15, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "EURUSD": {"Spread": 10, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "GBPUSD": {"Spread": 15, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "NZDUSD": {"Spread": 20, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "USDCAD": {"Spread": 15, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "USDJPY": {"Spread": 10, "Digits": 3, "Points": 0.001, "Swap_Long": 0, "Swap_Short": 0}
}

# ==============================================
# ⏰ TIMEFRAME MAPPING
# ==============================================

# สำหรับ Training (python_LightGBM_16_Signal.py)
TIMEFRAME_MAP = {
    "M1": 1, "M5": 5, "M15": 15, "M30": 30, 
    "H1": 60, "H2": 120, "H4": 240, "D1": 1440
}

# สำหรับ MT5 Server (python_to_mt5_WebRequest_server_12_Signal.py)
MT5_TIMEFRAME_MAP = {
    "PERIOD_M1": mt5.TIMEFRAME_M1,
    "PERIOD_M2": mt5.TIMEFRAME_M2,
    "PERIOD_M3": mt5.TIMEFRAME_M3,
    "PERIOD_M4": mt5.TIMEFRAME_M4,
    "PERIOD_M5": mt5.TIMEFRAME_M5,
    "PERIOD_M6": mt5.TIMEFRAME_M6,
    "PERIOD_M10": mt5.TIMEFRAME_M10,
    "PERIOD_M12": mt5.TIMEFRAME_M12,
    "PERIOD_M15": mt5.TIMEFRAME_M15,
    "PERIOD_M20": mt5.TIMEFRAME_M20,
    "PERIOD_M30": mt5.TIMEFRAME_M30,
    "PERIOD_H1": mt5.TIMEFRAME_H1,
    "PERIOD_H2": mt5.TIMEFRAME_H2,
    "PERIOD_H3": mt5.TIMEFRAME_H3,
    "PERIOD_H4": mt5.TIMEFRAME_H4,
    "PERIOD_H6": mt5.TIMEFRAME_H6,
    "PERIOD_H8": mt5.TIMEFRAME_H8,
    "PERIOD_H12": mt5.TIMEFRAME_H12,
    "PERIOD_D1": mt5.TIMEFRAME_D1,
    "PERIOD_W1": mt5.TIMEFRAME_W1,
    "PERIOD_MN1": mt5.TIMEFRAME_MN1
}

# Timeframe Code Mapping
TIMEFRAME_CODE_MAP = {
    "PERIOD_M1": 1, "PERIOD_M2": 5, "PERIOD_M3": 15, "PERIOD_M30": 30, 
    "PERIOD_H1": 60, "PERIOD_H4": 240
}

# ==============================================
# 📁 FOLDER CONFIGURATION
# ==============================================

# Model Folders
TEST_MODEL_FOLDER = "LightGBM_Model"
SINGLE_MODEL_FOLDER = "LightGBM_Single"
MULTI_MODEL_FOLDER = "LightGBM_Multi"

def get_test_folder():
    """ได้โฟลเดอร์ที่ใช้ตาม architecture"""
    return MULTI_MODEL_FOLDER if USE_MULTI_MODEL_ARCHITECTURE else SINGLE_MODEL_FOLDER

def get_output_folder():
    """ได้โฟลเดอร์ output"""
    return f"{get_test_folder()}/results"

# ==============================================
# 🌐 SERVER CONFIGURATION (สำหรับ MT5 Server เท่านั้น)
# ==============================================

# HTTP Server Settings
HTTP_PORT = 54321
HTTP_HOST = '127.0.0.1'

# Telegram Settings
TELEGRAM_OPEN = True
TELEGRAM_TOKEN = '**********************************************'
TELEGRAM_CHAT_ID = 6546140292

# ==============================================
# 📋 DATA CONFIGURATION
# ==============================================

# Test Groups (CSV Files)
TEST_GROUPS = {
    "M30": [
        "CSV_Files_Fixed/AUDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/EURGBP_M30_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GBPUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GOLD_M30_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_M30_FIXED.csv"
    ],
    "M60": [
        "CSV_Files_Fixed/AUDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/EURGBP_H1_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/GOLD_H1_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_H1_FIXED.csv"
    ]
}

# ==============================================
# 🔧 UTILITY FUNCTIONS
# ==============================================

def create_required_folders():
    """สร้างโฟลเดอร์ที่จำเป็น"""
    folders = [
        TEST_MODEL_FOLDER,
        get_test_folder(),
        get_output_folder(),
        f"{get_test_folder()}/models",
        f"{get_test_folder()}/thresholds",
        f"{get_test_folder()}/feature_importance"
    ]
    
    for folder in folders:
        if not os.path.exists(folder):
            os.makedirs(folder)
            print(f"📁 สร้างโฟลเดอร์: {folder}")

def get_symbol_info(symbol):
    """ได้ข้อมูล symbol"""
    return SYMBOL_INFO.get(symbol, SYMBOL_INFO["EURUSD"])  # Default to EURUSD

def print_config_summary():
    """แสดงสรุปการตั้งค่า"""
    print("🎯 Trading Configuration Summary")
    print("=" * 50)
    print(f"📊 Multi-Model Architecture: {USE_MULTI_MODEL_ARCHITECTURE}")
    print(f"🎯 Multi-class Target: {USE_MULTICLASS_TARGET}")
    print(f"📁 Test Folder: {get_test_folder()}")
    print(f"🔧 Hyperparameter Tuning: {DO_HYPERPARAMETER_TUNING}")
    print(f"💹 Initial Threshold: {INPUT_INITIAL_THRESHOLD}")
    print(f"📈 RSI Levels: In={INPUT_RSI_LEVEL_IN}, Out={INPUT_RSI_LEVEL_OUT}")
    print(f"🛡️ Risk Management: SL={INPUT_STOP_LOSS_ATR}, TP={INPUT_TAKE_PROFIT}")
    print("=" * 50)

if __name__ == "__main__":
    print_config_summary()
    create_required_folders()

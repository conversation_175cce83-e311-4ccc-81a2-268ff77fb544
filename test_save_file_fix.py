#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข Save_File variable error

Created: 2025-01-11
Author: AI Assistant
"""

import os
import sys

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_save_file_variable():
    """
    ทดสอบการเข้าถึงตัวแปร Save_File
    """
    print("🔍 ทดสอบการเข้าถึงตัวแปร Save_File")
    print("="*50)
    
    try:
        # Import ตัวแปร global
        from python_LightGBM_16_Signal import Save_File, test_folder
        
        print(f"✅ Save_File = {Save_File}")
        print(f"✅ test_folder = {test_folder}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_optimization_section():
    """
    ทดสอบส่วนที่มีปัญหา optimization
    """
    print("\n🎯 ทดสอบส่วน optimization ที่มีปัญหา")
    print("="*50)
    
    try:
        # Import ฟังก์ชันที่ต้องการ
        from python_LightGBM_16_Signal import (
            parse_filename,
            run_multi_model_optimization,
            Save_File,
            test_folder
        )
        
        print(f"✅ Import สำเร็จ")
        print(f"📊 Save_File = {Save_File}")
        print(f"📊 test_folder = {test_folder}")
        
        # ทดสอบ parse_filename
        test_file = "CSV_Files_Fixed/GOLD_H1_FIXED.csv"
        file_info = parse_filename(test_file)
        symbol = file_info["Name_Currency"]
        timeframe = file_info["Timeframe_Currency"]
        
        print(f"✅ parse_filename ทำงานได้: {symbol}, {timeframe}")
        
        # จำลองการทำงานของส่วน optimization
        optimization_results = {
            f"{symbol}_{timeframe}": {
                'symbol': symbol,
                'timeframe': timeframe,
                'scenarios': ['trend_following', 'counter_trend'],
                'optimal_thresholds': {'trend_following': 0.5, 'counter_trend': 0.5},
                'optimal_nbars': {'trend_following': 6, 'counter_trend': 6},
                'validation_samples': 14367
            }
        }
        
        # ทดสอบการบันทึกไฟล์ (ส่วนที่มีปัญหา)
        if Save_File and optimization_results:
            optimization_summary_file = f"{test_folder}/optimization_summary.json"
            print(f"✅ ทดสอบการบันทึกไฟล์: {optimization_summary_file}")
            
            # สร้างโฟลเดอร์ถ้าไม่มี
            os.makedirs(test_folder, exist_ok=True)
            
            try:
                import json
                with open(optimization_summary_file, 'w') as f:
                    # แปลง numpy types เป็น Python types สำหรับ JSON serialization
                    serializable_results = {}
                    for key, result in optimization_results.items():
                        serializable_results[key] = {
                            'symbol': result['symbol'],
                            'timeframe': result['timeframe'],
                            'scenarios': result['scenarios'],
                            'optimal_thresholds': {k: float(v) if isinstance(v, (int, float)) else str(v) 
                                                 for k, v in result['optimal_thresholds'].items()},
                            'optimal_nbars': {k: int(v) if isinstance(v, (int, float)) else str(v) 
                                            for k, v in result['optimal_nbars'].items()},
                            'validation_samples': result['validation_samples']
                        }
                    
                    json.dump(serializable_results, f, indent=2, ensure_ascii=False)
                
                print(f"✅ บันทึกสรุปการหา optimal parameters สำเร็จ")
                
                # ตรวจสอบไฟล์ที่สร้าง
                if os.path.exists(optimization_summary_file):
                    file_size = os.path.getsize(optimization_summary_file)
                    print(f"📊 ไฟล์ที่สร้าง: {file_size} bytes")
                    
                    # อ่านและแสดงเนื้อหา
                    with open(optimization_summary_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        print(f"📄 เนื้อหาไฟล์ (100 ตัวอักษรแรก): {content[:100]}...")
                
                return True
                
            except Exception as e:
                print(f"❌ ไม่สามารถบันทึกสรุปการหา optimal parameters: {e}")
                return False
        else:
            print(f"⚠️ ไม่บันทึกไฟล์เพราะ Save_File={Save_File} หรือ optimization_results ว่าง")
            return False
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_run_main_analysis_simulation():
    """
    จำลองการทำงานของ run_main_analysis ส่วนที่มีปัญหา
    """
    print("\n🚀 จำลองการทำงานของ run_main_analysis")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import (
            Save_File,
            test_folder,
            USE_MULTI_MODEL_ARCHITECTURE
        )
        
        print(f"📊 USE_MULTI_MODEL_ARCHITECTURE = {USE_MULTI_MODEL_ARCHITECTURE}")
        print(f"📊 Save_File = {Save_File}")
        print(f"📊 test_folder = {test_folder}")
        
        # จำลองการทำงานของส่วน optimization
        if USE_MULTI_MODEL_ARCHITECTURE:
            print(f"🎯 เริ่มการหา optimal parameters สำหรับ Multi-Model Architecture")
            
            # จำลอง optimization_results
            optimization_results = {
                'GOLD_60': {
                    'symbol': 'GOLD',
                    'timeframe': 60,
                    'scenarios': ['trend_following', 'counter_trend'],
                    'optimal_thresholds': {'trend_following': 0.5, 'counter_trend': 0.5},
                    'optimal_nbars': {'trend_following': 6, 'counter_trend': 6},
                    'validation_samples': 14367
                }
            }
            
            print(f"📊 สรุปผลการหา optimal parameters:")
            for key, result in optimization_results.items():
                print(f"   • {key}:")
                for scenario in result['scenarios']:
                    threshold = result['optimal_thresholds'].get(scenario, 'N/A')
                    nbars = result['optimal_nbars'].get(scenario, 'N/A')
                    print(f"     - {scenario}: threshold={threshold}, nBars_SL={nbars}")
            
            # ทดสอบส่วนที่มีปัญหา
            if Save_File and optimization_results:
                print(f"✅ เงื่อนไข Save_File and optimization_results ผ่าน")
                
                optimization_summary_file = f"{test_folder}/optimization_summary.json"
                print(f"📁 จะบันทึกไฟล์: {optimization_summary_file}")
                
                return True
            else:
                print(f"❌ เงื่อนไข Save_File and optimization_results ไม่ผ่าน")
                return False
        else:
            print(f"⚠️ USE_MULTI_MODEL_ARCHITECTURE = False, ข้าม optimization")
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🚀 ทดสอบการแก้ไข Save_File Variable Error")
    print("="*80)
    
    # Test 1: ทดสอบการเข้าถึงตัวแปร
    test1_success = test_save_file_variable()
    
    # Test 2: ทดสอบส่วน optimization
    test2_success = test_optimization_section()
    
    # Test 3: จำลอง run_main_analysis
    test3_success = test_run_main_analysis_simulation()
    
    print("\n" + "="*80)
    print("📊 สรุปผลการทดสอบ")
    print("="*80)
    
    print(f"✅ Test 1 - Save_File Variable: {'ผ่าน' if test1_success else 'ล้มเหลว'}")
    print(f"✅ Test 2 - Optimization Section: {'ผ่าน' if test2_success else 'ล้มเหลว'}")
    print(f"✅ Test 3 - run_main_analysis Simulation: {'ผ่าน' if test3_success else 'ล้มเหลว'}")
    
    overall_success = all([test1_success, test2_success, test3_success])
    
    if overall_success:
        print(f"\n🎉 การแก้ไข Save_File Variable Error สำเร็จ!")
        print(f"🚀 ระบบพร้อมรัน run_main_analysis() ได้แล้ว")
    else:
        print(f"\n⚠️ ยังมีปัญหาบางส่วนที่ต้องแก้ไข")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
สคริปต์สำหรับเปิดไฟล์สรุปการเทรดที่บันทึกเป็น .txt
"""

import os
import subprocess
import glob
from datetime import datetime

def list_trading_summary_files():
    """
    แสดงรายการไฟล์สรุปการเทรดทั้งหมด
    """
    results_folder = "Test_LightGBM/results"
    
    if not os.path.exists(results_folder):
        print(f"❌ ไม่พบโฟลเดอร์: {results_folder}")
        return []
    
    # หาไฟล์ .txt ทั้งหมด
    txt_files = glob.glob(os.path.join(results_folder, "*.txt"))
    
    # แยกประเภทไฟล์
    trading_summaries = []
    daily_schedule = []
    other_files = []
    
    for file_path in txt_files:
        filename = os.path.basename(file_path)
        if "trading_summary" in filename:
            trading_summaries.append(file_path)
        elif "daily_trading_schedule" in filename:
            daily_schedule.append(file_path)
        else:
            other_files.append(file_path)
    
    return {
        'trading_summaries': sorted(trading_summaries),
        'daily_schedule': sorted(daily_schedule),
        'other_files': sorted(other_files)
    }

def open_file_with_notepad(file_path):
    """
    เปิดไฟล์ด้วย Notepad
    """
    try:
        subprocess.run(['notepad.exe', file_path], check=True)
        print(f"✅ เปิดไฟล์: {os.path.basename(file_path)}")
    except subprocess.CalledProcessError:
        print(f"❌ ไม่สามารถเปิดไฟล์: {file_path}")
    except FileNotFoundError:
        print("❌ ไม่พบ Notepad ในระบบ")

def display_file_info(file_path):
    """
    แสดงข้อมูลของไฟล์
    """
    if os.path.exists(file_path):
        file_size = os.path.getsize(file_path)
        mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
        print(f"  📄 {os.path.basename(file_path)}")
        print(f"      ขนาด: {file_size} bytes")
        print(f"      แก้ไขล่าสุด: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")

def show_file_preview(file_path, lines=10):
    """
    แสดงตัวอย่างเนื้อหาไฟล์
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content_lines = f.readlines()
            
        print(f"\n📖 ตัวอย่างเนื้อหา ({lines} บรรทัดแรก):")
        print("-" * 50)
        for i, line in enumerate(content_lines[:lines], 1):
            print(f"{i:2d}: {line.rstrip()}")
        
        if len(content_lines) > lines:
            print(f"... และอีก {len(content_lines) - lines} บรรทัด")
            
    except Exception as e:
        print(f"❌ ไม่สามารถอ่านไฟล์: {str(e)}")

def main():
    """
    ฟังก์ชันหลัก
    """
    print("📁 เครื่องมือจัดการไฟล์สรุปการเทรด")
    print("=" * 60)
    
    files = list_trading_summary_files()
    
    if not any(files.values()):
        print("❌ ไม่พบไฟล์สรุปการเทรดใดๆ")
        print("💡 กรุณาเทรนโมเดลก่อนเพื่อสร้างไฟล์สรุป")
        return
    
    while True:
        print("\n📋 เมนูหลัก:")
        print("1. แสดงรายการไฟล์สรุปการเทรดแต่ละ Symbol")
        print("2. แสดงไฟล์สรุปการเทรดรายวัน")
        print("3. แสดงไฟล์อื่นๆ")
        print("4. เปิดไฟล์ทั้งหมดด้วย Notepad")
        print("5. รีเฟรชรายการไฟล์")
        print("0. ออกจากโปรแกรม")
        
        choice = input("\nเลือกตัวเลือก (0-5): ").strip()
        
        if choice == "1":
            print("\n📊 ไฟล์สรุปการเทรดแต่ละ Symbol:")
            print("-" * 40)
            if files['trading_summaries']:
                for i, file_path in enumerate(files['trading_summaries'], 1):
                    print(f"\n{i}. ", end="")
                    display_file_info(file_path)
                
                try:
                    file_choice = input(f"\nเลือกไฟล์ที่ต้องการเปิด (1-{len(files['trading_summaries'])}) หรือ 'p' เพื่อดูตัวอย่าง: ").strip()
                    
                    if file_choice.lower() == 'p':
                        preview_choice = input(f"เลือกไฟล์ที่ต้องการดูตัวอย่าง (1-{len(files['trading_summaries'])}): ").strip()
                        if preview_choice.isdigit() and 1 <= int(preview_choice) <= len(files['trading_summaries']):
                            show_file_preview(files['trading_summaries'][int(preview_choice)-1])
                    elif file_choice.isdigit() and 1 <= int(file_choice) <= len(files['trading_summaries']):
                        open_file_with_notepad(files['trading_summaries'][int(file_choice)-1])
                        
                except (ValueError, IndexError):
                    print("❌ ตัวเลือกไม่ถูกต้อง")
            else:
                print("❌ ไม่พบไฟล์สรุปการเทรดแต่ละ Symbol")
        
        elif choice == "2":
            print("\n📅 ไฟล์สรุปการเทรดรายวัน:")
            print("-" * 40)
            if files['daily_schedule']:
                for file_path in files['daily_schedule']:
                    display_file_info(file_path)
                    
                action = input("\nต้องการ (o)เปิดไฟล์ หรือ (p)ดูตัวอย่าง? ").strip().lower()
                if action == 'o':
                    open_file_with_notepad(files['daily_schedule'][0])
                elif action == 'p':
                    show_file_preview(files['daily_schedule'][0])
            else:
                print("❌ ไม่พบไฟล์สรุปการเทรดรายวัน")
        
        elif choice == "3":
            print("\n📄 ไฟล์อื่นๆ:")
            print("-" * 40)
            if files['other_files']:
                for i, file_path in enumerate(files['other_files'], 1):
                    print(f"\n{i}. ", end="")
                    display_file_info(file_path)
                
                try:
                    file_choice = input(f"\nเลือกไฟล์ที่ต้องการเปิด (1-{len(files['other_files'])}) หรือ 'p' เพื่อดูตัวอย่าง: ").strip()
                    
                    if file_choice.lower() == 'p':
                        preview_choice = input(f"เลือกไฟล์ที่ต้องการดูตัวอย่าง (1-{len(files['other_files'])}): ").strip()
                        if preview_choice.isdigit() and 1 <= int(preview_choice) <= len(files['other_files']):
                            show_file_preview(files['other_files'][int(preview_choice)-1])
                    elif file_choice.isdigit() and 1 <= int(file_choice) <= len(files['other_files']):
                        open_file_with_notepad(files['other_files'][int(file_choice)-1])
                        
                except (ValueError, IndexError):
                    print("❌ ตัวเลือกไม่ถูกต้อง")
            else:
                print("❌ ไม่พบไฟล์อื่นๆ")
        
        elif choice == "4":
            print("\n🚀 เปิดไฟล์ทั้งหมดด้วย Notepad...")
            all_files = files['trading_summaries'] + files['daily_schedule'] + files['other_files']
            for file_path in all_files:
                open_file_with_notepad(file_path)
        
        elif choice == "5":
            print("\n🔄 รีเฟรชรายการไฟล์...")
            files = list_trading_summary_files()
            print("✅ รีเฟรชเสร็จสิ้น")
        
        elif choice == "0":
            print("\n👋 ขอบคุณที่ใช้งาน!")
            break
        
        else:
            print("❌ ตัวเลือกไม่ถูกต้อง กรุณาเลือกใหม่")

if __name__ == "__main__":
    main()

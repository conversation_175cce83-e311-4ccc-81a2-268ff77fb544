#!/usr/bin/env python3
import pickle

# โหลดค่า threshold และ nBars_SL สำหรับ GOLD M30
files_to_check = [
    'LightGBM_Multi/thresholds/030_GOLD_trend_following_optimal_threshold.pkl',
    'LightGBM_Multi/thresholds/030_GOLD_trend_following_optimal_nBars_SL.pkl',
    'LightGBM_Multi/thresholds/030_GOLD_counter_trend_optimal_threshold.pkl',
    'LightGBM_Multi/thresholds/030_GOLD_counter_trend_optimal_nBars_SL.pkl'
]

print('📊 GOLD M30 Optimal Parameters:')
print('=' * 50)

for file_path in files_to_check:
    try:
        with open(file_path, 'rb') as f:
            value = pickle.load(f)
        
        scenario = 'Trend Following' if 'trend_following' in file_path else 'Counter Trend'
        param_type = 'Threshold' if 'threshold' in file_path else 'nBars_SL'
        
        print(f'{scenario} {param_type}: {value}')
        
    except Exception as e:
        print(f'❌ Error loading {file_path}: {e}')

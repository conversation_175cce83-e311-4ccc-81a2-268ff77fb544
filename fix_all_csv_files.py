#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
แก้ไข CSV Files ทั้งหมดที่มีปัญหา Tab Separator
และเปลี่ยนชื่อไฟล์ให้เป็นรูปแบบที่ใช้งานง่าย
"""

import os
import pandas as pd
from datetime import datetime

def fix_csv_files():
    """แก้ไข CSV files ทั้งหมดและเปลี่ยนชื่อ"""

    # กำหนดโฟลเดอร์ต้นฉบับ
    source_dir = "MT5_250711"

    # กำหนดกลุ่มไฟล์ที่ต้องแก้ไข (ไฟล์ใหม่ล่าสุด)
    test_groups = {
        "M30": [
            "AUDUSD#_M30_201907080000_202507112330.csv",
            "EURGBP#_M30_201907080000_202507112330.csv",
            "EURUSD#_M30_201907080000_202507112330.csv",
            "GBPUSD#_M30_201907080000_202507112330.csv",
            "GOLD#_M30_201907080100_202507112330.csv",
            "NZDUSD#_M30_201907080000_202507112330.csv",
            "USDCAD#_M30_201907080000_202507112330.csv",
            "USDJPY#_M30_201907080000_202507112330.csv"
        ],
        "M60": [
            "AUDUSD#_H1_201307080000_202507112300.csv",
            "EURGBP#_H1_201307080000_202507112300.csv",
            "EURUSD#_H1_201307080000_202507112300.csv",
            "GBPUSD#_H1_201307080000_202507112300.csv",
            "GOLD#_H1_201307080000_202507112300.csv",
            "NZDUSD#_H1_201307080000_202507112300.csv",
            "USDCAD#_H1_201307080000_202507112300.csv",
            "USDJPY#_H1_201307080000_202507112300.csv"
        ]
    }
    
    print("🔧 เริ่มแก้ไข CSV Files ทั้งหมด")
    print("=" * 80)
    
    total_files = sum(len(files) for files in test_groups.values())
    processed_files = 0
    success_files = 0
    error_files = []
    
    # สร้างโฟลเดอร์สำหรับไฟล์ที่แก้ไขแล้ว
    fixed_dir = "CSV_Files_Fixed"
    if not os.path.exists(fixed_dir):
        os.makedirs(fixed_dir)
        print(f"📁 สร้างโฟลเดอร์: {fixed_dir}")
    
    for timeframe, files in test_groups.items():
        print(f"\n📊 ประมวลผล {timeframe} ({len(files)} ไฟล์)")
        print("-" * 60)
        
        for file_name in files:
            processed_files += 1
            print(f"\n[{processed_files}/{total_files}] 🔄 ประมวลผล: {file_name}")

            try:
                # สร้าง path เต็มไปยังไฟล์ต้นฉบับ
                source_file_path = os.path.join(source_dir, file_name)

                # ตรวจสอบว่าไฟล์มีอยู่หรือไม่
                if not os.path.exists(source_file_path):
                    print(f"   ❌ ไม่พบไฟล์: {source_file_path}")
                    error_files.append(f"{file_name} - ไม่พบไฟล์")
                    continue


                # อ่านไฟล์และตรวจสอบรูปแบบ
                print(f"   📖 อ่านไฟล์...")

                # ลองอ่านแบบ comma separator ก่อน
                df_comma = pd.read_csv(source_file_path, header=None, nrows=5)

                # ลองอ่านแบบ tab separator
                df_tab = pd.read_csv(source_file_path, sep='\t', header=None, nrows=5)
                
                print(f"   🔍 ตรวจสอบรูปแบบ:")
                print(f"      Comma separator: {df_comma.shape[1]} columns")
                print(f"      Tab separator: {df_tab.shape[1]} columns")
                
                # ตัดสินใจว่าใช้รูปแบบไหน
                if df_tab.shape[1] > df_comma.shape[1] and df_tab.shape[1] >= 8:
                    # ใช้ tab separator
                    print(f"   ✅ ใช้ Tab separator ({df_tab.shape[1]} columns)")
                    df_full = pd.read_csv(source_file_path, sep='\t', header=None)
                elif df_comma.shape[1] >= 8:
                    # ใช้ comma separator (ไฟล์ปกติ)
                    print(f"   ✅ ใช้ Comma separator ({df_comma.shape[1]} columns)")
                    df_full = pd.read_csv(source_file_path, header=None)
                else:
                    print(f"   ❌ ไม่สามารถระบุรูปแบบได้")
                    error_files.append(f"{file_name} - รูปแบบไม่ถูกต้อง")
                    continue
                
                # ตั้งชื่อ columns
                if df_full.shape[1] >= 8:
                    df_full.columns = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'TickVol', 'Vol'] + \
                                    [f'Col_{i}' for i in range(8, df_full.shape[1])]
                
                print(f"   📊 ข้อมูลที่อ่านได้: {df_full.shape[0]} rows, {df_full.shape[1]} columns")
                
                # สร้างชื่อไฟล์ใหม่
                symbol = file_name.split('#')[0]  # เอาเฉพาะชื่อ symbol
                if 'M30' in file_name:
                    new_name = f"{symbol}_M30_FIXED.csv"
                elif 'H1' in file_name:
                    new_name = f"{symbol}_H1_FIXED.csv"
                else:
                    new_name = f"{symbol}_FIXED.csv"
                
                # บันทึกไฟล์ที่แก้ไขแล้ว
                fixed_path = os.path.join(fixed_dir, new_name)
                df_full.to_csv(fixed_path, index=False)
                
                print(f"   💾 บันทึกไฟล์ที่แก้ไขแล้ว: {fixed_path}")
                
                # ตรวจสอบผลลัพธ์
                df_check = pd.read_csv(fixed_path)
                print(f"   ✅ ตรวจสอบไฟล์ใหม่: {df_check.shape[0]} rows, {df_check.shape[1]} columns")
                
                # แสดงตัวอย่างข้อมูล
                print(f"   📋 ตัวอย่างข้อมูล 3 แถวแรก:")
                print(df_check.head(3).to_string(index=False))
                
                success_files += 1
                print(f"   🎉 สำเร็จ!")
                
            except Exception as e:
                print(f"   ❌ เกิดข้อผิดพลาด: {str(e)}")
                error_files.append(f"{file_name} - {str(e)}")
    
    # สรุปผลการประมวลผล
    print(f"\n🎯 สรุปผลการประมวลผล")
    print("=" * 80)
    print(f"📊 สถิติ:")
    print(f"   • ไฟล์ทั้งหมด: {total_files}")
    print(f"   • ประมวลผลสำเร็จ: {success_files}")
    print(f"   • ประมวลผลไม่สำเร็จ: {len(error_files)}")
    print(f"   • อัตราความสำเร็จ: {(success_files/total_files)*100:.1f}%")
    
    if error_files:
        print(f"\n❌ ไฟล์ที่มีปัญหา:")
        for error in error_files:
            print(f"   • {error}")
    
    print(f"\n📁 ไฟล์ที่แก้ไขแล้วอยู่ใน: {fixed_dir}/")
    
    return success_files, error_files

def create_file_mapping():
    """สร้างไฟล์ mapping ระหว่างชื่อเดิมและชื่อใหม่"""

    # กำหนดกลุ่มไฟล์ที่ต้องแก้ไข (ไฟล์ใหม่ล่าสุด)
    test_groups = {
        "M30": [
            "AUDUSD#_M30_201907080000_202507112330.csv",
            "EURGBP#_M30_201907080000_202507112330.csv",
            "EURUSD#_M30_201907080000_202507112330.csv",
            "GBPUSD#_M30_201907080000_202507112330.csv",
            "GOLD#_M30_201907080100_202507112330.csv",
            "NZDUSD#_M30_201907080000_202507112330.csv",
            "USDCAD#_M30_201907080000_202507112330.csv",
            "USDJPY#_M30_201907080000_202507112330.csv"
        ],
        "M60": [
            "AUDUSD#_H1_201307080000_202507112300.csv",
            "EURGBP#_H1_201307080000_202507112300.csv",
            "EURUSD#_H1_201307080000_202507112300.csv",
            "GBPUSD#_H1_201307080000_202507112300.csv",
            "GOLD#_H1_201307080000_202507112300.csv",
            "NZDUSD#_H1_201307080000_202507112300.csv",
            "USDCAD#_H1_201307080000_202507112300.csv",
            "USDJPY#_H1_201307080000_202507112300.csv"
        ]
    }
    
    mapping = {}
    
    for _, files in test_groups.items():
        for file_name in files:
            symbol = file_name.split('#')[0]
            if 'M30' in file_name:
                new_name = f"{symbol}_M30_FIXED.csv"
            elif 'H1' in file_name:
                new_name = f"{symbol}_H1_FIXED.csv"
            else:
                new_name = f"{symbol}_FIXED.csv"
            
            mapping[file_name] = new_name
    
    # บันทึก mapping
    mapping_file = "file_name_mapping.txt"
    with open(mapping_file, 'w', encoding='utf-8') as f:
        f.write("File Name Mapping\n")
        f.write("=" * 50 + "\n")
        f.write(f"Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        for old_name, new_name in mapping.items():
            f.write(f"{old_name} -> {new_name}\n")
    
    print(f"📋 บันทึก File Mapping ไปยัง: {mapping_file}")
    return mapping

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 แก้ไข CSV Files และเปลี่ยนชื่อ")
    print("=" * 80)
    print("🎯 วัตถุประสงค์:")
    print("   • แก้ไขปัญหา Tab Separator ใน CSV files")
    print("   • เปลี่ยนชื่อไฟล์ให้ใช้งานง่าย")
    print("   • เตรียมไฟล์สำหรับเทรนโมเดล LightGBM")
    print("=" * 80)
    
    # สร้าง file mapping
    create_file_mapping()

    # แก้ไขไฟล์ทั้งหมด
    success_count, _ = fix_csv_files()

    # แสดงคำแนะนำการใช้งาน
    print(f"\n💡 คำแนะนำการใช้งาน:")
    print("=" * 80)
    print("1. ไฟล์ที่แก้ไขแล้วอยู่ในโฟลเดอร์ 'CSV_Files_Fixed/'")
    print("2. ตรวจสอบ 'file_name_mapping.txt' เพื่อดูการเปลี่ยนชื่อ")
    print("3. อัปเดต python_LightGBM_16_Signal.py ให้ใช้ไฟล์ใหม่")
    print("4. ไฟล์ต้นฉบับอยู่ในโฟลเดอร์ 'MT5_250711/'")
    print("5. ไฟล์ที่แก้ไขแล้วพร้อมใช้งานกับโมเดล LightGBM")
    
    if success_count > 0:
        print(f"\n🎉 แก้ไขสำเร็จ {success_count} ไฟล์!")
        print("✅ พร้อมใช้งานไฟล์ที่แก้ไขแล้ว")
    else:
        print(f"\n❌ ไม่สามารถแก้ไขไฟล์ใดได้")
        print("🔍 ตรวจสอบข้อผิดพลาดด้านบน")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการทำงานของ MT5 WebRequest Server ทั้ง 2 ระบบ:
1. Single Model Architecture (USE_MULTI_MODEL_ARCHITECTURE = False)
2. Multi-Model Architecture (USE_MULTI_MODEL_ARCHITECTURE = True)
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

def test_architecture_switching():
    """ทดสอบการเปลี่ยนระหว่าง Single Model และ Multi-Model Architecture"""
    print("🔄 ทดสอบการเปลี่ยน Architecture...")
    
    try:
        # ทดสอบ Multi-Model Architecture
        from python_LightGBM_16_Signal import USE_MULTI_MODEL_ARCHITECTURE
        print(f"📊 ปัจจุบันใช้ Multi-Model Architecture: {USE_MULTI_MODEL_ARCHITECTURE}")
        
        if USE_MULTI_MODEL_ARCHITECTURE:
            print("✅ ระบบตั้งค่าเป็น Multi-Model Architecture")
            expected_base_path = r'D:\test_gold\LightGBM_Multi\models'
            expected_threshold_path = r'D:\test_gold\LightGBM_Multi\thresholds'
        else:
            print("✅ ระบบตั้งค่าเป็น Single Model Architecture")
            expected_base_path = r'D:\test_gold\LightGBM_Single\models'
            expected_threshold_path = r'D:\test_gold\LightGBM_Single\thresholds'
        
        print(f"📁 Expected Model Path: {expected_base_path}")
        print(f"📁 Expected Threshold Path: {expected_threshold_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def test_file_structure():
    """ทดสอบโครงสร้างไฟล์สำหรับทั้ง 2 ระบบ"""
    print("\n🔍 ทดสอบโครงสร้างไฟล์...")
    
    symbol = "GOLD"
    timeframe = 60
    
    # ทดสอบ Multi-Model Architecture
    print(f"\n📊 ตรวจสอบ Multi-Model Architecture:")
    multi_model_base = r"LightGBM_Multi"
    
    # ตรวจสอบโฟลเดอร์หลัก
    if os.path.exists(multi_model_base):
        print(f"✅ พบโฟลเดอร์ {multi_model_base}")
        
        # ตรวจสอบโฟลเดอร์ models
        models_folder = os.path.join(multi_model_base, "models")
        if os.path.exists(models_folder):
            print(f"✅ พบโฟลเดอร์ {models_folder}")
            
            # ตรวจสอบ scenarios
            scenarios = ["trend_following", "counter_trend"]
            for scenario in scenarios:
                scenario_folder = os.path.join(models_folder, scenario)
                if os.path.exists(scenario_folder):
                    print(f"✅ พบโฟลเดอร์ {scenario_folder}")
                    
                    # ตรวจสอบไฟล์โมเดล
                    model_files = [
                        f"{timeframe:03d}_{symbol}_trained.pkl",
                        f"{timeframe:03d}_{symbol}_features.pkl",
                        f"{timeframe:03d}_{symbol}_scaler.pkl"
                    ]
                    
                    for file_name in model_files:
                        file_path = os.path.join(scenario_folder, file_name)
                        if os.path.exists(file_path):
                            print(f"  ✅ พบไฟล์ {file_name}")
                        else:
                            print(f"  ❌ ไม่พบไฟล์ {file_name}")
                else:
                    print(f"❌ ไม่พบโฟลเดอร์ {scenario_folder}")
        else:
            print(f"❌ ไม่พบโฟลเดอร์ {models_folder}")
        
        # ตรวจสอบโฟลเดอร์ thresholds
        thresholds_folder = os.path.join(multi_model_base, "thresholds")
        if os.path.exists(thresholds_folder):
            print(f"✅ พบโฟลเดอร์ {thresholds_folder}")
            
            # ตรวจสอบไฟล์ threshold
            threshold_files = [
                f"{timeframe:03d}_{symbol}_trend_following_optimal_threshold.pkl",
                f"{timeframe:03d}_{symbol}_trend_following_optimal_nBars_SL.pkl",
                f"{timeframe:03d}_{symbol}_counter_trend_optimal_threshold.pkl",
                f"{timeframe:03d}_{symbol}_counter_trend_optimal_nBars_SL.pkl",
                f"{timeframe:03d}_{symbol}_time_filters.pkl"
            ]
            
            for file_name in threshold_files:
                file_path = os.path.join(thresholds_folder, file_name)
                if os.path.exists(file_path):
                    print(f"  ✅ พบไฟล์ {file_name}")
                else:
                    print(f"  ❌ ไม่พบไฟล์ {file_name}")
        else:
            print(f"❌ ไม่พบโฟลเดอร์ {thresholds_folder}")
    else:
        print(f"❌ ไม่พบโฟลเดอร์ {multi_model_base}")
    
    # ทดสอบ Single Model Architecture
    print(f"\n📊 ตรวจสอบ Single Model Architecture:")
    single_model_base = r"LightGBM_Single"
    
    if os.path.exists(single_model_base):
        print(f"✅ พบโฟลเดอร์ {single_model_base}")
        
        # ตรวจสอบโฟลเดอร์ models
        models_folder = os.path.join(single_model_base, "models")
        if os.path.exists(models_folder):
            print(f"✅ พบโฟลเดอร์ {models_folder}")
            
            # ตรวจสอบโฟลเดอร์ symbol
            symbol_folder = os.path.join(models_folder, f"{timeframe:03d}_{symbol}")
            if os.path.exists(symbol_folder):
                print(f"✅ พบโฟลเดอร์ {symbol_folder}")
                
                # ตรวจสอบไฟล์โมเดล
                model_files = [
                    f"LightGBM_{timeframe:03d}_{symbol}_trained.pkl",
                    f"LightGBM_{timeframe:03d}_{symbol}_features.pkl",
                    f"LightGBM_{timeframe:03d}_{symbol}_scaler.pkl"
                ]
                
                for file_name in model_files:
                    file_path = os.path.join(symbol_folder, file_name)
                    if os.path.exists(file_path):
                        print(f"  ✅ พบไฟล์ {file_name}")
                    else:
                        print(f"  ❌ ไม่พบไฟล์ {file_name}")
            else:
                print(f"❌ ไม่พบโฟลเดอร์ {symbol_folder}")
        else:
            print(f"❌ ไม่พบโฟลเดอร์ {models_folder}")
        
        # ตรวจสอบโฟลเดอร์ thresholds
        thresholds_folder = os.path.join(single_model_base, "thresholds")
        if os.path.exists(thresholds_folder):
            print(f"✅ พบโฟลเดอร์ {thresholds_folder}")
            
            # ตรวจสอบไฟล์ threshold
            threshold_files = [
                f"{timeframe:03d}_{symbol}_optimal_threshold.pkl",
                f"{timeframe:03d}_{symbol}_optimal_nBars_SL.pkl",
                f"{timeframe:03d}_{symbol}_time_filters.pkl"
            ]
            
            for file_name in threshold_files:
                file_path = os.path.join(thresholds_folder, file_name)
                if os.path.exists(file_path):
                    print(f"  ✅ พบไฟล์ {file_name}")
                else:
                    print(f"  ❌ ไม่พบไฟล์ {file_name}")
        else:
            print(f"❌ ไม่พบโฟลเดอร์ {thresholds_folder}")
    else:
        print(f"❌ ไม่พบโฟลเดอร์ {single_model_base}")

def test_server_functions():
    """ทดสอบฟังก์ชันใน MT5 WebRequest Server"""
    print("\n🔍 ทดสอบฟังก์ชันใน Server...")
    
    try:
        # Import ฟังก์ชันจาก server
        import importlib.util
        spec = importlib.util.spec_from_file_location("server", "python_to_mt5_WebRequest_server_12_Signal.py")
        server_module = importlib.util.module_from_spec(spec)
        
        # ทดสอบการ import
        print("✅ Import server module สำเร็จ")
        
        # ทดสอบตัวแปรสำคัญ
        if hasattr(server_module, 'USE_MULTI_MODEL_ARCHITECTURE'):
            print(f"✅ พบตัวแปร USE_MULTI_MODEL_ARCHITECTURE")
        else:
            print(f"❌ ไม่พบตัวแปร USE_MULTI_MODEL_ARCHITECTURE")
        
        if hasattr(server_module, 'MODEL_BASE_PATH'):
            print(f"✅ พบตัวแปร MODEL_BASE_PATH")
        else:
            print(f"❌ ไม่พบตัวแปร MODEL_BASE_PATH")
        
        if hasattr(server_module, 'THRESHOLD_BASE_PATH'):
            print(f"✅ พบตัวแปร THRESHOLD_BASE_PATH")
        else:
            print(f"❌ ไม่พบตัวแปร THRESHOLD_BASE_PATH")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ server functions: {e}")
        return False

def test_path_consistency():
    """ทดสอบความสอดคล้องของ path ระหว่างระบบ"""
    print("\n🔍 ทดสอบความสอดคล้องของ path...")
    
    try:
        from python_LightGBM_16_Signal import USE_MULTI_MODEL_ARCHITECTURE, test_folder
        
        print(f"📊 USE_MULTI_MODEL_ARCHITECTURE: {USE_MULTI_MODEL_ARCHITECTURE}")
        print(f"📁 test_folder: {test_folder}")
        
        if USE_MULTI_MODEL_ARCHITECTURE:
            expected_model_path = f"{test_folder}/models"
            expected_threshold_path = f"{test_folder}/thresholds"
            print(f"✅ Multi-Model paths:")
            print(f"   Models: {expected_model_path}")
            print(f"   Thresholds: {expected_threshold_path}")
        else:
            expected_model_path = f"LightGBM_Single/models"
            expected_threshold_path = f"LightGBM_Single/thresholds"
            print(f"✅ Single Model paths:")
            print(f"   Models: {expected_model_path}")
            print(f"   Thresholds: {expected_threshold_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def main():
    """ฟังก์ชันหลักสำหรับทดสอบ"""
    print("🚀 เริ่มทดสอบ MT5 WebRequest Server ทั้ง 2 ระบบ")
    print("="*80)
    
    # ทดสอบการเปลี่ยน Architecture
    if not test_architecture_switching():
        print("❌ การทดสอบล้มเหลวที่ขั้นตอน Architecture switching")
        return
    
    # ทดสอบโครงสร้างไฟล์
    test_file_structure()
    
    # ทดสอบฟังก์ชันใน Server
    if not test_server_functions():
        print("❌ การทดสอบล้มเหลวที่ขั้นตอน Server functions")
        return
    
    # ทดสอบความสอดคล้องของ path
    if not test_path_consistency():
        print("❌ การทดสอบล้มเหลวที่ขั้นตอน Path consistency")
        return
    
    print("\n" + "="*80)
    print("✅ การทดสอบทั้งหมดเสร็จสิ้น!")
    print("📋 สรุป:")
    print("   🔄 Multi-Model Architecture: ตรวจสอบแล้ว")
    print("   📊 Single Model Architecture: ตรวจสอบแล้ว")
    print("   📁 File Structure: ตรวจสอบแล้ว")
    print("   🔧 Server Functions: ตรวจสอบแล้ว")
    print("   🎯 Path Consistency: ตรวจสอบแล้ว")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
เปรียบเทียบระบบ Hyperparameter Tuning เดิมและใหม่
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime

def analyze_hyperparameter_files():
    """วิเคราะห์ไฟล์ hyperparameter ที่มีอยู่"""
    
    print("🔍 วิเคราะห์ไฟล์ Hyperparameter ที่มีอยู่")
    print("="*60)
    
    results = {
        'old_system': [],
        'new_system': []
    }
    
    # ตรวจสอบโฟลเดอร์ hyperparameter
    hyper_dirs = {
        'LightGBM_Hyper_Multi': 'new_system',
        'LightGBM_Hyper_Single': 'old_system'
    }
    
    for hyper_dir, system_type in hyper_dirs.items():
        if os.path.exists(hyper_dir):
            print(f"\n📁 {hyper_dir} ({system_type}):")
            
            for symbol_folder in os.listdir(hyper_dir):
                symbol_path = os.path.join(hyper_dir, symbol_folder)
                
                if os.path.isdir(symbol_path):
                    files = [f for f in os.listdir(symbol_path) if f.endswith('_best_params.json')]
                    
                    for file in files:
                        file_path = os.path.join(symbol_path, file)
                        
                        try:
                            with open(file_path, 'r') as f:
                                data = json.load(f)
                            
                            # แยกข้อมูลจากชื่อไฟล์
                            parts = file.replace('_best_params.json', '').split('_')
                            timeframe = int(parts[0])
                            symbol = parts[1]
                            scenario = '_'.join(parts[2:]) if len(parts) > 2 else 'single'
                            
                            # ดึง parameters
                            params = data.get('best_params', data)
                            score = data.get('best_score', 0)
                            tuning_date = data.get('tuning_date', 'unknown')
                            
                            result_entry = {
                                'system': system_type,
                                'symbol': symbol,
                                'timeframe': timeframe,
                                'scenario': scenario,
                                'score': score,
                                'tuning_date': tuning_date,
                                'file': file,
                                'params': params
                            }
                            
                            results[system_type].append(result_entry)
                            
                            print(f"   ✅ {file}: Score={score:.4f}, Scenario={scenario}")
                            
                        except Exception as e:
                            print(f"   ❌ ไม่สามารถอ่าน {file}: {e}")
    
    return results

def compare_parameter_ranges(results):
    """เปรียบเทียบ parameter ranges ระหว่างระบบเดิมและใหม่"""
    
    print(f"\n📊 เปรียบเทียบ Parameter Ranges")
    print("="*60)
    
    # รวบรวม parameters จากทั้งสองระบบ
    old_params = []
    new_params = []
    
    for entry in results['old_system']:
        if entry['params']:
            old_params.append(entry['params'])
    
    for entry in results['new_system']:
        if entry['params']:
            new_params.append(entry['params'])
    
    # วิเคราะห์ parameter ranges
    param_keys = ['learning_rate', 'num_leaves', 'max_depth', 'min_data_in_leaf', 
                  'feature_fraction', 'bagging_fraction']
    
    print(f"\n📈 Parameter Ranges Comparison:")
    print("-" * 80)
    print(f"{'Parameter':<20} {'Old System':<25} {'New System':<25} {'Improvement':<10}")
    print("-" * 80)
    
    for param in param_keys:
        old_values = [p.get(param, 0) for p in old_params if p.get(param) is not None]
        new_values = [p.get(param, 0) for p in new_params if p.get(param) is not None]
        
        if old_values and new_values:
            old_range = f"{min(old_values):.3f}-{max(old_values):.3f}"
            new_range = f"{min(new_values):.3f}-{max(new_values):.3f}"
            
            old_std = np.std(old_values)
            new_std = np.std(new_values)
            
            improvement = "Better" if new_std > old_std else "Similar"
            
            print(f"{param:<20} {old_range:<25} {new_range:<25} {improvement:<10}")
    
    return old_params, new_params

def compare_scenario_differences(results):
    """เปรียบเทียบความแตกต่างระหว่าง scenarios"""
    
    print(f"\n🎯 เปรียบเทียบ Scenario Differences")
    print("="*60)
    
    # แยก parameters ตาม scenario
    trend_params = []
    counter_params = []
    
    for entry in results['new_system']:
        if entry['scenario'] == 'trend_following' and entry['params']:
            trend_params.append(entry['params'])
        elif entry['scenario'] == 'counter_trend' and entry['params']:
            counter_params.append(entry['params'])
    
    if trend_params and counter_params:
        param_keys = ['learning_rate', 'num_leaves', 'max_depth']
        
        print(f"\n📊 Scenario Parameter Comparison:")
        print("-" * 70)
        print(f"{'Parameter':<20} {'Trend Following':<20} {'Counter Trend':<20} {'Difference':<10}")
        print("-" * 70)
        
        for param in param_keys:
            trend_values = [p.get(param, 0) for p in trend_params if p.get(param) is not None]
            counter_values = [p.get(param, 0) for p in counter_params if p.get(param) is not None]
            
            if trend_values and counter_values:
                trend_mean = np.mean(trend_values)
                counter_mean = np.mean(counter_values)
                
                trend_std = np.std(trend_values)
                counter_std = np.std(counter_values)
                
                diff_pct = abs(counter_std - trend_std) / trend_std * 100 if trend_std > 0 else 0
                
                trend_text = f"{trend_mean:.3f}±{trend_std:.3f}"
                counter_text = f"{counter_mean:.3f}±{counter_std:.3f}"
                print(f"{param:<20} {trend_text:<20} {counter_text:<20} {diff_pct:.1f}%")
    
    else:
        print("❌ ไม่พบข้อมูล scenario เพียงพอสำหรับการเปรียบเทียบ")

def compare_performance_scores(results):
    """เปรียบเทียบ performance scores"""
    
    print(f"\n📈 เปรียบเทียบ Performance Scores")
    print("="*60)
    
    old_scores = [entry['score'] for entry in results['old_system'] if entry['score'] > 0]
    new_scores = [entry['score'] for entry in results['new_system'] if entry['score'] > 0]
    
    if old_scores and new_scores:
        print(f"\n📊 Score Statistics:")
        print(f"Old System: Mean={np.mean(old_scores):.4f}, Std={np.std(old_scores):.4f}, Count={len(old_scores)}")
        print(f"New System: Mean={np.mean(new_scores):.4f}, Std={np.std(new_scores):.4f}, Count={len(new_scores)}")
        
        improvement = (np.mean(new_scores) - np.mean(old_scores)) / np.mean(old_scores) * 100
        print(f"Improvement: {improvement:+.2f}%")
        
        # เปรียบเทียบตาม symbol
        print(f"\n📊 Score by Symbol:")
        symbols = set([entry['symbol'] for entry in results['old_system'] + results['new_system']])
        
        for symbol in symbols:
            old_symbol_scores = [entry['score'] for entry in results['old_system'] 
                               if entry['symbol'] == symbol and entry['score'] > 0]
            new_symbol_scores = [entry['score'] for entry in results['new_system'] 
                               if entry['symbol'] == symbol and entry['score'] > 0]
            
            if old_symbol_scores and new_symbol_scores:
                old_mean = np.mean(old_symbol_scores)
                new_mean = np.mean(new_symbol_scores)
                improvement = (new_mean - old_mean) / old_mean * 100
                
                print(f"  {symbol}: Old={old_mean:.4f}, New={new_mean:.4f}, Improvement={improvement:+.2f}%")
    
    else:
        print("❌ ไม่พบข้อมูล score เพียงพอสำหรับการเปรียบเทียบ")

def generate_summary_report(results):
    """สร้างรายงานสรุป"""
    
    print(f"\n📋 รายงานสรุปการเปรียบเทียบ")
    print("="*60)
    
    old_count = len(results['old_system'])
    new_count = len(results['new_system'])
    
    print(f"📊 จำนวนโมเดล:")
    print(f"  Old System: {old_count} models")
    print(f"  New System: {new_count} models")
    
    # นับ scenarios ใน new system
    scenarios = {}
    for entry in results['new_system']:
        scenario = entry['scenario']
        scenarios[scenario] = scenarios.get(scenario, 0) + 1
    
    if scenarios:
        print(f"\n🎯 Scenarios ใน New System:")
        for scenario, count in scenarios.items():
            print(f"  {scenario}: {count} models")
    
    # สรุปข้อดี
    print(f"\n✅ ข้อดีของระบบใหม่:")
    print(f"  1. ใช้ get_lgbm_params() เป็นฐาน - สอดคล้องกับ single model")
    print(f"  2. Scenario-specific parameter ranges - เหมาะสมกับลักษณะการทำงาน")
    print(f"  3. Symbol และ timeframe awareness - ปรับตามตลาด")
    print(f"  4. Parameter diversity ที่มีเหตุผล - ไม่ random")
    
    # แนะนำการใช้งาน
    print(f"\n💡 แนะนำการใช้งาน:")
    print(f"  1. ใช้ระบบใหม่สำหรับ multi-model training")
    print(f"  2. ตรวจสอบ parameter stability เป็นประจำ")
    print(f"  3. ปรับ parameter ranges ตามผลการวิเคราะห์")
    print(f"  4. เปรียบเทียบ performance ระหว่าง scenarios")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🏗️ Hyperparameter Systems Comparison")
    print("="*50)
    
    # วิเคราะห์ไฟล์ hyperparameter
    results = analyze_hyperparameter_files()
    
    # เปรียบเทียบ parameter ranges
    old_params, new_params = compare_parameter_ranges(results)
    
    # เปรียบเทียบความแตกต่างระหว่าง scenarios
    compare_scenario_differences(results)
    
    # เปรียบเทียบ performance scores
    compare_performance_scores(results)
    
    # สร้างรายงานสรุป
    generate_summary_report(results)
    
    print(f"\n✅ การเปรียบเทียบเสร็จสิ้น")

if __name__ == "__main__":
    main()

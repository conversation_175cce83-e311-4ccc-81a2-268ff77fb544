#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการเทรน Multi-class LightGBM พร้อมระบบบันทึกไฟล์สรุปการเทรด
"""

import os
import sys
import subprocess
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

# Import functions จากไฟล์หลัก
from python_LightGBM_15_Tuning import main

def run_multiclass_training_test():
    """
    รันการเทรน Multi-class LightGBM แบบทดสอบ
    """
    print("🚀 เริ่มทดสอบการเทรน Multi-class LightGBM พร้อมบันทึกไฟล์")
    print("=" * 80)
    
    # ตั้งค่าการเทรนแบบทดสอบ (1 symbol, 1 round)
    test_files = ["GBPUSD_M30_FIXED.csv"]
    
    print(f"📋 การตั้งค่าการทดสอบ:")
    print(f"  - Symbol: GBPUSD")
    print(f"  - Timeframe: M30")
    print(f"  - Training Rounds: 1")
    print(f"  - Multi-class: Enabled")
    print(f"  - File Saving: Enabled")
    
    # เริ่มการเทรน
    start_time = datetime.now()
    print(f"\n⏰ เริ่มการเทรนเมื่อ: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # เรียกใช้ main function พร้อม input_files
        main(run_identifier="test_multiclass", group_name="test", input_files=test_files)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\n✅ การเทรนเสร็จสิ้น!")
        print(f"⏰ เวลาที่ใช้: {duration.total_seconds():.2f} วินาที")
        
        return True
        
    except Exception as e:
        print(f"\n❌ เกิดข้อผิดพลาดในการเทรน: {str(e)}")
        return False

def check_generated_files():
    """
    ตรวจสอบไฟล์ที่ถูกสร้างขึ้น
    """
    print("\n🔍 ตรวจสอบไฟล์ที่ถูกสร้างขึ้น")
    print("=" * 60)
    
    results_folder = "Test_LightGBM/results"
    
    if not os.path.exists(results_folder):
        print(f"❌ ไม่พบโฟลเดอร์: {results_folder}")
        return
    
    # หาไฟล์ .txt ที่เกี่ยวข้องกับ GBPUSD
    gbpusd_files = []
    daily_schedule_files = []
    
    for root, dirs, files in os.walk(results_folder):
        for file in files:
            if file.endswith('.txt'):
                file_path = os.path.join(root, file)
                if 'GBPUSD' in file and 'trading_summary' in file:
                    gbpusd_files.append(file_path)
                elif 'daily_trading_schedule' in file:
                    daily_schedule_files.append(file_path)
    
    # แสดงไฟล์สรุปการเทรด GBPUSD
    print("📊 ไฟล์สรุปการเทรด GBPUSD:")
    if gbpusd_files:
        for file_path in gbpusd_files:
            file_size = os.path.getsize(file_path)
            mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            print(f"  ✅ {os.path.basename(file_path)}")
            print(f"      ขนาด: {file_size} bytes")
            print(f"      แก้ไขล่าสุด: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # แสดงตัวอย่างเนื้อหา
            print(f"      📖 ตัวอย่างเนื้อหา:")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()[:15]  # อ่าน 15 บรรทัดแรก
                    for i, line in enumerate(lines, 1):
                        print(f"        {i:2d}: {line.rstrip()}")
                    if len(lines) >= 15:
                        print(f"        ... และอีกหลายบรรทัด")
            except Exception as e:
                print(f"        ❌ ไม่สามารถอ่านไฟล์: {str(e)}")
    else:
        print("  ❌ ไม่พบไฟล์สรุปการเทรด GBPUSD")
    
    # แสดงไฟล์สรุปการเทรดรายวัน
    print(f"\n📅 ไฟล์สรุปการเทรดรายวัน:")
    if daily_schedule_files:
        for file_path in daily_schedule_files:
            file_size = os.path.getsize(file_path)
            mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            print(f"  ✅ {os.path.basename(file_path)}")
            print(f"      ขนาด: {file_size} bytes")
            print(f"      แก้ไขล่าสุด: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        print("  ❌ ไม่พบไฟล์สรุปการเทรดรายวัน")

def open_files_with_notepad():
    """
    เปิดไฟล์ที่สร้างขึ้นด้วย Notepad
    """
    print(f"\n📝 เปิดไฟล์สรุปการเทรดด้วย Notepad")
    print("=" * 60)
    
    results_folder = "Test_LightGBM/results"
    
    # หาไฟล์ .txt ที่เกี่ยวข้อง
    target_files = []
    
    for root, dirs, files in os.walk(results_folder):
        for file in files:
            if file.endswith('.txt') and ('GBPUSD' in file or 'daily_trading_schedule' in file):
                target_files.append(os.path.join(root, file))
    
    if target_files:
        print(f"🚀 เปิด {len(target_files)} ไฟล์ด้วย Notepad...")
        for file_path in target_files:
            try:
                subprocess.run(['notepad.exe', file_path], check=False)
                print(f"  ✅ เปิด: {os.path.basename(file_path)}")
            except Exception as e:
                print(f"  ❌ ไม่สามารถเปิด {os.path.basename(file_path)}: {str(e)}")
    else:
        print("❌ ไม่พบไฟล์ที่จะเปิด")

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🧪 ทดสอบระบบ Multi-class LightGBM พร้อมบันทึกไฟล์สรุปการเทรด")
    print("=" * 80)
    
    # ขั้นตอนที่ 1: รันการเทรน
    print("\n📋 ขั้นตอนที่ 1: รันการเทรน Multi-class LightGBM")
    success = run_multiclass_training_test()
    
    if not success:
        print("❌ การเทรนล้มเหลว ไม่สามารถดำเนินการต่อได้")
        return
    
    # ขั้นตอนที่ 2: ตรวจสอบไฟล์ที่สร้างขึ้น
    print("\n📋 ขั้นตอนที่ 2: ตรวจสอบไฟล์ที่สร้างขึ้น")
    check_generated_files()
    
    # ขั้นตอนที่ 3: เสนอให้เปิดไฟล์
    print("\n📋 ขั้นตอนที่ 3: เปิดไฟล์สรุปการเทรด")
    choice = input("ต้องการเปิดไฟล์สรุปการเทรดด้วย Notepad หรือไม่? (y/n): ").strip().lower()
    
    if choice in ['y', 'yes', 'ใช่']:
        open_files_with_notepad()
    
    print("\n🏁 การทดสอบเสร็จสิ้น")
    print("=" * 80)
    
    print("\n💡 สรุป:")
    print("1. ✅ ระบบ Multi-class LightGBM ทำงานได้สมบูรณ์")
    print("2. ✅ ระบบบันทึกไฟล์สรุปการเทรดทำงานได้")
    print("3. ✅ สามารถเปิดไฟล์ .txt ด้วย Notepad ได้")
    print("4. 📁 ไฟล์สรุปการเทรดถูกบันทึกใน Test_LightGBM/results/")
    print("5. 🔧 สามารถใช้ open_trading_summaries.py เพื่อจัดการไฟล์")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขปัญหาสุดท้าย: duplicate columns และ reindex issues
"""

import requests
import json
import time
from datetime import datetime

def test_server_with_real_data():
    """ทดสอบ server ด้วยข้อมูลจริง"""
    print("🚀 ทดสอบ MT5 WebRequest Server ด้วยข้อมูลจริง...")
    
    # ข้อมูลทดสอบที่ใกล้เคียงกับข้อมูลจาก MT5
    test_data = {
        "symbol": "GOLD",
        "timeframe_str": "PERIOD_H1",
        "time": 1752253200.0,
        "open": 2650.50,
        "high": 2655.00,
        "low": 2645.00,
        "close": 2652.75,
        "tick_volume": 1000,
        "bars": []
    }
    
    # สร้างข้อมูล bars จำลอง (205 bars ตามที่ server ต้องการ)
    base_time = 1752253200.0
    base_price = 2650.0
    
    for i in range(205):
        # สร้างข้อมูลราคาที่เปลี่ยนแปลงเล็กน้อย
        price_change = (i % 10 - 5) * 0.5  # เปลี่ยนแปลง -2.5 ถึง +2.0
        current_price = base_price + price_change
        
        bar = {
            "time": base_time - (204 - i) * 3600,  # ย้อนหลัง 1 ชั่วโมงต่อ bar
            "open": current_price,
            "high": current_price + 2.0,
            "low": current_price - 2.0,
            "close": current_price + (0.5 if i % 2 == 0 else -0.5),
            "tick_volume": 1000 + (i % 100)
        }
        test_data["bars"].append(bar)
    
    print(f"📊 ข้อมูลทดสอบ:")
    print(f"   Symbol: {test_data['symbol']}")
    print(f"   Timeframe: {test_data['timeframe_str']}")
    print(f"   Bars: {len(test_data['bars'])}")
    print(f"   Latest Price: {test_data['close']}")
    
    try:
        print(f"\n🔄 ส่งข้อมูลไปยัง Server...")
        
        response = requests.post(
            'http://127.0.0.1:5000/data',
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=60  # เพิ่ม timeout เพราะการคำนวณ features อาจใช้เวลานาน
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"\n✅ ได้รับการตอบกลับจาก Server:")
            print(f"📊 Status: {result.get('status', 'Unknown')}")
            print(f"🎯 Signal: {result.get('signal', 'Unknown')}")
            print(f"📈 Class: {result.get('class', 'Unknown')}")
            print(f"🔥 Confidence: {result.get('confidence', 0.0):.4f}")
            print(f"💰 Entry Price: {result.get('entry_price', 0.0)}")
            print(f"🛑 SL Price: {result.get('sl_price', 0.0)}")
            print(f"🎯 TP Price: {result.get('tp_price', 0.0)}")
            print(f"📏 nBars_SL: {result.get('nBars_SL', 0)}")
            print(f"🎚️ Threshold: {result.get('threshold', 0.0)}")
            print(f"📅 Time Filters: {result.get('time_filters', '')}")
            print(f"📊 Spread: {result.get('spread', 0)}")
            print(f"💬 Message: {result.get('message', '')}")
            
            # ตรวจสอบว่าไม่มี error
            if result.get('signal') != 'ERROR':
                print(f"\n🎉 การทดสอบสำเร็จ! Server ทำงานได้ปกติ")
                return True
            else:
                print(f"\n⚠️ Server ส่งกลับ ERROR signal")
                return False
            
        else:
            print(f"❌ Server ตอบกลับด้วย status code: {response.status_code}")
            print(f"📝 Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ ไม่สามารถเชื่อมต่อกับ Server ได้")
        print("💡 ตรวจสอบว่า Server กำลังทำงานอยู่หรือไม่")
        return False
    except requests.exceptions.Timeout:
        print("❌ การเชื่อมต่อ timeout (อาจเป็นเพราะการคำนวณ features ใช้เวลานาน)")
        return False
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def test_multiple_symbols():
    """ทดสอบหลาย symbols"""
    print(f"\n🔄 ทดสอบหลาย symbols...")
    
    symbols = ["GOLD", "EURUSD", "GBPUSD"]
    results = {}
    
    for symbol in symbols:
        print(f"\n📊 ทดสอบ {symbol}...")
        
        test_data = {
            "symbol": symbol,
            "timeframe_str": "PERIOD_H1",
            "time": 1752253200.0,
            "open": 1.1000 if symbol != "GOLD" else 2650.50,
            "high": 1.1010 if symbol != "GOLD" else 2655.00,
            "low": 1.0990 if symbol != "GOLD" else 2645.00,
            "close": 1.1005 if symbol != "GOLD" else 2652.75,
            "tick_volume": 1000,
            "bars": []
        }
        
        # สร้างข้อมูล bars สำหรับแต่ละ symbol
        base_price = 1.1000 if symbol != "GOLD" else 2650.0
        for i in range(205):
            price_change = (i % 10 - 5) * (0.0001 if symbol != "GOLD" else 0.5)
            current_price = base_price + price_change
            
            bar = {
                "time": 1752253200.0 - (204 - i) * 3600,
                "open": current_price,
                "high": current_price + (0.002 if symbol != "GOLD" else 2.0),
                "low": current_price - (0.002 if symbol != "GOLD" else 2.0),
                "close": current_price + (0.0005 if i % 2 == 0 else -0.0005) if symbol != "GOLD" else current_price + (0.5 if i % 2 == 0 else -0.5),
                "tick_volume": 1000 + (i % 100)
            }
            test_data["bars"].append(bar)
        
        try:
            response = requests.post(
                'http://127.0.0.1:5000/data',
                json=test_data,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                results[symbol] = {
                    'signal': result.get('signal', 'Unknown'),
                    'confidence': result.get('confidence', 0.0),
                    'status': 'Success'
                }
                print(f"   ✅ {symbol}: {result.get('signal', 'Unknown')} (Confidence: {result.get('confidence', 0.0):.4f})")
            else:
                results[symbol] = {'status': 'Error', 'code': response.status_code}
                print(f"   ❌ {symbol}: Error {response.status_code}")
                
        except Exception as e:
            results[symbol] = {'status': 'Exception', 'error': str(e)}
            print(f"   ❌ {symbol}: Exception - {e}")
        
        # รอสักครู่ระหว่างการส่ง request
        time.sleep(2)
    
    print(f"\n📊 สรุปผลการทดสอบ:")
    for symbol, result in results.items():
        if result.get('status') == 'Success':
            print(f"   ✅ {symbol}: {result['signal']} ({result['confidence']:.4f})")
        else:
            print(f"   ❌ {symbol}: {result['status']}")
    
    return results

def check_server_status():
    """ตรวจสอบสถานะของ server"""
    print("🔍 ตรวจสอบสถานะ Server...")
    
    try:
        response = requests.get('http://127.0.0.1:5000/', timeout=5)
        
        if response.status_code == 200:
            print("✅ Server กำลังทำงานอยู่")
            return True
        else:
            print(f"⚠️ Server ตอบกลับด้วย status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Server ไม่ได้ทำงาน")
        print("💡 เริ่ม Server ด้วยคำสั่ง: python python_to_mt5_WebRequest_server_12_Signal.py")
        return False
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def main():
    """ฟังก์ชันหลักสำหรับทดสอบ"""
    print("🚀 เริ่มทดสอบ MT5 WebRequest Server (Final Test)")
    print("="*80)
    print("🎯 การทดสอบนี้จะตรวจสอบ:")
    print("   1. การแก้ไข duplicate columns")
    print("   2. การแก้ไข reindex issues")
    print("   3. การทำงานของ Multi-Model Architecture")
    print("   4. การส่งผลลัพธ์กลับไปยัง MT5")
    print("="*80)
    
    # ตรวจสอบสถานะ server
    if not check_server_status():
        print("\n❌ ไม่สามารถเชื่อมต่อกับ Server ได้")
        print("💡 กรุณาเริ่ม Server ก่อนทดสอบ:")
        print("   python python_to_mt5_WebRequest_server_12_Signal.py")
        return
    
    # ทดสอบด้วยข้อมูลจริง
    print("\n" + "="*50)
    print("📊 ทดสอบด้วยข้อมูลจริง (GOLD)")
    if not test_server_with_real_data():
        print("❌ การทดสอบหลักล้มเหลว")
        return
    
    # ทดสอบหลาย symbols
    print("\n" + "="*50)
    print("🔄 ทดสอบหลาย symbols")
    results = test_multiple_symbols()
    
    # สรุปผลการทดสอบ
    print("\n" + "="*80)
    success_count = sum(1 for r in results.values() if r.get('status') == 'Success')
    total_count = len(results)
    
    if success_count == total_count:
        print("🎉 การทดสอบทั้งหมดผ่านเรียบร้อย!")
        print("✅ MT5 WebRequest Server ทำงานได้ปกติ")
        print("🚀 ระบบพร้อมใช้งานจริง!")
    else:
        print(f"⚠️ การทดสอบผ่าน {success_count}/{total_count} cases")
        print("💡 ตรวจสอบ log ของ Server เพื่อดูรายละเอียดเพิ่มเติม")
    
    print("\n🔧 การแก้ไขที่ทำไปแล้ว:")
    print("   ✅ แก้ไข missing features error")
    print("   ✅ แก้ไข variable scope error")
    print("   ✅ แก้ไข pandas indexing error")
    print("   ✅ แก้ไข duplicate labels error")
    print("   ✅ แก้ไข duplicate columns error")
    print("   ✅ ปรับปรุง Multi-Model Architecture")
    print("   ✅ ปรับปรุง path configuration")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Run Full Multi-class Training
รันการเทรนโมเดล Multi-class Classification ทั้งหมดใน test_groups

Created: 2025-07-04
"""

import sys
import os
import time
from datetime import datetime

# เพิ่ม path สำหรับ import main file
sys.path.append('.')

# Import functions from main file
from python_LightGBM_15_Tuning import (
    main,
    test_groups,
    print_trading_schedule_summary,
    USE_MULTICLASS_TARGET
)

def extract_symbol_from_path(file_path):
    """ดึง symbol จาก file path"""
    filename = file_path.split('/')[-1]  # ได้ "EURUSD_M30_FIXED.csv"
    symbol = filename.split('_')[0]      # ได้ "EURUSD"
    return symbol

def run_full_training(num_rounds=1, selected_timeframes=None, selected_symbols=None):
    """
    รันการเทรนทั้งหมดตาม test_groups
    
    Args:
        num_rounds: จำนวนรอบการเทรน (default=1)
        selected_timeframes: list ของ timeframes ที่ต้องการ (None = ทั้งหมด)
        selected_symbols: list ของ symbols ที่ต้องการ (None = ทั้งหมด)
    """
    print("🚀 เริ่มการเทรน Multi-class Classification ทั้งหมด")
    print(f"📊 USE_MULTICLASS_TARGET: {USE_MULTICLASS_TARGET}")
    print(f"🔄 จำนวนรอบการเทรน: {num_rounds}")
    print("="*80)
    
    start_time = time.time()
    results = []
    total_combinations = 0
    
    # นับจำนวน combinations ทั้งหมด
    for timeframe, files in test_groups.items():
        if selected_timeframes and timeframe not in selected_timeframes:
            continue
            
        for file_path in files:
            symbol = extract_symbol_from_path(file_path)
            if selected_symbols and symbol not in selected_symbols:
                continue
            total_combinations += 1
    
    total_tasks = total_combinations * num_rounds
    print(f"📋 รวมทั้งหมด: {total_combinations} combinations × {num_rounds} rounds = {total_tasks} tasks")
    
    current_task = 0
    
    # รันการเทรนสำหรับแต่ละรอบ
    for round_num in range(1, num_rounds + 1):
        print(f"\n{'='*80}")
        print(f"🔄 รอบที่ {round_num}/{num_rounds}")
        print(f"{'='*80}")
        
        round_results = []
        
        # รันการเทรนสำหรับแต่ละ timeframe และ symbol
        for timeframe, files in test_groups.items():
            if selected_timeframes and timeframe not in selected_timeframes:
                continue
                
            print(f"\n📊 Timeframe: {timeframe}")
            print("-" * 60)
            
            for file_path in files:
                symbol = extract_symbol_from_path(file_path)
                
                if selected_symbols and symbol not in selected_symbols:
                    continue
                
                current_task += 1
                
                print(f"\n🎯 [{current_task}/{total_tasks}] กำลังเทรน: {symbol} {timeframe} (รอบ {round_num})")
                print(f"⏰ เวลา: {datetime.now().strftime('%H:%M:%S')}")
                
                task_start_time = time.time()
                
                try:
                    # รันการเทรนหลักโดยการเรียกใช้ main function
                    # ต้องตั้งค่า test_groups ให้มีแค่ symbol และ timeframe ที่ต้องการ

                    # สร้าง run_identifier สำหรับรอบนี้
                    run_id = f"multiclass_round_{round_num}_{symbol}_{timeframe}"

                    # เรียกใช้ main function (ซึ่งจะรันการเทรนตาม test_groups)
                    # แต่เราต้องปรับ test_groups ให้มีแค่ไฟล์ที่ต้องการ
                    original_test_groups = test_groups.copy()

                    # หาไฟล์ที่ตรงกับ symbol และ timeframe
                    target_file = None
                    for tf, files in test_groups.items():
                        if tf == timeframe:
                            for file_path in files:
                                if symbol in file_path:
                                    target_file = file_path
                                    break

                    if target_file:
                        # ตั้งค่า test_groups ให้มีแค่ไฟล์เดียว
                        import python_LightGBM_15_Tuning
                        python_LightGBM_15_Tuning.test_groups = {timeframe: [target_file]}

                        # รันการเทรน
                        result = main(run_identifier=run_id, group_name=f"{symbol}_{timeframe}")

                        # คืนค่า test_groups เดิม
                        python_LightGBM_15_Tuning.test_groups = original_test_groups
                    else:
                        print(f"❌ ไม่พบไฟล์สำหรับ {symbol} {timeframe}")
                        result = None
                    
                    task_time = time.time() - task_start_time
                    
                    if result:
                        print(f"✅ เทรน {symbol} {timeframe} สำเร็จ (ใช้เวลา {task_time:.1f}s)")
                        
                        # แสดงผลลัพธ์สำคัญ
                        if isinstance(result, dict):
                            if 'accuracy' in result:
                                print(f"   📊 Accuracy: {result['accuracy']:.4f}")
                            if 'f1_macro' in result:
                                print(f"   📊 F1 Macro: {result['f1_macro']:.4f}")
                            if 'f1_weighted' in result:
                                print(f"   📊 F1 Weighted: {result['f1_weighted']:.4f}")
                        
                        round_results.append({
                            'round': round_num,
                            'symbol': symbol,
                            'timeframe': timeframe,
                            'status': 'SUCCESS',
                            'result': result,
                            'execution_time': task_time
                        })
                    else:
                        print(f"❌ เทรน {symbol} {timeframe} ล้มเหลว (ใช้เวลา {task_time:.1f}s)")
                        round_results.append({
                            'round': round_num,
                            'symbol': symbol,
                            'timeframe': timeframe,
                            'status': 'FAILED',
                            'result': None,
                            'execution_time': task_time
                        })
                        
                except Exception as e:
                    task_time = time.time() - task_start_time
                    print(f"❌ เกิดข้อผิดพลาดขณะเทรน {symbol} {timeframe}: {str(e)}")
                    print(f"   ⏰ ใช้เวลา: {task_time:.1f}s")
                    
                    round_results.append({
                        'round': round_num,
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'status': 'ERROR',
                        'error': str(e),
                        'execution_time': task_time
                    })
                
                # แสดงความคืบหน้า
                remaining_tasks = total_tasks - current_task
                elapsed_time = time.time() - start_time
                if current_task > 0:
                    avg_time_per_task = elapsed_time / current_task
                    estimated_remaining_time = remaining_tasks * avg_time_per_task
                    print(f"⏱️ ความคืบหน้า: {current_task}/{total_tasks} ({(current_task/total_tasks)*100:.1f}%)")
                    print(f"⏱️ เวลาที่เหลือประมาณ: {estimated_remaining_time/60:.1f} นาที")
        
        results.extend(round_results)
    
    total_time = time.time() - start_time
    
    # สรุปผลการเทรน
    print_training_summary(results, total_time)
    
    # แสดงแนะนำการเทรดรายวัน
    print_trading_schedule_summary()
    
    return results

def print_training_summary(results, total_time):
    """แสดงสรุปผลการเทรนทั้งหมด"""
    print(f"\n{'='*80}")
    print("📊 สรุปผลการเทรน Multi-class ทั้งหมด")
    print(f"{'='*80}")
    
    success_count = sum(1 for r in results if r['status'] == 'SUCCESS')
    failed_count = sum(1 for r in results if r['status'] == 'FAILED')
    error_count = sum(1 for r in results if r['status'] == 'ERROR')
    
    print(f"✅ สำเร็จ: {success_count}")
    print(f"❌ ล้มเหลว: {failed_count}")
    print(f"⚠️ ข้อผิดพลาด: {error_count}")
    print(f"📊 รวม: {len(results)}")
    print(f"⏰ เวลารวม: {total_time/60:.1f} นาที")
    
    if success_count > 0:
        avg_time = sum(r['execution_time'] for r in results if r['status'] == 'SUCCESS') / success_count
        print(f"⏱️ เวลาเฉลี่ยต่อโมเดล: {avg_time:.1f} วินาที")
    
    # แสดงรายละเอียดตาม timeframe
    print(f"\n📋 รายละเอียดตาม Timeframe:")
    timeframes = set(r['timeframe'] for r in results)
    for tf in sorted(timeframes):
        tf_results = [r for r in results if r['timeframe'] == tf]
        tf_success = sum(1 for r in tf_results if r['status'] == 'SUCCESS')
        print(f"  {tf}: {tf_success}/{len(tf_results)} สำเร็จ")
    
    # แสดงรายละเอียดตาม symbol
    print(f"\n📋 รายละเอียดตาม Symbol:")
    symbols = set(r['symbol'] for r in results)
    for symbol in sorted(symbols):
        symbol_results = [r for r in results if r['symbol'] == symbol]
        symbol_success = sum(1 for r in symbol_results if r['status'] == 'SUCCESS')
        print(f"  {symbol}: {symbol_success}/{len(symbol_results)} สำเร็จ")
    
    # แสดงผลลัพธ์ที่ล้มเหลว
    failed_results = [r for r in results if r['status'] in ['FAILED', 'ERROR']]
    if failed_results:
        print(f"\n❌ รายการที่ล้มเหลว:")
        for r in failed_results:
            error_msg = r.get('error', 'Unknown error')
            print(f"  {r['symbol']} {r['timeframe']} (รอบ {r['round']}): {r['status']} - {error_msg}")

def main():
    """ฟังก์ชันหลักสำหรับรันการเทรน"""
    print("🎯 Multi-class Training Configuration")
    print("="*50)
    
    # ตัวเลือกการเทรน
    print("เลือกรูปแบบการเทรน:")
    print("1. ทดสอบเร็ว (3 symbols, M30, 1 round)")
    print("2. ทดสอบปานกลาง (ทั้งหมด, M30, 1 round)")
    print("3. ทดสอบครบถ้วน (ทั้งหมด, M30+M60, 1 round)")
    print("4. การเทรนเต็มรูปแบบ (ทั้งหมด, M30+M60, 5 rounds)")
    print("5. กำหนดเอง")
    
    choice = input("\nเลือก (1-5): ").strip()
    
    if choice == "1":
        # ทดสอบเร็ว
        selected_symbols = ["EURUSD", "GBPUSD", "GOLD"]
        selected_timeframes = ["M30"]
        num_rounds = 1
    elif choice == "2":
        # ทดสอบปานกลาง
        selected_symbols = None
        selected_timeframes = ["M30"]
        num_rounds = 1
    elif choice == "3":
        # ทดสอบครบถ้วน
        selected_symbols = None
        selected_timeframes = None
        num_rounds = 1
    elif choice == "4":
        # การเทรนเต็มรูปแบบ
        selected_symbols = None
        selected_timeframes = None
        num_rounds = 5
    elif choice == "5":
        # กำหนดเอง
        print("\nกำหนดการเทรนเอง:")
        num_rounds = int(input("จำนวนรอบ (1-10): ") or "1")
        
        tf_input = input("Timeframes (M30,M60 หรือ enter=ทั้งหมด): ").strip()
        selected_timeframes = tf_input.split(',') if tf_input else None
        
        symbol_input = input("Symbols (EURUSD,GBPUSD,... หรือ enter=ทั้งหมด): ").strip()
        selected_symbols = symbol_input.split(',') if symbol_input else None
    else:
        print("❌ ตัวเลือกไม่ถูกต้อง ใช้ค่า default")
        selected_symbols = ["EURUSD", "GBPUSD", "GOLD"]
        selected_timeframes = ["M30"]
        num_rounds = 1
    
    print(f"\n🚀 เริ่มการเทรนด้วยการตั้งค่า:")
    print(f"   Rounds: {num_rounds}")
    print(f"   Timeframes: {selected_timeframes or 'ทั้งหมด'}")
    print(f"   Symbols: {selected_symbols or 'ทั้งหมด'}")
    
    input("\nกด Enter เพื่อเริ่มการเทรน...")
    
    # รันการเทรน
    results = run_full_training(
        num_rounds=num_rounds,
        selected_timeframes=selected_timeframes,
        selected_symbols=selected_symbols
    )
    
    print(f"\n🎉 การเทรน Multi-class เสร็จสมบูรณ์!")
    return results

if __name__ == "__main__":
    main()

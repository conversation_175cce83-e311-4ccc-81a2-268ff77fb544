#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Multi-class Training
ทดสอบการเทรนโมเดล Multi-class Classification จริง

Created: 2025-07-04
"""

import pandas as pd
import numpy as np
import sys
import os

# เพิ่ม path สำหรับ import main file
sys.path.append('.')

# Import functions from main file
from python_LightGBM_15_Tuning import (
    USE_MULTICLASS_TARGET,
    PROFIT_THRESHOLDS,
    CLASS_MAPPING,
    create_multiclass_target,
    process_trade_targets,
    get_lgbm_params,
    evaluate_multiclass_model
)

import lightgbm as lgb
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, f1_score

def load_sample_data():
    """โหลดข้อมูลตัวอย่างสำหรับทดสอบ"""
    print("📂 กำลังโหลดข้อมูลตัวอย่าง...")
    
    # ลองโหลดไฟล์ CSV ที่มีอยู่
    csv_files = [
        "CSV_Files_Fixed/GBPUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_M30_FIXED.csv"
    ]
    
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            print(f"✅ พบไฟล์: {csv_file}")
            try:
                df = pd.read_csv(csv_file, low_memory=False)
                print(f"✅ โหลดข้อมูลสำเร็จ: {len(df)} rows, {len(df.columns)} columns")
                print(f"📊 Columns: {list(df.columns)}")

                # แปลง data types
                numeric_columns = ['Open', 'High', 'Low', 'Close', 'TickVol', 'Vol']
                for col in numeric_columns:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col], errors='coerce')

                print(f"✅ แปลง data types สำเร็จ")
                return df, csv_file
            except Exception as e:
                print(f"❌ ไม่สามารถโหลด {csv_file}: {str(e)}")
                continue
    
    print("❌ ไม่พบไฟล์ CSV ที่ใช้ได้")
    return None, None

def create_sample_features_and_targets(df):
    """สร้าง features และ targets ตัวอย่างจากข้อมูล"""
    print("\n🔧 กำลังสร้าง features และ targets...")
    
    # สร้าง features พื้นฐาน
    df['Price_Change'] = df['Close'] - df['Open']
    df['Price_Range'] = df['High'] - df['Low']
    df['Volume_Ratio'] = df['TickVol'] / df['TickVol'].rolling(10).mean()
    
    # สร้าง moving averages
    df['MA_5'] = df['Close'].rolling(5).mean()
    df['MA_10'] = df['Close'].rolling(10).mean()
    df['MA_20'] = df['Close'].rolling(20).mean()
    
    # สร้าง technical indicators
    df['RSI'] = 50 + np.random.normal(0, 10, len(df))  # Mock RSI
    df['MACD'] = np.random.normal(0, 0.001, len(df))   # Mock MACD
    
    # สร้าง profit สำหรับ target (จำลอง)
    np.random.seed(42)
    df['Profit'] = np.random.normal(0, 100, len(df))  # จำลอง profit ระหว่าง -300 ถึง 300
    
    # สร้าง multi-class target
    df['Target_Multiclass'] = create_multiclass_target(df['Profit'])
    
    # สร้าง binary target สำหรับเปรียบเทียบ
    df['Target_Binary'] = (df['Profit'] > 0).astype(int)
    
    # เลือก features
    feature_columns = [
        'Price_Change', 'Price_Range', 'Volume_Ratio',
        'MA_5', 'MA_10', 'MA_20', 'RSI', 'MACD'
    ]
    
    # ลบ NaN values
    df = df.dropna()
    
    print(f"✅ สร้าง features และ targets สำเร็จ")
    print(f"📊 Features: {feature_columns}")
    print(f"📊 Data shape after cleaning: {df.shape}")
    
    # แสดงการกระจายของ targets
    print(f"\n📊 Multi-class Target Distribution:")
    target_dist = df['Target_Multiclass'].value_counts().sort_index()
    for class_id, count in target_dist.items():
        class_name = CLASS_MAPPING.get(class_id, f"Class_{class_id}")
        percentage = (count / len(df)) * 100
        print(f"  Class {class_id} ({class_name}): {count} samples ({percentage:.1f}%)")
    
    print(f"\n📊 Binary Target Distribution:")
    binary_dist = df['Target_Binary'].value_counts()
    print(f"  Class 0: {binary_dist.get(0, 0)} samples")
    print(f"  Class 1: {binary_dist.get(1, 0)} samples")
    
    return df, feature_columns

def train_and_compare_models(df, feature_columns):
    """เทรนและเปรียบเทียบโมเดล binary vs multi-class"""
    print("\n🚀 กำลังเทรนและเปรียบเทียบโมเดล...")
    
    # เตรียมข้อมูล
    X = df[feature_columns]
    y_binary = df['Target_Binary']
    y_multiclass = df['Target_Multiclass']
    
    # แบ่งข้อมูล
    X_train, X_test, y_bin_train, y_bin_test = train_test_split(
        X, y_binary, test_size=0.2, random_state=42, stratify=y_binary
    )
    
    _, _, y_multi_train, y_multi_test = train_test_split(
        X, y_multiclass, test_size=0.2, random_state=42, stratify=y_multiclass
    )
    
    # Scaling
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    print(f"📊 Training set: {X_train.shape}")
    print(f"📊 Test set: {X_test.shape}")
    
    # 1. เทรน Binary Classification Model
    print(f"\n🔵 เทรน Binary Classification Model...")
    binary_params = get_lgbm_params(y=y_bin_train)
    binary_params['n_estimators'] = 100  # เพิ่มใน params แทน
    binary_model = lgb.LGBMClassifier(**binary_params)
    binary_model.fit(X_train_scaled, y_bin_train)
    
    # ประเมินผล binary model
    y_bin_pred = binary_model.predict(X_test_scaled)
    binary_accuracy = accuracy_score(y_bin_test, y_bin_pred)
    binary_f1 = f1_score(y_bin_test, y_bin_pred)
    
    print(f"✅ Binary Model Results:")
    print(f"  Accuracy: {binary_accuracy:.4f}")
    print(f"  F1 Score: {binary_f1:.4f}")
    
    # 2. เทรน Multi-class Classification Model
    print(f"\n🟡 เทรน Multi-class Classification Model...")
    multiclass_params = get_lgbm_params(y=y_multi_train)
    multiclass_params['n_estimators'] = 100  # เพิ่มใน params แทน
    multiclass_model = lgb.LGBMClassifier(**multiclass_params)
    multiclass_model.fit(X_train_scaled, y_multi_train)
    
    # ประเมินผล multi-class model
    y_multi_pred = multiclass_model.predict(X_test_scaled)
    y_multi_pred_proba = multiclass_model.predict_proba(X_test_scaled)
    
    multiclass_metrics = evaluate_multiclass_model(y_multi_test, y_multi_pred, y_multi_pred_proba)
    
    print(f"✅ Multi-class Model Results:")
    print(f"  Accuracy: {multiclass_metrics['accuracy']:.4f}")
    print(f"  F1 Macro: {multiclass_metrics['f1_macro']:.4f}")
    print(f"  F1 Weighted: {multiclass_metrics['f1_weighted']:.4f}")
    print(f"  AUC OvR: {multiclass_metrics['auc_ovr']:.4f}")
    print(f"  Log Loss: {multiclass_metrics['log_loss']:.4f}")
    
    # แสดงผลต่อ class
    print(f"\n📊 Per-class Results:")
    for class_id in np.unique(y_multi_test):
        class_name = CLASS_MAPPING.get(class_id, f"Class_{class_id}")
        precision_key = f'{class_name}_precision'
        recall_key = f'{class_name}_recall'
        f1_key = f'{class_name}_f1'
        
        if all(key in multiclass_metrics for key in [precision_key, recall_key, f1_key]):
            print(f"  {class_name}:")
            print(f"    Precision: {multiclass_metrics[precision_key]:.4f}")
            print(f"    Recall: {multiclass_metrics[recall_key]:.4f}")
            print(f"    F1: {multiclass_metrics[f1_key]:.4f}")
    
    return binary_model, multiclass_model, multiclass_metrics

def main():
    """ฟังก์ชันหลักสำหรับการทดสอบ"""
    print("🚀 เริ่มการทดสอบ Multi-class Training")
    print(f"📊 USE_MULTICLASS_TARGET: {USE_MULTICLASS_TARGET}")
    print("="*60)
    
    # 1. โหลดข้อมูล
    df, csv_file = load_sample_data()
    if df is None:
        print("❌ ไม่สามารถโหลดข้อมูลได้ การทดสอบหยุด")
        return
    
    print(f"✅ ใช้ไฟล์: {csv_file}")
    
    # 2. สร้าง features และ targets
    df, feature_columns = create_sample_features_and_targets(df)
    
    # 3. เทรนและเปรียบเทียบโมเดล
    binary_model, multiclass_model, metrics = train_and_compare_models(df, feature_columns)
    
    print("\n" + "="*60)
    print("✅ การทดสอบ Multi-class Training เสร็จสมบูรณ์!")
    print("="*60)
    
    print(f"\n📊 สรุปผลการทดสอบ:")
    print(f"  ✅ Data loading: PASS")
    print(f"  ✅ Feature creation: PASS")
    print(f"  ✅ Binary model training: PASS")
    print(f"  ✅ Multi-class model training: PASS")
    print(f"  ✅ Model evaluation: PASS")
    
    print(f"\n🎯 Multi-class Implementation พร้อมใช้งาน!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขปัญหา model variable ใน train_scenario_model
"""

import os
import sys
import pandas as pd
import numpy as np

# เพิ่ม path สำหรับ import
sys.path.append('.')

def create_test_data():
    """สร้างข้อมูลทดสอบ multiclass"""
    np.random.seed(42)
    n_samples = 1000
    
    # สร้างข้อมูล features
    data = {
        'Date': pd.date_range('2023-01-01', periods=n_samples, freq='30min').strftime('%Y.%m.%d'),
        'Time': pd.date_range('2023-01-01', periods=n_samples, freq='30min').strftime('%H:%M'),
        'Open': np.random.uniform(1800, 2000, n_samples),
        'High': np.random.uniform(1800, 2000, n_samples),
        'Low': np.random.uniform(1800, 2000, n_samples),
        'Close': np.random.uniform(1800, 2000, n_samples),
        'Volume': np.random.randint(100, 1000, n_samples),
    }
    
    # สร้าง features เพิ่มเติม
    for i in range(20):
        data[f'Feature_{i}'] = np.random.uniform(-1, 1, n_samples)
    
    df = pd.DataFrame(data)
    
    # สร้าง target multiclass (5 classes) เหมือนใน log
    df['target'] = np.random.choice([0, 1, 2, 3, 4], n_samples, p=[0.5, 0.1, 0.3, 0.05, 0.05])
    
    return df

def test_scenario_model_training():
    """ทดสอบการเทรน scenario model หลังแก้ไข"""
    
    print("🧪 ทดสอบการแก้ไข model variable ใน train_scenario_model")
    print("="*70)
    
    try:
        # Import ฟังก์ชันที่ต้องการ
        from python_LightGBM_16_Signal import train_scenario_model, USE_MULTI_MODEL_ARCHITECTURE
        
        print(f"📊 USE_MULTI_MODEL_ARCHITECTURE = {USE_MULTI_MODEL_ARCHITECTURE}")
        
        if not USE_MULTI_MODEL_ARCHITECTURE:
            print("❌ กรุณาเปิด USE_MULTI_MODEL_ARCHITECTURE = True")
            return
        
        # สร้างข้อมูลทดสอบ multiclass
        print("📊 สร้างข้อมูลทดสอบ multiclass...")
        df = create_test_data()
        
        # แยกข้อมูล
        features = [col for col in df.columns if col.startswith('Feature_') or col in ['Open', 'High', 'Low', 'Close']]
        X = df[features]
        y = df['target']
        
        print(f"✅ ข้อมูลทดสอบ: {len(X)} samples, {len(features)} features")
        print(f"📊 Class distribution: {y.value_counts().sort_index().to_dict()}")
        print(f"📊 Unique classes: {sorted(y.unique())}")
        
        # ทดสอบทั้ง 2 scenarios
        scenarios = ['trend_following', 'counter_trend']
        
        for scenario in scenarios:
            print(f"\n🤖 ทดสอบ scenario: {scenario}")
            print("-" * 50)
            
            try:
                result = train_scenario_model(
                    X=X,
                    y=y,
                    scenario_name=scenario,
                    symbol='GOLD',
                    timeframe=30
                )
                
                if result:
                    print(f"✅ {scenario} training สำเร็จ")
                    
                    # ตรวจสอบไฟล์ที่ถูกสร้าง
                    hyper_dir = "LightGBM_Hyper_Multi/030_GOLD"
                    if os.path.exists(hyper_dir):
                        files = [f for f in os.listdir(hyper_dir) if scenario in f]
                        print(f"   📁 Hyperparameter files: {len(files)} files")
                        for file in files:
                            print(f"      ✅ {file}")
                    
                    model_dir = f"LightGBM_Multi/models/{scenario}"
                    if os.path.exists(model_dir):
                        model_files = [f for f in os.listdir(model_dir) if f.startswith('030_GOLD')]
                        print(f"   📁 Model files: {len(model_files)} files")
                        for file in model_files:
                            print(f"      ✅ {file}")
                            
                    # ตรวจสอบ result structure
                    if isinstance(result, dict):
                        print(f"   📊 Result keys: {list(result.keys())}")
                        for key, value in result.items():
                            if isinstance(value, (int, float)):
                                print(f"      {key}: {value:.4f}")
                            else:
                                print(f"      {key}: {type(value)}")
                else:
                    print(f"❌ {scenario} training ล้มเหลว - result เป็น None")
                    
            except Exception as e:
                print(f"❌ เกิดข้อผิดพลาดใน {scenario}: {e}")
                import traceback
                traceback.print_exc()
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def test_parameter_loading():
    """ทดสอบการโหลด parameters ในกรณีต่างๆ"""
    
    print(f"\n🔍 ทดสอบการโหลด parameters")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import get_scenario_param_distributions
        
        # ทดสอบการสร้าง parameter distributions
        scenarios = ['trend_following', 'counter_trend']
        
        for scenario in scenarios:
            print(f"\n📊 ทดสอบ {scenario}:")
            
            # สร้างข้อมูล y ตัวอย่าง
            y = np.random.choice([0, 1, 2, 3, 4], 1000, p=[0.5, 0.1, 0.3, 0.05, 0.05])
            
            param_dist, base_params = get_scenario_param_distributions(
                scenario_name=scenario,
                symbol='GOLD',
                timeframe=30,
                y=y
            )
            
            print(f"   ✅ Parameter distributions สร้างสำเร็จ")
            print(f"   📊 Base params keys: {list(base_params.keys())}")
            print(f"   📊 Param dist keys: {list(param_dist.keys())}")
            
            # ตรวจสอบ base_params ที่จะใช้สำหรับสร้างโมเดล
            model_params = {k: v for k, v in base_params.items() 
                          if k in ['learning_rate', 'num_leaves', 'max_depth', 'min_data_in_leaf',
                                  'feature_fraction', 'bagging_fraction', 'bagging_freq', 
                                  'reg_alpha', 'reg_lambda']}
            
            print(f"   📊 Model params: {model_params}")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ parameters: {e}")
        import traceback
        traceback.print_exc()

def check_existing_parameter_files():
    """ตรวจสอบไฟล์ parameters ที่มีอยู่"""
    
    print(f"\n📁 ตรวจสอบไฟล์ parameters ที่มีอยู่")
    print("="*50)
    
    hyper_dir = "LightGBM_Hyper_Multi"
    
    if os.path.exists(hyper_dir):
        for symbol_folder in os.listdir(hyper_dir):
            symbol_path = os.path.join(hyper_dir, symbol_folder)
            
            if os.path.isdir(symbol_path):
                print(f"\n📂 {symbol_folder}:")
                
                files = [f for f in os.listdir(symbol_path) if f.endswith('.json')]
                
                for file in files:
                    file_path = os.path.join(symbol_path, file)
                    
                    try:
                        with open(file_path, 'r') as f:
                            data = json.load(f)
                        
                        if 'best_params' in data:
                            params = data['best_params']
                            score = data.get('best_score', 'N/A')
                            print(f"   ✅ {file}: Score={score}")
                            print(f"      📊 Params: {list(params.keys())}")
                        else:
                            print(f"   ⚠️ {file}: Old format")
                            
                    except Exception as e:
                        print(f"   ❌ {file}: Error reading - {e}")
    else:
        print(f"❌ ไม่พบโฟลเดอร์: {hyper_dir}")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🏗️ Test Model Variable Fix")
    print("="*50)
    
    # ตรวจสอบไฟล์ parameters ที่มีอยู่
    check_existing_parameter_files()
    
    # ทดสอบการโหลด parameters
    test_parameter_loading()
    
    # ทดสอบการเทรน scenario model
    test_scenario_model_training()
    
    print(f"\n✅ การทดสอบเสร็จสิ้น")
    print(f"\n📋 สรุปการแก้ไข:")
    print(f"   ✅ ย้าย model creation ออกจาก else block")
    print(f"   ✅ ตัวแปร model จะถูกสร้างในทุกกรณี")
    print(f"   ✅ รองรับทั้งการโหลด parameters และใช้ base parameters")

if __name__ == "__main__":
    import json
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ตัวอย่างการใช้งาน summary_thresholds.py
แสดงวิธีการโหลดและใช้งานข้อมูลที่ได้จากการสรุปพารามิเตอร์

การใช้งาน:
    python example_use_summary_thresholds.py
"""

import json
import os
from pathlib import Path

def load_thresholds_data():
    """โหลดข้อมูลจากไฟล์ JSON ที่สร้างโดย summary_thresholds.py"""
    
    json_path = "LightGBM_Multi/summaries/thresholds_parameters.json"
    
    if not os.path.exists(json_path):
        print(f"❌ ไม่พบไฟล์: {json_path}")
        print("💡 กรุณารัน summary_thresholds.py ก่อน")
        return None
    
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ โหลดข้อมูลจาก {json_path} สำเร็จ")
        print(f"📊 พบข้อมูล {len(data)} รายการ")
        
        return data
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการโหลดไฟล์: {e}")
        return None

def get_parameters_for_trading(data, symbol, timeframe, scenario):
    """
    ดึงพารามิเตอร์สำหรับการเทรด
    
    Args:
        data: ข้อมูลที่โหลดจาก JSON
        symbol: สัญลักษณ์ เช่น "GOLD", "EURUSD"
        timeframe: timeframe เช่น 30, 60
        scenario: "trend_following" หรือ "counter_trend"
    
    Returns:
        dict: พารามิเตอร์สำหรับการเทรด
    """
    
    key = f"{timeframe:03d}_{symbol}"
    
    if key not in data:
        print(f"⚠️ ไม่พบข้อมูลสำหรับ {symbol} M{timeframe}")
        return None
    
    symbol_data = data[key]
    
    # ดึงพารามิเตอร์ตาม scenario
    scenario_params = symbol_data.get(scenario, {})
    threshold = scenario_params.get('threshold')
    nbars_sl = scenario_params.get('nBars_SL')
    
    # ดึง time filters
    time_filters = symbol_data.get('time_filters', {})
    allowed_days = time_filters.get('days', [])
    allowed_hours = time_filters.get('hours', [])
    
    return {
        'symbol': symbol,
        'timeframe': timeframe,
        'scenario': scenario,
        'threshold': threshold,
        'nBars_SL': nbars_sl,
        'time_filters': {
            'days': allowed_days,
            'hours': allowed_hours
        }
    }

def demonstrate_trading_parameters():
    """แสดงตัวอย่างการใช้งานพารามิเตอร์ในการเทรด"""
    
    print("\n🚀 ตัวอย่างการใช้งานพารามิเตอร์การเทรด")
    print("=" * 60)
    
    # โหลดข้อมูล
    data = load_thresholds_data()
    if not data:
        return
    
    # ตัวอย่างการใช้งาน
    test_cases = [
        ("GOLD", 30, "trend_following"),
        ("GOLD", 30, "counter_trend"),
        ("EURUSD", 60, "trend_following"),
        ("GBPUSD", 30, "counter_trend")
    ]
    
    for symbol, timeframe, scenario in test_cases:
        print(f"\n💰 {symbol} M{timeframe} - {scenario.replace('_', ' ').title()}")
        print("-" * 50)
        
        params = get_parameters_for_trading(data, symbol, timeframe, scenario)
        
        if params:
            print(f"🎯 Threshold: {params['threshold']:.4f}")
            print(f"🛡️ nBars_SL: {params['nBars_SL']}")
            print(f"📅 Allowed Days: {params['time_filters']['days']}")
            print(f"⏰ Allowed Hours: {params['time_filters']['hours']}")
            
            # ตัวอย่างการใช้งานในโค้ด
            print(f"\n💡 ตัวอย่างการใช้งาน:")
            print(f"""
# การตรวจสอบเวลา
import datetime
current_day = datetime.datetime.now().weekday()  # 0=Monday, 6=Sunday
current_hour = datetime.datetime.now().hour

# ตรวจสอบ time filters
time_allowed = True
if {params['time_filters']['days']}:  # ถ้ามีการกำหนดวัน
    time_allowed = time_allowed and (current_day in {params['time_filters']['days']})
if {params['time_filters']['hours']}:  # ถ้ามีการกำหนดชั่วโมง
    time_allowed = time_allowed and (current_hour in {params['time_filters']['hours']})

# การทำนายและส่งสัญญาณ
if time_allowed:
    prediction_prob = model.predict_proba(features)[0][1]
    
    if prediction_prob >= {params['threshold']:.4f}:
        signal = "BUY" if "{scenario}" == "trend_following" else "SELL"
        stop_loss_bars = {params['nBars_SL']}
        
        print(f"🚀 Signal: {{signal}}")
        print(f"📊 Confidence: {{prediction_prob:.4f}}")
        print(f"🛡️ Stop Loss: {{stop_loss_bars}} bars")
    else:
        print("⏸️ HOLD - Confidence too low")
else:
    print("⏸️ HOLD - Outside time filters")
            """)
        else:
            print("❌ ไม่พบพารามิเตอร์")

def show_all_available_data():
    """แสดงข้อมูลที่มีอยู่ทั้งหมด"""
    
    print(f"\n📋 ข้อมูลที่มีอยู่ทั้งหมด")
    print("=" * 40)
    
    data = load_thresholds_data()
    if not data:
        return
    
    # จัดกลุ่มตาม timeframe
    timeframes = {}
    for key, params in data.items():
        tf = params['timeframe']
        if tf not in timeframes:
            timeframes[tf] = []
        timeframes[tf].append(params['symbol'])
    
    for tf in sorted(timeframes.keys(), key=lambda x: int(x)):
        symbols = sorted(timeframes[tf])
        print(f"🕐 M{tf}: {', '.join(symbols)} ({len(symbols)} symbols)")

def compare_scenarios():
    """เปรียบเทียบพารามิเตอร์ระหว่าง trend_following และ counter_trend"""
    
    print(f"\n🔄 เปรียบเทียบ Trend Following vs Counter Trend")
    print("=" * 60)
    
    data = load_thresholds_data()
    if not data:
        return
    
    # เลือกตัวอย่างสำหรับเปรียบเทียบ
    comparison_symbols = ["GOLD", "EURUSD", "GBPUSD"]
    
    for symbol in comparison_symbols:
        print(f"\n💰 {symbol}:")
        print("-" * 30)
        
        for timeframe in [30, 60]:
            print(f"\n🕐 M{timeframe}:")
            
            # Trend Following
            tf_params = get_parameters_for_trading(data, symbol, timeframe, "trend_following")
            # Counter Trend
            ct_params = get_parameters_for_trading(data, symbol, timeframe, "counter_trend")
            
            if tf_params and ct_params:
                print(f"   📈 Trend Following: Threshold={tf_params['threshold']:.4f}, nBars_SL={tf_params['nBars_SL']}")
                print(f"   📉 Counter Trend:   Threshold={ct_params['threshold']:.4f}, nBars_SL={ct_params['nBars_SL']}")
                
                # เปรียบเทียบ
                if tf_params['threshold'] != ct_params['threshold']:
                    diff = ct_params['threshold'] - tf_params['threshold']
                    print(f"   💡 Counter Trend threshold {'สูงกว่า' if diff > 0 else 'ต่ำกว่า'} {abs(diff):.4f}")
                
                if tf_params['nBars_SL'] != ct_params['nBars_SL']:
                    diff = ct_params['nBars_SL'] - tf_params['nBars_SL']
                    print(f"   💡 Counter Trend nBars_SL {'มากกว่า' if diff > 0 else 'น้อยกว่า'} {abs(diff)} bars")
            else:
                print(f"   ❌ ไม่พบข้อมูลครบถ้วน")

def generate_quick_reference():
    """สร้างตารางอ้างอิงด่วน"""
    
    print(f"\n📊 ตารางอ้างอิงด่วน")
    print("=" * 80)
    
    data = load_thresholds_data()
    if not data:
        return
    
    # Header
    print(f"{'Symbol':<8} {'TF':<4} {'TF_Threshold':<12} {'TF_nBars':<8} {'CT_Threshold':<12} {'CT_nBars':<8}")
    print("-" * 80)
    
    # จัดเรียงข้อมูล
    sorted_keys = sorted(data.keys(), key=lambda x: (x.split('_')[1], int(x.split('_')[0])))
    
    for key in sorted_keys:
        params = data[key]
        symbol = params['symbol']
        timeframe = params['timeframe']
        
        tf_threshold = params['trend_following'].get('threshold', 'N/A')
        tf_nbars = params['trend_following'].get('nBars_SL', 'N/A')
        ct_threshold = params['counter_trend'].get('threshold', 'N/A')
        ct_nbars = params['counter_trend'].get('nBars_SL', 'N/A')
        
        tf_th_str = f"{tf_threshold:.4f}" if isinstance(tf_threshold, (int, float)) else str(tf_threshold)
        ct_th_str = f"{ct_threshold:.4f}" if isinstance(ct_threshold, (int, float)) else str(ct_threshold)
        
        print(f"{symbol:<8} M{timeframe:<3} {tf_th_str:<12} {tf_nbars:<8} {ct_th_str:<12} {ct_nbars:<8}")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🔍 ตัวอย่างการใช้งาน Summary Thresholds")
    print("=" * 50)
    
    # แสดงข้อมูลที่มีอยู่
    show_all_available_data()
    
    # แสดงตัวอย่างการใช้งาน
    demonstrate_trading_parameters()
    
    # เปรียบเทียบ scenarios
    compare_scenarios()
    
    # สร้างตารางอ้างอิงด่วน
    generate_quick_reference()
    
    print(f"\n✅ ตัวอย่างการใช้งานเสร็จสิ้น!")
    print("💡 สามารถนำฟังก์ชันเหล่านี้ไปใช้ในระบบเทรดจริงได้")

if __name__ == "__main__":
    main()

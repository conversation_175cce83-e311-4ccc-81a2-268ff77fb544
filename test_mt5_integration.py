#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_strong_signal_data():
    """สร้างข้อมูลที่มีแนวโน้มให้ signal แข็งแกร่ง"""
    
    # สร้างข้อมูล 210 แท่งที่มี trend ชัดเจน
    num_bars = 210
    bars = []
    
    # เริ่มต้นด้วยราคา GOLD
    base_time = int(datetime.now().timestamp()) - (num_bars * 1800)  # 30 minutes per bar
    base_price = 2650.0
    
    # สร้าง uptrend ที่ชัดเจนในช่วงท้าย
    for i in range(num_bars):
        # สร้าง uptrend ในช่วง 50 แท่งสุดท้าย
        if i >= num_bars - 50:
            trend_factor = (i - (num_bars - 50)) * 0.5  # เพิ่มขึ้นเรื่อยๆ
        else:
            trend_factor = np.random.normal(0, 0.5)  # random walk ในช่วงแรก
        
        open_price = base_price
        close_price = open_price + trend_factor + np.random.normal(0, 0.3)
        high_price = max(open_price, close_price) + abs(np.random.normal(0.5, 0.2))
        low_price = min(open_price, close_price) - abs(np.random.normal(0.5, 0.2))
        
        bar = {
            "time": base_time + (i * 1800),
            "open": round(open_price, 2),
            "high": round(high_price, 2),
            "low": round(low_price, 2),
            "close": round(close_price, 2),
            "volume": 1000 + i,
            "tick_volume": 1000 + i,
            "spread": 5,
            "real_volume": 1000 + i
        }
        bars.append(bar)
        base_price = close_price
    
    return bars

def test_mt5_integration():
    """ทดสอบการทำงานแบบ end-to-end กับ MT5"""
    
    url = 'http://127.0.0.1:54321/data'
    
    print("🔍 Creating strong signal test data...")
    bars = create_strong_signal_data()
    
    test_data = {
        "symbol": "GOLD",
        "timeframe_str": "PERIOD_M30",
        "bars": bars
    }
    
    print(f"✅ Created {len(bars)} bars with uptrend")
    print(f"🚀 Sending to server...")
    
    try:
        response = requests.post(url, json=test_data, timeout=120)
        
        print(f"✅ Server response: HTTP {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print("\n📊 Server Response Analysis:")
            print("="*50)
            signal = result.get('signal', 'N/A')
            class_name = result.get('class', 'N/A')
            confidence = result.get('confidence', 0.0)
            threshold = result.get('threshold', 0.0)
            
            print(f"Signal: {signal}")
            print(f"Class: {class_name}")
            print(f"Confidence: {confidence:.4f}")
            print(f"Threshold: {threshold:.4f}")
            print(f"Symbol: {result.get('symbol', 'N/A')}")
            print(f"Timeframe: {result.get('timeframe_str', 'N/A')}")
            print(f"Entry Price: {result.get('entry_price', 0.0)}")
            print(f"SL Price: {result.get('sl_price', 0.0)}")
            print(f"TP Price: {result.get('tp_price', 0.0)}")
            print(f"nBars_SL: {result.get('nBars_SL', 0)}")
            print(f"Time Filters: {result.get('time_filters', 'N/A')}")
            
            print("\n🎯 MT5 Integration Analysis:")
            print("="*50)
            
            # ตรวจสอบเงื่อนไขสำหรับ MT5
            if signal in ['BUY', 'SELL']:
                print(f"✅ Signal Type: {signal} (Valid for trading)")
                
                if confidence > 0.5:
                    print(f"✅ Confidence: {confidence:.4f} > 0.5 (Above threshold)")
                    print("🎯 MT5 should open trade!")
                else:
                    print(f"⚠️ Confidence: {confidence:.4f} <= 0.5 (Below threshold)")
                    print("❌ MT5 will NOT open trade (confidence too low)")
                    
            elif signal == 'HOLD':
                print(f"⚠️ Signal Type: {signal} (No trading action)")
                print("❌ MT5 will NOT open trade (HOLD signal)")
                
            elif signal == 'ERROR':
                print(f"❌ Signal Type: {signal} (Server error)")
                print("❌ MT5 will NOT open trade (error signal)")
                
            else:
                print(f"❓ Signal Type: {signal} (Unknown)")
                print("❌ MT5 will NOT open trade (unknown signal)")
            
            # ตรวจสอบข้อมูลราคา
            entry_price = result.get('entry_price', 0.0)
            sl_price = result.get('sl_price', 0.0)
            tp_price = result.get('tp_price', 0.0)
            
            if entry_price > 0 and sl_price > 0 and tp_price > 0:
                print(f"✅ Price Data: Complete (Entry: {entry_price}, SL: {sl_price}, TP: {tp_price})")
            else:
                print(f"⚠️ Price Data: Incomplete (Entry: {entry_price}, SL: {sl_price}, TP: {tp_price})")
            
            # สรุปผลการทดสอบ
            print("\n📋 Summary for MT5:")
            print("="*50)
            
            will_trade = (
                signal in ['BUY', 'SELL'] and 
                confidence > 0.5 and 
                entry_price > 0 and 
                sl_price > 0 and 
                tp_price > 0
            )
            
            if will_trade:
                print("🎉 SUCCESS: MT5 should execute trade!")
                print(f"   Action: {signal}")
                print(f"   Confidence: {confidence:.4f}")
                print(f"   Entry: {entry_price}")
                print(f"   SL: {sl_price}")
                print(f"   TP: {tp_price}")
            else:
                print("❌ FAIL: MT5 will NOT execute trade")
                reasons = []
                if signal not in ['BUY', 'SELL']:
                    reasons.append(f"Signal is {signal} (not BUY/SELL)")
                if confidence <= 0.5:
                    reasons.append(f"Confidence {confidence:.4f} <= 0.5")
                if entry_price <= 0:
                    reasons.append("No entry price")
                if sl_price <= 0:
                    reasons.append("No SL price")
                if tp_price <= 0:
                    reasons.append("No TP price")
                
                print("   Reasons:")
                for reason in reasons:
                    print(f"   - {reason}")
            
            return will_trade
            
        else:
            print(f"❌ Server error: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing MT5 Integration...")
    print("🎯 Goal: Get BUY/SELL signal with confidence > 0.5")
    
    success = test_mt5_integration()
    
    if success:
        print("\n✅ Integration test PASSED - MT5 should trade")
    else:
        print("\n❌ Integration test FAILED - MT5 will not trade")
        print("\n💡 Suggestions:")
        print("   1. Check if model needs retraining")
        print("   2. Adjust threshold values")
        print("   3. Verify time filters allow trading")
        print("   4. Check if data quality is sufficient")

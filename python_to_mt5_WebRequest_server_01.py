#+------------------------------------------------------------------+
#|                                                http_server.py    |
#|                    Receives Bar Data from MT5 via HTTP (Flask) |
#+------------------------------------------------------------------+
from flask import Flask, request, jsonify
import json
import time
import datetime
import MetaTrader5 as mt5
import pandas as pd
import threading
import pandas_ta as ta # Make sure pandas_ta is installed
from werkzeug.exceptions import BadRequest # นำเข้า BadRequest เพื่อจับ Error จาก Flask/Werkzeug

# --- Configuration ---
HTTP_PORT = 54321
HTTP_HOST = '127.0.0.1'

# MT5 Connection Configuration (ยังคงไว้ถ้าต้องการใช้ MT5 Library ใน Python)
MT5_LOGIN = 332940576 # <--- ใส่เลขบัญชี MT5 ของคุณ
MT5_PASSWORD = "UsrL_51180" # <--- ใส่รหัสผ่าน MT5
MT5_SERVER = "XMGlobal-MT5 9" # <--- ใส่ชื่อ Server ของ Broker
MT5_PATH = r"C:\Program Files\XM Global MT5\terminal64.exe" # <--- ใส่ Path ของ MT5 Terminal

# --- Add Timeframe Mapping ---
# Map MQL5 timeframe strings to MetaTrader5 Python timeframe constants
# ใช้สำหรับแปลง String ที่ได้รับจาก MQL5 กลับเป็นค่า enum ที่ Python เข้าใจ
timeframe_map = {
    "PERIOD_M1": mt5.TIMEFRAME_M1,
    "PERIOD_M2": mt5.TIMEFRAME_M2,
    "PERIOD_M3": mt5.TIMEFRAME_M3,
    "PERIOD_M4": mt5.TIMEFRAME_M4,
    "PERIOD_M5": mt5.TIMEFRAME_M5,
    "PERIOD_M6": mt5.TIMEFRAME_M6,
    "PERIOD_M10": mt5.TIMEFRAME_M10,
    "PERIOD_M12": mt5.TIMEFRAME_M12,
    "PERIOD_M15": mt5.TIMEFRAME_M15,
    "PERIOD_M20": mt5.TIMEFRAME_M20,
    "PERIOD_M30": mt5.TIMEFRAME_M30,
    "PERIOD_H1": mt5.TIMEFRAME_H1,
    "PERIOD_H2": mt5.TIMEFRAME_H2,
    "PERIOD_H3": mt5.TIMEFRAME_H3,
    "PERIOD_H4": mt5.TIMEFRAME_H4,
    "PERIOD_H6": mt5.TIMEFRAME_H6,
    "PERIOD_H8": mt5.TIMEFRAME_H8,
    "PERIOD_H12": mt5.TIMEFRAME_H12,
    "PERIOD_D1": mt5.TIMEFRAME_D1,
    "PERIOD_W1": mt5.TIMEFRAME_W1,
    "PERIOD_MN1": mt5.TIMEFRAME_MN1,
    # เพิ่ม Timeframe อื่นๆ ที่คุณต้องการใช้ ที่มีอยู่ใน MQL5 และ MT5 Python Constants
}


# --- Global Data Storage ---
# Key จะเป็น tuple (symbol, timeframe_enum) <--- ใช้ enum ที่แปลงแล้วเป็น key
market_data_store = {}
data_lock = threading.Lock()

# --- Flask App Setup ---
app = Flask(__name__)

# --- MT5 Connection Function (เหมือนเดิม) ---
def initialize_mt5():
    """Initialize connection to the MetaTrader 5 terminal"""
    print("Initializing MT5 connection...")
    if not mt5.is_initialized():
        if not mt5.initialize(login=MT5_LOGIN, password=MT5_PASSWORD, server=MT5_SERVER, path=MT5_PATH):
            print(f"MT5 initialize() failed, error code = {mt5.last_error()}")
            return False
        print("MT5 initialized successfully.")
        account_info = mt5.account_info()
        if account_info:
            print(f"Connected to account: {account_info.login}")
        else:
            print(f"Failed to get account info after initialize, error code = {mt5.last_error()}")
            return False
    else:
        print("MT5 already initialized.")
        account_info = mt5.account_info()
        if account_info:
            print(f"Connected to account: {account_info.login}")
        else:
            print(f"MT5 was initialized but failed to get account info, error code = {mt5.last_error()}")

    return True

# --- Data Processing Function (จะเรียกใช้ใน Thread แยก) ---
# --- แก้ไข: รับ timeframe_enum แทน timeframe_str ---
def process_data_and_trade(symbol, timeframe_enum):
    """
    Placeholder function to calculate indicators, run model, and place orders.
    This should run in a separate thread to avoid blocking the HTTP server.
    """
    # --- แก้ไข: ไม่ต้องแปลง timeframe_str แล้ว เพราะรับ enum มาแล้ว ---
    # หากต้องการชื่อ timeframe string ใน function นี้ อาจจะต้อง pass เพิ่มเข้ามา
    # แต่การใช้ enum ในการดึงข้อมูลจาก DataFrame key และ MT5 Library จะง่ายกว่า
    print(f"[{datetime.datetime.now()}] Processing data for {symbol} (enum: {timeframe_enum})...")


    with data_lock:
        # ใช้ timeframe_enum ที่รับมา เป็น key ใน market_data_store
        if (symbol, timeframe_enum) in market_data_store:
            df = market_data_store[(symbol, timeframe_enum)].copy()
            # print(f"Latest data for {symbol} (enum: {timeframe_enum}):\n{df.tail(1)}") # Uncomment for debugging

            # --- ส่วนคำนวณ Indicator และ Model ---
            try:
                if len(df) > 14:
                    df.ta.sma(length=14, append=True)
                    last_sma = df['SMA_14'].iloc[-1]
                    print(f"Calculated SMA_14 for {symbol} (enum: {timeframe_enum}): {last_sma:.5f}")

                    last_close = df['close'].iloc[-1]
                    if last_close > last_sma:
                        signal = "BUY"
                        print(f"Signal for {symbol} (enum: {timeframe_enum}): {signal}")
                        # --- ส่วนส่งคำสั่งซื้อขาย (ใช้ MT5 Library) ---
                        # ถ้าต้องการส่งคำสั่งซื้อขาย ต้องเรียก initialize_mt5() ก่อน
                        # และโค้ดส่งคำสั่งต้องใช้ timeframe_enum นี้
                        # if mt5.is_initialized():
                        #     # place_order(symbol, signal, timeframe_enum, ...) # <--- เรียกฟังก์ชันส่งคำสั่งจริงๆ ที่นี่
                        #     print(f"Attempting to place {signal} order for {symbol} timeframe {timeframe_enum}...")
                        # else:
                        #     print("MT5 not available to place order.")
                    else:
                        signal = "HOLD/SELL"
                        print(f"Signal for {symbol} (enum: {timeframe_enum}): {signal}")

                else:
                    print(f"Not enough data ({len(df)} bars) to calculate SMA_14 for {symbol} (enum: {timeframe_enum})")

            except Exception as e:
                print(f"Error during calculation/decision for {symbol} (enum: {timeframe_enum}): {e}")

        else:
            print(f"No data yet for {symbol} (enum: {timeframe_enum})")


    print(f"[{datetime.datetime.now()}] Finished processing for {symbol} (enum: {timeframe_enum}).")

# --- HTTP Flask Route to receive data ---
@app.route('/data', methods=['POST'])
def receive_data():
    """Receives JSON data from MT5 EA via HTTP POST."""
    # --- ส่วน Comment check is_json ออกชั่วคราว (ยังไม่ต้องเปิดกลับ) ---
    # if not request.is_json:
    #     print("Received non-JSON request (bypassed check)")
    #     return jsonify({"status": "ERROR", "message": "Request must be JSON"}), 415

    try:
        # --- Get JSON โดยตรง ---
        # force=True: พยายามแปลงเป็น JSON แม้ Content-Type จะไม่ตรงเป๊ะ (เพื่อ Debug)
        # silent=False: ถ้าแปลงไม่ได้ ให้ Raise Error แทนที่จะคืนค่า None
        data = request.get_json(force=True, silent=False)
        print("Successfully received and parsed JSON.") # เพิ่ม Print แจ้งว่าแปลง JSON สำเร็จ

        # --- แยกข้อมูลที่ได้รับ ---
        symbol = data.get('symbol')
        timeframe_str = data.get('timeframe_str') # e.g., "PERIOD_H1"
        bar_time_ts = data.get('time')
        bar_open = data.get('open')
        bar_high = data.get('high')
        bar_low = data.get('low')
        bar_close = data.get('close')
        bar_volume = data.get('tick_volume')

        # ตรวจสอบข้อมูลที่จำเป็นต้องมี
        if not all([symbol, timeframe_str, bar_time_ts, bar_open, bar_high, bar_low, bar_close]):
            print("Received incomplete data after parsing")
            return jsonify({"status": "ERROR", "message": "Incomplete data received after parsing"}), 400

        # --- แก้ไข: แปลง Timeframe String เป็น Enum ของ MT5 โดยใช้ Map ---
        # ใช้ .get() เพื่อป้องกัน Error ถ้า key (timeframe_str) ไม่มีอยู่ใน map
        timeframe_enum = timeframe_map.get(timeframe_str)
        if timeframe_enum is None:
            print(f"Invalid or unknown timeframe string received: {timeframe_str}")
            return jsonify({"status": "ERROR", "message": f"Invalid or unknown timeframe {timeframe_str}"}), 400
        # --- จบการแก้ไข ---

        # แปลง Timestamp เป็น Datetime
        bar_dt = datetime.datetime.fromtimestamp(bar_time_ts)

        # สร้าง DataFrame สำหรับข้อมูลใหม่
        new_data = pd.DataFrame([{
            'time': bar_dt,
            'open': bar_open,
            'high': bar_high,
            'low': bar_low,
            'close': bar_close,
            'volume': bar_volume
        }])
        new_data.set_index('time', inplace=True)

        # อัปเดต Data Store (ใช้ Lock เพื่อความปลอดภัย)
        with data_lock:
            # ใช้ timeframe_enum ที่แปลงแล้ว เป็น key ใน market_data_store
            key = (symbol, timeframe_enum)
            if key in market_data_store:
                # ต่อข้อมูลใหม่ (เช็คก่อนว่าซ้ำไหม โดยเทียบ index คือเวลา)
                if new_data.index[0] not in market_data_store[key].index:
                    # ใช้ pd.concat สำหรับต่อ DataFrame (ในเวอร์ชันใหม่ๆ อาจใช้ df.append ได้แต่กำลังจะเลิกใช้)
                    market_data_store[key] = pd.concat([market_data_store[key], new_data])
                    # จำกัดขนาดข้อมูลเก่าได้ตามต้องการ เช่น เก็บแค่ 200 บาร์ล่าสุด
                    market_data_store[key] = market_data_store[key].tail(200)
                # else: # Uncomment สำหรับ Debug ถ้าเจอ Bar ซ้ำ
                # print(f"Duplicate bar received for {symbol} {timeframe_str} at {bar_dt}")
            else:
                # สร้าง DataFrame ใหม่สำหรับ Symbol/Timeframe นี้
                market_data_store[key] = new_data
                print(f"Started tracking data for {symbol} ({timeframe_str})") # ใช้ timeframe_str ใน Print เพื่อให้คนอ่านเข้าใจง่าย

        # เรียกใช้ฟังก์ชันประมวลผลใน Thread แยก
        # --- แก้ไข: ส่ง timeframe_enum ไปแทน timeframe_str ---
        processing_thread = threading.Thread(target=process_data_and_trade, args=(symbol, timeframe_enum)) # <--- แก้ไขตรงนี้
        processing_thread.start()

        # ส่ง HTTP 200 OK กลับไป
        return jsonify({"status": "OK"}), 200

    # --- แก้ไขให้จับ Werkzeug Exception และเพิ่ม Print Raw Data ---
    except BadRequest as e: # <--- แก้ไขตรงนี้
        print(f"Received data but failed due to Bad Request: {e}")
        # Print Raw Data ที่ได้รับมา เพื่อ Debug ปัญหา Extra Data (ตอนนี้ปัญหานี้น่าจะแก้ได้แล้ว)
        print(f"Raw request data (bytes): {request.data}")
        # ส่ง 400 Bad Request กลับไป พร้อม Message Error จาก Python
        return jsonify({"status": "ERROR", "message": f"Bad Request: {e}"}), 400

    except Exception as e:
        # ถ้ามี Error อื่นๆ ที่ไม่ใช่ Bad Request/JSON Error จะมาที่นี่
        print(f"Error processing request: {e}")
        # ส่ง 500 Internal Server Error กลับไป พร้อม Message Error จาก Python
        return jsonify({"status": "ERROR", "message": f"Server Error: {e}"}), 500


# --- Main Execution ---
if __name__ == "__main__":
    # Initialize MT5 connection if needed for placing trades later
    # initialize_mt5() # คุณอาจจะเรียก initialize_mt5 ตรงนี้ ถ้าต้องการใช้ MT5 Library สำหรับส่งคำสั่งซื้อขาย

    print(f"Starting HTTP Server on http://{HTTP_HOST}:{HTTP_PORT}")
    # Use debug=True for development (auto-reloads on code changes)
    # Set debug=False and possibly use a production WSGI server (like Gunicorn) for production
    app.run(host=HTTP_HOST, port=HTTP_PORT, debug=True)

    # ส่วนนี้จะทำงานเมื่อ Flask Server หยุด
    # print("Shutting down MT5 connection...")
    # mt5.shutdown() # ปิดการเชื่อมต่อ MT5 เมื่อ Server หยุด
    print("Server stopped.")
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
วิเคราะห์ผลลัพธ์การปรับปรุงโมเดล
"""

import pandas as pd
import numpy as np

def analyze_performance_improvement():
    """วิเคราะห์การปรับปรุงประสิทธิภาพ"""
    print("📊 วิเคราะห์ผลลัพธ์การปรับปรุงโมเดล")
    print("=" * 80)
    
    # ข้อมูลผลลัพธ์ใหม่จากการเทรน
    new_results_m30 = {
        'AUDUSD': {'AUC': 0.888040, 'F1': 0.626764, 'CV_AUC': 0.846023},
        'EURGBP': {'AUC': 0.905927, 'F1': 0.605477, 'CV_AUC': 0.868362},
        'EURUSD': {'AUC': 0.884740, 'F1': 0.510248, 'CV_AUC': 0.834187},
        'GBPUSD': {'AUC': 0.893316, 'F1': 0.603585, 'CV_AUC': 0.808650},
        'GOLD': {'AUC': 0.900541, 'F1': 0.703903, 'CV_AUC': 0.767252},
        'NZDUSD': {'AUC': 0.861814, 'F1': 0.620632, 'CV_AUC': 0.500000},  # ปัญหา
        'USDCAD': {'AUC': 0.877999, 'F1': 0.542524, 'CV_AUC': 0.866952},
        'USDJPY': {'AUC': 0.846807, 'F1': 0.409931, 'CV_AUC': 0.850475}
    }
    
    new_results_h1 = {
        'AUDUSD': {'AUC': 0.857307, 'F1': 0.594837, 'CV_AUC': 0.863591},
        'EURGBP': {'AUC': 0.883323, 'F1': 0.445178, 'CV_AUC': 0.849967},
        'EURUSD': {'AUC': 0.858855, 'F1': 0.593913, 'CV_AUC': 0.839356},
        'GBPUSD': {'AUC': 0.871958, 'F1': 0.600737, 'CV_AUC': 0.854875},
        'GOLD': {'AUC': 0.879470, 'F1': 0.586109, 'CV_AUC': 0.808287},
        'NZDUSD': {'AUC': 0.887657, 'F1': 0.648851, 'CV_AUC': 0.804623},
        'USDCAD': {'AUC': 0.891662, 'F1': 0.634250, 'CV_AUC': 0.843978},
        'USDJPY': {'AUC': 0.823358, 'F1': 0.532381, 'CV_AUC': 0.818592}
    }
    
    # รวมผลลัพธ์ทั้งหมด
    all_results = []
    for symbol in new_results_m30.keys():
        all_results.append(new_results_m30[symbol])
        all_results.append(new_results_h1[symbol])
    
    # คำนวณค่าเฉลี่ย
    avg_auc = np.mean([r['AUC'] for r in all_results])
    avg_f1 = np.mean([r['F1'] for r in all_results])
    avg_cv_auc = np.mean([r['CV_AUC'] for r in all_results if r['CV_AUC'] > 0])  # ไม่รวม 0.5
    
    # คำนวณ overfitting gap
    overfitting_gaps = []
    for r in all_results:
        if r['CV_AUC'] > 0.5:  # ไม่รวมค่าที่ผิดปกติ
            gap = r['AUC'] - r['CV_AUC']
            overfitting_gaps.append(gap)
    
    avg_overfitting_gap = np.mean(overfitting_gaps)
    
    # เป้าหมายเดิม
    targets = {
        'AUC': 0.95,
        'F1': 0.85,
        'CV_AUC': 0.90,
        'Overfitting_Gap': 0.05
    }
    
    # ผลลัพธ์เดิม (ก่อนปรับปรุง)
    old_results = {
        'AUC': 0.885,
        'F1': 0.681,
        'CV_AUC': 0.770,
        'Overfitting_Gap': 0.115
    }
    
    print("📈 เปรียบเทียบผลลัพธ์:")
    print("-" * 80)
    print(f"{'Metric':<20} {'เดิม':<10} {'ใหม่':<10} {'เป้าหมาย':<10} {'สถานะ'}")
    print("-" * 80)
    
    metrics = [
        ('AUC', old_results['AUC'], avg_auc, targets['AUC']),
        ('F1 Score', old_results['F1'], avg_f1, targets['F1']),
        ('CV_AUC', old_results['CV_AUC'], avg_cv_auc, targets['CV_AUC']),
        ('Overfitting Gap', old_results['Overfitting_Gap'], avg_overfitting_gap, targets['Overfitting_Gap'])
    ]
    
    for metric_name, old_val, new_val, target_val in metrics:
        if metric_name == 'Overfitting Gap':
            # สำหรับ overfitting gap ต้องการให้ลดลง
            status = "✅ ดีขึ้น" if new_val < old_val else "⚠️ แย่ลง"
            improvement = f"{((old_val - new_val) / old_val) * 100:+.1f}%"
        else:
            # สำหรับ metrics อื่นต้องการให้เพิ่มขึ้น
            status = "✅ ดีขึ้น" if new_val > old_val else "⚠️ แย่ลง"
            improvement = f"{((new_val - old_val) / old_val) * 100:+.1f}%"
        
        print(f"{metric_name:<20} {old_val:<10.3f} {new_val:<10.3f} {target_val:<10.3f} {status}")
        print(f"{'':20} {'':10} {improvement:10} {'':10}")
    
    return avg_auc, avg_f1, avg_cv_auc, avg_overfitting_gap

def analyze_problem_models():
    """วิเคราะห์โมเดลที่มีปัญหา"""
    print("\n🔍 วิเคราะห์โมเดลที่มีปัญหา")
    print("=" * 80)
    
    # โมเดลที่มีปัญหา
    problem_models = [
        ('NZDUSD M30', 0.861814, 0.620632, 0.500000, 'CV_AUC = 0.5 (ปัญหาร้ายแรง)'),
        ('USDJPY M30', 0.846807, 0.409931, 0.850475, 'F1 Score ต่ำมาก (0.41)'),
        ('EURGBP H1', 0.883323, 0.445178, 0.849967, 'F1 Score ต่ำมาก (0.45)'),
        ('EURUSD M30', 0.884740, 0.510248, 0.834187, 'F1 Score ต่ำ (0.51)'),
        ('USDJPY H1', 0.823358, 0.532381, 0.818592, 'AUC ต่ำกว่า 0.85')
    ]
    
    print("โมเดลที่ต้องการการปรับปรุงเพิ่มเติม:")
    print("-" * 80)
    
    for model, auc, f1, cv_auc, issue in problem_models:
        print(f"🔴 {model}:")
        print(f"   AUC: {auc:.3f}, F1: {f1:.3f}, CV_AUC: {cv_auc:.3f}")
        print(f"   ปัญหา: {issue}")
        print()
    
    return problem_models

def suggest_next_improvements():
    """แนะนำการปรับปรุงขั้นต่อไป"""
    print("🚀 แนะนำการปรับปรุงขั้นต่อไป")
    print("=" * 80)
    
    improvements = [
        {
            "priority": "🔥 สูง",
            "issue": "NZDUSD M30 CV_AUC = 0.5",
            "solution": "ตรวจสอบข้อมูล, data leakage, หรือ feature engineering",
            "action": "รัน check_data_quality.py สำหรับ NZDUSD M30"
        },
        {
            "priority": "🔥 สูง", 
            "issue": "F1 Score ต่ำ (< 0.6) ใน 4 โมเดล",
            "solution": "ปรับ class weight และ optimal threshold",
            "action": "ใช้ find_optimal_threshold() และ class_weight custom"
        },
        {
            "priority": "🟡 กลาง",
            "issue": "AUC บางโมเดลยังต่ำกว่า 0.9",
            "solution": "เพิ่ม advanced features",
            "action": "ใช้ feature_engineering_v2.py"
        },
        {
            "priority": "🟢 ต่ำ",
            "issue": "Overfitting gap ยังสูงกว่าเป้าหมาย",
            "solution": "ปรับ regularization เพิ่มเติม",
            "action": "ลด learning_rate หรือเพิ่ม min_data_in_leaf"
        }
    ]
    
    for i, imp in enumerate(improvements, 1):
        print(f"{i}. {imp['priority']} {imp['issue']}")
        print(f"   💡 วิธีแก้: {imp['solution']}")
        print(f"   🔧 การดำเนินการ: {imp['action']}")
        print()

def calculate_parameter_stability_insights():
    """วิเคราะห์ความเสถียรของพารามิเตอร์"""
    print("📊 วิเคราะห์ความเสถียรของพารามิเตอร์")
    print("=" * 80)
    
    # ข้อมูลจาก parameter stability analysis
    stability_data = {
        'learning_rate': {'mean': 0.1388, 'cv': 10.8},
        'num_leaves': {'mean': 13.0, 'cv': 30.8},
        'max_depth': {'mean': 7.5, 'cv': 26.7},
        'min_data_in_leaf': {'mean': 11.875, 'cv': 21.1},
        'feature_fraction': {'mean': 0.7, 'cv': 0.0},
        'bagging_fraction': {'mean': 0.9188, 'cv': 2.7}
    }
    
    print("ความเสถียรของพารามิเตอร์:")
    print("-" * 60)
    
    for param, data in stability_data.items():
        cv = data['cv']
        if cv < 10:
            status = "🟢 เสถียรมาก"
        elif cv < 20:
            status = "🟡 เสถียรปานกลาง"
        elif cv < 30:
            status = "🟠 เสถียรน้อย"
        else:
            status = "🔴 ไม่เสถียร"
        
        print(f"{param:<20} CV: {cv:5.1f}% {status}")
    
    print(f"\n💡 ข้อเสนอแนะ:")
    print("• learning_rate เสถียรดี (CV=10.8%) - ใช้ค่าปัจจุบันได้")
    print("• num_leaves ไม่เสถียร (CV=30.8%) - ควรจำกัดช่วงใน param_dist")
    print("• feature_fraction เสถียรสมบูรณ์ (CV=0%) - ค่าเดียวเพียงพอ")

def main():
    """ฟังก์ชันหลัก"""
    print("🎯 วิเคราะห์ผลลัพธ์การปรับปรุงโมเดล LightGBM")
    print("=" * 80)
    
    # 1. วิเคราะห์ประสิทธิภาพรวม
    avg_auc, avg_f1, avg_cv_auc, avg_gap = analyze_performance_improvement()
    
    # 2. วิเคราะห์โมเดลที่มีปัญหา
    problem_models = analyze_problem_models()
    
    # 3. วิเคราะห์ความเสถียรของพารามิเตอร์
    calculate_parameter_stability_insights()
    
    # 4. แนะนำการปรับปรุงต่อไป
    suggest_next_improvements()
    
    # 5. สรุปผลลัพธ์
    print("🎉 สรุปผลการปรับปรุง")
    print("=" * 80)
    
    print(f"✅ ความสำเร็จ:")
    print(f"   • Parameter stability ดีขึ้น (learning_rate CV=10.8%)")
    print(f"   • ระบบ hyperparameter tuning ทำงานได้สมบูรณ์")
    print(f"   • ไม่มี critical errors ในการเทรน")
    
    print(f"\n⚠️ ปัญหาที่พบ:")
    print(f"   • NZDUSD M30 มีปัญหาร้ายแรง (CV_AUC=0.5)")
    print(f"   • F1 Score ยังต่ำกว่าเป้าหมายใน 4 โมเดล")
    print(f"   • AUC เฉลี่ย {avg_auc:.3f} ยังไม่ถึงเป้าหมาย 0.95")
    
    print(f"\n🚀 ขั้นตอนถัดไป:")
    print(f"   1. แก้ไข NZDUSD M30 เป็นอันดับแรก")
    print(f"   2. ปรับ class weight และ threshold สำหรับ F1 Score")
    print(f"   3. เพิ่ม advanced features สำหรับโมเดลที่ AUC ต่ำ")
    print(f"   4. Fine-tune regularization parameters")

if __name__ == "__main__":
    main()

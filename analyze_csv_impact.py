#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
วิเคราะห์ผลกระทบของการแก้ไข CSV files ต่อ python_LightGBM_15_Tuning.py
"""

import os
import pandas as pd
import re

def analyze_current_csv_reading():
    """วิเคราะห์วิธีการอ่าน CSV ปัจจุบัน"""
    
    print("🔍 วิเคราะห์วิธีการอ่าน CSV ในไฟล์หลัก")
    print("=" * 80)
    
    # อ่านไฟล์หลัก
    with open('python_LightGBM_15_Tuning.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # ค้นหาส่วนที่อ่าน CSV
    csv_reading_pattern = r'df = pd\.read_csv\(file, header=None\).*?df = df\.drop\(index=0\)\.reset_index\(drop=True\)'
    match = re.search(csv_reading_pattern, content, re.DOTALL)
    
    if match:
        print("📖 วิธีการอ่าน CSV ปัจจุบัน:")
        print("-" * 60)
        print(match.group(0))
        print("-" * 60)
        
        print("\n🔍 การวิเคราะห์:")
        print("1. อ่านไฟล์เป็น 1 column ด้วย pd.read_csv(file, header=None)")
        print("2. แยก column ด้วย df[0].str.split('\\t', expand=True)")
        print("3. ลบแถวแรก (header) ออก")
        print("4. Reset index")
        
        print("\n⚠️ ปัญหาที่พบ:")
        print("• วิธีนี้ใช้ได้กับไฟล์ที่มี tab separator")
        print("• แต่ถ้าไฟล์มี comma separator จะไม่ทำงาน")
        print("• การแยก column ด้วย str.split() อาจช้า")
        
    return match is not None

def test_csv_formats():
    """ทดสอบรูปแบบ CSV ที่มีอยู่"""
    
    print("\n🧪 ทดสอบรูปแบบ CSV ที่มีอยู่")
    print("=" * 80)
    
    test_groups = {
        "M30": [
            "NZDUSD#_M30_201905010000_202504302330.csv",
            "USDJPY#_M30_201905010000_202504302330.csv"
        ]
    }
    
    results = {}
    
    for timeframe, files in test_groups.items():
        print(f"\n📊 ทดสอบ {timeframe}:")
        print("-" * 40)
        
        for file_name in files:
            if not os.path.exists(file_name):
                print(f"   ❌ ไม่พบไฟล์: {file_name}")
                continue
            
            print(f"\n📂 ไฟล์: {file_name}")
            
            try:
                # ทดสอบวิธีปัจจุบัน (อ่านแล้วแยก tab)
                df_current = pd.read_csv(file_name, header=None)
                df_current_split = df_current[0].str.split('\t', expand=True)
                
                # ทดสอบอ่านด้วย tab separator
                df_tab = pd.read_csv(file_name, sep='\t', header=None)
                
                # ทดสอบอ่านด้วย comma separator
                df_comma = pd.read_csv(file_name, header=None)
                
                print(f"   🔍 ผลการทดสอบ:")
                print(f"      วิธีปัจจุบัน: {df_current.shape} -> {df_current_split.shape}")
                print(f"      Tab separator: {df_tab.shape}")
                print(f"      Comma separator: {df_comma.shape}")
                
                # ตรวจสอบข้อมูลตัวอย่าง
                print(f"   📋 ตัวอย่างข้อมูล (วิธีปัจจุบัน):")
                if df_current_split.shape[1] > 1:
                    print(f"      แถวแรก: {df_current_split.iloc[0].tolist()[:5]}")
                else:
                    print(f"      ❌ ไม่สามารถแยก column ได้")
                
                results[file_name] = {
                    'current_method': df_current_split.shape,
                    'tab_separator': df_tab.shape,
                    'comma_separator': df_comma.shape,
                    'works_correctly': df_current_split.shape[1] >= 8
                }
                
            except Exception as e:
                print(f"   ❌ เกิดข้อผิดพลาด: {str(e)}")
                results[file_name] = {'error': str(e)}
    
    return results

def analyze_impact_of_fixes():
    """วิเคราะห์ผลกระทบของการแก้ไข"""
    
    print("\n💡 วิเคราะห์ผลกระทบของการแก้ไข CSV")
    print("=" * 80)
    
    print("🎯 สถานการณ์ปัจจุบัน:")
    print("1. ไฟล์หลักใช้วิธี: pd.read_csv() -> str.split('\\t')")
    print("2. วิธีนี้ทำงานได้กับไฟล์ที่มี tab separator")
    print("3. แต่อาจมีปัญหากับไฟล์ที่มี comma separator")
    
    print("\n🔧 หลังจากแก้ไข CSV files:")
    print("1. ไฟล์ใหม่จะมี comma separator (standard CSV)")
    print("2. ไฟล์ใหม่จะมี header columns")
    print("3. ชื่อไฟล์จะเปลี่ยนเป็น SYMBOL_TIMEFRAME_FIXED.csv")
    
    print("\n⚠️ ผลกระทบต่อไฟล์หลัก:")
    print("1. ต้องอัปเดต test_groups ให้ใช้ชื่อไฟล์ใหม่")
    print("2. ต้องเปลี่ยนวิธีการอ่าน CSV:")
    print("   จาก: pd.read_csv(file, header=None) -> str.split('\\t')")
    print("   เป็น: pd.read_csv(file) หรือ pd.read_csv(file, header=0)")
    print("3. ไม่ต้องลบแถวแรก (header) อีกต่อไป")
    print("4. ไม่ต้องใช้ str.split() อีกต่อไป")
    
    print("\n✅ ข้อดีของการแก้ไข:")
    print("• อ่านไฟล์เร็วขึ้น (ไม่ต้องใช้ str.split)")
    print("• มี column names ที่ชัดเจน")
    print("• เป็น standard CSV format")
    print("• ลดความซับซ้อนในการอ่านข้อมูล")

def generate_update_script():
    """สร้างสคริปต์อัปเดตไฟล์หลัก"""
    
    print("\n📝 สร้างสคริปต์อัปเดตไฟล์หลัก")
    print("=" * 80)
    
    update_script = '''
# อัปเดต test_groups ให้ใช้ไฟล์ที่แก้ไขแล้ว
test_groups = {
    "M30": [
        "CSV_Files_Fixed/AUDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/EURGBP_M30_FIXED.csv", 
        "CSV_Files_Fixed/EURUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GBPUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GOLD_M30_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_M30_FIXED.csv"
    ],
    "M60": [
        "CSV_Files_Fixed/AUDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/EURGBP_H1_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_H1_FIXED.csv", 
        "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/GOLD_H1_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_H1_FIXED.csv"
    ]
}

# เปลี่ยนวิธีการอ่าน CSV
# จาก:
# df = pd.read_csv(file, header=None)
# df = df[0].str.split('\\t', expand=True)
# df = df.drop(index=0).reset_index(drop=True)

# เป็น:
df = pd.read_csv(file)  # อ่านไฟล์ที่มี header
# ไม่ต้องแยก column หรือลบแถวแรกอีกต่อไป
'''
    
    with open('update_main_file.txt', 'w', encoding='utf-8') as f:
        f.write("คำแนะนำการอัปเดตไฟล์หลัก\n")
        f.write("=" * 50 + "\n\n")
        f.write(update_script)
    
    print("📋 บันทึกคำแนะนำการอัปเดตไปยัง: update_main_file.txt")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🔍 วิเคราะห์ผลกระทบของการแก้ไข CSV Files")
    print("=" * 80)
    
    # 1. วิเคราะห์วิธีการอ่าน CSV ปัจจุบัน
    has_csv_reading = analyze_current_csv_reading()
    
    # 2. ทดสอบรูปแบบ CSV ที่มีอยู่
    test_results = test_csv_formats()
    
    # 3. วิเคราะห์ผลกระทบ
    analyze_impact_of_fixes()
    
    # 4. สร้างสคริปต์อัปเดต
    generate_update_script()
    
    # 5. สรุปผล
    print("\n🎯 สรุปการวิเคราะห์")
    print("=" * 80)
    
    working_files = sum(1 for result in test_results.values() 
                       if isinstance(result, dict) and result.get('works_correctly', False))
    total_files = len(test_results)
    
    print(f"📊 สถิติ:")
    print(f"   • ไฟล์ทั้งหมด: {total_files}")
    print(f"   • ไฟล์ที่ทำงานถูกต้อง: {working_files}")
    print(f"   • ไฟล์ที่มีปัญหา: {total_files - working_files}")
    
    if working_files == total_files:
        print(f"\n✅ ไฟล์ทั้งหมดทำงานถูกต้องกับวิธีปัจจุบัน")
        print(f"💡 แต่ยังแนะนำให้แก้ไขเพื่อ performance และ maintainability")
    else:
        print(f"\n❌ มีไฟล์ที่ไม่ทำงานถูกต้อง - จำเป็นต้องแก้ไข")
    
    print(f"\n📋 ขั้นตอนถัดไป:")
    print(f"1. รัน fix_all_csv_files.py เพื่อแก้ไขไฟล์ทั้งหมด")
    print(f"2. อัปเดต python_LightGBM_15_Tuning.py ตามคำแนะนำใน update_main_file.txt")
    print(f"3. ทดสอบการทำงานกับไฟล์ที่แก้ไขแล้ว")

if __name__ == "__main__":
    main()

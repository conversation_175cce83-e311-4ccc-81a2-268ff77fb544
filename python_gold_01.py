import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.metrics import (accuracy_score, classification_report, 
                           confusion_matrix, roc_auc_score, 
                           f1_score, precision_score, recall_score,
                           roc_curve, precision_recall_curve, average_precision_score)
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.utils import resample  # เพิ่มสำหรับ oversampling
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import joblib
import os
import plotly.graph_objects as go
import plotly.io as pio
import sys
from joblib import dump, load
import seaborn as sns
from sklearn.ensemble import RandomForestClassifier
import json
from sklearn import __version__ as sklearn_version
from datetime import datetime
import traceback

# ==============================================
# การตั้งค่าและกำหนดค่าพื้นฐาน (Configuration)
# ==============================================

# การตั้งค่าพื้นฐาน
sys.stdout.reconfigure(encoding='utf-8')
Plot_file = True  # ตั้งค่าเพื่อกำหนดว่าจะพล็อตกราฟหรือไม่

# กำหนดไฟล์และโฟลเดอร์
files = ["GOLDm#30.csv"]
output_folder = "results"
os.makedirs(output_folder, exist_ok=True)

# สร้างโฟลเดอร์สำหรับโมเดลแต่ละ timeframe
for timeframe in ['30', '60', '240']:
    os.makedirs(f"models/M{timeframe}", exist_ok=True)
            
# ==============================================
# กลุ่มฟังก์ชันการประมวลผลข้อมูล (Data Processing)
# ==============================================

def check_data_quality(df, file_name):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. check_data_quality .. 📌 ++")
    
    print(f"\n{'='*50}")
    print(f"Data Quality Check for {file_name}")
    print("="*50)
    
    missing = df.isnull().sum()
    print("\n[1] Missing Values:")
    print(missing[missing > 0].to_string())
    
    print("\n[2] Data Types:")
    print(df.dtypes.to_string())
    
    print("\n[3] Descriptive Stats:")
    print(df.describe(include='all').to_string())
    
    print(f"\n[4] Duplicate Rows: {df.duplicated().sum()}")

    fig, ax = plt.subplots(figsize=(10, 5))
    ax.hist(df['Close'], bins=50)
    ax.set_title('Price Distribution')
    plt.savefig(os.path.join(output_folder, f"{file_name}_price_dist.png"))
    plt.close(fig) 
    
    print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. check_data_quality .. ✅ ++")

def select_features(trade_df):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. select_features .. 📌 ++")
    
    """ฟังก์ชันเลือก features แบบครบวงจร"""
    # 1. กำหนดรายการ features ที่อาจมีประโยชน์
    potential_features = [
        # "Entry_Price", "Exit_Price", 
        "Previous_Close", "Volatility", "Volume_MA20", "Volume_Spike", 
        "EMA50", "EMA100", "EMA_diff", "MA_Cross", "Price_above_EMA50", 
        "Rolling_Vol_5", "Rolling_Vol_15", 
        "Dist_EMA50", "Dist_EMA100", 
        "RSI14", "RSI_signal", "RSI_Overbought", "RSI_Oversold", 
        "RSI_Shift", "RSI_Divergence", 
        "Upper_BB", "Lower_BB", "BB_width", 
        "Entry_DayOfWeek", "Entry_Hour", "IsWeekend",
        "IsMorning", "IsAfternoon", "IsEvening", "IsNight"
    ]
    
    # 2. เลือกเฉพาะ features ที่มีอยู่ใน DataFrame และเป็น numeric
    numeric_cols = trade_df.select_dtypes(include=['number']).columns.tolist()
    available_features = [f for f in potential_features if f in numeric_cols and f in trade_df.columns]
    
    if 'Target' not in trade_df.columns:
        print("⚠️ ไม่พบคอลัมน์ Target ในข้อมูล ใช้ features ที่มีทั้งหมด")
        return available_features
    
    # 3. คำนวณความสัมพันธ์กับ Target
    print("\n🔍 ความสัมพันธ์กับ Target (ทั้งหมด):")
    correlation = trade_df[available_features + ['Target']].corr()['Target'].abs().sort_values(ascending=False)
    print(correlation)
    
    # 4. เลือก features ที่มีความสัมพันธ์ > threshold
    corr_threshold = 0.05
    high_corr_threshold = 0.7
    
    # คำนวณความสัมพันธ์กับ Target
    corr_with_target = trade_df[available_features + ['Target']].corr()['Target'].abs()
    selected_features = corr_with_target[corr_with_target > corr_threshold].index.tolist()
    
    # ตรวจสอบ multicollinearity
    corr_matrix = trade_df[selected_features].corr().abs()
    upper = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
    to_drop = [col for col in upper.columns if any(upper[col] > high_corr_threshold)]
    
    # เลือก features ที่มีความสำคัญสูงกว่า
    final_features = [f for f in selected_features if f not in to_drop]
    
    # 4. ตรวจสอบ features ที่จำเป็น
    must_have_features = ['Volatility', 'BB_width', 'RSI14']
    for feat in must_have_features:
        if feat not in final_features and feat in available_features:
            final_features.append(feat)
    
    print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. select_features .. ✅ ++")
    return final_features

def calculate_bollinger_bands(df, window=20):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. calculate_bollinger_bands .. 📌 ++")
    
    """คำนวณ Bollinger Bands แบบป้องกัน missing values"""
    rolling_mean = df['Close'].rolling(window=window).mean()
    rolling_std = df['Close'].rolling(window=window).std()
    
    # คำนวณ bands
    df["Upper_BB"] = rolling_mean + (rolling_std * 2)
    df["Lower_BB"] = rolling_mean - (rolling_std * 2)
    
    # กรณีข้อมูลไม่พอคำนวณ (ช่วงแรกของข้อมูล)
    min_periods = max(1, int(window * 0.5))  # ต้องการข้อมูลอย่างน้อยครึ่งหนึ่งของ window
    df["BB_width"] = np.where(
        df['Close'].rolling(window, min_periods=min_periods).count() >= min_periods,
        df["Upper_BB"] - df["Lower_BB"],
        np.nan
    )
    
    # Backfill เฉพาะช่วงแรกที่คำนวณไม่ได้
    df[['Upper_BB', 'Lower_BB', 'BB_width']] = df[['Upper_BB', 'Lower_BB', 'BB_width']].bfill()
    
    print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. calculate_bollinger_bands .. ✅ ++")
    return df

# แก้ไขปัญหา Data Quality
def handle_missing_values(df):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. handle_missing_values .. 📌 ++")
    
    """จัดการ missing values แบบปรับปรุงแล้ว"""
    # 1. แก้ไข missing values ในคอลัมน์พื้นฐาน
    basic_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
    for col in basic_cols:
        if col in df.columns:
            df[col] = df[col].ffill().bfill()
    
    # 2. ตรวจสอบ technical indicators ที่อาจมี missing
    tech_cols = ['Upper_BB', 'Lower_BB', 'BB_width', 'Previous_Close']
    existing_tech_cols = [col for col in tech_cols if col in df.columns]
    
    # 3. แก้ไข missing ใน indicators
    for col in existing_tech_cols:
        if df[col].isnull().any():
            df[col] = df[col].ffill().bfill()
    
    print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. handle_missing_values .. ✅ ++")
    return df

def load_and_process_data(file):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. load_and_process_data .. 📌 ++")
    
    # 1. โหลดข้อมูลเบื้องต้น
    try:
        df = pd.read_csv(file, header=None)
        if len(df) < 1000:
            print(f"ไฟล์ {file} มีข้อมูลน้อยกว่า 1000 แท่ง ข้ามการคำนวณไฟล์นี้")
            return None, None, None, None, None
    except Exception as e:
        print(f"เกิดข้อผิดพลาดขณะโหลดไฟล์ {file}: {str(e)}")
        return None, None, None, None, None

    # 2. ตั้งชื่อคอลัมน์
    df.columns = ["Date", "Time", "Open", "High", "Low", "Close", "Volume"]

    # 3. สร้าง technical indicators
    
    # เพิ่มฟีเจอร์วันและเวลา
    df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])
    df['DayOfWeek'] = df['DateTime'].dt.dayofweek  # 0=Monday, 6=Sunday
    df['Hour'] = df['DateTime'].dt.hour
    df['IsWeekend'] = (df['DayOfWeek'] >= 5).astype(int)  # 5=Saturday, 6=Sunday
    
    # สร้างฟีเจอร์สำหรับช่วงเวลาที่สำคัญ
    df['IsMorning'] = ((df['Hour'] >= 8) & (df['Hour'] < 12)).astype(int)
    df['IsAfternoon'] = ((df['Hour'] >= 12) & (df['Hour'] < 16)).astype(int)
    df['IsEvening'] = ((df['Hour'] >= 16) & (df['Hour'] < 20)).astype(int)
    df['IsNight'] = ((df['Hour'] >= 20) | (df['Hour'] < 4)).astype(int)
    
    # Price action
    df['Previous_Close'] = df['Close'].shift(1)
    df['Volatility'] = df['High'] - df['Low']
    
    df['Volume_MA20'] = df['Volume'].rolling(20).mean()
    df['Volume_Spike'] = df['Volume'] / (df['Volume_MA20'] + 1e-10)
    
    # EMA Calculation
    df["EMA50"] = df["Close"].ewm(span=50, adjust=False).mean()
    df["EMA100"] = df["Close"].ewm(span=100, adjust=False).mean()
    df["EMA_diff"] = df["EMA50"] - df["EMA100"]
    df['MA_Cross'] = (df['EMA50'] > df['EMA100']).astype(int)
    df["Price_above_EMA50"] = (df["Close"] > df["EMA50"]).astype(int)
    
    # ความผันผวนระยะสั้น
    df['Rolling_Vol_5'] = df['Close'].pct_change().rolling(5).std()
    df['Rolling_Vol_15'] = df['Close'].pct_change().rolling(15).std()
    
    # ระยะทางจาก EMA
    df['Dist_EMA50'] = (df['Close'] - df['EMA50']) / df['EMA50']
    df['Dist_EMA100'] = (df['Close'] - df['EMA100']) / df['EMA100']
    
    # RSI Calculation
    window = 14
    delta = df["Close"].diff(1)
    gain = np.where(delta > 0, delta, 0)
    loss = np.where(delta < 0, -delta, 0)
    avg_gain = pd.Series(gain).rolling(window=window, min_periods=1).mean()
    avg_loss = pd.Series(loss).rolling(window=window, min_periods=1).mean()
    rs = avg_gain / (avg_loss + 1e-10) 
    df["RSI14"] = 100 - (100 / (1 + rs)).fillna(50) 
    df["RSI_signal"] = np.select(
        [df["RSI14"] < 30, df["RSI14"] > 70], 
        [-1, 1], 
        default=0
    )
    df['RSI_Overbought'] = (df['RSI14'] > 70).astype(int)
    df['RSI_Oversold'] = (df['RSI14'] < 30).astype(int)
    
    # RSI Divergence (ต้องปรับตามความเหมาะสม)
    df['RSI_Shift'] = df['RSI14'].shift(2)
    df['RSI_Divergence'] = np.where(
        (df['Close'] > df['Close'].shift(2)) & (df['RSI14'] < df['RSI_Shift']),
        1, np.where(
            (df['Close'] < df['Close'].shift(2)) & (df['RSI14'] > df['RSI_Shift']),
            -1, 0
        )
    )
    
    # Bollinger Bands
    df["Upper_BB"] = df["Close"].rolling(window=20).mean() + (df["Close"].rolling(window=20).std() * 2)
    df["Lower_BB"] = df["Close"].rolling(window=20).mean() - (df["Close"].rolling(window=20).std() * 2)
    df["BB_width"] = df["Upper_BB"] - df["Lower_BB"]
    
    df = calculate_bollinger_bands(df) # คำนวณ Bollinger Bands (แบบปรับปรุง)
    df = handle_missing_values(df) # จัดการ missing values (แบบปรับปรุง)
    
    print(df.tail())
    check_data_quality(df, file)
    
    print("\n🔍 ตรวจสอบข้อมูลก่อนสร้าง trade cycles:")
    print(f"ช่วงเวลาข้อมูล: {df['Date'].min()} ถึง {df['Date'].max()}")
    print(f"ค่าเฉลี่ย Close: {df['Close'].mean():.2f}")
    print(f"ค่า EMA50 ล่าสุด: {df['EMA50'].iloc[-1]:.2f}")
    print(f"ค่า RSI14 ล่าสุด: {df['RSI14'].iloc[-1]:.2f}")
    
    # 4. สร้าง trade cycles
    print("\n🔍 กำลังสร้าง trade cycles...")
    trade_df, stats = create_trade_cycles(df)
    
    # แสดงข้อมูลก่อนตรวจสอบ
    print("\n📌 ข้อมูลก่อนตรวจสอบ:")
    print(f"จำนวนแถวข้อมูลทั้งหมด: {len(df)} ตัวอย่างข้อมูล df")
    print(df.head() if not df.empty else "ไม่มีข้อมูลการซื้อขาย")
    print(f"จำนวนการซื้อขายที่พบ: {len(trade_df)} ตัวอย่างข้อมูล trade_df")
    print(trade_df.head() if not trade_df.empty else "ไม่มีข้อมูลการซื้อขาย")
    
    print("\n📊 สถิติการซื้อขาย:")
    print(f"{'='*40}")
    print(f"{'ประเภท':<20}{'ค่าสถิติ':<20}")
    print(f"{'-'*40}")

    # แสดงสถิติหลัก
    main_stats = {
        'Buy Win%': 'Buy Win%',
        'Buy Expectancy': 'Buy Expectancy',
        'Sell Win%': 'Sell Win%',
        'Sell Expectancy': 'Sell Expectancy',
        'Total Win%': 'Total Win%',
        'Total Expectancy': 'Total Expectancy',
        'Best_Day': 'Best Day',
        'Best_Hour': 'Best Hour'
    }
    
    for key, display_name in main_stats.items():
        if key in stats:
            value = stats[key]
            if isinstance(value, (int, float)):
                print(f"{display_name:<20}{value:<20.2f}")
            else:
                print(f"{display_name:<20}{str(value):<20}")

    # แสดงสถิติรายวัน (ถ้ามี)
    if 'Day_Details' in stats:
        print("\n📊 สถิติรายวัน:")
        print(f"{'วัน':<10}{'Win Rate (%)':<15}{'จำนวนการซื้อขาย':<20}")
        print(f"{'-'*45}")
        day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        for day, data in stats['Day_Details'].items():
            day_name = day_names[day] if isinstance(day, int) and 0 <= day < 7 else str(day)
            print(f"{day_name:<10}{data.get('win_rate', 0):<15.2f}{data.get('total_trades', 0):<20}")
    
    # แสดงสถิติรายชั่วโมง (ถ้ามี)
    if 'Hour_Details' in stats:
        print("\n📊 สถิติรายชั่วโมง:")
        print(f"{'ชั่วโมง':<10}{'Win Rate (%)':<15}{'จำนวนการซื้อขาย':<20}")
        print(f"{'-'*45}")
        for hour, data in sorted(stats['Hour_Details'].items()):
            print(f"{hour:<10}{data.get('win_rate', 0):<15.2f}{data.get('total_trades', 0):<20}")
    
    print(f"{'='*40}")
    
    if trade_df.empty:
        print("\n⚠️ ไม่มีข้อมูลการซื้อขายที่ตรงตามเงื่อนไข")
        print("ℹ️ สาเหตุอาจมาจาก:")
        print("- เงื่อนไขการซื้อขายเข้มงวดเกินไป")
        print("- ข้อมูลไม่เหมาะสมกับกลยุทธ์")
        print("- ช่วงเวลาที่วิเคราะห์ไม่มีสัญญาณซื้อขาย")
        return None, None, None, None, None
    
    # เพิ่มการตรวจสอบว่า trade_df เป็น DataFrame และไม่ว่างเปล่า
    if not isinstance(trade_df, pd.DataFrame) or trade_df.empty:
        print("⚠️ ไม่มีข้อมูลการซื้อขายที่ตรงตามเงื่อนไข")
        return None, None, None, None, None

    # 5. รวม features กับ trade data
    print("\n🔍 กำลังรวม features กับ trade data...")
    
    # แปลงรูปแบบวันที่ให้ตรงกันก่อน merge
    trade_df['Entry_DateTime'] = pd.to_datetime(trade_df['Entry Time'], format='%Y.%m.%d %H:%M')
    df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])
    
    # ตรวจสอบรูปแบบวันที่หลังแปลง
    print("\nตัวอย่าง Entry_DateTime ใน trade_df:", trade_df['Entry_DateTime'].head())
    print("ตัวอย่าง DateTime ใน df:", df['DateTime'].head())
    
    # ทำ merge โดยใช้ merge_asof สำหรับการจับคู่เวลาที่ใกล้ที่สุด
    trade_df = pd.merge_asof(
        trade_df.sort_values('Entry_DateTime'),
        df.sort_values('DateTime')[["DateTime", 
                                "Previous_Close", "Volatility", "Volume_MA20", "Volume_Spike", 
                                "EMA50", "EMA100", "EMA_diff", "MA_Cross", "Price_above_EMA50", 
                                "Rolling_Vol_5", "Rolling_Vol_15", 
                                "Dist_EMA50", "Dist_EMA100", 
                                "RSI14", "RSI_signal", "RSI_Overbought", "RSI_Oversold", 
                                "RSI_Shift", "RSI_Divergence", 
                                "Upper_BB", "Lower_BB", "BB_width"]],
        left_on='Entry_DateTime',
        right_on='DateTime',
        direction='nearest'
    )
    
    # เพิ่มฟีเจอร์วันและเวลา (ก่อนลบ Entry_DateTime)
    trade_df['Entry_DateTime'] = pd.to_datetime(trade_df['Entry Time'])
    trade_df['Entry_DayOfWeek'] = trade_df['Entry_DateTime'].dt.dayofweek
    trade_df['Entry_Hour'] = trade_df['Entry_DateTime'].dt.hour
    trade_df['IsWeekend'] = (trade_df['Entry_DayOfWeek'] >= 5).astype(int)
    trade_df['IsMorning'] = ((trade_df['Entry_Hour'] >= 8) & (trade_df['Entry_Hour'] < 12)).astype(int)
    trade_df['IsAfternoon'] = ((trade_df['Entry_Hour'] >= 12) & (trade_df['Entry_Hour'] < 16)).astype(int)
    trade_df['IsEvening'] = ((trade_df['Entry_Hour'] >= 16) & (trade_df['Entry_Hour'] < 20)).astype(int)
    trade_df['IsNight'] = ((trade_df['Entry_Hour'] >= 20) | (trade_df['Entry_Hour'] < 4)).astype(int)
    
    # ลบคอลัมน์ชั่วคราวที่สร้างขึ้น
    trade_df.drop(['Entry_DateTime', 'DateTime'], axis=1, inplace=True, errors='ignore')
    
    # ตรวจสอบหลัง merge
    print("\nจำนวน missing values หลัง merge:", trade_df.isnull().sum())
    
    # แสดงข้อมูลหลังรวม features
    print("\n📌 ข้อมูลหลังรวม features:")
    print(f"จำนวนแถว: {len(trade_df)}")
    print("ตัวอย่างคอลัมน์ใหม่ 5 แถว:")
    print(trade_df[['Entry Time', 'EMA50', 'EMA100', 'RSI14']].head())

    # เพิ่มใน features ที่จะใช้สำหรับโมเดล >> เนื่องจากใช้การคัดเลือกอัตโนมัติ ดูความสัมพันธ์กับ Target
    features = []
    
    # 6. สร้าง target variable
    print("\n🔍 กำลังสร้าง target variable...")
    
    # ตรวจสอบว่าคอลัมน์ Profit มีอยู่และเป็นตัวเลข
    if 'Profit' not in trade_df.columns or not pd.api.types.is_numeric_dtype(trade_df['Profit']):
        print("⚠️ ไม่พบคอลัมน์ Profit หรือไม่ใช่ตัวเลข ไม่สามารถสร้าง Target ได้")
        return None, None, None, None, None
    
    trade_df = process_trade_targets(trade_df)
    print("\n📌 ข้อมูลหลังสร้าง target variable:")
    print(f"จำนวนแถว: {len(trade_df)}")
    print("ตัวอย่างคอลัมน์ใหม่ 5 แถว:")
    print(trade_df[['Entry Time', 'EMA50', 'EMA100', 'RSI14']].head())
    
    # แสดงข้อมูล target
    print("\n📌 ข้อมูลหลังสร้าง target:")
    if 'Target' in trade_df.columns:
        print("การกระจายของ Target:")
        print(trade_df['Target'].value_counts())
    else:
        print("ไม่พบคอลัมน์ Target ในข้อมูล")
    
    # เพิ่มการตรวจสอบว่า trade_df ไม่ว่างเปล่าและมีคอลัมน์ Target
    if trade_df.empty or 'Target' not in trade_df.columns:
        print("\n⚠️ ไม่สามารถสร้าง Target ได้")
        return None, None, None, None, None
    
    # ทำความสะอาดข้อมูล
    trade_df = trade_df.dropna()  # ลบแถวที่มี missing values
    
    # แก้ไขชื่อ features ให้สอดคล้องกัน
    trade_df = trade_df.rename(columns={'Entry Price': 'Entry_Price', 'Exit Price': 'Exit_Price'})

    # ==============================================
    # ส่วนปรับปรุง: ใช้ฟังก์ชัน select_features แทนกระบวนการเดิม
    # ==============================================

    # เรียกใช้ฟังก์ชัน select_features
    print("\n🔍 เริ่มกระบวนการเลือก Features...")
    features = select_features(trade_df)
    
    # แสดงสรุป features ที่จะใช้
    print("\n📌 สรุป Features ที่จะใช้สำหรับโมเดล:")
    print(f"จำนวน Features: {len(features)}")
    print("รายการ Features:")
    for i, feat in enumerate(features, 1):
        print(f"{i}. {feat}")

    # ==============================================
    # ส่วนตรวจสอบ Class Imbalance
    # ==============================================
    
    print("\n🔍 กำลังตรวจสอบข้อมูลก่อนฝึกโมเดล...")

    # 1. ตรวจสอบการกระจายของ Target
    if 'Target' in trade_df.columns:
        target_dist = trade_df['Target'].value_counts(normalize=True)
        print("\n📊 การกระจายของ Target:")
        print(target_dist)
        
        # ตรวจสอบว่ามีทั้งสองคลาสหรือไม่
        if len(target_dist) < 2:
            print("⚠️ มีเพียงคลาสเดียวใน Target ไม่สามารถฝึกโมเดลได้")
            return None, None, None, None, None
            
        # ตรวจสอบ Class Imbalance
        imbalance_ratio = target_dist.min() / target_dist.max()
        print(f"อัตราส่วน Class Imbalance: {imbalance_ratio:.2f}")
        
        if imbalance_ratio < 0.2:  # ถ้าคลาส minority มีน้อยกว่า 20% ของคลาส majority
            print("⚠️ พบ Class Imbalance รุนแรง (อัตราส่วนน้อยกว่า 20%)")

    # 2. ตรวจสอบจำนวนข้อมูลขั้นต่ำ
    min_samples = 50  # กำหนดค่าต่ำสุดตามความเหมาะสม
    if len(trade_df) < min_samples:
        print(f"⚠️ ข้อมูลมีน้อยเกินไป ({len(trade_df)} แถว) ขั้นต่ำที่ต้องการ: {min_samples} แถว")
        return None, None, None, None, None

    # 3. ตรวจสอบ missing values
    missing_values = trade_df[features].isnull().sum().sum()
    if missing_values > 0:
        print(f"⚠️ พบ missing values {missing_values} ค่า ใน features")
        # แสดงคอลัมน์ที่มี missing values
        print("คอลัมน์ที่มี missing values:")
        print(trade_df[features].isnull().sum()[trade_df[features].isnull().sum() > 0])
    else:
        print("✅ ไม่พบ missing values ใน features")

    # 4. ตรวจสอบค่าผิดปกติใน features
    print("\n📊 สถิติพื้นฐานของ features:")
    print(trade_df[features].describe().transpose())

    # 5. ตรวจสอบ correlation สูงระหว่าง features
    print("\n🔍 กำลังตรวจสอบความสัมพันธ์ระหว่าง features...")
    try:
        corr_matrix = trade_df[features].corr().abs()
        
        # สร้าง upper triangle matrix แบบ boolean
        upper = np.triu(np.ones(corr_matrix.shape, dtype=bool), k=1)
        
        # นับจำนวนคู่ที่มี correlation สูง
        high_corr = (corr_matrix.where(upper) > 0.8).sum().sum()
        
        if high_corr > 0:
            print(f"⚠️ พบ {high_corr} คู่ features ที่มีความสัมพันธ์สูง (>0.8)")
            
            # แสดงคู่ features ที่มีความสัมพันธ์สูง
            high_corr_pairs = corr_matrix.stack()[
                (corr_matrix.stack() > 0.8) & 
                (corr_matrix.stack() < 1.0)  # ไม่รวมความสัมพันธ์กับตัวเอง
            ].reset_index()
            
            high_corr_pairs.columns = ['Feature 1', 'Feature 2', 'Correlation']
            print("\nคู่ features ที่มีความสัมพันธ์สูง:")
            print(high_corr_pairs.sort_values('Correlation', ascending=False).to_string(index=False))
        else:
            print("✅ ไม่พบ features ที่มีความสัมพันธ์สูงเกินไป")
            
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดขณะตรวจสอบความสัมพันธ์ระหว่าง features: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # ==============================================
    # แบ่งข้อมูลเป็น train/val/test
    # ==============================================
    
    # แบ่งข้อมูลเป็น train/val/test
    print("\n🔍 กำลังแบ่งข้อมูลเป็น train/val/test...")
    if len(trade_df) < 10:
        print(f"\n⚠️ ข้อมูลใน trade_df มีน้อยเกินไป ({len(trade_df)} แถว)")
        print("ตัวอย่างข้อมูลสุดท้าย 5 แถว:")
        print(trade_df.tail())
        return None, None, None, None, None

    test_size = max(1, int(len(trade_df) * 0.15))  # อย่างน้อย 1 แถว
    val_size = max(1, int(len(trade_df) * 0.20))   # อย่างน้อย 1 แถว
    
    # ตรวจสอบว่ามีข้อมูลพอสำหรับ train หรือไม่
    if len(trade_df) <= (test_size + val_size):
        print(f"⚠️ ข้อมูลไม่เพียงพอสำหรับแบ่ง train/val/test (มี {len(trade_df)} แถว)")
        return None, None, None, None, None

    train = trade_df.iloc[:-(test_size + val_size)]
    val = trade_df.iloc[-(test_size + val_size):-test_size]
    test = trade_df.iloc[-test_size:]

    # ตรวจสอบว่ามีข้อมูลในชุด train หรือไม่
    if len(train) == 0:
        print("⚠️ ไม่มีข้อมูลในชุด train หลังการแบ่ง")
        return None, None, None, None, None

    X_train, y_train = train[features], train["Target"]
    X_val, y_val = val[features], val["Target"]
    X_test, y_test = test[features], test["Target"]

    # 6. ทำ Feature Scaling
    print("\n🔍 กำลังทำ Feature Scaling...")
    try:
        scaler = StandardScaler()
        
        # ตรวจสอบว่ามีข้อมูลใน X_train หรือไม่
        if len(X_train) == 0:
            print("⚠️ ไม่มีข้อมูลใน X_train ไม่สามารถทำ Feature Scaling ได้")
            return None, None, None, None, None
            
        X_train_scaled = scaler.fit_transform(X_train)
        X_train = pd.DataFrame(X_train_scaled, columns=features, index=X_train.index)
        
        X_val_scaled = scaler.transform(X_val)
        X_val = pd.DataFrame(X_val_scaled, columns=features, index=X_val.index)
        
        X_test_scaled = scaler.transform(X_test)
        X_test = pd.DataFrame(X_test_scaled, columns=features, index=X_test.index)
        
        print("✅ ทำ Feature Scaling เรียบร้อยแล้ว")
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดขณะทำ Feature Scaling: {str(e)}")
        return None, None, None, None, None

    print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. load_and_process_data .. ✅ ++")
    return (X_train, y_train), (X_val, y_val), (X_test, y_test), df, trade_df

# ปรับปรุงโมเดลและพารามิเตอร์
def get_lgbm_params(y=None, use_scale_pos_weight=True):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. get_lgbm_params .. 📌 ++")
    
    """
    สร้างพารามิเตอร์มาตรฐานสำหรับ LightGBM
    Args:
        y: ข้อมูล target สำหรับคำนวณ class ratio (ถ้าไม่ระบุจะไม่ใช้ scale_pos_weight)
        use_scale_pos_weight: กำหนดว่าจะใช้ scale_pos_weight หรือไม่
    """
    # params = {
    #     'objective': 'binary',
    #     'metric': ['binary_error', 'auc'],
    #     'learning_rate': 0.05,
    #     'num_leaves': 63,
    #     'max_depth': -1,
    #     'min_data_in_leaf': 50,  # สามารถปรับค่าเป็น 20 หากพบปัญหา feature importance เป็น 0
    #     'feature_fraction': 0.8,
    #     'bagging_fraction': 0.8,
    #     'bagging_freq': 5,
    #     'verbosity': -1,
    #     # 'force_row_wise': True,
    #     'random_state': 42
    # }
    
    """พารามิเตอร์ที่ปรับปรุงแล้วสำหรับ LightGBM"""
    params = {
        'objective': 'binary',
        'metric': ['auc', 'binary_logloss'],
        'boosting_type': 'gbdt',
        'learning_rate': 0.03,  # ลด learning rate เพื่อความเสถียร
        'num_leaves': 31,       # ลดความซับซ้อน
        'max_depth': -1,        # ไม่จำกัดความลึก (ใช้คู่กับ min_data_in_leaf)
        'min_data_in_leaf': 30, # เพิ่มขึ้นเพื่อลด overfitting
        'feature_fraction': 0.7, # ใช้เพียง 70% ของ features ในแต่ละ tree
        'bagging_fraction': 0.8, # ใช้เพียง 80% ของข้อมูลในแต่ละ iteration
        'bagging_freq': 5,
        'lambda_l1': 0.2,      # เพิ่ม L1 regularization
        'lambda_l2': 0.2,      # เพิ่ม L2 regularization
        'verbosity': -1,
        'random_state': 42
    }
    
    # ปรับ class weight หากมีข้อมูล y
    if y is not None:
        class_ratio = np.sum(y == 0) / np.sum(y == 1) if np.sum(y == 1) > 0 else 1
        params['scale_pos_weight'] = min(class_ratio, 10)  # จำกัดไม่ให้ weight มากเกินไป
    
    print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. get_lgbm_params .. ✅ ++")
    return params

def test_random_forest(X_train, y_train, X_test, y_test, features):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. test_random_forest .. 📌 ++")
    
    """ทดสอบ RandomForest เพื่อเปรียบเทียบ"""
    print("\n🔍 กำลังทดสอบ RandomForest...")
    from sklearn.ensemble import RandomForestClassifier
    
    # ตรวจสอบว่ามีทั้งสองคลาสหรือไม่
    if len(np.unique(y_train)) < 2:
        print("⚠️ ข้อมูลมีเพียงคลาสเดียว ไม่สามารถฝึก RandomForest ได้")
        return pd.DataFrame({'Feature': features, 'Importance': np.zeros(len(features))})
    
    rf = RandomForestClassifier(n_estimators=100, random_state=42)
    rf.fit(X_train, y_train)
    
    # Feature Importance
    rf_importance = pd.DataFrame({
        'Feature': features,
        'Importance': rf.feature_importances_
    }).sort_values('Importance', ascending=False)
    
    print("\n📊 RandomForest Feature Importance:")
    print(rf_importance.head(15).to_string(index=False))
    
    # ทำนายและประเมินผล
    y_pred = rf.predict(X_test)
    print("\n📈 RandomForest Performance:")
    print(classification_report(y_test, y_pred))
    
    # บันทึกผลลัพธ์
    rf_path = os.path.join(output_folder, "random_forest_feature_importance.csv")
    rf_importance.to_csv(rf_path, index=False)
    print(f"💾 บันทึก RandomForest importance ที่: {rf_path}")
    
    print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. test_random_forest .. ✅ ++")
    return rf_importance

# ปรับปรุงการ Validation
def time_series_cv(X, y, timeframe, n_splits=5):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. time_series_cv .. 📌 ++")
    
    """Time Series Cross-Validation แบบปรับปรุงพร้อมระบบแจ้งเตือน"""
    from sklearn.model_selection import TimeSeriesSplit
    import warnings
    
    # ตรวจสอบข้อมูลเบื้องต้น
    if X.empty or y.empty:
        print("⚠️ ข้อผิดพลาด: ข้อมูล X หรือ y ว่างเปล่า")
        return {
            'accuracy': 0,
            'auc': 0.5,
            'f1': 0,
            'precision': 0,
            'recall': 0
        }
    
    if len(X) != len(y):
        print("⚠️ ข้อผิดพลาด: จำนวนข้อมูล X และ y ไม่เท่ากัน")
        return {
            'accuracy': 0,
            'auc': 0.5,
            'f1': 0,
            'precision': 0,
            'recall': 0
        }
    
    print(f"\n🔁 เริ่มทำ Time Series Cross-Validation (n_splits={n_splits})")
    
    try:
        tscv = TimeSeriesSplit(n_splits=n_splits)
        metrics = {
            'accuracy': [],
            'auc': [],
            'f1': [],
            'precision': [],
            'recall': []
        }
        
        for fold, (train_idx, val_idx) in enumerate(tscv.split(X), 1):
            print(f"\n📊 Fold {fold}/{n_splits}:")
            print(f"  - Train size: {len(train_idx)} ตัวอย่าง")
            print(f"  - Val size:   {len(val_idx)} ตัวอย่าง")
            
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            # ตรวจสอบ class distribution
            class_dist = pd.Series(y_train).value_counts()
            if len(class_dist) < 2:
                print(f"⚠️ เตือน: ใน Fold {fold} มีเพียงคลาสเดียวในข้อมูลฝึก ({class_dist.to_dict()})")
                continue
            
            # สร้างและฝึกโมเดล
            try:
                print("  🏗️ กำลังสร้างโมเดล...")
                
                # ใช้ LGBMClassifier แบบถูกต้อง
                model = lgb.LGBMClassifier(
                    **get_lgbm_params(y_train),
                    n_estimators=1000,  # จำนวน trees สูงๆ
                    verbose=-1
                )
                
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    model.fit(
                        X_train, y_train,
                        eval_set=[(X_val, y_val)],
                        eval_metric='auc',
                        callbacks=[
                            lgb.early_stopping(stopping_rounds=50, verbose=False),
                            lgb.log_evaluation(0)
                        ]
                    )
                
                print(f"  ✅ สร้างโมเดลสำเร็จ (ใช้ {model.n_estimators_} trees)")
                
                # ทำนายและประเมินผล
                y_pred = model.predict_proba(X_val)[:, 1]
                y_pred_bin = (y_pred > 0.5).astype(int)
                
                # คำนวณ metrics
                fold_metrics = {
                    'accuracy': accuracy_score(y_val, y_pred_bin),
                    'auc': roc_auc_score(y_val, y_pred),
                    'f1': f1_score(y_val, y_pred_bin, zero_division=0),
                    'precision': precision_score(y_val, y_pred_bin, zero_division=0),
                    'recall': recall_score(y_val, y_pred_bin, zero_division=0)
                }
                
                # บันทึกผลลัพธ์
                for k in metrics:
                    metrics[k].append(fold_metrics[k])
                
                print(f"  📊 ผลลัพธ์ Fold {fold}:")
                print(f"    - Accuracy:  {fold_metrics['accuracy']:.4f}")
                print(f"    - AUC:      {fold_metrics['auc']:.4f}")
                print(f"    - F1 Score: {fold_metrics['f1']:.4f}")
                print(f"    - Precision: {fold_metrics['precision']:.4f}")
                print(f"    - Recall:    {fold_metrics['recall']:.4f}")
                
            except Exception as e:
                print(f"⚠️ ข้อผิดพลาดใน Fold {fold}: {str(e)}")
                print("  ลองใช้วิธีแบบไม่มี early stopping...")
                try:
                    model = lgb.LGBMClassifier(**get_lgbm_params(y_train), n_estimators=100)
                    model.fit(X_train, y_train)
                    y_pred = model.predict_proba(X_val)[:, 1]
                    y_pred_bin = (y_pred > 0.5).astype(int)
                    
                    fold_metrics = {
                        'accuracy': accuracy_score(y_val, y_pred_bin),
                        'auc': roc_auc_score(y_val, y_pred),
                        'f1': f1_score(y_val, y_pred_bin, zero_division=0),
                        'precision': precision_score(y_val, y_pred_bin, zero_division=0),
                        'recall': recall_score(y_val, y_pred_bin, zero_division=0)
                    }
                    
                    for k in metrics:
                        metrics[k].append(fold_metrics[k])
                    
                    print(f"  ✅ สำรองสร้างโมเดลสำเร็จ (ใช้ 100 trees)")
                    print(f"  📊 ผลลัพธ์ Fold {fold}:")
                    print(f"    - Accuracy:  {fold_metrics['accuracy']:.4f}")
                    print(f"    - AUC:      {fold_metrics['auc']:.4f}")
                    print(f"    - F1 Score: {fold_metrics['f1']:.4f}")
                    
                except Exception as e2:
                    print(f"⚠️ ข้อผิดพลาดร้ายแรงใน Fold {fold}: {str(e2)}")
                    continue
        
        # คำนวณค่าเฉลี่ย metrics
        avg_metrics = {k: np.mean(v) if v else 0 for k, v in metrics.items()}
        
        print("\n✅ การทำ Cross-Validation เสร็จสมบูรณ์")
        print("📌 ผลลัพธ์เฉลี่ย:")
        print(f"  - Accuracy:  {avg_metrics['accuracy']:.4f}")
        print(f"  - AUC:      {avg_metrics['auc']:.4f}")
        print(f"  - F1 Score: {avg_metrics['f1']:.4f}")
        print(f"  - Precision: {avg_metrics['precision']:.4f}")
        print(f"  - Recall:    {avg_metrics['recall']:.4f}")
        
        print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. time_series_cv .. ✅ ++")
        return avg_metrics
    
    except Exception as e:
        print(f"⚠️ ข้อผิดพลาดร้ายแรงใน time_series_cv: {str(e)}")
        
        print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. time_series_cv .. ✅ ++")
        return {
            'accuracy': 0,
            'auc': 0.5,
            'f1': 0,
            'precision': 0,
            'recall': 0
        }

# ปรับปรุงการประเมินผล
def enhanced_evaluation(model, X_test, y_test, output_folder, timeframe):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. enhanced_evaluation .. 📌 ++")
    
    """การประเมินผลแบบละเอียดพร้อมการแยกไฟล์ตาม timeframe"""
    from sklearn.metrics import (roc_curve, precision_recall_curve, 
                               average_precision_score, roc_auc_score,
                               accuracy_score, f1_score, precision_score,
                               recall_score, confusion_matrix, classification_report)
    import matplotlib.pyplot as plt
    import os
    
    try:
        print(f"\n🔍 เริ่มการประเมินผลโมเดลแบบละเอียด (M{timeframe})...")
        
        # สร้างโฟลเดอร์ย่อยตาม timeframe ถ้ายังไม่มี
        timeframe_folder = os.path.join(output_folder, f"M{timeframe}")
        os.makedirs(timeframe_folder, exist_ok=True)
        print(f"📁 สร้างโฟลเดอร์สำหรับ timeframe M{timeframe} แล้ว")
        
        # ทำนายความน่าจะเป็น
        y_probs = model.predict(X_test, num_iteration=model.best_iteration)
        y_pred = (y_probs > 0.5).astype(int)
        
        # สร้างรายงานแบบละเอียด
        clf_report = classification_report(y_test, y_pred, output_dict=True)
        for key in clf_report:
            if isinstance(clf_report[key], dict):
                for metric in clf_report[key]:
                    if isinstance(clf_report[key][metric], (int, float)):
                        clf_report[key][metric] = float(clf_report[key][metric])
                    elif metric == 'support':
                        clf_report[key][metric] = int(clf_report[key][metric])

        print("\nClassification Report:")
        print(pd.DataFrame(clf_report).transpose())
        
        print("📝 กำลังคำนวณ metrics...")
        report = {
            'timeframe': timeframe,
            'accuracy': accuracy_score(y_test, y_pred),
            'auc_roc': roc_auc_score(y_test, y_probs),
            'auc_pr': average_precision_score(y_test, y_probs),
            'f1': f1_score(y_test, y_pred),
            'precision': precision_score(y_test, y_pred),
            'recall': recall_score(y_test, y_pred),
            'confusion_matrix': confusion_matrix(y_test, y_pred),
            'classification_report': clf_report  # ใช้ report ที่แก้ไขแล้ว
        }
        
        # พล็อต ROC และ Precision-Recall curves
        print("📊 กำลังสร้าง visualization...")
        plt.figure(figsize=(12, 5))
        
        # ROC Curve
        plt.subplot(1, 2, 1)
        fpr, tpr, _ = roc_curve(y_test, y_probs)
        plt.plot(fpr, tpr, label=f'AUC = {report["auc_roc"]:.3f}')
        plt.plot([0, 1], [0, 1], 'k--')
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title(f'ROC Curve (M{timeframe})')
        plt.legend()
        
        # Precision-Recall Curve
        plt.subplot(1, 2, 2)
        precision, recall, _ = precision_recall_curve(y_test, y_probs)
        plt.plot(recall, precision, label=f'AP = {report["auc_pr"]:.3f}')
        plt.xlabel('Recall')
        plt.ylabel('Precision')
        plt.title(f'Precision-Recall Curve (M{timeframe})')
        plt.legend()
        
        plt.tight_layout()
        
        # บันทึกไฟล์ภาพ (ระบุ timeframe ในชื่อไฟล์)
        plot_filename = f'performance_curves_M{timeframe}.png'
        plot_path = os.path.join(timeframe_folder, plot_filename)
        plt.savefig(plot_path)
        plt.close()
        print(f"✅ บันทึกกราฟประสิทธิภาพที่: {plot_path}")
        
        # บันทึกรายงานเป็นไฟล์ CSV (ระบุ timeframe ในชื่อไฟล์)
        report_filename = f'evaluation_report_M{timeframe}.csv'
        report_path = os.path.join(timeframe_folder, report_filename)
        pd.DataFrame.from_dict(report['classification_report']).transpose().to_csv(report_path)
        print(f"✅ บันทึกรายงานการประเมินที่: {report_path}")
        
        print(f"🎯 การประเมินผลสำหรับ M{timeframe} เสร็จสมบูรณ์!")
        
        print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. enhanced_evaluation .. ✅ ++")
        return report
        
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดขณะประเมินผล M{timeframe}: {str(e)}")
        import traceback
        traceback.print_exc()
        
        print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. enhanced_evaluation .. ✅ ++")
        return None

def compare_feature_importance(lgb_importance, rf_importance, timeframe, output_folder):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. compare_feature_importance .. 📌 ++")
    
    """เปรียบเทียบ Feature Importance ระหว่าง LightGBM และ RandomForest"""
    try:
        # สร้าง DataFrame สำหรับเปรียบเทียบ
        comparison = pd.merge(
            lgb_importance[['Feature', 'Gain']].rename(columns={'Gain': 'LightGBM'}),
            rf_importance[['Feature', 'Importance']].rename(columns={'Importance': 'RandomForest'}),
            on='Feature',
            how='outer'
        ).fillna(0)
        
        # Normalize importance scores
        comparison['LightGBM'] = comparison['LightGBM'] / comparison['LightGBM'].max()
        comparison['RandomForest'] = comparison['RandomForest'] / comparison['RandomForest'].max()
        
        # เรียงลำดับตาม LightGBM importance
        comparison = comparison.sort_values('LightGBM', ascending=False).head(20)
        
        # พล็อตกราฟ
        plt.figure(figsize=(12, 8))
        comparison.set_index('Feature').plot(kind='barh', color=['skyblue', 'salmon'])
        plt.title(f'Feature Importance Comparison (M{timeframe})', fontsize=14)
        plt.xlabel('Normalized Importance Score', fontsize=12)
        plt.ylabel('Features', fontsize=12)
        plt.legend(title='Model')
        plt.tight_layout()
        
        # บันทึกรูปภาพ
        comp_path = os.path.join(output_folder, f"feature_importance_comparison_M{timeframe}.png")
        plt.savefig(comp_path, dpi=150, bbox_inches='tight')
        plt.close()
        print(f"💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: {comp_path}")
        
        # บันทึกเป็น CSV
        csv_path = os.path.join(output_folder, f"feature_importance_comparison_M{timeframe}.csv")
        comparison.to_csv(csv_path, index=False)
        print(f"💾 บันทึกตารางเปรียบเทียบที่: {csv_path}")
        
        print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. compare_feature_importance .. ✅ ++")
        
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดขณะเปรียบเทียบ Feature Importance: {str(e)}")
        
        print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. compare_feature_importance .. ✅ ++")

def train_and_evaluate(model, model_name, train_data, val_data, test_data, timeframe):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. train_and_evaluate .. 📌 ++")
    
    """ฟังก์ชันฝึกและประเมินโมเดล"""
    # 1. เตรียมข้อมูล
    X_train, y_train = train_data
    X_val, y_val = val_data
    X_test, y_test = test_data
    
    # ตรวจสอบและจัดเรียง features ให้ตรงกับโมเดลเดิม (ถ้ามี)
    if model is not None:
        try:
            # ดึง features จากโมเดลเดิม
            model_features = model.feature_name()
            
            # ตรวจสอบว่า features ในข้อมูลตรงกับโมเดลเดิมหรือไม่
            if set(model_features) != set(X_train.columns):
                print("\n⚠️ เตือน: Features ในข้อมูลไม่ตรงกับโมเดลเดิม")
                print(f"Features ในโมเดลเดิม: {len(model_features)} features")
                print(f"Features ในข้อมูลปัจจุบัน: {len(X_train.columns)} features")
                
                # หา features ที่หายไป
                missing_features = set(model_features) - set(X_train.columns)
                if missing_features:
                    print(f"Features ที่หายไป: {missing_features}")
                
                # หา features ใหม่
                extra_features = set(X_train.columns) - set(model_features)
                if extra_features:
                    print(f"Features ใหม่: {extra_features}")
                
                # จัดเรียง features ให้ตรงกับโมเดลเดิม และเพิ่ม missing features เป็น 0
                X_train = X_train.reindex(columns=model_features, fill_value=0)
                X_val = X_val.reindex(columns=model_features, fill_value=0)
                X_test = X_test.reindex(columns=model_features, fill_value=0)
                
                print("✅ ปรับ features ให้ตรงกับโมเดลเดิมแล้ว (features ใหม่ถูกตั้งค่าเป็น 0)")
        except Exception as e:
            print(f"⚠️ เกิดข้อผิดพลาดขณะตรวจสอบ features: {str(e)}")
    
    # ==============================================
    # ส่วนเพิ่มเติม: ทดสอบ RandomForest ก่อนฝึก LightGBM
    # ==============================================
    print("\n" + "="*50)
    print("  การทดสอบเปรียบเทียบกับ RandomForest  ")
    print("="*50)
    rf_importance = test_random_forest(X_train, y_train, X_test, y_test, X_train.columns.tolist())
    
    # 2. ตรวจสอบการกระจายของคลาส
    print("\n📊 การกระจายของคลาส:")
    print(f"Train - 0: {sum(y_train==0)}, 1: {sum(y_train==1)}")
    print(f"Test - 0: {sum(y_test==0)}, 1: {sum(y_test==1)}")
    
    # 3. คำนวณ class ratio
    class_ratio = len(y_train[y_train==0]) / len(y_train[y_train==1]) if len(y_train[y_train==1]) > 0 else 1
    
    # 4. ทำ Cross-Validation
    print("\n🔍 เริ่มทำ Cross-Validation...")
    X_full = pd.concat([X_train, X_val])
    y_full = pd.concat([y_train, y_val])
    cv_results = time_series_cv(X_full, y_full, timeframe)

    # 5. ตรวจสอบข้อมูล
    if len(np.unique(y_train)) < 2:
        print("⚠️ ข้อมูลฝึกมีเพียงคลาสเดียว ไม่สามารถฝึกโมเดลได้")
        return {
            'model_name': model_name,
            'metrics': {
                'accuracy': 0,
                'auc': 0.5,
                'f1': 0,
                'precision': 0,
                'recall': 0,
                'confusion_matrix': None
            },
            'cv_results': {
                'accuracy': 0,
                'auc': 0.5,
                'f1': 0,
                'precision': 0,
                'recall': 0
            },
            'timeframe': timeframe,
            'num_trees': 0
        }

    # 6. ฝึกโมเดล
    if model is None:
        print("\n🚀 เริ่มการเทรนโมเดลใหม่")
        train_dataset = lgb.Dataset(X_train, label=y_train)
        valid_dataset = lgb.Dataset(X_val, label=y_val, reference=train_dataset)
        
        print(f"\nClass Ratio (0:1): {class_ratio:.2f}:1")
        
        # เรียกใช้พารามิเตอร์จากฟังก์ชันกลาง
        params = get_lgbm_params(y=y_train)
        # ตัวอย่างการเรียกใช้แบบปรับแต่งเฉพาะ
        # custom_params['learning_rate'] = 0.05  # ปรับ learning rate เฉพาะกรณีนี้
        # custom_params['min_data_in_leaf'] = 20  # ปรับเพื่อแก้ปัญหา feature importance เป็น 0
        print(f"\nClass Ratio (0:1): {params.get('scale_pos_weight', 1):.2f}:1")
        
        model = lgb.train(
            params,
            train_dataset,
            num_boost_round=5000,
            valid_sets=[valid_dataset],
            callbacks=[
                lgb.early_stopping(stopping_rounds=200),
                lgb.log_evaluation(100),
            ]
        )
    else:
        print("\n🔄 ทำการเทรนต่อจากโมเดลที่มีอยู่")
        print(f"จำนวน trees ก่อนเทรนต่อ: {model.num_trees()}")
        
        train_dataset = lgb.Dataset(X_train, label=y_train)
        valid_dataset = lgb.Dataset(X_val, label=y_val, reference=train_dataset)
        
        model = lgb.train(
            {},
            train_set=train_dataset,
            num_boost_round=1000,
            valid_sets=[valid_dataset],
            init_model=model,
            callbacks=[
                lgb.early_stopping(stopping_rounds=100),
                lgb.log_evaluation(50),
            ]
        )
        print(f"จำนวน trees หลังเทรนต่อ: {model.num_trees()}")

    # 7. ประเมินโมเดล (แทนที่ส่วนเดิมทั้งหมด)
    print(f"\n  การประเมินผลโมเดลแบบละเอียด (M{timeframe})  ")
    enhanced_metrics = enhanced_evaluation(model, X_test, y_test, output_folder, timeframe)
    
    if enhanced_metrics is None:
        print(f"⚠️ ไม่สามารถประเมินผลโมเดล M{timeframe} ได้ ใช้การประเมินพื้นฐานแทน")
        # Fallback to basic evaluation
        y_pred = model.predict(X_test, num_iteration=model.best_iteration)
        y_pred_binary = np.round(y_pred)
        metrics = {
            'timeframe': timeframe,
            'accuracy': accuracy_score(y_test, y_pred_binary),
            'auc': roc_auc_score(y_test, y_pred),
            'f1': f1_score(y_test, y_pred_binary),
            'precision': precision_score(y_test, y_pred_binary),
            'recall': recall_score(y_test, y_pred_binary),
            'confusion_matrix': confusion_matrix(y_test, y_pred_binary),
            'auc_pr': None  # ไม่มีค่าในโหมดพื้นฐาน
        }
    else:
        metrics = {
            'timeframe': timeframe,
            'accuracy': enhanced_metrics['accuracy'],
            'auc': enhanced_metrics['auc_roc'],
            'f1': enhanced_metrics['f1'],
            'precision': enhanced_metrics['precision'],
            'recall': enhanced_metrics['recall'],
            'confusion_matrix': enhanced_metrics['confusion_matrix'],
            'auc_pr': enhanced_metrics['auc_pr']
        }
    
    # 8. แสดงผลลัพธ์จาก enhanced evaluation
    if enhanced_metrics:
        print(f"\n📊 ผลการประเมินแบบละเอียด (M{timeframe}):")
        print(f"AUC-ROC: {metrics['auc']:.4f}")
        print(f"AUC-PR: {metrics['auc_pr']:.4f}")
        
        # print("\nClassification Report:")
        # print(pd.DataFrame(enhanced_metrics['classification_report']).transpose())
        
        # แปลงค่าใน report ให้เป็น float ก่อนแสดงผล
        clf_report = enhanced_metrics['classification_report']
        for key in clf_report:
            if isinstance(clf_report[key], dict):
                for metric in clf_report[key]:
                    if isinstance(clf_report[key][metric], (int, float)):
                        clf_report[key][metric] = clf_report[key][metric]
        
        print(pd.DataFrame(clf_report).transpose())
    
    print("\n📌 สรุปผลลัพธ์แบบละเอียด:")
    for metric, value in enhanced_metrics.items():
        if metric not in ['classification_report', 'confusion_matrix']:
            # ตรวจสอบก่อนว่า value เป็นตัวเลขหรือไม่ก่อนใช้ .4f
            if isinstance(value, (int, float)):
                print(f"- {metric.capitalize()}: {value:.4f}")
            else:
                print(f"- {metric.capitalize()}: {value}")
            
    # เก็บ metrics สำหรับคืนค่า
    metrics = {
        'accuracy': enhanced_metrics['accuracy'],
        'auc': enhanced_metrics['auc_roc'],
        'f1': enhanced_metrics['f1'],
        'precision': enhanced_metrics['precision'],
        'recall': enhanced_metrics['recall'],
        'confusion_matrix': enhanced_metrics['confusion_matrix'],
        'auc_pr': enhanced_metrics['auc_pr']  # เพิ่ม metric ใหม่
    }

    # 9. บันทึกโมเดล
    try:
        model_dir = f"models/M{timeframe}"
        os.makedirs(model_dir, exist_ok=True)
        
        model_path = os.path.join(model_dir, f"{model_name}_{timeframe}_trained.pkl")
        features_path = os.path.join(model_dir, f"{model_name}_{timeframe}_features.pkl")
        
        # ตรวจสอบว่าโมเดลมี method predict หรือไม่
        if not hasattr(model, 'predict'):
            raise ValueError("โมเดลไม่มี method predict ไม่สามารถบันทึกได้")
            
        # บันทึกโมเดล
        print(f"\nกำลังบันทึกโมเดลที่: {model_path}")
        joblib.dump(model, model_path)
        print(f"✅ บันทึกโมเดลเรียบร้อย (ขนาด: {os.path.getsize(model_path)/1024:.2f} KB)")
        
        # บันทึก features
        features = X_train.columns.tolist()
        print(f"\nกำลังบันทึก features ที่: {features_path}")
        joblib.dump(features, features_path)
        print(f"✅ บันทึก features เรียบร้อย (จำนวน features: {len(features)})")
        
    except Exception as e:
        print(f"\n⚠️ เกิดข้อผิดพลาดขณะบันทึกโมเดล: {str(e)}")
        import traceback
        traceback.print_exc()
        # เพิ่มการบันทึก error log
        error_log_path = os.path.join(output_folder, f"error_log_M{timeframe}.txt")
        with open(error_log_path, 'w') as f:
            f.write(f"Error saving model: {str(e)}\n")
            traceback.print_exc(file=f)

    # 10. บันทึกประวัติการเทรน
    history_entry = {
        'timestamp': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'),
        'timeframe': timeframe,
        'model': model_name,
        'accuracy': metrics['accuracy'],
        'auc': metrics['auc'],
        'num_trees': model.num_trees(),
        'model_path': model_path,
        'data_samples': len(X_train) + len(X_val) + len(X_test)
    }
    
    history_dir = "training_history"
    os.makedirs(history_dir, exist_ok=True)
    history_file = os.path.join(history_dir, f"training_history_{timeframe}.csv")
    
    try:
        if os.path.exists(history_file):
            history_df = pd.read_csv(history_file)
            new_entry_df = pd.DataFrame([history_entry])
            history_df = pd.concat([history_df, new_entry_df], ignore_index=True)
        else:
            history_df = pd.DataFrame([history_entry])
        
        history_df.to_csv(history_file, index=False, encoding='utf-8-sig')
        print(f"\n📝 บันทึกประวัติการเทรนที่: {history_file}")
    except Exception as e:
        print(f"\n⚠️ เกิดข้อผิดพลาดขณะบันทึกประวัติ: {str(e)}")

    def plot_feature_importance(model, features, model_name, timeframe, output_folder):
        """ฟังก์ชันย่อยสำหรับพล็อตและบันทึก Feature Importance"""
        try:
            # สร้าง DataFrame สำหรับ Feature Importance
            importance_data = {
                'Feature': model.feature_name(),
                'Gain': model.feature_importance(importance_type='gain'),
                'Split': model.feature_importance(importance_type='split')
            }
            
            # Normalize importance scores
            importance_df = pd.DataFrame(importance_data)
            importance_df['Gain'] = importance_df['Gain'] / importance_df['Gain'].sum()
            importance_df['Split'] = importance_df['Split'] / importance_df['Split'].sum()
            
            # เรียงลำดับตาม Gain
            importance_df = importance_df.sort_values('Gain', ascending=False)
            
            # แสดงผลใน Console
            print("\n📊 Feature Importance (Normalized Scores):")
            print(importance_df.to_string(index=False, float_format="%.4f"))
            
            # บันทึกเป็น CSV
            csv_path = os.path.join(output_folder, f"{model_name}_M{timeframe}_feature_importance.csv")
            importance_df.to_csv(csv_path, index=False)
            print(f"\n💾 บันทึก Feature Importance ละเอียดที่: {csv_path}")
            
            # พล็อตกราฟแบบ Side-by-Side
            plt.figure(figsize=(14, 10))
            
            # เลือกเฉพาะ Top 20 Features
            top_n = min(20, len(importance_df))
            plot_df = importance_df.head(top_n).copy()
            
            # เรียงลำดับใหม่สำหรับการแสดงผลกราฟ
            plot_df = plot_df.sort_values('Gain', ascending=True)
            
            # สร้างกราฟแบบ Side-by-Side
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, max(6, top_n*0.4)))
            
            # กราฟ Gain Importance
            ax1.barh(plot_df['Feature'], plot_df['Gain'], color='skyblue')
            ax1.set_title('Feature Importance (Gain)', fontsize=14)
            ax1.set_xlabel('Normalized Importance Score', fontsize=12)
            
            # กราฟ Split Importance
            ax2.barh(plot_df['Feature'], plot_df['Split'], color='salmon')
            ax2.set_title('Feature Importance (Split)', fontsize=14)
            ax2.set_xlabel('Normalized Importance Score', fontsize=12)
            
            plt.suptitle(f'Top {top_n} Feature Importance - {model_name} (M{timeframe})', 
                        fontsize=16, y=1.02)
            plt.tight_layout()
            
            # บันทึกรูปภาพ
            img_path = os.path.join(output_folder, f"{model_name}_M{timeframe}_feature_importance.png")
            plt.savefig(img_path, dpi=150, bbox_inches='tight')
            plt.close()
            print(f"💾 บันทึกกราฟ Feature Importance ที่: {img_path}")
            
            return importance_df
            
        except Exception as e:
            print(f"⚠️ เกิดข้อผิดพลาดขณะสร้าง Feature Importance: {str(e)}")
            return None
    
    # เรียกใช้ฟังก์ชันแสดง Feature Importance
    if hasattr(model, 'feature_importance'):
        importance_df = plot_feature_importance(
            model=model,
            features=X_train.columns.tolist(),
            model_name=model_name,
            timeframe=timeframe,
            output_folder=output_folder
        )
        
        # แสดง Feature Importance ที่สำคัญที่สุด
        if importance_df is not None:
            print("\n🔍 Top 5 Most Important Features (Gain):")
            top_features = importance_df.head(5)
            for idx, row in top_features.iterrows():
                print(f"{row['Feature']}: {row['Gain']:.4f} (Gain), {row['Split']:.4f} (Split)")

    # เรียกใช้ฟังก์ชันเปรียบเทียบ Feature Importance
    if importance_df is not None and 'rf_importance' in locals():
        compare_feature_importance(
            lgb_importance=importance_df,
            rf_importance=rf_importance,
            timeframe=timeframe,
            output_folder=output_folder
        )

    # 12. แสดงผลลัพธ์ Cross-Validation
    print("\n📊 เปรียบเทียบผลลัพธ์:")
    print(f"| Metric      | CV Avg    | Test Set |")
    print(f"|-------------|-----------|----------|")
    print(f"| Accuracy    | {cv_results['accuracy']:.4f}    | {metrics['accuracy']:.4f} |")
    print(f"| AUC         | {cv_results['auc']:.4f}    | {metrics['auc']:.4f} |")
    print(f"| F1 Score    | {cv_results['f1']:.4f}    | {metrics['f1']:.4f} |")

    print("\n" + "="*50)
    print("  การเปรียบเทียบ Feature Importance  ")
    print("="*50)
    
    if importance_df is not None and 'rf_importance' in locals():
        # สร้าง DataFrame สำหรับเปรียบเทียบ
        comparison_df = importance_df[['Feature', 'Gain']].rename(columns={'Gain': 'LightGBM'})
        comparison_df = comparison_df.merge(
            rf_importance[['Feature', 'Importance']],
            on='Feature',
            how='left'
        ).rename(columns={'Importance': 'RandomForest'})
        
        # Normalize scores
        comparison_df['LightGBM'] = comparison_df['LightGBM'] / comparison_df['LightGBM'].max()
        comparison_df['RandomForest'] = comparison_df['RandomForest'] / comparison_df['RandomForest'].max()
        
        # เรียงลำดับตาม LightGBM importance
        comparison_df = comparison_df.sort_values('LightGBM', ascending=False).head(10)
        
        # แสดงผลตารางเปรียบเทียบ
        print("\n📌 Comparison of Top 10 Feature Importance (Normalized):")
        print(comparison_df.to_string(index=False, float_format="%.4f"))
        
        # พล็อตกราฟเปรียบเทียบ
        plt.figure(figsize=(12, 8))
        comparison_df.set_index('Feature').plot(kind='barh', color=['skyblue', 'salmon'])
        plt.title('Feature Importance Comparison: LightGBM vs RandomForest', fontsize=14)
        plt.xlabel('Normalized Importance Score', fontsize=12)
        plt.ylabel('Features', fontsize=12)
        plt.legend(title='Model')
        plt.tight_layout()
        
        # บันทึกรูปภาพ
        comp_path = os.path.join(output_folder, f"feature_importance_comparison_M{timeframe}.png")
        plt.savefig(comp_path, dpi=150, bbox_inches='tight')
        plt.close()
        print(f"💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: {comp_path}")

    print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. train_and_evaluate .. ✅ ++")
    # 13. คืนค่าผลลัพธ์
    return {
        'model_name': model_name,
        'metrics': metrics,
        'cv_results': cv_results,
        'timeframe': timeframe,
        'num_trees': model.num_trees(),
        'feature_importance': importance_df.to_dict() if importance_df is not None else None
    }

def create_trade_cycles(df):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. create_trade_cycles .. 📌 ++")
    
    """สร้างรายการซื้อขายด้วยเงื่อนไขที่ปรับปรุงแล้ว"""
    trades = []
    in_trade_buy = False
    in_trade_sell = False
    entry_price_buy = entry_price_sell = None
    entry_time_buy = entry_time_sell = None
    trade_type_buy = trade_type_sell = None

    stats = {
        'buy': {'win': 0, 'loss': 0, 'total': 0, 'profit': 0, 'loss_amount': 0},
        'sell': {'win': 0, 'loss': 0, 'total': 0, 'profit': 0, 'loss_amount': 0},
        'day_stats': {i: {'win': 0, 'loss': 0, 'total': 0} for i in range(7)},  # 0=Monday, 6=Sunday
        'hour_stats': {i: {'win': 0, 'loss': 0, 'total': 0} for i in range(24)}
    }
    
    for i in range(1, len(df)):
        current_time = pd.to_datetime(df['Time'].iloc[i])
        hour = current_time.hour
        day_of_week = df['DayOfWeek'].iloc[i]
        
        # ใช้ Previous_Close แทน Close สำหรับเงื่อนไขการเข้า
        previous_close = df['Previous_Close'].iloc[i]
        current_close = df['Close'].iloc[i]
        
        # เงื่อนไขการเข้าซื้อ (ปรับความเข้มงวด)
        if not in_trade_buy:
            tech_condition = (
                previous_close > df["EMA50"].iloc[i] and
                df["RSI14"].iloc[i] > 40 and
                current_close > df["Open"].iloc[i] and
                df["Volume"].iloc[i] > df["Volume"].rolling(20).mean().iloc[i] * 0.8
            )
            
            # เงื่อนไขเวลาที่ปรับปรุงแล้ว
            time_condition = (6 <= hour < 22)  # ซื้อได้ระหว่าง 6 โมงเช้าถึง 4 ทุ่ม
            
            if tech_condition and time_condition:
                entry_price_buy = df["Close"].iloc[i]
                entry_time_buy = df["Date"].iloc[i] + " " + df["Time"].iloc[i]
                trade_type_buy = "Buy"
                in_trade_buy = True

        # เงื่อนไขการเข้าขาย (ปรับความเข้มงวด)
        if not in_trade_sell:
            tech_condition = (
                previous_close < df["EMA50"].iloc[i] and
                df["RSI14"].iloc[i] < 60 and
                current_close < df["Open"].iloc[i] and
                df["Volume"].iloc[i] > df["Volume"].rolling(20).mean().iloc[i] * 0.8
            )
            
            time_condition = (6 <= hour < 22)  # ขายได้ระหว่าง 6 โมงเช้าถึง 4 ทุ่ม
            
            if tech_condition and time_condition:
                entry_price_sell = df["Close"].iloc[i]
                entry_time_sell = df["Date"].iloc[i] + " " + df["Time"].iloc[i]
                trade_type_sell = "Sell"
                in_trade_sell = True

        # เงื่อนไขการออกจากตำแหน่งซื้อ
        if in_trade_buy and trade_type_buy == "Buy":
            exit_condition = (
                df["Close"].iloc[i] < df["EMA50"].iloc[i] or
                df["RSI14"].iloc[i] < 35 or
                (df["Close"].iloc[i] < entry_price_buy * 0.998)
            )
            
            if exit_condition:
                exit_price = df["Close"].iloc[i]
                exit_time = df["Date"].iloc[i] + " " + df["Time"].iloc[i]
                profit = exit_price - entry_price_buy
                
                entry_datetime = pd.to_datetime(entry_time_buy)
                entry_hour = entry_datetime.hour
                entry_day = entry_datetime.dayofweek
                
                trades.append([
                    entry_time_buy, entry_price_buy, 
                    exit_time, exit_price, 
                    profit, trade_type_buy,
                    entry_hour,
                    entry_day
                ])
                
                # อัปเดตสถิติ
                stats['buy']['total'] += 1
                if profit > 0:
                    stats['buy']['win'] += 1
                    stats['buy']['profit'] += profit
                    stats['day_stats'][entry_day]['win'] += 1
                    stats['hour_stats'][entry_hour]['win'] += 1
                else:
                    stats['buy']['loss'] += 1
                    stats['buy']['loss_amount'] += abs(profit)
                    stats['day_stats'][entry_day]['loss'] += 1
                    stats['hour_stats'][entry_hour]['loss'] += 1
                
                stats['day_stats'][entry_day]['total'] += 1
                stats['hour_stats'][entry_hour]['total'] += 1
                
                in_trade_buy = False

        # เงื่อนไขการออกจากตำแหน่งขาย
        if in_trade_sell and trade_type_sell == "Sell":
            exit_condition = (
                df["Close"].iloc[i] > df["EMA50"].iloc[i] or
                df["RSI14"].iloc[i] > 65 or
                (df["Close"].iloc[i] > entry_price_sell * 1.002)
            )
            
            if exit_condition:
                exit_price = df["Close"].iloc[i]
                exit_time = df["Date"].iloc[i] + " " + df["Time"].iloc[i]
                profit = entry_price_sell - exit_price
                
                entry_datetime = pd.to_datetime(entry_time_sell)
                entry_hour = entry_datetime.hour
                entry_day = entry_datetime.dayofweek
                
                trades.append([
                    entry_time_sell, entry_price_sell,
                    exit_time, exit_price,
                    profit, trade_type_sell,
                    entry_hour,
                    entry_day
                ])
                
                # อัปเดตสถิติ
                stats['sell']['total'] += 1
                if profit > 0:
                    stats['sell']['win'] += 1
                    stats['sell']['profit'] += profit
                    stats['day_stats'][entry_day]['win'] += 1
                    stats['hour_stats'][entry_hour]['win'] += 1
                else:
                    stats['sell']['loss'] += 1
                    stats['sell']['loss_amount'] += abs(profit)
                    stats['day_stats'][entry_day]['loss'] += 1
                    stats['hour_stats'][entry_hour]['loss'] += 1
                
                stats['day_stats'][entry_day]['total'] += 1
                stats['hour_stats'][entry_hour]['total'] += 1
                
                in_trade_sell = False

    # คำนวณสถิติ (เพิ่มส่วนของวันและเวลา)
    def calculate_stats(win, loss, total, profit, loss_amount):
        win_rate = (win / total) * 100 if total > 0 else 0
        avg_win = profit / win if win > 0 else 0
        avg_loss = loss_amount / loss if loss > 0 else 0
        expectancy = (avg_win * (win_rate/100)) - (avg_loss * (1 - (win_rate/100)))
        return win_rate, avg_win, avg_loss, expectancy

    buy_win_rate, buy_avg_win, buy_avg_loss, buy_expectancy = calculate_stats(
        stats['buy']['win'], stats['buy']['loss'], stats['buy']['total'],
        stats['buy']['profit'], stats['buy']['loss_amount']
    )

    sell_win_rate, sell_avg_win, sell_avg_loss, sell_expectancy = calculate_stats(
        stats['sell']['win'], stats['sell']['loss'], stats['sell']['total'],
        stats['sell']['profit'], stats['sell']['loss_amount']
    )

    total_trades = stats['buy']['total'] + stats['sell']['total']
    total_win = stats['buy']['win'] + stats['sell']['win']
    total_win_rate = (total_win / total_trades) * 100 if total_trades > 0 else 0
    total_avg_win = (stats['buy']['profit'] + stats['sell']['profit']) / total_win if total_win > 0 else 0
    total_avg_loss = (stats['buy']['loss_amount'] + stats['sell']['loss_amount']) / (stats['buy']['loss'] + stats['sell']['loss']) if (stats['buy']['loss'] + stats['sell']['loss']) > 0 else 0
    total_expectancy = (total_avg_win * (total_win_rate/100)) - (total_avg_loss * (1 - (total_win_rate/100)))

    # คำนวณสถิติตามวันและเวลา
    def get_best_time_stats(time_stats, time_name):
        best_time = None
        best_win_rate = 0
        time_details = {}
        
        for time, data in time_stats.items():
            if data['total'] > 0:
                win_rate = (data['win'] / data['total']) * 100
                time_details[time] = {
                    'win_rate': round(win_rate, 2),
                    'total_trades': data['total']
                }
                if win_rate > best_win_rate and data['total'] >= max(3, len(time_stats)*0.05):
                    best_win_rate = win_rate
                    best_time = time
        
        return {
            f'Best_{time_name}': best_time,
            f'{time_name}_Details': time_details
        }

    day_stats = get_best_time_stats(stats['day_stats'], 'Day')
    hour_stats = get_best_time_stats(stats['hour_stats'], 'Hour')

    statistics = {
        "Buy Win%": round(buy_win_rate, 3),
        "Buy Expectancy": round(buy_expectancy, 3),
        "Sell Win%": round(sell_win_rate, 3),
        "Sell Expectancy": round(sell_expectancy, 3),
        "Total Win%": round(total_win_rate, 3),
        "Total Expectancy": round(total_expectancy, 3),
        **day_stats,
        **hour_stats
    }

    if not trades:
        print("⚠️ ไม่พบการซื้อขายที่ตรงตามเงื่อนไข")
        return pd.DataFrame(), statistics

    print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. create_trade_cycles.. ✅ ++")
    return pd.DataFrame(trades, columns=[
        "Entry Time", "Entry Price", "Exit Time", "Exit Price", 
        "Profit", "Trade Type", "Entry Hour", "Entry Day"
    ]), statistics

def process_trade_targets(df):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. process_trade_targets .. 📌 ++")
    
    """สร้าง target variable โดยจัดการกรณีไม่มีข้อมูล"""
    if df.empty or len(df) < 10:  # ตรวจสอบว่ามีข้อมูลเพียงพอหรือไม่
        print("⚠️ ไม่มีข้อมูลเพียงพอสำหรับสร้าง Target")
        return df
    
    try:
        # ตรวจสอบว่าคอลัมน์ Profit มีค่าที่ถูกต้อง
        if not pd.api.types.is_numeric_dtype(df["Profit"]):
            print("⚠️ คอลัมน์ Profit ไม่ใช่ตัวเลข จะทำการแปลงค่า")
            df["Profit"] = pd.to_numeric(df["Profit"], errors='coerce')
            df = df.dropna(subset=["Profit"])
        
        # ตรวจสอบว่ามีข้อมูล Profit เพียงพอหรือไม่
        if len(df['Profit'].dropna()) < 10:
            print("⚠️ ไม่มีข้อมูลกำไร/ขาดทุนเพียงพอสำหรับสร้าง Target")
            return df
            
        # คำนวณ quantile เฉพาะเมื่อมีข้อมูลเพียงพอ
        upper_quantile = df["Profit"].quantile(0.7)
        lower_quantile = df["Profit"].quantile(0.3)
        
        df["Target"] = np.where(
            df["Profit"] > upper_quantile, 1,
            np.where(
                df["Profit"] < lower_quantile, 0,
                -1  # กรณีกลางไม่ใช้ฝึกโมเดล
            )
        )
        
        # ตรวจสอบค่าที่ไม่ถูกต้องใน Target
        invalid_targets = df[~df['Target'].isin([0, 1])]
        if not invalid_targets.empty:
            print(f"⚠️ พบค่า Target ที่ไม่ถูกต้อง {len(invalid_targets)} แถว จะถูกกรองออก")
            df = df[df['Target'].isin([0, 1])].copy()
        
        print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. process_trade_targets .. ✅ ++")
        return df
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดขณะสร้าง Target: {str(e)}")
        
        print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. process_trade_targets .. ✅ ++")
        return df

# ==============================================
# กลุ่มฟังก์ชันการแสดงผลและวิเคราะห์
# ==============================================

def plot_candlestick_with_entries_and_exits(df, trade_df, file_name):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. plot_candlestick_with_entries_and_exits .. 📌 ++")
    
    fig = go.Figure(data=[go.Candlestick(
        x=df['Date'] + ' ' + df['Time'],  
        open=df['Open'],
        high=df['High'],
        low=df['Low'],
        close=df['Close'],
        name="Candlestick"
    )])

    fig.add_trace(go.Scatter(
        x=trade_df[trade_df['Trade Type'] == 'Buy']['Entry Time'], 
        y=trade_df[trade_df['Trade Type'] == 'Buy']['Entry Price'], 
        mode='markers', 
        marker=dict(color='green', size=8, symbol='triangle-up'),
        name='Buy Entry Points'
    ))

    fig.add_trace(go.Scatter(
        x=trade_df[trade_df['Trade Type'] == 'Buy']['Exit Time'], 
        y=trade_df[trade_df['Trade Type'] == 'Buy']['Exit Price'], 
        mode='markers', 
        marker=dict(color='red', size=8, symbol='triangle-down'),
        name='Buy Exit Points'
    ))
    
    fig.add_trace(go.Scatter(
        x=trade_df[trade_df['Trade Type'] == 'Sell']['Entry Time'], 
        y=trade_df[trade_df['Trade Type'] == 'Sell']['Entry Price'], 
        mode='markers', 
        marker=dict(color='green', size=8, symbol='triangle-down'),
        name='Sell Entry Points'
    ))

    fig.add_trace(go.Scatter(
        x=trade_df[trade_df['Trade Type'] == 'Sell']['Exit Time'], 
        y=trade_df[trade_df['Trade Type'] == 'Sell']['Exit Price'], 
        mode='markers', 
        marker=dict(color='red', size=8, symbol='triangle-up'),
        name='Sell Exit Points'
    ))

    fig.update_layout(
        title=f"Candlestick Chart with Entry and Exit - {file_name}",
        xaxis_title="Date",
        yaxis_title="Price",
        xaxis_rangeslider_visible=True,
        dragmode='zoom',
        yaxis=dict(
            autorange=True,  
            fixedrange=False
        ),
        width=1200,
        height=800
    )

    print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. plot_candlestick_with_entries_and_exits ✅ ++")
    return fig

def plot_results(results_df):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. plot_results .. 📌 ++")
    
    """ฟังก์ชันสำหรับพล็อตผลลัพธ์โดยเฉพาะ"""
    plt.close('all') 
    plt.figure(figsize=(12, 6))
    
    # ตรวจสอบและใช้คอลัมน์ที่ถูกต้อง
    file_col = 'File' if 'File' in results_df.columns else 'file'
    accuracy_col = 'Test Accuracy' if 'Test Accuracy' in results_df.columns else 'accuracy'
    
    # ตรวจสอบว่ามีข้อมูลพอที่จะพล็อต
    if len(results_df) == 0:
        print("⚠️ ไม่มีข้อมูลที่จะพล็อต")
        return
    
    bars = plt.bar(results_df[file_col], results_df[accuracy_col], color='skyblue')
    
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height,
                 f'{height:.4f}',
                 ha='center', va='bottom')
    
    plt.xlabel("Timeframe")
    plt.ylabel("Accuracy Score")
    plt.title("Model Performance Comparison")
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # บันทึกรูปภาพ
    plot_path = os.path.join(output_folder, "model_performance_comparison.png")
    plt.savefig(plot_path)
    print(f"💾 บันทึกกราฟเปรียบเทียบประสิทธิภาพที่: {plot_path}")
    plt.show()
    
    print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. plot_results ✅ ++")

def safe_plot_results(results_df):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. safe_plot_results .. 📌 ++")
    
    """ฟังก์ชันพล็อตกราฟแบบปลอดภัย"""
    # ตรวจสอบคอลัมน์ที่จำเป็น โดยยืดหยุ่นกับชื่อคอลัมน์
    required_columns = {'file', 'accuracy'} 
    available_columns = set(results_df.columns)
    
    # ตรวจสอบว่ามีคอลัมน์ที่จำเป็นอย่างน้อยหนึ่งรูปแบบ
    if not (required_columns.issubset(available_columns)) or len(results_df) == 0:
        print("⚠️ ไม่พบคอลัมน์ที่จำเป็นสำหรับการพล็อตกราฟหรือไม่มีข้อมูล")
        print("คอลัมน์ที่มีอยู่:", results_df.columns.tolist())
        
        print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. safe_plot_results .. ✅ ++")
        return
    
    try:
        # สร้างคอลัมน์ชั่วคราวถ้าจำเป็น
        if 'File' not in results_df.columns and 'file' in results_df.columns:
            results_df['File'] = results_df['file']
        if 'Test Accuracy' not in results_df.columns and 'accuracy' in results_df.columns:
            results_df['Test Accuracy'] = results_df['accuracy']
        
        plot_results(results_df)
        
        print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. safe_plot_results ✅ ++")
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดขณะพล็อตกราฟ: {str(e)}")
        import traceback
        traceback.print_exc()
        
        print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. safe_plot_results ✅ ++")

def analyze_results(results):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. analyze_results .. 📌 ++")
    
    """วิเคราะห์ผลลัพธ์โดยรวม"""
    print("\n📈 การวิเคราะห์ผลลัพธ์โดยรวม:")
    
    # แปลงเป็น DataFrame
    if not results:
        print("⚠️ ไม่มีผลลัพธ์ให้วิเคราะห์")
        return
    
    try:
        # แปลงค่าใน results ให้เป็น float ก่อนสร้าง DataFrame
        processed_results = []
        for r in results:
            processed = {
                'File': r.get('file', ''),
                'Timeframe': r.get('timeframe', ''),
                'Accuracy': r.get('accuracy', 0),
                'AUC': r.get('auc', 0),
                'F1': r.get('f1_score', 0),
                'CV_Accuracy': r.get('cv_accuracy', 0),
                'CV_AUC': r.get('cv_auc', 0)
            }
            processed_results.append(processed)
        
        df = pd.DataFrame(processed_results)
        
        # ... ส่วนอื่นๆ ...
        
        best_model = df.loc[df['F1'].idxmax()]
        print(f"\n🏆 โมเดลที่ดีที่สุด (ตาม F1 Score):")
        print(f"ไฟล์: {best_model['File']}")
        print(f"Timeframe: M{best_model['Timeframe']}")
        print(f"F1 Score: {best_model['F1']:.4f}")
        
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดขณะวิเคราะห์ผลลัพธ์: {str(e)}")
    
    # สรุปสถิติ
    print("\n📌 สรุปสถิติ:")
    print(df.describe().to_string())
    
    # หาโมเดลที่ดีที่สุด
    best_model = df.loc[df['F1'].idxmax()]
    print(f"\n🏆 โมเดลที่ดีที่สุด (ตาม F1 Score):")
    print(f"ไฟล์: {best_model['File']}")
    print(f"Timeframe: M{best_model['Timeframe']}")
    # print(f"F1 Score: {best_model['F1']:.4f}")
    print(f"F1 Score: {best_model['F1']:.4f}")
    
    # บันทึกผลการวิเคราะห์
    analysis_path = os.path.join(output_folder, "performance_analysis.txt")
    with open(analysis_path, 'w', encoding='utf-8') as f:
        f.write("ผลการวิเคราะห์ประสิทธิภาพโมเดล\n")
        f.write("="*50 + "\n")
        f.write(df.to_string())
        f.write("\n\nโมเดลที่ดีที่สุด:\n")
        f.write(str(best_model))
    print(f"\n💾 บันทึกผลการวิเคราะห์ที่: {analysis_path}")
    
    print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. analyze_results ✅ ++")

# ==============================================
# กลุ่มฟังก์ชันการจัดการโมเดล
# ==============================================

def load_model(timeframe):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. load_model .. 📌 ++")
    
    """โหลดโมเดลที่บันทึกไว้ตาม timeframe"""
    model_dir = f"models/M{timeframe}"
    model_path = os.path.join(model_dir, f"LightGBM_{timeframe}_trained.pkl")
    features_path = os.path.join(model_dir, f"LightGBM_{timeframe}_features.pkl")
    
    try:
        # ตรวจสอบว่าโฟลเดอร์และไฟล์มีอยู่จริง
        if not os.path.exists(model_dir):
            print(f"⚠️ ไม่พบโฟลเดอร์โมเดลสำหรับ timeframe M{timeframe}")
            return None
            
        if not os.path.exists(model_path):
            print(f"⚠️ ไม่พบไฟล์โมเดลที่ {model_path}")
            return None
            
        print(f"กำลังโหลดโมเดลจาก: {model_path}")
        model = joblib.load(model_path)
        
        # ตรวจสอบว่าเป็น LightGBM model จริงหรือไม่
        if not hasattr(model, 'predict'):
            raise ValueError("ไฟล์ที่โหลดมาไม่ใช่ LightGBM model")
            
        # โหลด features ที่ใช้ในโมเดล
        if os.path.exists(features_path):
            features = joblib.load(features_path)
            print(f"✅ โหลด features สำเร็จ (จำนวน {len(features)} features)")
            setattr(model, 'feature_names_', features)
        else:
            print("⚠️ ไม่พบไฟล์ features จะใช้ features จากโมเดลโดยตรง")
            
        print(f"✅ โหลดโมเดลสำเร็จ (มี {model.num_trees()} trees)")
        
        print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. load_model ✅ ++")
        return model
        
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดขณะโหลดโมเดล: {str(e)}")
        import traceback
        traceback.print_exc()
        
        print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. load_model ✅ ++")
        return None

# ==============================================
# ส่วนการดำเนินการหลัก
# ==============================================

def analyze_time_performance(trade_df, file_name=None):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. analyze_time_performance .. 📌 ++")
    
    """วิเคราะห์ประสิทธิภาพการซื้อขายตามวันและเวลา"""
    if trade_df.empty:
        print("⚠️ ไม่มีข้อมูลการซื้อขายสำหรับวิเคราะห์")
        return
    
    try:
        # วิเคราะห์ตามวันในสัปดาห์
        print("\n📊 ประสิทธิภาพตามวันในสัปดาห์:")
        day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        
        day_stats = trade_df.groupby('Entry_DayOfWeek').agg({
            'Profit': ['count', 'mean', 'sum'],
            'Target': 'mean'
        }).rename(index={i: day_names[i] for i in range(7)})
        
        print(day_stats)
        
        # วิเคราะห์ตามชั่วโมง
        print("\n📊 ประสิทธิภาพตามชั่วโมง:")
        hour_stats = trade_df.groupby('Entry_Hour').agg({
            'Profit': ['count', 'mean', 'sum'],
            'Target': 'mean'
        })
        
        print(hour_stats)
        
        # สร้าง visualization
        if Plot_file:
            plt.figure(figsize=(15, 10))
            
            # ตรวจสอบว่ามีข้อมูลพอที่จะพล็อตหรือไม่
            valid_plots = 0
            plot_positions = [(1, 2, 1), (1, 2, 2), (2, 2, 1), (2, 2, 2)]
            plots = []
            
            # กราฟวันในสัปดาห์ (Win Rate)
            if 'Entry_DayOfWeek' in trade_df.columns and 'Target' in trade_df.columns:
                day_win_rate = trade_df.groupby('Entry_DayOfWeek')['Target'].mean()
                if len(day_win_rate) > 0:
                    plots.append(('bar', day_names[:len(day_win_rate)], day_win_rate.values, 'Win Rate by Day of Week'))
            
            # กราฟชั่วโมง (Win Rate)
            if 'Entry_Hour' in trade_df.columns and 'Target' in trade_df.columns:
                hour_win_rate = trade_df.groupby('Entry_Hour')['Target'].mean()
                if len(hour_win_rate) > 0:
                    plots.append(('bar', hour_win_rate.index, hour_win_rate.values, 'Win Rate by Hour'))
            
            # กราฟผลรวมกำไรตามวัน
            if 'Entry_DayOfWeek' in trade_df.columns and 'Profit' in trade_df.columns:
                day_profit = trade_df.groupby('Entry_DayOfWeek')['Profit'].sum()
                if len(day_profit) > 0:
                    plots.append(('bar', day_names[:len(day_profit)], day_profit.values, 'Total Profit by Day of Week'))
            
            # กราฟผลรวมกำไรตามชั่วโมง
            if 'Entry_Hour' in trade_df.columns and 'Profit' in trade_df.columns:
                hour_profit = trade_df.groupby('Entry_Hour')['Profit'].sum()
                if len(hour_profit) > 0:
                    plots.append(('bar', hour_profit.index, hour_profit.values, 'Total Profit by Hour'))
            
            # พล็อตเฉพาะกราฟที่มีข้อมูล
            for i, (plot_type, x, y, title) in enumerate(plots[:4]):
                plt.subplot(2, 2, i+1)
                if plot_type == 'bar':
                    plt.bar(x, y)
                plt.title(title)
                plt.xticks(rotation=45)
            
            plt.tight_layout()
            plot_path = os.path.join(output_folder, f"{file_name}_time_analysis.png")
            plt.savefig(plot_path)
            plt.close()
            print(f"💾 บันทึกกราฟวิเคราะห์เวลา ที่: {plot_path}")
            
        print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. analyze_time_performance ✅ ++")
            
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดขณะสร้างกราฟวิเคราะห์เวลา: {str(e)}")
        import traceback
        traceback.print_exc()
        
        print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. analyze_time_performance ✅ ++")

# ในฟังก์ชัน main() ส่วนบันทึกผลลัพธ์
def save_enhanced_report(results, output_folder):
    print(f"\n ++ 📌 - เริ่ม - การทำงาน .. save_enhanced_report .. 📌 ++")
    
    try:
        if not results:
            print("⚠️ ไม่มีผลลัพธ์ที่จะบันทึก")
            return
            
        # สร้างโฟลเดอร์ถ้ายังไม่มี
        os.makedirs(output_folder, exist_ok=True)
        
        # บันทึกเป็น JSON
        report_path = os.path.join(output_folder, 'detailed_report.json')
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump({
                'summary': pd.DataFrame(results).to_dict(orient='records'),
                'best_model': max(results, key=lambda x: x['f1_score']),
                'execution_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'system_info': {
                    'python_version': sys.version,
                    'lgb_version': lgb.__version__,
                    'sklearn_version': sklearn_version
                }
            }, f, indent=2, ensure_ascii=False)
        print(f"✅ บันทึกรายงานละเอียดที่: {report_path}")

        # บันทึกเป็น CSV
        csv_path = os.path.join(output_folder, 'final_results.csv')
        pd.DataFrame(results).to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"✅ บันทึกรายงานสรุปที่: {csv_path}")
        
        print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. save_enhanced_report ✅ ++")
        
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดขณะบันทึกรายงาน: {str(e)}")
        error_log_path = os.path.join(output_folder, "report_error_log.txt")
        with open(error_log_path, 'w') as f:
            f.write(f"Error saving report: {str(e)}\n")
            traceback.print_exc(file=f)
        
        print(f"\n ++ ✅ ( สิ้นสุด ) การทำงาน .. save_enhanced_report ✅ ++")

def main():
    results = []
    results_report = []
    
    for file in files:
        print(f"\n{'='*50}")
        print(f"กำลังทดสอบไฟล์: {file}")
        
        timeframe = file.split('#')[1].split('.')[0]
        model_name = "LightGBM"
        
        # ตรวจสอบว่าโฟลเดอร์ models มีอยู่หรือไม่
        models_dir = "models"
        if not os.path.exists(models_dir):
            os.makedirs(models_dir)
            print(f"สร้างโฟลเดอร์ {models_dir} เรียบร้อยแล้ว")
            
        # ตรวจสอบว่าโฟลเดอร์ timeframe มีอยู่หรือไม่
        timeframe_dir = f"models/M{timeframe}"
        if not os.path.exists(timeframe_dir):
            os.makedirs(timeframe_dir)
            print(f"สร้างโฟลเดอร์ {timeframe_dir} เรียบร้อยแล้ว")
            
        model = load_model(timeframe)
        train_data, val_data, test_data, df, trade_df = load_and_process_data(file)
        
        # เรียกใช้ฟังก์ชันวิเคราะห์เวลา
        if trade_df is not None and not trade_df.empty:
            analyze_time_performance(trade_df, file)
        
        # ตรวจสอบว่ามีข้อมูลหรือไม่ (แก้ไขส่วนนี้)
        if train_data is None or val_data is None or test_data is None:
            print(f"⚠️ ข้ามไฟล์ {file} เนื่องจากมีปัญหาในการโหลดหรือประมวลผลข้อมูล")
            
            # เพิ่มข้อมูลในรายงานแม้ว่าจะมีปัญหา
            results_report.append({
                "File": file,
                "Timeframe": timeframe,
                "Model": model_name,
                "Status": "Failed - No data",
                "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"),
                "Accuracy": "N/A",
                "AUC": "N/A",
                "F1 Score": "N/A"
            })
            continue
            
        X_train, y_train = train_data
        if len(X_train) == 0 or len(y_train) == 0:
            print(f"⚠️ ข้ามไฟล์ {file} เนื่องจากไม่มีข้อมูลในชุดฝึก")
            
            results_report.append({
                "File": file,
                "Timeframe": timeframe,
                "Model": model_name,
                "Status": "Failed - Empty training set",
                "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"),
                "Accuracy": "N/A",
                "AUC": "N/A",
                "F1 Score": "N/A"
            })
            continue
        
        # ส่วนนี้คงเดิมทั้งหมด
        try:
            # รับผลลัพธ์ทั้งหมดเป็น dictionary
            result = train_and_evaluate(model, model_name, train_data, val_data, test_data, timeframe)
            
            # บันทึกผลลัพธ์ที่สำคัญ
            results.append({
                'file': file,
                'timeframe': timeframe,
                'accuracy': result['metrics']['accuracy'],
                'auc': result['metrics']['auc'],
                'f1_score': result['metrics']['f1'],
                'precision': result['metrics']['precision'],
                'recall': result['metrics']['recall'],
                'cv_accuracy': result['cv_results']['accuracy'],
                'cv_auc': result['cv_results']['auc'],
                'cv_f1': result['cv_results']['f1']
            })

            # แสดงผลลัพธ์เปรียบเทียบ
            print("\n📊 เปรียบเทียบผลลัพธ์:")
            print(f"| Metric      | CV Avg    | Test Set |")
            print(f"|-------------|-----------|----------|")
            print(f"| Accuracy    | {result['cv_results']['accuracy']:.4f}    | {result['metrics']['accuracy']:.4f} |")
            print(f"| AUC         | {result['cv_results']['auc']:.4f}    | {result['metrics']['auc']:.4f} |")
            print(f"| F1 Score    | {result['cv_results']['f1']:.4f}    | {result['metrics']['f1']:.4f} |")
            
            if Plot_file:
                if df is not None and trade_df is not None:
                    fig = plot_candlestick_with_entries_and_exits(df, trade_df, file)

                    html_file = os.path.join(output_folder, f"{file}_candlestick_chart.html")
                    fig.write_html(html_file)
                    print(f"✅ บันทึก HTML: {html_file}")

                    image_file = os.path.join(output_folder, f"{file}_candlestick_chart.png")
                    pio.write_image(fig, image_file, format="png", width=1200, height=800)
                    print(f"✅ บันทึกรูปภาพ PNG: {image_file}")
                else:
                    print(f"⚠️ ไม่สามารถสร้างกราฟสำหรับไฟล์: {file}")
            
            # เตรียมข้อมูลสำหรับรายงาน
            results_report.append({
                "File": file,
                "Timeframe": timeframe,
                "Model": "LightGBM",
                "Accuracy": f"{result['metrics']['accuracy']:.4f}",
                "AUC": f"{result['metrics']['auc']:.4f}",
                "F1 Score": f"{result['metrics']['f1']:.4f}",
                "Precision": f"{result['metrics']['precision']:.4f}",
                "Recall": f"{result['metrics']['recall']:.4f}",
                "CV Accuracy": f"{result['cv_results']['accuracy']:.4f}",
                "CV AUC": f"{result['cv_results']['auc']:.4f}",
                "CV F1": f"{result['cv_results']['f1']:.4f}",
                "Status": "Success",
                "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
            })

        except Exception as e:
            print(f"⚠️ เกิดข้อผิดพลาดขณะประมวลผลไฟล์ {file}: {str(e)}")
            results_report.append({
                "File": file,
                "Timeframe": timeframe,
                "Model": model_name,
                "Status": f"Failed - {str(e)}",
                "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"),
                "Accuracy": "N/A",
                "AUC": "N/A",
                "F1 Score": "N/A"
            })

    # ส่วนการสร้างรายงาน (แก้ไขให้ยืดหยุ่นมากขึ้น)
    try:
        # สร้าง DataFrame สำหรับผลลัพธ์
        results_df = pd.DataFrame(results) if results else pd.DataFrame()
        final_report_df = pd.DataFrame(results_report)
        
        # กำหนดคอลัมน์ที่ต้องการแสดง (เฉพาะที่มีอยู่จริง)
        available_columns = final_report_df.columns.tolist()
        desired_columns = [
            "File", "Timeframe", "Model", "Timestamp", "Status",
            "Accuracy", "AUC", "F1 Score", "Precision", "Recall",
            "CV Accuracy", "CV AUC", "CV F1"
        ]
        
        # เลือกเฉพาะคอลัมน์ที่มีอยู่จริง
        columns_to_show = [col for col in desired_columns if col in available_columns]
        
        # เรียงลำดับคอลัมน์ใหม่
        final_report_df = final_report_df[columns_to_show]

        # บันทึกผลลัพธ์
        if not results_df.empty:
            results_df.to_csv(os.path.join(output_folder, "final_results.csv"), index=False, encoding='utf-8-sig')
        final_report_df.to_csv(os.path.join(output_folder, "training_results.csv"), index=False, encoding='utf-8-sig')
        
        # แสดงผลลัพธ์
        print("\nผลลัพธ์การทดสอบโดยสรุป:")
        print(final_report_df.to_string(index=False))
        
        print(f"\n💾 บันทึกผลลัพธ์ทั้งหมดที่: {os.path.join(output_folder, 'final_results.csv')}")
        print("✅ บันทึกผลลัพธ์การฝึกโมเดลเรียบร้อยแล้ว")

        # วิเคราะห์และพล็อตผลลัพธ์ (เฉพาะกรณีที่มีผลลัพธ์)
        if not results_df.empty:
            analyze_results(results)
            
            # เปลี่ยนชื่อคอลัมน์ให้สอดคล้องกันหากจำเป็น
            if 'file' in results_df.columns and 'File' not in results_df.columns:
                results_df = results_df.rename(columns={'file': 'File'})
            if 'accuracy' in results_df.columns and 'Test Accuracy' not in results_df.columns:
                results_df = results_df.rename(columns={'accuracy': 'Test Accuracy'})

            # เรียกใช้ฟังก์ชันพล็อตผลลัพธ์แบบปลอดภัย
            safe_plot_results(results_df)
            
        # บันทึกรายงานแบบละเอียด
        if not results_df.empty:
            save_enhanced_report(results, output_folder)
            
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดขณะสร้างรายงาน: {str(e)}")

    print("✅ การประมวลผลเสร็จสิ้น")
        
if __name__ == "__main__":
    main()
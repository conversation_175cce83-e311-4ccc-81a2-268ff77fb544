#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🔍 เริ่มทดสอบ server...")

try:
    print("1. ทดสอบ import lightgbm_server_functions...")
    from lightgbm_server_functions import USE_MULTI_MODEL_ARCHITECTURE, MARKET_SCENARIOS
    print(f"✅ Import สำเร็จ: USE_MULTI_MODEL_ARCHITECTURE = {USE_MULTI_MODEL_ARCHITECTURE}")
    print(f"✅ MARKET_SCENARIOS = {list(MARKET_SCENARIOS.keys())}")
    
    print("\n2. ทดสอบ import Flask...")
    from flask import Flask, request, jsonify
    print("✅ Flask import สำเร็จ")
    
    print("\n3. ทดสอบ import MetaTrader5...")
    import MetaTrader5 as mt5
    print("✅ MetaTrader5 import สำเร็จ")
    
    print("\n4. ทดสอบสร้าง Flask app...")
    app = Flask(__name__)
    print("✅ Flask app สร้างสำเร็จ")
    
    print("\n5. ทดสอบโหลดโมเดล...")
    from lightgbm_server_functions import load_scenario_models
    models = load_scenario_models('GOLD', 30)
    if models:
        print(f"✅ โหลดโมเดลสำเร็จ: {len(models)} scenarios")
    else:
        print("❌ ไม่สามารถโหลดโมเดลได้")
    
    print("\n✅ การทดสอบเสร็จสิ้น - server ควรรันได้")
    
except Exception as e:
    print(f"\n❌ เกิดข้อผิดพลาด: {e}")
    import traceback
    traceback.print_exc()

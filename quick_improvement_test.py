#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Improvement Test - 2025-07-05
ทดสอบการปรับปรุงโมเดลสำหรับ USDJPY และ GBPUSD
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import train_test_split, TimeSeriesSplit, RandomizedSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, roc_auc_score, f1_score, classification_report
import warnings
warnings.filterwarnings('ignore')

# ===== CONFIGURATION =====
TEST_SYMBOLS = ['USDJPY', 'GBPUSD']  # ทดสอบเฉพาะ symbols ที่มีปัญหา
TEST_TIMEFRAMES = ['M30', 'H1']
QUICK_TEST = True  # ใช้ข้อมูลน้อยลงเพื่อทดสอบเร็ว

# ===== IMPROVED PARAMETER DISTRIBUTIONS =====
param_dist_improved = {
    # Learning rate: เน้นช่วงที่เสถียร
    'learning_rate': [0.01, 0.02, 0.05, 0.08, 0.1],
    
    # Num leaves: เน้นรอบค่าเฉลี่ย
    'num_leaves': [8, 10, 12, 15, 20, 25],
    
    # Max depth: เพิ่ม unlimited depth
    'max_depth': [-1, 5, 6, 7, 8],
    
    # Min data in leaf: ลดค่าต่ำสุดเพื่อ minority class
    'min_data_in_leaf': [5, 10, 15, 20],
    
    # Regularization: เพิ่มการ regularize
    'reg_alpha': [0.0, 0.1, 0.3, 0.5, 1.0],
    'reg_lambda': [0.0, 0.1, 0.3, 0.5, 1.0],
    
    # Feature sampling: เน้นช่วงที่เสถียร
    'feature_fraction': [0.7, 0.8, 0.9, 1.0],
    
    # Data sampling: เน้นช่วงที่เสถียร
    'bagging_fraction': [0.7, 0.8, 0.9, 1.0],
    'bagging_freq': [1, 3, 5],
    
    # เพิ่มพารามิเตอร์สำหรับ class imbalance
    'class_weight': ['balanced', None],
    'subsample_for_bin': [50000, 100000, 200000]
}

def get_symbol_specific_params(symbol):
    """ส่งคืนพารามิเตอร์เฉพาะสำหรับแต่ละ symbol"""
    
    if symbol == 'USDJPY':
        # พารามิเตอร์เฉพาะสำหรับ USDJPY (แก้ไข AUC=0.0)
        return {
            'objective': 'binary',
            'metric': ['auc', 'binary_logloss'],
            'boosting_type': 'gbdt',
            'learning_rate': 0.01,      # ลดลงมากเพื่อ stable learning
            'num_leaves': 8,            # ลดลงเพื่อลด overfitting
            'max_depth': 4,             # ลดลงเพื่อ simple model
            'min_data_in_leaf': 30,     # เพิ่มขึ้นเพื่อ stable prediction
            'reg_alpha': 0.5,           # เพิ่มขึ้นเพื่อ regularization
            'reg_lambda': 0.5,          # เพิ่มขึ้นเพื่อ regularization
            'feature_fraction': 0.7,    # ลดลงเพื่อ feature selection
            'bagging_fraction': 0.7,    # ลดลงเพื่อ stable training
            'bagging_freq': 3,
            'min_gain_to_split': 0.02,  # เพิ่มขึ้นเพื่อ conservative split
            'max_bin': 128,             # ลดลงเพื่อ less complexity
            'verbosity': -1,
            'random_state': 42,
            'n_jobs': -1,
            'force_col_wise': True      # เพิ่มเพื่อ stability
        }
    
    elif symbol == 'GBPUSD':
        # พารามิเตอร์เฉพาะสำหรับ GBPUSD (แก้ไข Accuracy=56.5%)
        return {
            'objective': 'binary',
            'metric': ['auc', 'binary_logloss'],
            'boosting_type': 'gbdt',
            'learning_rate': 0.02,      # ลดลงเพื่อ careful learning
            'num_leaves': 12,           # เพิ่มขึ้นเพื่อ capture pattern
            'max_depth': 6,             # เพิ่มขึ้นเพื่อ model capacity
            'min_data_in_leaf': 15,     # ลดลงเพื่อ minority class
            'reg_alpha': 0.3,           # moderate regularization
            'reg_lambda': 0.3,          # moderate regularization
            'feature_fraction': 0.8,    # คงเดิม
            'bagging_fraction': 0.8,    # คงเดิม
            'bagging_freq': 5,
            'min_gain_to_split': 0.01,
            'max_bin': 255,
            'verbosity': -1,
            'random_state': 42,
            'n_jobs': -1,
            'class_weight': 'balanced'  # เพิ่มเพื่อ handle imbalance
        }
    
    else:
        # พารามิเตอร์ทั่วไปสำหรับ symbol อื่นๆ
        return {
            'objective': 'binary',
            'metric': ['auc', 'binary_logloss'],
            'boosting_type': 'gbdt',
            'learning_rate': 0.05,
            'num_leaves': 10,
            'max_depth': 6,
            'min_data_in_leaf': 15,
            'reg_alpha': 0.2,
            'reg_lambda': 0.2,
            'feature_fraction': 0.8,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbosity': -1,
            'random_state': 42,
            'n_jobs': -1
        }

def check_data_quality(X, y, symbol, timeframe):
    """ตรวจสอบ data quality และ class imbalance"""
    print(f"\n🔍 Data Quality Check - {symbol} {timeframe}")
    print("="*50)
    
    # ตรวจสอบ class distribution
    class_dist = y.value_counts()
    print(f"Class distribution: {dict(class_dist)}")
    
    # คำนวณ class ratio
    class_0_count = sum(y == 0)
    class_1_count = sum(y == 1)
    class_ratio = class_0_count / max(class_1_count, 1)
    print(f"Class ratio (0:1): {class_ratio:.2f}")
    
    # ตรวจสอบ minority class ratio
    minority_class_ratio = min(class_0_count, class_1_count) / len(y)
    print(f"Minority class ratio: {minority_class_ratio:.3f}")
    
    # แจ้งเตือนถ้า imbalance รุนแรง
    if minority_class_ratio < 0.05:
        print("⚠️ WARNING: Extreme class imbalance detected!")
        return "EXTREME_IMBALANCE"
    elif minority_class_ratio < 0.1:
        print("⚠️ CAUTION: Moderate class imbalance detected")
        return "MODERATE_IMBALANCE"
    else:
        print("✅ Class distribution is acceptable")
        return "BALANCED"
    
    # ตรวจสอบ missing values
    missing_count = X.isnull().sum().sum()
    if missing_count > 0:
        print(f"⚠️ Missing values detected: {missing_count}")
    else:
        print("✅ No missing values detected")
    
    return "OK"

def test_model_improvement(symbol, timeframe, data_file):
    """ทดสอบการปรับปรุงโมเดลสำหรับ symbol และ timeframe ที่กำหนด"""
    
    print(f"\n{'='*60}")
    print(f"🧪 Testing Model Improvement: {symbol} {timeframe}")
    print(f"{'='*60}")
    
    try:
        # โหลดข้อมูล (สมมติว่ามีไฟล์ CSV)
        # ในการใช้งานจริง ให้ปรับ path และ format ตามข้อมูลจริง
        print(f"📂 Loading data from: {data_file}")
        
        # สร้างข้อมูลตัวอย่างสำหรับการทดสอบ
        # ในการใช้งานจริง ให้แทนที่ด้วยการโหลดข้อมูลจริง
        np.random.seed(42)
        n_samples = 1000 if QUICK_TEST else 5000
        n_features = 20
        
        # สร้างข้อมูลที่มี class imbalance เพื่อจำลองปัญหาจริง
        if symbol == 'USDJPY':
            # จำลอง extreme imbalance สำหรับ USDJPY
            imbalance_ratio = 0.03  # 3% minority class
        elif symbol == 'GBPUSD':
            # จำลอง moderate imbalance สำหรับ GBPUSD
            imbalance_ratio = 0.08  # 8% minority class
        else:
            imbalance_ratio = 0.2   # 20% minority class
        
        # สร้างข้อมูลที่มีความสัมพันธ์ระหว่าง features และ target
        # เพื่อให้โมเดลสามารถเรียนรู้ได้จริง
        X = np.random.randn(n_samples, n_features)

        # สร้าง target ที่มีความสัมพันธ์กับ features
        # ใช้ linear combination ของ features บางตัวเพื่อสร้าง signal
        signal_features = X[:, :5]  # ใช้ 5 features แรกเป็น signal
        signal = np.sum(signal_features * np.array([0.5, -0.3, 0.4, -0.2, 0.6]), axis=1)

        # แปลง signal เป็น probability ด้วย sigmoid
        prob_positive = 1 / (1 + np.exp(-signal))

        # ปรับ probability ให้เข้ากับ imbalance ratio ที่ต้องการ
        # โดยการ shift และ scale
        prob_positive = prob_positive - np.mean(prob_positive) + imbalance_ratio
        prob_positive = np.clip(prob_positive, 0.001, 0.999)  # ป้องกันค่าสุดขั้ว

        # สร้าง target จาก probability
        y = np.random.binomial(1, prob_positive)
        
        # แปลงเป็น DataFrame
        feature_names = [f'feature_{i}' for i in range(n_features)]
        X = pd.DataFrame(X, columns=feature_names)
        y = pd.Series(y)
        
        print(f"✅ Data loaded: {len(X)} samples, {len(X.columns)} features")
        
        # ตรวจสอบ data quality
        data_quality = check_data_quality(X, y, symbol, timeframe)
        
        # แบ่งข้อมูล
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        X_train, X_val, y_train, y_val = train_test_split(
            X_train, y_train, test_size=0.25, random_state=42, stratify=y_train
        )
        
        print(f"📊 Data split:")
        print(f"   - Train: {len(X_train)} samples")
        print(f"   - Val: {len(X_val)} samples") 
        print(f"   - Test: {len(X_test)} samples")
        
        # Scaling
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_val_scaled = scaler.transform(X_val)
        X_test_scaled = scaler.transform(X_test)
        
        # 1. ทดสอบด้วย Default Parameters (เดิม)
        print(f"\n1️⃣ Testing with Default Parameters")
        default_params = {
            'objective': 'binary',
            'metric': 'auc',
            'boosting_type': 'gbdt',
            'learning_rate': 0.1,
            'num_leaves': 31,
            'max_depth': -1,
            'min_data_in_leaf': 20,
            'verbosity': -1,
            'random_state': 42
        }
        
        default_model = lgb.LGBMClassifier(**default_params, n_estimators=100)
        default_model.fit(X_train_scaled, y_train)
        
        default_pred = default_model.predict_proba(X_val_scaled)[:, 1]
        default_pred_binary = default_model.predict(X_val_scaled)
        
        default_auc = roc_auc_score(y_val, default_pred) if len(np.unique(y_val)) > 1 else 0.0
        default_f1 = f1_score(y_val, default_pred_binary)
        default_acc = accuracy_score(y_val, default_pred_binary)
        
        print(f"   Default - AUC: {default_auc:.3f}, F1: {default_f1:.3f}, Acc: {default_acc:.3f}")
        
        # 2. ทดสอบด้วย Symbol-Specific Parameters (ใหม่)
        print(f"\n2️⃣ Testing with Symbol-Specific Parameters")
        improved_params = get_symbol_specific_params(symbol)
        
        improved_model = lgb.LGBMClassifier(**improved_params, n_estimators=100)
        improved_model.fit(X_train_scaled, y_train)
        
        improved_pred = improved_model.predict_proba(X_val_scaled)[:, 1]
        improved_pred_binary = improved_model.predict(X_val_scaled)
        
        improved_auc = roc_auc_score(y_val, improved_pred) if len(np.unique(y_val)) > 1 else 0.0
        improved_f1 = f1_score(y_val, improved_pred_binary)
        improved_acc = accuracy_score(y_val, improved_pred_binary)
        
        print(f"   Improved - AUC: {improved_auc:.3f}, F1: {improved_f1:.3f}, Acc: {improved_acc:.3f}")
        
        # 3. ทดสอบด้วย Hyperparameter Tuning (ใหม่)
        print(f"\n3️⃣ Testing with Hyperparameter Tuning")
        
        # ใช้การตั้งค่าเฉพาะสำหรับ problematic symbols
        if symbol in ['USDJPY', 'GBPUSD']:
            n_iter_tuning = 20 if QUICK_TEST else 50  # ลดลงสำหรับ quick test
            cv_splits = 3
            scoring_metric = 'f1_weighted'
        else:
            n_iter_tuning = 15 if QUICK_TEST else 30
            cv_splits = 3
            scoring_metric = 'roc_auc'
        
        base_estimator = lgb.LGBMClassifier(**improved_params, n_estimators=100)
        
        # ลด param_dist สำหรับ quick test
        quick_param_dist = {
            'learning_rate': [0.01, 0.05, 0.1],
            'num_leaves': [8, 12, 20],
            'max_depth': [4, 6, -1],
            'min_data_in_leaf': [10, 20],
            'reg_alpha': [0.0, 0.3],
            'reg_lambda': [0.0, 0.3]
        } if QUICK_TEST else param_dist_improved
        
        search = RandomizedSearchCV(
            base_estimator,
            param_distributions=quick_param_dist,
            n_iter=n_iter_tuning,
            scoring=scoring_metric,
            cv=TimeSeriesSplit(n_splits=cv_splits),
            verbose=1,
            random_state=42,
            n_jobs=-1
        )
        
        search.fit(X_train_scaled, y_train)
        
        tuned_pred = search.predict_proba(X_val_scaled)[:, 1]
        tuned_pred_binary = search.predict(X_val_scaled)
        
        tuned_auc = roc_auc_score(y_val, tuned_pred) if len(np.unique(y_val)) > 1 else 0.0
        tuned_f1 = f1_score(y_val, tuned_pred_binary)
        tuned_acc = accuracy_score(y_val, tuned_pred_binary)
        
        print(f"   Tuned - AUC: {tuned_auc:.3f}, F1: {tuned_f1:.3f}, Acc: {tuned_acc:.3f}")
        print(f"   Best params: {search.best_params_}")
        
        # สรุปผลการปรับปรุง
        print(f"\n📈 Improvement Summary for {symbol} {timeframe}:")
        print(f"   AUC:  {default_auc:.3f} → {improved_auc:.3f} → {tuned_auc:.3f}")
        print(f"   F1:   {default_f1:.3f} → {improved_f1:.3f} → {tuned_f1:.3f}")
        print(f"   Acc:  {default_acc:.3f} → {improved_acc:.3f} → {tuned_acc:.3f}")
        
        # ประเมินการปรับปรุง
        auc_improvement = tuned_auc - default_auc
        f1_improvement = tuned_f1 - default_f1
        acc_improvement = tuned_acc - default_acc
        
        print(f"\n🎯 Improvement Analysis:")
        print(f"   AUC improvement: {auc_improvement:+.3f}")
        print(f"   F1 improvement: {f1_improvement:+.3f}")
        print(f"   Accuracy improvement: {acc_improvement:+.3f}")
        
        if auc_improvement > 0.05 or f1_improvement > 0.05:
            print("✅ Significant improvement detected!")
        elif auc_improvement > 0.02 or f1_improvement > 0.02:
            print("🔶 Moderate improvement detected")
        else:
            print("⚠️ Limited improvement - may need further optimization")
        
        return {
            'symbol': symbol,
            'timeframe': timeframe,
            'data_quality': data_quality,
            'default_auc': default_auc,
            'improved_auc': improved_auc,
            'tuned_auc': tuned_auc,
            'default_f1': default_f1,
            'improved_f1': improved_f1,
            'tuned_f1': tuned_f1,
            'auc_improvement': auc_improvement,
            'f1_improvement': f1_improvement,
            'best_params': search.best_params_
        }
        
    except Exception as e:
        print(f"❌ Error testing {symbol} {timeframe}: {str(e)}")
        return None

def main():
    """Main function สำหรับการทดสอบ"""
    print("🚀 Quick Model Improvement Test - 2025-07-05")
    print("="*60)
    
    results = []
    
    for symbol in TEST_SYMBOLS:
        for timeframe in TEST_TIMEFRAMES:
            # สร้าง dummy file path (ในการใช้งานจริงให้ใช้ path จริง)
            data_file = f"data/{symbol}_{timeframe}_processed.csv"
            
            result = test_model_improvement(symbol, timeframe, data_file)
            if result:
                results.append(result)
    
    # สรุปผลรวม
    if results:
        print(f"\n{'='*60}")
        print("📊 OVERALL IMPROVEMENT SUMMARY")
        print(f"{'='*60}")
        
        for result in results:
            print(f"\n{result['symbol']} {result['timeframe']}:")
            print(f"   Data Quality: {result['data_quality']}")
            print(f"   AUC: {result['default_auc']:.3f} → {result['tuned_auc']:.3f} ({result['auc_improvement']:+.3f})")
            print(f"   F1:  {result['default_f1']:.3f} → {result['tuned_f1']:.3f} ({result['f1_improvement']:+.3f})")
        
        # คำนวณการปรับปรุงเฉลี่ย
        avg_auc_improvement = np.mean([r['auc_improvement'] for r in results])
        avg_f1_improvement = np.mean([r['f1_improvement'] for r in results])
        
        print(f"\n🎯 Average Improvements:")
        print(f"   AUC: {avg_auc_improvement:+.3f}")
        print(f"   F1:  {avg_f1_improvement:+.3f}")
        
        if avg_auc_improvement > 0.05 or avg_f1_improvement > 0.05:
            print("\n✅ Overall improvements are SIGNIFICANT!")
            print("   Recommend proceeding with full training")
        elif avg_auc_improvement > 0.02 or avg_f1_improvement > 0.02:
            print("\n🔶 Overall improvements are MODERATE")
            print("   Consider additional optimizations")
        else:
            print("\n⚠️ Overall improvements are LIMITED")
            print("   May need fundamental changes to approach")

if __name__ == "__main__":
    main()

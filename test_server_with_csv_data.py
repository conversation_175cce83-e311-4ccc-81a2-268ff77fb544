#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import pandas as pd
import numpy as np
from datetime import datetime

def load_csv_data(symbol='GOLD', timeframe='M30', num_bars=210):
    """โหลดข้อมูลจาก CSV_Files_Fixed"""
    
    csv_file = f"CSV_Files_Fixed/{symbol}_{timeframe}_FIXED.csv"
    
    try:
        print(f"🔍 Loading data from {csv_file}")
        
        # อ่านข้อมูลจาก CSV (ใช้ comma separator)
        df = pd.read_csv(csv_file, sep=',')
        print(f"✅ Loaded {len(df)} rows from CSV")
        print(f"📊 Columns: {list(df.columns)}")

        # ข้าม header rows ที่ไม่ใช่ข้อมูล
        df = df[df['Date'] != '<DATE>'].copy()
        print(f"📊 After filtering: {len(df)} rows")

        # เปลี่ยนชื่อคอลัมน์ให้ตรงกับที่ต้องการ
        column_mapping = {
            'Date': 'Date',
            'Time': 'Time',
            'Open': 'Open',
            'High': 'High',
            'Low': 'Low',
            'Close': 'Close',
            'TickVol': 'Volume',
            'Vol': 'RealVolume'
        }

        # ตรวจสอบคอลัมน์ที่จำเป็น
        required_cols = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'TickVol']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"❌ Missing columns: {missing_cols}")
            return None
        
        # เลือกข้อมูลล่าสุด num_bars แท่ง
        df_recent = df.tail(num_bars).copy()
        print(f"📊 Selected {len(df_recent)} recent bars")

        # รวม Date และ Time เป็น datetime
        df_recent['DateTime'] = pd.to_datetime(df_recent['Date'] + ' ' + df_recent['Time'])
        df_recent['timestamp'] = df_recent['DateTime'].astype('int64') // 10**9  # Convert to Unix timestamp

        # แปลงข้อมูลเป็น numeric
        numeric_cols = ['Open', 'High', 'Low', 'Close', 'TickVol']
        for col in numeric_cols:
            df_recent[col] = pd.to_numeric(df_recent[col], errors='coerce')
        
        # สร้าง bars list สำหรับ server
        bars = []
        for _, row in df_recent.iterrows():
            bar = {
                "time": int(row['timestamp']),
                "open": float(row['Open']),
                "high": float(row['High']),
                "low": float(row['Low']),
                "close": float(row['Close']),
                "volume": int(row['TickVol']),
                "tick_volume": int(row['TickVol']),
                "spread": 5,
                "real_volume": int(row['TickVol'])
            }
            bars.append(bar)
        
        print(f"✅ Created {len(bars)} bars for server")
        print(f"🕒 Time range: {bars[0]['time']} to {bars[-1]['time']}")
        
        return bars
        
    except Exception as e:
        print(f"❌ Error loading CSV data: {e}")
        return None

def test_server_with_csv_data():
    """ทดสอบ server ด้วยข้อมูลจาก CSV"""
    
    url = 'http://127.0.0.1:54321/data'
    
    print("🔍 Loading CSV data...")
    bars = load_csv_data('GOLD', 'M30', 210)
    
    if not bars:
        print("❌ Failed to load CSV data")
        return False
    
    test_data = {
        "symbol": "GOLD",
        "timeframe_str": "PERIOD_M30",
        "bars": bars
    }
    
    try:
        print("🚀 Sending CSV data to server...")
        print(f"URL: {url}")
        print(f"Data: {len(bars)} bars from CSV")
        
        # ส่งข้อมูลไป server
        response = requests.post(url, json=test_data, timeout=60)
        
        print(f"✅ Server response: HTTP {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("\n📊 Server response:")
                print(f"  Signal: {result.get('signal', 'N/A')}")
                print(f"  Class: {result.get('class', 'N/A')}")
                print(f"  Confidence: {result.get('confidence', 'N/A')}")
                print(f"  Symbol: {result.get('symbol', 'N/A')}")
                print(f"  Timeframe: {result.get('timeframe_str', 'N/A')}")
                print(f"  Entry Price: {result.get('entry_price', 'N/A')}")
                print(f"  SL Price: {result.get('sl_price', 'N/A')}")
                print(f"  TP Price: {result.get('tp_price', 'N/A')}")
                print(f"  Threshold: {result.get('threshold', 'N/A')}")
                print(f"  nBars_SL: {result.get('nBars_SL', 'N/A')}")
                print(f"  Time Filters: {result.get('time_filters', 'N/A')}")
                print(f"  Best Entry: {result.get('best_entry', 'N/A')}")
                print(f"  Spread: {result.get('spread', 'N/A')}")
                
                # วิเคราะห์ผลลัพธ์
                signal = result.get('signal', 'HOLD')
                confidence = result.get('confidence', 0.0)
                
                print(f"\n🎯 Analysis:")
                if signal == 'ERROR':
                    print("  ❌ Server returned ERROR signal")
                    print("  🔍 Check server logs for detailed error information")
                elif signal == 'HOLD':
                    print("  📊 Model recommends HOLD")
                    if confidence == 0.0:
                        print("  ⚠️  Confidence = 0 may indicate:")
                        print("     - Insufficient data for prediction")
                        print("     - Model not confident in decision")
                        print("     - Not in suitable time window for trading")
                elif signal in ['BUY', 'SELL']:
                    print(f"  🎯 Model recommends {signal} with confidence {confidence}")
                    print(f"  💰 Entry: {result.get('entry_price', 'N/A')}")
                    print(f"  🛡️  SL: {result.get('sl_price', 'N/A')}")
                    print(f"  🎯 TP: {result.get('tp_price', 'N/A')}")
                
                return signal != 'ERROR'
                
            except json.JSONDecodeError:
                print(f"❌ Cannot decode JSON: {response.text[:200]}...")
                return False
        else:
            print(f"❌ Server error: {response.text[:200]}...")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server")
        return False
    except requests.exceptions.Timeout:
        print("❌ Server timeout")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing server with CSV data...")
    
    success = test_server_with_csv_data()
    
    if success:
        print("\n✅ Test successful - server working properly")
    else:
        print("\n❌ Test failed - server has issues")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Flexible Threshold Criteria
ทดสอบเกณฑ์ที่ยืดหยุ่นกว่าสำหรับการหา threshold
"""

import pandas as pd
import numpy as np
import sys
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

# เพิ่ม path สำหรับ import
sys.path.append('.')

def create_test_scenarios():
    """
    สร้างสถานการณ์ทดสอบต่างๆ
    """
    scenarios = [
        {
            'name': 'Large Model (216 features)',
            'total_features': 216,
            'available_features': 72,  # 33%
            'expected_result': 'PASS',  # ควรผ่านเพราะ 72 > 54 (25% of 216)
            'description': 'โมเดลใหญ่ที่มี features 33% ควรผ่าน'
        },
        {
            'name': 'Large Model (200 features)',
            'total_features': 200,
            'available_features': 40,   # 20%
            'expected_result': 'PASS',  # ควรผ่านเพราะ 40 > 50 (25% of 200) แต่ 40 > 30 (min)
            'description': 'โมเดลใหญ่ที่มี features 20% ควรผ่านเพราะมากกว่า 30'
        },
        {
            'name': 'Large Model (150 features)',
            'total_features': 150,
            'available_features': 25,   # 17%
            'expected_result': 'FAIL',  # ควรไม่ผ่านเพราะ 25 < 37.5 (25% of 150) และ 25 < 30 (min)
            'description': 'โมเดลใหญ่ที่มี features น้อยเกินไป'
        },
        {
            'name': 'Small Model (50 features)',
            'total_features': 50,
            'available_features': 25,   # 50%
            'expected_result': 'PASS',  # ควรผ่านเพราะ 25 > 20 (40% of 50)
            'description': 'โมเดลเล็กที่มี features 50% ควรผ่าน'
        },
        {
            'name': 'Small Model (40 features)',
            'total_features': 40,
            'available_features': 12,   # 30%
            'expected_result': 'FAIL',  # ควรไม่ผ่านเพราะ 12 < 16 (40% of 40) และ 12 < 15 (min)
            'description': 'โมเดลเล็กที่มี features น้อยเกินไป'
        }
    ]
    
    return scenarios

def calculate_min_features_required(total_features):
    """
    คำนวณจำนวน features ขั้นต่ำที่ต้องการตามเกณฑ์ใหม่
    """
    if total_features > 100:
        min_features_required = max(30, total_features * 0.25)  # 25% สำหรับโมเดลใหญ่
    else:
        min_features_required = max(15, total_features * 0.4)   # 40% สำหรับโมเดลเล็ก
    
    return min_features_required

def test_threshold_criteria():
    """
    ทดสอบเกณฑ์การตรวจสอบ features
    """
    print("🧪 Testing Flexible Threshold Criteria")
    print("=" * 80)
    
    scenarios = create_test_scenarios()
    results = []
    
    for scenario in scenarios:
        print(f"\n📋 Testing: {scenario['name']}")
        print(f"   Total features: {scenario['total_features']}")
        print(f"   Available features: {scenario['available_features']} ({scenario['available_features']/scenario['total_features']*100:.1f}%)")
        
        # คำนวณเกณฑ์ขั้นต่ำ
        min_required = calculate_min_features_required(scenario['total_features'])
        print(f"   Min required: {min_required:.0f}")
        
        # ตรวจสอบผลลัพธ์
        actual_result = "PASS" if scenario['available_features'] >= min_required else "FAIL"
        expected_result = scenario['expected_result']
        
        print(f"   Expected: {expected_result}, Actual: {actual_result}")
        
        if actual_result == expected_result:
            print(f"   ✅ CORRECT - {scenario['description']}")
            results.append(True)
        else:
            print(f"   ❌ INCORRECT - {scenario['description']}")
            results.append(False)
    
    return results

def create_mock_data_for_scenario(total_features, available_features):
    """
    สร้างข้อมูล mock สำหรับสถานการณ์ทดสอบ
    """
    # สร้าง feature names
    all_features = [f"Feature_{i:03d}" for i in range(total_features)]
    available_feature_names = all_features[:available_features]
    
    # สร้างข้อมูล validation
    n_samples = 200
    val_data = {}
    
    for feature in available_feature_names:
        val_data[feature] = np.random.randn(n_samples)
    
    val_data['Target'] = np.random.randint(0, 2, n_samples)
    val_data['Close'] = 1.1000 + np.random.randn(n_samples) * 0.001
    
    val_df = pd.DataFrame(val_data)
    
    # สร้าง mock model
    X_train = np.random.randn(100, total_features)
    y_train = np.random.randint(0, 2, 100)
    
    model = RandomForestClassifier(n_estimators=5, random_state=42)
    model.fit(X_train, y_train)
    
    scaler = StandardScaler()
    scaler.fit(X_train)
    
    return val_df, all_features, model, scaler

def test_real_threshold_functions():
    """
    ทดสอบฟังก์ชัน threshold จริงกับเกณฑ์ใหม่
    """
    print(f"\n🧪 Testing Real Threshold Functions with New Criteria")
    print("=" * 80)
    
    try:
        from python_LightGBM_16_Signal import (
            find_best_threshold_simple,
            find_optimal_threshold_multi_model,
            analyze_market_conditions_for_threshold
        )
        
        # ทดสอบกับสถานการณ์ที่ควรผ่าน (โมเดลใหญ่ 216 features, มี 72 features)
        print(f"\n📊 Testing Large Model Scenario (216 features, 72 available)")
        
        val_df, all_features, model, scaler = create_mock_data_for_scenario(216, 72)
        
        # ทดสอบ find_best_threshold_simple
        print(f"\n1. Testing find_best_threshold_simple:")
        result1 = find_best_threshold_simple(
            model=model,
            scaler=scaler,
            val_df=val_df,
            model_features=all_features,
            thresholds=np.arange(0.3, 0.8, 0.2)  # ใช้ range เล็กเพื่อทดสอบเร็ว
        )
        print(f"   Result: {result1}")
        
        # ทดสอบ full system
        print(f"\n2. Testing full threshold system:")
        
        # สร้าง models dict
        models_dict = {
            'trend_following': {
                'model': model,
                'scaler': scaler,
                'features': all_features
            },
            'counter_trend': {
                'model': model,
                'scaler': scaler,
                'features': all_features
            }
        }
        
        result2 = find_optimal_threshold_multi_model(
            models_dict=models_dict,
            val_df=val_df,
            symbol="GOLD",
            timeframe=60
        )
        print(f"   Result: {result2}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_comprehensive_test():
    """
    รันการทดสอบแบบครอบคลุม
    """
    print("🧪 Flexible Threshold Criteria - Comprehensive Test")
    print("=" * 80)
    
    # ทดสอบเกณฑ์การคำนวณ
    criteria_results = test_threshold_criteria()
    
    # ทดสอบฟังก์ชันจริง
    function_result = test_real_threshold_functions()
    
    # สรุปผลการทดสอบ
    print(f"\n📊 Test Results Summary")
    print("=" * 80)
    
    criteria_passed = sum(criteria_results)
    criteria_total = len(criteria_results)
    
    print(f"Criteria Tests: {criteria_passed}/{criteria_total} passed")
    print(f"Function Test: {'✅ PASSED' if function_result else '❌ FAILED'}")
    
    if criteria_passed == criteria_total and function_result:
        print(f"\n🎉 All tests passed! Flexible criteria are working correctly.")
        print(f"\n💡 New Criteria Summary:")
        print(f"   - Large Models (>100 features): min 25% or 30 features (whichever is higher)")
        print(f"   - Small Models (≤100 features): min 40% or 15 features (whichever is higher)")
        print(f"   - This allows 72/216 features (33%) to pass for large models")
        print(f"   - While maintaining quality standards for smaller models")
    else:
        print(f"\n⚠️ Some tests failed. Please check the implementation.")
    
    return criteria_results, function_result

if __name__ == "__main__":
    results = run_comprehensive_test()
    
    print(f"\n📝 Test completed.")
    print(f"💡 The flexible criteria should now allow reasonable feature ratios while maintaining quality!")

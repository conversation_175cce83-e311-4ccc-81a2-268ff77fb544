#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ตรวจสอบและแก้ไขขั้นตอนการทดสอบ optimization

Created: 2025-01-11
Author: AI Assistant
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
import json
from pathlib import Path

# เพิ่ม path สำหรับ import
sys.path.append('.')

def debug_threshold_optimization():
    """
    ตรวจสอบขั้นตอนการหา optimal threshold อย่างละเอียด
    """
    print("🔍 ตรวจสอบขั้นตอนการหา optimal threshold อย่างละเอียด")
    print("="*70)
    
    try:
        from python_LightGBM_16_Signal import (
            load_scenario_models,
            load_validation_data_for_optimization,
            find_best_threshold_on_val
        )
        
        symbol = "GOLD"
        timeframe = 60
        
        print(f"📊 ทดสอบกับ {symbol} M{timeframe}")
        
        # 1. โหลดโมเดล
        print(f"\n1. โหลดโมเดล:")
        models_dict = load_scenario_models(symbol, timeframe)
        
        if not models_dict:
            print(f"❌ ไม่พบโมเดลสำหรับ {symbol} M{timeframe}")
            return False
        
        print(f"✅ โหลดโมเดลสำเร็จ: {len(models_dict)} scenarios")
        for scenario, model_data in models_dict.items():
            print(f"   {scenario}: {len(model_data['features'])} features")
        
        # 2. โหลดข้อมูล validation
        print(f"\n2. โหลดข้อมูล validation:")
        val_df = load_validation_data_for_optimization(symbol, timeframe)
        
        if val_df is None:
            print(f"❌ ไม่สามารถโหลดข้อมูล validation ได้")
            return False
        
        print(f"✅ โหลดข้อมูล validation สำเร็จ: {len(val_df)} rows, {len(val_df.columns)} columns")
        print(f"📊 คอลัมน์ที่มี: {list(val_df.columns)[:10]}...")
        
        # 3. ทดสอบการหา threshold แต่ละ scenario
        for scenario_name, model_data in models_dict.items():
            print(f"\n3. ทดสอบการหา threshold สำหรับ {scenario_name}:")
            
            model = model_data['model']
            features = model_data['features']
            scaler = model_data['scaler']
            
            print(f"   📊 Model features: {len(features)} features")
            print(f"   📊 Validation columns: {len(val_df.columns)} columns")
            
            # ตรวจสอบ features ที่มีอยู่
            available_features = [f for f in features if f in val_df.columns]
            missing_features = [f for f in features if f not in val_df.columns]
            
            print(f"   📊 Available features: {len(available_features)}/{len(features)}")
            print(f"   ⚠️ Missing features: {len(missing_features)}")
            
            if len(missing_features) > 0:
                print(f"   📋 Missing features (first 10): {missing_features[:10]}")
            
            # ถ้ามี features เพียงพอ ให้ทดสอบการหา threshold
            min_features_required = max(10, len(features) * 0.3)
            
            if len(available_features) >= min_features_required:
                print(f"   ✅ Features เพียงพอสำหรับการหา threshold ({len(available_features)} >= {min_features_required:.0f})")
                
                # สร้าง validation data ที่มีเฉพาะ features ที่ต้องการ
                val_df_subset = val_df[available_features + ['Target']].copy()
                
                print(f"   📊 Validation subset: {len(val_df_subset)} rows, {len(val_df_subset.columns)} columns")
                print(f"   📊 Target distribution: {val_df_subset['Target'].value_counts().to_dict()}")
                
                # ทดสอบการหา threshold
                try:
                    print(f"   🚀 เริ่มการหา optimal threshold...")
                    
                    # ทดสอบด้วย threshold range ที่หลากหลาย
                    threshold_range = np.arange(0.3, 0.8, 0.05)
                    print(f"   📊 Threshold range: {threshold_range}")
                    
                    best_threshold = find_best_threshold_on_val(
                        model=model,
                        scaler=scaler,
                        features=available_features,
                        val_df=val_df_subset,
                        threshold_range=threshold_range
                    )
                    
                    print(f"   ✅ พบ optimal threshold: {best_threshold}")
                    
                    # ทดสอบการทำนายด้วย threshold ที่ได้
                    X_val = val_df_subset[available_features].fillna(0)
                    X_val_scaled = scaler.transform(X_val)
                    y_pred_proba = model.predict_proba(X_val_scaled)[:, 1]
                    y_pred = (y_pred_proba >= best_threshold).astype(int)
                    
                    # คำนวณ metrics
                    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
                    
                    accuracy = accuracy_score(val_df_subset['Target'], y_pred)
                    precision = precision_score(val_df_subset['Target'], y_pred, zero_division=0)
                    recall = recall_score(val_df_subset['Target'], y_pred, zero_division=0)
                    f1 = f1_score(val_df_subset['Target'], y_pred, zero_division=0)
                    
                    print(f"   📊 Performance metrics:")
                    print(f"     Accuracy: {accuracy:.4f}")
                    print(f"     Precision: {precision:.4f}")
                    print(f"     Recall: {recall:.4f}")
                    print(f"     F1-Score: {f1:.4f}")
                    
                except Exception as e:
                    print(f"   ❌ เกิดข้อผิดพลาดในการหา threshold: {e}")
                    print(f"   🔧 ใช้ default threshold: 0.5")
            else:
                print(f"   ❌ Features ไม่เพียงพอสำหรับการหา threshold ({len(available_features)} < {min_features_required:.0f})")
                print(f"   🔧 ใช้ default threshold: 0.5")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def debug_nbars_optimization():
    """
    ตรวจสอบขั้นตอนการหา optimal nBars_SL อย่างละเอียด
    """
    print("\n🔍 ตรวจสอบขั้นตอนการหา optimal nBars_SL อย่างละเอียด")
    print("="*70)
    
    try:
        from python_LightGBM_16_Signal import (
            load_validation_data_for_optimization,
            find_optimal_nbars_simple
        )
        
        symbol = "GOLD"
        timeframe = 60
        
        print(f"📊 ทดสอบกับ {symbol} M{timeframe}")
        
        # โหลดข้อมูล validation
        print(f"\n1. โหลดข้อมูล validation:")
        val_df = load_validation_data_for_optimization(symbol, timeframe)
        
        if val_df is None:
            print(f"❌ ไม่สามารถโหลดข้อมูล validation ได้")
            return False
        
        print(f"✅ โหลดข้อมูล validation สำเร็จ: {len(val_df)} rows")
        
        # ตรวจสอบข้อมูลที่จำเป็นสำหรับการคำนวณ nBars_SL
        required_columns = ['High', 'Low', 'Close']
        available_columns = [col for col in required_columns if col in val_df.columns]
        
        print(f"📊 Required columns: {required_columns}")
        print(f"📊 Available columns: {available_columns}")
        
        if len(available_columns) == len(required_columns):
            print(f"✅ มีข้อมูลครบสำหรับการคำนวณ nBars_SL")
            
            # ทดสอบการคำนวณ volatility
            print(f"\n2. ทดสอบการคำนวณ volatility:")
            
            # คำนวณ True Range
            tr1 = val_df['High'] - val_df['Low']
            tr2 = abs(val_df['High'] - val_df['Close'].shift(1))
            tr3 = abs(val_df['Low'] - val_df['Close'].shift(1))
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            
            # คำนวณ ATR
            atr_period = 14
            atr = true_range.rolling(window=atr_period).mean()
            
            # คำนวณ volatility
            volatility = atr.iloc[-100:].mean() / val_df['Close'].iloc[-100:].mean()
            
            print(f"   📊 True Range (last 10): {true_range.tail(10).values}")
            print(f"   📊 ATR (last 10): {atr.tail(10).values}")
            print(f"   📊 Calculated volatility: {volatility:.6f}")
            
            # ทดสอบการหา optimal nBars_SL
            print(f"\n3. ทดสอบการหา optimal nBars_SL:")
            
            try:
                optimal_nbars = find_optimal_nbars_simple(val_df)
                print(f"   ✅ พบ optimal nBars_SL: {optimal_nbars}")
                
                # ทดสอบด้วยค่า volatility ที่แตกต่างกัน
                print(f"\n4. ทดสอบด้วยค่า volatility ที่แตกต่างกัน:")
                
                test_volatilities = [0.001, 0.002, 0.003, 0.005, 0.01]
                
                for test_vol in test_volatilities:
                    if test_vol <= 0.002:
                        test_nbars = max(3, min(15, int(6 + (0.002 - test_vol) * 1000)))
                    else:
                        test_nbars = max(3, min(15, int(6 - (test_vol - 0.002) * 500)))
                    
                    print(f"   📊 Volatility: {test_vol:.4f} → nBars_SL: {test_nbars}")
                
            except Exception as e:
                print(f"   ❌ เกิดข้อผิดพลาดในการหา nBars_SL: {e}")
                print(f"   🔧 ใช้ default nBars_SL: 6")
        else:
            print(f"❌ ขาดข้อมูลที่จำเป็น: {set(required_columns) - set(available_columns)}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_multiple_symbols():
    """
    ทดสอบกับหลายสัญลักษณ์เพื่อดูความแตกต่าง
    """
    print("\n🔍 ทดสอบกับหลายสัญลักษณ์เพื่อดูความแตกต่าง")
    print("="*60)
    
    try:
        from python_LightGBM_16_Signal import (
            parse_filename,
            load_scenario_models,
            load_validation_data_for_optimization
        )
        
        # รายการไฟล์ที่จะทดสอบ
        test_files = [
            "CSV_Files_Fixed/EURUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/GOLD_H1_FIXED.csv", 
            "CSV_Files_Fixed/USDJPY_H1_FIXED.csv"
        ]
        
        results = {}
        
        for file in test_files:
            print(f"\n📊 ทดสอบไฟล์: {file}")
            
            try:
                # Parse filename
                file_info = parse_filename(file)
                symbol = file_info["Name_Currency"]
                timeframe = file_info["Timeframe_Currency"]
                
                print(f"   Symbol: {symbol}, Timeframe: M{timeframe}")
                
                # ตรวจสอบว่ามีโมเดลหรือไม่
                models_dict = load_scenario_models(symbol, timeframe)
                
                if models_dict:
                    print(f"   ✅ มีโมเดล: {len(models_dict)} scenarios")
                    
                    # โหลดข้อมูล validation
                    val_df = load_validation_data_for_optimization(symbol, timeframe)
                    
                    if val_df is not None:
                        print(f"   ✅ มีข้อมูล validation: {len(val_df)} rows")
                        
                        # คำนวณ basic statistics
                        if 'Close' in val_df.columns:
                            close_mean = val_df['Close'].mean()
                            close_std = val_df['Close'].std()
                            close_range = val_df['Close'].max() - val_df['Close'].min()
                            
                            print(f"   📊 Close statistics:")
                            print(f"     Mean: {close_mean:.4f}")
                            print(f"     Std: {close_std:.4f}")
                            print(f"     Range: {close_range:.4f}")
                            
                            # คำนวณ volatility
                            if all(col in val_df.columns for col in ['High', 'Low']):
                                tr1 = val_df['High'] - val_df['Low']
                                tr2 = abs(val_df['High'] - val_df['Close'].shift(1))
                                tr3 = abs(val_df['Low'] - val_df['Close'].shift(1))
                                true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
                                atr = true_range.rolling(window=14).mean()
                                volatility = atr.iloc[-100:].mean() / close_mean
                                
                                print(f"     Volatility: {volatility:.6f}")
                                
                                # คำนวณ nBars_SL ที่แนะนำ
                                if volatility <= 0.002:
                                    suggested_nbars = max(3, min(15, int(6 + (0.002 - volatility) * 1000)))
                                else:
                                    suggested_nbars = max(3, min(15, int(6 - (volatility - 0.002) * 500)))
                                
                                print(f"     Suggested nBars_SL: {suggested_nbars}")
                                
                                results[symbol] = {
                                    'volatility': volatility,
                                    'suggested_nbars': suggested_nbars,
                                    'close_mean': close_mean,
                                    'close_std': close_std
                                }
                        
                    else:
                        print(f"   ❌ ไม่สามารถโหลดข้อมูล validation ได้")
                else:
                    print(f"   ❌ ไม่พบโมเดลสำหรับ {symbol} M{timeframe}")
                    
            except Exception as e:
                print(f"   ❌ เกิดข้อผิดพลาด: {e}")
        
        # สรุปผลการทดสอบ
        print(f"\n📊 สรุปผลการทดสอบ:")
        print("="*50)
        
        for symbol, data in results.items():
            print(f"{symbol}:")
            print(f"  Volatility: {data['volatility']:.6f}")
            print(f"  Suggested nBars_SL: {data['suggested_nbars']}")
            print(f"  Close Mean: {data['close_mean']:.4f}")
            print(f"  Close Std: {data['close_std']:.4f}")
        
        return len(results) > 0
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🚀 ตรวจสอบและแก้ไขขั้นตอนการทดสอบ Optimization")
    print("="*80)
    
    # Test 1: ตรวจสอบการหา threshold
    threshold_success = debug_threshold_optimization()
    
    # Test 2: ตรวจสอบการหา nBars_SL
    nbars_success = debug_nbars_optimization()
    
    # Test 3: ทดสอบกับหลายสัญลักษณ์
    multi_symbol_success = test_multiple_symbols()
    
    print("\n" + "="*80)
    print("📊 สรุปผลการตรวจสอบ")
    print("="*80)
    
    print(f"✅ Test 1 - Threshold Optimization: {'ผ่าน' if threshold_success else 'ล้มเหลว'}")
    print(f"✅ Test 2 - nBars_SL Optimization: {'ผ่าน' if nbars_success else 'ล้มเหลว'}")
    print(f"✅ Test 3 - Multiple Symbols: {'ผ่าน' if multi_symbol_success else 'ล้มเหลว'}")
    
    overall_success = all([threshold_success, nbars_success, multi_symbol_success])
    
    if overall_success:
        print(f"\n🎉 การตรวจสอบเสร็จสิ้น!")
        print(f"💡 แนะนำ: ปรับปรุงการหา threshold ให้ทำงานได้จริง")
        print(f"💡 แนะนำ: เพิ่มการแสดงผลรายละเอียดในขั้นตอน optimization")
    else:
        print(f"\n⚠️ พบปัญหาในการตรวจสอบ")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ลด Overfitting ในโมเดล LightGBM Trading
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score, accuracy_score, f1_score
import warnings
warnings.filterwarnings('ignore')

def get_anti_overfitting_params():
    """พารามิเตอร์ที่ช่วยลด overfitting"""
    return {
        # Regularization
        'reg_alpha': 0.1,        # L1 regularization
        'reg_lambda': 0.1,       # L2 regularization
        
        # Tree structure
        'max_depth': 4,          # ลดความลึกของต้นไม้
        'num_leaves': 8,         # ลดจำนวน leaves
        'min_data_in_leaf': 20,  # เพิ่มข้อมูลขั้นต่ำใน leaf
        'min_gain_to_split': 0.1, # เพิ่ม threshold สำหรับการแบ่ง
        
        # Learning
        'learning_rate': 0.05,   # ลด learning rate
        'feature_fraction': 0.8, # ใช้ feature subset
        'bagging_fraction': 0.8, # ใช้ data subset
        'bagging_freq': 5,       # frequency ของ bagging
        
        # Early stopping
        'n_estimators': 2000,    # เพิ่มจำนวน estimators
        'early_stopping_rounds': 100, # early stopping
        
        # Other
        'objective': 'binary',
        'metric': 'auc',
        'boosting_type': 'gbdt',
        'verbose': -1,
        'random_state': 42
    }

def get_conservative_params():
    """พารามิเตอร์แบบ conservative มากขึ้น"""
    return {
        'reg_alpha': 0.3,
        'reg_lambda': 0.3,
        'max_depth': 3,
        'num_leaves': 6,
        'min_data_in_leaf': 30,
        'min_gain_to_split': 0.2,
        'learning_rate': 0.03,
        'feature_fraction': 0.7,
        'bagging_fraction': 0.7,
        'bagging_freq': 3,
        'n_estimators': 3000,
        'early_stopping_rounds': 150,
        'objective': 'binary',
        'metric': 'auc',
        'boosting_type': 'gbdt',
        'verbose': -1,
        'random_state': 42
    }

def improved_cross_validation(X, y, params, cv_folds=5):
    """Cross-validation ที่ปรับปรุงแล้ว"""
    print(f"🔄 รัน Cross-Validation ({cv_folds} folds)")
    
    # ใช้ TimeSeriesSplit สำหรับข้อมูล time series
    tscv = TimeSeriesSplit(n_splits=cv_folds)
    
    cv_scores = {
        'train_auc': [],
        'val_auc': [],
        'train_acc': [],
        'val_acc': [],
        'train_f1': [],
        'val_f1': [],
        'n_estimators': []
    }
    
    for fold, (train_idx, val_idx) in enumerate(tscv.split(X), 1):
        print(f"  Fold {fold}/{cv_folds}...")
        
        # แบ่งข้อมูล
        X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
        y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
        
        # Scale ข้อมูล
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_val_scaled = scaler.transform(X_val)
        
        # สร้างและฝึกโมเดล
        model = lgb.LGBMClassifier(**params)
        
        model.fit(
            X_train_scaled, y_train,
            eval_set=[(X_train_scaled, y_train), (X_val_scaled, y_val)],
            eval_names=['train', 'val'],
            eval_metric='auc',
            callbacks=[
                lgb.early_stopping(stopping_rounds=params.get('early_stopping_rounds', 100), verbose=False),
                lgb.log_evaluation(0)
            ]
        )
        
        # ทำนายและประเมินผล
        train_pred_proba = model.predict_proba(X_train_scaled)[:, 1]
        val_pred_proba = model.predict_proba(X_val_scaled)[:, 1]
        
        train_pred = (train_pred_proba > 0.5).astype(int)
        val_pred = (val_pred_proba > 0.5).astype(int)
        
        # คำนวณ metrics
        cv_scores['train_auc'].append(roc_auc_score(y_train, train_pred_proba))
        cv_scores['val_auc'].append(roc_auc_score(y_val, val_pred_proba))
        cv_scores['train_acc'].append(accuracy_score(y_train, train_pred))
        cv_scores['val_acc'].append(accuracy_score(y_val, val_pred))
        cv_scores['train_f1'].append(f1_score(y_train, train_pred))
        cv_scores['val_f1'].append(f1_score(y_val, val_pred))
        cv_scores['n_estimators'].append(model.n_estimators_)
    
    # คำนวณค่าเฉลี่ย
    results = {}
    for metric in ['train_auc', 'val_auc', 'train_acc', 'val_acc', 'train_f1', 'val_f1']:
        results[f'{metric}_mean'] = np.mean(cv_scores[metric])
        results[f'{metric}_std'] = np.std(cv_scores[metric])
    
    results['avg_n_estimators'] = np.mean(cv_scores['n_estimators'])
    results['overfitting_gap'] = results['train_auc_mean'] - results['val_auc_mean']
    
    return results

def test_overfitting_reduction():
    """ทดสอบการลด overfitting"""
    print("🧪 ทดสอบการลด Overfitting")
    print("="*80)
    
    # สร้างข้อมูลตัวอย่าง (จำลอง)
    np.random.seed(42)
    n_samples = 1000
    n_features = 50
    
    # สร้าง features
    X = pd.DataFrame(np.random.randn(n_samples, n_features), 
                     columns=[f'feature_{i}' for i in range(n_features)])
    
    # สร้าง target ที่มีความสัมพันธ์กับบาง features
    important_features = X.iloc[:, :5].sum(axis=1)
    noise = np.random.randn(n_samples) * 0.5
    y = pd.Series((important_features + noise > 0).astype(int))
    
    print(f"📊 ข้อมูลทดสอบ: {n_samples} samples, {n_features} features")
    print(f"📈 Target distribution: {y.value_counts().to_dict()}")
    
    # ทดสอบพารามิเตอร์ต่างๆ
    param_sets = {
        "Original (Overfitting)": {
            'max_depth': 8,
            'num_leaves': 31,
            'min_data_in_leaf': 5,
            'learning_rate': 0.1,
            'n_estimators': 1000,
            'reg_alpha': 0,
            'reg_lambda': 0,
            'objective': 'binary',
            'metric': 'auc',
            'verbose': -1,
            'random_state': 42
        },
        "Anti-Overfitting": get_anti_overfitting_params(),
        "Conservative": get_conservative_params()
    }
    
    results_comparison = {}
    
    for name, params in param_sets.items():
        print(f"\n🔬 ทดสอบ: {name}")
        print("-" * 40)
        
        results = improved_cross_validation(X, y, params, cv_folds=3)
        results_comparison[name] = results
        
        print(f"  Train AUC: {results['train_auc_mean']:.4f} ± {results['train_auc_std']:.4f}")
        print(f"  Val AUC:   {results['val_auc_mean']:.4f} ± {results['val_auc_std']:.4f}")
        print(f"  Overfitting Gap: {results['overfitting_gap']:.4f}")
        print(f"  Avg Estimators: {results['avg_n_estimators']:.0f}")
    
    # สรุปผลการเปรียบเทียบ
    print(f"\n📊 สรุปการเปรียบเทียบ")
    print("="*80)
    
    comparison_df = pd.DataFrame({
        name: {
            'Val_AUC': results['val_auc_mean'],
            'Overfitting_Gap': results['overfitting_gap'],
            'Val_F1': results['val_f1_mean'],
            'Estimators': results['avg_n_estimators']
        }
        for name, results in results_comparison.items()
    }).round(4)
    
    print(comparison_df.T)
    
    # หาพารามิเตอร์ที่ดีที่สุด
    best_config = min(results_comparison.items(), 
                     key=lambda x: x[1]['overfitting_gap'])
    
    print(f"\n🏆 การตั้งค่าที่ดีที่สุด: {best_config[0]}")
    print(f"  Overfitting Gap: {best_config[1]['overfitting_gap']:.4f}")
    print(f"  Validation AUC: {best_config[1]['val_auc_mean']:.4f}")
    
    return best_config[0], param_sets[best_config[0]]

def generate_updated_params():
    """สร้างพารามิเตอร์ที่ปรับปรุงแล้ว"""
    print(f"\n🔧 พารามิเตอร์ที่แนะนำสำหรับ python_LightGBM_15_Tuning.py")
    print("="*80)
    
    # พารามิเตอร์ที่แนะนำ
    recommended_params = {
        'learning_rate': [0.03, 0.05, 0.08],  # ลดลง
        'num_leaves': [6, 8, 10],              # ลดลง
        'max_depth': [3, 4, 5],                # ลดลง
        'min_data_in_leaf': [20, 25, 30],      # เพิ่มขึ้น
        'reg_alpha': [0.1, 0.2, 0.3],          # เพิ่ม regularization
        'reg_lambda': [0.1, 0.2, 0.3],         # เพิ่ม regularization
        'feature_fraction': [0.7, 0.8, 0.9],   # feature sampling
        'bagging_fraction': [0.7, 0.8, 0.9],   # data sampling
    }
    
    # พารามิเตอร์เริ่มต้นใหม่
    new_defaults = {
        'learning_rate': 0.05,
        'num_leaves': 8,
        'max_depth': 4,
        'min_data_in_leaf': 25,
        'reg_alpha': 0.2,
        'reg_lambda': 0.2,
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'early_stopping_rounds': 150
    }
    
    print("📝 param_dist ใหม่:")
    for param, values in recommended_params.items():
        print(f"  '{param}': {values},")
    
    print(f"\n📝 Default parameters ใหม่:")
    for param, value in new_defaults.items():
        print(f"  {param}={value}")
    
    return recommended_params, new_defaults

def main():
    """ฟังก์ชันหลัก"""
    print("🎯 ลด Overfitting ในโมเดล LightGBM Trading")
    print("="*80)
    
    # 1. ทดสอบการลด overfitting
    best_config_name, best_params = test_overfitting_reduction()
    
    # 2. สร้างพารามิเตอร์ที่แนะนำ
    recommended_params, new_defaults = generate_updated_params()
    
    # 3. คำแนะนำการใช้งาน
    print(f"\n🚀 คำแนะนำการใช้งาน:")
    print("1. อัปเดต param_dist ใน python_LightGBM_15_Tuning.py")
    print("2. อัปเดต default parameters ในฟังก์ชัน get_lgbm_params()")
    print("3. เพิ่ม early_stopping_rounds = 150")
    print("4. รัน hyperparameter tuning ใหม่")
    print("5. ตรวจสอบ overfitting gap (ควร < 0.05)")
    
    print(f"\n📊 เป้าหมาย:")
    print("  • Overfitting Gap: < 0.05")
    print("  • Validation AUC: > 0.85")
    print("  • Model Stability: CV < 15%")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการเทรน Multi-class LightGBM ด้วยไฟล์ที่แก้ไขแล้ว
"""

import os
import sys
import subprocess
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_single_symbol_training():
    """
    ทดสอบการเทรนด้วย symbol เดียว
    """
    print("🧪 ทดสอบการเทรน Multi-class LightGBM ด้วยไฟล์ที่แก้ไขแล้ว")
    print("=" * 80)
    
    # ตั้งค่าการทดสอบ
    test_symbol = "GBPUSD"
    test_timeframe = "M30"
    test_file = f"CSV_Files_Fixed/{test_symbol}_{test_timeframe}_FIXED.csv"
    
    print(f"📋 การตั้งค่าการทดสอบ:")
    print(f"  Symbol: {test_symbol}")
    print(f"  Timeframe: {test_timeframe}")
    print(f"  File: {test_file}")
    
    # ตรวจสอบไฟล์
    if not os.path.exists(test_file):
        print(f"❌ ไม่พบไฟล์: {test_file}")
        return False
    
    file_size = os.path.getsize(test_file)
    print(f"  File Size: {file_size:,} bytes")
    
    # สร้างสคริปต์ทดสอบชั่วคราว
    test_script_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
สคริปต์ทดสอบการเทรนชั่วคราว
"""

import sys
sys.path.append('.')

from python_LightGBM_15_Tuning import main

# ตั้งค่าการทดสอบ
test_files = ["{test_file}"]

print("🚀 เริ่มการทดสอบการเทรน")
print("=" * 60)

try:
    # เรียกใช้ main function
    main(run_identifier="test_fixed", group_name="test_fixed", input_files=test_files)
    print("✅ การทดสอบเสร็จสิ้น")
    
except Exception as e:
    print(f"❌ เกิดข้อผิดพลาด: {{str(e)}}")
    import traceback
    traceback.print_exc()
'''
    
    # บันทึกสคริปต์ทดสอบ
    test_script_path = "temp_test_training.py"
    with open(test_script_path, 'w', encoding='utf-8') as f:
        f.write(test_script_content)
    
    print(f"\n🚀 เริ่มการทดสอบการเทรน...")
    print(f"⏰ เริ่มเมื่อ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # รันการทดสอบ
        result = subprocess.run([
            sys.executable, test_script_path
        ], capture_output=True, text=True, timeout=300)  # 5 นาที timeout
        
        print(f"\n📊 ผลการทดสอบ:")
        print(f"Return Code: {result.returncode}")
        
        if result.stdout:
            print(f"\n📝 Output:")
            print(result.stdout)
        
        if result.stderr:
            print(f"\n⚠️ Errors:")
            print(result.stderr)
        
        # ลบสคริปต์ทดสอบ
        if os.path.exists(test_script_path):
            os.remove(test_script_path)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print(f"⏰ การทดสอบใช้เวลานานเกิน 5 นาที")
        return False
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการรัน: {str(e)}")
        return False

def check_generated_files():
    """
    ตรวจสอบไฟล์ที่ถูกสร้างขึ้น
    """
    print("\n🔍 ตรวจสอบไฟล์ที่ถูกสร้างขึ้น")
    print("=" * 60)
    
    results_folder = "Test_LightGBM/results"
    
    if not os.path.exists(results_folder):
        print(f"❌ ไม่พบโฟลเดอร์: {results_folder}")
        return
    
    # หาไฟล์ .txt ที่เกี่ยวข้องกับการทดสอบ
    recent_files = []
    
    for root, dirs, files in os.walk(results_folder):
        for file in files:
            if file.endswith('.txt'):
                file_path = os.path.join(root, file)
                mod_time = os.path.getmtime(file_path)
                recent_files.append((file_path, mod_time))
    
    # เรียงตามเวลาล่าสุด
    recent_files.sort(key=lambda x: x[1], reverse=True)
    
    print(f"📁 ไฟล์ล่าสุด (5 ไฟล์แรก):")
    for i, (file_path, mod_time) in enumerate(recent_files[:5]):
        mod_time_str = datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S')
        file_size = os.path.getsize(file_path)
        print(f"  {i+1}. {os.path.basename(file_path)}")
        print(f"     แก้ไขล่าสุด: {mod_time_str}")
        print(f"     ขนาด: {file_size} bytes")

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🧪 ทดสอบการแก้ไข Multi-class LightGBM Training Issues")
    print("=" * 80)
    
    # ขั้นตอนที่ 1: ทดสอบการเทรน
    print("\n📋 ขั้นตอนที่ 1: ทดสอบการเทรน")
    success = test_single_symbol_training()
    
    # ขั้นตอนที่ 2: ตรวจสอบไฟล์ที่สร้างขึ้น
    print("\n📋 ขั้นตอนที่ 2: ตรวจสอบไฟล์ที่สร้างขึ้น")
    check_generated_files()
    
    # สรุปผล
    print("\n🏁 สรุปผลการทดสอบ")
    print("=" * 80)
    
    if success:
        print("✅ การทดสอบสำเร็จ!")
        print("💡 การแก้ไขทำงานได้ถูกต้อง:")
        print("  1. ✅ แก้ไข LightGBM configuration error")
        print("  2. ✅ แก้ไข variable scope error")
        print("  3. ✅ เพิ่ม fallback mechanisms")
        print("  4. ✅ ระบบบันทึกไฟล์ทำงานได้")
        
        print("\n🚀 พร้อมสำหรับการเทรนแบบเต็มรูปแบบ:")
        print("  - สามารถเทรนได้ทั้ง Multi-class และ Binary")
        print("  - มี fallback เมื่อข้อมูลไม่เพียงพอ")
        print("  - มี error handling ที่ดี")
        print("  - ไฟล์สรุปการเทรดถูกสร้างอัตโนมัติ")
        
    else:
        print("❌ การทดสอบล้มเหลว")
        print("💡 ต้องตรวจสอบเพิ่มเติม:")
        print("  1. ตรวจสอบ error messages")
        print("  2. ตรวจสอบการตั้งค่า parameters")
        print("  3. ตรวจสอบข้อมูล input")

if __name__ == "__main__":
    main()

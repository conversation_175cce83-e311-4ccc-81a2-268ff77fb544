#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข SELL Entry Function
"""

import os
import sys
import pandas as pd
import numpy as np

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_entry_conditions():
    """ทดสอบ entry conditions สำหรับ sell"""
    
    print("🧪 ทดสอบ Entry Conditions สำหรับ Sell")
    print("="*60)
    
    try:
        from python_LightGBM_16_Signal import entry_conditions
        
        print(f"📊 Entry Conditions ที่มีอยู่:")
        for name, funcs in entry_conditions.items():
            print(f"   {name}:")
            print(f"      buy: {funcs['buy']}")
            print(f"      sell: {funcs['sell']}")
        
        # ทดสอบ trend_following_sell
        if 'trend_following_sell' in entry_conditions:
            print(f"\n🎯 ทดสอบ trend_following_sell:")
            
            # สร้างข้อมูลทดสอบที่ควรให้ sell signal
            test_data = {
                'close': 1.0500,
                'open': 1.0520,  # close < open ✅
                'ema200': 1.0550,  # close < ema200 ✅
                'rsi14': 75,     # > 70 ✅
                'macd_signal': -1.0,  # bearish ✅
                'volume': 1200,
                'volume_ma20': 1000,  # volume > volume_ma20 * 0.8 ✅
                'pullback_sell': 0.45,  # > 0.40 ✅
                'ratio_sell': 8.0,     # > 7.5 ✅
            }
            
            sell_func = entry_conditions['trend_following_sell']['sell']
            result = sell_func(test_data)
            
            print(f"   📊 Test data: {test_data}")
            print(f"   🎯 Sell result: {result}")
            
            if result:
                print(f"   ✅ trend_following_sell ทำงานถูกต้อง!")
            else:
                print(f"   ❌ trend_following_sell ไม่ทำงาน - ตรวจสอบเงื่อนไข")
                
                # ตรวจสอบแต่ละเงื่อนไข
                cond1 = test_data['close'] < test_data['open']
                cond2 = test_data['close'] < test_data['ema200']
                cond3 = test_data['rsi14'] > 70
                cond4 = test_data['macd_signal'] == -1.0
                cond5 = test_data['volume'] > test_data['volume_ma20'] * 0.8
                cond6 = test_data['pullback_sell'] > 0.40
                cond7 = test_data['ratio_sell'] > 7.5
                
                print(f"      Close < Open: {cond1}")
                print(f"      Close < EMA200: {cond2}")
                print(f"      RSI > 70: {cond3}")
                print(f"      MACD Bearish: {cond4}")
                print(f"      Volume OK: {cond5}")
                print(f"      PullBack > 0.4: {cond6}")
                print(f"      Ratio > 7.5: {cond7}")
        else:
            print(f"❌ ไม่พบ trend_following_sell!")
        
        # ทดสอบ trend_following_buy (ที่มีปัญหา)
        if 'trend_following_buy' in entry_conditions:
            print(f"\n🎯 ทดสอบ trend_following_buy (ที่มีปัญหา):")
            
            sell_func = entry_conditions['trend_following_buy']['sell']
            result = sell_func({})
            
            print(f"   🎯 Sell result: {result}")
            print(f"   📝 Note: ควรเป็น False เสมอ (ตามที่ออกแบบ)")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def test_quick_run():
    """รันการทดสอบแบบเร็วเพื่อดู debug messages ใหม่"""
    
    print(f"\n🧪 รันการทดสอบแบบเร็วเพื่อดู Debug Messages ใหม่")
    print("="*70)
    
    try:
        from python_LightGBM_16_Signal import test_groups
        
        if 'M30' in test_groups and test_groups['M30']:
            test_file = test_groups['M30'][0]
            
            if os.path.exists(test_file):
                print(f"📁 ไฟล์ทดสอบ: {test_file}")
                
                # รันแค่ส่วนเล็กๆ
                from python_LightGBM_16_Signal import load_and_process_data
                
                print(f"🚀 กำลังโหลดข้อมูล...")
                
                result = load_and_process_data(
                    file=test_file,
                    modelname="test_model",
                    symbol="AUDUSD",
                    timeframe=30,
                    identifier=1,
                    model=None,
                    scaler=None,
                    nBars_SL=20,
                    confidence_threshold=0.5
                )
                
                if result and len(result) == 6:
                    train_data, val_data, test_data, df, trade_df, stats = result
                    print(f"✅ โหลดข้อมูลสำเร็จ: {df.shape if df is not None else 'None'}")
                    
                    if trade_df is not None and len(trade_df) > 0:
                        # ตรวจสอบ sell signals
                        sell_trades = trade_df[trade_df['Trade Type'] == 'Sell']
                        buy_trades = trade_df[trade_df['Trade Type'] == 'Buy']
                        
                        print(f"📊 ผลลัพธ์การเทรด:")
                        print(f"   📈 BUY trades: {len(buy_trades)}")
                        print(f"   📉 SELL trades: {len(sell_trades)}")
                        
                        if len(sell_trades) > 0:
                            print(f"   ✅ มี SELL signals แล้ว!")
                            print(f"   📄 ตัวอย่าง SELL trades:")
                            print(sell_trades[['Entry Date', 'Entry Time', 'Entry Price', 'Exit Condition']].head().to_string())
                        else:
                            print(f"   ❌ ยังไม่มี SELL signals")
                    else:
                        print(f"❌ ไม่มีข้อมูล trade_df")
                else:
                    print(f"❌ ไม่สามารถโหลดข้อมูลได้")
            else:
                print(f"❌ ไม่พบไฟล์: {test_file}")
        else:
            print(f"❌ ไม่พบกลุ่ม M30")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def main():
    """ฟังก์ชันหลัก"""
    
    print("🏗️ Test SELL Entry Function Fix")
    print("="*60)
    
    # ทดสอบ entry conditions
    test_entry_conditions()
    
    # รันการทดสอบแบบเร็ว
    test_quick_run()
    
    print(f"\n✅ การทดสอบเสร็จสิ้น")
    print(f"\n📋 สรุปการแก้ไข:")
    print(f"   ✅ เปลี่ยนจาก entry_condition_func['sell'] เป็น trend_following_sell")
    print(f"   ✅ trend_following_sell มีเงื่อนไขที่เหมาะสมสำหรับ sell")
    print(f"   ✅ เพิ่ม debug messages เพื่อแสดงฟังก์ชันที่ใช้")
    
    print(f"\n🚀 ขั้นตอนต่อไป:")
    print(f"   1. รัน python_LightGBM_16_Signal.py")
    print(f"   2. ดู console messages: 'SELL Entry Function (trend_following_sell)'")
    print(f"   3. ตรวจสอบว่ามี SELL signals ในไฟล์ trade_log")

if __name__ == "__main__":
    main()

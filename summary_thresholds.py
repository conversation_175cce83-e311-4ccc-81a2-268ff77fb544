#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
สรุปเงื่อนไขและพารามิเตอร์ Multi-Model Architecture
รวบรวมข้อมูล threshold, nBars_SL และ time_filters แยกตาม timeframe และ symbol

โครงสร้างไฟล์ที่ประมวลผล:
LightGBM_Multi/thresholds/
├─ {timeframe}_{symbol}_counter_trend_optimal_nBars_SL.pkl
├─ {timeframe}_{symbol}_counter_trend_optimal_threshold.pkl
├─ {timeframe}_{symbol}_trend_following_optimal_nBars_SL.pkl
├─ {timeframe}_{symbol}_trend_following_optimal_threshold.pkl
└─ {timeframe}_{symbol}_time_filters.pkl

การใช้งาน:
    python summary_thresholds.py
"""

import os
import sys
import pickle
import datetime
import json
from pathlib import Path

# กำหนดโฟลเดอร์ที่เก็บไฟล์
THRESHOLDS_DIR = "LightGBM_Multi/thresholds"
OUTPUT_DIR = "LightGBM_Multi/summaries"

def load_pickle_file(file_path, default_value=None):
    """โหลดไฟล์ pickle อย่างปลอดภัย"""
    try:
        if os.path.exists(file_path):
            with open(file_path, 'rb') as f:
                return pickle.load(f)
        else:
            return default_value
    except Exception as e:
        print(f"⚠️ ไม่สามารถโหลดไฟล์ {file_path}: {e}")
        return default_value

def format_days_display(days_list):
    """แปลงรายการวันเป็นข้อความที่อ่านง่าย"""
    if not days_list:
        return "ไม่มีวัน"
    
    if len(days_list) == 7 and set(days_list) == set(range(7)):
        return "ทุกวัน"
    
    day_names = ['จันทร์', 'อังคาร', 'พุธ', 'พฤหัสบดี', 'ศุกร์', 'เสาร์', 'อาทิตย์']
    return ', '.join([day_names[d] for d in sorted(days_list)])

def format_hours_display(hours_list):
    """แปลงรายการชั่วโมงเป็นข้อความที่อ่านง่าย"""
    if not hours_list:
        return "ไม่มีชั่วโมง"
    
    if len(hours_list) == 24 and set(hours_list) == set(range(24)):
        return "ตลอด 24 ชั่วโมง"
    
    # จัดกลุ่มชั่วโมงที่ต่อเนื่องกัน
    sorted_hours = sorted(hours_list)
    ranges = []
    start = sorted_hours[0]
    end = start
    
    for i in range(1, len(sorted_hours)):
        if sorted_hours[i] == end + 1:
            end = sorted_hours[i]
        else:
            if start == end:
                ranges.append(f"{start:02d}:00")
            else:
                ranges.append(f"{start:02d}:00-{end:02d}:59")
            start = sorted_hours[i]
            end = start
    
    # เพิ่มช่วงสุดท้าย
    if start == end:
        ranges.append(f"{start:02d}:00")
    else:
        ranges.append(f"{start:02d}:00-{end:02d}:59")
    
    return ', '.join(ranges)

def discover_symbol_timeframe_combinations():
    """ค้นหาการผสมผสานของ symbol และ timeframe จากไฟล์ที่มีอยู่"""
    
    if not os.path.exists(THRESHOLDS_DIR):
        print(f"❌ ไม่พบโฟลเดอร์: {THRESHOLDS_DIR}")
        return []
    
    combinations = set()
    
    # ค้นหาจากไฟล์ time_filters
    for file in os.listdir(THRESHOLDS_DIR):
        if file.endswith('_time_filters.pkl'):
            # รูปแบบ: {timeframe}_{symbol}_time_filters.pkl
            parts = file.replace('_time_filters.pkl', '').split('_')
            if len(parts) >= 2:
                timeframe = parts[0]
                symbol = '_'.join(parts[1:])  # รองรับ symbol ที่มี underscore
                combinations.add((timeframe, symbol))
    
    return sorted(list(combinations), key=lambda x: (int(x[0]), x[1]))

def load_parameters_for_symbol_timeframe(timeframe, symbol):
    """โหลดพารามิเตอร์ทั้งหมดสำหรับ symbol และ timeframe ที่กำหนด"""
    
    base_path = f"{THRESHOLDS_DIR}/{timeframe}_{symbol}"
    
    # โหลด time_filters
    time_filters_path = f"{base_path}_time_filters.pkl"
    time_filters = load_pickle_file(time_filters_path, {'days': [], 'hours': []})
    
    # โหลดพารามิเตอร์ trend_following
    tf_threshold_path = f"{base_path}_trend_following_optimal_threshold.pkl"
    tf_nbars_path = f"{base_path}_trend_following_optimal_nBars_SL.pkl"
    
    tf_threshold = load_pickle_file(tf_threshold_path)
    tf_nbars = load_pickle_file(tf_nbars_path)
    
    # โหลดพารามิเตอร์ counter_trend
    ct_threshold_path = f"{base_path}_counter_trend_optimal_threshold.pkl"
    ct_nbars_path = f"{base_path}_counter_trend_optimal_nBars_SL.pkl"
    
    ct_threshold = load_pickle_file(ct_threshold_path)
    ct_nbars = load_pickle_file(ct_nbars_path)
    
    return {
        'timeframe': timeframe,
        'symbol': symbol,
        'time_filters': time_filters,
        'trend_following': {
            'threshold': tf_threshold,
            'nBars_SL': tf_nbars
        },
        'counter_trend': {
            'threshold': ct_threshold,
            'nBars_SL': ct_nbars
        }
    }

def generate_summary_report():
    """สร้างรายงานสรุปพารามิเตอร์ทั้งหมด"""
    
    print("🚀 เริ่มการสรุปพารามิเตอร์ Multi-Model Architecture")
    print("=" * 80)
    
    # ค้นหาการผสมผสานที่มีอยู่
    combinations = discover_symbol_timeframe_combinations()
    
    if not combinations:
        print("❌ ไม่พบไฟล์พารามิเตอร์ใดๆ")
        return
    
    print(f"📊 พบข้อมูล {len(combinations)} รายการ")
    
    # สร้างโฟลเดอร์ output
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # รวบรวมข้อมูลทั้งหมด
    all_data = {}
    timeframes = {}
    
    for timeframe, symbol in combinations:
        print(f"📋 ประมวลผล {symbol} M{timeframe}...")
        
        data = load_parameters_for_symbol_timeframe(timeframe, symbol)
        key = f"{timeframe}_{symbol}"
        all_data[key] = data
        
        # จัดกลุ่มตาม timeframe
        if timeframe not in timeframes:
            timeframes[timeframe] = []
        timeframes[timeframe].append(data)
    
    # สร้างรายงานหลัก
    create_main_summary_report(all_data, timeframes)
    
    # สร้างรายงานแยกตาม timeframe
    create_timeframe_reports(timeframes)
    
    # สร้างรายงานแยกตาม symbol
    create_symbol_reports(all_data)
    
    # บันทึกข้อมูล JSON
    save_json_data(all_data)
    
    print(f"\n✅ สร้างรายงานเสร็จสิ้น!")
    print(f"📁 ไฟล์ถูกบันทึกใน: {OUTPUT_DIR}")

def create_main_summary_report(all_data, timeframes):
    """สร้างรายงานสรุปหลัก"""
    
    report_path = os.path.join(OUTPUT_DIR, "thresholds_summary_main.txt")
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("📊 สรุปพารามิเตอร์ Multi-Model Architecture\n")
        f.write("=" * 80 + "\n")
        f.write(f"วันที่สร้างรายงาน: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"จำนวนรายการทั้งหมด: {len(all_data)}\n\n")
        
        # สรุปตาม timeframe
        for timeframe in sorted(timeframes.keys(), key=lambda x: int(x)):
            f.write(f"\n🕐 Timeframe M{timeframe}:\n")
            f.write("=" * 60 + "\n")
            
            symbols_data = timeframes[timeframe]
            symbols_data.sort(key=lambda x: x['symbol'])
            
            for data in symbols_data:
                write_symbol_summary(f, data)
    
    print(f"💾 บันทึกรายงานหลัก: {report_path}")

def write_symbol_summary(f, data):
    """เขียนสรุปข้อมูลของ symbol"""
    
    symbol = data['symbol']
    f.write(f"\n💰 {symbol}:\n")
    
    # Time Filters
    if data['time_filters']:
        days_str = format_days_display(data['time_filters'].get('days', []))
        hours_str = format_hours_display(data['time_filters'].get('hours', []))
        f.write(f"   ⏰ Time Filters: {days_str}, {hours_str}\n")
    else:
        f.write(f"   ⏰ Time Filters: ไม่พบข้อมูล\n")
    
    # Trend Following
    tf_data = data['trend_following']
    if tf_data['threshold'] is not None and tf_data['nBars_SL'] is not None:
        f.write(f"   📈 Trend Following: Threshold={tf_data['threshold']:.4f}, nBars_SL={tf_data['nBars_SL']}\n")
    else:
        f.write(f"   📈 Trend Following: ไม่พบข้อมูล\n")
    
    # Counter Trend
    ct_data = data['counter_trend']
    if ct_data['threshold'] is not None and ct_data['nBars_SL'] is not None:
        f.write(f"   📉 Counter Trend: Threshold={ct_data['threshold']:.4f}, nBars_SL={ct_data['nBars_SL']}\n")
    else:
        f.write(f"   📉 Counter Trend: ไม่พบข้อมูล\n")

def create_timeframe_reports(timeframes):
    """สร้างรายงานแยกตาม timeframe"""

    for timeframe in timeframes.keys():
        report_path = os.path.join(OUTPUT_DIR, f"thresholds_summary_M{timeframe}.txt")

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(f"📊 สรุปพารามิเตอร์ Multi-Model Architecture - Timeframe M{timeframe}\n")
            f.write("=" * 80 + "\n")
            f.write(f"วันที่สร้างรายงาน: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            symbols_data = timeframes[timeframe]
            symbols_data.sort(key=lambda x: x['symbol'])

            for data in symbols_data:
                symbol = data['symbol']
                f.write(f"\n💰 {symbol}:\n")
                f.write("-" * 40 + "\n")

                # Time Filters
                if data['time_filters']:
                    days_str = format_days_display(data['time_filters'].get('days', []))
                    hours_str = format_hours_display(data['time_filters'].get('hours', []))
                    f.write(f"⏰ Time Filters:\n")
                    f.write(f"   วัน: {days_str}\n")
                    f.write(f"   เวลา: {hours_str}\n")
                else:
                    f.write(f"⏰ Time Filters: ไม่พบข้อมูล\n")

                # Trend Following
                f.write(f"\n📈 Trend Following Model:\n")
                tf_data = data['trend_following']
                if tf_data['threshold'] is not None and tf_data['nBars_SL'] is not None:
                    f.write(f"   Threshold: {tf_data['threshold']:.4f}\n")
                    f.write(f"   nBars_SL: {tf_data['nBars_SL']}\n")
                else:
                    f.write(f"   ไม่พบข้อมูล\n")

                # Counter Trend
                f.write(f"\n📉 Counter Trend Model:\n")
                ct_data = data['counter_trend']
                if ct_data['threshold'] is not None and ct_data['nBars_SL'] is not None:
                    f.write(f"   Threshold: {ct_data['threshold']:.4f}\n")
                    f.write(f"   nBars_SL: {ct_data['nBars_SL']}\n")
                else:
                    f.write(f"   ไม่พบข้อมูล\n")

                f.write("\n")

        print(f"💾 บันทึกรายงาน M{timeframe}: {report_path}")

def create_symbol_reports(all_data):
    """สร้างรายงานแยกตาม symbol"""

    # จัดกลุ่มตาม symbol
    symbols = {}
    for key, data in all_data.items():
        symbol = data['symbol']
        if symbol not in symbols:
            symbols[symbol] = []
        symbols[symbol].append(data)

    for symbol in sorted(symbols.keys()):
        report_path = os.path.join(OUTPUT_DIR, f"thresholds_summary_{symbol}.txt")

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(f"📊 สรุปพารามิเตอร์ Multi-Model Architecture - {symbol}\n")
            f.write("=" * 80 + "\n")
            f.write(f"วันที่สร้างรายงาน: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            timeframes_data = symbols[symbol]
            timeframes_data.sort(key=lambda x: int(x['timeframe']))

            for data in timeframes_data:
                timeframe = data['timeframe']
                f.write(f"\n🕐 Timeframe M{timeframe}:\n")
                f.write("-" * 40 + "\n")

                # Time Filters
                if data['time_filters']:
                    days_str = format_days_display(data['time_filters'].get('days', []))
                    hours_str = format_hours_display(data['time_filters'].get('hours', []))
                    f.write(f"⏰ Time Filters:\n")
                    f.write(f"   วัน: {days_str}\n")
                    f.write(f"   เวลา: {hours_str}\n")
                else:
                    f.write(f"⏰ Time Filters: ไม่พบข้อมูล\n")

                # Trend Following
                f.write(f"\n📈 Trend Following Model:\n")
                tf_data = data['trend_following']
                if tf_data['threshold'] is not None and tf_data['nBars_SL'] is not None:
                    f.write(f"   Threshold: {tf_data['threshold']:.4f}\n")
                    f.write(f"   nBars_SL: {tf_data['nBars_SL']}\n")
                else:
                    f.write(f"   ไม่พบข้อมูล\n")

                # Counter Trend
                f.write(f"\n📉 Counter Trend Model:\n")
                ct_data = data['counter_trend']
                if ct_data['threshold'] is not None and ct_data['nBars_SL'] is not None:
                    f.write(f"   Threshold: {ct_data['threshold']:.4f}\n")
                    f.write(f"   nBars_SL: {ct_data['nBars_SL']}\n")
                else:
                    f.write(f"   ไม่พบข้อมูล\n")

                f.write("\n")

        print(f"💾 บันทึกรายงาน {symbol}: {report_path}")

def save_json_data(all_data):
    """บันทึกข้อมูลเป็น JSON สำหรับการใช้งานโปรแกรม"""

    json_path = os.path.join(OUTPUT_DIR, "thresholds_parameters.json")

    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(all_data, f, ensure_ascii=False, indent=2)

    print(f"💾 บันทึกข้อมูล JSON: {json_path}")

def show_statistics(all_data):
    """แสดงสถิติสรุป"""

    print(f"\n📊 สถิติสรุป:")
    print("-" * 40)

    # นับ timeframes และ symbols
    timeframes = set()
    symbols = set()
    complete_entries = 0

    for data in all_data.values():
        timeframes.add(data['timeframe'])
        symbols.add(data['symbol'])

        # ตรวจสอบความสมบูรณ์ของข้อมูล
        has_time_filters = data['time_filters'] is not None
        has_trend_params = (data['trend_following']['threshold'] is not None and
                          data['trend_following']['nBars_SL'] is not None)
        has_counter_params = (data['counter_trend']['threshold'] is not None and
                            data['counter_trend']['nBars_SL'] is not None)

        if has_time_filters and has_trend_params and has_counter_params:
            complete_entries += 1

    print(f"📈 Timeframes: {len(timeframes)} ({', '.join(sorted(timeframes, key=lambda x: int(x)))})")
    print(f"💰 Symbols: {len(symbols)} ({', '.join(sorted(symbols))})")
    print(f"✅ รายการที่มีข้อมูลครบ: {complete_entries}/{len(all_data)}")

    # แสดงตำแหน่งไฟล์ที่สร้าง
    print(f"\n📁 ไฟล์ที่สร้าง:")
    print(f"   📄 thresholds_summary_main.txt - สรุปรวมทั้งหมด")
    print(f"   📄 thresholds_parameters.json - ข้อมูล JSON สำหรับโปรแกรม")

    for tf in sorted(timeframes, key=lambda x: int(x)):
        print(f"   📄 thresholds_summary_M{tf}.txt - สรุปเฉพาะ timeframe M{tf}")

    for symbol in sorted(symbols):
        print(f"   📄 thresholds_summary_{symbol}.txt - สรุปเฉพาะ {symbol}")

def main():
    """ฟังก์ชันหลัก"""

    try:
        # ตรวจสอบโฟลเดอร์
        if not os.path.exists(THRESHOLDS_DIR):
            print(f"❌ ไม่พบโฟลเดอร์: {THRESHOLDS_DIR}")
            print("💡 กรุณาตรวจสอบว่าได้เทรนโมเดลและหา optimal parameters แล้ว")
            return

        # สร้างรายงาน
        all_data = {}
        combinations = discover_symbol_timeframe_combinations()

        if not combinations:
            print("❌ ไม่พบไฟล์พารามิเตอร์ใดๆ")
            return

        print(f"📊 พบข้อมูล {len(combinations)} รายการ")

        # สร้างโฟลเดอร์ output
        os.makedirs(OUTPUT_DIR, exist_ok=True)

        # รวบรวมข้อมูลทั้งหมด
        timeframes = {}

        for timeframe, symbol in combinations:
            print(f"📋 ประมวลผล {symbol} M{timeframe}...")

            data = load_parameters_for_symbol_timeframe(timeframe, symbol)
            key = f"{timeframe}_{symbol}"
            all_data[key] = data

            # จัดกลุ่มตาม timeframe
            if timeframe not in timeframes:
                timeframes[timeframe] = []
            timeframes[timeframe].append(data)

        # สร้างรายงานทั้งหมด
        create_main_summary_report(all_data, timeframes)
        create_timeframe_reports(timeframes)
        create_symbol_reports(all_data)
        save_json_data(all_data)

        # แสดงสถิติ
        show_statistics(all_data)

        print(f"\n✅ สร้างรายงานเสร็จสิ้น!")
        print(f"📁 ไฟล์ถูกบันทึกใน: {OUTPUT_DIR}")

    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
ทดสอบระบบ Dual System (trend_following + counter_trend) ที่ปรับปรุงแล้ว
"""

import requests
import json
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>

def create_test_data():
    """สร้างข้อมูลทดสอบ"""
    # สร้างข้อมูล 220 แท่งสำหรับทดสอบ
    dates = pd.date_range(start='2024-01-01', periods=220, freq='30min')

    # สร้างข้อมูลราคาแบบ uptrend
    base_price = 2600.0
    trend = np.linspace(0, 50, 220)  # uptrend 50 points
    noise = np.random.normal(0, 2, 220)  # random noise

    prices = base_price + trend + noise

    data = []
    for i, date in enumerate(dates):
        # สร้างข้อมูล OHLCV
        open_price = prices[i]
        high_price = open_price + abs(np.random.normal(0, 1))
        low_price = open_price - abs(np.random.normal(0, 1))
        close_price = open_price + np.random.normal(0, 0.5)
        volume = np.random.randint(1000, 5000)

        # ใช้รูปแบบเดียวกับที่ MT5 ส่งมา (timestamp เป็น seconds)
        timestamp = int(date.timestamp())
        bar_data = {
            'time': timestamp,
            'open': round(open_price, 5),
            'high': round(high_price, 5),
            'low': round(low_price, 5),
            'close': round(close_price, 5),
            'tick_volume': volume,
            'spread': 25,
            'real_volume': volume
        }
        data.append(bar_data)

    return data

def test_dual_system():
    """ทดสอบระบบ Dual System"""
    print("🔄 Testing Dual System (Trend Following + Counter Trend)")
    print("=" * 60)
    
    # สร้างข้อมูลทดสอบ
    test_data = create_test_data()
    
    # ข้อมูลที่จะส่งไป server
    payload = {
        'symbol': 'GOLD',
        'timeframe_str': 'PERIOD_M30',
        'bars': test_data
    }
    
    try:
        # ส่งข้อมูลไป server
        print(f"📤 Sending {len(test_data)} bars to server...")
        response = requests.post(
            'http://127.0.0.1:54321/data',
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Server response received successfully!")
            print("-" * 60)
            
            # แสดงข้อมูลหลัก
            print(f"📊 Signal: {result.get('signal', 'N/A')}")
            print(f"📊 Class: {result.get('class', 'N/A')}")
            print(f"📊 Confidence: {result.get('confidence', 0):.4f}")
            print(f"📊 Market Condition: {result.get('market_condition', 'N/A')}")
            print(f"📊 Action Type: {result.get('action_type', 'N/A')}")
            print(f"📊 Scenario Used: {result.get('scenario_used', 'N/A')}")
            
            print("-" * 60)
            
            # แสดงข้อมูลทั้ง 2 ระบบ
            print("🎯 Dual System Parameters:")
            print(f"   Trend Following - Threshold: {result.get('trend_following_threshold', 0.5):.3f}, nBars: {result.get('trend_following_nbars', 6)}")
            print(f"                   - BUY: {result.get('trend_following_buy_confidence', 0.0):.4f}, SELL: {result.get('trend_following_sell_confidence', 0.0):.4f}")
            print(f"   Counter Trend   - Threshold: {result.get('counter_trend_threshold', 0.5):.3f}, nBars: {result.get('counter_trend_nbars', 6)}")
            print(f"                   - BUY: {result.get('counter_trend_buy_confidence', 0.0):.4f}, SELL: {result.get('counter_trend_sell_confidence', 0.0):.4f}")
            
            # แสดงข้อมูลการวิเคราะห์
            analysis_summary = result.get('analysis_summary', {})
            if analysis_summary:
                print("-" * 60)
                print("📈 Analysis Summary:")
                
                for scenario in ['trend_following', 'counter_trend']:
                    if scenario in analysis_summary:
                        print(f"   {scenario.replace('_', ' ').title()}:")
                        for action in ['buy', 'sell']:
                            if action in analysis_summary[scenario]:
                                data = analysis_summary[scenario][action]
                                should_trade = data.get('should_trade', False)
                                confidence = data.get('confidence', 0.0)
                                available = data.get('model_info', {}).get('available', False)
                                
                                status = "✅" if should_trade else "❌"
                                avail_status = "🟢" if available else "🔴"
                                print(f"     {action.upper()}: {status} {confidence:.4f} {avail_status}")
            
            print("-" * 60)
            print("💰 Trading Information:")
            print(f"   Entry Price: {result.get('entry_price', 0):.5f}")
            print(f"   SL Price: {result.get('sl_price', 0):.5f}")
            print(f"   TP Price: {result.get('tp_price', 0):.5f}")
            print(f"   Current Threshold: {result.get('threshold', 0.5):.3f}")
            print(f"   Current nBars_SL: {result.get('nBars_SL', 6)}")
            
            print("=" * 60)
            print("✅ Dual System Test Completed Successfully!")
            
            return True
            
        else:
            print(f"❌ Server error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Dual System Test")
    print(f"⏰ Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success = test_dual_system()
    
    print()
    if success:
        print("🎉 All tests passed! Dual System is working correctly.")
    else:
        print("💥 Test failed! Please check the server and try again.")

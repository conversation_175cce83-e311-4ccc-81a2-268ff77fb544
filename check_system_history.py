#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import glob
import pandas as pd
from datetime import datetime, timedelta
import re

class SystemHistoryChecker:
    def __init__(self):
        self.log_patterns = {
            'server': 'server_*.log',
            'monitor': 'system_monitor_*.txt',
            'mt5': 'MT5_Trading_Log_*.txt',
            'startup': 'system_startup.log'
        }
    
    def get_time_range_input(self):
        """รับช่วงเวลาที่ต้องการตรวจสอบ"""
        print("📅 Select time range to check:")
        print("1. Last 1 hour")
        print("2. Last 6 hours") 
        print("3. Last 24 hours")
        print("4. Last 3 days")
        print("5. Last week")
        print("6. Custom range")
        
        choice = input("\nEnter choice (1-6): ").strip()
        
        now = datetime.now()
        
        if choice == '1':
            return now - timedelta(hours=1), now, "Last 1 Hour"
        elif choice == '2':
            return now - timedelta(hours=6), now, "Last 6 Hours"
        elif choice == '3':
            return now - timedelta(hours=24), now, "Last 24 Hours"
        elif choice == '4':
            return now - timedelta(days=3), now, "Last 3 Days"
        elif choice == '5':
            return now - timedelta(weeks=1), now, "Last Week"
        elif choice == '6':
            try:
                start_str = input("Enter start time (YYYY-MM-DD HH:MM): ")
                end_str = input("Enter end time (YYYY-MM-DD HH:MM): ")
                start_time = datetime.strptime(start_str, '%Y-%m-%d %H:%M')
                end_time = datetime.strptime(end_str, '%Y-%m-%d %H:%M')
                return start_time, end_time, f"Custom ({start_str} to {end_str})"
            except:
                print("❌ Invalid date format, using last 24 hours")
                return now - timedelta(hours=24), now, "Last 24 Hours (Default)"
        else:
            return now - timedelta(hours=24), now, "Last 24 Hours (Default)"
    
    def analyze_server_logs(self, start_time, end_time):
        """วิเคราะห์ server logs"""
        print("\n🖥️  Analyzing Server Logs...")
        
        server_files = glob.glob('server_*.log')
        if not server_files:
            return {"error": "No server log files found"}
        
        analysis = {
            "requests_received": 0,
            "signals_generated": {"BUY": 0, "SELL": 0, "HOLD": 0, "ERROR": 0},
            "errors": 0,
            "processing_times": [],
            "symbols_processed": set(),
            "recent_errors": []
        }
        
        for log_file in server_files:
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        # Parse timestamp
                        timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                        if not timestamp_match:
                            continue
                        
                        try:
                            log_time = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S')
                            if not (start_time <= log_time <= end_time):
                                continue
                        except:
                            continue
                        
                        # Analyze content
                        if 'Received HTTP POST request' in line:
                            analysis["requests_received"] += 1
                        elif 'Signal:' in line and 'Confidence:' in line:
                            for signal in analysis["signals_generated"]:
                                if f'Signal: {signal}' in line:
                                    analysis["signals_generated"][signal] += 1
                                    break
                        elif 'ERROR' in line:
                            analysis["errors"] += 1
                            if len(analysis["recent_errors"]) < 5:
                                analysis["recent_errors"].append(line.strip())
                        
                        # Extract symbols
                        symbol_match = re.search(r'symbol[=\s]+([A-Z]+)', line)
                        if symbol_match:
                            analysis["symbols_processed"].add(symbol_match.group(1))
            except:
                continue
        
        analysis["symbols_processed"] = list(analysis["symbols_processed"])
        return analysis
    
    def analyze_mt5_logs(self, start_time, end_time):
        """วิเคราะห์ MT5 logs"""
        print("🔧 Analyzing MT5 Logs...")
        
        mt5_files = glob.glob('MT5_Trading_Log_*.txt')
        if not mt5_files:
            return {"error": "No MT5 log files found"}
        
        analysis = {
            "trades_executed": {"BUY": 0, "SELL": 0},
            "server_communications": 0,
            "system_events": 0,
            "errors": 0,
            "recent_trades": [],
            "recent_signals": []
        }
        
        # อ่านไฟล์ล่าสุด
        latest_file = max(mt5_files, key=os.path.getctime)
        
        try:
            with open(latest_file, 'r', encoding='utf-8') as f:
                for line in f:
                    # Parse timestamp
                    timestamp_match = re.search(r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]', line)
                    if not timestamp_match:
                        continue
                    
                    try:
                        log_time = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S')
                        if not (start_time <= log_time <= end_time):
                            continue
                    except:
                        continue
                    
                    # Analyze content
                    if 'TRADE_BUY' in line:
                        analysis["trades_executed"]["BUY"] += 1
                        if len(analysis["recent_trades"]) < 5:
                            analysis["recent_trades"].append(line.strip())
                    elif 'TRADE_SELL' in line:
                        analysis["trades_executed"]["SELL"] += 1
                        if len(analysis["recent_trades"]) < 5:
                            analysis["recent_trades"].append(line.strip())
                    elif 'SERVER_' in line:
                        analysis["server_communications"] += 1
                        if 'Signal=' in line and len(analysis["recent_signals"]) < 5:
                            analysis["recent_signals"].append(line.strip())
                    elif 'SYSTEM_' in line:
                        analysis["system_events"] += 1
                    elif 'ERROR' in line:
                        analysis["errors"] += 1
        except:
            pass
        
        return analysis
    
    def analyze_monitor_logs(self, start_time, end_time):
        """วิเคราะห์ monitor logs"""
        print("🔍 Analyzing Monitor Logs...")
        
        monitor_files = glob.glob('system_monitor_*.txt')
        if not monitor_files:
            return {"error": "No monitor log files found"}
        
        analysis = {
            "health_checks": 0,
            "server_errors": 0,
            "mt5_checks": 0,
            "system_restarts": 0,
            "uptime_records": []
        }
        
        for log_file in monitor_files:
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        if not line.strip():
                            continue
                        
                        try:
                            entry = json.loads(line.strip())
                            entry_time = datetime.fromisoformat(entry['timestamp'])
                            
                            if not (start_time <= entry_time <= end_time):
                                continue
                            
                            event_type = entry.get('type', '')
                            
                            if event_type == 'SERVER_CHECK':
                                analysis["health_checks"] += 1
                            elif event_type == 'SERVER_ERROR':
                                analysis["server_errors"] += 1
                            elif event_type == 'MT5_LOG_CHECK':
                                analysis["mt5_checks"] += 1
                            elif event_type == 'MONITOR_START':
                                analysis["system_restarts"] += 1
                        except:
                            continue
            except:
                continue
        
        return analysis
    
    def generate_history_report(self, start_time, end_time, time_desc):
        """สร้างรายงานประวัติ"""
        print(f"\n📊 SYSTEM HISTORY REPORT - {time_desc}")
        print("=" * 70)
        print(f"📅 Period: {start_time.strftime('%Y-%m-%d %H:%M')} to {end_time.strftime('%Y-%m-%d %H:%M')}")
        print(f"⏱️  Duration: {(end_time - start_time).total_seconds() / 3600:.1f} hours")
        
        # Server Analysis
        server_analysis = self.analyze_server_logs(start_time, end_time)
        if "error" not in server_analysis:
            print(f"\n🖥️  SERVER PERFORMANCE:")
            print(f"   📡 Requests Received: {server_analysis['requests_received']}")
            print(f"   🎯 Signals Generated:")
            for signal, count in server_analysis['signals_generated'].items():
                print(f"      {signal}: {count}")
            print(f"   ❌ Errors: {server_analysis['errors']}")
            print(f"   💱 Symbols Processed: {', '.join(server_analysis['symbols_processed'])}")
            
            if server_analysis['recent_errors']:
                print(f"   🚨 Recent Errors:")
                for error in server_analysis['recent_errors']:
                    print(f"      {error}")
        else:
            print(f"\n🖥️  SERVER: {server_analysis['error']}")
        
        # MT5 Analysis
        mt5_analysis = self.analyze_mt5_logs(start_time, end_time)
        if "error" not in mt5_analysis:
            print(f"\n🔧 MT5 PERFORMANCE:")
            print(f"   💰 Trades Executed:")
            for trade_type, count in mt5_analysis['trades_executed'].items():
                print(f"      {trade_type}: {count}")
            print(f"   📡 Server Communications: {mt5_analysis['server_communications']}")
            print(f"   🔧 System Events: {mt5_analysis['system_events']}")
            print(f"   ❌ Errors: {mt5_analysis['errors']}")
            
            if mt5_analysis['recent_trades']:
                print(f"   📈 Recent Trades:")
                for trade in mt5_analysis['recent_trades']:
                    print(f"      {trade}")
        else:
            print(f"\n🔧 MT5: {mt5_analysis['error']}")
        
        # Monitor Analysis
        monitor_analysis = self.analyze_monitor_logs(start_time, end_time)
        if "error" not in monitor_analysis:
            print(f"\n🔍 MONITORING PERFORMANCE:")
            print(f"   ✅ Health Checks: {monitor_analysis['health_checks']}")
            print(f"   ❌ Server Errors Detected: {monitor_analysis['server_errors']}")
            print(f"   🔧 MT5 Checks: {monitor_analysis['mt5_checks']}")
            print(f"   🔄 System Restarts: {monitor_analysis['system_restarts']}")
        else:
            print(f"\n🔍 MONITOR: {monitor_analysis['error']}")
        
        # Summary
        print(f"\n📋 SUMMARY:")
        total_activity = (
            server_analysis.get('requests_received', 0) +
            sum(mt5_analysis.get('trades_executed', {}).values()) +
            monitor_analysis.get('health_checks', 0)
        )
        total_errors = (
            server_analysis.get('errors', 0) +
            mt5_analysis.get('errors', 0) +
            monitor_analysis.get('server_errors', 0)
        )
        
        print(f"   📊 Total Activity: {total_activity} events")
        print(f"   ❌ Total Errors: {total_errors}")
        if total_activity > 0:
            error_rate = (total_errors / total_activity) * 100
            print(f"   📈 Error Rate: {error_rate:.2f}%")
        
        print("=" * 70)
        
        # Save report
        report_file = f"history_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"System History Report - {time_desc}\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Server Analysis: {json.dumps(server_analysis, indent=2, ensure_ascii=False)}\n\n")
            f.write(f"MT5 Analysis: {json.dumps(mt5_analysis, indent=2, ensure_ascii=False)}\n\n")
            f.write(f"Monitor Analysis: {json.dumps(monitor_analysis, indent=2, ensure_ascii=False)}\n")
        
        print(f"💾 Detailed report saved to: {report_file}")

def main():
    checker = SystemHistoryChecker()
    
    print("🔍 TRADING SYSTEM HISTORY CHECKER")
    print("This tool analyzes past system performance and activities")
    print()
    
    start_time, end_time, time_desc = checker.get_time_range_input()
    checker.generate_history_report(start_time, end_time, time_desc)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการรันแบบย่อ - ทดสอบ 1 ไฟล์จากแต่ละกลุ่ม
"""

import os
import sys
import shutil

def setup_mini_test():
    """
    ตั้งค่าการทดสอบแบบย่อ
    """
    print("🔧 ตั้งค่าการทดสอบแบบย่อ")
    print("=" * 50)
    
    # สำรองโฟลเดอร์ผลลัพธ์เดิม
    results_folder = "Test_LightGBM/results"
    backup_folder = "Test_LightGBM/results_backup"
    
    if os.path.exists(results_folder):
        if os.path.exists(backup_folder):
            shutil.rmtree(backup_folder)
        shutil.copytree(results_folder, backup_folder)
        print(f"📁 สำรองโฟลเดอร์ผลลัพธ์ไปที่: {backup_folder}")
        
        # ลบโฟลเดอร์ผลลัพธ์เดิม
        shutil.rmtree(results_folder)
        print(f"🗑️ ลบโฟลเดอร์ผลลัพธ์เดิม: {results_folder}")
    
    # สร้างโฟลเดอร์ใหม่
    os.makedirs(results_folder, exist_ok=True)
    print(f"📁 สร้างโฟลเดอร์ผลลัพธ์ใหม่: {results_folder}")

def modify_config_for_mini_test():
    """
    แก้ไขการตั้งค่าสำหรับการทดสอบแบบย่อ
    """
    print("\n🔧 แก้ไขการตั้งค่าสำหรับการทดสอบแบบย่อ")
    print("=" * 50)
    
    config_changes = """
# การเปลี่ยนแปลงที่ต้องทำใน python_LightGBM_15_Tuning.py:

1. เปลี่ยน NUM_TRAINING_ROUNDS = 1 (แทน 5)
2. เปลี่ยน test_groups ให้มีแค่ 1 ไฟล์ต่อกลุ่ม:

test_groups = {
    "M30": ["CSV_Files_Fixed/GOLD_M30_FIXED.csv"],
    "M60": ["CSV_Files_Fixed/GOLD_H1_FIXED.csv"]
}

3. เปลี่ยน Steps_to_do = True เพื่อดู debug messages
4. เปลี่ยน Save_File = True เพื่อบันทึกผลลัพธ์
"""
    
    print(config_changes)
    
    # สร้างไฟล์ config สำหรับการทดสอบ
    mini_config = """
# Mini Test Configuration
NUM_TRAINING_ROUNDS = 1

test_groups = {
    "M30": ["CSV_Files_Fixed/GOLD_M30_FIXED.csv"],
    "M60": ["CSV_Files_Fixed/GOLD_H1_FIXED.csv"]
}

Steps_to_do = True
Save_File = True
"""
    
    with open("mini_test_config.py", "w", encoding="utf-8") as f:
        f.write(mini_config)
    
    print(f"📄 สร้างไฟล์ config: mini_test_config.py")

def check_expected_results():
    """
    ตรวจสอบผลลัพธ์ที่คาดหวัง
    """
    print("\n🔍 ตรวจสอบผลลัพธ์ที่คาดหวัง")
    print("=" * 50)
    
    expected_structure = """
Test_LightGBM/results/
├── M30/
│   └── 030_GOLD/
│       ├── confusion_matrix.png
│       ├── feature_importance.png
│       ├── roc_curve.png
│       └── classification_report.txt
├── M60/
│   └── 060_GOLD/
│       ├── confusion_matrix.png
│       ├── feature_importance.png
│       ├── roc_curve.png
│       └── classification_report.txt
└── daily_trading_schedule_summary.txt
"""
    
    print("📁 โครงสร้างที่คาดหวัง:")
    print(expected_structure)
    
    print("✅ สิ่งที่ควรเกิดขึ้น:")
    print("1. ไฟล์ M30 อยู่ในโฟลเดอร์ M30 เท่านั้น")
    print("2. ไฟล์ M60 อยู่ในโฟลเดอร์ M60 เท่านั้น")
    print("3. daily_trading_schedule_summary.txt แสดงสัญลักษณ์ที่หลากหลาย")
    print("4. ไม่มีการปนเปื้อนระหว่าง timeframe")

def create_mini_test_script():
    """
    สร้างสคริปต์สำหรับการทดสอบแบบย่อ
    """
    print("\n🔧 สร้างสคริปต์สำหรับการทดสอบแบบย่อ")
    print("=" * 50)
    
    mini_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
สคริปต์ทดสอบแบบย่อ - รัน 1 ไฟล์จากแต่ละกลุ่ม
"""

# แก้ไขการตั้งค่าสำหรับการทดสอบ
NUM_TRAINING_ROUNDS = 1

test_groups = {
    "M30": ["CSV_Files_Fixed/GOLD_M30_FIXED.csv"],
    "M60": ["CSV_Files_Fixed/GOLD_H1_FIXED.csv"]
}

Steps_to_do = True
Save_File = True

# Import และรันโค้ดหลัก
if __name__ == "__main__":
    print("🚀 เริ่มการทดสอบแบบย่อ")
    print("=" * 50)
    
    # เปลี่ยนการตั้งค่าใน main file
    import python_LightGBM_15_Tuning as main_module
    
    # อัพเดทการตั้งค่า
    main_module.NUM_TRAINING_ROUNDS = NUM_TRAINING_ROUNDS
    main_module.test_groups = test_groups
    main_module.Steps_to_do = Steps_to_do
    main_module.Save_File = Save_File
    
    print(f"📊 จำนวนรอบการเทรน: {NUM_TRAINING_ROUNDS}")
    print(f"📁 กลุ่มทดสอบ: {list(test_groups.keys())}")
    print(f"🔍 Debug mode: {Steps_to_do}")
    print(f"💾 บันทึกไฟล์: {Save_File}")
    
    # รันการทดสอบ
    try:
        # วนลูปแต่ละกลุ่ม (M30, M60)
        for group_name, group_files in test_groups.items():
            print(f"\\n=== เริ่มการเทรน กลุ่ม {group_name} ===")
            
            # กำหนด output_folder เฉพาะกลุ่ม
            output_folder = f"Test_LightGBM/results/{group_name}"
            import os
            os.makedirs(output_folder, exist_ok=True)
            
            input_files = group_files
            
            # เทรน 1 รอบ
            for run_i in range(NUM_TRAINING_ROUNDS):
                current_run_identifier = run_i + 1
                print(f"\\n### เริ่มการเทรนรอบที่ {current_run_identifier}/{NUM_TRAINING_ROUNDS} ของกลุ่ม {group_name} ###")
                
                try:
                    main_module.main(run_identifier=current_run_identifier, group_name=group_name, input_files=input_files)
                    print(f"✅ เทรนกลุ่ม {group_name} สำเร็จ")
                except Exception as e:
                    print(f"❌ เกิดข้อผิดพลาดในกลุ่ม {group_name}: {e}")
                    import traceback
                    traceback.print_exc()
            
            print(f"=== เสร็จสิ้นการเทรนกลุ่ม {group_name} ===")
        
        # สร้างสรุปการเทรดรายวัน
        print(f"\\n🏗️ กำลังสร้างสรุปการเทรดรายวัน...")
        main_module.print_trading_schedule_summary()
        
        print("\\n✅ การทดสอบแบบย่อเสร็จสิ้น")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        import traceback
        traceback.print_exc()
'''
    
    with open("run_mini_test.py", "w", encoding="utf-8") as f:
        f.write(mini_script)
    
    print(f"📄 สร้างสคริปต์ทดสอบ: run_mini_test.py")

if __name__ == "__main__":
    print("🔧 เตรียมการทดสอบแบบย่อ")
    print("=" * 60)
    
    setup_mini_test()
    modify_config_for_mini_test()
    check_expected_results()
    create_mini_test_script()
    
    print("\n✅ เตรียมการเสร็จสิ้น")
    print("\n🚀 ขั้นตอนต่อไป:")
    print("1. รัน: python run_mini_test.py")
    print("2. ตรวจสอบผลลัพธ์ใน Test_LightGBM/results/")
    print("3. ยืนยันว่าไม่มีการปนเปื้อน timeframe")
    print("4. ตรวจสอบ daily_trading_schedule_summary.txt")

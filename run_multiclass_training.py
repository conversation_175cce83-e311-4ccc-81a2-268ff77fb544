#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Run Multi-class Training
รันการเทรนโมเดล Multi-class Classification จริง

Created: 2025-07-04
"""

import sys
import os

# เพิ่ม path สำหรับ import main file
sys.path.append('.')

# Import main training function
from python_LightGBM_15_Tuning import main_training_pipeline

def main():
    """ฟังก์ชันหลักสำหรับรันการเทรน Multi-class"""
    print("🚀 เริ่มการเทรน Multi-class Classification")
    print("="*60)
    
    # กำหนด symbols และ timeframes สำหรับการเทรน
    symbols = ["GBPUSD", "EURUSD"]  # เริ่มด้วย 2 symbols ก่อน
    timeframes = ["M30"]  # เริ่มด้วย M30 ก่อน
    
    print(f"📊 Symbols: {symbols}")
    print(f"📊 Timeframes: {timeframes}")
    print(f"📊 Total combinations: {len(symbols) * len(timeframes)}")
    
    # รันการเทรนสำหรับแต่ละ combination
    results = []
    
    for symbol in symbols:
        for timeframe in timeframes:
            print(f"\n{'='*60}")
            print(f"🎯 กำลังเทรน: {symbol} {timeframe}")
            print(f"{'='*60}")
            
            try:
                # รันการเทรนหลัก
                result = main_training_pipeline(
                    symbol=symbol,
                    timeframe=timeframe,
                    do_hyperparameter_tuning=False,  # ปิด hyperparameter tuning ก่อน
                    use_smote=False,  # ปิด SMOTE ก่อน
                    save_model=True,
                    save_plots=True
                )
                
                if result:
                    print(f"✅ เทรน {symbol} {timeframe} สำเร็จ")
                    results.append({
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'status': 'SUCCESS',
                        'result': result
                    })
                else:
                    print(f"❌ เทรน {symbol} {timeframe} ล้มเหลว")
                    results.append({
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'status': 'FAILED',
                        'result': None
                    })
                    
            except Exception as e:
                print(f"❌ เกิดข้อผิดพลาดขณะเทรน {symbol} {timeframe}: {str(e)}")
                import traceback
                traceback.print_exc()
                results.append({
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'status': 'ERROR',
                    'error': str(e)
                })
    
    # สรุปผลการเทรน
    print(f"\n{'='*60}")
    print("📊 สรุปผลการเทรน Multi-class")
    print(f"{'='*60}")
    
    success_count = sum(1 for r in results if r['status'] == 'SUCCESS')
    failed_count = sum(1 for r in results if r['status'] == 'FAILED')
    error_count = sum(1 for r in results if r['status'] == 'ERROR')
    
    print(f"✅ สำเร็จ: {success_count}")
    print(f"❌ ล้มเหลว: {failed_count}")
    print(f"⚠️ ข้อผิดพลาด: {error_count}")
    print(f"📊 รวม: {len(results)}")
    
    # แสดงรายละเอียดผลการเทรน
    print(f"\n📋 รายละเอียดผลการเทรน:")
    for result in results:
        status_icon = "✅" if result['status'] == 'SUCCESS' else "❌" if result['status'] == 'FAILED' else "⚠️"
        print(f"  {status_icon} {result['symbol']} {result['timeframe']}: {result['status']}")
        
        if result['status'] == 'SUCCESS' and 'result' in result and result['result']:
            # แสดงข้อมูลเพิ่มเติมถ้ามี
            res = result['result']
            if isinstance(res, dict):
                if 'accuracy' in res:
                    print(f"    📊 Accuracy: {res['accuracy']:.4f}")
                if 'f1_macro' in res:
                    print(f"    📊 F1 Macro: {res['f1_macro']:.4f}")
                if 'f1_weighted' in res:
                    print(f"    📊 F1 Weighted: {res['f1_weighted']:.4f}")
    
    print(f"\n🎯 การเทรน Multi-class เสร็จสมบูรณ์!")
    
    if success_count > 0:
        print(f"✅ มีโมเดล Multi-class ที่เทรนสำเร็จ {success_count} โมเดล")
        print(f"📁 ตรวจสอบผลลัพธ์ได้ที่โฟลเดอร์ Test_LightGBM/")
    
    return results

if __name__ == "__main__":
    main()

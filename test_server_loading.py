#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการโหลดโมเดลใน MT5 WebRequest Server ที่ปรับปรุงแล้ว
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

def test_single_model_loading():
    """ทดสอบการโหลด Single Model Architecture"""
    print("📊 ทดสอบการโหลด Single Model Architecture...")
    
    try:
        # Import ฟังก์ชันจาก server
        sys.path.append('.')
        
        # Import ฟังก์ชันที่ต้องการทดสอบ
        from python_to_mt5_WebRequest_server_12_Signal import (
            load_single_model_threshold,
            load_single_model_nbars,
            load_single_model_time_filters
        )
        
        symbol = "GOLD"
        timeframe = 60
        
        print(f"🔍 ทดสอบการโหลดพารามิเตอร์สำหรับ {symbol} M{timeframe}")
        
        # ทดสอบการโหลด threshold
        threshold = load_single_model_threshold(symbol, timeframe)
        print(f"   Threshold: {threshold}")
        
        # ทดสอบการโหลด nBars_SL
        nbars = load_single_model_nbars(symbol, timeframe)
        print(f"   nBars_SL: {nbars}")
        
        # ทดสอบการโหลด time_filters
        time_filters = load_single_model_time_filters(symbol, timeframe)
        print(f"   Time Filters: {time_filters}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_model_loading():
    """ทดสอบการโหลด Multi-Model Architecture"""
    print("\n🔄 ทดสอบการโหลด Multi-Model Architecture...")
    
    try:
        from python_LightGBM_16_Signal import (
            load_scenario_models,
            load_scenario_threshold,
            load_scenario_nbars,
            load_time_filters
        )
        
        symbol = "GOLD"
        timeframe = 60
        
        print(f"🔍 ทดสอบการโหลดโมเดลสำหรับ {symbol} M{timeframe}")
        
        # ทดสอบการโหลดโมเดล
        scenario_models = load_scenario_models(symbol, timeframe)
        
        if scenario_models:
            print(f"✅ โหลดโมเดลสำเร็จ: {len(scenario_models)} scenarios")
            
            for scenario_name in scenario_models.keys():
                print(f"\n📋 {scenario_name}:")
                
                # ทดสอบการโหลด threshold
                threshold = load_scenario_threshold(symbol, timeframe, scenario_name)
                print(f"   Threshold: {threshold}")
                
                # ทดสอบการโหลด nBars_SL
                nbars = load_scenario_nbars(symbol, timeframe, scenario_name)
                print(f"   nBars_SL: {nbars}")
            
            # ทดสอบการโหลด time_filters
            time_filters = load_time_filters(symbol, timeframe)
            print(f"\n📅 Time Filters: {time_filters}")
            
            return True
        else:
            print("❌ ไม่สามารถโหลดโมเดลได้")
            return False
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_server_model_loading():
    """ทดสอบการโหลดโมเดลผ่าน server functions"""
    print("\n🖥️ ทดสอบการโหลดโมเดลผ่าน Server Functions...")
    
    try:
        # Import ฟังก์ชันจาก server
        from python_to_mt5_WebRequest_server_12_Signal import load_model_components
        from python_LightGBM_16_Signal import USE_MULTI_MODEL_ARCHITECTURE
        
        symbol = "GOLD"
        timeframe = 60
        
        print(f"🔍 ทดสอบการโหลดโมเดลสำหรับ {symbol} M{timeframe}")
        print(f"📊 Architecture: {'Multi-Model' if USE_MULTI_MODEL_ARCHITECTURE else 'Single Model'}")
        
        # ทดสอบการโหลดโมเดล
        result = load_model_components(symbol, timeframe)
        
        if USE_MULTI_MODEL_ARCHITECTURE:
            if result:
                print(f"✅ โหลด Multi-Model components สำเร็จ: {len(result)} scenarios")
                for scenario_name, model_info in result.items():
                    print(f"   {scenario_name}: {type(model_info['model']).__name__}")
            else:
                print("❌ ไม่สามารถโหลด Multi-Model components ได้")
        else:
            if result and len(result) == 3:
                model, scaler, features_list = result
                print(f"✅ โหลด Single Model components สำเร็จ")
                print(f"   Model: {type(model).__name__}")
                print(f"   Features: {len(features_list)} features")
                print(f"   Scaler: {type(scaler).__name__}")
            else:
                print("❌ ไม่สามารถโหลด Single Model components ได้")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_path_configuration():
    """ทดสอบการตั้งค่า path ใน server"""
    print("\n📁 ทดสอบการตั้งค่า Path ใน Server...")
    
    try:
        from python_LightGBM_16_Signal import USE_MULTI_MODEL_ARCHITECTURE, test_folder
        
        print(f"📊 USE_MULTI_MODEL_ARCHITECTURE: {USE_MULTI_MODEL_ARCHITECTURE}")
        print(f"📁 test_folder: {test_folder}")
        
        # คำนวณ path ที่คาดหวัง
        if USE_MULTI_MODEL_ARCHITECTURE:
            expected_model_path = r'D:\test_gold\LightGBM_Multi\models'
            expected_threshold_path = r'D:\test_gold\LightGBM_Multi\thresholds'
        else:
            expected_model_path = r'D:\test_gold\LightGBM_Single\models'
            expected_threshold_path = r'D:\test_gold\LightGBM_Single\thresholds'
        
        print(f"📁 Expected Model Path: {expected_model_path}")
        print(f"📁 Expected Threshold Path: {expected_threshold_path}")
        
        # ตรวจสอบว่า path มีอยู่จริง
        if os.path.exists(expected_model_path):
            print(f"✅ Model path exists")
        else:
            print(f"❌ Model path does not exist")
        
        if os.path.exists(expected_threshold_path):
            print(f"✅ Threshold path exists")
        else:
            print(f"❌ Threshold path does not exist")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def test_file_naming_consistency():
    """ทดสอบความสอดคล้องของการตั้งชื่อไฟล์"""
    print("\n📝 ทดสอบความสอดคล้องของการตั้งชื่อไฟล์...")
    
    symbol = "GOLD"
    timeframe = 60
    
    try:
        from python_LightGBM_16_Signal import USE_MULTI_MODEL_ARCHITECTURE
        
        if USE_MULTI_MODEL_ARCHITECTURE:
            print("🔄 Multi-Model Architecture File Naming:")
            
            # Model files
            scenarios = ["trend_following", "counter_trend"]
            for scenario in scenarios:
                model_file = f"LightGBM_Multi/models/{scenario}/{timeframe:03d}_{symbol}_trained.pkl"
                features_file = f"LightGBM_Multi/models/{scenario}/{timeframe:03d}_{symbol}_features.pkl"
                scaler_file = f"LightGBM_Multi/models/{scenario}/{timeframe:03d}_{symbol}_scaler.pkl"
                
                print(f"   {scenario}:")
                print(f"     Model: {model_file} {'✅' if os.path.exists(model_file) else '❌'}")
                print(f"     Features: {features_file} {'✅' if os.path.exists(features_file) else '❌'}")
                print(f"     Scaler: {scaler_file} {'✅' if os.path.exists(scaler_file) else '❌'}")
            
            # Threshold files
            threshold_files = [
                f"LightGBM_Multi/thresholds/{timeframe:03d}_{symbol}_trend_following_optimal_threshold.pkl",
                f"LightGBM_Multi/thresholds/{timeframe:03d}_{symbol}_trend_following_optimal_nBars_SL.pkl",
                f"LightGBM_Multi/thresholds/{timeframe:03d}_{symbol}_counter_trend_optimal_threshold.pkl",
                f"LightGBM_Multi/thresholds/{timeframe:03d}_{symbol}_counter_trend_optimal_nBars_SL.pkl",
                f"LightGBM_Multi/thresholds/{timeframe:03d}_{symbol}_time_filters.pkl"
            ]
            
            print("   Threshold files:")
            for file_path in threshold_files:
                print(f"     {os.path.basename(file_path)}: {'✅' if os.path.exists(file_path) else '❌'}")
        
        else:
            print("📊 Single Model Architecture File Naming:")
            
            # Model files
            model_files = [
                f"LightGBM_Single/models/{timeframe:03d}_{symbol}/{timeframe:03d}_{symbol}_trained.pkl",
                f"LightGBM_Single/models/{timeframe:03d}_{symbol}/{timeframe:03d}_{symbol}_features.pkl",
                f"LightGBM_Single/models/{timeframe:03d}_{symbol}/{timeframe:03d}_{symbol}_scaler.pkl"
            ]
            
            print("   Model files:")
            for file_path in model_files:
                print(f"     {os.path.basename(file_path)}: {'✅' if os.path.exists(file_path) else '❌'}")
            
            # Threshold files
            threshold_files = [
                f"LightGBM_Single/thresholds/{timeframe:03d}_{symbol}_optimal_threshold.pkl",
                f"LightGBM_Single/thresholds/{timeframe:03d}_{symbol}_optimal_nBars_SL.pkl",
                f"LightGBM_Single/thresholds/{timeframe:03d}_{symbol}_time_filters.pkl"
            ]
            
            print("   Threshold files:")
            for file_path in threshold_files:
                print(f"     {os.path.basename(file_path)}: {'✅' if os.path.exists(file_path) else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def main():
    """ฟังก์ชันหลักสำหรับทดสอบ"""
    print("🚀 เริ่มทดสอบการโหลดโมเดลใน MT5 WebRequest Server")
    print("="*80)
    
    # ทดสอบการตั้งค่า path
    if not test_path_configuration():
        print("❌ การทดสอบล้มเหลวที่ขั้นตอน Path configuration")
        return
    
    # ทดสอบความสอดคล้องของการตั้งชื่อไฟล์
    if not test_file_naming_consistency():
        print("❌ การทดสอบล้มเหลวที่ขั้นตอน File naming consistency")
        return
    
    # ทดสอบการโหลดโมเดลผ่าน server
    if not test_server_model_loading():
        print("❌ การทดสอบล้มเหลวที่ขั้นตอน Server model loading")
        return
    
    # ทดสอบการโหลด Single Model
    if not test_single_model_loading():
        print("❌ การทดสอบล้มเหลวที่ขั้นตอน Single model loading")
        return
    
    # ทดสอบการโหลด Multi-Model
    if not test_multi_model_loading():
        print("❌ การทดสอบล้มเหลวที่ขั้นตอน Multi-model loading")
        return
    
    print("\n" + "="*80)
    print("✅ การทดสอบการโหลดโมเดลเสร็จสิ้น!")
    print("🎉 MT5 WebRequest Server พร้อมใช้งานทั้ง 2 ระบบ")

if __name__ == "__main__":
    main()

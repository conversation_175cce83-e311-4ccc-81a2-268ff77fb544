# Standard library imports
import os
import sys
import json
import datetime
import math
import traceback

# Third-party library imports
import pandas as pd
import numpy as np
import pandas_ta as ta
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.io as pioa
import joblib
from joblib import dump, load
from imblearn.over_sampling import SMOTE

# scikit-learn imports
from sklearn import __version__ as sklearn_version
from sklearn.model_selection import train_test_split, StratifiedKFold
# from sklearn.utils import resample  # สำหรับ oversampling
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import (accuracy_score, classification_report,
                            confusion_matrix, roc_auc_score,
                            f1_score, precision_score, recall_score,
                            roc_curve, precision_recall_curve, average_precision_score)
from sklearn.ensemble import RandomForestClassifier
import lightgbm as lgb
from sklearn.model_selection import cross_validate

from sklearn.model_selection import TimeSeriesSplit # เพิ่ม import TimeSeriesSplit
from sklearn.impute import SimpleImputer # เพิ่ม import สำหรับการเติมค่าว่าง
from statsmodels.tsa.stattools import adfuller
import pickle # Import pickle library
import time

import plotly.io as pio

# ==============================================
# การตั้งค่าและกำหนดค่าพื้นฐาน (Configuration)
# ==============================================

# การตั้งค่าภาษา
sys.stdout.reconfigure(encoding='utf-8')

# การตั้งค่าพื้นฐาน
Plot_file = False  # True False ตั้งค่าเพื่อกำหนดว่าจะพล็อตกราฟหรือไม่
Steps_to_calculating = False
Steps_to_do = True
# model_confidence_threshold = 0.20

# กำหนดจำนวนรอบการเทรนที่นี่
NUM_TRAINING_ROUNDS = 1

input_files = [ "GOLD#_M30_201905010100_202504302330.csv"]

# input_files = [ "AUDUSD#_M30_201905010000_202504302330.csv",
#                 "EURGBP#_M30_201905010000_202504302330.csv",
#                 "EURUSD#_M30_201905010000_202504302330.csv",
#                 "GBPUSD#_M30_201905010000_202504302330.csv",
#                 "GOLD#_M30_201905010100_202504302330.csv",
#                 "NZDUSD#_M30_201905010000_202504302330.csv",
#                 "USDCAD#_M30_201905010000_202504302330.csv",
#                 "USDJPY#_M30_201905010000_202504302330.csv"]

symbol_info = {
    "GOLD":   {"Spread": 25, "Digits": 2, "Points": 0.01, "Swap_Long": 0, "Swap_Short": 0},
    "AUDUSD": {"Spread": 15, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "EURGBP": {"Spread": 22, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "EURUSD": {"Spread": 13, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "GBPUSD": {"Spread": 25, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "NZDUSD": {"Spread": 22, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "USDCAD": {"Spread": 28, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "USDJPY": {"Spread": 16, "Digits": 3, "Points": 0.001, "Swap_Long": 0, "Swap_Short": 0}
}

timeframe_map = {"M1": 1, "M5": 5, "M15": 15, "M30": 30, "H1": 60, "H2": 120, "H4": 240, "D1": 1440}

# ตรวจสอบว่าโฟลเดอร์
output_folder = "results"
os.makedirs(output_folder, exist_ok=True)

# ==============================================
# data_processing.py
# ฟังก์ชันเกี่ยวกับการโหลด, เตรียม, สร้างฟีเจอร์, ตรวจสอบคุณภาพข้อมูล, สร้าง target ฯลฯ
# ==============================================

"""โหลดข้อมูลจากไฟล์, สร้างฟีเจอร์ทางเทคนิค, ตรวจสอบคุณภาพข้อมูล, สร้าง target, แบ่งข้อมูลเป็น train/val/test, ทำ scaling, และเตรียมข้อมูลสำหรับโมเดล"""
def load_and_process_data(file, modelname, symbol, timeframe, identifier):
    print(f"\n🏗️ เปิดใช้งาน load and process data") if Steps_to_do else None

    # 1. โหลดข้อมูลเบื้องต้น
    try:
        # df = pd.read_csv(file, header=None)

        # อ่านไฟล์แบบธรรมดา (1 คอลัมน์ก่อน)
        df = pd.read_csv(file, header=None)
        # แยกคอลัมน์ด้วยตัว '\t'
        df = df[0].str.split('\t', expand=True)
        # ลบแถวแรกออก (เพราะมันเป็น <DATE> <TIME> ...)
        df = df.drop(index=0).reset_index(drop=True)

        if len(df) < 1000:
            print(f"ไฟล์ {file} มีข้อมูลน้อยกว่า 1000 แท่ง ข้ามการคำนวณไฟล์นี้")
            return None, None, None, None, None, None
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะโหลดไฟล์ {file}: {str(e)}")
        return None, None, None, None, None, None

    # 2. ตั้งชื่อคอลัมน์
    # df.columns = ["Date", "Time", "Open", "High", "Low", "Close", "Volume"]

    # ตั้งชื่อคอลัมน์
    df.columns = ["Date", "Time", "Open", "High", "Low", "Close", "Tickvol", "Vol", "Spread"]
    # เลือกเฉพาะคอลัมน์ที่ต้องการ และเปลี่ยนชื่อ
    df = df[["Date", "Time", "Open", "High", "Low", "Close", "Tickvol"]]
    df = df.rename(columns={"Tickvol": "Volume"})

    # แปลงคอลัมน์ที่เกี่ยวข้องให้เป็นตัวเลข
    numeric_cols = ["Open", "High", "Low", "Close", "Volume"]
    for col in numeric_cols:
        # ใช้ pd.to_numeric เพื่อแปลงเป็นตัวเลข
        # errors='coerce' จะเปลี่ยนค่าที่ไม่สามารถแปลงเป็นตัวเลขได้ให้กลายเป็น NaN (Not a Number)
        df[col] = pd.to_numeric(df[col], errors='coerce')

    # แสดงผล
    print("✅ ข้อมูล : df_features")
    print(df.info())
    print(df.head())
    print(df.tail())

    # 3. สร้าง technical indicators
    
    # เพิ่มฟีเจอร์วันและเวลา
    print(f"📝 เริ่มคำนวณ Time action") if Steps_to_calculating else None
    df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])
    df['Entry_DayOfWeek'] = df['DateTime'].dt.dayofweek  # 0=Monday, 6=Sunday
    df['Entry_Hour'] = df['DateTime'].dt.hour
    
    # สร้างฟีเจอร์สำหรับช่วงเวลาที่สำคัญ
    df['IsMorning'] = ((df['Entry_Hour'] >= 8) & (df['Entry_Hour'] < 12)).astype(int)
    df['IsAfternoon'] = ((df['Entry_Hour'] >= 12) & (df['Entry_Hour'] < 16)).astype(int)
    df['IsEvening'] = ((df['Entry_Hour'] >= 16) & (df['Entry_Hour'] < 20)).astype(int)
    df['IsNight'] = ((df['Entry_Hour'] >= 20) | (df['Entry_Hour'] < 4)).astype(int)
    
    df["Bar_CLp"] = df['Close'].shift(1)

    # Price action
    print(f"📝 เริ่มคำนวณ Price action") if Steps_to_calculating else None
    df["Bar_CL"] = 0.0  # ตั้งค่าเริ่มต้นเป็น 0.0
    df.loc[df['Close'] > df['Open'], "Bar_CL"] = 1.0
    df.loc[df['Close'] < df['Open'], "Bar_CL"] = -1.0

    df["Bar_CL_OC"] = 0.0
    df.loc[df['Close'] > np.maximum(df['Open'].shift(1), df['Close'].shift(1)), "Bar_CL_OC"] = 1.0
    df.loc[df['Close'] < np.minimum(df['Open'].shift(1), df['Close'].shift(1)), "Bar_CL_OC"] = -1.0

    df["Bar_CL_HL"] = 0.0
    df.loc[(df['Close'] > df['High'].shift(1)) & (df['Close'] > df['Open']), "Bar_CL_HL"] = 1.0
    df.loc[(df['Close'] < df['Low'].shift(1)) & (df['Close'] < df['Open']), "Bar_CL_HL"] = -1.0
    
    df["Bar_SW"] = 0.0
    df.loc[(df['Close'] > df['Open']) & (df['High'] > df['High'].shift(1)) & (df['Low'] > df['Low'].shift(1)), "Bar_SW"] = 1.0
    df.loc[(df['Close'] < df['Open']) & (df['High'] < df['High'].shift(1)) & (df['Low'] < df['Low'].shift(1)), "Bar_SW"] = -1.0

    df["Bar_TL"] = 0.0
    df.loc[(df['Close'] > df['Open']) & (df['High'] < df['High'].shift(1)) & (df['Low'] < df['Low'].shift(1)), "Bar_TL"] = 1.0
    df.loc[(df['Close'] < df['Open']) & (df['High'] > df['High'].shift(1)) & (df['Low'] > df['Low'].shift(1)), "Bar_TL"] = 1.0
    # print(df[df["Bar_TL"]==1.0]) # ต้องการแสดงแบบเฉพาะเจาะจง

    df["Bar_DTB"] = 0.0
    df.loc[(df['Close'] > df['Open']) & (df['Low'] == df['Low'].shift(1)), "Bar_DTB"] = 1.0
    df.loc[(df['Close'] < df['Open']) & (df['High'] == df['High'].shift(1)), "Bar_DTB"] = -1.0
    # print(df[df["Bar_DTB"]==1.0])

    df["Bar_OSB"] = 0.0
    df.loc[(df['Close'] > df['Open']) & (df['High'] > df['High'].shift(1)) & (df['Low'] < df['Low'].shift(1)), "Bar_OSB"] = 1.0
    df.loc[(df['Close'] < df['Open']) & (df['High'] > df['High'].shift(1)) & (df['Low'] < df['Low'].shift(1)), "Bar_OSB"] = -1.0

    df["Bar_FVG"] = 0.0
    df.loc[(df["Low"] > df["High"].shift(2)) & (df["Close"] > df["Open"]), "Bar_FVG"] = 1.0
    df.loc[(df["High"] < df["Low"].shift(2)) & (df["Close"] < df["Open"]), "Bar_FVG"] = -1.0

    num_bars = 3
    df["Low_Prev_Min"] = df["Low"].shift(1).rolling(window=num_bars).min()
    df["High_Prev_Max"] = df["High"].shift(1).rolling(window=num_bars).max()
    # print(df[["DateTime","Open","High","Low","Close","Low_Prev_Min","High_Prev_Max"]].tail(30))

    print(f"📝 เริ่มคำนวณ Pin Bar") if Steps_to_calculating else None
    df["Bar_longwick"] = 0.0
    epsilon = 1e-9  # ค่าเล็กๆ เพื่อป้องกันหารด้วยศูนย์
    lower_wick = (np.minimum(df['Open'], df['Close']) - df['Low']).replace(0, epsilon)
    upper_wick = (df['High'] - np.maximum(df['Open'], df['Close'])).replace(0, epsilon)
    pinbar_up = lower_wick / (df['High'] - np.minimum(df['Open'], df['Close']))
    pinbar_down = upper_wick / (np.maximum(df['Open'], df['Close']) - df['Low'])
    df.loc[pinbar_up > pinbar_down, "Bar_longwick"] = pinbar_up
    df.loc[pinbar_down > pinbar_up, "Bar_longwick"] = -1 * pinbar_down

    print(f"📝 เริ่มคำนวณ Range Bar") if Steps_to_calculating else None
    df['Price_Range'] = df["High"] - df["Low"]
    df['Price_Move'] = df["Close"] - df["Open"]

    print(f"📝 เริ่มคำนวณ Price Strangth") if Steps_to_calculating else None
    df['Price_Strangth'] = 0.0
    body_size_oc = np.abs(df["Close"]-df["Open"])
    body_size_ocp = np.abs(df["Close"].shift(1)-df["Open"].shift(1))
    body_size_hl = np.abs(df["High"]-df["Low"])
    body_size_hlp = np.abs(df["High"].shift(1)-df["Low"].shift(1))

    df.loc[(body_size_oc < body_size_ocp) & (body_size_hl < body_size_hlp) & (df["Close"] < df["Open"]), "Price_Strangth"] = 1
    df.loc[(body_size_oc < body_size_ocp) & (body_size_hl < body_size_hlp) & (df["Close"] > df["Open"]), "Price_Strangth"] = -1
    
    # --- Volume_MA20, Volume_Spike ---
    print(f"📝 เริ่มคำนวณ Volume") if Steps_to_calculating else None
    df['Volume_MA20'] = df['Volume'].rolling(20, min_periods=1).mean()
    # การเติมค่า NaN ที่ถูกต้องขึ้นอยู่กับว่าต้องการให้ค่าเริ่มต้นเป็นอะไร
    # การใช้ Volume.mean() อาจจะไม่เหมาะสมในช่วงแรกๆ ของข้อมูล
    # อาจจะปล่อยให้ dropna() หรือใช้ fillna(method='bfill') หลัง dropna

    # df['Volume_MA20'].fillna(df['Volume'].mean(), inplace=True)
    df['Volume_MA20'] = df['Volume_MA20'].fillna(df['Volume'].mean())

    df['Volume_Spike'] = df['Volume'] / (df['Volume_MA20'] + 1e-10)
    
    # EMA Calculation
    print(f"📝 เริ่มคำนวณ EMA") if Steps_to_calculating else None
    df['EMA50'] = df['Close'].ewm(span=50, min_periods=1).mean()
    df['EMA100'] = df['Close'].ewm(span=100, min_periods=1).mean()
    df['EMA200'] = df['Close'].ewm(span=200, min_periods=1).mean()

    # --- EMA Related Features ---
    df['EMA_diff'] = (df['EMA50'] - df['EMA200'])

    # df['MA_Cross'] = (df['EMA50'] > df['EMA200']).astype(int)
    df['MA_Cross'] = 0.0
    df.loc[(df['EMA50'] > df['EMA200']), "MA_Cross"] = 1.0
    df.loc[(df['EMA50'] < df['EMA200']), "MA_Cross"] = -1.0

    # df["Price_above_EMA50"] = (df["Close"] > df["EMA50"]).astype(int)
    df["Price_above_EMA50"] = 0.0
    df.loc[(df["Close"] > df["EMA50"]), "Price_above_EMA50"] = 1.0
    df.loc[(df["Close"] < df["EMA50"]), "Price_above_EMA50"] = -1.0
    
    # ความผันผวนระยะสั้น
    df['Rolling_Vol_5'] = df['Close'].pct_change().rolling(5, min_periods=1).std()
    df['Rolling_Vol_15'] = df['Close'].pct_change().rolling(15, min_periods=1).std()
    
    # ระยะทางจาก EMA
    df['Dist_EMA50'] = (df['Close'] - df['EMA50']) / (df['EMA50'] + 1e-10)
    df['Dist_EMA100'] = (df['Close'] - df['EMA100']) / (df['EMA100'] + 1e-10)
    df['Dist_EMA200'] = (df['Close'] - df['EMA200']) / (df['EMA200'] + 1e-10)

    # --- RSI Calculation ---
    print(f"📝 เริ่มคำนวณ RSI") if Steps_to_calculating else None
    window_rsi = 14
    delta = df["Close"].diff(1)
    gain = pd.Series(np.where(delta > 0, delta, 0), index=df.index)
    loss = pd.Series(np.where(delta < 0, -delta, 0), index=df.index)

    avg_gain = gain.ewm(span=window_rsi, adjust=False).mean() # มักใช้ EWM สำหรับ RSI Calculation
    avg_loss = loss.ewm(span=window_rsi, adjust=False).mean()

    # Handle division by zero explicitly for rs calculation
    # ใช้ .replace เพื่อจัดการค่า inf และ -inf จากการหาร
    rs = avg_gain / avg_loss
    rs = rs.replace([np.inf, -np.inf], np.nan)

    # Replace NaN in rs resulting from division by zero or initial periods
    # การเติม NaN ด้วย 0 ที่นี่อาจทำให้ RSI14 เป็น 100 ในบางกรณีที่ avg_loss เป็น 0
    # หากต้องการให้แม่นยำกว่า อาจพิจารณาเติม NaN ใน rs ด้วย np.nan แล้วค่อยจัดการ NaN ใน RSI14
    # แต่เนื่องจากคุณเติม NaN ใน RSI14 ช่วงแรกอยู่แล้ว วิธีนี้อาจจะยังใช้ได้
    rs = rs.fillna(0) # Or another appropriate value if needed

    # ปรับการคำนวณ RSI14 ให้ใช้ EWM และจัดการค่า NaN/inf
    df["RSI14"] = 100 - (100 / (1 + rs))

    # เติม NaN ใน RSI14 ช่วงแรกที่คำนวณไม่ได้
    # แก้ไข warning โดยใช้ .loc ในการกำหนดค่าโดยตรง
    df.loc[df.index[:window_rsi-1], "RSI14"] = np.nan # เติม NaN ใน window_rsi - 1 แถวแรก

    df["RSI_signal"] = np.select(
        [df["RSI14"] < 30, df["RSI14"] > 70],
        [-1, 1],
        default=0
    )

    df['RSI_Overbought'] = (df['RSI14'] > 70).astype(int)
    df['RSI_Oversold'] = (df['RSI14'] < 30).astype(int)

    # RSI_ROC
    print(f"📝 เริ่มคำนวณ RSI ROC") if Steps_to_calculating else None
    divisor = df['RSI14'] + 1e-10
    df['RSI_ROC_i2'] = (df['RSI14'] - df['RSI14'].shift(2)) / divisor
    df['RSI_ROC_i4'] = (df['RSI14'] - df['RSI14'].shift(4)) / divisor
    df['RSI_ROC_i6'] = (df['RSI14'] - df['RSI14'].shift(6)) / divisor
    df['RSI_ROC_i8'] = (df['RSI14'] - df['RSI14'].shift(8)) / divisor

    print(f"📝 เริ่มคำนวณ RSI Divergence") if Steps_to_calculating else None
    close_shift_2 = df['Close'].shift(2)
    rsi14_shift_2 = df['RSI14'].shift(2)
    # ใช้ np.isnan().any() เพื่อตรวจสอบ NaN ใน Series ที่ได้จากการ shift (แม้ว่า .isnull().all() ก็ใช้ได้ แต่ .any() อาจชัดเจนกว่าในบริบทนี้)
    if not (pd.isna(close_shift_2).any() or pd.isna(rsi14_shift_2).any()): # ตรวจสอบว่าไม่มี NaN ในแถวที่จะเปรียบเทียบ
        df['RSI_Divergence_i2'] = np.where(
            (df['Close'] > close_shift_2) & (df['RSI14'] < rsi14_shift_2), # แก้ไข: ใช้ rsi14_shift_2
                1, np.where(
                    (df['Close'] < close_shift_2) & (df['RSI14'] > rsi14_shift_2), # แก้ไข: ใช้ rsi14_shift_2
                    -1, 0
            )
        )
    else:
        df['RSI_Divergence_i2'] = 0 # หรือ np.nan ขึ้นอยู่กับความต้องการ

    close_shift_4 = df['Close'].shift(4)
    rsi14_shift_4 = df['RSI14'].shift(4)
    if not (pd.isna(close_shift_4).any() or pd.isna(rsi14_shift_4).any()):
        df['RSI_Divergence_i4'] = np.where(
            (df['Close'] > close_shift_4) & (df['RSI14'] < rsi14_shift_4),
                1, np.where(
                    (df['Close'] < close_shift_4) & (df['RSI14'] > rsi14_shift_4),
                    -1, 0
            )
        )
    else:
        df['RSI_Divergence_i4'] = 0 # หรือ np.nan ขึ้นอยู่กับความต้องการ

    close_shift_6 = df['Close'].shift(6)
    rsi14_shift_6 = df['RSI14'].shift(6)
    if not (pd.isna(close_shift_6).any() or pd.isna(rsi14_shift_6).any()):
        df['RSI_Divergence_i6'] = np.where(
            (df['Close'] > close_shift_6) & (df['RSI14'] < rsi14_shift_6),
                1, np.where(
                    (df['Close'] < close_shift_6) & (df['RSI14'] > rsi14_shift_6),
                    -1, 0
            )
        )
    else:
        df['RSI_Divergence_i6'] = 0

    # --- Indicator Calculation (MACD, RSI, Stochastic, BB, ADX, ATR, SR) ---
    # คำนวณ Indicators หลักก่อน
    print(f"📝 เริ่มคำนวณ MACD, RSI, Stochastic, BB, ADX, ATR, SR") if Steps_to_calculating else None
    macd = ta.macd(df["Close"])
    stoch = ta.stoch(high=df["High"], low=df["Low"], close=df["Close"])
    adx = ta.adx(high=df["High"], low=df["Low"], close=df["Close"])

    window_bb = 20
    rolling_mean_bb = df["Close"].rolling(window=window_bb, min_periods=1).mean()
    rolling_std_bb = df["Close"].rolling(window=window_bb, min_periods=1).std()
    bb_upper = (rolling_mean_bb + (rolling_std_bb * 2))
    bb_lower = (rolling_mean_bb - (rolling_std_bb * 2))
    bb_width = bb_upper - bb_lower

    window_atr = 14
    if all(col in df.columns for col in ['High', 'Low', 'Close']):
        tr1 = df['High'] - df['Low']
        if len(df) > 1:
            tr2 = (df['High'] - df['Close'].shift()).abs()
            tr3 = (df['Low'] - df['Close'].shift()).abs()
            true_range_df = pd.concat([tr1, tr2, tr3], axis=1) # สามารถ concat ตรงนี้ได้
            true_range = true_range_df.max(axis=1)
        else:
            true_range = pd.Series(np.nan, index=df.index)

        atr = true_range.rolling(window_atr, min_periods=1).mean()
    else:
        atr = pd.Series(np.nan, index=df.index) # สร้าง Series ว่างถ้าคอลัมน์ไม่ครบ

    lookback_sr = 50
    if all(col in df.columns for col in ['Low', 'High']):
        support = df['Low'].rolling(lookback_sr, min_periods=1).min()
        resistance = df['High'].rolling(lookback_sr, min_periods=1).max()
    else:
        support = pd.Series(np.nan, index=df.index)
        resistance = pd.Series(np.nan, index=df.index)

    print(f"📝 เริ่มคำนวณ MACD Features") if Steps_to_calculating else None
    macd_line_col = 'MACD_12_26_9'
    macd_signal_col = 'MACDs_12_26_9'
    df["MACD_12_26_9"] = macd[macd_line_col]
    df["MACDs_12_26_9"] = macd[macd_signal_col]
    df["MACDh_12_26_9"] = macd[macd_line_col] - macd[macd_signal_col]

    if macd_line_col in macd.columns:
        df["MACD_line"] = (macd[macd_line_col] > 0.0).astype(int) - (macd[macd_line_col] < 0.0).astype(int) # Convert to 1, 0, -1
    if macd_line_col in macd.columns and not macd[macd_line_col].shift(1).isnull().all():
        df["MACD_deep"] = (macd[macd_line_col] > macd[macd_line_col].shift(1)).astype(int) - (macd[macd_line_col] < macd[macd_line_col].shift(1)).astype(int)
    if macd_line_col in macd.columns and macd_signal_col in macd.columns:
        df["MACD_signal"] = (macd[macd_line_col] > macd[macd_signal_col]).astype(int) - (macd[macd_line_col] < macd[macd_signal_col]).astype(int)

    # Stochastic Features
    print(f"📝 เริ่มคำนวณ STO") if Steps_to_calculating else None
    stoch_k_col = 'STOCHk_14_3_3'
    stoch_d_col = 'STOCHd_14_3_3'

    if stoch_k_col in stoch.columns and stoch_d_col in stoch.columns:
        df["STO_cross"] = (stoch[stoch_k_col] > stoch[stoch_d_col]).astype(int) - (stoch[stoch_k_col] < stoch[stoch_d_col]).astype(int)
        df["STO_zone"] = (stoch[stoch_k_col] > 50).astype(int) - (stoch[stoch_k_col] < 50).astype(int)
        df["STO_overbought"] = (stoch[stoch_k_col] > 80).astype(int)
        df["STO_Oversold"] = (stoch[stoch_k_col] < 20).astype(int)

    # ADX Features
    print(f"📝 เริ่มคำนวณ ADX") if Steps_to_calculating else None
    adx_col = 'ADX_14'
    dmp_col = 'DMP_14'
    dmn_col = 'DMN_14'

    df["ADX_14"] = adx[adx_col]
    df["DMP_14"] = adx[dmp_col]
    df["DMN_14"] = adx[dmn_col]

    df["ADX_Deep"] = (df["ADX_14"] > df["ADX_14"].shift(1)).astype(int) - (df["ADX_14"] < df["ADX_14"].shift(1)).astype(int)

    df["ADX_zone_25"] = (df["ADX_14"] > 25).astype(int)
    df["ADX_zone_15"] = (df["ADX_14"] > 15).astype(int)

    if dmp_col in adx.columns and dmn_col in adx.columns:
        df["ADX_cross"] = (adx[dmp_col] > adx[dmn_col]).astype(int) - (adx[dmp_col] < adx[dmn_col]).astype(int)

    # ATR Feature
    print(f"📝 เริ่มคำนวณ ATR") if Steps_to_calculating else None
    df['ATR'] = atr # ATR Series ที่คำนวณไว้ก่อนหน้า

    df['ATR_Deep'] = (df["ATR"] > df["ATR"].shift(1)).astype(int) - (df["ATR"] < df["ATR"].shift(1)).astype(int)

    df['ATR_ROC_i2'] = (df['ATR'] - df['ATR'].shift(2)) / (df['ATR'] + 1e-10)
    df['ATR_ROC_i4'] = (df['ATR'] - df['ATR'].shift(4)) / (df['ATR'] + 1e-10)
    df['ATR_ROC_i6'] = (df['ATR'] - df['ATR'].shift(6)) / (df['ATR'] + 1e-10)
    df['ATR_ROC_i8'] = (df['ATR'] - df['ATR'].shift(8)) / (df['ATR'] + 1e-10)

    # BB Width Feature
    print(f"📝 เริ่มคำนวณ BB") if Steps_to_calculating else None
    df['BB_width'] = bb_width # BB_width Series ที่คำนวณไว้ก่อนหน้า

    # SR Features
    print(f"📝 เริ่มคำนวณ S and R") if Steps_to_calculating else None
    df['Support'] = support # 'Low'
    df['Resistance'] = resistance # 'High'

    df['PullBack_Up'] = (df['Resistance'] - df['Close']) / (df['Resistance'] - df['Support'] + 1e-10)
    df['PullBack_Down'] = (df['Close'] - df['Support']) / (df['Resistance'] - df['Support'] + 1e-10)
    # print(df[["DateTime", "Open", "High", "Low", "Close", "Support", "Resistance", "PullBack_Up", "PullBack_Down"]].tail(25))
    # print(df[df['PullBack_Up'] > 0.50][['Date', 'Time', 'PullBack_Up']].tail(10))
    # print(df[df['PullBack_Down'] > 0.50][['Date', 'Time', 'PullBack_Down']].tail(10))

    # หลีกเลี่ยงการคำนวณที่อาจทำให้เกิด division by zero
    epsilon = 1e-9
    Points = symbol_info[symbol]["Points"]

    df["SL_Buy"] =  np.minimum(
                    df["Open"] - 100 * Points,
                    np.maximum(df["Low_Prev_Min"], df["Support"])
                ) + epsilon
    
    df["SL_Sell"] = np.maximum(
                    df["Open"] + 100 * Points,
                    np.minimum(df["High_Prev_Max"], df["Resistance"])
                ) + epsilon
    
    # สร้างคอลัมน์สัดส่วนของ Buy Sell
    df["Ratio_Buy"] = ((df["Resistance"] - df["Open"]) / (df["Open"] - df["SL_Buy"]))
    df["Ratio_Sell"] = ((df["Open"] - df["Support"]) / (df["SL_Sell"] - df["Open"]))
    # print(df[["DateTime", "Open", "High", "Low", "Close", "Support", "Resistance", "Low_Prev_Min", "High_Prev_Max", "SL_Buy", "SL_Sell", "Ratio_Buy", "Ratio_Sell"]].tail(25))
    # print(df[df['Ratio_Buy'] > 1.00][['Date', 'Time', 'Ratio_Buy']].tail(10))
    # print(df[df['Ratio_Sell'] > 1.00][['Date', 'Time', 'Ratio_Sell']].tail(10))

    # print(df[(df['Ratio_Buy'] > 1.00) & (df['PullBack_Up'] > 0.50)][['Date', 'Time', 'Ratio_Buy', 'PullBack_Up']])
    # print(df[(df['Ratio_Sell'] > 1.00) & (df['PullBack_Down'] > 0.50)][['Date', 'Time', 'Ratio_Sell', 'PullBack_Down']])

    print("\n✅ ข้อมูล df ก่อนทำ Lag Features")
    # print(df.info())
    print(df.head())

    print(f"📝 เริ่มคำนวณ Lag Features") if Steps_to_calculating else None
    # กำหนด Lag periods ที่ต้องการ (ปรับตามความเหมาะสมของ timeframe)
    if timeframe >= 240:  # สำหรับ timeframe ขนาดใหญ่ (H4, D1)
        lags = [1, 2, 3, 5, 10]  # ตัวอย่างสำหรับ daily data
    else:  # สำหรับ timeframe ที่เล็กกว่า (M1, M5, M15, H1)
        lags = [1, 2, 3, 5, 10, 15, 20, 30, 50]  # ตัวอย่างสำหรับ intraday data

    # --- สร้าง Lag Features (ปรับปรุงเพื่อ Performance) ---
    # สร้าง DataFrame เปล่าสำหรับเก็บ Lag Features
    lag_features = pd.DataFrame(index=df.index)

    # Lag Features สำหรับคอลัมน์ราคา/Volume
    price_cols_upper = ['Close', 'Open', 'High', 'Low', 'Volume']
    for col in price_cols_upper:
        if col in df.columns:
            for lag in lags:
                lag_features[f'{col}_Lag_{lag}'] = df[col].shift(lag)
        else:
            print(f"Warning: Price column '{col}' not found in df for Lag Features.")

    # กรองเฉพาะคอลัมน์ที่มีอยู่จริงใน df หรือ DataFrame indicator_features
    # หรือใน Series ที่คำนวณ Indicators หลัก
    existing_indicator_cols_for_lag = [
        'RSI14' if 'RSI14' in df.columns else None,
        'EMA50' if 'EMA50' in df.columns else None,
        'EMA100' if 'EMA100' in df.columns else None,
        'EMA200' if 'EMA200' in df.columns else None,
        'ATR' if 'ATR' in df.columns else None, # หรือ atr.name ถ้า atr เป็น Series
        'BB_width' if 'BB_width' in df.columns else None, # หรือ bb_width.name
        macd_line_col if macd_line_col in macd.columns else None,
        macd_signal_col if macd_signal_col in macd.columns else None,
        stoch_k_col if stoch_k_col in stoch.columns else None,
        stoch_d_col if stoch_d_col in stoch.columns else None,
        adx_col if adx_col in adx.columns else None,
        dmp_col if dmp_col in adx.columns else None,
        dmn_col if dmn_col in adx.columns else None
    ]
    existing_indicator_cols_for_lag = [col for col in existing_indicator_cols_for_lag if col is not None] # กรอง None ออก

    # รวม df และ indicator_features (และ Series indicators หลัก) ชั่วคราวเพื่อทำ Lag
    # หรือเข้าถึง Series indicators หลักโดยตรง
    temp_df_for_lag = pd.concat([df[['Close', 'Open', 'High', 'Low', 'Volume', 'RSI14', 'EMA50', 'EMA100', 'EMA200']],
                                macd, stoch, adx, atr.rename('ATR'), bb_width.rename('BB_width'), support.rename('Support'), resistance.rename('Resistance')], axis=1)

    for indicator in existing_indicator_cols_for_lag:
        # ตรวจสอบว่าคอลัมน์ Indicator มีอยู่จริงใน temp_df_for_lag
        if indicator in temp_df_for_lag.columns:
            for lag in [1, 2, 3, 5]: # ใช้ lag periods ที่ใช้กับ indicators
                lag_features[f'{indicator}_Lag_{lag}'] = temp_df_for_lag[indicator].shift(lag)
        else:
            print(f"Warning: Indicator column '{indicator}' not found in combined data for Lag Features.")

    print(f"📝 เริ่มคำนวณ returns changes features") if Steps_to_calculating else None
    # --- สร้าง Features แบบเปลี่ยนแปลงจาก Lag (Returns, Changes) (ปรับปรุงเพื่อ Performance) ---
    # สร้าง DataFrame เปล่าสำหรับเก็บ Features Returns/Changes
    returns_changes_features = pd.DataFrame(index=df.index)

    for lag in [1, 2, 3, 5]: # ใช้ lag periods ที่ใช้กับ returns/changes
        if 'Close' in df.columns:
            returns_changes_features[f'Close_Return_{lag}'] = df['Close'].pct_change(lag)
        else:
            print(f"Warning: 'Close' column not found for Close_Return_{lag}.")
            returns_changes_features[f'Close_Return_{lag}'] = np.nan

        if 'Volume' in df.columns:
            returns_changes_features[f'Volume_Change_{lag}'] = df['Volume'].diff(lag) / (df['Volume'].shift(lag) + 1e-10)
        else:
            print(f"Warning: 'Volume' column not found for Volume_Change_{lag}.")
            returns_changes_features[f'Volume_Change_{lag}'] = np.nan

    print(f"📝 เริ่มคำนวณ Rolling Features") if Steps_to_calculating else None
    # --- สร้าง Rolling Features (ปรับปรุงเพื่อ Performance) ---
    # สร้าง DataFrame เปล่าสำหรับเก็บ Rolling Features
    rolling_features = pd.DataFrame(index=df.index)

    for window in [3, 5, 10, 20]: # ใช้ window sizes ที่ใช้กับ rolling features
        if 'Close' in df.columns:
            rolling_features[f'Close_MA_{window}'] = df['Close'].rolling(window, min_periods=1).mean().shift(1)
            rolling_features[f'Close_Std_{window}'] = df['Close'].rolling(window, min_periods=1).std().shift(1)
        else:
            print(f"Warning: 'Close' column not found for Close Rolling Features (window {window}).")
            rolling_features[f'Close_MA_{window}'] = np.nan
            rolling_features[f'Close_Std_{window}'] = np.nan

        if 'Volume' in df.columns:
            rolling_features[f'Volume_MA_{window}'] = df['Volume'].rolling(window, min_periods=1).mean().shift(1)
        else:
            print(f"Warning: 'Volume' column not found for Volume_MA_{window}.")
            rolling_features[f'Volume_MA_{window}'] = np.nan

    df_combined = pd.concat([df[['Date', 'Time', 'DateTime', 'Open', 'High', 'Low', 'Close', 'Volume', # ราคาเดิม
                                                'Entry_DayOfWeek', 'Entry_Hour', # Features เวลา
                                                'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', # ช่วงเวลา
                                                'Bar_CLp', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 
                                                'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', # Price Action
                                                'Price_Range', 'Price_Move', 'Price_Strangth', # Price Movement
                                                'Volume_MA20', 'Volume_Spike', # Volume Features
                                                'EMA50', 'EMA100', 'EMA200', 
                                                'EMA_diff', 'MA_Cross', 'Price_above_EMA50', # EMA Features
                                                'Rolling_Vol_5', 'Rolling_Vol_15', # Volatility
                                                'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', # Distance to EMA
                                                'RSI14', # RSI Calculation
                                                'RSI_signal', 'RSI_Overbought', 'RSI_Oversold',
                                                'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8',
                                                'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6',
                                                'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9',
                                                'MACD_line', 'MACD_deep', 'MACD_signal', 
                                                'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold',
                                                'ADX_14', 'DMP_14', 'DMN_14',
                                                'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross',
                                                'ATR',
                                                'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8',
                                                'BB_width',
                                                'Support', 'Resistance',
                                                'PullBack_Up', 'PullBack_Down',
                                                'Ratio_Buy', 'Ratio_Sell'
                                                ]], # เลือกคอลัมน์เดิมที่ยังต้องการเก็บไว้ก่อน concat
                                            lag_features, # Lag Features
                                            returns_changes_features, # Returns/Changes Features
                                            rolling_features # Rolling Features
                                        ], axis=1)

    # ตรวจสอบข้อมูลหลังสร้าง Features
    print(f"\n✅ ข้อมูล df_combined หลังสร้าง Features:")
    print(df_combined.info())
    print(df_combined.head())

    # --- ลบแถวที่มีค่า NaN ที่เกิดจากการสร้าง Indicators และ Features ---
    initial_rows = len(df_combined)
    if not df_combined.empty:
        df = df_combined.dropna() # ตอนนี้ df จะเป็น DF ที่รวม features แล้ว
        print(f"✅ สร้าง Indicators และ Features เรียบร้อย (ลบแถวที่ขาดหายไป {initial_rows - len(df)} จาก {initial_rows} แถว)")
    else:
        print(f"Warning: df_combined is empty before dropna.")
        return

    if df.empty:
        print(f"Warning: No data left in df after dropna.")
        return

    # ตัวอย่างการสร้าง model_features (คุณอาจจะต้องปรับปรุง list นี้ให้ตรงกับ features ที่จะใช้จริง)
    model_features = [col for col in df.columns if col not in ['Date', 'Time', 'DateTime', 'Open', 'High', 'Low', 'Close', 'Volume']]
    # ลบคอลัมน์ชั่วคราวหรือคอลัมน์ที่ไม่ใช้เป็น input model ออก
    # model_features = [f for f in model_features if f not in ['RSI_Shift']] # ตัวอย่างลบคอลัมน์ชั่วคราว

    # ตรวจสอบข้อมูลหลังสร้าง Features
    print(f"\n✅ ข้อมูล df หลังสร้างรวม df_ft_combined")
    print(df.info())
    # print(df.columns.tolist())
    print(df)
    # print(df.head())
    # print(df.tail())

    print("\n🔍 ตรวจสอบ Temporal Dependence และคุณสมบัติอนุกรมเวลา")
    is_sorted = df['DateTime'].is_monotonic_increasing
    print(f"- ข้อมูลเรียงตามเวลา: {'ใช่' if is_sorted else 'ไม่'} (ควรเป็น 'ใช่')")
    
    if not is_sorted:
        print("⚠️ เตือน : ข้อมูลไม่เรียงตามเวลา กำลังเรียงข้อมูลใหม่...")
        df = df.sort_values('DateTime')
    
    # ตรวจสอบช่วงเวลาของข้อมูล
    time_diff = df['DateTime'].diff().dropna()
    print(f"- ช่วงเวลาข้อมูล: {df['DateTime'].min()} ถึง {df['DateTime'].max()}")
    print(f"- ระยะเวลารวม: {df['DateTime'].max() - df['DateTime'].min()}")
    print(f"- ช่วงห่างระหว่างบันทึก (เฉลี่ย): {time_diff.mean()}")
    print(f"- ช่วงห่างระหว่างบันทึก (สูงสุด): {time_diff.max()}")
    print(f"- ช่วงห่างระหว่างบันทึก (ต่ำสุด): {time_diff.min()}")
    
    # ตรวจสอบความต่อเนื่องของข้อมูล
    expected_interval = pd.Timedelta(minutes=timeframe)  # ใช้ timeframe จากข้อมูล
    missing_periods = (time_diff > expected_interval * 1.5).sum()  # 1.5 เท่าของช่วงเวลาปกติ
    print(f"- จำนวนช่วงเวลาที่หายไป: {missing_periods} (จากทั้งหมด {len(df)-1} ช่วง)")
    
    if missing_periods > 0:
        print("⚠️ เตือน : พบช่วงเวลาที่หายไปซึ่งอาจส่งผลต่อการวิเคราะห์")
        # สามารถเพิ่มโค้ดสำหรับจัดการข้อมูลที่หายไปที่นี่
    
    # ตรวจสอบ duplicate timestamps
    duplicate_times = df['DateTime'].duplicated().sum()
    print(f"- จำนวน timestamp ที่ซ้ำกัน: {duplicate_times}")
    
    if duplicate_times > 0:
        print("⚠️ เตือน : พบ timestamp ที่ซ้ำกัน กำลังจัดการด้วยการเฉลี่ย...")
        df = df.groupby('DateTime').mean().reset_index()  # หรือใช้วิธีอื่นที่เหมาะสม

    # ตรวจสอบ Stationarity ของราคา
    print("\n🔍 ตรวจสอบ Stationarity ของข้อมูล:")
    is_close_stationary = check_stationarity(df['Close'], 'Close')
    is_returns_stationary = check_stationarity(df['Close'].pct_change(), 'Returns')

    # สร้างรายงานสรุป
    temporal_report = {
        'symbol': symbol,
        'timeframe': timeframe,
        'data_period': f"{df['DateTime'].min()} to {df['DateTime'].max()}",
        'total_bars': len(df),
        'missing_periods': missing_periods,
        'duplicate_timestamps': duplicate_times,
        'is_stationary_close': is_close_stationary,
        'is_stationary_returns': is_returns_stationary,
        'average_time_gap': str(time_diff.mean())
        # 'hourly_win_rate_correlation': hour_win_rate.corr(pd.Series(np.arange(24)))  # ตรวจสอบความสัมพันธ์กับชั่วโมง
    }

    report_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_temporal_report.json")
    with open(report_path, 'w') as f:
        json.dump(temporal_report, f, indent=2, default=str)
    print(f"\n💾 บันทึกรายงาน Temporal Analysis ที่: {report_path}")

    # --- จัดการ Missing Values ---
    initial_rows = len(df)
    print(f"\n📌 จำนวน Missing Values หลังการประมวลผลเบื้องต้น:")
    # แสดงเฉพาะคอลัมน์ที่มีค่าว่างและจำนวน > 0
    print(df.isnull().sum()[df.isnull().sum() > 0])

    # # จัดการ Missing Values ใน Support และ Resistance
    lookback = 50
    df['Support'] = df['Low'].rolling(lookback).min().bfill()
    df['Resistance'] = df['High'].rolling(lookback).max().bfill()

    # ตรวจสอบ Missing Values หลังการประมวลผล
    missing_values_after_processing = df.isnull().sum()
    print("\n📌 จำนวน Missing Values หลังการประมวลผล:")
    print(missing_values_after_processing[missing_values_after_processing > 0])
    
    print(df)
    check_data_quality(df, file, symbol, timeframe)

    print("\n🔍 ตรวจสอบข้อมูลก่อนสร้าง trade cycles:")
    print(f"ช่วงเวลาข้อมูล: {df['Date'].min()} ถึง {df['Date'].max()}")
    print(f"ค่าเฉลี่ย Close: {df['Close'].mean():.2f}")
    print(f"ค่า EMA50 ล่าสุด: {df['EMA50'].iloc[-1]:.2f}")
    print(f"ค่า RSI14 ล่าสุด: {df['RSI14'].iloc[-1]:.2f}")
    print(f"ค่า MACD ล่าสุด: {df['MACD_12_26_9'].iloc[-1]:.2f}")
    
    # ตรวจสอบค่า DayOfWeek แสดงตัวอย่างวันที่มีการบันทึก
    print(f"Unique values in df['Entry_DayOfWeek']: {df['Entry_DayOfWeek'].unique()}")
    
    # 4. สร้าง trade cycles
    print("\n🔍 กำลังสร้าง trade cycles...")
    model_name_to_use = modelname # ชื่อโมเดลที่ใช้ในการบันทึก/โหลด
    symbol_to_use = symbol
    timeframe_to_use = timeframe

    print(f"\n⚙️ โหลด Model และการตั้งค่าสำหรับ symbol {symbol_to_use} timeframe {timeframe_to_use}")
    trained_model = load_model(model_name_to_use, symbol_to_use, timeframe_to_use)
    scaler = load_scaler(model_name_to_use, symbol_to_use, timeframe_to_use)

    # โหลดรายชื่อ features ที่โมเดลใช้
    model_dir = f"models/{str(timeframe_to_use).zfill(3)}_{symbol_to_use}"
    model_features_path = os.path.join(model_dir, f"{model_name_to_use}_{str(timeframe_to_use).zfill(3)}_{symbol_to_use}_features.pkl") # ใช้ model_name ด้วย
    model_features = None
    if os.path.exists(model_features_path):
        try:
            model_features = joblib.load(model_features_path)
            print(f"\n✅ โหลดรายชื่อ Features ที่ Model ใช้สำเร็จ: {len(model_features)} features")

            for i, feat in enumerate(model_features, 1):
                print(f"{i}. {feat}")
            print("\nFirst 5 rows of df:")
            print(df.head()) # ดูหน้าตาข้อมูลและชื่อคอลัมน์

        except Exception as e:
            print(f"⚠️ เกิดข้อผิดพลาด ขณะโหลดรายชื่อ Features: {str(e)}")
            model_features = None # ใช้ None ถ้าโหลดไม่ได้
    else:
        print(f"\n⚠️ ไม่พบ ไฟล์รายชื่อ Features ที่ Model ใช้: {model_features_path}")
        model_features = None # ใช้ None ถ้า⚠️ ไม่พบ ไฟล์

    # ตรวจสอบ ก่อนเข้า create trade cycles with model
    print(f"🔍 ตรวจสอบ columns")
    print(df.columns.tolist())


    # เรียกใช้ฟังก์ชันสร้างรายการซื้อขายพร้อม Model ช่วยตัดสินใจ
    # df_processed ในที่นี้ควรเป็น Dataframe ที่เตรียม Features ครบถ้วนแล้ว
    trade_df, cycle_stats = create_trade_cycles_with_model(
        df, # DataFrame ที่มีข้อมูลและ Features ครบถ้วน
        trained_model=trained_model,
        scaler=scaler,
        model_features=model_features,
        model_confidence_threshold=None, # ตั้งค่าเป็น None เพื่อให้คำนวณอัตโนมัติ
        # ส่งค่าคงที่อื่นๆ เข้าไปด้วย
        rsi_level=35,
        rsi_level_out=30,
        stop_loss_atr_multiplier=2.00,
        take_profit_stop_loss_ratio=1.00,
        symbol=symbol_to_use,
        timeframe=timeframe_to_use,
        identifier = identifier
    )

    # print(f"\n✅ ตรวจสอบ features หลังเรียก create trade cycles with model")
    # for i, feat in enumerate(model_features, 1):
    #     print(f"{i}. {feat}")

    # แสดงข้อมูลก่อนตรวจสอบ
    print("\n📌 ข้อมูลก่อนตรวจสอบ:")
    print(f"จำนวนแถวข้อมูลทั้งหมด: {len(df)} ตัวอย่างข้อมูล df")
    print(df.head() if not df.empty else "ไม่มีข้อมูล การซื้อขาย")
    print(f"จำนวนการซื้อขายที่พบ: {len(trade_df)} ตัวอย่างข้อมูล trade_df")
    print(trade_df.head() if not trade_df.empty else "ไม่มีข้อมูล การซื้อขาย")

    # หลังจากได้ trade_df แล้ว ให้คำนวณและบันทึกค่า threshold ที่เหมาะสม
    if not trade_df.empty and 'Profit' in trade_df.columns:
        # คำนวณค่า threshold ที่เหมาะสมตามประสิทธิภาพจริง
        optimal_threshold = optimize_threshold_based_on_performance(trade_df, symbol, timeframe)
        
        # บันทึกค่า threshold ที่คำนวณได้
        save_optimal_threshold(symbol, timeframe, optimal_threshold)

    print("\n📊 สถิติการซื้อขาย:")
    print(f"{'='*40}")
    print(f"{'ประเภท':<20}{'ค่าสถิติ':<20}")
    print(f"{'-'*40}")

    # วิเคราะห์ประสิทธิภาพการซื้อขาย (จะได้ stats ที่มี buy, sell, buy_sell)
    stats = analyze_trade_performance(trade_df)

    # แสดงสถิติหลัก
    if 'buy' in stats and 'sell' in stats and 'buy_sell' in stats:
        print("📈 สถิติสำหรับ Buy Trades:")
        print(f"{'Win%':<20}{stats['buy'].get('win_rate', 0):<20.2f}")
        print(f"{'Expectancy':<20}{stats['buy'].get('expectancy', 0):<20.2f}")

        print("📈 สถิติสำหรับ Sell Trades:")
        print(f"{'Win%':<20}{stats['sell'].get('win_rate', 0):<20.2f}")
        print(f"{'Expectancy':<20}{stats['sell'].get('expectancy', 0):<20.2f}")

        print("📈 สถิติสำหรับ Buy_sell Trades:")
        print(f"{'Win%':<20}{stats['buy_sell'].get('win_rate', 0):<20.2f}")
        print(f"{'Expectancy':<20}{stats['buy_sell'].get('expectancy', 0):<20.2f}")
    else:
        print("⚠️ ไม่พบ สถิติหลัก")

    print(f"{'='*40}")

    # แสดงสถิติรายวัน (ใช้ cycle_stats ที่ได้จาก create_trade_cycles)
    if 'day_stats' in cycle_stats:
        print("\n📊 สถิติรายวัน:")
        print(f"{'วัน':<10}{'Win Rate (%)':<15}{'จำนวนการซื้อขาย':<20}")
        print(f"{'-'*45}")
        day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        for day, data in cycle_stats['day_stats'].items():
            if data.get('total', 0) > 0:  # เพิ่มเงื่อนไขตรวจสอบจำนวนการซื้อขาย
                win_rate = (data.get('win', 0) / data.get('total', 1)) * 100 if data.get('total', 0) > 0 else 0
                day_name = day_names[day] if isinstance(day, int) and 0 <= day < 7 else str(day)
                print(f"{day_name:<10}{win_rate:<15.2f}{data.get('total', 0):<20}")
        if not any(data.get('total', 0) > 0 for data in cycle_stats['day_stats'].values()):
            print("ไม่มีข้อมูล สถิติรายวัน") # แสดงข้อความหากไม่มีข้อมูล 
    else:
        print("⚠️ ไม่พบ สถิติรายวัน")

    print(f"{'='*40}")
    
    # แสดงสถิติรายชั่วโมง (ใช้ cycle_stats ที่ได้จาก create_trade_cycles)
    if 'hour_stats' in cycle_stats:
        print("\n📊 สถิติรายชั่วโมง:")
        print(f"{'ชั่วโมง':<10}{'Win Rate (%)':<15}{'จำนวนการซื้อขาย':<20}")
        print(f"{'-'*45}")
        has_data = False # เพิ่มตัวแปรตรวจสอบว่ามีข้อมูลหรือไม่
        for hour, data in sorted(cycle_stats['hour_stats'].items()):
            if data.get('total', 0) > 0:  # เพิ่มเงื่อนไขตรวจสอบจำนวนการซื้อขาย
                win_rate = (data.get('win', 0) / data.get('total', 1)) * 100 if data.get('total', 0) > 0 else 0
                print(f"{hour:<10}{win_rate:<15.2f}{data.get('total', 0):<20}")
                has_data = True
        if not has_data:
            print("ไม่มีข้อมูล สถิติรายชั่วโมง") # แสดงข้อความหากไม่มีข้อมูล 
    else:
        print("⚠️ ไม่พบ สถิติรายชั่วโมง")

    print(f"{'='*40}")
    
    if trade_df.empty:
        print("\n⚠️ ไม่มีข้อมูล การซื้อขายที่ตรงตามเงื่อนไข")
        print("ℹ️ สาเหตุอาจมาจาก:")
        print("- เงื่อนไขการซื้อขายเข้มงวดเกินไป")
        print("- ข้อมูลไม่เหมาะสมกับกลยุทธ์")
        print("- ช่วงเวลาที่วิเคราะห์ไม่มีสัญญาณซื้อขาย")
        return None, None, None, None, None, None
    
    # เพิ่มการตรวจสอบว่า trade_df เป็น DataFrame และไม่ว่างเปล่า
    if not isinstance(trade_df, pd.DataFrame) or trade_df.empty:
        print("⚠️ ไม่มีข้อมูล การซื้อขายที่ตรงตามเงื่อนไข")
        return None, None, None, None, None, None

    # 5. รวม features กับ trade data
    print("\n🔍 กำลังรวม features กับ trade data...")
    print("🔍 ทดลองพิมพ์ trade_df")
    print(trade_df)

    print("\n🔍 ทดลองพิมพ์ df")
    print(df)
    
    # แปลงรูปแบบวันที่ให้ตรงกันก่อน merge
    # trade_df['Entry_DateTime'] = pd.to_datetime(trade_df['Entry Time'], format='%Y.%m.%d %H:%M')
    # df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])

    # แก้ไข format string ของ trade_df['Entry Time'] ให้รวมวินาทีด้วย (%S)
    trade_df['Entry_DateTime'] = pd.to_datetime(trade_df['Entry Time'], format='%Y.%m.%d %H:%M:%S')

    # สำหรับ df['DateTime'] คุณไม่ได้ระบุ format
    # จาก output ของ df['Date'] และ df['Time'] (2020.01.02 09:00:00)
    # รูปแบบก็คือ "%Y.%m.%d %H:%M:%S" เช่นกัน
    # Pandas มักจะ auto-detect รูปแบบนี้ได้ แต่ถ้ายังติดปัญหา ควรระบุ format ให้ชัดเจนไปด้วย
    df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'], format='%Y.%m.%d %H:%M:%S') # แนะนำให้ระบุ format ที่นี่ด้วย
    
    # ตรวจสอบรูปแบบวันที่หลังแปลง
    print("\nตัวอย่าง Entry_DateTime ใน trade_df:", trade_df['Entry_DateTime'].head())
    print("ตัวอย่าง DateTime ใน df:", df['DateTime'].head())

    # ทำ merge โดยใช้ merge_asof สำหรับการจับคู่เวลาที่ใกล้ที่สุด (backward เพื่อป้องกัน look-ahead bias)
    # เพิ่ม tolerance เพื่อให้แน่ใจว่าเวลาใกล้กันจริง
    # เพิ่มคอลัมน์ Indicator ที่ต้องการใช้ทั้งหมด
    columns_to_merge = [
        # Indicator
        'DateTime', 
        'Entry_DayOfWeek', 'Entry_Hour', 
        'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 
        'Bar_CLp', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 
        'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 
        'Price_Range', 'Price_Move', 'Price_Strangth', 
        'Volume_MA20', 'Volume_Spike', 
        'EMA50', 'EMA100', 'EMA200', 
        'EMA_diff', 'MA_Cross', 'Price_above_EMA50', 
        'Rolling_Vol_5', 'Rolling_Vol_15', 
        'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 
        'RSI14', 
        'RSI_signal', 'RSI_Overbought', 'RSI_Oversold', 
        'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 
        'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 
        'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 
        'MACD_line', 'MACD_deep', 'MACD_signal', 
        'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 
        'ADX_14', 'DMP_14', 'DMN_14', 
        'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 
        'ATR', 
        'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 
        'BB_width', 
        'Support', 'Resistance', 
        'PullBack_Up', 'PullBack_Down', 
        'Ratio_Buy', 'Ratio_Sell', 

        # Lag Features สำหรับ Price Lags
        'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 
        'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 
        'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 
        'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 

        # Lag Features สำหรับ Volume Lags
        'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 

        # Lag Features สำหรับ Indicator Lags
        'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 
        'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 
        'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 
        'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 
        'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 
        'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 
        'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 
        'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 
        'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 
        'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 
        'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 
        'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 
        'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 

        # Return and Change Features
        'Close_Return_1', 'Volume_Change_1', 
        'Close_Return_2', 'Volume_Change_2', 
        'Close_Return_3', 'Volume_Change_3', 
        'Close_Return_5', 'Volume_Change_5', 

        # Moving Averages and Std
        'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 
        'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 
        'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 
        'Close_MA_20', 'Close_Std_20', 'Volume_MA_20'
    ]
    
    # กรองคอลัมน์ที่ต้องการ Merge เฉพาะคอลัมน์ที่มีอยู่ใน df จริงๆ (เผื่อในอนาคตมีการเพิ่ม/ลด Indicator ที่คำนวณ)
    available_columns_to_merge = [col for col in columns_to_merge if col in df.columns]
    print(f"🔍 กำลังทำการ merge_asof โดยใช้คอลัมน์ {len(available_columns_to_merge)} features : {available_columns_to_merge}")

    trade_df = pd.merge_asof(
        trade_df.sort_values('Entry_DateTime'),
        df.sort_values('DateTime')[available_columns_to_merge], # ใช้รายการคอลัมน์ที่กรองแล้ว
        left_on='Entry_DateTime',
        right_on='DateTime',
        direction='backward', # แก้ไขตรงนี้
        tolerance=pd.Timedelta('5min') # เพิ่ม tolerance ตรงนี้ (ปรับค่า '5min' ตามความเหมาะสมของ Timeframe)
    )

    # ลบคอลัมน์ DateTime ที่ได้จากการ merge ของ df ออก (เนื่องจากซ้ำกับ Entry_DateTime)
    trade_df.drop('DateTime', axis=1, inplace=True, errors='ignore')

    # เพิ่มฟีเจอร์วันและเวลา (คำนวณจาก Entry_DateTime หลัง merge)
    # โค้ดเดิมมีอยู่แล้ว แต่อาจต้อง ensure ว่า Entry_DateTime ยังเป็น datetime หลัง merge
    # trade_df['Entry_DateTime'] = pd.to_datetime(trade_df['Entry Time']) # อาจไม่ต้องทำซ้ำถ้า merge_asof เก็บ Entry_DateTime ไว้
    trade_df['Entry_DayOfWeek'] = trade_df['Entry_DateTime'].dt.dayofweek
    trade_df['Entry_Hour'] = trade_df['Entry_DateTime'].dt.hour
    trade_df['IsWeekend'] = (trade_df['Entry_DayOfWeek'] >= 5).astype(int)
    trade_df['IsMorning'] = ((trade_df['Entry_Hour'] >= 8) & (trade_df['Entry_Hour'] < 12)).astype(int)
    trade_df['IsAfternoon'] = ((trade_df['Entry_Hour'] >= 12) & (trade_df['Entry_Hour'] < 16)).astype(int)
    trade_df['IsEvening'] = ((trade_df['Entry_Hour'] >= 16) & (trade_df['Entry_Hour'] < 20)).astype(int)
    trade_df['IsNight'] = ((trade_df['Entry_Hour'] >= 20) | (trade_df['Entry_Hour'] < 4)).astype(int)

    # ลบคอลัมน์ Entry_DateTime ชั่วคราวที่สร้างขึ้นเพื่อ merge
    trade_df.drop(['Entry_DateTime'], axis=1, inplace=True, errors='ignore')

    # ตรวจสอบหลัง merge
    # print("\nจำนวน missing values หลัง merge:", trade_df.isnull().sum())

    missing_values = trade_df.isnull().sum()
    has_missing = (missing_values > 0).any()

    print("\n📌 ตรวจสอบ Missing Values หลัง Merge:")
    if has_missing:
        print("พบ Missing Values ในคอลัมน์ต่อไปนี้:")
        print(missing_values[missing_values > 0])
    else:
        print("✅ ไม่พบ Missing Values ใน DataFrame")
    
    # แสดงข้อมูลหลังรวม features
    print("\n📌 ข้อมูลหลังรวม features:")
    print(f"จำนวนแถว: {len(trade_df)}")
    print("ตัวอย่างคอลัมน์ใหม่ 5 แถว:")
    print(trade_df[['Entry Time', 'EMA50', 'EMA100', 'RSI14']].head())

    # เพิ่มใน features ที่จะใช้สำหรับโมเดล >> เนื่องจากใช้การคัดเลือกอัตโนมัติ ดูความสัมพันธ์กับ Target
    features = []
    
    # 6. สร้าง target variable
    print("\n🔍 กำลังสร้าง target variable...")
    
    # ตรวจสอบว่าคอลัมน์ Profit มีอยู่และเป็นตัวเลข
    if 'Profit' not in trade_df.columns or not pd.api.types.is_numeric_dtype(trade_df['Profit']):
        print("⚠️ ไม่พบ คอลัมน์ Profit หรือไม่ใช่ตัวเลข ไม่สามารถสร้าง Target ได้")
        return None, None, None, None, None, None
    
    trade_df = process_trade_targets(trade_df)

    print("\n📌 ข้อมูลหลังสร้าง target variable:")
    print(f"จำนวนแถว: {len(trade_df)}")
    print("ตัวอย่างคอลัมน์ใหม่ 5 แถว:")
    print(trade_df[['Entry Time', 'EMA50', 'EMA100', 'RSI14']].head())
    
    # แสดงข้อมูล target
    print("\n📌 ข้อมูลหลังสร้าง target:")
    if 'Target' in trade_df.columns:
        print("การกระจายของ Target:")
        print(trade_df['Target'].value_counts())
    else:
        print("⚠️ ไม่พบ คอลัมน์ Target ในข้อมูล")
    
    # เพิ่มการตรวจสอบว่า trade_df ไม่ว่างเปล่าและมีคอลัมน์ Target
    if trade_df.empty or 'Target' not in trade_df.columns:
        print("\n⚠️ ไม่สามารถสร้าง Target ได้")
        return None, None, None, None, None, None
    
    # ==============================================
    # ส่วนปรับปรุง: ใช้ฟังก์ชัน select features แทนกระบวนการเดิม
    # ==============================================

    # # ทำความสะอาดข้อมูล
    # trade_df = trade_df.dropna()  # ลบแถวที่มี missing values
    
    # # แก้ไขชื่อ features ให้สอดคล้องกัน
    # trade_df = trade_df.rename(columns={'Entry Price': 'Entry_Price', 'Exit Price': 'Exit_Price'})

    # # เรียกใช้ฟังก์ชัน select features
    # print("\n🔍 เริ่มกระบวนการเลือก Features...")
    # features = select features(trade_df)


    # ส่วนเรียกใช้ select features ใน load and process data
    # ... (โค้ดส่วนอื่นๆ ใน load and process data ก่อนเรียก select features)
    # ทำความสะอาดข้อมูล (ควรทำก่อน select features เพื่อให้การคำนวณ correlation แม่นยำ)
    trade_df = trade_df.dropna() # ลบแถวที่มี missing values ที่เกิดจากการ merge หรือคำนวณ

    # แก้ไขชื่อ features ให้สอดคล้องกัน (ถ้าจำเป็น)
    # trade_df = trade_df.rename(columns={'OldName': 'NewName'}) # ตรวจสอบว่าชื่อคอลัมน์หลัง merge เป็นอย่างไร

    # ตรวจสอบจำนวนข้อมูลหลัง dropna
    if trade_df.empty:
        print("⚠️ ข้อมูล trade_df ว่างเปล่าหลังจากจัดการ Missing Values ไม่สามารถเลือก Features ได้")
        return None, None, None, None, None, None

    # เรียกใช้ฟังก์ชัน select features
    print("\n🔍 เริ่มกระบวนการเลือก Features...")
    features = select_features(trade_df, timeframe) # เรียกใช้ฟังก์ชันที่แก้ไขแล้ว

    # แสดงสรุป features ที่จะใช้
    print("\n📌 สรุป Features ที่จะใช้สำหรับโมเดล:")
    print(f"จำนวน Features: {len(features)}")
    for i, feat in enumerate(features, 1):
        print(f"{i}. {feat}")

    # ==============================================
    # ส่วนตรวจสอบ Class Imbalance
    # ==============================================
    
    print("\n🔍 กำลังตรวจสอบข้อมูลก่อนฝึกโมเดล...")

    # 1. ตรวจสอบการกระจายของ Target
    if 'Target' in trade_df.columns:
        target_dist = trade_df['Target'].value_counts(normalize=True)
        print("\n📊 การกระจายของ Target:")
        print(target_dist)
        
        # ตรวจสอบว่ามีทั้งสองคลาสหรือไม่
        if len(target_dist) < 2:
            print("⚠️ มีเพียงคลาสเดียวใน Target ไม่สามารถฝึกโมเดลได้")
            return None, None, None, None, None, None
            
        # ตรวจสอบ Class Imbalance
        imbalance_ratio = target_dist.min() / target_dist.max()
        print(f"อัตราส่วน Class Imbalance: {imbalance_ratio:.2f}")
        
        if imbalance_ratio < 0.2:  # ถ้าคลาส minority มีน้อยกว่า 20% ของคลาส majority
            print("⚠️ พบ Class Imbalance รุนแรง (อัตราส่วนน้อยกว่า 20%)")

    # 2. ตรวจสอบจำนวนข้อมูลขั้นต่ำ
    min_samples = 50  # กำหนดค่าต่ำสุดตามความเหมาะสม
    if len(trade_df) < min_samples:
        print(f"⚠️ ข้อมูลมีน้อยเกินไป ({len(trade_df)} แถว) ขั้นต่ำที่ต้องการ: {min_samples} แถว")
        return None, None, None, None, None, None

    # 3. ตรวจสอบ missing values
    missing_values = trade_df[features].isnull().sum().sum()
    if missing_values > 0:
        print(f"⚠️ พบ missing values {missing_values} ค่า ใน features")
        # แสดงคอลัมน์ที่มี missing values
        print("คอลัมน์ที่มี missing values:")
        print(trade_df[features].isnull().sum()[trade_df[features].isnull().sum() > 0])
    else:
        print("✅ ไม่พบ missing values ใน features")

    # 4. ตรวจสอบค่าผิดปกติใน features
    print("\n📊 สถิติพื้นฐานของ features:")
    print(trade_df[features].describe().transpose())

    # 5. ตรวจสอบ correlation สูงระหว่าง features
    print("\n🔍 กำลังตรวจสอบความสัมพันธ์ระหว่าง features...")
    try:
        corr_matrix = trade_df[features].corr().abs()
        
        # สร้าง upper triangle matrix แบบ boolean
        upper = np.triu(np.ones(corr_matrix.shape, dtype=bool), k=1)
        
        # นับจำนวนคู่ที่มี correlation สูง
        high_corr = (corr_matrix.where(upper) > 0.8).sum().sum()
        
        if high_corr > 0:
            print(f"⚠️ พบ {high_corr} คู่ features ที่มีความสัมพันธ์สูง (>0.8)")
            
            # แสดงคู่ features ที่มีความสัมพันธ์สูง
            high_corr_pairs = corr_matrix.stack()[
                (corr_matrix.stack() > 0.8) & 
                (corr_matrix.stack() < 1.0)  # ไม่รวมความสัมพันธ์กับตัวเอง
            ].reset_index()
            
            high_corr_pairs.columns = ['Feature 1', 'Feature 2', 'Correlation']
            print("\nคู่ features ที่มีความสัมพันธ์สูง:")
            print(high_corr_pairs.sort_values('Correlation', ascending=False).to_string(index=False))
        else:
            print("✅ ไม่พบ features ที่มีความสัมพันธ์สูงเกินไป")
            
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะตรวจสอบความสัมพันธ์ระหว่าง features: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # ==============================================
    # แบ่งข้อมูลเป็น train/val/test
    # ==============================================
    
    # แบ่งข้อมูลเป็น train/val/test
    print("\n🔍 กำลังแบ่งข้อมูลเป็น train/val/test...")
    if len(trade_df) < 10:
        print(f"\n⚠️ ข้อมูลใน trade_df มีน้อยเกินไป ({len(trade_df)} แถว)")
        print("ตัวอย่างข้อมูลสุดท้าย 5 แถว:")
        print(trade_df.tail())
        return None, None, None, None, None, None

    # เรียงข้อมูลตามเวลา (หากยังไม่ได้เรียง)
    trade_df = trade_df.sort_values('Entry Time')
    
    # แบ่งตามสัดส่วนเวลา
    train_size = int(0.6 * len(trade_df))  # 60% สำหรับฝึก
    val_size = int(0.2 * len(trade_df))    # 20% สำหรับ validation
    
    train = trade_df.iloc[:train_size]
    val = trade_df.iloc[train_size:train_size + val_size]
    test = trade_df.iloc[train_size + val_size:]
    
    # ตรวจสอบการกระจายของ Target ในแต่ละชุด
    print("\nการกระจายของ Target ในชุดข้อมูล:")
    print("Train:", train['Target'].value_counts(normalize=True))
    print("Val:", val['Target'].value_counts(normalize=True))
    print("Test:", test['Target'].value_counts(normalize=True))
    
    X_train, y_train = train[features], train["Target"]
    X_val, y_val = val[features], val["Target"]
    X_test, y_test = test[features], test["Target"]

    # 6. ทำ Feature Scaling
    print("\n🔍 กำลังทำ Feature Scaling...")
    try:
        scaler = StandardScaler()
        
        # ตรวจสอบว่ามีข้อมูลใน X_train หรือไม่
        if len(X_train) == 0:
            print("⚠️ ไม่มีข้อมูล ใน X_train ไม่สามารถทำ Feature Scaling ได้")
            return None, None, None, None, None, None
            
        X_train_scaled = scaler.fit_transform(X_train)
        X_train = pd.DataFrame(X_train_scaled, columns=features, index=X_train.index)
        
        X_val_scaled = scaler.transform(X_val)
        X_val = pd.DataFrame(X_val_scaled, columns=features, index=X_val.index)
        
        X_test_scaled = scaler.transform(X_test)
        X_test = pd.DataFrame(X_test_scaled, columns=features, index=X_test.index)
        
        print("✅ ทำ Feature Scaling เรียบร้อยแล้ว")
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะทำ Feature Scaling: {str(e)}")
        return None, None, None, None, None, None
    
    return (X_train, y_train), (X_val, y_val), (X_test, y_test), df, trade_df, stats

"""ตรวจสอบคุณภาพข้อมูล เช่น duplicate, missing values, และแสดง distribution ของราคาปิด"""
def check_data_quality(df, file_name, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน check data quality") if Steps_to_do else None

    print(f"{'='*50}")
    print(f"Data Quality Check for {file_name}")
    print("="*50)
    
    # missing = df.isnull().sum()
    # print("\n[1] Missing Values:")
    # print(missing[missing > 0].to_string())
    
    # print("\n[2] Data Types:")
    # print(df.dtypes.to_string())
    
    # print("\n[3] Descriptive Stats:")
    # print(df.describe(include='all').to_string())
    
    print(f"\n[4] Duplicate Rows: {df.duplicated().sum()}")

    fig, ax = plt.subplots(figsize=(10, 5))
    ax.hist(df['Close'], bins=50)
    ax.set_title('Price Distribution')
    plt.savefig(os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_price_dist.png"))
    plt.close(fig)

"""ตรวจสอบความ stationary ของ series (เช่น ราคาปิด) ด้วย ADF test"""
def check_stationarity(series, name='Close'):
    print(f"\n🏗️ เปิดใช้งาน check stationarity") if Steps_to_do else None

    result = adfuller(series.dropna())
    print(f'📊 ผลการทดสอบ Stationarity สำหรับ {name}:')
    print(f'ADF Statistic: {result[0]:.4f}')
    print(f'p-value: {result[1]:.4f}')
    print('Critical Values:')
    for key, value in result[4].items():
        print(f'   {key}: {value:.4f}')
    return result[1] < 0.05  # คืนค่า True ถ้า stationary

"""สร้าง target variable จากข้อมูลการเทรด"""
def process_trade_targets(df):
    print(f"\n🏗️ เปิดใช้งาน process trade targets") if Steps_to_do else None

    """สร้าง target variable โดยจัดการกรณีไม่มีข้อมูล """
    if df.empty or len(df) < 10:
        print("⚠️ ไม่มีข้อมูล เพียงพอสำหรับสร้าง Target")
        return df

    try:
        # ตรวจสอบคอลัมน์ที่จำเป็น
        required_cols = ['Profit', 'Exit Condition', 'Risk', 'Reward', 'Trade Type']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"⚠️ ไม่พบ คอลัมน์ที่จำเป็น: {missing_cols}")
            return df

        # แปลงคอลัมน์ Profit เป็นตัวเลข
        if not pd.api.types.is_numeric_dtype(df["Profit"]):
            df["Profit"] = pd.to_numeric(df["Profit"], errors='coerce')
            df = df.dropna(subset=["Profit"])

        # ======================================================
        # วิธีที่ 1: สร้าง Target จาก Risk/Reward Ratio
        # ======================================================
        df['RR_Ratio'] = df['Reward'] / df['Risk']

        # ======================================================
        # วิธีที่ 2: สร้าง Target หลักแบบเดิม (Binary)
        # 1 = TP Hit (Win), 0 = SL Hit หรือ Technical Exit (Loss)
        # ======================================================
        conditions_main = [
            (df['Exit Condition'] == 'TP Hit')
        ]
        df['Target'] = np.select(conditions_main, [1], default=0)
        df['Main_Target'] = df['Target']  # เก็บค่าเดิมไว้

        # ======================================================
        # วิธีที่ 3: สร้าง Target แยกสำหรับ Buy และ Sell
        # ======================================================
        
        # Target สำหรับ Buy (1=ชนะ, 0=แพ้, -1=ไม่ใช่ Buy Trade)
        conditions_buy = [
            (df['Trade Type'] == 'Buy') & (df['Exit Condition'] == 'TP Hit'),  # ชนะ
            (df['Trade Type'] == 'Buy') & (df['Exit Condition'] != 'TP Hit')   # แพ้
        ]
        choices_buy = [1, 0]
        df['Target_Buy'] = np.select(conditions_buy, choices_buy, default=-1)  # Default สำหรับไม่ใช่ Buy Trade

        # Target สำหรับ Sell (1=ชนะ, 0=แพ้, -1=ไม่ใช่ Sell Trade)
        conditions_sell = [
            (df['Trade Type'] == 'Sell') & (df['Exit Condition'] == 'TP Hit'),  # ชนะ
            (df['Trade Type'] == 'Sell') & (df['Exit Condition'] != 'TP Hit')   # แพ้
        ]
        choices_sell = [1, 0]
        df['Target_Sell'] = np.select(conditions_sell, choices_sell, default=-1)  # Default สำหรับไม่ใช่ Sell Trade

        # ======================================================
        # ตรวจสอบและทำความสะอาดข้อมูล
        # ======================================================
        valid_trades = df[
            df['Main_Target'].isin([0, 1]) & 
            df['Target_Buy'].isin([-1, 0, 1]) & 
            df['Target_Sell'].isin([-1, 0, 1])
        ].copy()

        print("\n📊 การกระจายของ Target ต่างๆ:")
        print("1. Target หลัก (Binary):")
        print(valid_trades['Main_Target'].value_counts())
        
        print("\n2. Target สำหรับ Buy Trades:")
        print(valid_trades[valid_trades['Target_Buy'] != -1]['Target_Buy'].value_counts())
        
        print("\n3. Target สำหรับ Sell Trades:")
        print(valid_trades[valid_trades['Target_Sell'] != -1]['Target_Sell'].value_counts())

        return valid_trades

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะสร้าง Target: {str(e)}")
        traceback.print_exc()
        return df

"""เลือกฟีเจอร์ที่สำคัญสำหรับโมเดล โดยดูจาก correlation กับ target และ multicollinearity"""
def select_features(trade_df, timeframe):
    print(f"\n🏗️ เปิดใช้งาน select features") if Steps_to_do else None

    # 1. กำหนดรายชื่อ features ที่อาจมีประโยชน์ (จากลักษณะทางเทคนิคและเวลา)
    # รายการนี้ควรรวมคอลัมน์ทั้งหมดใน trade_df ที่คุณคิดว่าเป็น potential feature
    potential_features_list = []

    # เพิ่ม Time Features (ที่คำนวณจาก Entry_DateTime)
    add_if_exists(potential_features_list, trade_df, "Entry_DayOfWeek")
    add_if_exists(potential_features_list, trade_df, "Entry_Hour")
    add_if_exists(potential_features_list, trade_df, "IsWeekend")
    add_if_exists(potential_features_list, trade_df, "IsMorning")
    add_if_exists(potential_features_list, trade_df, "IsAfternoon")
    add_if_exists(potential_features_list, trade_df, "IsEvening")
    add_if_exists(potential_features_list, trade_df, "IsNight")

    # เพิ่ม Price Action Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "Bar_CL")
    add_if_exists(potential_features_list, trade_df, "Bar_CL_OC")
    add_if_exists(potential_features_list, trade_df, "Bar_CL_HL")
    add_if_exists(potential_features_list, trade_df, "Bar_SW")
    add_if_exists(potential_features_list, trade_df, "Bar_TL")
    add_if_exists(potential_features_list, trade_df, "Bar_DTB")
    add_if_exists(potential_features_list, trade_df, "Bar_OSB")
    add_if_exists(potential_features_list, trade_df, "Bar_FVG")
    add_if_exists(potential_features_list, trade_df, "Bar_longwick")

    # เพิ่ม Price Info Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "Price_Range")
    add_if_exists(potential_features_list, trade_df, "Price_Move")
    add_if_exists(potential_features_list, trade_df, "Price_Strangth")

    # เพิ่ม Volume Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "Volume_MA20")
    add_if_exists(potential_features_list, trade_df, "Volume_Spike")

    # เพิ่ม EMA Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "EMA_diff")
    add_if_exists(potential_features_list, trade_df, "MA_Cross")
    add_if_exists(potential_features_list, trade_df, "Price_above_EMA50")
    add_if_exists(potential_features_list, trade_df, "Dist_EMA50")
    add_if_exists(potential_features_list, trade_df, "Dist_EMA100")
    add_if_exists(potential_features_list, trade_df, "Dist_EMA200")

    # เพิ่ม Volatility Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "Rolling_Vol_5")
    add_if_exists(potential_features_list, trade_df, "Rolling_Vol_15")

    # เพิ่ม RSI Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "RSI_signal")
    add_if_exists(potential_features_list, trade_df, "RSI_Overbought")
    add_if_exists(potential_features_list, trade_df, "RSI_Oversold")
    add_if_exists(potential_features_list, trade_df, "RSI_ROC_i2")
    add_if_exists(potential_features_list, trade_df, "RSI_ROC_i4")
    add_if_exists(potential_features_list, trade_df, "RSI_ROC_i6")
    add_if_exists(potential_features_list, trade_df, "RSI_ROC_i8")
    add_if_exists(potential_features_list, trade_df, "RSI_Divergence_i2")
    add_if_exists(potential_features_list, trade_df, "RSI_Divergence_i4")
    add_if_exists(potential_features_list, trade_df, "RSI_Divergence_i6")

    # เพิ่ม MACD Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "MACDh_12_26_9")
    add_if_exists(potential_features_list, trade_df, "MACD_line")
    add_if_exists(potential_features_list, trade_df, "MACD_deep")
    add_if_exists(potential_features_list, trade_df, "MACD_signal")

    # เพิ่ม Stochastic Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "STO_cross")
    add_if_exists(potential_features_list, trade_df, "STO_zone")
    add_if_exists(potential_features_list, trade_df, "STO_overbought")
    add_if_exists(potential_features_list, trade_df, "STO_Oversold")

    # เพิ่ม ADX Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "ADX_Deep")
    add_if_exists(potential_features_list, trade_df, "ADX_zone_15")
    add_if_exists(potential_features_list, trade_df, "ADX_zone_25")
    add_if_exists(potential_features_list, trade_df, "ADX_cross")

    # เพิ่ม ATR Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "ATR_Deep")
    add_if_exists(potential_features_list, trade_df, "ATR_ROC_i2")
    add_if_exists(potential_features_list, trade_df, "ATR_ROC_i4")
    add_if_exists(potential_features_list, trade_df, "ATR_ROC_i6")
    add_if_exists(potential_features_list, trade_df, "ATR_ROC_i8")

    # เพิ่ม BB Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "BB_width")

    # เพิ่ม Support/Resistance (จาก merge ถ้าต้องการใช้)
    add_if_exists(potential_features_list, trade_df, "PullBack_Up")
    add_if_exists(potential_features_list, trade_df, "PullBack_Down")

    add_if_exists(potential_features_list, trade_df, "Ratio_Buy")
    add_if_exists(potential_features_list, trade_df, "Ratio_Sell")

    # เพิ่ม Lag Features ในรายการ potential features
    lag_features = [col for col in trade_df.columns if any(x in col for x in ['_Lag_', '_Return_', '_Change_', '_MA_', '_Std_'])]

    # # 2. กำหนดรายชื่อ features ที่เกี่ยวข้องกับ SL/TP (ที่คำนวณบน trade_df)
    # stop_loss_take_profit_features = [
    #     'Risk', 'Reward', 'RR_Ratio', # ต้องแน่ใจว่าคำนวณบน trade_df แล้ว
    #     'Pct_Risk', 'Pct_Reward'    # ต้องแน่ใจว่าคำนวณบน trade_df แล้ว
    #     # ATR, BB_width อยู่ในกลุ่ม Technical Features ที่ merge มาแล้ว
    # ]

    # all_potential_features = potential_features_list + [
    #     f for f in stop_loss_take_profit_features if f in trade_df.columns
    # ]

    # นำ features ทั้งสองกลุ่มมารวมกัน
    all_potential_features = potential_features_list + lag_features # การตรวจสอบเบื้องต้นถ้าใช้ stop_loss_take_profit_features อาจส่งผลกระทบ

    print(f"ℹ️ Potential features for model input (Pre-Trade Only): {potential_features_list}+{lag_features} = {len(all_potential_features)} features considered.")
    
    # 3. เลือกเฉพาะ features ที่มีอยู่ใน DataFrame และเป็นตัวเลข
    numeric_columns = trade_df.select_dtypes(include=['number']).columns.tolist()
    # กรองอีกครั้งเพื่อให้แน่ใจว่ามีอยู่จริงและเป็นตัวเลข
    available_numeric_features = [
        f for f in all_potential_features if f in numeric_columns
    ]

    # 4. ตรวจสอบว่ามีคอลัมน์ Target หรือไม่
    if 'Target' not in trade_df.columns:
        print("⚠️ ไม่พบ คอลัมน์ Target ในข้อมูล ใช้ features ที่มีทั้งหมดที่เป็นตัวเลข")
        return available_numeric_features

    # 5. คำนวณความสัมพันธ์กับ Target
    print("\n🔍 ความสัมพันธ์กับ Target (ทั้งหมด):")
    # เลือกเฉพาะคอลัมน์ที่เป็น available_numeric_features และ Target ก่อนคำนวณ corr เพื่อป้องกัน error
    cols_for_corr = [f for f in available_numeric_features if f in trade_df.columns] + ['Target']
    if 'Target' in trade_df.columns and all(col in trade_df.columns for col in available_numeric_features):
        correlation_with_target = trade_df[cols_for_corr].corr()['Target'].abs().sort_values(ascending=False)
        print(correlation_with_target)
    else:
        print("ไม่สามารถคำนวณความสัมพันธ์กับ Target ได้ เนื่องจากมีคอลัมน์ไม่ครบถ้วน")
        # ในกรณีนี้ อาจจะ return available_numeric_features ไปก่อน หรือหยุดการทำงาน
        return available_numeric_features # หรือ raise Error

    # 6. กำหนดค่า threshold สำหรับความสัมพันธ์ และเลือก features ที่มีความสัมพันธ์สูงกว่า
    correlation_threshold = 0.05
    high_correlation_threshold_for_multicollinearity = 0.8 # ปรับ threshold สำหรับ multicollinearity ให้สูงขึ้นเล็กน้อย

    # เลือก features ที่มีความสัมพันธ์กับ Target สูงกว่า threshold (ไม่รวม Target ตัวเอง)
    highly_correlated_with_target = correlation_with_target[
        (correlation_with_target > correlation_threshold) &
        (correlation_with_target.index != 'Target')
    ].index.tolist()

    # 7. ตรวจสอบ multicollinearity (ความสัมพันธ์สูงระหว่าง features กันเอง)
    # ตรวจสอบให้แน่ใจว่า highly_correlated_with_target ไม่ว่างเปล่าก่อนสร้าง correlation_matrix
    if len(highly_correlated_with_target) > 1:
        correlation_matrix = trade_df[highly_correlated_with_target].corr().abs()
        # ใช้ NumPy ในการสร้าง upper triangle mask
        upper_triangle = correlation_matrix.where(np.triu(np.ones(correlation_matrix.shape), k=1).astype(bool))

        # หา features ที่มีความสัมพันธ์สูงกับ feature อื่นๆ (เก็บชื่อคอลัมน์ที่ต้องการ drop)
        # จะ drop คอลัมน์ที่ 'any' ค่าในคอลัมน์นั้นใน upper_triangle สูงกว่า threshold
        features_to_drop_due_to_multicollinearity = [
            col for col in upper_triangle.columns if any(upper_triangle[col] > high_correlation_threshold_for_multicollinearity)
        ]

        # 8. สร้างรายชื่อ features สุดท้าย โดยตัด features ที่มีความสัมพันธ์กันเองสูงเกินไปออก
        final_selected_features = [
            f for f in highly_correlated_with_target if f not in features_to_drop_due_to_multicollinearity
        ]

        # แสดง features ที่ถูกตัดออกเนื่องจาก Multicollinearity
        if features_to_drop_due_to_multicollinearity:
            print("\n🚫 Features ถูกตัดออกเนื่องจาก Multicollinearity สูง:", features_to_drop_due_to_multicollinearity)

    else:
        # กรณีที่มี highly_correlated_with_target แค่ 0 หรือ 1 ตัว ไม่ต้องทำ multicollinearity check
        print("\nℹ️ มี Features ที่มีความสัมพันธ์กับ Target ต่ำกว่าหรือเท่ากับ 1 ตัว ไม่ต้องตรวจสอบ Multicollinearity.")
        final_selected_features = highly_correlated_with_target

    # 9. กำหนด features ที่จำเป็นต้องมี และเพิ่มเข้าไปในรายชื่อ หากยังไม่มี
    # ควรใช้ชื่อคอลัมน์ที่ถูกต้องหลังจาก merge และคำนวณแล้ว
    # กำหนด features ที่จำเป็นต้องมี (add_features_in_model) ---
    print("\ืเริ่มขั้นตอนการตรวจสอบ features ที่ต้องใช้จากการทำ analyze cross asset feature importance")
    add_features_in_model = ['Entry_DayOfWeek', 'Entry_Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight'] # ตรวจสอบชื่อคอลัมน์ให้ถูกต้อง
    
    # ตรวจสอบว่ามีไฟล์ pickle อยู่หรือไม่ ถ้ามี ให้โหลดจากไฟล์ ถ้าไม่มี ให้ใช้ hardcode list
    must_have_pickle_path=f'D:\\test_gold\\feature_importance\\{str(timeframe).zfill(3)}_must_have_features.pkl'
    must_have_features_in_model = []
    if os.path.exists(must_have_pickle_path):
        try:
            with open(must_have_pickle_path, 'rb') as f: # ใช้ 'rb' สำหรับ binary read
                must_have_features_in_model = pickle.load(f)
            print(f"\n👍 โหลดรายชื่อ Features ที่จำเป็นจากไฟล์: {must_have_pickle_path} ({len(must_have_features_in_model)} Features)")
        except Exception as e:
            print(f"⚠️ เกิดข้อผิดพลาด ในการโหลดไฟล์ Features ที่จำเป็น {must_have_pickle_path}: {e}")
            # ถ้าโหลดไม่ได้ ให้ใช้ hardcode list แทน
            print("ℹ️ ใช้ Hardcoded must_have_features_in_model เป็นค่าเริ่มต้น")
            must_have_features_in_model = add_features_in_model
    else:
        print(f"\nℹ️ ⚠️ ไม่พบ ไฟล์ Features ที่จำเป็น '{must_have_pickle_path}'. จะใช้ Hardcoded must_have_features_in_model เป็นค่าเริ่มต้น")
        # Hardcoded list เป็นค่าเริ่มต้นเมื่อ⚠️ ไม่พบ ไฟล์
        must_have_features_in_model = add_features_in_model
    
    print("\nfeaturesasset_feature_importance : len ",len(must_have_features_in_model))
    print(must_have_features_in_model)

    for feature in must_have_features_in_model:
        # เพิ่มเฉพาะ feature ที่มีอยู่จริงใน numeric pre-trade features และยังไม่มีใน final list
        if feature not in final_selected_features and feature in available_numeric_features:
            final_selected_features.append(feature)
            print(f"👍 เพิ่ม Feature ที่จำเป็น '{feature}' เข้าไปในรายการ")
        elif feature not in available_numeric_features:
            print(f"⚠️ Feature ที่จำเป็น '{feature}' ⚠️ ไม่พบ ในข้อมูลตัวเลขที่มีอยู่")

    # 10. ตรวจสอบและนำ 'Target' ออกจากรายชื่อ features สุดท้าย (ถ้ามี)
    if 'Target' in final_selected_features:
        final_selected_features.remove('Target')
        print("\n✅ นำคอลัมน์ 'Target' ออกจากรายชื่อ Features แล้ว")

    # 11. แสดง Features สุดท้ายที่เลือกได้
    if not final_selected_features:
        print("\n⚠️ ไม่สามารถเลือก Features ใดๆ ได้จากกระบวนการคัดเลือกอัตโนมัติ")
        print("ℹ️ สาเหตุอาจมาจาก:")
        print("- ความสัมพันธ์กับ Target ต่ำกว่า Threshold")
        print("- มี Multicollinearity สูงมาก")
        print("- จำนวนข้อมูลน้อยเกินไปที่จะคำนวณความสัมพันธ์ได้อย่างแม่นยำ")
        # อาจจะ return available_numeric_features ทั้งหมด หรือ [] ขึ้นอยู่กับว่าต้องการให้โมเดลทำงานอย่างไร
        return [] # หรือ available_numeric_features

    # print(f"\n✅ Final selected features for training: {final_selected_features}")
    # print(f"\n✅ Final selected features for training:")
    # for i, feat in enumerate(final_selected_features, 1):
    #     print(f"{i}. {feat}")

    return final_selected_features

"""เพิ่มชื่อคอลัมน์ใน list ถ้าคอลัมน์นั้นมีอยู่ใน DataFrame"""
def add_if_exists(features_list, df, column_name):
    # print(f"\n🏗️ เปิดใช้งาน add if exists") if Steps_to_do else None

    if column_name in df.columns:
        features_list.append(column_name)
    else:
        print(f"⚠️ Feature '{column_name}' not found in DataFrame. Skipping.")
    return features_list

"""แปลงข้อมูล numpy/pandas ให้เป็นชนิดที่ serialize เป็น JSON ได้"""
def safe_json_serialize(obj):
    # print(f"\n🏗️ เปิดใช้งาน safe json serialize") if Steps_to_do else None
    
    if isinstance(obj, (np.int_, np.intc, np.intp, np.int8,
                    np.int16, np.int32, np.int64, np.uint8,
                    np.uint16, np.uint32, np.uint64)):
        return int(obj)
    elif isinstance(obj, (np.float_, np.float16, np.float32, np.float64)):
        return float(obj)
    elif isinstance(obj, (np.bool_)):
        return bool(obj)
    elif isinstance(obj, (np.ndarray)):
        return obj.tolist()
    elif isinstance(obj, (pd.Timestamp)):
        return obj.isoformat()
    else:
        return str(obj)

"""แยกข้อมูล symbol, timeframe, ฯลฯ จากชื่อไฟล์"""
def parse_filename(name):
    parts = name.split("_")
    pair_tf = parts[0]
    timeframe_str = parts[1]

    name_currency = pair_tf.replace("#", "")
    
    if name_currency == "GOLD":
        base_currency = "GOLD"
        quote_currency = "USD"
    else:
        base_currency = name_currency[:3]
        quote_currency = name_currency[3:]

    tf_value = timeframe_map.get(timeframe_str, None)
    if tf_value is None:
        raise ValueError(f"Unknown timeframe: {timeframe_str}")

    info = symbol_info.get(name_currency)
    if info is None:
        raise ValueError(f"Unknown symbol info for: {name_currency}")

    result = {
        "Base_Currency": base_currency,
        "Quote_Currency": quote_currency,
        "Name_Currency": name_currency,
        "Timeframe_Currency": tf_value,
        "Spread": info["Spread"],
        "Digits": info["Digits"],
        "Points": info["Points"],
        "Swap_Long": info["Swap_Long"],
        "Swap_Short": info["Swap_Short"],
    }

    return result

"""ปัดเศษราคาขึ้น/ลงตามจำนวน digits ที่กำหนด"""
def ceiling_price(value, digits):
    return math.ceil(value/digits) * digits

"""ปัดเศษราคาขึ้น/ลงตามจำนวน digits ที่กำหนด"""
def floor_price(value, digits):
    return math.floor(value/digits) * digits

# ==============================================
# model_management.py
# ฟังก์ชันเกี่ยวกับการโหลด/บันทึกโมเดล, scaler, พารามิเตอร์โมเดล
# ==============================================

"""โหลดโมเดลที่บันทึกไว้จากไฟล์"""
def load_model(modelname, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน load model") if Steps_to_do else None

    model_dir = f"models/{str(timeframe).zfill(3)}_{symbol}"
    model_path = os.path.join(model_dir, f"{modelname}_{str(timeframe).zfill(3)}_{symbol}_trained.pkl")
    features_path = os.path.join(model_dir, f"{modelname}_{str(timeframe).zfill(3)}_{symbol}_features.pkl")

    try:
        # ตรวจสอบว่าโฟลเดอร์และไฟล์มีอยู่จริง
        if not os.path.exists(model_dir):
            print(f"⚠️ ไม่พบ โฟลเดอร์โมเดลสำหรับ symbol {symbol} timeframe {timeframe}")
            return None
        
        if not os.path.exists(model_path):
            print(f"⚠️ ไม่พบ ไฟล์โมเดลที่ {model_path}")
            return None
        
        print(f"กำลังโหลดโมเดลจาก: {model_path}")
        model = joblib.load(model_path)

        # ตรวจสอบว่าเป็นโมเดลที่ใช้ predict ได้หรือไม่ (LGBMClassifier มี predict)
        if not hasattr(model, 'predict'):
            raise ValueError("ไฟล์ที่โหลดมาไม่ใช่โมเดลที่สามารถใช้ทำนายได้")
        
        loaded_features = None
        if os.path.exists(features_path):
            try:
                loaded_features = joblib.load(features_path)
                print(f"✅ โหลด features สำเร็จ (จำนวน {len(loaded_features)} features)")
            except Exception as e:
                print(f"⚠️ เกิดข้อผิดพลาด ขณะโหลดไฟล์ features: {str(e)}")
        else:
            print("⚠️ ไม่พบ ไฟล์ features")
            if hasattr(model, 'feature_name_') and model.feature_name_:
                loaded_features = model.feature_name_
                print(f"ℹ️ ใช้ feature names จากโมเดลที่โหลดมา (จำนวน {len(loaded_features)} features)")
            elif hasattr(model, 'feature_name') and model.feature_name(): # Fallback สำหรับ Booster หรือบางกรณี
                loaded_features = model.feature_name()
                print(f"ℹ️ ใช้ feature names จากเมธอด feature_name() ของโมเดลที่โหลดมา (จำนวน {len(loaded_features)} features)")
            else:
                print("⚠️ ไม่พบ feature names ในไฟล์หรือในโมเดลที่โหลดมา")

        num_trees_info = "N/A"
        if hasattr(model, 'n_estimators_'): # จำนวนรอบ boosting ที่ใช้จริงหลัง early stopping
            num_trees_info = f"{model.n_estimators_} (used)"
        elif hasattr(model, 'n_estimators'): # จำนวนรอบสูงสุดที่ตั้งไว้
            num_trees_info = f"{model.n_estimators} (total configured)"
        elif hasattr(model, 'num_trees'): # กรณีเป็น Booster (เพื่อความเข้ากันได้ย้อนหลังถ้าจำเป็น)
            num_trees_info = f"{model.num_trees()} (Booster API)"

        print(f"✅ โหลดโมเดลสำเร็จ (มี {num_trees_info} trees)")

        model.loaded_features_ = loaded_features # แนบ features เข้าไปกับ object โมเดลที่โหลดมา
        return model

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะโหลดโมเดล: {str(e)}")
        traceback.print_exc()
        return None

"""โหลด Scaler ที่บันทึกไว้จากไฟล์"""
def load_scaler(modelname, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน load scaler") if Steps_to_do else None

    """โหลด Scaler ที่บันทึกไว้ตาม timeframe และ model_name"""
    scaler_dir = f"models/{str(timeframe).zfill(3)}_{symbol}"
    scaler_path = os.path.join(scaler_dir, f"{modelname}_{str(timeframe).zfill(3)}_{symbol}_scaler.pkl") # ใช้ model_name ด้วย

    try:
        if not os.path.exists(scaler_path):
            print(f"⚠️ ไม่พบ ไฟล์ Scaler ที่ {scaler_path}")
            return None
        print(f"กำลังโหลด Scaler จาก: {scaler_path}")
        scaler = joblib.load(scaler_path)
        # ตรวจสอบว่าเป็น Scaler จริงหรือไม่ (optional แต่ดี)
        if not hasattr(scaler, 'transform'):
            raise ValueError("ไฟล์ที่โหลดมาไม่ใช่ Scaler")
        print(f"✅ โหลด Scaler สำเร็จ")
        return scaler
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะโหลด Scaler: {str(e)}")
        traceback.print_exc()
        return None

"""คืนค่า parameter dictionary สำหรับ LightGBM (ปรับตาม class imbalance)"""
def get_lgbm_params(y=None, use_scale_pos_weight=True):
    print(f"\n🏗️ เปิดใช้งาน get lgbm params") if Steps_to_do else None

    """พารามิเตอร์ที่ปรับปรุงแล้วสำหรับ LightGBM"""
    params = {
        'objective': 'binary',
        'metric': ['auc', 'binary_logloss', 'binary_error'],
        'boosting_type': 'gbdt',
        'learning_rate': 0.02,  # ลด learning rate เพื่อความเสถียร
        'num_leaves': 31,       # ลดความซับซ้อนเพื่อป้องกัน overfitting
        'max_depth': -1,        # ไม่จำกัดความลึก (ใช้คู่กับ min_data_in_leaf)
        'min_data_in_leaf': 30, # เพิ่มขึ้นเพื่อลด overfitting
        'feature_fraction': 0.8, # ใช้ 80% ของ features ในแต่ละ tree
        'bagging_fraction': 0.8, # ใช้ 80% ของข้อมูลในแต่ละ iteration
        'bagging_freq': 5,
        'lambda_l1': 0.1,      # L1 regularization
        'lambda_l2': 0.1,      # L2 regularization
        'min_gain_to_split': 0.01, # ป้องกันการ split ที่ไม่สำคัญ
        'max_bin': 255,        # ลดความซับซ้อนของการคำนวณ
        'verbosity': -1,
        'random_state': 42,
        'n_jobs': -1           # ใช้ CPU ทุก core
    }

    # ปรับ class weight โดยอัตโนมัติ
    if y is not None and use_scale_pos_weight:
        class_ratio = np.sum(y == 0) / np.sum(y == 1) if np.sum(y == 1) > 0 else 1
        params['scale_pos_weight'] = min(class_ratio, 10)  # จำกัดค่าสูงสุดที่ 10
        
        # เพิ่ม is_unbalance สำหรับการจัดการ class imbalance อีกทาง
        # params['is_unbalance'] = True # scale_pos_weight และ is_unbalance = True ไว้ในเงื่อนไขเดียวกัน ทำให้เกิดข้อผิดพลาด
    
    return params

"""บันทึก/คำนวณ/ปรับค่า threshold ที่เหมาะสมสำหรับการตัดสินใจเข้าเทรดโดยโมเดล"""
def save_optimal_threshold(symbol, timeframe, threshold):
    print(f"\n🏗️ เปิดใช้งาน save optimal threshold") if Steps_to_do else None

    """บันทึกค่า threshold ที่เหมาะสมลงไฟล์"""
    try:
        os.makedirs("thresholds", exist_ok=True)
        threshold_file = f"thresholds/{symbol}_{timeframe}_optimal_threshold.pkl"
        
        with open(threshold_file, 'wb') as f:
            pickle.dump(threshold, f)
        
        print(f"✅ บันทึกค่า threshold ที่เหมาะสมสำหรับ {symbol} {timeframe}: {threshold:.4f}")
    except Exception as e:
        print(f"⚠️ ไม่สามารถบันทึกค่า threshold: {e}")

def calculate_optimal_threshold(symbol, timeframe, initial_threshold=0.20):
    print(f"\n🏗️ เปิดใช้งาน calculate optimal threshold") if Steps_to_do else None

    """คำนวณค่า threshold ที่เหมาะสมตามประวัติประสิทธิภาพของคู่เงินและ timeframe"""
    # กำหนด path สำหรับไฟล์เก็บค่า threshold
    threshold_file = f"thresholds/{symbol}_{timeframe}_optimal_threshold.pkl"
    
    try:
        # พยายามโหลดค่า threshold ที่บันทึกไว้
        if os.path.exists(threshold_file):
            with open(threshold_file, 'rb') as f:
                optimal_threshold = pickle.load(f)
            print(f"✅ โหลดค่า threshold ที่เหมาะสมสำหรับ {symbol} {timeframe}: {optimal_threshold:.4f}")
            return optimal_threshold
    except Exception as e:
        print(f"⚠️ ไม่สามารถโหลดค่า threshold จากไฟล์: {e}")
    
    # หากไม่มีไฟล์หรือโหลดไม่ได้ ให้ใช้ค่าเริ่มต้น
    print(f"ℹ️ ใช้ค่า threshold เริ่มต้นสำหรับ {symbol} {timeframe}: {initial_threshold:.4f}")
    return initial_threshold

def optimize_threshold_based_on_performance(trade_df, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน optimize threshold based on performance") if Steps_to_do else None

    """ปรับค่า threshold ให้เหมาะสมตาม Win Rate และ Expectancy"""
    try:
        # คำนวณ Win Rate และ Expectancy จาก trade_df
        wins = trade_df[trade_df['Profit'] > 0]
        losses = trade_df[trade_df['Profit'] < 0]
        num_wins = len(wins)
        num_losses = len(losses)
        total = num_wins + num_losses
        
        if total == 0:
            return 0.20  # ค่าเริ่มต้นหากไม่มีข้อมูล
        
        win_rate = num_wins / total
        avg_win = wins['Profit'].mean() if num_wins > 0 else 0
        avg_loss = abs(losses['Profit'].mean()) if num_losses > 0 else 0
        expectancy = (avg_win * win_rate) - (avg_loss * (1 - win_rate))
        
        # ปรับค่า threshold ตามสูตรที่ออกแบบมา
        # ตัวอย่างสูตรปรับค่า (สามารถปรับเปลี่ยนได้ตามความเหมาะสม)
        if win_rate > 0.55 and expectancy > 0:
            # หาก Win Rate สูงและ Expectancy เป็นบวก ลด threshold เพื่อเพิ่มโอกาสเปิดออเดอร์
            new_threshold = max(0.10, 0.20 - (win_rate - 0.55) * 0.5)
        elif win_rate < 0.45 or expectancy < 0:
            # หาก Win Rate ต่ำหรือ Expectancy เป็นลบ เพิ่ม threshold เพื่อกรองสัญญาณที่ดีกว่า
            new_threshold = min(0.40, 0.20 + (0.45 - win_rate) * 0.5)
        else:
            new_threshold = 0.20  # ค่าเดิมหากอยู่ในช่วงกลาง
        
        print(f"ปรับค่า threshold สำหรับ {symbol} {timeframe}: Win Rate={win_rate:.2%}, Expectancy={expectancy:.2f}, New Threshold={new_threshold:.3f}")
        return new_threshold
        
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดในการปรับค่า threshold: {e}")
        return 0.20  # ค่าเริ่มต้นหากเกิดข้อผิดพลาด

# ==============================================
# model_training.py
# ฟังก์ชันเกี่ยวกับการฝึก, ประเมิน, cross-validation, เปรียบเทียบโมเดล
# ==============================================

"""ประเมินผลโมเดลแบบละเอียด (accuracy, auc, f1, confusion matrix, classification report, plot ROC/PR curve)"""
def enhanced_evaluation(model, X_test, y_test, output_folder, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน enhanced evaluation") if Steps_to_do else None

    """การประเมินผลแบบละเอียดพร้อมการแยกไฟล์ตาม timeframe"""

    try:
        print(f"🔍 เริ่มการประเมินผลโมเดลแบบละเอียด symbol {symbol} timeframe {timeframe}...")

        # สร้างโฟลเดอร์ย่อยตาม timeframe ถ้ายังไม่มี
        timeframe_folder = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}")
        os.makedirs(timeframe_folder, exist_ok=True)
        print(f"📁 สร้างโฟลเดอร์สำหรับ timeframe symbol {symbol} timeframe {timeframe} แล้ว")

        # ปรับค่า y_test ให้เป็น binary (0 และ 1)
        y_test_binary = np.where(y_test > 0, 1, 0) # กำหนดให้ 1 และ 2 เป็น 1, 0 เป็น 0

        # ทำนายความน่าจะเป็น (สำหรับ LightGBM อาจต้องใช้ predict_proba)
        if hasattr(model, 'predict_proba'):
            y_probs = model.predict_proba(X_test)[:, 1]
        else:
            y_probs = model.predict(X_test) # สำหรับโมเดลที่ไม่คืนความน่าจะเป็นโดยตรง
            # อาจต้องปรับเกณฑ์ (threshold) ในภายหลังหากจำเป็น

        y_pred = (y_probs > 0.5).astype(int)

        # สร้างรายงานแบบละเอียด
        clf_report = classification_report(y_test_binary, y_pred, output_dict=True)
        for key in clf_report:
            if isinstance(clf_report[key], dict):
                for metric in clf_report[key]:
                    if isinstance(clf_report[key][metric], (int, float)):
                        clf_report[key][metric] = float(clf_report[key][metric])
                    elif metric == 'support':
                        clf_report[key][metric] = int(clf_report[key][metric])

        print("\nClassification Report (Binary Target):")
        print(pd.DataFrame(clf_report).transpose())

        print("📝 กำลังคำนวณ metrics...")
        report = {
            'timeframe': timeframe,
            'accuracy': accuracy_score(y_test_binary, y_pred),
            'auc_roc': roc_auc_score(y_test_binary, y_probs),
            'auc_pr': average_precision_score(y_test_binary, y_probs),
            'f1': f1_score(y_test_binary, y_pred, average='macro'),
            'precision': precision_score(y_test_binary, y_pred, average='macro'),
            'recall': recall_score(y_test_binary, y_pred, average='macro'),
            'confusion_matrix': confusion_matrix(y_test_binary, y_pred),
            'classification_report': clf_report  # ใช้ report ที่แก้ไขแล้ว
        }

        # พล็อต ROC และ Precision-Recall curves
        print("📊 กำลังสร้าง visualization...")
        plt.figure(figsize=(12, 5))

        # ROC Curve
        plt.subplot(1, 2, 1)
        fpr, tpr, _ = roc_curve(y_test_binary, y_probs)
        plt.plot(fpr, tpr, label=f'AUC = {report["auc_roc"]:.3f}')
        plt.plot([0, 1], [0, 1], 'k--')
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title(f'ROC Curve symbol {symbol} timeframe {timeframe}')
        plt.legend()

        # Precision-Recall Curve
        plt.subplot(1, 2, 2)
        precision, recall, _ = precision_recall_curve(y_test_binary, y_probs)
        plt.plot(recall, precision, label=f'AP = {report["auc_pr"]:.3f}')
        plt.xlabel('Recall')
        plt.ylabel('Precision')
        plt.title(f'Precision-Recall Curve symbol {symbol} timeframe {timeframe}')
        plt.legend()

        plt.tight_layout()

        # บันทึกไฟล์ภาพ (ระบุ timeframe ในชื่อไฟล์)
        plot_filename = f'{str(timeframe).zfill(3)}_{symbol}_performance_curves.png'
        plot_path = os.path.join(timeframe_folder, plot_filename)
        plt.savefig(plot_path)
        plt.close()
        print(f"✅ บันทึกกราฟประสิทธิภาพที่: {plot_path}")

        # บันทึกรายงานเป็นไฟล์ CSV (ระบุ timeframe ในชื่อไฟล์)
        report_filename = f'{str(timeframe).zfill(3)}_{symbol}_evaluation_report.csv'
        report_path = os.path.join(timeframe_folder, report_filename)
        pd.DataFrame.from_dict(report['classification_report']).transpose().to_csv(report_path)
        print(f"✅ บันทึกรายงานการประเมินที่: {report_path}")

        print(f"🎯 การประเมินผลสำหรับ symbol {symbol} timeframe {timeframe} เสร็จสมบูรณ์!")
        
        return report

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะประเมินผล symbol {symbol} timeframe {timeframe}: {str(e)}")
        import traceback
        traceback.print_exc()
        
        return None    

"""ฝึกและประเมินโมเดล (LightGBM), ทำ cross-validation, scaling, บันทึกโมเดล, plot feature importance, และสรุปผล"""
def train_and_evaluate(input_model, model_name, train_data, val_data, test_data, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน train and evaluate") if Steps_to_do else None

    """ฟังก์ชันฝึกและประเมินโมเดล"""
    # 1. เตรียมข้อมูล
    X_train, y_train = train_data
    X_val, y_val = val_data
    X_test, y_test = test_data

    print("🔍 ตรวจสอบ Temporal Dependence ในชุด Train/Val/Test")
    # สมมติว่า X_train มีคอลัมน์ DateTime (อาจต้องปรับตามโครงสร้างข้อมูลจริง)
    if 'DateTime' in X_train.columns:
        # ตรวจสอบช่วงเวลาของแต่ละชุด
        print(f"- Train: {X_train['DateTime'].min()} ถึง {X_train['DateTime'].max()}")
        print(f"- Val:   {X_val['DateTime'].min()} ถึง {X_val['DateTime'].max()}")
        print(f"- Test:  {X_test['DateTime'].min()} ถึง {X_test['DateTime'].max()}")
        
        # ตรวจสอบว่าชุด Val อยู่หลังชุด Train และ Test อยู่หลัง Val
        assert X_val['DateTime'].min() >= X_train['DateTime'].max(), "Validation set ต้องอยู่หลัง Training set"
        assert X_test['DateTime'].min() >= X_val['DateTime'].max(), "Test set ต้องอยู่หลัง Validation set"
        print("✅ การเรียงลำดับเวลาของชุดข้อมูลถูกต้อง")
        
        # ตรวจสอบการกระจายตามเวลาของ Target
        plt.figure(figsize=(12, 4))
        plt.scatter(X_train['DateTime'], y_train, alpha=0.1, label='Train')
        plt.scatter(X_val['DateTime'], y_val, alpha=0.1, label='Val')
        plt.scatter(X_test['DateTime'], y_test, alpha=0.1, label='Test')
        plt.title(f'การกระจายของ Target ตามเวลา ({timeframe} {symbol})')
        plt.legend()
        plot_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_target_distribution.png")
        plt.savefig(plot_path)
        plt.close()
        print(f"💾 บันทึกกราฟการกระจาย Target ตามเวลาที่: {plot_path}")
    
    # ตรวจสอบว่ามี Features หรือไม่ ก่อนทำ Scaling
    if X_train.empty or X_val.empty or X_test.empty:
        print("⚠️ ไม่มีข้อมูล Features สำหรับการฝึกโมเดล")
        return None, None # หรือจัดการตามความเหมาะสม

    # 2. Scale Features (จำเป็นสำหรับโมเดลส่วนใหญ่)
    # **ต้องทำ Scaling หลังจากการ Split Data เพื่อป้องกัน Data Leakage**
    print("\n⚙️ กำลัง Scaling Features...")
    scaler = StandardScaler()
    
    # Fit Scaler เฉพาะบน Training Data
    scaler.fit(X_train)

    # Transform Data ทุกชุด
    X_train_scaled = scaler.transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    X_test_scaled = scaler.transform(X_test)

    # แปลงกลับเป็น DataFrame เพื่อให้ Features Names ยังคงอยู่ (Optional แต่มีประโยชน์)
    X_train_scaled = pd.DataFrame(X_train_scaled, columns=X_train.columns, index=X_train.index)
    X_val_scaled = pd.DataFrame(X_val_scaled, columns=X_val.columns, index=X_val.index)
    X_test_scaled = pd.DataFrame(X_test_scaled, columns=X_test.columns, index=X_test.index)
    
    print("✅ Scaling Features เสร็จสิ้น")

    # 3. จัดการ Class Imbalance ด้วย SMOTE (ถ้าจำเป็น)
    # ทำบนข้อมูลที่ Scale แล้วและเฉพาะ Training Data เท่านั้น
    # ... (โค้ด SMOTE เดิม - ถ้าใช้ ควรทำบน X_train_scaled, y_train และใช้ตัวแปรใหม่เช่น X_train_resampled) ...

    # ==============================================
    # <<< ส่วนแก้ไข: สร้างหรือเตรียมโมเดลหลักตรงนี้ >>>
    # ==============================================
    print("-> กำลังเตรียมโมเดล LightGBM หลัก (LGBMClassifier)")
    main_model = None # ใช้ตัวแปรใหม่เพื่อความชัดเจน

    if input_model is None: # ถ้าไม่ได้ส่งโมเดลเข้ามา ให้สร้างโมเดลใหม่
        print("\n🚀 กำลังสร้างโมเดล LGBMClassifier ใหม่")
        params = get_lgbm_params(y=y_train) # ใช้ y_train ก่อน Scale
        print(f"Class Ratio (0:1): {params.get('scale_pos_weight', 1):.2f}:1")
        
        # สร้าง LGBMClassifier Instance
        main_model = lgb.LGBMClassifier(
            **params, # ใช้ parameters ที่กำหนด
            n_estimators=5000, # ตั้งค่าสูงไว้ก่อน แล้วใช้ early stopping
            # random_state=42,
            # n_jobs=-1
        )
        print("✅ สร้างโมเดล LGBMClassifier ใหม่สำเร็จ")

    else: # ถ้าส่งโมเดลเข้ามา ให้ใช้โมเดลนั้น (สมมติว่าเป็น LGBMClassifier Instance ที่โหลดมาแล้ว)
        print("\n🔄 กำลังใช้โมเดล LGBMClassifier ที่ส่งเข้ามา")
        # ตรวจสอบเบื้องต้นว่าเป็นโมเดลที่ใช้ fit ได้หรือไม่
        if not hasattr(input_model, 'fit'):
            print("⚠️ โมเดลที่ส่งเข้ามาไม่มีเมธอด .fit() ไม่สามารถนำมาใช้ได้")
            return None, None # คืน None ถ้าโมเดลไม่ถูกต้อง

        main_model = input_model # ใช้โมเดลที่ส่งเข้ามา
        print("✅ เตรียมโมเดลที่ส่งเข้ามาเสร็จสิ้น")

    # ตรวจสอบว่ามีโมเดลที่พร้อม Fit หรือไม่ ก่อนดำเนินการต่อ
    if main_model is None:
        print("❌ ไม่สามารถเตรียมโมเดลหลักสำหรับการฝึกได้")
        return None, None # คืน None ถ้าโมเดลเป็น None หลังพยายามเตรียม

    # ==============================================
    # <<< ส่วนแก้ไข: ทำการ Fit โมเดลหลักตรงนี้ โดยใช้ main_model >>>
    # ==============================================
    print("⚙️ กำลัง Fit โมเดลหลัก...")
    try:
        # ใช้ main_model ที่สร้างหรือเตรียมไว้แล้ว
        # ใช้ข้อมูลที่ Scale แล้ว (และผ่าน SMOTE ถ้าใช้)
        # ถ้าใช้ SMOTE: main_model.fit(X_train_resampled, y_train_resampled, ...)
        # ถ้าไม่ใช้ SMOTE:
        main_model.fit(
            X_train_scaled, y_train, # ใช้ X_train_scaled และ y_train (ก่อน SMOTE ถ้ามี)
            eval_set=[(X_val_scaled, y_val)], # ใช้ X_val_scaled และ y_val
            eval_metric='auc', # หรือ metric อื่นๆ ที่ต้องการติดตามระหว่างฝึก
            callbacks=[
                lgb.early_stopping(stopping_rounds=200, verbose=-1), # verbose=-1 ปิด log ระหว่างฝึก
                lgb.log_evaluation(100), # log ทุก 100 รอบ
            ]
        )
        print("✅ ฝึกโมเดลหลักสำเร็จ")
        # หลังจาก fit โมเดลหลักแล้ว main_model จะมี trained model อยู่

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะ Fit โมเดล LightGBM หลัก: {str(e)}")
        traceback.print_exc()
        return None, None # คืน None model และ scaler ถ้า Fit ล้มเหลว

    print("-> เสร็จสิ้นการ Fit โมเดล LightGBM หลัก")

    # ตรวจสอบว่าโมเดล Fit สำเร็จและพร้อมใช้งานหรือไม่
    if not hasattr(main_model, 'predict'):
        print("⚠️ โมเดลหลักไม่สามารถฝึกได้สำเร็จ หรือไม่ใช่โมเดลที่ถูกต้องหลัง Fit")
        return None, None

    # ==============================================
    # ส่วนตรวจสอบและจัดเรียง features (ปรับตำแหน่งและ logic เล็กน้อย)
    # ==============================================
    print("-> กำลังเรียกตรวจสอบ features ของโมเดลหลัก")
    # ใช้ Feature Names จากข้อมูล X_train_scaled ที่ใช้ Fit โมเดลหลัก
    features_used_in_training = X_train_scaled.columns.tolist()

    try:
        # LightGBMClassifier หลังจาก Fit จะเก็บ Feature Names ใน attribute feature_name_
        model_feature_names = []
        if hasattr(main_model, 'feature_name_') and main_model.feature_name_:
            model_feature_names = main_model.feature_name_
        elif hasattr(main_model, 'feature_name'): # Fallback สำหรับ lgb.Booster หรือเวอร์ชันเก่า
            model_feature_names = main_model.feature_name()

        # ตรวจสอบว่า Features ในข้อมูลที่ใช้ Fit ตรงกับ Feature Names ในโมเดลหลัง Fit หรือไม่
        # (กรณีทั่วไปควรตรงกัน ถ้าไม่มีปัญหาในการเตรียมข้อมูลหรือการแปลง)
        if model_feature_names and set(model_feature_names) != set(features_used_in_training):
            print("\n⚠️ เตือน : Features ใน Model (หลัง Fit) ไม่ตรงกับ Features ในข้อมูลที่ใช้ Fit!")
            print(f"Features ในโมเดล (หลัง Fit): {len(model_feature_names)} features")
            print(f"Features ในข้อมูลที่ใช้ Fit: {len(features_used_in_training)} features")
            print("ℹ️ ปัญหานี้อาจบ่งชี้ว่าการเตรียมข้อมูล Features หรือการประมวลผลมีปัญหา")
            # ไม่ต้อง reindex ที่นี่แล้ว เพราะข้อมูลถูก Fit ไปแล้ว
            # ปัญหานี้ต้องแก้ที่ขั้นตอนการเตรียมข้อมูล *ก่อน* เข้า train and evaluate

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะตรวจสอบ features ของโมเดลหลัก: {str(e)}")
        traceback.print_exc()
    print("-> เสร็จสิ้นตรวจสอบ features")

    # ==============================================
    # ส่วนเพิ่มเติม: ทดสอบ RandomForest ก่อนฝึก LightGBM (ยังใช้ X_train, y_train ดั้งเดิม)
    # ==============================================
    print("\n" + "="*50)
    print("  การทดสอบเปรียบเทียบกับ RandomForest  ")
    print("="*50)
    print("-> กำลังเรียก test random forest")
    # **ข้อควรระวัง:** test random forest ใช้ X_train, X_test ที่ *ไม่* ได้ Scale!
    # ควรแน่ใจว่า RandomForestClassifier สามารถรับข้อมูลแบบไม่ Scale ได้ หรือปรับให้ Scaler ก่อน Fit ในฟังก์ชันนั้น
    # จากโค้ด test random forest ที่คุณให้มา มัน Fit ด้วยข้อมูล *ไม่ Scale* ซึ่งอาจจะไม่เหมาะสมถ้าคุณต้องการเปรียบเทียบกับ LGBM ที่ Fit ด้วยข้อมูล Scale
    # สำหรับตอนนี้ เราจะเรียกด้วยข้อมูลไม่ Scale ตามโค้ดเดิมของคุณ แต่โปรดทราบว่านี่คือจุดที่อาจต้องการการปรับปรุง
    if not X_train.empty and len(np.unique(y_train)) >= 2:
        rf_importance = test_random_forest(X_train, y_train, X_test, y_test, X_train.columns.tolist(), symbol, timeframe)
    else:
        print("⚠️ ข้อมูลไม่เพียงพอหรือมีเพียงคลาสเดียวสำหรับทดสอบ RandomForest")
        rf_importance = None
    print("-> เสร็จสิ้น test random forest")
    
    # 2. ตรวจสอบการกระจายของคลาส
    print("\n📊 การกระจายของคลาส:")
    print(f"Train - 0: {sum(y_train==0)}, 1: {sum(y_train==1)}")
    print(f"Test - 0: {sum(y_test==0)}, 1: {sum(y_test==1)}")
    
    # 3. คำนวณ class ratio
    class_ratio = len(y_train[y_train==0]) / len(y_train[y_train==1]) if len(y_train[y_train==1]) > 0 else 1
    
    # 4. ทำ Cross-Validation
    print("\n🔍 เริ่มทำ Cross-Validation...")
    X_full = pd.concat([X_train, X_val]) # ใช้ X_train, X_val ดั้งเดิม (ไม่ Scale)
    y_full = pd.concat([y_train, y_val])

    print("-> กำลังเรียก time series cv")
    # **ข้อควรระวัง:** time series cv ต้อง Fit Scaler ใหม่ภายในแต่ละ Fold และ Scale ข้อมูลของ Fold ก่อน Fit โมเดล
    # ถ้า time series cv ไม่ได้ทำ Scaling ภายใน จะเกิดความไม่สอดคล้องกันในการเปรียบเทียบ
    if not X_full.empty and len(np.unique(y_full)) >= 2:
        cv_results = time_series_cv(X_full, y_full, timeframe) # เรียกด้วยข้อมูลไม่ Scale
    else:
        print("⚠️ ข้อมูลไม่เพียงพอหรือมีเพียงคลาสเดียวสำหรับ Time Series CV")
        cv_results = { 'accuracy': 0, 'auc': 0.5, 'f1': 0, 'precision': 0, 'recall': 0 }
    print("-> เสร็จสิ้น time series cv")

    # 5. ตรวจสอบข้อมูล (ซ้ำซ้อนกับเช็คก่อน CV, อาจปรับปรุงได้)
    if len(np.unique(y_train)) < 2: # ตรวจ y_train ดั้งเดิม
        print("⚠️ ข้อมูลฝึกมีเพียงคลาสเดียว ไม่สามารถดำเนินการประเมินต่อได้")
        # คุณควร return ออกจากฟังก์ชันที่นี่เลย ถ้าไม่สามารถฝึกโมเดลได้
        return {
            'model_name': model_name,
            'metrics': None, # ไม่มีการประเมิน Test Set ถ้าฝึกไม่สำเร็จ
            'cv_results': cv_results, # คืนค่า CV ถ้าคำนวณได้
            'timeframe': timeframe,
            'num_trees': 0
        }, None # คืน None scaler ด้วย

    # 7. ประเมินโมเดลหลัก (ใช้ main_model)
    print(f"\n  การประเมินผลโมเดลแบบละเอียด symbol {symbol} timeframe {timeframe}")
    # **ข้อควรระวัง:** enhanced evaluation ต้องใช้ข้อมูล X_test_scaled ครับ
    # ตรวจสอบให้แน่ใจว่า enhanced evaluation ใช้ X_test_scaled และ y_test
    enhanced_metrics = enhanced_evaluation(main_model, X_test_scaled, y_test, output_folder, symbol, timeframe) # ใช้ main_model และ X_test_scaled
    
    if enhanced_metrics is None:
        print(f"⚠️ ไม่สามารถประเมินผลโมเดล symbol {symbol} timeframe {timeframe} ได้ ใช้การประเมินพื้นฐานแทน")
        # Fallback to basic evaluation
        y_pred = input_model.predict(X_test, num_iteration=input_model.best_iteration)
        y_pred_binary = np.round(y_pred)
        metrics = {
            'timeframe': timeframe,
            'accuracy': accuracy_score(y_test, y_pred_binary),
            'auc': roc_auc_score(y_test, y_pred),
            'f1': f1_score(y_test, y_pred_binary),
            'precision': precision_score(y_test, y_pred_binary),
            'recall': recall_score(y_test, y_pred_binary),
            'confusion_matrix': confusion_matrix(y_test, y_pred_binary),
            'auc_pr': None  # ไม่มีค่าในโหมดพื้นฐาน
        }
    else:
        metrics = {
            'timeframe': timeframe,
            'accuracy': enhanced_metrics['accuracy'],
            'auc': enhanced_metrics['auc_roc'],
            'f1': enhanced_metrics['f1'],
            'precision': enhanced_metrics['precision'],
            'recall': enhanced_metrics['recall'],
            'confusion_matrix': enhanced_metrics['confusion_matrix'],
            'auc_pr': enhanced_metrics['auc_pr']
        }
    
    # 8. แสดงผลลัพธ์จาก enhanced evaluation
    if enhanced_metrics:
        print(f"\n📊 ผลการประเมินแบบละเอียด symbol {symbol} timeframe {timeframe}")
        print(f"AUC-ROC: {metrics['auc']:.4f}")
        print(f"AUC-PR: {metrics['auc_pr']:.4f}")
        
        # print("\nClassification Report:")
        # print(pd.DataFrame(enhanced_metrics['classification_report']).transpose())
        
        # แปลงค่าใน report ให้เป็น float ก่อนแสดงผล
        clf_report = enhanced_metrics['classification_report']
        for key in clf_report:
            if isinstance(clf_report[key], dict):
                for metric in clf_report[key]:
                    if isinstance(clf_report[key][metric], (int, float)):
                        clf_report[key][metric] = clf_report[key][metric]
        
        print(pd.DataFrame(clf_report).transpose())
    
    print("\n📌 สรุปผลลัพธ์แบบละเอียด:")
    for metric, value in enhanced_metrics.items():
        if metric not in ['classification_report', 'confusion_matrix']:
            # ตรวจสอบก่อนว่า value เป็นตัวเลขหรือไม่ก่อนใช้ .4f
            if isinstance(value, (int, float)):
                print(f"- {metric.capitalize()}: {value:.4f}")
            else:
                print(f"- {metric.capitalize()}: {value}")
    
    # เก็บ metrics สำหรับคืนค่า (จาก enhanced_metrics)
    metrics = {
        'accuracy': enhanced_metrics.get('accuracy', 0), # ใช้ .get() เผื่อค่าไม่มี
        'auc': enhanced_metrics.get('auc_roc', 0.5),
        'f1': enhanced_metrics.get('f1', 0),
        'precision': enhanced_metrics.get('precision', 0),
        'recall': enhanced_metrics.get('recall', 0),
        'confusion_matrix': enhanced_metrics.get('confusion_matrix'),
        'auc_pr': enhanced_metrics.get('auc_pr', 0.5)
    }

    # 9. บันทึกโมเดล Scaler และ Features
    try:
        model_dir = f"models/{str(timeframe).zfill(3)}_{symbol}"
        os.makedirs(model_dir, exist_ok=True)

        model_path = os.path.join(model_dir, f"{model_name}_{str(timeframe).zfill(3)}_{symbol}_trained.pkl")
        features_path = os.path.join(model_dir, f"{model_name}_{str(timeframe).zfill(3)}_{symbol}_features.pkl")
        scaler_path = os.path.join(model_dir, f"{model_name}_{str(timeframe).zfill(3)}_{symbol}_scaler.pkl")

        # ตรวจสอบว่าโมเดลมี method predict หรือไม่ (main_model คือ LGBMClassifier)
        if not hasattr(main_model, 'predict'):
            print("⚠️ โมเดลหลักหลังฝึกไม่มีเมธอด predict ไม่สามารถบันทึกได้")
        else:
            # บันทึกโมเดล
            print(f"\nกำลังบันทึกโมเดลที่: {model_path}")
            joblib.dump(main_model, model_path) # <--- บันทึก main_model
            print(f"✅ บันทึกโมเดลเรียบร้อย (ขนาด: {os.path.getsize(model_path)/1024:.2f} KB)")

            # บันทึก features
            # Feature names ควรเอามาจากข้อมูลที่ใช้ Fit หลัก
            features_to_save = X_train.columns.tolist() # หรือ features_used_in_training
            print(f"\nกำลังบันทึก features ที่: {features_path}")
            joblib.dump(features_to_save, features_path)
            print(f"✅ บันทึก features เรียบร้อย (จำนวน features: {len(features_to_save)})")

            # บันทึก Scaler
            if scaler is not None:
                print(f"\nกำลังบันทึก Scaler ที่: {scaler_path}")
                joblib.dump(scaler, scaler_path) # <--- บันทึก scaler ที่สร้างและ Fit ไว้ก่อนหน้านี้
                print(f"✅ บันทึก Scaler เรียบร้อย (ขนาด: {os.path.getsize(scaler_path)/1024:.2f} KB)")
            else:
                print("⚠️ Scaler ไม่ได้ถูกสร้าง ไม่บันทึก Scaler")

    except Exception as e:
        print(f"\n⚠️ เกิดข้อผิดพลาด ขณะบันทึกไฟล์โมเดล/scaler/features: {str(e)}")
        traceback.print_exc()
        # เพิ่มการบันทึก error log...

    # 10. บันทึกประวัติการเทรน
    # ... (โค้ดเดิม ใช้ metrics จาก enhanced_metrics) ...
    history_entry = {
        'timestamp': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'),
        'timeframe': timeframe,
        'model': model_name,
        'accuracy': round(metrics['accuracy'],5), # ใช้ค่าจาก metrics ที่รวบรวมไว้
        'auc': round(metrics['auc'],5),
        'num_trees': main_model.n_estimators_ if hasattr(main_model, 'n_estimators_') else 0, # จำนวน Trees จาก LGBMClassifier
        'model_path': model_path if 'model_path' in locals() else 'N/A',
        'data_samples': len(X_train) + len(X_val) + len(X_test)
    }

    history_dir = "training_history"
    os.makedirs(history_dir, exist_ok=True)
    history_file = os.path.join(history_dir, f"{str(timeframe).zfill(3)}_{symbol}_training_history.csv")
    
    try:
        if os.path.exists(history_file):
            history_df = pd.read_csv(history_file)
            new_entry_df = pd.DataFrame([history_entry])
            history_df = pd.concat([history_df, new_entry_df], ignore_index=True)
        else:
            history_df = pd.DataFrame([history_entry])
        
        history_df.to_csv(history_file, index=False, encoding='utf-8-sig')
        print(f"\n📝 บันทึกประวัติการเทรนที่: {history_file}")
    except Exception as e:
        print(f"\n⚠️ เกิดข้อผิดพลาด ขณะบันทึกประวัติ: {str(e)}")

    # Feature Importance (ใช้ main_model ซึ่งคือ LGBMClassifier)
    if hasattr(main_model, 'feature_importances_'): # LGBMClassifier มี feature_importances_
        print("-> กำลังสร้าง Feature Importance")
        # ปรับ plot_feature_importance ให้รับ feature_importances_ และ feature names
        # หรือให้ plot_feature_importance รับ LGBMClassifier ได้โดยตรง

        print(f"✅ พิมพ์ feature ก่อนส่งเข้า plot_feature_importance")
        print(X_train.columns.tolist())
        # for i, feat in enumerate(X_train.columns.tolist(), 1):
        #     print(f"{i}. {feat}")

        importance_df = plot_feature_importance(
            model=main_model, # ส่ง LGBMClassifier instance
            features=X_train.columns.tolist(), # ส่งชื่อ features ดั้งเดิม
            model_name=model_name,
            symbol=symbol,
            timeframe=timeframe,
            output_folder=output_folder
        )
        print("-> เสร็จสิ้น Feature Importance")

        # แสดง Feature Importance ที่สำคัญที่สุด
        if importance_df is not None:
            print("\n🔍 Top 5 Most Important Features (Gain):")
            top_features = importance_df.head(5)
            for idx, row in top_features.iterrows():
                print(f"{row['Feature']}: {row['Gain']:.4f} (Gain), {row['Split']:.4f} (Split)")

    else:
        print("⚠️ โมเดลหลักไม่มี attribute 'feature_importances_' ไม่สามารถสร้าง Feature Importance ได้")
        importance_df = None

    # เปรียบเทียบ Feature Importance (ถ้ามีทั้งของ LGBM และ RF)
    if 'rf_importance' in locals() and rf_importance is not None and importance_df is not None:
        compare_feature_importance(
            lgb_importance=importance_df, # Use the df created above
            rf_importance=rf_importance,
            symbol=symbol,
            timeframe=timeframe,
            output_folder=output_folder
        )
    else:
        print("\n⚠️ ข้อมูล Feature Importance ไม่ครบถ้วน ไม่สามารถเปรียบเทียบได้")

    # 12. แสดงผลลัพธ์ Cross-Validation
    if cv_results is not None: # เช็คว่า cv_results ถูกกำหนดค่าหรือไม่
        print("\n📊 เปรียบเทียบผลลัพธ์:")
        print(f"| Metric      | CV Avg    | Test Set |")
        print(f"|-------------|-----------|----------|")
        print(f"| Accuracy    | {cv_results.get('accuracy', 0):.4f}    | {metrics.get('accuracy', 0):.4f} |") # ใช้ .get()
        print(f"| AUC         | {cv_results.get('auc', 0.5):.4f}    | {metrics.get('auc', 0.5):.4f} |") # ใช้ .get()
        print(f"| F1 Score    | {cv_results.get('f1', 0):.4f}    | {metrics.get('f1', 0):.4f} |") # ใช้ .get()
    else:
        print("\n📊 ไม่สามารถแสดงผลลัพธ์เปรียบเทียบได้ เนื่องจากไม่มีผลลัพธ์ CV")

    print("-> train and evaluate ทำงานเสร็จสิ้น")
    # 13. คืนค่าผลลัพธ์และ Scaler
    # คืน main_model แทน input_model เพราะ main_model คือ instance ที่ถูก Fit แล้ว
    return {
        'model_name': model_name,
        'metrics': metrics,
        'cv_results': cv_results,
        'timeframe': timeframe,
        'num_trees': main_model.n_estimators_ if hasattr(main_model, 'n_estimators_') else 0,
        'feature_importance': importance_df.to_dict() if importance_df is not None else None
    }, scaler # คืน scaler ที่ Fit แล้ว

"""ทำ Time Series Cross-Validation สำหรับข้อมูลอนุกรมเวลา"""
def time_series_cv(X, y, timeframe, n_splits=5):
    print(f"\n🏗️ เปิดใช้งาน time series cv") if Steps_to_do else None

    """Time Series Cross-Validation แบบปรับปรุงสำหรับข้อมูลอนุกรมเวลา"""
    from sklearn.model_selection import TimeSeriesSplit
    import warnings
    
    # ตรวจสอบข้อมูลเบื้องต้น
    if X.empty or y.empty:
        print("⚠️ เกิดข้อผิดพลาด : ข้อมูล X หรือ y ว่างเปล่า")
        return {
            'accuracy': 0,
            'auc': 0.5,
            'f1': 0,
            'precision': 0,
            'recall': 0
        }
    
    if len(X) != len(y):
        print("⚠️ เกิดข้อผิดพลาด : จำนวนข้อมูล X และ y ไม่เท่ากัน")
        return {
            'accuracy': 0,
            'auc': 0.5,
            'f1': 0,
            'precision': 0,
            'recall': 0
        }
    
    print(f"🔁 เริ่มทำ Time Series Cross-Validation (n_splits={n_splits})")
    
    try:
        # ใช้ TimeSeriesSplit สำหรับข้อมูลอนุกรมเวลา
        tscv = TimeSeriesSplit(n_splits=n_splits, test_size=int(len(X)*0.2))  # กำหนด test_size เป็น 20%
        
        metrics = {
            'accuracy': [],
            'auc': [],
            'f1': [],
            'precision': [],
            'recall': []
        }
        
        # เพิ่ม Scaler ในแต่ละ Fold เพื่อป้องกัน Data Leakage
        scalers = []
        
        for fold, (train_idx, val_idx) in enumerate(tscv.split(X), 1):
            print(f"\n📊 Fold {fold}/{n_splits}:")
            print(f"  - Train size: {len(train_idx)} ตัวอย่าง ({(train_idx[-1]+1)/len(X):.1%} ของข้อมูลทั้งหมด)")
            print(f"  - Val size:   {len(val_idx)} ตัวอย่าง")
            
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            # ตรวจสอบ class distribution
            class_dist = pd.Series(y_train).value_counts(normalize=True)
            print(f"  - การกระจายคลาสใน Train: {class_dist.to_dict()}")
            
            if len(class_dist) < 2:
                print(f"⚠️ เตือน : ใน Fold {fold} มีเพียงคลาสเดียวในข้อมูลฝึก ({class_dist.to_dict()})")
                continue
            
            # Scale ข้อมูลในแต่ละ Fold แยกกัน
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_val_scaled = scaler.transform(X_val)
            scalers.append(scaler)  # เก็บ scaler แต่ละ fold (หากต้องการใช้ภายหลัง)
            
            # สร้างและฝึกโมเดล
            try:
                print("🏗️ กำลังสร้างโมเดล...")
                
                model = lgb.LGBMClassifier(
                    **get_lgbm_params(y_train),
                    n_estimators=1000,
                    verbose=-1
                )
                
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    model.fit(
                        X_train_scaled, y_train,
                        eval_set=[(X_val_scaled, y_val)],
                        eval_metric='auc',
                        callbacks=[
                            lgb.early_stopping(stopping_rounds=50, verbose=False),
                            lgb.log_evaluation(0)
                        ]
                    )
                
                print(f"  ✅ สร้างโมเดลสำเร็จ (ใช้ {model.n_estimators_} trees)")
                
                # ทำนายและประเมินผล
                y_pred = model.predict_proba(X_val_scaled)[:, 1]
                y_pred_bin = (y_pred > 0.5).astype(int)
                
                # คำนวณ metrics
                fold_metrics = {
                    'accuracy': accuracy_score(y_val, y_pred_bin),
                    'auc': roc_auc_score(y_val, y_pred),
                    'f1': f1_score(y_val, y_pred_bin, zero_division=0),
                    'precision': precision_score(y_val, y_pred_bin, zero_division=0),
                    'recall': recall_score(y_val, y_pred_bin, zero_division=0)
                }
                
                # บันทึกผลลัพธ์
                for k in metrics:
                    metrics[k].append(fold_metrics[k])
                
                print(f"  📊 ผลลัพธ์ Fold {fold}:")
                print(f"    - Accuracy:  {fold_metrics['accuracy']:.4f}")
                print(f"    - AUC:       {fold_metrics['auc']:.4f}")
                print(f"    - F1 Score:  {fold_metrics['f1']:.4f}")
                print(f"    - Precision: {fold_metrics['precision']:.4f}")
                print(f"    - Recall:    {fold_metrics['recall']:.4f}")
                
            except Exception as e:
                print(f"⚠️ เกิดข้อผิดพลาด ใน Fold {fold}: {str(e)}")
                continue
        
        # คำนวณค่าเฉลี่ย metrics
        avg_metrics = {k: np.mean(v) if v else 0 for k, v in metrics.items()}
        
        print("\n✅ การทำ Time Series Cross-Validation เสร็จสมบูรณ์")
        print("📌 ผลลัพธ์เฉลี่ย:")
        print(f"  - Accuracy:  {avg_metrics['accuracy']:.4f}")
        print(f"  - AUC:       {avg_metrics['auc']:.4f}")
        print(f"  - F1 Score:  {avg_metrics['f1']:.4f}")
        print(f"  - Precision: {avg_metrics['precision']:.4f}")
        print(f"  - Recall:    {avg_metrics['recall']:.4f}")
        
        return avg_metrics
    
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ร้ายแรงใน time series cv: {str(e)}")
        return {
            'accuracy': 0,
            'auc': 0.5,
            'f1': 0,
            'precision': 0,
            'recall': 0
        }

"""ทดสอบ RandomForest เพื่อเปรียบเทียบ feature importance กับ LightGBM"""
def test_random_forest(X_train, y_train, X_test, y_test, features, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน test random forest") if Steps_to_do else None

    """ทดสอบ RandomForest เพื่อเปรียบเทียบ"""
    print("🔍 กำลังทดสอบ RandomForest...")
    from sklearn.ensemble import RandomForestClassifier
    
    # ตรวจสอบว่ามีทั้งสองคลาสหรือไม่
    if len(np.unique(y_train)) < 2:
        print("⚠️ ข้อมูลมีเพียงคลาสเดียว ไม่สามารถฝึก RandomForest ได้")
        return pd.DataFrame({'Feature': features, 'Importance': np.zeros(len(features))})
    
    rf = RandomForestClassifier(n_estimators=100, random_state=42)
    rf.fit(X_train, y_train)
    
    # Feature Importance
    rf_importance = pd.DataFrame({
        'Feature': features,
        'Importance': rf.feature_importances_
    }).sort_values('Importance', ascending=False)
    
    print("\n📊 RandomForest Feature Importance:")
    print(rf_importance.to_string(index=False))
    # print(rf_importance.head(15).to_string(index=False))
    
    # ทำนายและประเมินผล
    y_pred = rf.predict(X_test)
    print("\n📈 RandomForest Performance:")
    print(classification_report(y_test, y_pred))
    
    # บันทึกผลลัพธ์
    rf_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_random_forest_feature_importance.csv")
    rf_importance.to_csv(rf_path, index=False)
    print(f"💾 บันทึก RandomForest importance ที่: {rf_path}")
    
    return rf_importance

"""ตรวจสอบ look-ahead bias ในฟีเจอร์ที่ใช้กับโมเดล"""
def enhanced_look_ahead_check(df, start_index, model_features):
    print("\n🔍 ตรวจสอบ Look-Ahead Bias แบบละเอียด")
    
    # 1. ตรวจสอบ Features ที่มีความเสี่ยงสูง (อาจจะต้องเพิ่ม Feature ที่แก้ไขไปแล้วทั้งหมด)
    high_risk_features = [
        'Volume_MA20', 'Rolling_Vol_5', 'Rolling_Vol_15', 
        'MA_Cross', 'EMA_diff', 'MACD_12_26_9', 'RSI14', # Feature ที่เคยมีปัญหา
        'STOCHk_14_3_3', 'STOCHd_14_3_3', # Stochastic
        'Upper_BB', 'Lower_BB', 'BB_width', # Bollinger Bands
        'ADX_14', 'DMP_14', 'DMN_14', # ADX
        'ATR', # ATR
        'Support', 'Resistance' # SR
        ] 
    # เพิ่ม Rolling MAs/Stds ที่เพิ่งแก้ไข
    for window in [3, 5, 10, 20]:
        high_risk_features.extend([f'Close_MA_{window}', f'Volume_MA_{window}', f'Close_Std_{window}'])

    # กรองให้เหลือเฉพาะ Feature ที่มีอยู่ใน DataFrame และอยู่ใน model_features
    features_to_check = [feat for feat in high_risk_features if feat in df.columns and feat in model_features]
    
    problematic_features = set()
    
    print(f"กำลังตรวจสอบ Features จำนวน {len(features_to_check)} รายการ...")

    for feat in features_to_check:
        # ตรวจสอบ 5 จุดสำคัญ
        check_points = [
            start_index + i * (len(df) - start_index) // 4 for i in range(5)
        ]
        check_points = [idx for idx in check_points if idx >= start_index + 1 and idx < len(df) - 1] # ตรวจสอบจุดที่เหมาะสม

        # ถ้า check_points ว่าง ให้ข้ามไป
        if not check_points:
            print(f"ข้าม Feature {feat}: ไม่มีจุดให้ตรวจสอบ")
            continue
            
        # print(f"\nตรวจสอบ Feature: {feat}") # เปิดคอมเมนต์นี้เพื่อดูว่ากำลังเช็ค Feature ไหน
        
        flagged_this_feature = False
        for idx in check_points:
            current_val = df[feat].iloc[idx]
            next_val = df[feat].iloc[idx+1]

            # ปรับ tolerance ให้เหมาะสมกับแต่ละฟีเจอร์
            if feat == 'Volume_MA20' or 'Volume_MA_' in feat:
                tol = 0.01  # 1% tolerance สำหรับ Volume
            elif feat in ['EMA_diff', 'ATR', 'BB_width']:
                tol = 1e-4 # tolerance เล็กน้อย
            elif 'EMA' in feat or 'MACD' in feat or 'RSI' in feat or 'STOCH' in feat or 'ADX' in feat or 'Dist_' in feat or 'BB_' in feat or 'Support' in feat or 'Resistance' in feat or 'Close_MA_' in feat or 'Close_Std_' in feat:
                # tolerance สำหรับ indicators ทั่วไปและค่าเฉลี่ย/Std ของราคา
                # คำนวณ relative tolerance จากค่าเฉลี่ยของสองค่า หรือ absolute tolerance ถ้าค่าใกล้ศูนย์
                avg_val = (abs(current_val) + abs(next_val)) / 2
                if avg_val < 1e-6: # ถ้าค่าใกล้ศูนย์มาก ใช้ absolute tolerance
                        tol = 1e-6 # absolute tolerance
                else:
                        tol = 1e-5 # relative tolerance 0.001%
            else: # features อื่นๆ เช่น Bar patterns, IsMorning etc. (ซึ่งไม่ควรมี look-ahead)
                tol = 1e-9 # strict tolerance สำหรับค่า discrete หรือ binary

            # ถ้าค่าที่ index i และ i+1 *เท่ากันเป๊ะ* หรือ *ใกล้เคียงกันมากเกินไป* #               # อาจบ่งชี้ว่าค่าที่ index i ได้ใช้ข้อมูลจาก index i+1 ในการคำนวณ
            if np.isclose(current_val, next_val, rtol=tol, atol=tol): # ใช้ทั้ง relative และ absolute tolerance
                problematic_features.add(feat)
                # print(f" ⚠️ พบความใกล้เคียงที่น่าสงสัยสำหรับ Feature '{feat}' ที่ index {idx}: {current_val:.5f} -> {next_val:.5f} (tol={tol})")
                flagged_this_feature = True # Mark ว่า Feature นี้มีปัญหาแล้ว
                break # หยุดเช็ค Feature นี้ที่จุดอื่น เพื่อไม่ให้ print ซ้ำเยอะเกินไป

        # ถ้าเช็คเกอร์เดิมแจ้งเตือน ⚠️เมื่อค่า *ไม่* ใกล้เคียง (your original logic)
        # if not np.isclose(current_val, next_val, rtol=tol):
        #      problematic_features.add(feat)
        #      change_pct = ((next_val-current_val)/current_val)*100 if current_val != 0 else 0
        #      print(f" ⚠️ index {idx}: {current_val:.5f} -> {next_val:.5f} (เปลี่ยนแปลง {change_pct:.2f}%)")

    # 2. แสดงตัวอย่างการคำนวณ Features (5 แถวแรก)
    print("\nตัวอย่างการคำนวณ Features (5 แถวแรกหลังจาก dropna):")
    # ใช้เฉพาะ Feature ที่เราสนใจและอยู่ใน model_features
    sample_features = [feat for feat in ['Close', 'Volume', 'Volume_MA20', 'EMA50', 'EMA200', 'EMA_diff', 'RSI14', 'Upper_BB', 'ATR', 'Support', 'Close_MA_20'] if feat in df.columns and feat in model_features] # macd_line_col, stoch_k_col,  เอาออกก่อน
    if not sample_features:
        print("ไม่มี Features ที่เลือกไว้สำหรับแสดงตัวอย่าง")
        # อาจจะเลือก Features อื่นๆ แทน
        sample_features = [col for col in df.columns if col not in ['Date', 'Time', 'DateTime', 'Open', 'High', 'Low', 'Close', 'Volume']][:10] # เลือก 10 Features แรกที่เหลือ
        print(f"(แสดงตัวอย่าง 10 Features แรก: {sample_features})")

    # หา index เริ่มต้นหลัง dropna
    start_index_after_dropna = df.index[0]

    for i in range(start_index_after_dropna, min(start_index_after_dropna+3, len(df))): # แสดง 10 แถวแรก
        print(f"\n📌 แถวที่ {i} (เวลา: {df['DateTime'].iloc[i]})")
        for feat in sample_features:
            if feat in df.columns:
                    current_val = df[feat].iloc[i]
                    prev_val = df[feat].iloc[i-1] if i > start_index_after_dropna else None # ใช้ค่าที่ index i-1
                    
                    # แสดงค่าปัจจุบัน
                    current_str = f"{current_val:.5f}" if isinstance(current_val, (int, float)) else str(current_val)
                    
                    # แสดงค่าก่อนหน้า (ที่ index i-1)
                    prev_str = f"{prev_val:.5f}" if prev_val is not None and isinstance(prev_val, (int, float)) else "N/A"
                    
                    print(f"{feat}: {current_str} (ก่อนหน้า: {prev_str})")
            
    if problematic_features:
        print("\n❌ พบ Features ที่น่าสงสัยว่าอาจมี Look-Ahead Bias:")
        for feat in problematic_features:
            print(f"- {feat}")
        print("\n⚠️ โปรดตรวจสอบการคำนวณ Feature เหล่านี้อีกครั้ง โดยเฉพาะการใช้ .shift(1) ที่ผลลัพธ์สุดท้าย")
        return False
    else:
        print("\n✅ ไม่พบ Features ที่น่าสงสัยว่ามี Look-Ahead Bias ในจุดที่ตรวจสอบ")
        return True

"""สร้างและบันทึกกราฟ feature importance ของโมเดล"""
def plot_feature_importance(model, features, model_name, symbol, timeframe, output_folder):
    """ฟังก์ชันย่อยสำหรับพล็อตและบันทึก Feature Importance"""
    print(f"-> กำลังสร้าง Feature Importance สำหรับ {model_name} symbol {symbol} timeframe {timeframe}")
    try:
        # ตรวจสอบว่าโมเดลมีการฝึกแล้วและมี booster_ object
        if not hasattr(model, 'booster_'):
            print("⚠️ โมเดลยังไม่ได้ถูกฝึก หรือไม่มี booster_ object สำหรับดึง Feature Importance แบบละเอียด")
            # ลองใช้ feature_importances_ attribute แทน ถ้ามี
            if hasattr(model, 'feature_importances_') and hasattr(model, 'feature_name_'):
                print("ℹ️ ใช้ .feature_importances_ attribute แทน (อาจไม่ใช่ 'gain' หรือ 'split' แยกกัน)")
                importance_data = {
                    'Feature': model.feature_name_,
                    'Importance': model.feature_importances_ # attribute นี้มักจะเป็น gain หรือ split ขึ้นอยู่กับการตั้งค่าตอน fit
                }
                importance_df = pd.DataFrame(importance_data)

                # Normalize importance scores
                if importance_df['Importance'].sum() > 0:
                    importance_df['Importance'] = importance_df['Importance'] / importance_df['Importance'].sum()
                else:
                    importance_df['Importance'] = 0 # ป้องกันหารด้วยศูนย์

                importance_df = importance_df.sort_values('Importance', ascending=False)

                # แสดงผลเฉพาะค่า Importance ที่ได้จาก attribute
                print("\n📊 Feature Importance (Normalized Scores - From Attribute):")
                print(importance_df.to_string(index=False, float_format="%.4f"))

                # บันทึกเป็น CSV
                csv_path = os.path.join(output_folder, f"{model_name}_{str(timeframe).zfill(3)}_{symbol}_feature_importance_attribute.csv")
                importance_df.to_csv(csv_path, index=False)
                print(f"\n💾 บันทึก Feature Importance ละเอียดที่: {csv_path}")

                # พล็อตกราฟเฉพาะ Importance
                plt.figure(figsize=(10, max(6, len(importance_df.head(20))*0.4))) # ปรับขนาดความสูงตามจำนวน features
                top_n = min(20, len(importance_df))
                plot_df = importance_df.head(top_n).sort_values('Importance', ascending=True).copy() # เรียงจากน้อยไปมากเพื่อบาร์ยาวอยู่ด้านบน

                plt.barh(plot_df['Feature'], plot_df['Importance'], color='skyblue')
                plt.title(f'Top {top_n} Feature Importance - {model_name} symbol {symbol} timeframe {timeframe}', fontsize=14)
                plt.xlabel('Normalized Importance Score', fontsize=12)
                plt.tight_layout()

                # บันทึกรูปภาพ
                img_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_feature_importance_attribute.png")
                plt.savefig(img_path, dpi=150, bbox_inches='tight')
                plt.close() # ปิด figure เพื่อไม่ให้แสดงใน console หรือ memory leak
                print(f"💾 บันทึกกราฟ Feature Importance ที่: {img_path}")

                return importance_df # Return DataFrame even if only one type is available


            else:
                print("⚠️ ไม่พบ attribute 'feature_importances_' หรือ 'feature_name_' ในโมเดล ไม่สามารถดึง Feature Importance ได้")
                return None


        # หากมี booster_ object สามารถดึง importance ได้ทั้ง Gain และ Split
        booster = model.booster_

        # สร้าง DataFrame สำหรับ Feature Importance โดยใช้ booster_ object
        importance_data = {
            # ใช้ booster_.feature_name()
            'Feature': booster.feature_name(),
            # ใช้ booster_.feature_importance() methods
            'Gain': booster.feature_importance(importance_type='gain'),
            'Split': booster.feature_importance(importance_type='split')
        }

        # Normalize importance scores
        importance_df = pd.DataFrame(importance_data)
        # Handle potential division by zero if sums are zero
        if importance_df['Gain'].sum() > 0:
            importance_df['Gain'] = importance_df['Gain'] / importance_df['Gain'].sum()
        else:
            importance_df['Gain'] = 0
        if importance_df['Split'].sum() > 0:
            importance_df['Split'] = importance_df['Split'] / importance_df['Split'].sum()
        else:
            importance_df['Split'] = 0


        # เรียงลำดับตาม Gain (หรือ Split ขึ้นอยู่กับว่าต้องการแสดงอะไรเป็นหลัก)
        importance_df = importance_df.sort_values('Gain', ascending=False)

        # แสดงผลใน Console
        print("\n📊 Feature Importance (Normalized Scores - Gain and Split):")
        print(importance_df.to_string(index=False, float_format="%.4f"))

        # บันทึกเป็น CSV
        csv_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_feature_importance.csv")
        importance_df.to_csv(csv_path, index=False)
        print(f"\n💾 บันทึก Feature Importance ละเอียดที่: {csv_path}")

        # พล็อตกราฟแบบ Side-by-Side
        # plt.figure(figsize=(14, 10)) # ไม่จำเป็นเมื่อใช้ subplots

        # เลือกเฉพาะ Top N Features
        top_n = min(20, len(importance_df))
        plot_df_gain = importance_df.head(top_n).sort_values('Gain', ascending=True).copy() # เรียงจากน้อยไปมากสำหรับกราฟ Gain
        plot_df_split = importance_df.head(top_n).sort_values('Split', ascending=True).copy() # เรียงจากน้อยไปมากสำหรับกราฟ Split


        # สร้างกราฟแบบ Side-by-Side (1 แถว, 2 คอลัมน์)
        # ปรับความสูงตามจำนวน features ที่แสดง
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, max(6, top_n*0.4)))

        # กราฟ Gain Importance
        ax1.barh(plot_df_gain['Feature'], plot_df_gain['Gain'], color='skyblue')
        ax1.set_title('Feature Importance (Gain)', fontsize=14)
        ax1.set_xlabel('Normalized Importance Score', fontsize=12)


        # กราฟ Split Importance
        ax2.barh(plot_df_split['Feature'], plot_df_split['Split'], color='salmon')
        ax2.set_title('Feature Importance (Split)', fontsize=14)
        ax2.set_xlabel('Normalized Importance Score', fontsize=12)

        # ทำให้แกน y เหมือนกันทั้งสองกราฟ เพื่อให้ Feature ตรงกัน
        # หา Features ทั้งหมดที่อยู่ใน Top N ของทั้ง Gain และ Split
        all_top_features = list(set(plot_df_gain['Feature'].tolist() + plot_df_split['Feature'].tolist()))
        all_top_features.sort(key=lambda x: importance_df[importance_df['Feature'] == x]['Gain'].iloc[0]) # อาจเรียงตาม Gain เพื่อให้ consistent

        # ตั้งค่า y-ticks ของทั้งสองแกนให้เป็นชื่อ Features ที่รวมไว้
        ax1.set_yticks(np.arange(len(all_top_features)))
        ax1.set_yticklabels(all_top_features)
        ax2.set_yticks(np.arange(len(all_top_features)))
        ax2.set_yticklabels(all_top_features)


        plt.suptitle(f'Top {top_n} Feature Importance - {model_name} symbol {symbol} timeframe {timeframe}',
                    fontsize=16, y=1.02)
        plt.tight_layout(rect=[0, 0, 1, 0.98]) # ปรับ rect เพื่อให้ suptitle ไม่ทับกราฟ

        # บันทึกรูปภาพ
        img_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_feature_importance.png")
        plt.savefig(img_path, dpi=150, bbox_inches='tight')
        plt.close() # ปิด figure
        print(f"💾 บันทึกกราฟ Feature Importance ที่: {img_path}")

        return importance_df # คืนค่า DataFrame

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะสร้าง Feature Importance: {str(e)}")
        traceback.print_exc() # พิมพ์ traceback เพื่อดูรายละเอียดข้อผิดพลาด
        return None

"""เปรียบเทียบ feature importance ระหว่าง LightGBM และ RandomForest"""
def compare_feature_importance(lgb_importance, rf_importance, symbol, timeframe, output_folder):
    print(f"\n🏗️ เปิดใช้งาน compare feature importance") if Steps_to_do else None

    """เปรียบเทียบ Feature Importance ระหว่าง LightGBM และ RandomForest"""
    try:
        # สร้าง DataFrame สำหรับเปรียบเทียบ
        comparison = pd.merge(
            lgb_importance[['Feature', 'Gain']].rename(columns={'Gain': 'LightGBM'}),
            rf_importance[['Feature', 'Importance']].rename(columns={'Importance': 'RandomForest'}),
            on='Feature',
            how='outer'
        ).fillna(0)
        
        # Normalize importance scores
        comparison['LightGBM'] = comparison['LightGBM'] / comparison['LightGBM'].max()
        comparison['RandomForest'] = comparison['RandomForest'] / comparison['RandomForest'].max()
        
        # เรียงลำดับตาม LightGBM importance
        comparison = comparison.sort_values('LightGBM', ascending=False).head(20)
        
        # พล็อตกราฟ
        plt.figure(figsize=(12, 8))
        comparison.set_index('Feature').plot(kind='barh', color=['skyblue', 'salmon'])
        plt.title(f'Feature Importance Comparison symbol {symbol} timeframe {timeframe}', fontsize=14)
        plt.xlabel('Normalized Importance Score', fontsize=12)
        plt.ylabel('Features', fontsize=12)
        plt.legend(title='Model')
        plt.tight_layout()
        
        # บันทึกรูปภาพ
        comp_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_feature_importance_comparison.png")
        plt.savefig(comp_path, dpi=150, bbox_inches='tight')
        plt.close()
        print(f"💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: {comp_path}")
        
        # บันทึกเป็น CSV
        csv_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_feature_importance_comparison.csv")
        comparison.to_csv(csv_path, index=False)
        print(f"💾 บันทึกตารางเปรียบเทียบที่: {csv_path}")
    
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะเปรียบเทียบ Feature Importance: {str(e)}")

# ==============================================
# trade_cycle.py
# ฟังก์ชันเกี่ยวกับการสร้างรายการซื้อขาย (trade cycles) และ logic การ backtest
# ==============================================

"""ตรวจสอบ look-ahead bias และเตรียม features สำหรับโมเดลในแต่ละแถว"""
def check_look_ahead_bias(df, i, model_features, scaler, nan_count, suspect_feature_count, suspect_features):
    """
    ฟังก์ชันสำหรับตรวจสอบ Look-Ahead Bias ที่ปรับปรุงแล้ว และเตรียม Features สำหรับ Model
    """
    # เลือกข้อมูลของแถวปัจจุบันสำหรับ Features ที่โมเดลใช้
    # .loc[[i], ...] จะเลือกแถวที่ i และคง DataFrame Structure รวมถึงชื่อคอลัมน์ไว้
    current_features_data = df.loc[[i], model_features] 
    
    # ตรวจสอบ NaN (ยังคง Logic เดิม)
    nan_features = current_features_data.columns[current_features_data.isna().any()].tolist()
    if nan_features:
        nan_count += 1
        # print(f"⚠️ เตือน : พบ NaN ใน Features: {nan_features} ที่แถว {i}") # อาจจะปิดอันนี้ถ้าแจ้งเตือน ⚠️บ่อยเกินไป
        # เติมค่า NaN ด้วยค่าเฉลี่ยของคอลัมน์นั้นๆ ในข้อมูลทั้งหมด (หรือเลือกวิธีเติมที่เหมาะสมกับคุณ)
        current_features_data = current_features_data.fillna(df[model_features].mean()) 
    
    # Scale Features
    scaled_features_array = None
    try:
        if scaler:
            # scaler.transform คืนค่าเป็น NumPy array
            scaled_features_array = scaler.transform(current_features_data) 
        else:
            # .values คืนค่าเป็น NumPy array
            scaled_features_array = current_features_data.values 
            
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะ scaling features ที่แถว {i}: {str(e)}")
        import traceback
        traceback.print_exc()
        # ในกรณีที่เกิดข้อผิดพลาด ⚠️ ในการ Scale เราควรจัดการให้เหมาะสม
        # เช่น อาจจะข้ามแถวนี้ หรือเติมค่าด้วยวิธีอื่นที่ปลอดภัย
        # แต่เพื่อให้ Backtest ดำเนินต่อไป จะลองใช้ค่า original โดยไม่ scale (อาจมีปัญหากับ Model)
        scaled_features_array = current_features_data.values # Fallback to original values array

    # === แก้ไขตรงนี้: แปลง NumPy array กลับเป็น DataFrame ===
    if scaled_features_array is not None:
        # สร้าง DataFrame ใหม่ โดยใช้ข้อมูลจาก scaled_features_array
        # ใช้ index เดียวกับ current_features_data (ซึ่งคือ [i]) และใช้ model_features เป็นชื่อคอลัมน์
        scaled_features_df = pd.DataFrame(scaled_features_array, index=current_features_data.index, columns=model_features)
    else:
        # กรณีเกิดข้อผิดพลาด ⚠️ ร้ายแรงจน scaled_features_array เป็น None
        scaled_features_df = pd.DataFrame(columns=model_features) # คืน DataFrame ว่างเปล่า (อาจต้องปรับการจัดการ error ใน loop หลัก)

    return current_features_data, scaled_features_df, nan_count, suspect_feature_count, suspect_features

"""สร้างรายการซื้อขาย (trade cycles) โดยใช้โมเดล ML ช่วยตัดสินใจเข้า/ออกเทรด"""
def create_trade_cycles_with_model(df, trained_model=None, scaler=None, model_features=None, model_confidence_threshold=None, rsi_level=35, rsi_level_out=30, stop_loss_atr_multiplier=2.00, take_profit_stop_loss_ratio=1.00, symbol=None, timeframe=None, identifier=None):
    print(f"\n🏗️ เปิดใช้งาน create trade cycles with model") if Steps_to_do else None

    """
    สร้างรายการซื้อขายด้วยเงื่อนไขทางเทคนิค โดยใช้ Model ที่เทรนแล้วช่วยตัดสินใจ
    Args:
        df (pd.DataFrame): DataFrame ที่มีข้อมูล OHLC, Indicators, และ Features
        trained_model: โมเดล ML ที่เทรนแล้ว (e.g., LightGBMClassifier)
        scaler: Scaler ที่ใช้ scale features สำหรับโมเดล (e.g., StandardScaler)
        model_features (list): รายชื่อ features ที่โมเดลคาดหวัง
        model_confidence_threshold (float): เกณฑ์ความน่าจะเป็น (0-1) สำหรับการเข้าเทรด
        ... (parameters อื่นๆ เหมือนเดิม) ...
    """

    # หากไม่ได้ระบุ model_confidence_threshold ให้คำนวณค่าเหมาะสมอัตโนมัติ
    if model_confidence_threshold is None:
        model_confidence_threshold = calculate_optimal_threshold(symbol, timeframe)

    trades = []
    in_trade_buy = False
    in_trade_sell = False
    entry_price_buy = entry_price_sell = None
    entry_time_buy = entry_time_sell = None
    trade_type_buy = trade_type_sell = None

    nan_count = 0
    suspect_feature_count = 0
    suspect_features = set()

    symbol_spread = symbol_info[symbol]["Spread"]
    symbol_digits = symbol_info[symbol]["Digits"]
    symbol_points = symbol_info[symbol]["Points"]

    stats = {
        'buy': {'total': 0, 'profit_sum': 0},
        'sell': {'total': 0, 'profit_sum': 0},
        'day_stats': {i: {'win': 0, 'loss': 0, 'total': 0} for i in range(7)},  # 0=Monday, 6=Sunday
        'hour_stats': {i: {'win': 0, 'loss': 0, 'total': 0} for i in range(24)}
    }

    # ตรวจสอบว่ามี Model, Scaler, Features พร้อมหรือไม่
    # use_model_for_decision = False
    use_model_for_decision = (trained_model is not None and scaler is not None and model_features is not None and len(model_features) > 0)

    if use_model_for_decision:
        print("\n🤖 จะใช้ Model ML ช่วยในการตัดสินใจเข้าเทรด")
        print(f"📊 Features ที่ Model ใช้: {model_features}")
        print(f"🎯 เกณฑ์ความน่าจะเป็น Model (TP Hit): > {model_confidence_threshold:.4f}")
    else:
        print("❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม")

    # กำหนดชื่อไฟล์ log โดยใช้ timeframe
    log_file_name = f"{str(timeframe).zfill(3)}_{symbol}_trade_log_model_enhanced.txt" if timeframe else "trade_log_model_enhanced.txt"

    # เปิดไฟล์สำหรับบันทึกข้อมูล
    with open(log_file_name, "w") as log_file:
        # เริ่ม Loop ตั้งแต่ index ที่พอจะคำนวณ Indicator ต่างๆ ได้ครบ
        # ต้องเผื่อสำหรับ Indicators ที่ต้องใช้ข้อมูลย้อนหลัง และเผื่อสำหรับ SL/TP prev bars (index i-2)
        # ดังนั้น เริ่ม loop ที่ index ที่มากพอ เช่น 200 หรือมากกว่า
        start_index = max(300, df.first_valid_index() or 0) # เลือกค่าที่มากสุดระหว่าง 200 กับ index แรกที่มีข้อมูล

        print("\n🔍 ตรวจสอบ Temporal Dependence สำหรับการเทรด")
        start_time = df['DateTime'].iloc[start_index]
        end_time = df['DateTime'].iloc[-1]
        print(f"- ช่วงเวลาที่จะทำ backtest: {start_time} ถึง {end_time}")
        print(f"- จำนวนแท่งข้อมูลทั้งหมดสำหรับ backtest: {len(df) - start_index}")
        
        # ตรวจสอบความถี่ของการเทรด
        trade_freq = df['DateTime'].diff().value_counts().head(5)
        print("\nความถี่ของช่วงเวลาระหว่างแท่งข้อมูล:")
        print(trade_freq)
        
        # ตรวจสอบความสัมพันธ์ระหว่างเวลาและผลลัพธ์การเทรด (ตัวอย่าง)
        if 'Target' in df.columns:
            hour_profit = df.groupby(df['DateTime'].dt.hour)['Target'].mean()
            print("\nอัตราการชน TP (Win Rate) แยกตามชั่วโมง:")
            print(hour_profit)

        print(f"\n▶️ เริ่ม Backtest จาก index: {start_index} (เพื่อให้ Indicators คำนวณได้ครบ)")

        # ส่วนที่เรียกใช้การตรวจสอบในโค้ด Backtest
        # ...
        # start_index = ... # กำหนด index ที่จะเริ่ม backtest หรือ index แรกหลัง dropna
        # model_features = ... # กำหนด list ของ features ที่ใช้ใน model
        # ...
        print("\n🔍 ตรวจสอบ Look-Ahead Bias ก่อนเริ่ม Backtest")
        try:
            # ตรวจสอบตั้งแต่ index แรกหลัง dropna
            start_index_for_check = df.index[0]
            look_ahead_ok = enhanced_look_ahead_check(df, start_index_for_check, model_features)
            
            if not look_ahead_ok:
                print("\n❌ พบปัญหา Look-Ahead Bias ใน Features ที่ระบุ")
                print("โปรดแก้ไขการคำนวณ Features เหล่านั้นก่อนดำเนินการต่อ")
                # return pd.DataFrame(), {}  # ยกเลิกการ Backtest ถ้าพบปัญหา
                # หรือจะเลือกที่จะดำเนินการต่อแต่มีคำเตือน ⚠️
                # raise ValueError("Look-Ahead Bias detected in features.") # อาจจะ raise Exception เพื่อหยุด Backtest
                # ในตัวอย่างนี้ แค่ print เตือน ⚠️แต่ยังให้โค้ดทำงานต่อได้ (ไม่แนะนำสำหรับการ Backtest จริงจัง)
            else:
                print("\n✅ การตรวจสอบ Look-Ahead Bias เสร็จสมบูรณ์ ไม่มีปัญหาที่ตรวจพบโดยอัตโนมัติ")

        except Exception as e:
            print(f"⚠️ เกิดข้อผิดพลาด ขณะตรวจสอบ Look-Ahead Bias: {str(e)}")
            import traceback
            traceback.print_exc()
            # return pd.DataFrame(), {} # ยกเลิกการ Backtest ถ้าเกิดข้อผิดพลาด ⚠️ ในการตรวจสอบ
            # ในตัวอย่างนี้ แค่ print error แต่ยังให้โค้ดทำงานต่อได้ (ไม่แนะนำสำหรับการ Backtest จริงจัง)

        if use_model_for_decision:
            print(f"\n🤖 จะใช้ Model ML ช่วยในการตัดสินใจเข้าเทรด {timeframe} {symbol}")
            print(f"📊 Features ที่ Model ใช้: {model_features}")
            print(f"🎯 เกณฑ์ความน่าจะเป็น Model (TP Hit): > {model_confidence_threshold:.3f} รอบที่ {identifier} / {NUM_TRAINING_ROUNDS}")
        else:
            print("\n❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม")

        # กำหนดชื่อไฟล์ log โดยใช้ timeframe
        log_file_name = f"{str(timeframe).zfill(3)}_{symbol}_trade_log_model_enhanced.txt" if timeframe else "trade_log_model_enhanced.txt"

        for i in range(start_index, len(df)):
            # current_time refers to the time of the bar 'i' where the potential entry is made at Open[i]
            current_time = pd.to_datetime(df['Date'].iloc[i] + ' ' + df['Time'].iloc[i])
            hour = current_time.hour
            day_of_week = df['Entry_DayOfWeek'].iloc[i] # This can use data from bar i, as DayOfWeek is known

            # --- Check if we have enough previous data for lookback periods ---
            # This check depends on the maximum lookback needed for your indicators/features + SL calculation
            min_lookback = 3 # Minimum bars needed for SL (i-3, i-2, i-1) + features/indicators lookback
            if i < start_index + min_lookback:
                continue # Skip if not enough previous data

            # --- Data from the PREVIOUS completed bar (i-1) ---
            # This is the data available when the decision to trade at Open[i] is made.
            prev_close = df['Close'].iloc[i-1]
            prev_open = df['Open'].iloc[i-1]
            prev_high = df['High'].iloc[i-1]
            prev_low = df['Low'].iloc[i-1]
            prev_volume = df['Volume'].iloc[i-1]
            prev_atr = df['ATR'].iloc[i-1] # Assuming ATR is calculated based on previous bars
            prev_ema50 = df['EMA50'].iloc[i-1]
            prev_ema200 = df['EMA200'].iloc[i-1]
            prev_macd_signal = df['MACD_signal'].iloc[i-1]
            prev_rsi14 = df['RSI14'].iloc[i-1]
            prev_sto_cross = df['STO_cross'].iloc[i-1]
            prev_volume_ma20 = df['Volume_MA20'].iloc[i-1]

            prev_pullback_buy = df['PullBack_Up'].iloc[i-1]
            prev_pullback_sell = df['PullBack_Down'].iloc[i-1]

            prev_ratio_buy = df['Ratio_Buy'].iloc[i-1]
            prev_ratio_sell = df['Ratio_Sell'].iloc[i-1]

            # --- Technical Signals (using data from the PREVIOUS completed bar i-1) ---
            if not in_trade_buy:
                tech_signal_buy = (
                    prev_close > prev_open and # Previous bar closed higher
                    prev_close > prev_ema50 and # prev_close > prev_ema200 and
                    prev_macd_signal == 1.0 and
                    prev_rsi14 > rsi_level and # prev_sto_cross == 1.0 and
                    prev_volume > prev_volume_ma20 * 0.8 and
                    prev_pullback_buy > 0.50 and
                    prev_ratio_buy > take_profit_stop_loss_ratio
                )

                # เงื่อนไขเวลาที่ปรับปรุงแล้ว (ใช้เวลาของแท่งปัจจุบัน i ซึ่งทราบแล้ว)
                time_condition = (6 <= hour < 22)

                # ตรวจสอบสัญญาณเข้าซื้อ (อิงจากข้อมูลแท่ง i-1 และเวลากับวันของแท่ง i)
                if tech_signal_buy and time_condition:

                    # --- ใช้ Model ML ช่วยตัดสินใจ (ถ้ามี) ---
                    model_decision_buy = True # Default: ถ้าไม่ใช้โมเดล ให้ตัดสินใจเข้าตามกฎเดิม
                    prob_win = -1 # Initialize prob_win

                    if use_model_for_decision:
                        try:
                            # ==============================================
                            # ส่วนเพิ่มเติม: ตรวจสอบ Look-Ahead Bias ก่อนทำนาย
                            # ใช้ index 'i' ในการเรียก แต่ check look ahead bias
                            # จะดึงข้อมูลจาก df.iloc[i-1] และก่อนหน้า
                            # ==============================================
                            current_features_data, scaled_features, nan_count, suspect_feature_count, suspect_features = check_look_ahead_bias(
                                df, i, model_features, scaler, nan_count, suspect_feature_count, suspect_features)

                            # ทำนายความน่าจะเป็น
                            prediction_proba = trained_model.predict_proba(scaled_features)[0]
                            prob_win = prediction_proba[1] # ความน่าจะเป็นของ Target=1 (TP Hit)

                            # ตรวจสอบเกณฑ์ความน่าจะเป็น
                            model_decision_buy = (prob_win > model_confidence_threshold)

                            # ==============================================
                            # ส่วนเพิ่มเติม: บันทึกข้อมูลการทำนายเพื่อตรวจสอบ
                            # ==============================================
                            # บันทึกข้อมูลแท่งที่ใช้คำนวณ (แท่ง i-1) และแท่งปัจจุบัน (แท่ง i)
                            # print(f"--- Decision at {df['Date'].iloc[i]} {df['Time'].iloc[i]} (Open of Bar i) ---")
                            # print(f"Using data from bar i-1: {df['Date'].iloc[i-1]} {df['Time'].iloc[i-1]} (Closed)")
                            # print(f"  Close[i-1]: {prev_close}, Open[i-1]: {prev_open}, High[i-1]: {prev_high}, Low[i-1]: {prev_low}")
                            # print(f"  Indicators [i-1]: EMA50={prev_ema50:.2f}, RSI14={prev_rsi14:.2f}, MACD_signal={prev_macd_signal}, STO_cross={prev_sto_cross}")
                            # print(f"  Features for Model (from i-1 and earlier): {current_features_data.iloc[0].to_dict()}")
                            # print(f"Model Probability (Win): {prob_win:.4f}. Threshold: {model_confidence_threshold:.3f}. Decision: {'ENTER' if model_decision_buy else 'SKIP'}")
                            # print(f"Current Bar (i) Open: {df['Open'].iloc[i]}") # Show the open price where entry would occur
                            # print("-" * 20)

                            if i % 100 == 0: # บันทึกทุก 100 แถว
                                log_entry = {
                                    'index_current_bar': i,
                                    'time_current_bar': str(df['Date'].iloc[i] + ' ' + df['Time'].iloc[i]),
                                    'index_features_bar': i-1, # Log which bar's data was used for features
                                    'time_features_bar': str(df['Date'].iloc[i-1] + ' ' + df['Time'].iloc[i-1]),
                                    'prob_win': float(prob_win),
                                    'decision': bool(model_decision_buy),
                                    'features_from_bar_i_minus_1': {k: safe_json_serialize(v) for k, v in current_features_data.iloc[0].to_dict().items()}
                                }
                                with open(f"{str(timeframe).zfill(3)}_{symbol}_prediction_log.json", "a") as log:
                                    log.write(json.dumps(log_entry) + "\n")

                            # (Optional) Log การตัดสินใจของ Model
                            log_file.write(f"[{df['Date'].iloc[i]} {df['Time'].iloc[i]}] BUY Signal Detected based on data up to [{df['Date'].iloc[i-1]} {df['Time'].iloc[i-1]}]. Model Probability (Win): {prob_win:.4f}. Threshold: {model_confidence_threshold:.3f}. Decision: {'ENTER' if model_decision_buy else 'SKIP'}\n")

                        except Exception as e:
                            print(f"⚠️ เกิดข้อผิดพลาด ขณะใช้ Model ทำนาย (Buy) ที่แท่ง {df['Date'].iloc[i]} {df['Time'].iloc[i]}: {str(e)}. ข้ามการใช้ Model.")
                            traceback.print_exc()
                            model_decision_buy = True # กลับไปใช้กฎเดิมถ้า Model มีปัญหา
                            prob_win = -2 # Indicate error

                    # --- ถ้าเงื่อนไขทางเทคนิคและ Model อนุญาต (หรือถ้าไม่ใช้ Model) ---
                    if model_decision_buy:
                        
                        entry_price_buy = df["Open"].iloc[i] + (symbol_spread * symbol_points)
                        entry_time_buy = df["Date"].iloc[i] + " " + df["Time"].iloc[i]
                        trade_type_buy = "Buy"

                        # คำนวณ SL/TP
                        sl_atr = entry_price_buy - stop_loss_atr_multiplier * prev_atr # ใช้ ATR จากแท่ง i-1
                        # SL คำนวณจาก Low ของ 3 แท่งก่อนหน้าแท่งปัจจุบัน i (คือ Low[i-3], Low[i-2], Low[i-1])
                        # นี้คือการใช้ข้อมูลที่ทราบแล้ว ณ เวลาเข้าเทรด Open[i]
                        sl_prev_bars = min(df['Low'].iloc[i-3:i].min(), entry_price_buy - 2*symbol_points)

                        # แสดงข้อมูลแท่งที่ใช้คำนวณ SL_prev_bars
                        # print(f"Calculating SL_prev_bars for entry at {entry_time_buy} @ {entry_price_buy:.5f}")
                        # print(f"Using Low from bars: {df['Time'].iloc[i-3]}, {df['Time'].iloc[i-2]}, {df['Time'].iloc[i-1]}")
                        # print(f"Low values: {df['Low'].iloc[i-3]:.5f}, {df['Low'].iloc[i-2]:.5f}, {df['Low'].iloc[i-1]:.5f}")
                        # print(f"Min Low of these bars: {df['Low'].iloc[i-3:i].min():.5f}")
                        # print(f"Calculated sl_prev_bars: {sl_prev_bars:.5f}")
                        # print("-" * 20)

                        if 'Support' in df.columns and not pd.isna(df['Support'].iloc[i-1]): # ใช้ Support จากแท่ง i-1
                            sl_support = df['Support'].iloc[i-1]
                            sl_price_buy = max(sl_atr, sl_prev_bars, sl_support)
                        else:
                            sl_price_buy = max(sl_atr, sl_prev_bars)
                        
                        sl_price_buy = floor_price(sl_price_buy, symbol_points)

                        tp_price_buy = entry_price_buy + (entry_price_buy - sl_price_buy) * take_profit_stop_loss_ratio

                        tp_price_buy = ceiling_price(tp_price_buy, symbol_points)

                        # if 'Resistance' in df.columns and not pd.isna(df['Resistance'].iloc[i-1]): # ใช้ Resistance จากแท่ง i-1
                        #     tp_resistance = df['Resistance'].iloc[i-1]
                        #     tp_price_buy = min(tp_price_buy, tp_resistance)

                        if tp_price_buy <= entry_price_buy:
                            tp_price_buy = entry_price_buy + 2*symbol_points # ตั้ง TP ขั้นต่ำเพื่อหลีกเลี่ยง TP <= Entry

                        in_trade_buy = True

                        # บันทึกข้อมูล ณ เวลาที่เข้าซื้อ (Features ที่ใช้ทำนาย)
                        # บันทึก features ที่ได้มาจาก check look ahead bias ซึ่งใช้ข้อมูลจาก i-1
                        atr_entry_buy = df['ATR'].iloc[i-1]
                        bb_width_entry_buy = df['BB_width'].iloc[i-1]
                        rsi14_entry_buy = df['RSI14'].iloc[i-1]
                        volatility_entry_buy = df['Volume_MA20'].iloc[i-1]
                        volume_spike_entry_buy = df['Volume_Spike'].iloc[i-1]

                        # คุณอาจต้องการเก็บค่า prob_win ที่ใช้ตัดสินใจเข้า trade นี้ด้วย
                        prob_win_at_entry_buy = prob_win

                        log_message = f"[{df['Date'].iloc[i]} {df['Time'].iloc[i]}] Open Order : trade_{trade_type_buy} : Entry_time_{entry_time_buy} Entry_price_{entry_price_buy:0.5f} Entry_sl_{sl_price_buy:0.5f} Entry_tp_{tp_price_buy:0.5f} Prob_Win_{prob_win_at_entry_buy:.4f}\n"
                        log_file.write(log_message)

            # --- เงื่อนไขการออกจากตำแหน่งซื้อ (Buy) ---
            # (โค้ดส่วนนี้เหมือนเดิม ตรวจสอบ SL/TP/Technical Exit)
            if in_trade_buy and trade_type_buy == "Buy":
                exit_condition = None
                exit_price = None

                # ระดับที่ต้องไปถึงก่อนเลื่อน SL
                high_prev = df["High"].iloc[i-1] if i > 0 else None
                trail_trigger = entry_price_buy + 0.5 * (entry_price_buy - sl_price_buy)

                if high_prev is not None and high_prev >= trail_trigger:
                    if sl_price_buy < entry_price_buy:  # เพื่อไม่ให้ปรับซ้ำซ้อน
                        sl_price_buy = entry_price_buy
                        # สามารถ log ได้ว่า "SL moved to Entry"

                if df["Low"].iloc[i] <= sl_price_buy:
                    exit_price = sl_price_buy
                    exit_condition = "SL Hit"
                elif df["High"].iloc[i] > tp_price_buy:
                    exit_price = tp_price_buy
                    exit_condition = "TP Hit"
                # เงื่อนไขอื่นๆ (Technical Exit)
                elif (df["Close"].iloc[i] < df["EMA50"].iloc[i] if "EMA50" in df.columns else False or # ตรวจสอบคอลัมน์ก่อนใช้
                    df["RSI14"].iloc[i] < rsi_level_out if "RSI14" in df.columns else False): # (df["Close"].iloc[i] < entry_price_buy * 0.998)
                    exit_price = df["Close"].iloc[i]
                    exit_condition = "Technical Exit"

                # ถ้ามีเงื่อนไขออกเกิดขึ้น
                if exit_condition:
                    # ... (ส่วนบันทึก Trade และอัปเดต Stats เหมือนเดิม) ...
                    exit_time = df["Date"].iloc[i] + " " + df["Time"].iloc[i]
                    profit = (exit_price - entry_price_buy) / symbol_points

                    risk_amount = entry_price_buy - sl_price_buy
                    reward_amount = tp_price_buy - entry_price_buy # ใช้ tp_price_buy ที่คำนวณหรือเลือกไว้

                    entry_datetime = pd.to_datetime(entry_time_buy)
                    entry_hour = entry_datetime.hour
                    entry_day = entry_datetime.dayofweek

                    log_message = f"time_{df['Date'].iloc[i]} {df['Time'].iloc[i]} op_{df['Open'].iloc[i]} cl_{df['Close'].iloc[i]} : trade_{trade_type_buy} : Entry_time_{entry_time_buy} Entry_price_{entry_price_buy:0.5f} Entry_sl_{sl_price_buy:0.5f} Entry_tp_{tp_price_buy:0.5f} : Exit_time_{exit_time} Exit_price_{exit_price:0.5f} : Condition_{exit_condition} Profit_{profit}\n"
                    log_file.write(log_message)

                    trades.append([
                        entry_time_buy, entry_price_buy,
                        exit_time, exit_price,
                        profit, trade_type_buy,
                        entry_hour, entry_day,
                        exit_condition,
                        sl_price_buy, tp_price_buy,
                        risk_amount, reward_amount,
                        atr_entry_buy,
                        bb_width_entry_buy,
                        rsi14_entry_buy,
                        (entry_price_buy - sl_price_buy) / entry_price_buy if risk_amount > 0 else 0, # % Risk (ป้องกันหารด้วยศูนย์)
                        (tp_price_buy - entry_price_buy) / entry_price_buy if reward_amount > 0 else 0, # % Reward (ป้องกันหารด้วยศูนย์)
                        volatility_entry_buy, volume_spike_entry_buy
                    ])

                    # อัปเดตสถิติ
                    stats['buy']['total'] += 1
                    stats['buy']['profit_sum'] += profit
                    stats['day_stats'][entry_day]['total'] += 1
                    stats['hour_stats'][entry_hour]['total'] += 1
                    if profit > 0:
                        stats['day_stats'][entry_day]['win'] += 1
                        stats['hour_stats'][entry_hour]['win'] += 1
                    elif profit < 0:
                        stats['day_stats'][entry_day]['loss'] += 1
                        stats['hour_stats'][entry_hour]['loss'] += 1

                    in_trade_buy = False # ออกจากตำแหน่ง

            # --- เงื่อนไขการเข้าขาย (Sell) ---
            if not in_trade_sell:
                tech_signal_sell = (
                    prev_close < prev_open and
                    prev_close < prev_ema50 and # prev_close < prev_ema200 and
                    prev_macd_signal == -1.0 and
                    prev_rsi14 < (100-rsi_level) and # prev_sto_cross == -1.0 and
                    prev_volume > prev_volume_ma20 * 0.8 and
                    prev_pullback_sell > 0.50 and
                    prev_ratio_sell > take_profit_stop_loss_ratio
                )

                time_condition = (6 <= hour < 22)

                if tech_signal_sell and time_condition:

                    model_decision_sell = True
                    prob_win = -1

                    if use_model_for_decision:
                        try:
                            current_features_data, scaled_features, nan_count, suspect_feature_count, suspect_features = check_look_ahead_bias(
                                df, i, model_features, scaler, nan_count, suspect_feature_count, suspect_features)

                            prediction_proba = trained_model.predict_proba(scaled_features)[0]
                            prob_win = prediction_proba[1]

                            model_decision_sell = (prob_win > model_confidence_threshold)
                            
                            # print(f"--- Decision at {df['Date'].iloc[i]} {df['Time'].iloc[i]} (Open of Bar i) ---")
                            # print(f"Using data from bar i-1: {df['Date'].iloc[i-1]} {df['Time'].iloc[i-1]} (Closed)")
                            # print(f"  Close[i-1]: {prev_close}, Open[i-1]: {prev_open}, High[i-1]: {prev_high}, Low[i-1]: {prev_low}")
                            # print(f"  Indicators [i-1]: EMA50={prev_ema50:.2f}, RSI14={prev_rsi14:.2f}, MACD_signal={prev_macd_signal}, STO_cross={prev_sto_cross}")
                            # print(f"  Features for Model (from i-1 and earlier): {current_features_data.iloc[0].to_dict()}")
                            # print(f"Model Probability (Win): {prob_win:.4f}. Threshold: {model_confidence_threshold:.3f}. Decision: {'ENTER' if model_decision_sell else 'SKIP'}")
                            # print(f"Current Bar (i) Open: {df['Open'].iloc[i]}")
                            # print("-" * 20)

                            if i % 100 == 0:
                                log_entry = {
                                    'index_current_bar': i,
                                    'time_current_bar': str(df['Date'].iloc[i] + ' ' + df['Time'].iloc[i]),
                                    'index_features_bar': i-1,
                                    'time_features_bar': str(df['Date'].iloc[i-1] + ' ' + df['Time'].iloc[i-1]),
                                    'prob_win': float(prob_win),
                                    'decision': bool(model_decision_sell),
                                    'features_from_bar_i_minus_1': {k: safe_json_serialize(v) for k, v in current_features_data.iloc[0].to_dict().items()}
                                }
                                with open(f"{str(timeframe).zfill(3)}_{symbol}_prediction_log.json", "a") as log:
                                    log.write(json.dumps(log_entry) + "\n")

                            log_file.write(f"[{df['Date'].iloc[i]} {df['Time'].iloc[i]}] sell Signal Detected based on data up to [{df['Date'].iloc[i-1]} {df['Time'].iloc[i-1]}]. Model Probability (Win): {prob_win:.4f}. Threshold: {model_confidence_threshold:.3f}. Decision: {'ENTER' if model_decision_sell else 'SKIP'}\n")

                        except Exception as e:
                            print(f"⚠️ เกิดข้อผิดพลาด ขณะใช้ Model ทำนาย (sell) ที่แท่ง {df['Date'].iloc[i]} {df['Time'].iloc[i]}: {str(e)}. ข้ามการใช้ Model.")
                            traceback.print_exc()
                            model_decision_sell = True
                            prob_win = -2

                    if model_decision_sell:
                        entry_price_sell = df["Open"].iloc[i]
                        entry_time_sell = df["Date"].iloc[i] + " " + df["Time"].iloc[i]
                        trade_type_sell = "Sell"

                        sl_atr = entry_price_sell + stop_loss_atr_multiplier * prev_atr
                        sl_prev_bars = max(df['High'].iloc[i-3:i].max(), entry_price_sell + 2*symbol_points)

                        # print(f"Calculating SL_prev_bars for entry at {entry_time_sell} @ {entry_price_sell:.5f}")
                        # print(f"Using High from bars: {df['Time'].iloc[i-3]}, {df['Time'].iloc[i-2]}, {df['Time'].iloc[i-1]}")
                        # print(f"High values: {df['High'].iloc[i-3]:.5f}, {df['High'].iloc[i-2]:.5f}, {df['High'].iloc[i-1]:.5f}")
                        # print(f"Max Low of these bars: {df['High'].iloc[i-3:i].max():.5f}")
                        # print(f"Calculated sl_prev_bars: {sl_prev_bars:.5f}")
                        # print("-" * 20)

                        if 'Resistance' in df.columns and not pd.isna(df['Resistance'].iloc[i-1]):
                            sl_resistance = df['Resistance'].iloc[i-1]
                            sl_price_sell = min(sl_atr, sl_prev_bars, sl_resistance) + (symbol_spread * symbol_points)
                        else:
                            sl_price_sell = min(sl_atr, sl_prev_bars) + (symbol_spread * symbol_points)

                        sl_price_sell = ceiling_price(sl_price_sell, symbol_points)

                        tp_price_sell = entry_price_sell - (sl_price_sell - entry_price_sell) * take_profit_stop_loss_ratio

                        tp_price_sell = floor_price(tp_price_sell, symbol_points)

                        # if 'Support' in df.columns and not pd.isna(df['Support'].iloc[i-1]):
                        #     tp_support = df['Support'].iloc[i-1]
                        #     tp_price_sell = max(tp_price_sell, tp_support)

                        if tp_price_sell >= entry_price_sell:
                            tp_price_sell = entry_price_sell - 2*symbol_points

                        in_trade_sell = True

                        atr_entry_sell = df['ATR'].iloc[i-1]
                        bb_width_entry_sell = df['BB_width'].iloc[i-1]
                        rsi14_entry_sell = df['RSI14'].iloc[i-1]
                        volatility_entry_sell = df['Volume_MA20'].iloc[i-1]
                        volume_spike_entry_sell = df['Volume_Spike'].iloc[i-1]

                        prob_win_at_entry_sell = prob_win

                        log_message = f"[{df['Date'].iloc[i]} {df['Time'].iloc[i]}] Open Order : trade_{trade_type_sell} : Entry_time_{entry_time_sell} Entry_price_{entry_price_sell:0.5f} Entry_sl_{sl_price_sell:0.5f} Entry_tp_{tp_price_sell:0.5f} Prob_Win_{prob_win_at_entry_sell:.4f}\n"
                        log_file.write(log_message)

            if in_trade_sell and trade_type_sell == "Sell":
                exit_condition = None
                exit_price = None

                # ระดับที่ต้องไปถึงก่อนเลื่อน SL
                low_prev = df["Low"].iloc[i-1] if i > 0 else None
                trail_trigger = entry_price_sell - 0.5 * (sl_price_sell - entry_price_sell)

                if low_prev is not None and low_prev + (symbol_spread * symbol_points) <= trail_trigger:
                    if sl_price_sell > entry_price_sell:  # เพื่อไม่ให้ปรับซ้ำซ้อน
                        sl_price_sell = entry_price_sell
                        # สามารถ log ได้ว่า "SL moved to Entry"

                if df["High"].iloc[i] + (symbol_spread * symbol_points) >= sl_price_sell:
                    exit_price = sl_price_sell
                    exit_condition = "SL Hit"
                elif df["Low"].iloc[i] + (symbol_spread * symbol_points) < tp_price_sell:
                    exit_price = tp_price_sell
                    exit_condition = "TP Hit"
                elif (df["Close"].iloc[i] > df["EMA50"].iloc[i] if "EMA50" in df.columns else False or
                    df["RSI14"].iloc[i] > (100-rsi_level_out) if "RSI14" in df.columns else False): # (df["Close"].iloc[i] > entry_price_sell * 1.002)
                    exit_price = df["Close"].iloc[i] + (symbol_spread * symbol_points)
                    exit_condition = "Technical Exit"

                if exit_condition:
                    exit_time = df["Date"].iloc[i] + " " + df["Time"].iloc[i]
                    profit = (entry_price_sell - exit_price) / symbol_points

                    risk_amount = sl_price_sell - entry_price_sell
                    reward_amount = entry_price_sell - tp_price_sell

                    entry_datetime = pd.to_datetime(entry_time_sell)
                    entry_hour = entry_datetime.hour
                    entry_day = entry_datetime.dayofweek

                    log_message = f"time_{df['Date'].iloc[i]} {df['Time'].iloc[i]} op_{df['Open'].iloc[i]} cl_{df['Close'].iloc[i]} : trade_{trade_type_sell} : Entry_time_{entry_time_sell} Entry_price_{entry_price_sell:0.5f} Entry_sl_{sl_price_sell:0.5f} Entry_tp_{tp_price_sell:0.5f} : Exit_time_{exit_time} Exit_price_{exit_price:0.5f} : Condition_{exit_condition} Profit_{profit:0.5f}\n"
                    log_file.write(log_message)

                    trades.append([
                        entry_time_sell, entry_price_sell,
                        exit_time, exit_price,
                        profit, trade_type_sell,
                        entry_hour, entry_day,
                        exit_condition,
                        sl_price_sell, tp_price_sell,
                        risk_amount, reward_amount,
                        atr_entry_sell,
                        bb_width_entry_sell,
                        rsi14_entry_sell,
                        (sl_price_sell - entry_price_sell) / entry_price_sell if risk_amount > 0 else 0,
                        (entry_price_sell - tp_price_sell) / entry_price_sell if reward_amount > 0 else 0,
                        volatility_entry_sell, volume_spike_entry_sell
                    ])

                    stats['sell']['total'] += 1
                    stats['sell']['profit_sum'] += profit
                    stats['day_stats'][entry_day]['total'] += 1
                    stats['hour_stats'][entry_hour]['total'] += 1
                    if profit > 0:
                        stats['day_stats'][entry_day]['win'] += 1
                        stats['hour_stats'][entry_hour]['win'] += 1
                    elif profit < 0:
                        stats['day_stats'][entry_day]['loss'] += 1
                        stats['hour_stats'][entry_hour]['loss'] += 1

                    in_trade_sell = False

    # ... (ส่วนท้ายฟังก์ชัน create_trade_cycles เหมือนเดิม) ...
    TRADE_COLUMNS = [
        "Entry Time", "Entry Price", "Exit Time", "Exit Price",
        "Profit", "Trade Type", 
        "Entry Hour", "Entry Day", 
        "Exit Condition",
        "SL Price", "TP Price",
        "Risk", "Reward",
        "ATR at Entry", 
        "BB Width at Entry", 
        "RSI14 at Entry",
        "Pct_Risk", 
        "Pct_Reward",
        "Volume MA20 at Entry", "Volume Spike at Entry"
        # เพิ่มคอลัมน์ตาม Features ที่คุณบันทึกไว้
    ]

    trade_df = pd.DataFrame(trades, columns=TRADE_COLUMNS)
    # print(trade_df[trade_df["Profit"] > 0.0][["Trade Type","Profit"]])

    # ==============================================
    # ส่วนเพิ่มเติม: สรุปการตรวจสอบ Look-Ahead Bias
    # ==============================================
    print("\n🔍 สรุปการตรวจสอบ Look-Ahead Bias")
    print(f"- จำนวนการทำนายทั้งหมด: {len(df) - start_index}")
    print(f"- จำนวนครั้งที่พบ NaN ใน Features: {nan_count}")
    print(f"- จำนวนครั้งที่พบ Features อาจมีปัญหา: {suspect_feature_count}")
    
    if suspect_feature_count > 0:
        print("\n⚠️ คำเตือน ⚠️: พบ Features ที่อาจมี Look-Ahead Bias")
        print("โปรดตรวจสอบ Features ต่อไปนี้:")
        for feat in suspect_features:
            print(f"- {feat}")
    else:
        print("✅ ไม่พบ ปัญหาการใช้ข้อมูลจากอนาคต (Look-Ahead Bias)")

    TRADE_COLUMNS = [
        "Entry Time", "Entry Price", "Exit Time", "Exit Price",
        "Profit", "Trade Type", 
        # ... คอลัมน์เดิม ...
    ]

    return trade_df, stats

"""คำนวณ win rate และ expectancy จากชุด trade"""
def calculate_stats(subset):
    wins = subset[subset['Profit'] > 0]
    losses = subset[subset['Profit'] < 0]
    # total = len(subset)
    num_wins = len(wins)
    num_losses = len(losses)
    total = num_wins + num_losses

    win_rate = (num_wins / total) * 100 if total > 0 else 0
    avg_win = wins['Profit'].mean() if num_wins > 0 else 0
    avg_loss = abs(losses['Profit'].mean()) if num_losses > 0 else 0
    expectancy = (avg_win * (win_rate / 100)) - (avg_loss * (1 - (win_rate / 100)))
    return {"win_rate": round(win_rate, 2), "expectancy": round(expectancy, 3)}

"""วิเคราะห์ win rate และ expectancy แยกตามประเภทการเทรด (buy/sell/all)"""
def analyze_trade_performance(trades_df):
    print(f"\n🏗️ เปิดใช้งาน analyze trade performance") if Steps_to_do else None

    """วิเคราะห์ผลการเทรดเพื่อหา Win Rate และ Expectancy แยกตามประเภท"""
    analysis = {}

    # 1. วิเคราะห์ Buy Trades
    buy_trades = trades_df[trades_df['Trade Type'] == 'Buy']
    analysis['buy'] = calculate_stats(buy_trades)

    # 2. วิเคราะห์ Sell Trades
    sell_trades = trades_df[trades_df['Trade Type'] == 'Sell']
    analysis['sell'] = calculate_stats(sell_trades)

    # 3. วิเคราะห์ Buy + Sell Trades
    analysis['buy_sell'] = calculate_stats(trades_df)

    return analysis

# ==============================================
# analysis.py
# ฟังก์ชันวิเคราะห์ผลลัพธ์, SL/TP, เวลา, รายงาน, cross-asset feature importance
# ==============================================

"""วิเคราะห์ประสิทธิภาพของ Stop Loss/Take Profit (SL/TP) และสรุปสถิติที่เกี่ยวข้อง"""
def analyze_sl_tp_performance(trade_df):
    print(f"\n🏗️ เปิดใช้งาน analyze sl tp performance") if Steps_to_do else None

    """วิเคราะห์ประสิทธิภาพของ SL/TP"""
    if trade_df.empty:
        print("⚠️ ไม่มีข้อมูล การซื้อขายสำหรับวิเคราะห์ SL/TP")
        return
    
    # ตรวจสอบว่ามีคอลัมน์ที่จำเป็นหรือไม่
    required_cols = ['Exit Condition', 'Profit']
    if not all(col in trade_df.columns for col in required_cols):
        print("⚠️ ไม่พบ คอลัมน์ที่จำเป็นสำหรับการวิเคราะห์ SL/TP")
        return
    
    # คำนวณอัตราการชน SL vs TP
    sl_hits = len(trade_df[trade_df['Exit Condition'] == "SL Hit"])
    tp_hits = len(trade_df[trade_df['Exit Condition'] == "TP Hit"])
    tech_exits = len(trade_df[trade_df['Exit Condition'] == "Technical Exit"])
    total_trades = len(trade_df)
    
    print("\n📊 สถิติการทำงานของ SL/TP:")
    print(f"{'='*50}")
    print(f"{'ประเภทการออก':<20}{'จำนวน':<10}{'อัตราส่วน':<10}")
    print(f"{'-'*50}")
    print(f"{'TP Hit':<20}{tp_hits:<10}{tp_hits/total_trades:.2%}")
    print(f"{'SL Hit':<20}{sl_hits:<10}{sl_hits/total_trades:.2%}")
    print(f"{'Technical Exit':<20}{tech_exits:<10}{tech_exits/total_trades:.2%}")
    print(f"{'SL + Tech Exit':<20}{sl_hits+tech_exits:<10}{(sl_hits+tech_exits)/total_trades:.2%}")
    print(f"{'='*50}")
    
    # วิเคราะห์กำไรเฉลี่ย
    if tp_hits > 0:
        avg_profit_tp = trade_df[trade_df['Exit Condition'] == "TP Hit"]['Profit'].mean()
        print(f"กำไรเฉลี่ยเมื่อ TP Hit: {avg_profit_tp:.2f}")
        
    if sl_hits > 0:
        avg_loss_sl = trade_df[trade_df['Exit Condition'] == "SL Hit"]['Profit'].mean()
        print(f"ขาดทุนเฉลี่ยเมื่อ SL Hit: {avg_loss_sl:.2f}")
    
    if tech_exits > 0:
        avg_profit_tech = trade_df[trade_df['Exit Condition'] == "Technical Exit"]['Profit'].mean()
        print(f"กำไร/ขาดทุนเฉลี่ยเมื่อออกด้วยสัญญาณเทคนิค: {avg_profit_tech:.2f}")
    
    # คำนวณ Risk/Reward Ratio
    if sl_hits > 0 and tp_hits > 0:
        avg_risk = abs(avg_loss_sl)
        avg_reward = avg_profit_tp
        rr_ratio = avg_reward / avg_risk
        print(f"อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:{rr_ratio:.2f}")

    # เพิ่มการวิเคราะห์ผลรวม SL + Technical Exit
    if (sl_hits + tech_exits) > 0:
        combined_loss = trade_df[trade_df['Exit Condition'].isin(["SL Hit", "Technical Exit"])]['Profit'].mean()
        combined_rr_ratio = avg_reward / abs(combined_loss)
        print(f"\nผลรวม SL + Technical Exit:")
        print(f"- จำนวนเทรดรวม: {sl_hits + tech_exits}")
        print(f"- อัตราส่วน: {(sl_hits + tech_exits)/total_trades:.2%}")
        print(f"- กำไร/ขาดทุนเฉลี่ย: {combined_loss:.2f}")
        print(f"- อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:{combined_rr_ratio:.2f}")

    # เพิ่มในส่วนวิเคราะห์กำไรเฉลี่ย
    if tech_exits > 0:
        tech_profits = trade_df[trade_df['Exit Condition'] == "Technical Exit"]['Profit']
        avg_profit_tech = tech_profits.mean()
        winning_tech = len(tech_profits[tech_profits > 0])
        losing_tech = len(tech_profits[tech_profits <= 0])
        
        print(f"\nการออกด้วยสัญญาณเทคนิค:")
        print(f"- กำไรเฉลี่ยเมื่อชน TP: {tech_profits[tech_profits > 0].mean():.2f}")
        print(f"- ขาดทุนเฉลี่ยเมื่อชน SL: {tech_profits[tech_profits <= 0].mean():.2f}")
        print(f"- อัตราการชน TP: {winning_tech/tech_exits:.2%}")
        print(f"- อัตราการชน SL: {losing_tech/tech_exits:.2%}")

"""วิเคราะห์ประสิทธิภาพการเทรดตามวันในสัปดาห์และชั่วโมง"""
def analyze_time_performance(trade_df, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน analyze time performance") if Steps_to_do else None

    """วิเคราะห์ประสิทธิภาพการซื้อขายตามวันและเวลา"""
    if trade_df.empty:
        print("⚠️ ไม่มีข้อมูล การซื้อขายสำหรับวิเคราะห์")
        return
    
    try:
        # วิเคราะห์ตามวันในสัปดาห์
        print("📊 ประสิทธิภาพตามวันในสัปดาห์:")
        day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        
        day_stats = trade_df.groupby('Entry_DayOfWeek').agg({
            'Profit': ['count', 'mean', 'sum'],
            'Target': 'mean'
        }).rename(index={i: day_names[i] for i in range(7)})
        
        print(day_stats)
        
        # วิเคราะห์ตามชั่วโมง
        print("\n📊 ประสิทธิภาพตามชั่วโมง:")
        hour_stats = trade_df.groupby('Entry_Hour').agg({
            'Profit': ['count', 'mean', 'sum'],
            'Target': 'mean'
        })
        
        print(hour_stats)
        
        # สร้าง visualization
        plt.figure(figsize=(15, 10))
        
        # ตรวจสอบว่ามีข้อมูลพอที่จะพล็อตหรือไม่
        valid_plots = 0
        plot_positions = [(1, 2, 1), (1, 2, 2), (2, 2, 1), (2, 2, 2)]
        plots = []
        
        # กราฟวันในสัปดาห์ (Win Rate)
        if 'Entry_DayOfWeek' in trade_df.columns and 'Target' in trade_df.columns:
            day_win_rate = trade_df.groupby('Entry_DayOfWeek')['Target'].mean()
            if len(day_win_rate) > 0:
                plots.append(('bar', day_names[:len(day_win_rate)], day_win_rate.values, 'Win Rate by Day of Week'))
        
        # กราฟชั่วโมง (Win Rate)
        if 'Entry_Hour' in trade_df.columns and 'Target' in trade_df.columns:
            hour_win_rate = trade_df.groupby('Entry_Hour')['Target'].mean()
            if len(hour_win_rate) > 0:
                plots.append(('bar', hour_win_rate.index, hour_win_rate.values, 'Win Rate by Hour'))
        
        # กราฟผลรวมกำไรตามวัน
        if 'Entry_DayOfWeek' in trade_df.columns and 'Profit' in trade_df.columns:
            day_profit = trade_df.groupby('Entry_DayOfWeek')['Profit'].sum()
            if len(day_profit) > 0:
                plots.append(('bar', day_names[:len(day_profit)], day_profit.values, 'Total Profit by Day of Week'))
        
        # กราฟผลรวมกำไรตามชั่วโมง
        if 'Entry_Hour' in trade_df.columns and 'Profit' in trade_df.columns:
            hour_profit = trade_df.groupby('Entry_Hour')['Profit'].sum()
            if len(hour_profit) > 0:
                plots.append(('bar', hour_profit.index, hour_profit.values, 'Total Profit by Hour'))
        
        # พล็อตเฉพาะกราฟที่มีข้อมูล
        for i, (plot_type, x, y, title) in enumerate(plots[:4]):
            plt.subplot(2, 2, i+1)
            if plot_type == 'bar':
                plt.bar(x, y)
            plt.title(title)
            plt.xticks(rotation=45)
        
        plt.tight_layout()
        plot_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_time_analysis.png")
        plt.savefig(plot_path)
        plt.close()
        print(f"💾 บันทึกกราฟวิเคราะห์เวลา ที่: {plot_path}")
            
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะสร้างกราฟวิเคราะห์เวลา: {str(e)}")
        import traceback
        traceback.print_exc()

"""วิเคราะห์ผลลัพธ์โดยรวมของโมเดลแต่ละไฟล์/แต่ละ timeframe"""
def analyze_results(results, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน analyze results") if Steps_to_do else None

    """วิเคราะห์ผลลัพธ์โดยรวม"""
    print("📈 การวิเคราะห์ผลลัพธ์โดยรวม:")
    
    # แปลงเป็น DataFrame
    if not results:
        print("⚠️ ไม่มีผลลัพธ์ให้วิเคราะห์")
        return
    
    try:
        # แปลงค่าใน results ให้เป็น float ก่อนสร้าง DataFrame
        processed_results = []
        for r in results:
            processed = {
                'File': r.get('file', ''),
                'Timeframe': r.get('timeframe', ''),
                'Accuracy': r.get('accuracy', 0),
                'AUC': r.get('auc', 0),
                'F1': r.get('f1_score', 0),
                'CV_Accuracy': r.get('cv_accuracy', 0),
                'CV_AUC': r.get('cv_auc', 0)
            }
            processed_results.append(processed)
        
        df = pd.DataFrame(processed_results)
        
        # ... ส่วนอื่นๆ ...
        
        best_model = df.loc[df['F1'].idxmax()]
        print(f"\n🏆 โมเดลที่ดีที่สุด (ตาม F1 Score):")
        print(f"ไฟล์: {best_model['File']}")
        print(f"Timeframe: M{best_model['Timeframe']}")
        print(f"F1 Score: {best_model['F1']:.4f}")
        
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะวิเคราะห์ผลลัพธ์: {str(e)}")
    
    # สรุปสถิติ
    print("\n📌 สรุปสถิติ:")
    print(df.describe().to_string())
    
    # หาโมเดลที่ดีที่สุด
    best_model = df.loc[df['F1'].idxmax()]
    print(f"\n🏆 โมเดลที่ดีที่สุด (ตาม F1 Score):")
    print(f"ไฟล์: {best_model['File']}")
    print(f"Timeframe: M{best_model['Timeframe']}")
    # print(f"F1 Score: {best_model['F1']:.4f}")
    print(f"F1 Score: {best_model['F1']:.4f}")
    
    # บันทึกผลการวิเคราะห์
    analysis_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_performance_analysis.txt")
    with open(analysis_path, 'w', encoding='utf-8') as f:
        f.write("ผลการวิเคราะห์ประสิทธิภาพโมเดล\n")
        f.write("="*50 + "\n")
        f.write(df.to_string())
        f.write("\n\nโมเดลที่ดีที่สุด:\n")
        f.write(str(best_model))
    print(f"\n💾 บันทึกผลการวิเคราะห์ที่: {analysis_path}")

"""วิเคราะห์ feature importance ข้ามหลาย asset เพื่อหา features ที่สำคัญร่วมกัน"""
def analyze_cross_asset_feature_importance(input_files, importance_files_dir, pickle_output_path, num_top_features_per_asset=None, min_assets_threshold=None, overall_top_n=None):
    print(f"\n🏗️ เปิดใช้งาน analyze cross asset feature importance") if Steps_to_do else None

    """
    วิเคราะห์ Feature Importance จากไฟล์ของแต่ละ Asset ในรายการ input_files
    เพื่อหารายชื่อ Features ที่มีความสำคัญอย่างสม่ำเสมอ และบันทึกลงในไฟล์ .pkl

    Args:
        input_files (list): รายชื่อไฟล์ Asset ดั้งเดิม (เช่น ["AUDUSD#_H1_...csv", ...])
        importance_files_dir (str): Directory ที่เก็บไฟล์ feature_importance_*.csv
        pickle_output_path (str): Path แบบเต็มสำหรับบันทึกรายชื่อ Features ที่เลือกในรูปแบบ .pkl
        num_top_features_per_asset (int, optional): จำนวน Top N features จากแต่ละ Asset ที่จะนำมารวมกัน. Defaults to None.
        min_assets_threshold (int, optional): Feature ต้องปรากฏใน Top N (ตาม num_top_features_per_asset) อย่างน้อยกี่ Asset. Defaults to None.
        overall_top_n (int, optional): เลือก Features ที่มีความสำคัญเฉลี่ย (หรือรวม) สูงสุด N อันดับ. Defaults to None.
                                        สามารถใช้ร่วมกับ num_top_features_per_asset + min_assets_threshold หรือเดี่ยวๆ ก็ได้

    Returns:
        list: รายชื่อ Features ที่ถูกเลือก (หรือ [] หากเกิดข้อผิดพลาด ⚠️ หรือไม่มี features ถูกเลือก)
    """
    print(f"--- เริ่มวิเคราะห์ Feature Importance ข้าม Assets ตาม {len(input_files)} ไฟล์ที่ระบุ ---")
    all_importance_data = {} # Dictionary to store importance scores for each feature across assets
    processed_asset_count = 0

    # 1. สร้างรายชื่อไฟล์ Feature Importance ที่คาดหวังจาก input_files
    expected_importance_file_paths = []
    for input_file in input_files:
        info = parse_filename(input_file)
        if info is None:
            print(f"⚠️ ข้ามไฟล์ input '{input_file}' เนื่องจากแยกข้อมูลไม่ได้")
            continue

        symbol = info["Name_Currency"]
        timeframe = info["Timeframe_Currency"] # นี่คือ timeframe เป็นนาทีแล้ว

        # สร้างชื่อไฟล์ Feature Importance ที่คาดหวัง
        expected_filename = f"{str(timeframe).zfill(3)}_{symbol}_feature_importance.csv"
        expected_filepath = os.path.join(importance_files_dir, expected_filename)
        expected_importance_file_paths.append((symbol, expected_filepath)) # เก็บทั้ง symbol และ path

    if not expected_importance_file_paths:
        print("⚠️ ไม่สามารถสร้างรายชื่อไฟล์ Feature Importance ที่คาดหวังจาก input_files ได้เลย")
        # แม้จะไม่มีไฟล์ importance ให้วิเคราะห์ ก็ยังสร้างไฟล์ pkl ว่างๆ หรือไม่มี list ได้
        # หรือจะ return [] เลยก็ได้ ขึ้นอยู่กับว่าจะให้ select features จัดการอย่างไร
        selected_features = [] # ไม่มีไฟล์ให้วิเคราะห์ ก็ไม่มี features จาก analysis
        # ไปขั้นตอนบันทึก
    else:
        print(f"คาดหวังไฟล์ Feature Importance ทั้งหมด {len(expected_importance_file_paths)} ไฟล์:")
        for symbol, path in expected_importance_file_paths:
            print(f"- {os.path.basename(path)}")

        # 2. อ่านและประมวลผลไฟล์ Feature Importance ที่คาดหวังและมีอยู่จริง
        print("\n--- เริ่มอ่านไฟล์ Feature Importance ที่พบ ---")
        for symbol, file_path in expected_importance_file_paths:
            file_name = os.path.basename(file_path)

            if not os.path.exists(file_path):
                print(f"ℹ️ ข้าม Asset '{symbol}': ⚠️ ไม่พบ ไฟล์ Feature Importance ที่คาดหวังที่ '{file_path}'")
                continue # ข้ามไฟล์นี้ไป

            print(f"✅ กำลังประมวลผลไฟล์: {file_name}")
            processed_asset_count += 1

            try:
                df_importance = pd.read_csv(file_path)
                # ตรวจสอบคอลัมน์ Feature และคอลัมน์ Importance ที่ใช้
                if 'Feature' not in df_importance.columns:
                    print(f"⚠️ ไฟล์ {file_name} ไม่มีคอลัมน์ 'Feature'")
                    processed_asset_count -= 1
                    continue

                importance_metric = None
                if 'Gain' in df_importance.columns and df_importance['Gain'].sum() > 0:
                    importance_metric = 'Gain'
                elif 'Split' in df_importance.columns and df_importance['Split'].sum() > 0:
                    importance_metric = 'Split'
                else:
                    print(f"⚠️ ไฟล์ {file_name} ไม่มีคอลัมน์ Feature Importance ที่ใช้งานได้ ('Gain' หรือ 'Split') หรือคอลัมน์นั้นเป็นศูนย์ทั้งหมด")
                    processed_asset_count -= 1
                    continue

                # เรียงตามความสำคัญและเลือก Top N ของแต่ละ Asset ถ้ากำหนด
                if num_top_features_per_asset is not None:
                    df_importance = df_importance.sort_values(by=importance_metric, ascending=False).head(num_top_features_per_asset)

                # เก็บข้อมูลความสำคัญ
                for index, row in df_importance.iterrows():
                    feature = row['Feature']
                    importance = row[importance_metric]

                    if feature not in all_importance_data:
                        # เก็บเป็น list ของ tuple (importance, symbol) เพื่อ track ได้ว่ามาจาก asset ไหน
                        all_importance_data[feature] = {'importances': [], 'asset_symbols': []}

                    all_importance_data[feature]['importances'].append(importance)
                    if symbol not in all_importance_data[feature]['asset_symbols']: # ป้องกันการนับ asset ซ้ำ
                        all_importance_data[feature]['asset_symbols'].append(symbol)

            except Exception as e:
                print(f"⚠️ เกิดข้อผิดพลาด ในการอ่านหรือประมวลผลไฟล์ {file_name}: {e}")
                processed_asset_count -= 1
                continue

        if processed_asset_count == 0:
            print("⚠️ ประมวลผลไฟล์ Feature Importance ไม่ได้เลยจาก Assets ที่พบ")
            selected_features = [] # ถ้าประมวลผลไม่ได้เลย ก็ไม่มี features จาก analysis
        else:
            print(f"\n✅ ประมวลผลไฟล์ Feature Importance ไปทั้งสิ้น {processed_asset_count} Assets")

            # 3. ประมวลผลข้อมูลรวม
            feature_summary = []
            for feature, data in all_importance_data.items():
                avg_importance = sum(data['importances']) / len(data['importances'])
                asset_count = len(data['asset_symbols'])
                feature_summary.append({
                    'Feature': feature,
                    'Avg_Importance': avg_importance,
                    'Asset_Count': asset_count
                })

            df_summary = pd.DataFrame(feature_summary)

            # 4. คัดเลือก Features ตามเกณฑ์
            selected_features = []

            # เกณฑ์ที่ 1: ต้องปรากฏใน Top N ของ Asset อย่างน้อย X ตัว
            if num_top_features_per_asset is not None and min_assets_threshold is not None:
                df_filtered_by_assets = df_summary[df_summary['Asset_Count'] >= min_assets_threshold].copy()
                print(f"\nพบ Features ที่ปรากฏใน Top {num_top_features_per_asset} ของอย่างน้อย {min_assets_threshold} Assets: {len(df_filtered_by_assets)} Features")

                if overall_top_n is not None:
                    selected_features = df_filtered_by_assets.sort_values(by='Avg_Importance', ascending=False).head(overall_top_n)['Feature'].tolist()
                    print(f"คัดเลือก Overall Top {overall_top_n} Features จากกลุ่มนี้")
                else:
                    selected_features = df_filtered_by_assets['Feature'].tolist()
                    print("คัดเลือก Features ทั้งหมดที่ผ่านเกณฑ์ Asset Count")

            # เกณฑ์ที่ 2: เลือก Overall Top N จากทั้งหมด (ถ้าไม่ได้ใช้เกณฑ์ Asset Count)
            elif overall_top_n is not None:
                print(f"\nคัดเลือก Overall Top {overall_top_n} Features จากความสำคัญเฉลี่ย")
                selected_features = df_summary.sort_values(by='Avg_Importance', ascending=False).head(overall_top_n)['Feature'].tolist()

            else:
                print("\nℹ️ ไม่ได้ระบุเกณฑ์การคัดเลือก Features (num_top_features_per_asset, min_assets_threshold, overall_top_n)")
                print("จะคืนค่า Features ทั้งหมดที่พบพร้อมความสำคัญเฉลี่ย")
                selected_features = df_summary.sort_values(by='Avg_Importance', ascending=False)['Feature'].tolist()

    # 5. บันทึกผลลัพธ์ลงในไฟล์ .pkl
    print(f"\n--- เริ่มบันทึกรายชื่อ Features ที่เลือก ({len(selected_features)} รายการ) ---")
    try:
        # สร้าง directory ถ้ายังไม่มี
        output_dir = os.path.dirname(pickle_output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            print(f"📁 สร้างไดเรกทอรี: {output_dir}")

        with open(pickle_output_path, 'wb') as f: # ใช้ 'wb' สำหรับ binary write
            pickle.dump(selected_features, f)
        print(f"✅ บันทึกรายชื่อ Features ที่เลือกไปยัง: {pickle_output_path}")
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ในการบันทึกไฟล์ {pickle_output_path}: {e}")
        # ในกรณีที่บันทึกไม่ได้ อาจจะคืนค่า [] หรือ None ก็ได้ แล้วแต่การจัดการ error ที่ต้องการ
        return [] # คืนค่าเป็น [] ถ้าบันทึกไม่ได้

    print("\n--- สิ้นสุดการวิเคราะห์และบันทึก Feature Importance ---")
    return selected_features

"""บันทึกรายงานผลลัพธ์แบบละเอียด (JSON/CSV)"""
def save_enhanced_report(results, output_folder, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน save enhanced report") if Steps_to_do else None

    try:
        if not results:
            print("⚠️ ไม่มีผลลัพธ์ที่จะบันทึก")
            return
            
        # สร้างโฟลเดอร์ถ้ายังไม่มี
        os.makedirs(output_folder, exist_ok=True)
        
        # บันทึกเป็น JSON
        report_path = os.path.join(output_folder, 'detailed_report.json')
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump({
                'summary': pd.DataFrame(results).to_dict(orient='records'),
                'best_model': max(results, key=lambda x: x['f1_score']),
                'execution_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'system_info': {
                    'python_version': sys.version,
                    'lgb_version': lgb.__version__,
                    'sklearn_version': sklearn_version
                }
            }, f, indent=2, ensure_ascii=False)
        print(f"✅ บันทึกรายงานละเอียดที่: {report_path}")

        # บันทึกเป็น CSV
        csv_path = os.path.join(output_folder, f'{str(timeframe).zfill(3)}_{symbol}_final_results.csv')
        pd.DataFrame(results).to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"✅ บันทึกรายงานสรุปที่: {csv_path}")
        
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะบันทึกรายงาน: {str(e)}")
        error_log_path = os.path.join(output_folder, "report_error_log.txt")
        with open(error_log_path, 'w') as f:
            f.write(f"Error saving report: {str(e)}\n")
            traceback.print_exc(file=f)

# ==============================================
# visualization.py
# ฟังก์ชันเกี่ยวกับการ plot กราฟ, candlestick, safe plot
# ==============================================

"""สร้างกราฟแท่งเทียนพร้อมจุดเข้า-ออกการเทรด"""
def plot_candlestick_with_entries_and_exits(df, trade_df, file_name):
    print(f"\n🏗️ เปิดใช้งาน plot candlestick with entries and exits") if Steps_to_do else None

    fig = go.Figure(data=[go.Candlestick(
        x=df['Date'] + ' ' + df['Time'],
        open=df['Open'],
        high=df['High'],
        low=df['Low'],
        close=df['Close'],
        name="Candlestick"
    )])

    fig.add_trace(go.Scatter(
        x=trade_df[trade_df['Trade Type'] == 'Buy']['Entry Time'],
        y=trade_df[trade_df['Trade Type'] == 'Buy']['Entry Price'], # แก้ไขตรงนี้
        mode='markers',
        marker=dict(color='green', size=8, symbol='triangle-up'),
        name='Buy Entry Points'
    ))

    fig.add_trace(go.Scatter(
        x=trade_df[trade_df['Trade Type'] == 'Buy']['Exit Time'],
        y=trade_df[trade_df['Trade Type'] == 'Buy']['Exit Price'], # แก้ไขตรงนี้
        mode='markers',
        marker=dict(color='red', size=8, symbol='triangle-down'),
        name='Buy Exit Points'
    ))

    fig.add_trace(go.Scatter(
        x=trade_df[trade_df['Trade Type'] == 'Sell']['Entry Time'],
        y=trade_df[trade_df['Trade Type'] == 'Sell']['Entry Price'], # แก้ไขตรงนี้
        mode='markers',
        marker=dict(color='green', size=8, symbol='triangle-down'),
        name='Sell Entry Points'
    ))

    fig.add_trace(go.Scatter(
        x=trade_df[trade_df['Trade Type'] == 'Sell']['Exit Time'],
        y=trade_df[trade_df['Trade Type'] == 'Sell']['Exit Price'], # แก้ไขตรงนี้
        mode='markers',
        marker=dict(color='red', size=8, symbol='triangle-up'),
        name='Sell Exit Points'
    ))

    fig.update_layout(
        title=f"Candlestick Chart with Entry and Exit - {file_name}",
        xaxis_title="Date",
        yaxis_title="Price",
        xaxis_rangeslider_visible=True,
        dragmode='zoom',
        yaxis=dict(
            autorange=True,
            fixedrange=False
        ),
        width=1200,
        height=800
    )

    return fig

"""พล็อตกราฟผลลัพธ์ (accuracy, auc, ฯลฯ) แบบปลอดภัย (มี error handling)"""
def safe_plot_results(df, symbol, timeframe, output_path="results/plots"):
    print(f"\n🏗️ เปิดใช้งาน safe plot results") if Steps_to_do else None

    try:
        if df.empty:
            print("⚠️ DataFrame ว่างเปล่า ไม่มีข้อมูล สำหรับพล็อต")
            return

        os.makedirs(output_path, exist_ok=True) # สร้างโฟลเดอร์สำหรับบันทึกรูปภาพถ้ายังไม่มี

        timeframes = df['timeframe']
        metrics_to_plot = ['Test Accuracy', 'auc', 'f1_score', 'precision', 'recall', 'cv_accuracy', 'cv_auc', 'cv_f1']
        num_metrics_plotted = 0

        for metric in metrics_to_plot:
            if metric in df.columns and not df[metric].isnull().all():
                plt.figure(figsize=(10, 6))
                plt.plot(timeframes, df[metric], marker='o', label=metric)
                plt.xlabel('Timeframe')
                plt.ylabel('Score')
                plt.title(f'{metric} Comparison Across Timeframes')
                plt.legend()
                plt.grid(True)

                # สร้างชื่อไฟล์สำหรับบันทึก
                filename = f'performance_{metric.replace(" ", "_")}_comparison.png'
                filepath = os.path.join(output_path, filename)

                # บันทึกเป็นไฟล์ PNG
                plt.savefig(filepath)
                plt.close() # ปิด Figure หลังจากบันทึกเพื่อประหยัด memory
                print(f"✅ บันทึกกราฟ {metric} ที่: {filepath}")
                num_metrics_plotted += 1

        if num_metrics_plotted == 0:
            print("⚠️ ไม่มี Metrics ที่มีข้อมูลเพียงพอสำหรับการพล็อต")

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ในฟังก์ชัน safe plot results: {e}")
        print(f"คอลัมน์ที่มีอยู่: {df.columns.tolist()}")
        traceback.print_exc()

# ==============================================
# main.py
# ส่วนที่เป็น entry point ของโปรแกรม, วนลูปไฟล์, เรียกใช้ฟังก์ชันจากโมดูลต่างๆ
# ==============================================

"""จุดเริ่มต้นของโปรแกรม วนลูปไฟล์, เรียกใช้ฟังก์ชันต่างๆ, สรุปผล, วิเคราะห์, และบันทึกผลลัพธ์"""
def main(run_identifier=None):
    print(f"\n🏗️ เปิดใช้งาน main") if Steps_to_do else None

    # --- เริ่มจับเวลาทั้งหมด ---
    start_time_total = time.perf_counter()

    results = []
    results_report = []
    
    for file in input_files:
        print(f"{'--'*30}")
        print(f"กำลังทดสอบไฟล์: {file}")
        print(f"{'--'*30}")

        try:
            info = parse_filename(file)  # ใช้ฟังก์ชันที่เราสร้างไว้ก่อนหน้านี้
            symbol = info["Name_Currency"]
            timeframe = info["Timeframe_Currency"]
            
        except IndexError:
            print(f"⚠️ รูปแบบชื่อไฟล์ไม่ถูกต้อง: {file}. คาดหวังรูปแบบ 'ชื่อ#timeframe.csv'")
            continue # ข้ามไฟล์นี้

        model_name = "LightGBM"
        print(f"ข้อมูลพื้นฐาน {model_name} {timeframe} {symbol}")

        # ตรวจสอบว่าโฟลเดอร์ มีอยู่หรือไม่

        models_dir = "models"
        if not os.path.exists(models_dir):
            os.makedirs(models_dir)
            print(f"สร้างโฟลเดอร์ {models_dir} เรียบร้อยแล้ว")
        
        timeframe_dir = f"models/{str(timeframe).zfill(3)}_{symbol}"
        if not os.path.exists(timeframe_dir):
            os.makedirs(timeframe_dir)
            print(f"สร้างโฟลเดอร์ {timeframe_dir} เรียบร้อยแล้ว")
        
        feature_dir = "feature_importance"
        if not os.path.exists(feature_dir):
            os.makedirs(feature_dir)
            print(f"สร้างโฟลเดอร์ {feature_dir} เรียบร้อยแล้ว")

        thresholds_dir = "thresholds"
        if not os.path.exists(thresholds_dir):
            os.makedirs(thresholds_dir)
            print(f"สร้างโฟลเดอร์ {thresholds_dir} เรียบร้อยแล้ว")
        
        # --- ปรับ Logic: ก่อนเทรน ให้ตัดสินใจว่าจะโหลดหรือไม่โหลด ---
        # ทางเลือกที่ 1 (แนะนำ): Retrain ใหม่เสมอเมื่อ Feature เปลี่ยน
        # หลักการ: ไม่โหลดโมเดลเก่า แต่ให้เทรนโมเดลใหม่ตั้งแต่ต้นทุกครั้งที่รัน
        # โดยใช้ Feature Set ที่ถูกเลือกในรอบนั้นๆ
        
        # - หาบรรทัดที่โหลดโมเดล: 
        # model = None  # หรือสร้างโมเดลใหม่ตรงนี้เลย
        model = load_model(model_name, symbol, timeframe)
        scaler = load_scaler(model_name, symbol, timeframe)

        train_data, val_data, test_data, df, trade_df, stats = load_and_process_data(file, model_name, symbol, timeframe, run_identifier)
        
        print("\n🔍 การวิเคราะห์ Temporal Dependence ระดับสูง")

        # ตรวจสอบ Autocorrelation ใน Target
        if trade_df is not None and 'Target' in trade_df.columns:
            from statsmodels.graphics.tsaplots import plot_acf
            
            plt.figure(figsize=(12, 4))
            plot_acf(trade_df['Target'], lags=20, alpha=0.05)
            plt.title(f'Autocorrelation ของ Target ({timeframe} {symbol})')
            acf_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_target_autocorrelation.png")
            plt.savefig(acf_path)
            plt.close()
            print(f"💾 บันทึกกราฟ Autocorrelation ที่: {acf_path}")
        
        # ตรวจสอบ Seasonality
        if trade_df is not None and 'Entry_Hour' in trade_df.columns:
            hour_win_rate = trade_df.groupby('Entry_Hour')['Target'].mean()
            day_win_rate = trade_df.groupby('Entry_DayOfWeek')['Target'].mean()
            
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
            hour_win_rate.plot(kind='bar', ax=ax1, title='Win Rate by Hour')
            day_win_rate.plot(kind='bar', ax=ax2, title='Win Rate by Day of Week')
            plt.tight_layout()
            season_path = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_seasonality_analysis.png")
            plt.savefig(season_path)
            plt.close()
            print(f"💾 บันทึกกราฟ Seasonality ที่: {season_path}")

        # เรียกใช้การวิเคราะห์ SL/TP + ฟังก์ชันวิเคราะห์เวลา
        if trade_df is not None and not trade_df.empty:
            analyze_sl_tp_performance(trade_df)
            analyze_time_performance(trade_df, symbol, timeframe)
        
        # ตรวจสอบว่ามีข้อมูล หรือไม่
        if train_data is None or val_data is None or test_data is None:
            print(f"⚠️ ข้ามไฟล์ {file} เนื่องจากมีปัญหาในการโหลดหรือประมวลผลข้อมูล")
            
            # เพิ่มข้อมูลในรายงานแม้ว่าจะมีปัญหา
            results_report.append({
                "File": file,
                "Timeframe": timeframe,
                "Model": model_name,
                "Status": "Failed - No data",
                "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"),
                "Accuracy": "N/A",
                "AUC": "N/A",
                "F1 Score": "N/A"
            })
            continue
            
        X_train, y_train = train_data
        if len(X_train) == 0 or len(y_train) == 0:
            print(f"⚠️ ข้ามไฟล์ {file} เนื่องจากไม่มีข้อมูล ในชุดฝึก")
            
            results_report.append({
                "File": file,
                "Timeframe": timeframe,
                "Model": model_name,
                "Status": "Failed - Empty training set",
                "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"),
                "Accuracy": "N/A",
                "AUC": "N/A",
                "F1 Score": "N/A"
            })
            continue
        
        try:
            # รับผลลัพธ์ทั้งหมดเป็น dictionary และ scaler ที่เทรนแล้ว
            # train and evaluate คืนค่าเป็น tuple: (ผลลัพธ์ dict, scaler object)
            # แก้ไขตรงนี้: รับค่าเป็น result_dict และ trained_scaler
            result_dict, trained_scaler = train_and_evaluate(model, model_name, train_data, val_data, test_data, symbol, timeframe)

            # ตรวจสอบว่า train and evaluate ทำงานสำเร็จและคืนค่า dict มาหรือไม่
            if result_dict is None:
                print(f"⚠️ train and evaluate ล้มเหลวสำหรับไฟล์ {file} หรือคืนค่า None")
                # ถ้า train and evaluate คืนค่า None, None การจัดการ error จะถูกรวมอยู่ใน except block ด้านล่างแล้ว
                # ดังนั้นไม่ต้องทำอะไรเพิ่มเติมที่นี่ ถ้าต้องการให้มันไป trigger except block
                # หรือจะเพิ่มการจัดการข้อผิดพลาดเฉพาะจุดที่นี่ก็ได้ ถ้า logic ซับซ้อนกว่า
                # ในกรณีนี้ เนื่องจากโค้ดที่เหลือจะพยายามเข้าถึง result_dict[]
                # และถ้า result_dict เป็น None จะเกิด TypeError ซึ่งจะถูกจับโดย except block ด้านล่าง
                # การจัดการ error จึงยังทำงานอยู่ เพียงแต่ traceback จะชี้ไปที่บรรทัดถัดจาก if result_dict is None:

            else:
                # บันทึกผลลัพธ์ที่สำคัญจาก result_dict
                results.append({ # results list เดิม (อาจเปลี่ยนชื่อเป็น all_training_eval_metrics หากอยู่ใน multi-run)
                    'file': file,
                    'timeframe': timeframe,
                    # เข้าถึงค่าต่างๆ จาก result_dict
                    'accuracy': result_dict['metrics']['accuracy'],
                    'auc': result_dict['metrics']['auc'],
                    'f1_score': result_dict['metrics']['f1'],
                    'precision': result_dict['metrics']['precision'],
                    'recall': result_dict['metrics']['recall'],
                    'cv_accuracy': result_dict['cv_results']['accuracy'],
                    'cv_auc': result_dict['cv_results']['auc'],
                    'cv_f1': result_dict['cv_results']['f1']
                })

                # แสดงผลลัพธ์เปรียบเทียบจาก result_dict
                print("\n📊 เปรียบเทียบผลลัพธ์:")
                print(f"| Metric      | CV Avg    | Test Set |")
                print(f"|-------------|-----------|----------|")
                print(f"| Accuracy    | {result_dict['cv_results']['accuracy']:.4f}    | {result_dict['metrics']['accuracy']:.4f} |")
                print(f"| AUC         | {result_dict['cv_results']['auc']:.4f}    | {result_dict['metrics']['auc']:.4f} |")
                print(f"| F1 Score    | {result_dict['cv_results']['f1']:.4f}    | {result_dict['metrics']['f1']:.4f} |")

                # ส่วนการสร้างกราฟ (ไม่มีการเปลี่ยนแปลงการเข้าถึงผลลัพธ์โมเดลตรงนี้)
                if Plot_file:
                    if df is not None and trade_df is not None:

                        # print(f"\nตรวจสอบ trade_df")
                        # print(trade_df.columns) # ตรวจสอบ trade_df
                        # print(trade_df.head())

                        # TODO: ตรวจสอบและแก้ไขฟังก์ชัน plot candlestick with entries and exits
                        # ว่ารับ parameters ถูกต้องหรือไม่ และบันทึกไฟล์โดยใช้ output_folder และ run_identifier (ถ้ามี)
                        fig = plot_candlestick_with_entries_and_exits(df, trade_df, file)

                        html_file = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_candlestick_chart.html") # TODO: Add run_identifier here if using multi-run
                        fig.write_html(html_file)
                        print(f"✅ บันทึก HTML: {html_file}")

                        image_file = os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_candlestick_chart.png") # TODO: Add run_identifier here
                        pio.write_image(fig, image_file, format="png", width=1200, height=800)
                        print(f"✅ บันทึกรูปภาพ PNG: {image_file}")
                        # เพิ่มแจ้งเตือน ⚠️ชั่วคราว เนื่องจากโค้ด plot ยังไม่ได้รวมใน snippet นี้
                        print("ℹ️ ข้ามการสร้างกราฟ Candlestick ในโค้ด Main นี้ (ต้องแก้ไขฟังก์ชันแยก)")

                    else:
                        print(f"⚠️ ไม่สามารถสร้างกราฟสำหรับไฟล์: {file} เนื่องจากข้อมูลไม่สมบูรณ์")

                # เตรียมข้อมูลสำหรับรายงานสรุปใน main (results_report list เดิม)
                results_report.append({ # results_report list เดิม (อาจเปลี่ยนชื่อเป็น all_file_run_summary_reports หากอยู่ใน multi-run)
                    "File": file,
                    "Timeframe": timeframe,
                    "Model": "LightGBM",
                    # เข้าถึงค่าต่างๆ จาก result_dict
                    "Accuracy": f"{result_dict['metrics']['accuracy']:.4f}",
                    "AUC": f"{result_dict['metrics']['auc']:.4f}",
                    "F1 Score": f"{result_dict['metrics']['f1']:.4f}",
                    "Precision": f"{result_dict['metrics']['precision']:.4f}",
                    "Recall": f"{result_dict['metrics']['recall']:.4f}",
                    "CV Accuracy": f"{result_dict['cv_results']['accuracy']:.4f}",
                    "CV AUC": f"{result_dict['cv_results']['auc']:.4f}",
                    "CV F1": f"{result_dict['cv_results']['f1']:.4f}",
                    "Status": "Success",
                    "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
                        # TODO: หากอยู่ใน multi-run loop ต้องเพิ่ม "Run": run_i + 1 ที่นี่
                        # TODO: เพิ่ม Backtest Metrics จาก trade_df/stats ถ้าต้องการรวมในรายงานสรุปหลัก
                })

        except Exception as e:
            # หาก train and evaluate เกิดข้อผิดพลาด ⚠️ ภายในแล้ว propagate ออกมา หรือ
            # หาก result_dict เป็น None แล้วโค้ดด้านบนพยายามเข้าถึง key จะเกิด TypeError ซึ่งถูกจับที่นี่
            print(f"⚠️ เกิดข้อผิดพลาด ขณะประมวลผลไฟล์ {file} (หลัง train and evaluate): {str(e)}")
            traceback.print_exc() # พิมพ์ traceback เพื่อดูรายละเอียดข้อผิดพลาด
            results_report.append({ # results_report list เดิม
                "File": file,
                "Timeframe": timeframe,
                "Model": model_name,
                "Status": f"Failed - Post-Train Process: {str(e)}",
                "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"),
                "Accuracy": "N/A",
                "AUC": "N/A",
                "F1 Score": "N/A",
            })

    if len(input_files) > 1.0: 
        # ขั้นตอนการวิเคราะห์และบันทึก Features สำคัญข้าม Assets (รันหลังจากเทรนทุก Asset ครั้งแรก/มีการอัปเดต) ---
        importance_results_directory = r'D:\test_gold\results' # โฟลเดอร์ที่เก็บไฟล์ feature_importance_*.csv
        feature_importance_analysis_dir = r'D:\test_gold\feature_importance' # โฟลเดอร์ที่จะเก็บไฟล์ .pkl
        must_have_features_pickle_file = os.path.join(feature_importance_analysis_dir, f'{str(timeframe).zfill(3)}_must_have_features.pkl') # ชื่อไฟล์ .pkl
        
        print("\nเริ่มขั้นตอนวิเคราะห์ Feature Importance ข้าม Assets")
        analyzed_must_have_features = analyze_cross_asset_feature_importance(
            input_files = input_files,
            importance_files_dir = importance_results_directory,
            pickle_output_path = must_have_features_pickle_file,

            # เงื่อนไขที่ 1 : ใช้เกณฑ์แบบ Asset Count:
            num_top_features_per_asset = 15, # พิจารณา Features ใน Top 15 ของแต่ละ Asset
            min_assets_threshold = 4, # ต้องปรากฏใน Top 20 ของอย่างน้อย 4 Assets

            # เงื่อนไขที่ 2 : เลือกจากลำดับสูงสุด จากการเรียงลำดับ
            overall_top_n = 3, # เลือก 20 Features ที่สำคัญเฉลี่ยสูงสุด
        )
        # ขั้นตอนการทำงานของโค้ด : เมื่อตั้งเงื่อนไขทั้ง 2 ข้อพร้อมกัน
        # หา Features ทั้งหมดที่ปรากฏใน Feature Importance CSV ของอย่างน้อย 4 Assets (ในบรรดา Assets ที่ไฟล์ Importance ถูกพบ) และในแต่ละ Asset ที่ปรากฏนั้น Feature นั้นต้องอยู่ในอันดับ Top 20 ของ Asset นั้นๆ
        # นำ Features ที่ผ่านเกณฑ์ในข้อ 1 มาเรียงลำดับตามค่า Avg_Importance จากมากไปน้อย
        # เลือก Features ที่อยู่ในอันดับ 1 ถึง 25 จากรายการที่เรียงแล้วในข้อ 2

        print(f"\nFeatures ที่ได้จากการวิเคราะห์เพื่อบันทึกลง .pkl: {analyzed_must_have_features}")

    # ส่วนการสร้างรายงาน (แก้ไขให้ยืดหยุ่นมากขึ้น)
    try:
        # สร้าง DataFrame สำหรับผลลัพธ์
        results_df = pd.DataFrame(results) if results else pd.DataFrame()
        final_report_df = pd.DataFrame(results_report)
        
        # กำหนดคอลัมน์ที่ต้องการแสดง (เฉพาะที่มีอยู่จริง)
        available_columns = final_report_df.columns.tolist()
        desired_columns = [
            "File", "Timeframe", "Model", "Timestamp", "Status",
            "Accuracy", "AUC", "F1 Score", "Precision", "Recall",
            "CV Accuracy", "CV AUC", "CV F1"
        ]
        
        # เลือกเฉพาะคอลัมน์ที่มีอยู่จริง
        columns_to_show = [col for col in desired_columns if col in available_columns]
        
        # เรียงลำดับคอลัมน์ใหม่
        final_report_df = final_report_df[columns_to_show]

        # บันทึกผลลัพธ์
        if not results_df.empty:
            results_df.to_csv(os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_final_results.csv"), index=False, encoding='utf-8-sig')
        final_report_df.to_csv(os.path.join(output_folder, f"{str(timeframe).zfill(3)}_{symbol}_training_results.csv"), index=False, encoding='utf-8-sig')
        
        # แสดงผลลัพธ์
        print("\nผลลัพธ์การทดสอบโดยสรุป:")
        print(final_report_df.to_string(index=False))
        
        print(f"\n💾 บันทึกผลลัพธ์ทั้งหมดที่: {os.path.join(output_folder, f'{str(timeframe).zfill(3)}_{symbol}_final_results.csv')}")
        print("✅ บันทึกผลลัพธ์การฝึกโมเดลเรียบร้อยแล้ว")

        # วิเคราะห์และพล็อตผลลัพธ์ (เฉพาะกรณีที่มีผลลัพธ์)
        if not results_df.empty:
            analyze_results(results, symbol, timeframe)
            
            # เปลี่ยนชื่อคอลัมน์ให้สอดคล้องกันหากจำเป็น
            if 'file' in results_df.columns and 'File' not in results_df.columns:
                results_df = results_df.rename(columns={'file': 'File'})
            if 'accuracy' in results_df.columns and 'Test Accuracy' not in results_df.columns:
                results_df = results_df.rename(columns={'accuracy': 'Test Accuracy'})

            # เรียกใช้ฟังก์ชันพล็อตผลลัพธ์แบบปลอดภัย
            safe_plot_results(results_df, symbol, timeframe)
            
        # บันทึกรายงานแบบละเอียด
        if not results_df.empty:
            save_enhanced_report(results, output_folder, symbol, timeframe)
            
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะสร้างรายงาน: {str(e)}")

    # --- หยุดจับเวลาทั้งหมด ---
    end_time_total = time.perf_counter()
    total_duration = end_time_total - start_time_total
    total_files = len(input_files)

    # --- แสดงผลเวลารวมและเวลาเฉลี่ย ---
    print(f"\n{'='*40}")
    print(f"⏱️ สรุปเวลาการประมวลผลทั้งหมด")
    print(f"{'='*40}")
    print(f"1. เวลาทั้งหมดที่ใช้ในการประมวลผล: {total_duration:.4f} วินาที")

    # คำนวณเวลาเฉลี่ยต่อไฟล์ (ใช้จำนวนไฟล์ทั้งหมดใน input_files)
    if total_files > 0:
        average_time_per_file = total_duration / total_files
        print(f"2. เวลาเฉลี่ยที่ใช้ในการประมวลผลต่อ 1 ไฟล์ ({total_files} ไฟล์ {run_identifier} รอบ): {average_time_per_file:.4f} วินาที")
    else:
        print("ไม่มีไฟล์ให้ประมวลผล ไม่สามารถคำนวณเวลาเฉลี่ยได้")

    print("✅ การประมวลผลเสร็จสิ้น")

if __name__ == "__main__":
    
    all_runs_results_report = [] # เก็บรายงานสรุปของทุกไฟล์ในทุกรอบ
    print(f"=== เริ่มการเทรนทั้งหมด {NUM_TRAINING_ROUNDS} รอบ ===")

    for run_i in range(NUM_TRAINING_ROUNDS):
        current_run_identifier = run_i + 1
        print(f"\n{'#'*50}")
        print(f"### เริ่มการเทรนรอบที่ {current_run_identifier}/{NUM_TRAINING_ROUNDS} ###")
        print(f"{'#'*50}")

        try:
            main(run_identifier=current_run_identifier)

        except Exception as e:
            print(f"\n⚠️ เกิดข้อผิดพลาด ร้ายแรงในสคริปต์ระหว่างรอบที่ {current_run_identifier}:")
            traceback.print_exc()
            print(f"\n### การเทรนรอบที่ {current_run_identifier} หยุดลงเนื่องจากข้อผิดพลาด ###")
            # ไม่จำเป็นต้อง exit โปรแกรมทันที ถ้าต้องการให้รอบถัดไปทำงานต่อ
    
    print(f"\n{'='*50}")
    print(f"=== การเทรนทั้งหมด {NUM_TRAINING_ROUNDS} รอบเสร็จสิ้น ===")
    print(f"{'='*50}")
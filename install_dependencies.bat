@echo off
echo ========================================
echo 🖥️ การติดตั้งสำหรับ python_LightGBM_15_Tuning.py
echo ========================================

echo.
echo 🔍 ตรวจสอบ Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python ไม่พบ! กรุณาติดตั้ง Python 3.9-3.11 ก่อน
    echo 💡 ดาวน์โหลดจาก: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo.
echo 🔍 ตรวจสอบ pip...
pip --version
if %errorlevel% neq 0 (
    echo ❌ pip ไม่พบ! กรุณาติดตั้ง pip
    pause
    exit /b 1
)

echo.
echo 📦 อัปเดต pip...
python -m pip install --upgrade pip

echo.
echo 🏗️ สร้าง Virtual Environment...
python -m venv trading_env
if %errorlevel% neq 0 (
    echo ⚠️ ไม่สามารถสร้าง virtual environment ได้
    echo 💡 ติดตั้ง virtualenv: pip install virtualenv
)

echo.
echo 🔧 เปิดใช้งาน Virtual Environment...
call trading_env\Scripts\activate.bat

echo.
echo 📊 ติดตั้ง Core Data Science Libraries...
pip install numpy==1.24.3
pip install pandas==2.0.3
pip install scipy==1.11.1

echo.
echo 🤖 ติดตั้ง Machine Learning Libraries...
pip install scikit-learn==1.3.0
pip install lightgbm==4.0.0
pip install imbalanced-learn==0.11.0
pip install joblib==1.3.1

echo.
echo 📈 ติดตั้ง Technical Analysis Libraries...
echo ⚠️ กำลังติดตั้ง TA-Lib (อาจใช้เวลานาน)...
pip install TA-Lib==0.4.25
if %errorlevel% neq 0 (
    echo ❌ TA-Lib ติดตั้งไม่สำเร็จ
    echo 💡 วิธีแก้:
    echo    1. ติดตั้ง Microsoft Visual C++ Build Tools
    echo    2. หรือดาวน์โหลด wheel file จาก:
    echo       https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
    echo    3. แล้วติดตั้งด้วย: pip install TA_Lib-0.4.xx-cpxx-cpxx-win_amd64.whl
)

pip install pandas-ta==0.3.14b0

echo.
echo 📊 ติดตั้ง Visualization Libraries...
pip install matplotlib==3.7.2
pip install seaborn==0.12.2
pip install plotly==5.15.0

echo.
echo 📈 ติดตั้ง Statistical Analysis...
pip install statsmodels==0.14.0

echo.
echo 🧪 ทดสอบการติดตั้ง...
python test_installation.py

echo.
echo ========================================
echo ✅ การติดตั้งเสร็จสิ้น!
echo ========================================
echo.
echo 💡 วิธีใช้งาน:
echo    1. เปิด Command Prompt
echo    2. รัน: trading_env\Scripts\activate.bat
echo    3. รัน: python python_LightGBM_15_Tuning.py
echo.
echo 📁 ไฟล์สำคัญ:
echo    - setup_guide_new_machine.md (คู่มือละเอียด)
echo    - requirements.txt (รายการ libraries)
echo    - test_installation.py (ทดสอบการติดตั้ง)
echo.
pause

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
วิเคราะห์และแนะนำการปรับปรุง Threshold และ nBars_SL สำหรับ Multi-Model Architecture

Created: 2025-01-11
Author: AI Assistant
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
from pathlib import Path

# Configuration
BASE_PATH = "LightGBM_Multi"
TIMEFRAME = 60

def check_threshold_files_structure():
    """
    ตรวจสอบโครงสร้างไฟล์ threshold และ nBars_SL ที่มีอยู่
    """
    print("🔍 ตรวจสอบโครงสร้างไฟล์ Threshold และ nBars_SL")
    print("="*60)
    
    thresholds_dir = Path(BASE_PATH) / "thresholds"
    print(f"📁 Thresholds Directory: {thresholds_dir}")
    print(f"   Exists: {thresholds_dir.exists()}")
    
    if thresholds_dir.exists():
        files = list(thresholds_dir.glob("*.pkl"))
        print(f"   Total files: {len(files)}")
        
        # จำแนกประเภทไฟล์
        file_types = {
            'time_filters': [],
            'optimal_threshold': [],
            'optimal_nBars_SL': []
        }
        
        for file in files:
            if 'time_filters' in file.name:
                file_types['time_filters'].append(file.name)
            elif 'optimal_threshold' in file.name:
                file_types['optimal_threshold'].append(file.name)
            elif 'optimal_nBars_SL' in file.name:
                file_types['optimal_nBars_SL'].append(file.name)
        
        print(f"\n📊 การจำแนกไฟล์:")
        for file_type, file_list in file_types.items():
            print(f"   {file_type}: {len(file_list)} files")
            for file_name in file_list:
                print(f"     - {file_name}")
        
        return file_types
    else:
        print("   ❌ Directory not found")
        return {}

def analyze_current_threshold_usage():
    """
    วิเคราะห์การใช้งาน threshold ปัจจุบันใน Multi-Model Architecture
    """
    print("\n🔍 วิเคราะห์การใช้งาน Threshold ปัจจุบัน")
    print("="*50)
    
    # ตรวจสอบว่ามีการใช้งาน threshold แยกตาม scenario หรือไม่
    symbols = ["AUDUSD", "GOLD", "USDJPY"]
    scenarios = ["trend_following", "counter_trend"]
    
    print("📋 Expected threshold files for Multi-Model Architecture:")
    
    missing_files = []
    expected_files = []
    
    for symbol in symbols:
        for scenario in scenarios:
            # รูปแบบที่ควรมีสำหรับ Multi-Model
            expected_file = f"{TIMEFRAME}_{symbol}_{scenario}_optimal_threshold.pkl"
            expected_files.append(expected_file)
            
            file_path = Path(BASE_PATH) / "thresholds" / expected_file
            if file_path.exists():
                print(f"   ✅ {expected_file}")
            else:
                print(f"   ❌ {expected_file}")
                missing_files.append(expected_file)
        
        # รูปแบบเดิม (ไม่แยก scenario)
        old_format = f"{symbol}_{TIMEFRAME}_optimal_threshold.pkl"
        old_file_path = Path(BASE_PATH) / "thresholds" / old_format
        if old_file_path.exists():
            print(f"   🔄 {old_format} (รูปแบบเดิม)")
    
    print(f"\n📊 สรุป:")
    print(f"   Expected files: {len(expected_files)}")
    print(f"   Missing files: {len(missing_files)}")
    print(f"   Coverage: {(len(expected_files) - len(missing_files))/len(expected_files)*100:.1f}%")
    
    return missing_files

def analyze_current_nbars_usage():
    """
    วิเคราะห์การใช้งาน nBars_SL ปัจจุบันใน Multi-Model Architecture
    """
    print("\n🔍 วิเคราะห์การใช้งาน nBars_SL ปัจจุบัน")
    print("="*50)
    
    symbols = ["AUDUSD", "GOLD", "USDJPY"]
    scenarios = ["trend_following", "counter_trend"]
    
    print("📋 Expected nBars_SL files for Multi-Model Architecture:")
    
    missing_files = []
    expected_files = []
    
    for symbol in symbols:
        for scenario in scenarios:
            # รูปแบบที่ควรมีสำหรับ Multi-Model
            expected_file = f"{TIMEFRAME}_{symbol}_{scenario}_optimal_nBars_SL.pkl"
            expected_files.append(expected_file)
            
            file_path = Path(BASE_PATH) / "thresholds" / expected_file
            if file_path.exists():
                print(f"   ✅ {expected_file}")
            else:
                print(f"   ❌ {expected_file}")
                missing_files.append(expected_file)
        
        # รูปแบบเดิม (ไม่แยก scenario)
        old_format = f"{symbol}_{TIMEFRAME}_optimal_nBars_SL.pkl"
        old_file_path = Path(BASE_PATH) / "thresholds" / old_format
        if old_file_path.exists():
            print(f"   🔄 {old_format} (รูปแบบเดิม)")
    
    print(f"\n📊 สรุป:")
    print(f"   Expected files: {len(expected_files)}")
    print(f"   Missing files: {len(missing_files)}")
    print(f"   Coverage: {(len(expected_files) - len(missing_files))/len(expected_files)*100:.1f}%")
    
    return missing_files

def recommend_multi_model_optimization():
    """
    แนะนำการปรับปรุงสำหรับ Multi-Model Architecture
    """
    print("\n💡 แนะนำการปรับปรุงสำหรับ Multi-Model Architecture")
    print("="*60)
    
    print("🎯 ปัญหาที่พบ:")
    print("1. ไม่มีการแยก threshold ตาม scenario (trend_following vs counter_trend)")
    print("2. ไม่มีการแยก nBars_SL ตาม scenario")
    print("3. ใช้ฟังก์ชันเดิมที่ไม่รองรับ Multi-Model Architecture")
    
    print("\n🔧 การแก้ไขที่แนะนำ:")
    
    print("\n1. สร้างฟังก์ชันใหม่สำหรับ Multi-Model:")
    print("   - find_optimal_threshold_multi_model()")
    print("   - find_optimal_nbars_sl_multi_model()")
    
    print("\n2. โครงสร้างไฟล์ที่แนะนำ:")
    print("   thresholds/")
    print("   ├─ 060_SYMBOL_trend_following_optimal_threshold.pkl")
    print("   ├─ 060_SYMBOL_counter_trend_optimal_threshold.pkl")
    print("   ├─ 060_SYMBOL_trend_following_optimal_nBars_SL.pkl")
    print("   ├─ 060_SYMBOL_counter_trend_optimal_nBars_SL.pkl")
    print("   └─ 060_SYMBOL_time_filters.pkl")
    
    print("\n3. การจัดลำดับการทดสอบ:")
    print("   Step 1: เทรนโมเดลทั้ง 2 scenarios")
    print("   Step 2: หา optimal threshold แยกตาม scenario")
    print("   Step 3: หา optimal nBars_SL แยกตาม scenario")
    print("   Step 4: ทดสอบ combined performance")
    
    print("\n4. การใช้งานใน Production:")
    print("   - โหลด threshold ตาม scenario ที่เลือก")
    print("   - โหลด nBars_SL ตาม scenario ที่เลือก")
    print("   - ใช้ค่าที่เหมาะสมกับสถานการณ์ตลาด")

def create_multi_model_threshold_functions():
    """
    สร้างตัวอย่างฟังก์ชันสำหรับ Multi-Model threshold optimization
    """
    print("\n📝 ตัวอย่างฟังก์ชันสำหรับ Multi-Model Architecture")
    print("="*60)
    
    function_template = '''
def find_optimal_threshold_multi_model(models_dict, val_df, symbol, timeframe):
    """
    หา optimal threshold แยกตาม scenario
    
    Args:
        models_dict: dict ของโมเดลแต่ละ scenario
        val_df: validation data
        symbol: สัญลักษณ์
        timeframe: timeframe
    
    Returns:
        dict: threshold สำหรับแต่ละ scenario
    """
    optimal_thresholds = {}
    
    for scenario_name, model_info in models_dict.items():
        model = model_info['model']
        scaler = model_info['scaler']
        features = model_info['features']
        
        print(f"🔍 หา optimal threshold สำหรับ {scenario_name}")
        
        # ใช้ฟังก์ชันเดิมแต่บันทึกแยก scenario
        best_threshold = find_best_threshold_on_val(
            model=model,
            scaler=scaler,
            val_df=val_df,
            model_features=features
        )
        
        optimal_thresholds[scenario_name] = best_threshold
        
        # บันทึกแยกตาม scenario
        threshold_file = f"thresholds/{timeframe}_{symbol}_{scenario_name}_optimal_threshold.pkl"
        with open(threshold_file, 'wb') as f:
            pickle.dump(best_threshold, f)
        
        print(f"✅ บันทึก threshold สำหรับ {scenario_name}: {best_threshold:.4f}")
    
    return optimal_thresholds

def find_optimal_nbars_sl_multi_model(models_dict, val_df, symbol, timeframe):
    """
    หา optimal nBars_SL แยกตาม scenario
    """
    optimal_nbars = {}
    
    for scenario_name, model_info in models_dict.items():
        print(f"🔍 หา optimal nBars_SL สำหรับ {scenario_name}")
        
        # ใช้ฟังก์ชันเดิมแต่บันทึกแยก scenario
        best_nbars = find_optimal_nbars_sl(
            val_df=val_df,
            symbol=symbol,
            timeframe=timeframe,
            entry_func=None,  # ปรับตามการใช้งาน
            best_entry_name=scenario_name
        )
        
        optimal_nbars[scenario_name] = best_nbars
        
        # บันทึกแยกตาม scenario
        nbars_file = f"thresholds/{timeframe}_{symbol}_{scenario_name}_optimal_nBars_SL.pkl"
        with open(nbars_file, 'wb') as f:
            pickle.dump(best_nbars, f)
        
        print(f"✅ บันทึก nBars_SL สำหรับ {scenario_name}: {best_nbars}")
    
    return optimal_nbars

def load_scenario_threshold(symbol, timeframe, scenario_name, default=0.5):
    """
    โหลด threshold สำหรับ scenario ที่กำหนด
    """
    threshold_file = f"thresholds/{timeframe}_{symbol}_{scenario_name}_optimal_threshold.pkl"
    
    try:
        with open(threshold_file, 'rb') as f:
            threshold = pickle.load(f)
        return threshold
    except:
        return default

def load_scenario_nbars(symbol, timeframe, scenario_name, default=6):
    """
    โหลด nBars_SL สำหรับ scenario ที่กำหนด
    """
    nbars_file = f"thresholds/{timeframe}_{symbol}_{scenario_name}_optimal_nBars_SL.pkl"
    
    try:
        with open(nbars_file, 'rb') as f:
            nbars = pickle.load(f)
        return nbars
    except:
        return default
    '''
    
    print(function_template)

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🚀 วิเคราะห์ Threshold และ nBars_SL สำหรับ Multi-Model Architecture")
    print("="*80)
    
    # 1. ตรวจสอบโครงสร้างไฟล์ปัจจุบัน
    file_types = check_threshold_files_structure()
    
    # 2. วิเคราะห์การใช้งาน threshold
    missing_threshold_files = analyze_current_threshold_usage()
    
    # 3. วิเคราะห์การใช้งาน nBars_SL
    missing_nbars_files = analyze_current_nbars_usage()
    
    # 4. แนะนำการปรับปรุง
    recommend_multi_model_optimization()
    
    # 5. สร้างตัวอย่างฟังก์ชัน
    create_multi_model_threshold_functions()
    
    print("\n" + "="*80)
    print("✅ การวิเคราะห์เสร็จสิ้น")

if __name__ == "__main__":
    main()

# ⚡ Quick Setup Guide - เครื่องใหม่

## 🚀 ติดตั้งแบบเร็ว (Quick Install)

### Windows:
```bash
# 1. ติดตั้ง Python 3.9-3.11 จาก python.org
# 2. เปิด Command Prompt และรัน:
install_dependencies.bat
```

### Linux/Mac:
```bash
# 1. ติดตั้ง Python 3.9-3.11
# 2. เปิด Terminal และรัน:
chmod +x install_dependencies.sh
./install_dependencies.sh
```

### Manual Install:
```bash
# สร้าง virtual environment
python -m venv trading_env

# เปิดใช้งาน (Windows)
trading_env\Scripts\activate

# เปิดใช้งาน (Linux/Mac)
source trading_env/bin/activate

# ติดตั้ง dependencies
pip install -r requirements.txt

# ทดสอบ
python test_installation.py
```

## 📋 สิ่งที่ต้องติดตั้งก่อน

### Windows:
1. **Python 3.9-3.11** จาก [python.org](https://www.python.org/downloads/)
2. **Microsoft Visual C++ Build Tools** จาก [Microsoft](https://visualstudio.microsoft.com/visual-cpp-build-tools/)

### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv build-essential wget
```

### macOS:
```bash
# ติดตั้ง Homebrew ก่อน
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# ติดตั้ง Python และ dependencies
brew install python3 ta-lib
```

## 🧪 ทดสอบการติดตั้ง

```bash
# เปิดใช้งาน virtual environment
# Windows: trading_env\Scripts\activate
# Linux/Mac: source trading_env/bin/activate

# รันการทดสอบ
python test_installation.py
```

## 🎯 Libraries หลักที่ต้องมี

| Library | Version | Purpose |
|---------|---------|---------|
| Python | 3.9-3.11 | Base |
| numpy | 1.24.3 | Numerical computing |
| pandas | 2.0.3 | Data manipulation |
| scikit-learn | 1.3.0 | Machine learning |
| lightgbm | 4.0.0 | Gradient boosting |
| TA-Lib | 0.4.25 | Technical analysis |
| pandas-ta | 0.3.14b0 | Technical analysis |
| matplotlib | 3.7.2 | Plotting |
| plotly | 5.15.0 | Interactive plots |

## ⚠️ ปัญหาที่พบบ่อย

### TA-Lib ติดตั้งไม่ได้:
```bash
# Windows: ดาวน์โหลด wheel file จาก
# https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
pip install TA_Lib-0.4.xx-cpxx-cpxx-win_amd64.whl

# Linux: ติดตั้ง build tools
sudo apt-get install build-essential

# macOS: ใช้ Homebrew
brew install ta-lib
```

### LightGBM ติดตั้งไม่ได้:
```bash
pip install --upgrade setuptools wheel
pip install lightgbm --no-cache-dir
```

## ✅ เสร็จแล้ว!

หลังจากติดตั้งเสร็จ สามารถรัน:
```bash
python python_LightGBM_15_Tuning.py
```

## 📁 ไฟล์ที่สร้าง

- `setup_guide_new_machine.md` - คู่มือละเอียด
- `requirements.txt` - รายการ libraries
- `test_installation.py` - ทดสอบการติดตั้ง
- `install_dependencies.bat` - Auto install (Windows)
- `install_dependencies.sh` - Auto install (Linux/Mac)
- `QUICK_SETUP.md` - คู่มือย่อ (ไฟล์นี้)

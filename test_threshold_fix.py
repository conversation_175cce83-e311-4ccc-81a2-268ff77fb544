#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข threshold สำหรับ Buy/Sell signals
"""

import os
import sys
import pandas as pd
import numpy as np

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_threshold_calculations():
    """ทดสอบการคำนวณ threshold ใหม่"""
    
    print("🧪 ทดสอบการคำนวณ Threshold ใหม่")
    print("="*60)
    
    # ทดสอบกับ threshold ต่างๆ
    base_thresholds = [0.3, 0.4, 0.5, 0.6, 0.7]
    
    print("📊 การคำนวณ Threshold สำหรับ Buy และ Sell:")
    print("-" * 60)
    print(f"{'Base Threshold':<15} {'Buy Threshold':<15} {'Sell Threshold':<15}")
    print("-" * 60)
    
    for base_threshold in base_thresholds:
        buy_threshold = base_threshold * 0.8   # ลด 20%
        sell_threshold = base_threshold * 0.7  # ลด 30%
        
        print(f"{base_threshold:<15.3f} {buy_threshold:<15.3f} {sell_threshold:<15.3f}")
    
    print("\n💡 เหตุผลการปรับ Threshold:")
    print("   📈 Buy Threshold = Base × 0.8 (ลด 20%) - เพื่อให้ได้ buy signals มากขึ้น")
    print("   📉 Sell Threshold = Base × 0.7 (ลด 30%) - เพื่อให้ได้ sell signals มากขึ้น (sell ยากกว่า)")

def simulate_signal_generation():
    """จำลองการสร้าง signals ด้วย threshold ใหม่"""
    
    print(f"\n🎯 จำลองการสร้าง Signals ด้วย Threshold ใหม่")
    print("="*60)
    
    # ใช้ข้อมูลจาก debug ที่ได้
    model_results = [
        {
            'name': 'AUDUSD trend_following',
            'buy_prob_mean': 0.408,
            'sell_prob_mean': 0.376,
            'buy_prob_max': 0.481,
            'sell_prob_max': 0.475
        },
        {
            'name': 'EURGBP trend_following', 
            'buy_prob_mean': 0.057,
            'sell_prob_mean': 0.575,
            'buy_prob_max': 0.065,
            'sell_prob_max': 0.619
        },
        {
            'name': 'AUDUSD counter_trend',
            'buy_prob_mean': 0.154,
            'sell_prob_mean': 0.446,
            'buy_prob_max': 0.156,
            'sell_prob_max': 0.448
        },
        {
            'name': 'EURGBP counter_trend',
            'buy_prob_mean': 0.062,
            'sell_prob_mean': 0.537,
            'buy_prob_max': 0.065,
            'sell_prob_max': 0.559
        }
    ]
    
    base_threshold = 0.5
    buy_threshold = base_threshold * 0.8   # 0.4
    sell_threshold = base_threshold * 0.7  # 0.35
    
    print(f"📊 Base Threshold: {base_threshold}")
    print(f"📈 Buy Threshold: {buy_threshold}")
    print(f"📉 Sell Threshold: {sell_threshold}")
    print()
    
    for model in model_results:
        print(f"🤖 {model['name']}:")
        
        # ทดสอบกับ mean probabilities
        buy_signal_mean = model['buy_prob_mean'] > buy_threshold
        sell_signal_mean = model['sell_prob_mean'] > sell_threshold
        
        # ทดสอบกับ max probabilities
        buy_signal_max = model['buy_prob_max'] > buy_threshold
        sell_signal_max = model['sell_prob_max'] > sell_threshold
        
        print(f"   📊 Buy prob: mean={model['buy_prob_mean']:.3f}, max={model['buy_prob_max']:.3f}")
        print(f"   📊 Sell prob: mean={model['sell_prob_mean']:.3f}, max={model['sell_prob_max']:.3f}")
        print(f"   🎯 Signals (mean): Buy={'✅' if buy_signal_mean else '❌'}, Sell={'✅' if sell_signal_mean else '❌'}")
        print(f"   🎯 Signals (max): Buy={'✅' if buy_signal_max else '❌'}, Sell={'✅' if sell_signal_max else '❌'}")
        print()

def test_with_different_base_thresholds():
    """ทดสอบกับ base threshold ต่างๆ"""
    
    print(f"\n📊 ทดสอบกับ Base Threshold ต่างๆ")
    print("="*60)
    
    # ใช้ข้อมูลจาก EURGBP ที่มี sell prob สูง
    sell_prob = 0.575
    buy_prob = 0.057
    
    base_thresholds = [0.3, 0.4, 0.5, 0.6, 0.7]
    
    print(f"ทดสอบกับ EURGBP (buy_prob={buy_prob:.3f}, sell_prob={sell_prob:.3f}):")
    print("-" * 70)
    print(f"{'Base':<6} {'Buy Th':<8} {'Sell Th':<8} {'Buy Signal':<12} {'Sell Signal':<12}")
    print("-" * 70)
    
    for base_threshold in base_thresholds:
        buy_threshold = base_threshold * 0.8
        sell_threshold = base_threshold * 0.7
        
        buy_signal = buy_prob > buy_threshold
        sell_signal = sell_prob > sell_threshold
        
        print(f"{base_threshold:<6.1f} {buy_threshold:<8.3f} {sell_threshold:<8.3f} {'✅' if buy_signal else '❌':<12} {'✅' if sell_signal else '❌':<12}")
    
    print(f"\n💡 สังเกต:")
    print(f"   - Base threshold 0.5 → Sell threshold 0.35 → ✅ Sell signal!")
    print(f"   - Base threshold 0.6 → Sell threshold 0.42 → ✅ Sell signal!")
    print(f"   - Base threshold 0.7 → Sell threshold 0.49 → ✅ Sell signal!")

def create_test_trading_scenario():
    """สร้างสถานการณ์การเทรดทดสอบ"""
    
    print(f"\n🎮 สร้างสถานการณ์การเทรดทดสอบ")
    print("="*60)
    
    # สร้างข้อมูล probabilities ที่หลากหลาย
    np.random.seed(42)
    n_samples = 20
    
    scenarios = []
    for i in range(n_samples):
        # สร้าง probabilities แบบสุ่มแต่สมจริง
        probs = np.random.dirichlet([2, 3, 5, 3, 2])  # ให้ class 2 (no_trade) มีโอกาสสูงสุด
        
        buy_prob = probs[3] + probs[4]  # weak_buy + strong_buy
        sell_prob = probs[0] + probs[1]  # strong_sell + weak_sell
        no_trade_prob = probs[2]
        
        scenarios.append({
            'id': i+1,
            'probs': probs,
            'buy_prob': buy_prob,
            'sell_prob': sell_prob,
            'no_trade_prob': no_trade_prob
        })
    
    # ทดสอบกับ threshold ใหม่
    base_threshold = 0.5
    buy_threshold = base_threshold * 0.8   # 0.4
    sell_threshold = base_threshold * 0.7  # 0.35
    
    buy_signals = 0
    sell_signals = 0
    no_signals = 0
    
    print(f"📊 ทดสอบ {n_samples} สถานการณ์:")
    print(f"📈 Buy Threshold: {buy_threshold:.3f}")
    print(f"📉 Sell Threshold: {sell_threshold:.3f}")
    print()
    
    for scenario in scenarios[:10]:  # แสดงแค่ 10 อันแรก
        buy_signal = scenario['buy_prob'] > buy_threshold
        sell_signal = scenario['sell_prob'] > sell_threshold
        
        if buy_signal:
            signal = "BUY"
            buy_signals += 1
        elif sell_signal:
            signal = "SELL"
            sell_signals += 1
        else:
            signal = "HOLD"
            no_signals += 1
        
        print(f"   {scenario['id']:2d}: Buy={scenario['buy_prob']:.3f}, Sell={scenario['sell_prob']:.3f} → {signal}")
    
    # นับสัญญาณทั้งหมด
    for scenario in scenarios:
        buy_signal = scenario['buy_prob'] > buy_threshold
        sell_signal = scenario['sell_prob'] > sell_threshold
        
        if buy_signal and not sell_signal:
            buy_signals += 1
        elif sell_signal and not buy_signal:
            sell_signals += 1
        else:
            no_signals += 1
    
    print(f"\n📊 สรุปผลลัพธ์ ({n_samples} scenarios):")
    print(f"   📈 BUY signals: {buy_signals} ({buy_signals/n_samples*100:.1f}%)")
    print(f"   📉 SELL signals: {sell_signals} ({sell_signals/n_samples*100:.1f}%)")
    print(f"   ⏸️ HOLD signals: {no_signals} ({no_signals/n_samples*100:.1f}%)")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🏗️ Test Threshold Fix for Buy/Sell Signals")
    print("="*60)
    
    # ทดสอบการคำนวณ threshold
    test_threshold_calculations()
    
    # จำลองการสร้าง signals
    simulate_signal_generation()
    
    # ทดสอบกับ base threshold ต่างๆ
    test_with_different_base_thresholds()
    
    # สร้างสถานการณ์ทดสอบ
    create_test_trading_scenario()
    
    print(f"\n✅ การทดสอบเสร็จสิ้น")
    print(f"\n📋 สรุปการแก้ไข:")
    print(f"   ✅ Buy Threshold = Base × 0.8 (ลด 20%)")
    print(f"   ✅ Sell Threshold = Base × 0.7 (ลด 30%)")
    print(f"   ✅ เพิ่ม debug messages แสดง threshold ที่ใช้")
    print(f"   ✅ ควรได้ sell signals มากขึ้น")
    
    print(f"\n🚀 ขั้นตอนต่อไป:")
    print(f"   1. รัน python_LightGBM_16_Signal.py")
    print(f"   2. ตรวจสอบไฟล์ trade_log ว่ามี SELL signals")
    print(f"   3. ดู debug messages ใน console")

if __name__ == "__main__":
    main()

จากโค้ดที่แนบมา ระบบของคุณมีโครงสร้างที่ดีและครบถ้วนสำหรับการเทรดด้วย ML (LightGBM) และมีการป้องกัน look-ahead bias, การเลือกฟีเจอร์, การ cross-validation, การวิเคราะห์ผลลัพธ์ ฯลฯ อย่างไรก็ตาม หากต้องการ เพิ่มประสิทธิภาพการเข้าที่แม่นยำขึ้น (entry accuracy) สามารถพิจารณาแนวทางดังนี้

1. ปรับปรุง Feature Engineering
เพิ่มฟีเจอร์ใหม่ ที่สะท้อน momentum, volatility, หรือ pattern เฉพาะ เช่น rolling max/min, drawdown, หรือ indicator เฉพาะกลุ่มตลาดนั้นๆ
สร้างฟีเจอร์ interaction (เช่น RSI × Volume_Spike, EMA_diff × ATR) เพื่อให้โมเดลจับความสัมพันธ์ซับซ้อนได้ดีขึ้น
ใช้ feature selection ที่เข้มข้นขึ้น เช่น Recursive Feature Elimination (RFE), SHAP values, หรือ Permutation Importance เพื่อคัดเฉพาะฟีเจอร์ที่มีผลต่อ entry จริงๆ
2. ปรับปรุง Entry Condition
ใช้โมเดลช่วยตัดสินใจร่วมกับ rule-based (Hybrid): เช่น ให้โมเดลเป็นตัวกรองสุดท้ายหลังผ่าน rule-based หรือใช้โมเดลเป็นตัวให้คะแนนความมั่นใจ (confidence score) แล้วเลือกเฉพาะ entry ที่โมเดลมั่นใจสูง
ปรับ threshold ของโมเดล ให้เหมาะสมกับแต่ละ symbol/timeframe โดยใช้ validation set (ซึ่งคุณมีแล้ว) และอาจใช้ metric อื่นนอกจาก expectancy เช่น precision/recall ของ entry
เพิ่ม ensemble ของ entry condition เช่น ใช้หลาย entry condition แล้วให้โมเดลเลือกหรือ voting
3. ปรับปรุง Target/Label
เปลี่ยน target จาก binary เป็น multi-class (เช่น strong buy, weak buy, no trade, weak sell, strong sell) เพื่อให้โมเดลแยกแยะ entry ที่มีคุณภาพสูงได้ดีขึ้น
ใช้ profit threshold ในการสร้าง target เช่น ให้เฉพาะ trade ที่ได้กำไรเกิน X point เป็น positive class
4. ปรับปรุง Data Split & Validation
ใช้ walk-forward validation หรือ rolling window เพื่อให้โมเดลเรียนรู้จากอดีตและทดสอบกับอนาคตจริงๆ
เพิ่ม test set ที่ไม่เคยเห็นมาก่อน เพื่อประเมินความแม่นยำของ entry จริง
5. ปรับปรุง Model Training
ปรับ hyperparameter tuning ของ LightGBM เช่น learning_rate, num_leaves, min_data_in_leaf, feature_fraction, scale_pos_weight ฯลฯ ด้วย GridSearch/Optuna
ลองใช้โมเดลอื่นเปรียบเทียบ เช่น CatBoost, XGBoost, หรือ Neural Network (MLP) เพื่อดูว่า entry accuracy ดีขึ้นหรือไม่
6. วิเคราะห์ Error/False Entry
วิเคราะห์ trade ที่โมเดลเข้าแล้วผิด (false positive/false negative) เพื่อหา pattern หรือฟีเจอร์ที่ทำให้โมเดลสับสน แล้วนำไปปรับปรุงฟีเจอร์หรือ entry condition
7. เพิ่มการวิเคราะห์ Feature Importance
ใช้ SHAP/Permutation Importance เพื่อดูว่าฟีเจอร์ไหนมีผลต่อ entry จริงๆ แล้วนำ insight ไปปรับปรุงฟีเจอร์หรือ entry rule
8. เพิ่มการ Filter เวลา/สถานการณ์
ใช้ time filter (เช่น เฉพาะช่วงที่ตลาด active หรือช่วงที่ win rate สูง) เพื่อกรอง entry ที่มีโอกาสสำเร็จสูง
ตัวอย่างจุดที่ควรปรับในโค้ด
ในฟังก์ชัน select_features อาจเพิ่มการใช้ SHAP หรือ Permutation Importance
ใน create_trade_cycles_with_model อาจเพิ่ม logic ให้โมเดลตัดสินใจร่วมกับ rule-based หรือ confidence threshold ที่ adaptive
ใน process_trade_targets อาจปรับ target ให้ละเอียดขึ้น
ใน main loop อาจเพิ่มการวิเคราะห์ error ของ entry
สรุป:
การเพิ่มประสิทธิภาพ entry ที่แม่นยำขึ้นควรเน้นที่การปรับปรุงฟีเจอร์, การเลือก entry condition ที่เหมาะสม, การปรับ threshold, การวิเคราะห์ข้อผิดพลาด, และการใช้ validation ที่สะท้อนสถานการณ์จริงมากขึ้น

หากต้องการตัวอย่างโค้ดหรือแนวทางเฉพาะจุด แจ้งรายละเอียดเพิ่มเติมได้ครับ!

+++++++++++++++

วิเคราะห์ประสิทธิภาพโมเดล (M30 และ H1)
1. ภาพรวมผลลัพธ์
Accuracy (ความถูกต้อง): ส่วนใหญ่สูงกว่า 0.80 ทั้งสองช่วงเวลา (M30, H1) ถือว่าดี
AUC (Area Under Curve): ส่วนใหญ่ 0.81-0.87 แสดงว่าโมเดลแยกแยะคลาสได้ดี
F1 Score: อยู่ในช่วง 0.66-0.73 ถือว่าดีสำหรับงานที่มี class imbalance
CV_Accuracy / CV_AUC: หลายไฟล์มีค่า 0.000 หรือ 0.500 แปลว่า cross-validation ไม่ได้ผล หรือข้อมูลไม่พอในบาง fold
2. ข้อสังเกต
บางไฟล์ CV_Accuracy/CV_AUC = 0 หรือ 0.5
สาเหตุอาจเกิดจากข้อมูลใน fold นั้นมีคลาสเดียว หรือจำนวนข้อมูลน้อยเกินไป ทำให้วัดผลไม่ได้
ผลลัพธ์โดยรวมของทั้งสอง timeframe ใกล้เคียงกัน
ไม่มี timeframe ไหนที่โดดเด่นกว่ากันชัดเจน
ข้อเสนอแนะเพื่อเพิ่มประสิทธิภาพ (จากโค้ดและลำดับขั้นตอน)
A. ข้อมูลและ Cross-Validation
ตรวจสอบข้อมูลในแต่ละ fold ของ TimeSeriesSplit
เพิ่มการเช็คว่าแต่ละ fold มีคลาสครบทั้ง 0 และ 1 ก่อนวัดผล
ถ้า fold ไหนมีคลาสเดียว ให้ข้ามหรือปรับขนาด fold
เพิ่มจำนวนข้อมูล หรือปรับขนาด test_size ใน TimeSeriesSplit
เพื่อให้แต่ละ fold มีข้อมูลเพียงพอ
B. Feature Engineering
เพิ่ม/ปรับปรุงฟีเจอร์
วิเคราะห์ feature importance ข้าม asset แล้วนำ features สำคัญมาใช้จริง
ทดลองเพิ่มฟีเจอร์ใหม่ เช่น volatility, seasonality, หรือฟีเจอร์จาก external data
ลด multicollinearity
ตรวจสอบและตัดฟีเจอร์ที่มี correlation สูงเกินไป
C. Class Imbalance
SMOTE/Undersampling
ใช้ SMOTE หรือเทคนิคอื่นๆ เฉพาะใน train set เท่านั้น
ตรวจสอบว่า minority class มีจำนวนเพียงพอสำหรับ SMOTE
D. Hyperparameter Tuning
ปรับพารามิเตอร์ LightGBM
ใช้ GridSearch/RandomSearch กับ validation set
ทดลอง learning_rate, num_leaves, min_data_in_leaf, scale_pos_weight ฯลฯ
E. Threshold Optimization
ปรับ threshold การตัดสินใจ
ใช้ validation set หา threshold ที่เหมาะสมกับแต่ละ asset/timeframe
F. การแบ่งข้อมูล
แบ่งข้อมูลแบบ time-based split
ป้องกัน data leakage โดยไม่ใช้ข้อมูลอนาคตใน train set
G. รายงานผล
เพิ่มการวิเคราะห์ confusion matrix, precision, recall, PR curve
เพื่อดูว่าคลาสไหนที่โมเดลยังทำได้ไม่ดี
สรุป
โมเดลมีประสิทธิภาพดีในภาพรวม แต่ควรปรับปรุง cross-validation, การเลือกฟีเจอร์, การจัดการ class imbalance และการ tuning พารามิเตอร์
ควรตรวจสอบข้อมูลในแต่ละ fold ของ cross-validation และเพิ่มการวิเคราะห์เชิงลึกในแต่ละ asset/timeframe
การปรับ threshold และการเลือกฟีเจอร์ที่สำคัญข้าม asset จะช่วยให้โมเดล robust และ generalize ได้ดีขึ้น
หากต้องการรายละเอียดการปรับปรุงแต่ละจุด สามารถสอบถามเพิ่มเติมได้ครับ
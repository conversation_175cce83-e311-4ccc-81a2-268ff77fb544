#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขปัญหาการโหลด CSV ที่ใช้ separators ต่างๆ
"""

import os
import sys
import pandas as pd
import numpy as np

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_specific_problematic_file():
    """ทดสอบไฟล์ที่มีปัญหาโดยเฉพาะ"""
    
    print("🧪 ทดสอบไฟล์ที่มีปัญหาโดยเฉพาะ")
    print("="*60)
    
    # ไฟล์ที่มีปัญหา
    problematic_file = "CSV_Files_Fixed/EURUSD_M30_FIXED.csv"
    
    if not os.path.exists(problematic_file):
        print(f"❌ ไม่พบไฟล์: {problematic_file}")
        return
    
    print(f"📁 ทดสอบไฟล์: {problematic_file}")
    
    # ทดสอบการอ่านด้วย separators ต่างๆ
    separators = [
        ('comma', ','),
        ('tab', '\t'),
        ('semicolon', ';')
    ]
    
    for sep_name, sep in separators:
        try:
            df = pd.read_csv(problematic_file, sep=sep)
            print(f"📊 {sep_name}: {df.shape[1]} คอลัมน์, {df.shape[0]} แถว")
            
            if df.shape[1] > 1:
                print(f"   ✅ คอลัมน์: {list(df.columns)}")
                print(f"   📄 ตัวอย่างข้อมูล:")
                print(f"      {df.head(2).to_string()}")
                
                # ตรวจสอบ header row
                if df.iloc[0].astype(str).str.contains('<').any():
                    print(f"   🔄 พบ header row ที่ต้องลบ")
                    df_clean = df.drop(index=0).reset_index(drop=True)
                    print(f"   📊 หลังลบ header: {df_clean.shape}")
                    print(f"   📄 ข้อมูลหลังลบ header:")
                    print(f"      {df_clean.head(2).to_string()}")
                
        except Exception as e:
            print(f"❌ {sep_name}: {e}")

def test_load_and_process_data_function():
    """ทดสอบฟังก์ชัน load_and_process_data ที่แก้ไขแล้ว"""
    
    print(f"\n🧪 ทดสอบฟังก์ชัน load_and_process_data ที่แก้ไขแล้ว")
    print("="*70)
    
    try:
        # ตรวจสอบ signature ของฟังก์ชัน
        from python_LightGBM_16_Signal import load_and_process_data
        import inspect
        
        sig = inspect.signature(load_and_process_data)
        print(f"📊 Function signature: {sig}")
        print(f"📊 Parameters: {list(sig.parameters.keys())}")
        
        # ทดสอบกับไฟล์ที่มีปัญหา
        test_file = "CSV_Files_Fixed/EURUSD_M30_FIXED.csv"
        
        if os.path.exists(test_file):
            print(f"\n🎯 ทดสอบกับไฟล์: {test_file}")
            
            try:
                # เรียกใช้ฟังก์ชันด้วย parameters ที่ถูกต้อง
                result = load_and_process_data(
                    file=test_file,
                    symbol="EURUSD",
                    timeframe=30,
                    run_identifier=1,
                    model=None,
                    scaler=None,
                    nBars_SL=20,
                    confidence_threshold=0.5
                )
                
                if result and len(result) == 6:
                    train_data, val_data, test_data, df, trade_df, stats = result
                    
                    if df is not None:
                        print(f"   ✅ โหลดข้อมูลสำเร็จ")
                        print(f"      📊 DataFrame shape: {df.shape}")
                        print(f"      📊 คอลัมน์: {list(df.columns)}")
                        print(f"      📊 ข้อมูลตัวอย่าง:")
                        print(f"         {df.head(2).to_string()}")
                        
                        # ตรวจสอบ data types
                        print(f"      📊 Data types:")
                        for col in df.columns:
                            print(f"         {col}: {df[col].dtype}")
                            
                    else:
                        print(f"   ❌ DataFrame เป็น None")
                        
                    if train_data is not None:
                        print(f"      📊 Training data shape: {train_data.shape}")
                    if val_data is not None:
                        print(f"      📊 Validation data shape: {val_data.shape}")
                    if test_data is not None:
                        print(f"      📊 Test data shape: {test_data.shape}")
                        
                else:
                    print(f"   ❌ ผลลัพธ์ไม่ถูกต้อง: {type(result)}")
                    
            except Exception as e:
                print(f"   ❌ เกิดข้อผิดพลาด: {e}")
                import traceback
                traceback.print_exc()
        else:
            print(f"❌ ไม่พบไฟล์ทดสอบ: {test_file}")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def test_all_files_quick():
    """ทดสอบไฟล์ทั้งหมดแบบเร็ว"""
    
    print(f"\n🚀 ทดสอบไฟล์ทั้งหมดแบบเร็ว")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import test_groups
        
        success_count = 0
        total_count = 0
        
        for group_name, files in test_groups.items():
            print(f"\n📂 กลุ่ม {group_name}:")
            
            for file in files:
                total_count += 1
                print(f"   📁 {os.path.basename(file)}: ", end="")
                
                if not os.path.exists(file):
                    print("❌ ไม่พบไฟล์")
                    continue
                
                try:
                    # ลองอ่านด้วย separators ต่างๆ
                    df = None
                    for sep_name, sep in [('comma', ','), ('tab', '\t'), ('semicolon', ';')]:
                        try:
                            df_temp = pd.read_csv(file, sep=sep, nrows=5)
                            if len(df_temp.columns) > 1:
                                df = df_temp
                                print(f"✅ {sep_name} ({df.shape[1]} cols)")
                                success_count += 1
                                break
                        except:
                            continue
                    
                    if df is None:
                        print("❌ ไม่สามารถอ่านได้")
                        
                except Exception as e:
                    print(f"❌ Error: {e}")
        
        print(f"\n📊 สรุป: {success_count}/{total_count} ไฟล์อ่านได้สำเร็จ")
        success_rate = (success_count / total_count * 100) if total_count > 0 else 0
        print(f"📈 อัตราความสำเร็จ: {success_rate:.1f}%")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🏗️ Test CSV Loading Fix")
    print("="*50)
    
    # ทดสอบไฟล์ที่มีปัญหาโดยเฉพาะ
    test_specific_problematic_file()
    
    # ทดสอบฟังก์ชัน load_and_process_data
    test_load_and_process_data_function()
    
    # ทดสอบไฟล์ทั้งหมดแบบเร็ว
    test_all_files_quick()
    
    print(f"\n✅ การทดสอบเสร็จสิ้น")
    print(f"\n📋 สรุปการแก้ไข:")
    print(f"   ✅ เพิ่ม import traceback ใน run_main_analysis()")
    print(f"   ✅ ลองอ่านไฟล์ด้วย separators ต่างๆ (comma, tab, semicolon)")
    print(f"   ✅ ตรวจสอบและลบ header row อัตโนมัติ")
    print(f"   ✅ ตรวจสอบจำนวนคอลัมน์ก่อนดำเนินการต่อ")
    print(f"   ✅ แสดงข้อมูล debug ที่ละเอียด")

if __name__ == "__main__":
    main()

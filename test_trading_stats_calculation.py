#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการคำนวณสถิติการเทรด
"""

import numpy as np
import pandas as pd

def calculate_stats_test(subset):
    """ฟังก์ชันทดสอบ calculate_stats"""
    wins = subset[subset['Profit'] > 0]
    losses = subset[subset['Profit'] < 0]
    num_wins = len(wins)
    num_losses = len(losses)
    total = num_wins + num_losses

    win_rate = (num_wins / total) * 100 if total > 0 else 0
    avg_win = wins['Profit'].mean() if num_wins > 0 else 0
    avg_loss = abs(losses['Profit'].mean()) if num_losses > 0 else 0
    expectancy = (avg_win * (win_rate / 100)) - (avg_loss * (1 - (win_rate / 100)))
    
    return {
        "win_rate": round(win_rate, 2), 
        "expectancy": round(expectancy, 3),
        "num_wins": num_wins,
        "num_losses": num_losses,
        "total": total,
        "avg_win": avg_win,
        "avg_loss": avg_loss
    }

def analyze_trade_performance_test(trades_df):
    """ฟังก์ชันทดสอบ analyze_trade_performance"""
    analysis = {}
    
    # วิเคราะห์ Buy + Sell Trades
    analysis['buy_sell'] = calculate_stats_test(trades_df)
    
    return analysis

def test_trading_stats_calculation():
    """ทดสอบการคำนวณสถิติการเทรด"""
    
    print("🧪 ทดสอบการคำนวณสถิติการเทรด")
    print("="*60)
    
    # สร้างข้อมูลทดสอบที่เหมือนกับปัญหาจริง
    np.random.seed(42)
    
    # สร้าง trade data ที่มี 145 เทรด
    num_trades = 145
    
    # สร้างกำไร/ขาดทุนแบบสมจริง
    profits = []
    for i in range(num_trades):
        if np.random.random() < 0.45:  # 45% ชนะ
            profit = np.random.uniform(10, 100)  # กำไร 10-100
        else:  # 55% แพ้
            profit = np.random.uniform(-80, -5)  # ขาดทุน 5-80
        profits.append(profit)
    
    # สร้าง DataFrame
    trade_df = pd.DataFrame({
        'Trade Type': ['Buy'] * num_trades,
        'Profit': profits,
        'Exit Condition': ['TP Hit'] * (num_trades // 3) + 
                         ['SL Hit'] * (num_trades // 3) + 
                         ['Technical Exit'] * (num_trades - 2 * (num_trades // 3))
    })
    
    print(f"📊 ข้อมูลทดสอบ:")
    print(f"  - จำนวนเทรด: {len(trade_df)}")
    print(f"  - กำไรรวม: {trade_df['Profit'].sum():.2f}")
    print(f"  - กำไรเฉลี่ย: {trade_df['Profit'].mean():.2f}")
    print(f"  - เทรดที่ชนะ: {len(trade_df[trade_df['Profit'] > 0])}")
    print(f"  - เทรดที่แพ้: {len(trade_df[trade_df['Profit'] < 0])}")
    
    # ทดสอบการคำนวณ
    print(f"\n🔧 ทดสอบการคำนวณสถิติ...")
    
    # 1. คำนวณด้วยฟังก์ชันทดสอบ
    perf = analyze_trade_performance_test(trade_df)
    stats = perf['buy_sell']
    
    print(f"\n✅ ผลการคำนวณ:")
    print(f"  - Win Rate: {stats['win_rate']:.2f}%")
    print(f"  - Expectancy: {stats['expectancy']:.3f}")
    print(f"  - จำนวนเทรดที่ชนะ: {stats['num_wins']}")
    print(f"  - จำนวนเทรดที่แพ้: {stats['num_losses']}")
    print(f"  - จำนวนเทรดรวม: {stats['total']}")
    print(f"  - กำไรเฉลี่ยต่อเทรดที่ชนะ: {stats['avg_win']:.2f}")
    print(f"  - ขาดทุนเฉลี่ยต่อเทรดที่แพ้: {stats['avg_loss']:.2f}")
    
    # 2. ทดสอบการสร้าง trade_stats (แบบเก่าที่ผิด)
    print(f"\n❌ การคำนวณแบบเก่า (ผิด):")
    
    best_stats = {
        'num_trades': stats['total'],
        'win_rate': stats['win_rate'],  # เป็นเปอร์เซ็นต์ (0-100)
        'expectancy': stats['expectancy']
    }
    
    # แบบเก่าที่ผิด
    winning_trades_old = int(best_stats['num_trades'] * best_stats['win_rate'])
    losing_trades_old = int(best_stats['num_trades'] * (1 - best_stats['win_rate']))
    
    print(f"  - เทรดที่ชนะ (ผิด): {winning_trades_old}")
    print(f"  - เทรดที่แพ้ (ผิด): {losing_trades_old}")
    print(f"  - Win Rate (ผิด): {best_stats['win_rate']:.2f}%")
    
    # 3. ทดสอบการสร้าง trade_stats (แบบใหม่ที่ถูก)
    print(f"\n✅ การคำนวณแบบใหม่ (ถูก):")
    
    # แปลง win_rate จากเปอร์เซ็นต์เป็นทศนิยม
    win_rate_decimal = best_stats['win_rate'] / 100.0
    
    winning_trades_new = int(best_stats['num_trades'] * win_rate_decimal)
    losing_trades_new = int(best_stats['num_trades'] * (1 - win_rate_decimal))
    
    # คำนวณกำไรจริง
    total_profit = trade_df['Profit'].sum()
    avg_profit_per_trade = trade_df['Profit'].mean()
    
    trade_stats = {
        'total_trades': best_stats['num_trades'],
        'winning_trades': winning_trades_new,
        'losing_trades': losing_trades_new,
        'win_rate': best_stats['win_rate'],  # เก็บเป็นเปอร์เซ็นต์สำหรับแสดงผล
        'total_profit': total_profit,
        'avg_profit_per_trade': avg_profit_per_trade,
        'expectancy': best_stats['expectancy']
    }
    
    print(f"  - เทรดที่ชนะ (ถูก): {trade_stats['winning_trades']}")
    print(f"  - เทรดที่แพ้ (ถูก): {trade_stats['losing_trades']}")
    print(f"  - Win Rate (ถูก): {trade_stats['win_rate']:.2f}%")
    print(f"  - กำไรรวม: {trade_stats['total_profit']:.2f}")
    print(f"  - กำไรเฉลี่ยต่อเทรด: {trade_stats['avg_profit_per_trade']:.2f}")
    print(f"  - Expectancy: {trade_stats['expectancy']:.2f}")
    
    # 4. ตรวจสอบความถูกต้อง
    print(f"\n🔍 ตรวจสอบความถูกต้อง:")
    
    actual_wins = len(trade_df[trade_df['Profit'] > 0])
    actual_losses = len(trade_df[trade_df['Profit'] < 0])
    actual_total = len(trade_df)
    actual_win_rate = (actual_wins / actual_total) * 100
    
    print(f"  - เทรดที่ชนะจริง: {actual_wins}")
    print(f"  - เทรดที่แพ้จริง: {actual_losses}")
    print(f"  - Win Rate จริง: {actual_win_rate:.2f}%")
    
    # เปรียบเทียบ
    win_diff = abs(trade_stats['winning_trades'] - actual_wins)
    loss_diff = abs(trade_stats['losing_trades'] - actual_losses)
    rate_diff = abs(trade_stats['win_rate'] - actual_win_rate)
    
    print(f"\n📊 ความแตกต่าง:")
    print(f"  - เทรดที่ชนะ: {win_diff} (ควรเป็น 0 หรือใกล้เคียง)")
    print(f"  - เทรดที่แพ้: {loss_diff} (ควรเป็น 0 หรือใกล้เคียง)")
    print(f"  - Win Rate: {rate_diff:.2f}% (ควรเป็น 0)")
    
    # สรุปผล
    if win_diff <= 1 and loss_diff <= 1 and rate_diff < 0.1:
        print(f"\n✅ การแก้ไขสำเร็จ! การคำนวณถูกต้อง")
        return True
    else:
        print(f"\n❌ การแก้ไขยังไม่สมบูรณ์")
        return False

def simulate_problem_case():
    """จำลองกรณีที่เกิดปัญหาจริง"""
    
    print(f"\n🔍 จำลองกรณีปัญหาจริง")
    print("="*60)
    
    # จำลองข้อมูลที่ทำให้เกิดปัญหา
    # จำนวนเทรด: 145
    # เทรดที่ชนะ: 6427 (ผิดปกติ)
    # เทรดที่แพ้: -6282 (ผิดปกติ)
    # Win Rate: 4433.00% (ผิดปกติ)
    
    # สมมติว่า win_rate จาก calculate_stats เป็น 44.33%
    simulated_stats = {
        'num_trades': 145,
        'win_rate': 44.33,  # เปอร์เซ็นต์
        'expectancy': 60.20
    }
    
    print(f"📊 ข้อมูลจำลอง:")
    print(f"  - จำนวนเทรด: {simulated_stats['num_trades']}")
    print(f"  - Win Rate: {simulated_stats['win_rate']:.2f}%")
    print(f"  - Expectancy: {simulated_stats['expectancy']:.2f}")
    
    # การคำนวณแบบเก่า (ผิด)
    print(f"\n❌ การคำนวณแบบเก่า (ที่ทำให้เกิดปัญหา):")
    winning_trades_old = int(simulated_stats['num_trades'] * simulated_stats['win_rate'])
    losing_trades_old = int(simulated_stats['num_trades'] * (1 - simulated_stats['win_rate']))
    
    print(f"  - เทรดที่ชนะ: {winning_trades_old} (ผิดปกติ!)")
    print(f"  - เทรดที่แพ้: {losing_trades_old} (ผิดปกติ!)")
    print(f"  - Win Rate: {simulated_stats['win_rate']:.2f}%")
    
    # การคำนวณแบบใหม่ (ถูก)
    print(f"\n✅ การคำนวณแบบใหม่ (แก้ไขแล้ว):")
    win_rate_decimal = simulated_stats['win_rate'] / 100.0
    winning_trades_new = int(simulated_stats['num_trades'] * win_rate_decimal)
    losing_trades_new = int(simulated_stats['num_trades'] * (1 - win_rate_decimal))
    
    print(f"  - เทรดที่ชนะ: {winning_trades_new} (ถูกต้อง)")
    print(f"  - เทรดที่แพ้: {losing_trades_new} (ถูกต้อง)")
    print(f"  - Win Rate: {simulated_stats['win_rate']:.2f}%")
    
    # ตรวจสอบ
    total_check = winning_trades_new + losing_trades_new
    print(f"\n🔍 ตรวจสอบ:")
    print(f"  - ผลรวม: {total_check} (ควรเท่ากับ {simulated_stats['num_trades']})")
    print(f"  - Win Rate คำนวณใหม่: {(winning_trades_new/simulated_stats['num_trades'])*100:.2f}%")
    
    return winning_trades_new, losing_trades_new

def main():
    """ฟังก์ชันหลัก"""
    
    print("🧪 ทดสอบการแก้ไขการคำนวณสถิติการเทรด")
    print("="*70)
    
    # ทดสอบการคำนวณ
    calculation_success = test_trading_stats_calculation()
    
    # จำลองกรณีปัญหา
    simulate_problem_case()
    
    print(f"\n🏁 สรุปผลการทดสอบ")
    print("="*70)
    
    if calculation_success:
        print("✅ การแก้ไขการคำนวณสถิติการเทรดสำเร็จ!")
        print("💡 ปัญหาที่แก้ไข:")
        print("  1. ✅ แปลง win_rate จากเปอร์เซ็นต์เป็นทศนิยมก่อนคำนวณ")
        print("  2. ✅ คำนวณ winning_trades และ losing_trades ถูกต้อง")
        print("  3. ✅ เพิ่มการคำนวณ total_profit และ avg_profit_per_trade")
        print("  4. ✅ Win Rate แสดงผลเป็นเปอร์เซ็นต์ปกติ")
        
        print(f"\n🚀 ผลลัพธ์ที่คาดหวัง:")
        print("  - เทรดที่ชนะ: จำนวนที่สมเหตุสมผล (ไม่ใช่ 6427)")
        print("  - เทรดที่แพ้: จำนวนที่สมเหตุสมผล (ไม่ใช่ -6282)")
        print("  - Win Rate: เปอร์เซ็นต์ปกติ 0-100% (ไม่ใช่ 4433%)")
        print("  - กำไรรวม: ค่าจริงจาก trade data")
        print("  - กำไรเฉลี่ย: ค่าจริงจาก trade data")
        
    else:
        print("❌ การแก้ไขยังไม่สมบูรณ์!")
    
    return calculation_success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)

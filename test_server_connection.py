#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

def test_server_connection():
    """ทดสอบการเชื่อมต่อ server"""
    
    url = 'http://127.0.0.1:54321/data'
    
    # สร้างข้อมูลทดสอบ (ข้อมูล GOLD M30)
    test_data = {
        "symbol": "GOLD",
        "timeframe_str": "PERIOD_M30",
        "bars": [
            {
                "time": 1737021600,  # 2025-01-15 10:00:00 UTC timestamp
                "open": 2650.0,
                "high": 2655.0,
                "low": 2648.0,
                "close": 2652.0,
                "volume": 1000,
                "tick_volume": 1000,
                "spread": 5,
                "real_volume": 1000
            },
            {
                "time": 1737023400,  # 2025-01-15 10:30:00 UTC timestamp
                "open": 2652.0,
                "high": 2658.0,
                "low": 2650.0,
                "close": 2656.0,
                "volume": 1200,
                "tick_volume": 1200,
                "spread": 5,
                "real_volume": 1200
            }
        ]
    }
    
    try:
        print("🔍 ทดสอบการเชื่อมต่อ server...")
        print(f"URL: {url}")
        
        # ส่งข้อมูลไป server
        response = requests.post(url, json=test_data, timeout=10)
        
        print(f"✅ Server ตอบกลับ: HTTP {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("📊 ผลลัพธ์จาก server:")
                print(f"  Signal: {result.get('signal', 'N/A')}")
                print(f"  Class: {result.get('class', 'N/A')}")
                print(f"  Confidence: {result.get('confidence', 'N/A')}")
                print(f"  Symbol: {result.get('symbol', 'N/A')}")
                print(f"  Timeframe: {result.get('timeframe_str', 'N/A')}")
                print(f"  Entry Price: {result.get('entry_price', 'N/A')}")
                print(f"  SL Price: {result.get('sl_price', 'N/A')}")
                print(f"  TP Price: {result.get('tp_price', 'N/A')}")
                print(f"  Threshold: {result.get('threshold', 'N/A')}")
                print(f"  nBars_SL: {result.get('nBars_SL', 'N/A')}")
                print(f"  Time Filters: {result.get('time_filters', 'N/A')}")
                
                return True
            except json.JSONDecodeError:
                print(f"❌ ไม่สามารถแปลง JSON ได้: {response.text[:200]}...")
                return False
        else:
            print(f"❌ Server ตอบกลับด้วย error: {response.text[:200]}...")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ ไม่สามารถเชื่อมต่อ server ได้")
        print("   - ตรวจสอบว่า server รันอยู่หรือไม่")
        print("   - ตรวจสอบ port 54321")
        return False
    except requests.exceptions.Timeout:
        print("❌ Server ไม่ตอบกลับภายในเวลาที่กำหนด")
        return False
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

if __name__ == "__main__":
    print("🚀 เริ่มทดสอบการเชื่อมต่อ server...")
    
    # รอ server เริ่มทำงาน
    print("⏳ รอ server เริ่มทำงาน...")
    time.sleep(3)
    
    success = test_server_connection()
    
    if success:
        print("\n✅ การทดสอบสำเร็จ - server ทำงานปกติ")
    else:
        print("\n❌ การทดสอบล้มเหลว - server มีปัญหา")

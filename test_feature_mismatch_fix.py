#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Feature Mismatch Fix
ทดสอบการแก้ไขปัญหา feature mismatch ในระบบ threshold
"""

import pandas as pd
import numpy as np
import sys
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

# เพิ่ม path สำหรับ import
sys.path.append('.')

def create_test_data_with_missing_features():
    """
    สร้างข้อมูลทดสอบที่มี feature mismatch
    """
    print("📊 Creating test data with feature mismatch scenario")
    
    # สร้างข้อมูลตัวอย่าง
    np.random.seed(42)
    n_samples = 200
    
    # Features ที่โมเดลต้องการ (จำลองจากข้อผิดพลาดจริง)
    model_features = [
        'RSI14', 'MACD', 'EMA50', 'EMA200', 'Volume_MA_20',
        'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'ADX_14_x_ATR',
        'Price_Change', 'Volume_Change', 'High_Low_Ratio',
        'Feature_A', 'Feature_B', 'Feature_C'  # Features ที่ขาดหายไป
    ]
    
    # Features ที่มีอยู่ใน validation data (บางส่วนขาดหายไป)
    available_features = [
        'RSI14', 'MACD', 'EMA50', 'EMA200', 'Volume_MA_20',
        'Price_Change', 'Volume_Change', 'High_Low_Ratio'
    ]
    
    # สร้างข้อมูล validation ที่มีเฉพาะ features บางส่วน
    val_data = {}
    for feature in available_features:
        val_data[feature] = np.random.randn(n_samples)
    
    # เพิ่ม Target และ Close
    val_data['Target'] = np.random.randint(0, 2, n_samples)
    val_data['Close'] = 1.1000 + np.random.randn(n_samples) * 0.001
    
    val_df = pd.DataFrame(val_data)
    
    print(f"✅ Created validation data:")
    print(f"   - Model expects: {len(model_features)} features")
    print(f"   - Validation has: {len(available_features)} features")
    print(f"   - Missing: {len(model_features) - len(available_features)} features")
    print(f"   - Missing features: {[f for f in model_features if f not in available_features]}")
    
    return val_df, model_features, available_features

def create_mock_trained_model(model_features):
    """
    สร้าง mock model ที่เทรนด้วย features ครบ
    """
    print(f"🤖 Creating mock trained model with {len(model_features)} features")
    
    # สร้าง mock training data ที่มี features ครบ
    n_train = 500
    X_train = np.random.randn(n_train, len(model_features))
    y_train = np.random.randint(0, 2, n_train)
    
    # สร้างและเทรนโมเดล
    model = RandomForestClassifier(n_estimators=10, random_state=42)
    model.fit(X_train, y_train)
    
    # สร้าง scaler
    scaler = StandardScaler()
    scaler.fit(X_train)
    
    print(f"✅ Model trained with features: {model_features[:5]}... (showing first 5)")
    
    return model, scaler

def test_find_best_threshold_simple_with_fix():
    """
    ทดสอบฟังก์ชัน find_best_threshold_simple ที่แก้ไขแล้ว
    """
    print(f"\n🧪 Testing find_best_threshold_simple with feature mismatch fix")
    print("-" * 70)
    
    try:
        # Import function
        from python_LightGBM_16_Signal import find_best_threshold_simple
        
        # สร้างข้อมูลทดสอบ
        val_df, model_features, available_features = create_test_data_with_missing_features()
        model, scaler = create_mock_trained_model(model_features)
        
        print(f"\n📊 Testing threshold optimization...")
        
        # ทดสอบฟังก์ชัน
        result = find_best_threshold_simple(
            model=model,
            scaler=scaler,
            val_df=val_df,
            model_features=model_features,
            thresholds=np.arange(0.3, 0.8, 0.1)  # ใช้ range เล็กเพื่อทดสอบเร็ว
        )
        
        print(f"✅ Function completed successfully!")
        print(f"📊 Result threshold: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_backtest_with_fix():
    """
    ทดสอบฟังก์ชัน Enhanced Backtest ที่แก้ไขแล้ว
    """
    print(f"\n🧪 Testing Enhanced Backtest with feature mismatch fix")
    print("-" * 70)
    
    try:
        # Import functions
        from python_LightGBM_16_Signal import (
            find_optimal_threshold_enhanced_backtest,
            analyze_market_conditions_for_threshold
        )
        
        # สร้างข้อมูลทดสอบ
        val_df, model_features, available_features = create_test_data_with_missing_features()
        model, scaler = create_mock_trained_model(model_features)
        
        # เพิ่ม Profit column สำหรับ Enhanced Backtest
        val_df['Profit'] = np.random.randn(len(val_df)) * 10
        
        # วิเคราะห์ market conditions
        market_analysis = analyze_market_conditions_for_threshold(val_df, "EURUSD")
        
        print(f"\n📊 Testing Enhanced Backtest...")
        
        # ทดสอบฟังก์ชัน
        result = find_optimal_threshold_enhanced_backtest(
            model=model,
            scaler=scaler,
            val_df=val_df,
            model_features=model_features,
            scenario_name="trend_following",
            market_analysis=market_analysis,
            symbol="EURUSD"
        )
        
        print(f"✅ Enhanced Backtest completed successfully!")
        print(f"📊 Result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_threshold_system_with_fix():
    """
    ทดสอบระบบ threshold ทั้งหมดที่แก้ไขแล้ว
    """
    print(f"\n🧪 Testing Full Threshold System with feature mismatch fix")
    print("-" * 70)
    
    try:
        # Import function
        from python_LightGBM_16_Signal import find_optimal_threshold_multi_model
        
        # สร้างข้อมูลทดสอบ
        val_df, model_features, available_features = create_test_data_with_missing_features()
        
        # สร้าง mock models dict
        trend_model, trend_scaler = create_mock_trained_model(model_features)
        counter_model, counter_scaler = create_mock_trained_model(model_features)
        
        models_dict = {
            'trend_following': {
                'model': trend_model,
                'scaler': trend_scaler,
                'features': model_features
            },
            'counter_trend': {
                'model': counter_model,
                'scaler': counter_scaler,
                'features': model_features
            }
        }
        
        print(f"\n📊 Testing full threshold system...")
        
        # ทดสอบระบบทั้งหมด
        result = find_optimal_threshold_multi_model(
            models_dict=models_dict,
            val_df=val_df,
            symbol="EURUSD",
            timeframe=60
        )
        
        print(f"✅ Full threshold system completed successfully!")
        print(f"📊 Result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_all_tests():
    """
    รันการทดสอบทั้งหมด
    """
    print("🧪 Feature Mismatch Fix - Comprehensive Test")
    print("=" * 80)
    
    tests = [
        ("find_best_threshold_simple", test_find_best_threshold_simple_with_fix),
        ("enhanced_backtest", test_enhanced_backtest_with_fix),
        ("full_threshold_system", test_full_threshold_system_with_fix)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 Running test: {test_name}")
        try:
            success = test_func()
            results[test_name] = "✅ PASSED" if success else "❌ FAILED"
        except Exception as e:
            results[test_name] = f"❌ ERROR: {str(e)}"
    
    # สรุปผลการทดสอบ
    print(f"\n📊 Test Results Summary")
    print("=" * 80)
    
    for test_name, result in results.items():
        print(f"{test_name}: {result}")
    
    all_passed = all("✅ PASSED" in result for result in results.values())
    
    if all_passed:
        print(f"\n🎉 All tests passed! Feature mismatch fix is working correctly.")
    else:
        print(f"\n⚠️ Some tests failed. Please check the implementation.")
    
    return results

if __name__ == "__main__":
    results = run_all_tests()
    
    print(f"\n💡 The feature mismatch fix handles missing features by:")
    print(f"   1. Detecting missing features in validation data")
    print(f"   2. Creating complete feature matrix with zeros for missing features")
    print(f"   3. Copying available feature values")
    print(f"   4. Proceeding with model prediction safely")
    print(f"   5. Falling back to defaults if too many features are missing")

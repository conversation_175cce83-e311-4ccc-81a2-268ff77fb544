#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
แสดงตัวอย่างการแสดงผลใหม่ใน MT5 EA
"""

def preview_combined_display():
    """แสดงตัวอย่างการแสดงผลแบบรวม"""
    
    # ข้อมูลตัวอย่างจาก Python
    py_data = {
        'symbol': 'GOLD',
        'timeframe': 'H1',
        'signal': 'HOLD',
        'class': 'HOLD',
        'confidence': 0.4522,
        'threshold': 0.500,
        'spread': 25,
        'sl_bars': 6,
        'entry_price': 0.0,  # ไม่มีเพราะเป็น HOLD
        'time_filters': 'Days:[0,1,2,3,4],Hours:[7,8,11,21]'
    }
    
    # ข้อมูลตัวอย่างจาก MT5
    mt5_data = {
        'symbol': 'GOLD#',
        'magic': 16098,
        'mt5_spread': 25,
        'point_value': 1.000,
        'py_spread': 25,
        'py_sl_bars': 6,
        'py_threshold': 0.500
    }
    
    print("🎨 ตัวอย่างการแสดงผลใหม่ใน MT5 EA")
    print("="*60)
    
    # แสดงตัวอย่างการแสดงผลแบบรวม
    print("\n📊 การแสดงผลแบบ Comment (ใหม่):")
    print("="*50)
    
    combined_text = ""
    combined_text += "╔═══════════════════════════════════════╗\n"
    combined_text += "║           🐍 PYTHON ANALYSIS          ║\n"
    combined_text += "╠═══════════════════════════════════════╣\n"
    combined_text += f"║ Symbol: {py_data['symbol']:<8}  Timeframe: {py_data['timeframe']:<4}    ║\n"
    combined_text += f"║ Signal: {py_data['signal']:<8}  Class: {py_data['class']:<8}    ║\n"
    combined_text += f"║ Confidence: {py_data['confidence']:<6.3f}  Threshold: {py_data['threshold']:<6.3f} ║\n"
    combined_text += f"║ Spread: {py_data['spread']:<8}  SL_Bars: {py_data['sl_bars']:<6}    ║\n"
    
    # แสดงราคาเฉพาะเมื่อมี signal
    if py_data['signal'] in ['BUY', 'SELL']:
        combined_text += f"║ Entry Price: {py_data['entry_price']:<20.5f}    ║\n"
    
    combined_text += f"║ Time Filters: {py_data['time_filters']:<23} ║\n"
    combined_text += "╠═══════════════════════════════════════╣\n"
    
    # ส่วน MT5 Configuration
    combined_text += "║           🔧 MT5 CONFIGURATION        ║\n"
    combined_text += "╠═══════════════════════════════════════╣\n"
    combined_text += f"║ Symbol: {mt5_data['symbol']:<8}  Magic: {mt5_data['magic']:<10.0f}   ║\n"
    combined_text += f"║ MT5 Spread: {mt5_data['mt5_spread']:<6}  Point Value: {mt5_data['point_value']:<6.3f} ║\n"
    combined_text += f"║ Python Spread: {mt5_data['py_spread']:<6}  SL_Bars: {mt5_data['py_sl_bars']:<6}   ║\n"
    combined_text += f"║ Python Threshold: {mt5_data['py_threshold']:<18.3f} ║\n"
    combined_text += "╚═══════════════════════════════════════╝"
    
    print(combined_text)
    
    print("\n📋 การแสดงผลแบบ Label (เสริม):")
    print("="*50)
    
    label_text = ""
    label_text += "═══════════════════════════════════════\n"
    label_text += "🔧 MT5 CONFIGURATION\n"
    label_text += "═══════════════════════════════════════\n"
    label_text += f"Symbol: {mt5_data['symbol']}  Magic: {mt5_data['magic']:.0f}\n"
    label_text += f"MT5 Spread: {mt5_data['mt5_spread']}  Point Value: {mt5_data['point_value']:.3f}\n"
    label_text += f"Python Spread: {mt5_data['py_spread']}  SL_Bars: {mt5_data['py_sl_bars']}\n"
    label_text += f"Python Threshold: {mt5_data['py_threshold']:.3f}\n"
    
    print(label_text)

def preview_buy_signal_display():
    """แสดงตัวอย่างเมื่อมี BUY signal"""
    
    print("\n🟢 ตัวอย่างเมื่อมี BUY Signal:")
    print("="*50)
    
    # ข้อมูลตัวอย่าง BUY signal
    py_data = {
        'symbol': 'GOLD',
        'timeframe': 'H1',
        'signal': 'BUY',
        'class': 'STRONG_BUY',
        'confidence': 0.7850,
        'threshold': 0.500,
        'spread': 25,
        'sl_bars': 6,
        'entry_price': 2652.75,
        'time_filters': 'Days:[0,1,2,3,4],Hours:[7,8,11,21]'
    }
    
    mt5_data = {
        'symbol': 'GOLD#',
        'magic': 16098,
        'mt5_spread': 25,
        'point_value': 1.000,
        'py_spread': 25,
        'py_sl_bars': 6,
        'py_threshold': 0.500
    }
    
    combined_text = ""
    combined_text += "╔═══════════════════════════════════════╗\n"
    combined_text += "║           🐍 PYTHON ANALYSIS          ║\n"
    combined_text += "╠═══════════════════════════════════════╣\n"
    combined_text += f"║ Symbol: {py_data['symbol']:<8}  Timeframe: {py_data['timeframe']:<4}    ║\n"
    combined_text += f"║ Signal: {py_data['signal']:<8}  Class: {py_data['class']:<8}    ║\n"
    combined_text += f"║ Confidence: {py_data['confidence']:<6.3f}  Threshold: {py_data['threshold']:<6.3f} ║\n"
    combined_text += f"║ Spread: {py_data['spread']:<8}  SL_Bars: {py_data['sl_bars']:<6}    ║\n"
    combined_text += f"║ Entry Price: {py_data['entry_price']:<20.5f}    ║\n"
    combined_text += f"║ Time Filters: {py_data['time_filters']:<23} ║\n"
    combined_text += "╠═══════════════════════════════════════╣\n"
    combined_text += "║           🔧 MT5 CONFIGURATION        ║\n"
    combined_text += "╠═══════════════════════════════════════╣\n"
    combined_text += f"║ Symbol: {mt5_data['symbol']:<8}  Magic: {mt5_data['magic']:<10.0f}   ║\n"
    combined_text += f"║ MT5 Spread: {mt5_data['mt5_spread']:<6}  Point Value: {mt5_data['point_value']:<6.3f} ║\n"
    combined_text += f"║ Python Spread: {mt5_data['py_spread']:<6}  SL_Bars: {mt5_data['py_sl_bars']:<6}   ║\n"
    combined_text += f"║ Python Threshold: {mt5_data['py_threshold']:<18.3f} ║\n"
    combined_text += "╚═══════════════════════════════════════╝"
    
    print(combined_text)

def preview_error_display():
    """แสดงตัวอย่างเมื่อมี Error"""
    
    print("\n🔴 ตัวอย่างเมื่อมี Error:")
    print("="*50)
    
    # ข้อมูลตัวอย่าง Error
    py_data = {
        'symbol': 'GOLD',
        'timeframe': 'H1',
        'signal': 'ERROR',
        'class': 'ERROR',
        'confidence': 0.000,
        'threshold': 0.500,
        'spread': 25,
        'sl_bars': 6,
        'entry_price': 0.0,
        'time_filters': 'Connection Error'
    }
    
    mt5_data = {
        'symbol': 'GOLD#',
        'magic': 16098,
        'mt5_spread': 25,
        'point_value': 1.000,
        'py_spread': 25,
        'py_sl_bars': 6,
        'py_threshold': 0.500
    }
    
    combined_text = ""
    combined_text += "╔═══════════════════════════════════════╗\n"
    combined_text += "║           🐍 PYTHON ANALYSIS          ║\n"
    combined_text += "╠═══════════════════════════════════════╣\n"
    combined_text += f"║ Symbol: {py_data['symbol']:<8}  Timeframe: {py_data['timeframe']:<4}    ║\n"
    combined_text += f"║ Signal: {py_data['signal']:<8}  Class: {py_data['class']:<8}    ║\n"
    combined_text += f"║ Confidence: {py_data['confidence']:<6.3f}  Threshold: {py_data['threshold']:<6.3f} ║\n"
    combined_text += f"║ Spread: {py_data['spread']:<8}  SL_Bars: {py_data['sl_bars']:<6}    ║\n"
    combined_text += f"║ Time Filters: {py_data['time_filters']:<23} ║\n"
    combined_text += "╠═══════════════════════════════════════╣\n"
    combined_text += "║           🔧 MT5 CONFIGURATION        ║\n"
    combined_text += "╠═══════════════════════════════════════╣\n"
    combined_text += f"║ Symbol: {mt5_data['symbol']:<8}  Magic: {mt5_data['magic']:<10.0f}   ║\n"
    combined_text += f"║ MT5 Spread: {mt5_data['mt5_spread']:<6}  Point Value: {mt5_data['point_value']:<6.3f} ║\n"
    combined_text += f"║ Python Spread: {mt5_data['py_spread']:<6}  SL_Bars: {mt5_data['py_sl_bars']:<6}   ║\n"
    combined_text += f"║ Python Threshold: {mt5_data['py_threshold']:<18.3f} ║\n"
    combined_text += "╚═══════════════════════════════════════╝"
    
    print(combined_text)

def main():
    """ฟังก์ชันหลัก"""
    print("🎨 ตัวอย่างการแสดงผลใหม่ใน MT5 EA")
    print("="*60)
    print("📝 การปรับปรุง:")
    print("   ✅ เปลี่ยนจาก OBJPROP_TEXT เป็น Comment()")
    print("   ✅ แยกข้อมูล Python และ MT5 ออกจากกัน")
    print("   ✅ ใช้กรอบและสัญลักษณ์ที่สวยงาม")
    print("   ✅ จัดรูปแบบข้อความให้อ่านง่าย")
    print("   ✅ แสดงราคาเฉพาะเมื่อมี BUY/SELL signal")
    print("="*60)
    
    # แสดงตัวอย่างต่างๆ
    preview_combined_display()
    preview_buy_signal_display()
    preview_error_display()
    
    print("\n" + "="*60)
    print("🎯 ข้อดีของการแสดงผลใหม่:")
    print("   📊 แสดงข้อมูลครบถ้วนในที่เดียว")
    print("   🎨 รูปแบบสวยงามและเป็นระเบียบ")
    print("   📖 อ่านง่าย แยกส่วน Python และ MT5 ชัดเจน")
    print("   🔄 อัปเดตแบบ real-time ผ่าน Comment()")
    print("   💾 ไม่มีปัญหาขีดจำกัดของ OBJPROP_TEXT")
    print("   🎯 แสดงเฉพาะข้อมูลที่เกี่ยวข้อง")

if __name__ == "__main__":
    main()

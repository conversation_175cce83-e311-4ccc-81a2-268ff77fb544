#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข Optimal Parameters สำหรับ Multi-Model Architecture

Created: 2025-01-11
Author: AI Assistant
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
import json
from pathlib import Path

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_parse_filename_fix():
    """
    ทดสอบการแก้ไข parse_filename()
    """
    print("🔍 ทดสอบการแก้ไข parse_filename()")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import parse_filename
        
        test_files = [
            'CSV_Files_Fixed/GOLD_H1_FIXED.csv',
            'CSV_Files_Fixed/AUDUSD_H1_FIXED.csv',
            'CSV_Files_Fixed/USDJPY_H1_FIXED.csv'
        ]
        
        results = []
        
        for file in test_files:
            try:
                result = parse_filename(file)
                symbol = result['Name_Currency']
                timeframe = result['Timeframe_Currency']
                
                print(f"✅ {file}")
                print(f"   Symbol: {symbol}")
                print(f"   Timeframe: {timeframe}")
                
                results.append((symbol, timeframe))
                
            except Exception as e:
                print(f"❌ {file} → Error: {e}")
                results.append(None)
        
        success_count = len([r for r in results if r is not None])
        print(f"\n📊 สรุป: {success_count}/{len(test_files)} ไฟล์ทำงานได้")
        
        return results
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return []
    except Exception as e:
        print(f"❌ Error: {e}")
        return []

def test_optimization_workflow():
    """
    ทดสอบ workflow การหา optimal parameters
    """
    print("\n🎯 ทดสอบ workflow การหา optimal parameters")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import (
            load_scenario_models,
            run_multi_model_optimization,
            load_scenario_threshold,
            load_scenario_nbars
        )
        
        # ทดสอบกับ GOLD
        symbol = "GOLD"
        timeframe = 60
        
        print(f"🔍 ทดสอบกับ {symbol} M{timeframe}")
        
        # 1. ตรวจสอบโมเดล
        loaded_models = load_scenario_models(symbol, timeframe)
        
        if loaded_models:
            print(f"✅ โหลดโมเดลสำเร็จ: {len(loaded_models)} scenarios")
            
            # 2. รันการหา optimal parameters
            print(f"\n🚀 รันการหา optimal parameters...")
            
            result = run_multi_model_optimization(symbol, timeframe)
            
            if result:
                print(f"✅ Optimization สำเร็จ!")
                print(f"📊 Scenarios: {result['scenarios']}")
                print(f"📊 Thresholds: {result['optimal_thresholds']}")
                print(f"📊 nBars_SL: {result['optimal_nbars']}")
                
                # 3. ทดสอบการโหลดพารามิเตอร์
                print(f"\n🔍 ทดสอบการโหลดพารามิเตอร์...")
                
                for scenario in result['scenarios']:
                    threshold = load_scenario_threshold(symbol, timeframe, scenario)
                    nbars = load_scenario_nbars(symbol, timeframe, scenario)
                    
                    print(f"   {scenario}:")
                    print(f"     Threshold: {threshold}")
                    print(f"     nBars_SL: {nbars}")
                
                return True
            else:
                print(f"❌ Optimization ล้มเหลว")
                return False
        else:
            print(f"❌ ไม่พบโมเดลสำหรับ {symbol} M{timeframe}")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_file_creation():
    """
    ทดสอบการสร้างไฟล์ optimal parameters
    """
    print("\n📁 ทดสอบการสร้างไฟล์ optimal parameters")
    print("="*50)
    
    base_path = Path("LightGBM_Multi/thresholds")
    
    if not base_path.exists():
        print(f"❌ ไม่พบโฟลเดอร์: {base_path}")
        return False
    
    # ตรวจสอบไฟล์ที่ควรมี
    expected_files = [
        "060_GOLD_trend_following_optimal_threshold.pkl",
        "060_GOLD_counter_trend_optimal_threshold.pkl",
        "060_GOLD_trend_following_optimal_nBars_SL.pkl",
        "060_GOLD_counter_trend_optimal_nBars_SL.pkl"
    ]
    
    existing_files = []
    
    for file_name in expected_files:
        file_path = base_path / file_name
        
        if file_path.exists():
            print(f"✅ {file_name}")
            existing_files.append(file_name)
            
            # ทดสอบการโหลดไฟล์
            try:
                with open(file_path, 'rb') as f:
                    data = pickle.load(f)
                print(f"   📊 ข้อมูล: {data}")
            except Exception as e:
                print(f"   ❌ ไม่สามารถโหลดได้: {e}")
        else:
            print(f"❌ {file_name}")
    
    print(f"\n📊 สรุป: {len(existing_files)}/{len(expected_files)} ไฟล์มีอยู่")
    
    return len(existing_files) > 0

def test_prediction_with_optimized_params():
    """
    ทดสอบการทำนายด้วยพารามิเตอร์ที่ optimize แล้ว
    """
    print("\n🔮 ทดสอบการทำนายด้วยพารามิเตอร์ที่ optimize แล้ว")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import (
            predict_with_optimal_parameters,
            load_scenario_models
        )
        
        symbol = "GOLD"
        timeframe = 60
        
        # ตรวจสอบโมเดล
        loaded_models = load_scenario_models(symbol, timeframe)
        
        if not loaded_models:
            print(f"❌ ไม่พบโมเดลสำหรับ {symbol} M{timeframe}")
            return False
        
        # สร้างข้อมูลตัวอย่าง
        first_model = list(loaded_models.values())[0]
        required_features = first_model['features']
        
        print(f"📊 สร้างข้อมูลตัวอย่าง ({len(required_features)} features)")
        
        # สร้างข้อมูลตัวอย่าง
        np.random.seed(42)
        market_data = {}
        
        for feature in required_features:
            if feature == 'Target':
                market_data[feature] = 1
            elif 'Close' in feature:
                market_data[feature] = 1800.0
            elif 'High' in feature:
                market_data[feature] = 1810.0
            elif 'Low' in feature:
                market_data[feature] = 1790.0
            elif 'EMA200' in feature:
                market_data[feature] = 1750.0  # EMA200 ต่ำกว่าราคา = uptrend
            else:
                market_data[feature] = np.random.randn()
        
        # ทดสอบการทำนาย
        test_cases = ['buy', 'sell']
        
        for action_type in test_cases:
            print(f"\n🧪 ทดสอบการทำนาย {action_type.upper()}:")
            
            result = predict_with_optimal_parameters(
                symbol, timeframe, market_data, action_type
            )
            
            if result['success']:
                print(f"   ✅ การทำนายสำเร็จ")
                print(f"   📊 Prediction: {result['prediction']}")
                print(f"   📊 Confidence: {result['confidence']:.4f}")
                print(f"   📊 Scenario: {result['scenario']}")
                print(f"   📊 Threshold: {result['threshold']:.4f}")
                print(f"   📊 nBars_SL: {result['nBars_SL']}")
            else:
                print(f"   ❌ การทำนายล้มเหลว: {result['error']}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🚀 ทดสอบการแก้ไข Optimal Parameters")
    print("="*80)
    
    # Test 1: parse_filename fix
    parse_results = test_parse_filename_fix()
    
    # Test 2: optimization workflow
    optimization_success = test_optimization_workflow()
    
    # Test 3: file creation
    files_created = test_file_creation()
    
    # Test 4: prediction with optimized params
    prediction_success = test_prediction_with_optimized_params()
    
    print("\n" + "="*80)
    print("📊 สรุปผลการทดสอบ")
    print("="*80)
    
    print(f"✅ Parse filename fix: {'ผ่าน' if len(parse_results) > 0 else 'ล้มเหลว'}")
    print(f"✅ Optimization workflow: {'ผ่าน' if optimization_success else 'ล้มเหลว'}")
    print(f"✅ File creation: {'ผ่าน' if files_created else 'ล้มเหลว'}")
    print(f"✅ Prediction with optimized params: {'ผ่าน' if prediction_success else 'ล้มเหลว'}")
    
    overall_success = all([
        len(parse_results) > 0,
        optimization_success,
        files_created,
        prediction_success
    ])
    
    if overall_success:
        print(f"\n🎉 การแก้ไข Optimal Parameters สำเร็จ 100%!")
        print(f"🚀 ระบบ Multi-Model Architecture พร้อมใช้งานใน Production")
    else:
        print(f"\n⚠️ ยังมีปัญหาบางส่วนที่ต้องแก้ไข")
        
        if not optimization_success:
            print(f"💡 แนะนำ: ตรวจสอบการโหลดข้อมูล validation และ features")
        
        if not files_created:
            print(f"💡 แนะนำ: ตรวจสอบการบันทึกไฟล์ optimal parameters")

if __name__ == "__main__":
    main()

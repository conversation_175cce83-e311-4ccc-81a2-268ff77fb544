#+------------------------------------------------------------------+
#|                                                http_server.py    |
#|                    Receives Bar Data from MT5 via HTTP (Flask) |
#+------------------------------------------------------------------+
from flask import Flask, request, jsonify
import json
import time
import datetime
import MetaTrader5 as mt5
import pandas as pd
import threading
import pandas_ta as ta # Make sure pandas_ta is installed
from werkzeug.exceptions import BadRequest # นำเข้า BadRequest เพื่อจับ Error จาก Flask/Werkzeug
import os # Import os for path joining and existence checks
import joblib # Import joblib for loading models and scalers
import numpy as np # Import numpy for numerical operations
import traceback # Import traceback for detailed error logging
import pickle # Import pickle library (สำหรับโหลด features_list ถ้าบันทึกด้วย pickle)
import sys # Import sys
import pytz # Import pytz for timezone handling (recommended for timestamps)

# --- Import necessary components from python_LightGBM.py ---
try:
    # เพิ่ม Path ของโฟลเดอร์ที่เก็บ python_LightGBM.py เข้าไปใน sys.path ชั่วคราว
    # แก้ไข path นี้ให้ชี้ไปยังโฟลเดอร์ที่เก็บไฟล์ python_LightGBM.py ของคุณ
    python_lightgbm_folder = r'C:\Users\<USER>\test_gold' # *** แก้ Path นี้ ***
    if python_lightgbm_folder not in sys.path:
        sys.path.append(python_lightgbm_folder)
        print(f"Added {python_lightgbm_folder} to sys.path")

    # Import specific functions needed
    from python_LightGBM import load_model, load_scaler, select_features, safe_json_serialize

    # Define the model confidence threshold
    model_confidence_threshold = 0.50 # ใช้ค่า Threshold จากโมเดลของคุณ

    # Define the base path for your models
    MODEL_BASE_PATH = r'C:\Users\<USER>\test_gold\models' # *** แก้ Path นี้ ***
    print(f"Model base path set to: {MODEL_BASE_PATH}")

    # กำหนด Timezone ของ MT5 Server (มักจะเป็น UTC)
    MT5_TIMEZONE = pytz.utc # หรือ pytz.timezone('Etc/UTC') หรือ Timezone ของ Server Broker

except ImportError as e:
    print(f"Error: Could not import components from python_LightGBM.py - {e}")
    print("Please ensure python_LightGBM.py is in the specified path or in Python's path.")
    print("Exiting server initialization.")
    exit()
except Exception as e:
    print(f"An unexpected error occurred during import from python_LightGBM.py: {e}")
    traceback.print_exc()
    print("Exiting server initialization.")
    exit()

# --- Configuration ---
HTTP_PORT = 54321
HTTP_HOST = '127.0.0.1'

# MT5 Connection Configuration (ยังคงไว้ถ้าต้องการใช้ MT5 Library ใน Python)
# Note: These are needed only if Python needs to place trades directly via mt5.py library
# If MT5 EA places trades based on the signal, this might not be strictly necessary for the server itself.
# คุณอาจจะเรียก initialize_mt5() ตรงนี้ ถ้าต้องการใช้ MT5 Library สำหรับส่งคำสั่งเทรด
# initialize_mt5()

# MT5 Connection Configuration (ยังคงไว้ถ้าต้องการใช้ MT5 Library ใน Python)
# def initialize_mt5():
#     """Initialize connection to the MetaTrader 5 terminal"""
#     print("Initializing MT5 connection...")
#     if not mt5.is_initialized():
#         if not mt5.initialize(login=MT5_LOGIN, password=MT5_PASSWORD, server=MT5_SERVER, path=MT5_PATH, timeout=5000):
#             print(f"MT5 initialize() failed, error code = {mt5.last_error()}")
#             return False
#         print("MT5 initialized successfully.")
#         account_info = mt5.account_info()
#         if account_info:
#             print(f"Connected to account: {account_info.login}")
#         else:
#             print(f"Failed to get account info after initialize, error code = {mt5.last_error()}")
#             return False
#     else:
#         print("MT5 already initialized.")
#         account_info = mt5.account_info()
#         if account_info:
#             print(f"Connected to account: {account_info.login}")
#         else:
#              print(f"MT5 was initialized but failed to get account info, error code = {mt5.last_error()}")
#              return False
#
#     return True

# --- Add Timeframe Mapping ---
timeframe_map = {
    "PERIOD_M1": mt5.TIMEFRAME_M1,
    "PERIOD_M2": mt5.TIMEFRAME_M2,
    "PERIOD_M3": mt5.TIMEFRAME_M3,
    "PERIOD_M4": mt5.TIMEFRAME_M4,
    "PERIOD_M5": mt5.TIMEFRAME_M5,
    "PERIOD_M6": mt5.TIMEFRAME_M6,
    "PERIOD_M10": mt5.TIMEFRAME_M10,
    "PERIOD_M12": mt5.TIMEFRAME_M12,
    "PERIOD_M15": mt5.TIMEFRAME_M15,
    "PERIOD_M20": mt5.TIMEFRAME_M20,
    "PERIOD_M30": mt5.TIMEFRAME_M30,
    "PERIOD_H1": mt5.TIMEFRAME_H1,
    "PERIOD_H2": mt5.TIMEFRAME_H2,
    "PERIOD_H3": mt5.TIMEFRAME_H3,
    "PERIOD_H4": mt5.TIMEFRAME_H4,
    "PERIOD_H6": mt5.TIMEFRAME_H6,
    "PERIOD_H8": mt5.TIMEFRAME_H8,
    "PERIOD_H12": mt5.TIMEFRAME_H12,
    "PERIOD_D1": mt5.TIMEFRAME_D1,
    "PERIOD_W1": mt5.TIMEFRAME_W1,
    "PERIOD_MN1": mt5.TIMEFRAME_MN1,
}

timeframe_code_map = {
    "PERIOD_M1": 1, "PERIOD_M2": 5, "PERIOD_M3": 15, "PERIOD_M30": 30, "PERIOD_H1": 60, "PERIOD_H4": 240
    # เพิ่ม Timeframe ที่เหลือตาม folder structure ของคุณ
}

# --- Global Data Storage ---
market_data_store = {}
data_lock = threading.Lock()

# --- Dictionary to store the latest signal and confidence for each symbol/timeframe ---
# This will be used to send the signal back in the HTTP response.
# Key: (cleaned_symbol, timeframe_enum)
# Value: {"signal": str, "confidence": float, "timestamp": datetime}
latest_signals_data = {}
signals_lock = threading.Lock() # Lock for accessing latest_signals_data

# --- Flask App Setup ---
app = Flask(__name__)

# --- Model Loading Cache ---
loaded_models = {}
model_lock = threading.Lock()

def load_model_components(symbol, timeframe):
    """
    Loads the trained model, scaler, and feature list for a given symbol and timeframe.
    Uses a cache to avoid reloading.
    """
    
    key = (symbol, timeframe)
    with model_lock:
        if key in loaded_models:
            # print(f"Loading model components from cache for {symbol} (enum: {timeframe})") # Print น้อยลง
            return loaded_models[key]

        model_name = "LightGBM"

        model_dir = os.path.join(MODEL_BASE_PATH, f"{symbol}_{str(timeframe).zfill(3)}")
        model_path = os.path.join(model_dir, f"LightGBM_{symbol}_{str(timeframe).zfill(3)}_trained.pkl")
        scaler_path = os.path.join(model_dir, f"LightGBM_{symbol}_{str(timeframe).zfill(3)}_scaler.pkl")
        features_list_path = os.path.join(model_dir, f"LightGBM_{symbol}_{str(timeframe).zfill(3)}_features.pkl")

        print(f"Attempting to load model components from: {model_dir}")

        if not os.path.exists(model_path):
            print(f"Error: Model file not found at {model_path}")
            return None, None, None
        if not os.path.exists(scaler_path):
            print(f"Error: Scaler file not found at {scaler_path}")
            return None, None, None
        if not os.path.exists(features_list_path):
            print(f"Error: Features list file not found at {features_list_path}")
            return None, None, None

        try:
            print(f"ทดสอบ model_path ก่อนโหลด model : {model_path} scaler : {scaler_path}")
            
            model = load_model(model_name,symbol,timeframe)
            scaler = load_scaler(model_name,symbol,timeframe)
            with open(features_list_path, 'rb') as f:
                try:
                    features_list = pickle.load(f)
                except Exception:
                    f.seek(0)
                    features_list = joblib.load(f)

            print(f"Successfully loaded model components for {symbol} (enum: {timeframe})")

            loaded_models[key] = (model, scaler, features_list)

            return model, scaler, features_list

        except FileNotFoundError as e:
            print(f"Error loading model components: File not found - {e}")
            traceback.print_exc()
            return None, None, None
        except Exception as e:
            print(f"Error loading model components for {symbol} (enum: {timeframe}): {e}")
            traceback.print_exc()
            return None, None, None

# --- Data Processing Function (จะเรียกใช้ใน Thread แยก) ---
def process_data_and_trade(symbol, timeframe):
    """
    Calculates indicators, runs model, determines trade signal and confidence.
    This runs in a separate thread.
    """

    print(f"[{datetime.datetime.now()}] Processing data for {symbol} ({timeframe})...")

    df = None
    with data_lock:
        key = (symbol, timeframe)
        if key in market_data_store and len(market_data_store[key]) > 0:
            df = market_data_store[key].copy()
            latest_bar_dt = df.index[-1] # Get the timestamp of the latest bar
        else:
            print(f"[{datetime.datetime.now()}] No sufficient data yet for {symbol} ({timeframe}). Waiting for more bars.")
            return

    # --- แก้ไข: เปลี่ยนชื่อคอลัมน์ให้เป็นตัวพิมพ์ใหญ่เพื่อให้ตรงกับโค้ด Indicator ---
    # คอลัมน์ที่ต้องการเปลี่ยน: 'open', 'high', 'low', 'close', 'volume' ให้เป็น: 'Open', 'High', 'Low', 'Close', 'Volume'
    rename_map = {
        'open': 'Open',
        'high': 'High',
        'low': 'Low',
        'close': 'Close',
        'volume': 'Volume'
    }
    # ใช้ .rename() เพื่อเปลี่ยนชื่อคอลัมน์ โดยใช้ errors='ignore' เพื่อไม่ให้ error ถ้าคอลัมน์ไม่มี
    df_features = df.rename(columns=rename_map, errors='ignore')

    # ตรวจสอบว่าเปลี่ยนชื่อคอลัมน์หลักสำเร็จหรือไม่
    required_price_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
    if not all(col in df_features.columns for col in required_price_cols):
        print(f"Error: Missing required price columns after renaming: {[col for col in required_price_cols if col not in df_features.columns]}")
        # Print คอลัมน์ที่มีอยู่เพื่อ Debug
        print(f"Available columns in df_features: {df_features.columns.tolist()}")
        return # หยุดการประมวลผลถ้าคอลัมน์หลักไม่ครบ
    # --- จบการแก้ไข ---

    # --- ตรวจสอบจำนวนข้อมูลที่เพียงพอสำหรับการคำนวณ Indicator ---
    required_bars = 202 # ปรับตามการวิเคราะห์ Indicator และ Shift ของคุณ
    if len(df_features) < required_bars: # ใช้ df_features ที่เปลี่ยนชื่อคอลัมน์แล้ว
        print(f"[{datetime.datetime.now()}] Not enough data ({len(df_features)} bars) for {symbol} ({timeframe}). Minimum required: {required_bars}. Skipping processing.")
        return

    signal = "HOLD" # Default signal
    probability_tp_hit = 0.0 # Default confidence

    try:
        # --- 1. คำนวณ Indicators และสร้าง Features ทั้งหมดที่ใช้ในโมเดล ---
        # คัดลอก Logic จาก load_and_process_data ส่วน "3. สร้าง technical indicators" มาที่นี่
        # ใช้ df_features ที่เปลี่ยนชื่อคอลัมน์เป็นตัวพิมพ์ใหญ่แล้ว

        # แสดงผล
        print("ข้อมูลดิบที่โหลดและแปลงเป็นตัวเลข:")
        print(df_features.info())
        print(df_features.head())

        # --- เพิ่มฟีเจอร์วันและเวลา ---
        # ใน Server, Bar Time มาเป็น datetime index อยู่แล้ว
        df_features['Entry_DayOfWeek'] = df_features.index.dayofweek
        df_features['Entry_Hour'] = df_features.index.hour

        # --- สร้างฟีเจอร์สำหรับช่วงเวลาที่สำคัญ ---
        df_features['IsMorning'] = ((df_features['Entry_Hour'] >= 8) & (df_features['Entry_Hour'] < 12)).astype(int)
        df_features['IsAfternoon'] = ((df_features['Entry_Hour'] >= 12) & (df_features['Entry_Hour'] < 16)).astype(int)
        df_features['IsEvening'] = ((df_features['Entry_Hour'] >= 16) & (df_features['Entry_Hour'] < 20)).astype(int)
        df_features['IsNight'] = ((df_features['Entry_Hour'] >= 20) | (df_features['Entry_Hour'] < 4)).astype(int) # ตรวจสอบ Logic ช่วงเวลาอีกครั้งให้ถูกต้อง

        # --- Price action Features ---
        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('Open', 'Close')
        df_features["Bar_CL"] = 0.0
        df_features.loc[df_features['Close'] > df_features['Open'], "Bar_CL"] = 1.0
        df_features.loc[df_features['Close'] < df_features['Open'], "Bar_CL"] = -1.0

        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('Close', 'Open') และ .shift(1)
        df_features["Bar_CL_OC"] = 0.0
        df_features.loc[df_features['Close'] > np.maximum(df_features['Open'].shift(1), df_features['Close'].shift(1)), "Bar_CL_OC"] = 1.0
        df_features.loc[df_features['Close'] < np.minimum(df_features['Open'].shift(1), df_features['Close'].shift(1)), "Bar_CL_OC"] = -1.0

        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('Close', 'High', 'Low', 'Open') และ .shift(1)
        df_features["Bar_CL_HL"] = 0.0
        df_features.loc[(df_features['Close'] > df_features['High'].shift(1)) & (df_features['Close'] > df_features['Open']), "Bar_CL_HL"] = 1.0
        df_features.loc[(df_features['Close'] < df_features['Low'].shift(1)) & (df_features['Close'] < df_features['Open']), "Bar_CL_HL"] = -1.0

        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('Close', 'Open', 'High', 'Low') และ .shift(1)
        df_features["Bar_SW"] = 0.0
        df_features.loc[(df_features['Close'] > df_features['Open']) & (df_features['High'] > df_features['High'].shift(1)) & (df_features['Low'] > df_features['Low'].shift(1)), "Bar_SW"] = 1.0
        df_features.loc[(df_features['Close'] < df_features['Open']) & (df_features['High'] < df_features['High'].shift(1)) & (df_features['Low'] < df_features['Low'].shift(1)), "Bar_SW"] = -1.0

        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('Close', 'Open', 'High', 'Low') และ .shift(1)
        df_features["Bar_TL"] = 0.0
        df_features.loc[(df_features['Close'] > df_features['Open']) & (df_features['High'] < df_features['High'].shift(1)) & (df_features['Low'] < df_features['Low'].shift(1)), "Bar_TL"] = 1.0
        df_features.loc[(df_features['Close'] < df_features['Open']) & (df_features['High'] > df_features['High'].shift(1)) & (df_features['Low'] > df_features['Low'].shift(1)), "Bar_TL"] = 1.0

        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('Close', 'Open', 'Low', 'High') และ .shift(1)
        df_features["Bar_DTB"] = 0.0
        df_features.loc[(df_features['Close'] > df_features['Open']) & (df_features['Low'] == df_features['Low'].shift(1)), "Bar_DTB"] = 1.0
        df_features.loc[(df_features['Close'] < df_features['Open']) & (df_features['High'] == df_features['High'].shift(1)), "Bar_DTB"] = -1.0

        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('Close', 'Open', 'High', 'Low') และ .shift(1)
        df_features["Bar_OSB"] = 0.0
        df_features.loc[(df_features['Close'] > df_features['Open']) & (df_features['High'] > df_features['High'].shift(1)) & (df_features['Low'] < df_features['Low'].shift(1)), "Bar_OSB"] = 1.0
        df_features.loc[(df_features['Close'] < df_features['Open']) & (df_features['High'] > df_features['High'].shift(1)) & (df_features['Low'] < df_features['Low'].shift(1)), "Bar_OSB"] = -1.0

        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('Low', 'High', 'Close', 'Open') และ .shift(2)
        df_features["Bar_FVG"] = 0.0
        df_features.loc[(df_features["Low"] > df_features["High"].shift(2)) & (df_features["Close"] > df_features["Open"]), "Bar_FVG"] = 1.0
        df_features.loc[(df_features["High"] < df_features["Low"].shift(2)) & (df_features["Close"] < df_features["Open"]), "Bar_FVG"] = -1.0

        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('Open', 'Close', 'Low', 'High')
        df_features["Bar_longwick"] = 0.0
        epsilon = 1e-9
        lower_wick = (np.minimum(df_features['Open'], df_features['Close']) - df_features['Low']).replace(0, epsilon)
        upper_wick = (df_features['High'] - np.maximum(df_features['Open'], df_features['Close'])).replace(0, epsilon)
        pinbar_up = (df_features['High'] - np.minimum(df_features['Open'], df_features['Close'])) / lower_wick
        pinbar_down = (np.maximum(df_features['Open'], df_features['Close']) - df_features['Low']) / upper_wick
        df_features.loc[pinbar_up > pinbar_down, "Bar_longwick"] = pinbar_up
        df_features.loc[pinbar_down > pinbar_up, "Bar_longwick"] = -1 * pinbar_down

        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('High', 'Low', 'Close', 'Open')
        df_features['Price_Range'] = df_features["High"] - df_features["Low"]
        df_features['Price_Move'] = df_features["Close"] - df_features["Open"]

        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('Close', 'Open', 'High', 'Low') และ .shift(1)
        df_features['Price_Strangth'] = 0.0
        body_size_oc = np.abs(df_features["Close"]-df_features["Open"])
        body_size_ocp = np.abs(df_features["Close"].shift(1)-df_features["Open"].shift(1))
        body_size_hl = np.abs(df_features["High"]-df_features["Low"])
        body_size_hlp = np.abs(df_features["High"].shift(1)-df_features["Low"].shift(1))
        df_features.loc[(df_features["Close"] < df_features["Open"]) & (body_size_oc < body_size_ocp) & (body_size_hl < body_size_hlp), "Price_Strangth"] = 1
        df_features.loc[(df_features["Close"] > df_features["Open"]) & (body_size_oc < body_size_ocp) & (body_size_hl < body_size_hlp), "Price_Strangth"] = -1

        # --- Volume_MA20 ---
        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('Volume') และ .rolling().mean().shift(1)
        df_features['Volume_MA20'] = df_features['Volume'].rolling(20, min_periods=1).mean().shift(1)
        df_features['Volume_MA20'].fillna(df_features['Volume'].mean(), inplace=True) # จัดการ NaN เหมือนต้นฉบับ
        df_features['Volume_Spike'] = df_features['Volume'] / (df_features['Volume_MA20'] + 1e-10) # ป้องกันหารด้วย 0

        # --- EMA Calculation ---
        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('Close') และ ewm().mean().shift(1)
        df_features['EMA50'] = df_features['Close'].ewm(span=50, min_periods=1).mean().shift(1)
        df_features['EMA100'] = df_features['Close'].ewm(span=100, min_periods=1).mean().shift(1)
        df_features['EMA200'] = df_features['Close'].ewm(span=200, min_periods=1).mean().shift(1)

        # --- EMA Related Features ---
        # ใช้คอลัมน์ EMA ที่คำนวณแล้ว (ซึ่งได้จากการ shift(1))
        df_features['EMA_diff'] = (df_features['EMA50'] - df_features['EMA200'])
        df_features['MA_Cross'] = (df_features['EMA50'] > df_features['EMA200']).astype(int)
        df_features["Price_above_EMA50"] = (df_features["Close"] > df_features["EMA50"]).astype(int)

        # --- ความผันผวนระยะสั้น ---
        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('Close') และ pct_change().rolling().std().shift(1)
        df_features['Rolling_Vol_5'] = df_features['Close'].pct_change().rolling(5, min_periods=1).std().shift(1) # เพิ่ม min_periods=1
        df_features['Rolling_Vol_15'] = df_features['Close'].pct_change().rolling(15, min_periods=1).std().shift(1) # เพิ่ม min_periods=1

        # --- ระยะทางจาก EMA ---
        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('Close') และคอลัมน์ EMA ที่คำนวณแล้ว
        df_features['Dist_EMA50'] = (df_features['Close'] - df_features['EMA50']) / (df_features['EMA50'] + 1e-10) # ป้องกันหารด้วย 0
        df_features['Dist_EMA100'] = (df_features['Close'] - df_features['EMA100']) / (df_features['EMA100'] + 1e-10) # ป้องกันหารด้วย 0
        df_features['Dist_EMA200'] = (df_features['Close'] - df_features['EMA200']) / (df_features['EMA200'] + 1e-10) # ป้องกันหารด้วย 0

        # --- MACD Calculation ---
        # ใช้ ta.macd().shift(1)
        # ตรวจสอบว่า df_features มีคอลัมน์ 'Close' (ตัวพิมพ์ใหญ่) อยู่แล้ว
        macd = ta.macd(df_features["Close"]).shift(1)
        df_features = pd.concat([df_features, macd], axis=1)
        macd_line_col = 'MACD_12_26_9'
        if macd_line_col not in df_features.columns: print(f"Warning: MACD line column '{macd_line_col}' not found after calculation.")
        macd_signal_col = 'MACDs_12_26_9'
        if macd_signal_col not in df_features.columns: print(f"Warning: MACD signal column '{macd_signal_col}' not found after calculation.")

        # --- Features ที่ใช้ MACD ---
        # ใช้คอลัมน์ MACD ที่ shift แล้ว
        df_features["MACD_line_feature"] = 0.0
        if macd_line_col in df_features.columns:
            df_features.loc[df_features[macd_line_col] > 0.0, "MACD_line_feature"] = 1
            df_features.loc[df_features[macd_line_col] < 0.0, "MACD_line_feature"] = -1

        df_features["MACD_deep"] = 0.0
        if macd_line_col in df_features.columns and f'{macd_line_col}_shift(1)' in df_features.columns: # ตรวจสอบคอลัมน์ที่ shift แล้วด้วย
            # Note: df_features[macd_line_col].shift(1) จะสร้างคอลัมน์ชั่วคราว ถ้าไม่ได้เก็บไว้ใน df_features โดยตรง
            # แต่การใช้ loc กับ df_features[...] จะทำงานได้
            df_features.loc[df_features[macd_line_col] > df_features[macd_line_col].shift(1), "MACD_deep"] = 1
            df_features.loc[df_features[macd_line_col] < df_features[macd_line_col].shift(1), "MACD_deep"] = -1
        elif macd_line_col in df_features.columns:
            print(f"Warning: Cannot calculate MACD_deep for {symbol} ({timeframe}) due to insufficient data for shift(1).")

        df_features["MACD_signal_feature"] = 0.0
        if macd_line_col in df_features.columns and macd_signal_col in df_features.columns:
            df_features.loc[df_features[macd_line_col] > df_features[macd_signal_col], "MACD_signal_feature"] = 1
            df_features.loc[df_features[macd_line_col] < df_features[macd_signal_col], "MACD_signal_feature"] = -1
        elif macd_line_col in df_features.columns or macd_signal_col in df_features.columns:
            print(f"Warning: Cannot calculate MACD_signal_feature for {symbol} ({timeframe}) due to missing MACD or Signal column.")

        # --- RSI Calculation ---
        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('Close')
        window = 14
        delta = df_features["Close"].diff(1)
        gain = pd.Series(np.where(delta > 0, delta, 0), index=df_features.index)
        loss = pd.Series(np.where(delta < 0, -delta, 0), index=df_features.index)

        # ใช้ min_periods=window เพื่อให้ผลลัพธ์เหมือนตอนเทรน (ถ้าตอนเทรนใช้ min_periods เท่ากับ window)
        avg_gain = gain.rolling(window=window, min_periods=window).mean()
        avg_loss = loss.rolling(window=window, min_periods=window).mean()
        rs = avg_gain / (avg_loss + 1e-10)

        df_features["RSI14"] = (100 - (100 / (1 + rs))).shift(1) # คำนวณ RSI ที่แท่งปัจจุบัน แล้ว shift กลับ

        # --- Features ที่ใช้ RSI ---
        if 'RSI14' in df_features.columns:
            df_features["RSI_signal"] = np.select(
                [df_features["RSI14"] < 30, df_features["RSI14"] > 70],
                [-1, 1],
                default=0
            )
            df_features['RSI_Overbought'] = (df_features['RSI14'] > 70).astype(int)
            df_features['RSI_Oversold'] = (df_features['RSI14'] < 30).astype(int)

            # --- RSI Divergence ---
            df_features['RSI_Shift_temp'] = df_features['RSI14'].shift(2) # ใช้ชื่อชั่วคราว
            if 'RSI_Shift_temp' in df_features.columns and not df_features['RSI_Shift_temp'].isnull().all():
                df_features['RSI_Divergence'] = np.where(
                    (df_features['Close'] > df_features['Close'].shift(2)) & (df_features['RSI14'] < df_features['RSI_Shift_temp']),
                    1, np.where(
                        (df_features['Close'] < df_features['Close'].shift(2)) & (df_features['RSI14'] > df_features['RSI_Shift_temp']),
                        -1, 0
                    )
                )
                df_features.drop(columns=['RSI_Shift_temp'], inplace=True) # ลบคอลัมน์ชั่วคราว
            else:
                print(f"Warning: Cannot calculate RSI_Divergence for {symbol} ({timeframe}) due to insufficient data for shift(2).")
                # อาจจะ fillna ด้วย 0 หรือค่าอื่นตามที่คุณจัดการตอนเทรน
                df_features['RSI_Divergence'] = 0 # Default เป็น 0 หรือ NaN แล้วแต่ต้องการ
        else:
            print(f"Warning: RSI14 column not found for {symbol} ({timeframe}). Cannot calculate RSI related features.")
            # สร้างคอลัมน์ feature ที่เกี่ยวข้องกับ RSI ด้วยค่า Default (เช่น 0 หรือ NaN) ถ้ามันไม่มี
            rsi_related_cols = ["RSI_signal", "RSI_Overbought", "RSI_Oversold", "RSI_Divergence"]
            for col in rsi_related_cols:
                if col not in df_features.columns:
                    df_features[col] = 0 # Default เป็น 0 (ปรับตามความเหมาะสม)

        # --- Stochastic Oscillator Calculation ---
        # ใช้ ta.stoch().shift(1)
        # ตรวจสอบว่า df_features มีคอลัมน์ 'High', 'Low', 'Close' (ตัวพิมพ์ใหญ่) อยู่แล้ว
        stoch = ta.stoch(high=df_features["High"], low=df_features["Low"], close=df_features["Close"]).shift(1)
        df_features = pd.concat([df_features, stoch], axis=1)
        stoch_k_col = 'STOCHk_14_3_3'
        if stoch_k_col not in df_features.columns: print(f"Warning: Stoch K column '{stoch_k_col}' not found after calculation.")
        stoch_d_col = 'STOCHd_14_3_3'
        if stoch_d_col not in df_features.columns: print(f"Warning: Stoch D column '{stoch_d_col}' not found after calculation.")

        # --- Features ที่ใช้ Stochastic ---
        if stoch_k_col in df_features.columns and stoch_d_col in df_features.columns:
            df_features["STO_cross"] = 0.0
            df_features.loc[df_features[stoch_k_col] > df_features[stoch_d_col], "STO_cross"] = 1
            df_features.loc[df_features[stoch_k_col] < df_features[stoch_d_col], "STO_cross"] = -1

            df_features["STO_zone"] = 0.0
            df_features.loc[df_features[stoch_k_col] > 50, "STO_zone"] = 1
            df_features.loc[df_features[stoch_k_col] < 50, "STO_zone"] = -1

            df_features["STO_overbought"] = 0.0
            df_features['STO_overbought'] = (df_features[stoch_k_col] > 80).astype(int)

            df_features["STO_Oversold"] = 0.0
            df_features['STO_Oversold'] = (df_features[stoch_k_col] < 20).astype(int)
        else:
            print(f"Warning: Stoch K/D columns not found for {symbol} ({timeframe}). Cannot calculate Stoch related features.")
            # สร้างคอลัมน์ feature ที่เกี่ยวข้องกับ Stoch ด้วยค่า Default (เช่น 0 หรือ NaN) ถ้ามันไม่มี
            stoch_related_cols = ["STO_cross", "STO_zone", "STO_overbought", "STO_Oversold"]
            for col in stoch_related_cols:
                if col not in df_features.columns:
                    df_features[col] = 0 # Default เป็น 0 (ปรับตามความเหมาะสม)

        # --- Bollinger Bands ---
        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('Close')
        window_bb = 20
        rolling_mean_bb = df_features["Close"].rolling(window=window_bb, min_periods=1).mean()
        rolling_std_bb = df_features["Close"].rolling(window=window_bb, min_periods=1).std()

        # แก้ไข: shift ผลลัพธ์สุดท้ายของการคำนวณ Upper/Lower BB
        df_features["Upper_BB"] = (rolling_mean_bb + (rolling_std_bb * 2)).shift(1)
        df_features["Lower_BB"] = (rolling_mean_bb - (rolling_std_bb * 2)).shift(1)

        # BB_width ใช้ Upper_BB และ Lower_BB ที่ shift แล้ว
        if 'Upper_BB' in df_features.columns and 'Lower_BB' in df_features.columns:
            df_features["BB_width"] = df_features["Upper_BB"] - df_features["Lower_BB"]
        else:
            print(f"Warning: BB_width cannot be calculated for {symbol} ({timeframe}) due to missing Upper/Lower BB columns.")
            df_features["BB_width"] = np.nan # หรือ 0 แล้วแต่การจัดการ NaN ของคุณ

        # --- ADX Calculation ---
        # ใช้ ta.adx().shift(1)
        # ตรวจสอบว่า df_features มีคอลัมน์ 'High', 'Low', 'Close' (ตัวพิมพ์ใหญ่) อยู่แล้ว
        adx = ta.adx(high=df_features["High"], low=df_features["Low"], close=df_features["Close"]).shift(1)
        df_features = pd.concat([df_features, adx], axis=1)

        adx_col = 'ADX_14'
        if adx_col not in df_features.columns: print(f"Warning: ADX column '{adx_col}' not found after calculation.")
        dmp_col = 'DMP_14'
        if dmp_col not in df_features.columns: print(f"Warning: DMP column '{dmp_col}' not found after calculation.")
        dmn_col = 'DMN_14'
        if dmn_col not in df_features.columns: print(f"Warning: DMN column '{dmn_col}' not found after calculation.")

        # --- Features ที่ใช้ ADX ---
        if adx_col in df_features.columns:
            df_features["ADX_zone"] = df_features[adx_col] # ใช้คอลัมน์ ADX_14 ที่ shift แล้ว
        else:
            print(f"Warning: ADX_zone cannot be calculated for {symbol} ({timeframe}) due to missing ADX column.")
            df_features["ADX_zone"] = np.nan # หรือ 0 แล้วแต่การจัดการ NaN ของคุณ

        df_features["ADX_cross"] = 0.0
        if dmp_col in df_features.columns and dmn_col in df_features.columns:
            df_features.loc[df_features[dmp_col] > df_features[dmn_col], "ADX_cross"] = 1
            df_features.loc[df_features[dmn_col] > df_features[dmp_col], "ADX_cross"] = -1
        elif dmp_col in df_features.columns or dmn_col in df_features.columns:
            print(f"Warning: ADX_cross cannot be calculated for {symbol} ({timeframe}) due to missing DMP or DMN column.")
            # สร้างคอลัมน์ ADX_cross ด้วยค่า Default ถ้าไม่มี
            if "ADX_cross" not in df_features.columns:
                df_features["ADX_cross"] = 0 # Default เป็น 0 (ปรับตามความเหมาะสม)

        # --- ATR Calculation ---
        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('High', 'Low', 'Close') และ .shift()
        window_atr = 14
        # ตรวจสอบว่าคอลัมน์หลักมีอยู่ก่อนคำนวณ True Range
        if all(col in df_features.columns for col in ['High', 'Low', 'Close']):
            tr1 = df_features['High'] - df_features['Low']
            # tr2, tr3 ใช้ Close.shift() ต้องมีข้อมูลอย่างน้อย 2 แท่ง
            if len(df_features) > 1:
                tr2 = (df_features['High'] - df_features['Close'].shift()).abs()
                tr3 = (df_features['Low'] - df_features['Close'].shift()).abs()
                # ใช้ pd.concat เพื่อรวม tr1, tr2, tr3 แล้วหา max
                true_range_df = pd.concat([tr1, tr2, tr3], axis=1)
                # max(axis=1) อาจสร้าง NaN ถ้าบางคอลัมน์เป็น NaN ซึ่งต้องการ
                true_range = true_range_df.max(axis=1)
            else:
                print(f"Warning: Not enough data to calculate True Range using shift() for {symbol} ({timeframe}).")
                true_range = pd.Series(np.nan, index=df_features.index) # สร้าง Series เป็น NaN

            # คำนวณ Rolling Mean ของ True Range แล้ว shift(1)
            # ใช้ min_periods=1 เหมือน Indicator อื่นๆ ถ้าต้องการ
            atr = true_range.rolling(window_atr, min_periods=1).mean().shift(1)
            df_features['ATR'] = atr
        else:
            print(f"Warning: Cannot calculate ATR for {symbol} ({timeframe}) due to missing price columns.")
            df_features['ATR'] = np.nan # หรือ 0 แล้วแต่การจัดการ NaN ของคุณ

        # --- SR Calculation ---
        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('Low', 'High') และ .rolling().min/max().shift(1)
        lookback = 50
        if all(col in df_features.columns for col in ['Low', 'High']):
            df_features['Support'] = df_features['Low'].rolling(lookback, min_periods=1).min().shift(1)
            df_features['Resistance'] = df_features['High'].rolling(lookback, min_periods=1).max().shift(1)
        else:
            print(f"Warning: Cannot calculate Support/Resistance for {symbol} ({timeframe}) due to missing price columns.")
            df_features['Support'] = np.nan # หรือ 0 แล้วแต่การจัดการ NaN ของคุณ
            df_features['Resistance'] = np.nan # หรือ 0 แล้วแต่การจัดการ NaN ของคุณ

        # --- กำหนด Lag periods ที่ต้องการ ---
        if timeframe >= 240: # ใช้ค่า Enum ตรงๆ เพื่อความถูกต้อง
            lags = [1, 2, 3, 5, 10]
        else: # รวมถึง H1 และ Timeframe ที่เล็กกว่า
            lags = [1, 2, 3, 5, 10, 15, 20, 30, 50]

        # --- สร้าง Lag Features สำหรับคอลัมน์สำคัญ ---
        price_cols_upper = ['Close', 'Open', 'High', 'Low', 'Volume']
        for col in price_cols_upper:
            if col in df_features.columns: # ตรวจสอบว่าคอลัมน์มีอยู่จริง
                for lag in lags:
                    df_features[f'{col}_Lag_{lag}'] = df_features[col].shift(lag)
            else:
                print(f"Warning: Price column '{col}' not found in df_features for Lag Features.")

        # --- สร้าง Lag Features สำหรับ Indicators ---
        # ใช้ชื่อคอลัมน์ Feature ที่คุณสร้าง ไม่ใช่ชื่อคอลัมน์ Indicator เดิม
        indicator_cols_for_lag = ['RSI14', 'MACD_line_feature', 'ATR', 'BB_width', 'EMA50', 'EMA200',
                                'ADX_14', 'DMP_14', 'DMN_14', # เพิ่ม ADX, DMP, DMN indicators ที่ได้จาก ta
                                'STOCHk_14_3_3', 'STOCHd_14_3_3'] # เพิ่ม Stoch K/D indicators ที่ได้จาก ta

        for indicator in indicator_cols_for_lag:
            if indicator in df_features.columns: # ตรวจสอบว่าคอลัมน์ Indicator มีอยู่จริง
                for lag in [1, 2, 3, 5]: # ใช้ lag periods ที่ใช้กับ indicators
                    df_features[f'{indicator}_Lag_{lag}'] = df_features[indicator].shift(lag)
            else:
                print(f"Warning: Indicator column '{indicator}' not found in df_features for Lag Features.")

        # --- สร้าง Features แบบเปลี่ยนแปลงจาก Lag (Returns, Changes) ---
        for lag in [1, 2, 3, 5]: # ใช้ lag periods ที่ใช้กับ returns/changes
            if 'Close' in df_features.columns:
                df_features[f'Close_Return_{lag}'] = df_features['Close'].pct_change(lag)
            else:
                print(f"Warning: 'Close' column not found for Close_Return_{lag}.")
                df_features[f'Close_Return_{lag}'] = np.nan

            if 'Volume' in df_features.columns:
                df_features[f'Volume_Change_{lag}'] = df_features['Volume'].diff(lag) / (df_features['Volume'].shift(lag) + 1e-10)
            else:
                print(f"Warning: 'Volume' column not found for Volume_Change_{lag}.")
                df_features[f'Volume_Change_{lag}'] = np.nan

        # --- สร้าง Rolling Features จาก Lag (หมายถึง Rolling Average/Std ของ Price/Volume) ---
        for window in [3, 5, 10, 20]: # ใช้ window sizes ที่ใช้กับ rolling features
            if 'Close' in df_features.columns:
                df_features[f'Close_MA_{window}'] = df_features['Close'].rolling(window, min_periods=1).mean().shift(1)
                df_features[f'Close_Std_{window}'] = df_features['Close'].rolling(window, min_periods=1).std().shift(1)
            else:
                print(f"Warning: 'Close' column not found for Close Rolling Features (window {window}).")
                df_features[f'Close_MA_{window}'] = np.nan
                df_features[f'Close_Std_{window}'] = np.nan

            if 'Volume' in df_features.columns:
                df_features[f'Volume_MA_{window}'] = df_features['Volume'].rolling(window, min_periods=1).mean().shift(1)
            else:
                print(f"Warning: 'Volume' column not found for Volume_MA_{window}.")
                df_features[f'Volume_MA_{window}'] = np.nan

        # ตรวจสอบข้อมูลหลังสร้าง Features
        print(f"\nข้อมูลหลังสร้าง Features:")
        print(df_features.info())
        print(df_features.head())

        # --- ลบแถวที่มีค่า NaN ที่เกิดจากการสร้าง Indicators และ Features ---
        initial_rows = len(df_features)
        if not df_features.empty:
            df_features = df_features.dropna()
            print(f"✅ สร้าง Indicators และ Features เรียบร้อย (ลบแถวที่ขาดหายไป {initial_rows - len(df_features)} จาก {initial_rows} แถว)")
        else:
            print(f"Warning: df_features is empty before dropna.")
            return

        if df_features.empty:
            print(f"Warning: No data left in df_features after dropna.")
            return

        # --- 2. โหลดโมเดล, Scaler, และ Feature List ---
        # model, scaler, features_list = None, None, None
        model, scaler, features_list = load_model_components(symbol, timeframe)

        if model is None or scaler is None or features_list is None:
            print(f"[{datetime.datetime.now()}] Failed to load model components for {symbol} ({timeframe}). Cannot make prediction.")
            return

        # --- 3. เตรียม Feature Input สำหรับโมเดล ---
        try:
            if df_features.empty:
                print(f"Warning: df_features is empty when attempting to select features.")
                return

            if not isinstance(features_list, (list, tuple)) or not features_list:
                    print(f"Error: Loaded features_list is invalid or empty: {features_list}")
                    return

            missing_cols = [col for col in features_list if col not in df_features.columns]
            if missing_cols:
                print(f"Error: Missing required feature columns in df_features: {missing_cols}")
                print(f"Available columns: {df_features.columns.tolist()}")
                return

            # ดึงข้อมูลแถวสุดท้าย (ล่าสุด) ที่มีค่า Feature ครบถ้วน
            latest_bar_features_df = df_features.tail(1)[features_list]

        except KeyError as e:
            print(f"[{datetime.datetime.now()}] Error selecting features for {symbol} ({timeframe}): Missing feature - {e}")
            print(f"Available columns after indicator calculation: {df_features.columns.tolist()}")
            print(f"Expected features according to features_list: {features_list}")
            traceback.print_exc()
            return
        except Exception as e:
            print(f"[{datetime.datetime.now()}] Error preparing features for {symbol} ({timeframe}): {e}")
            traceback.print_exc()
            return


        # ... (โค้ดส่วน Scale Features - ส่วนนี้เหมือนเดิม) ...
        features_array = latest_bar_features_df.values
        scaled_features_array = scaler.transform(features_array) # ได้ numpy array

        # *** ส่วนที่เพิ่มเพื่อแปลง scaled numpy array กลับเป็น DataFrame พร้อม feature names ***
        # นี่คือการแก้ไข UserWarning: X does not have valid feature names...
        # ตรวจสอบว่าจำนวนคอลัมน์ใน scaled_features_array ตรงกับจำนวน feature names
        if scaled_features_array.shape[1] != len(features_list):
            print(f"Error: Mismatch between scaled features columns ({scaled_features_array.shape[1]}) and features_list length ({len(features_list)}). Cannot create DataFrame for prediction.")
            return # หรือจัดการ Error อื่นๆ

        # สร้าง DataFrame จาก numpy array โดยใช้ features_list เป็นชื่อคอลัมน์
        scaled_features_df = pd.DataFrame(scaled_features_array, columns=features_list, index=latest_bar_features_df.index)
        # *** จบส่วนที่เพิ่ม ***


        # ใช้ scaled_features_df (DataFrame ที่มี Feature Names) ในการทำนาย
        prediction_proba = model.predict_proba(scaled_features_df)[:, 1] # Probability of class 1 (TP Hit)
        probability_tp_hit = prediction_proba[0]


        # --- 5. ตัดสินใจ Signal ---
        signal = "HOLD" # Default

        if probability_tp_hit >= model_confidence_threshold:
            latest_features_dict = latest_bar_features_df.iloc[0].to_dict()

            # *** Logic การตัดสินใจ BUY/SELL ที่นี่ ต้องตรงกับโมเดลของคุณ ***
            # ตัวอย่าง Logic (สมมติว่าถ้า probability_tp_hit สูงกว่าเกณฑ์ และ MACD_signal_feature > 0 ให้ Buy)
            # คุณอาจต้องใช้ features อื่นๆ ในการตัดสินใจทิศทาง
            if 'MACD_signal_feature' in latest_features_dict:
                if latest_features_dict['MACD_signal_feature'] > 0:
                    signal = "BUY"
                elif latest_features_dict['MACD_signal_feature'] < 0:
                    signal = "SELL"
                # else: signal = "HOLD" ถ้า MACD_signal_feature เป็น 0

            # หากไม่มี MACD_signal_feature หรือต้องการ Logic อื่น
            elif 'Price_above_EMA50' in latest_features_dict: # ตัวอย่าง Logic อื่น
                if latest_features_dict['Price_above_EMA50'] == 1:
                    signal = "BUY"
                else:
                    signal = "SELL" # หรือ "HOLD"

            print(f"[{datetime.datetime.now()}] Model confidence ({probability_tp_hit:.4f}) >= {model_confidence_threshold:.2f}. Generated Signal: {signal}")
        # else: signal = "HOLD"

        # --- เพิ่ม: Print Signal และ Confidence Probability ที่คำนวณได้ ---
        print(f"[{datetime.datetime.now()}] Prediction Result for {symbol} ({timeframe}):")
        print(f"  Signal: {signal}")
        print(f"  Confidence (Probability TP Hit): {probability_tp_hit:.4f}")
        print("------------------------------------------------------------")
        # --- จบส่วน Print ---

        # --- ส่วนส่ง Signal หรือข้อมูลกลับไปที่ MT5 (ต้องเพิ่ม Logic จริงๆ ที่นี่) ---
        # ... (ไม่ได้เพิ่ม Logic ส่งกลับในโค้ดนี้) ...

        # --- ส่วนที่เพิ่ม: Store the calculated signal and confidence ---
        # Store the signal and confidence in the global dictionary
        with signals_lock: # <--- บรรทัดนี้เริ่มต้นส่วนที่บันทึก Signal โดยใช้ Lock
            signals_key = (symbol, timeframe) # ใช้ symbol และ timeframe เป็น key
            latest_signals_data[signals_key] = { # <--- บรรทัดนี้บันทึกข้อมูลลง Global Variable
                "symbol": symbol,
                "timeframe": timeframe, # Store enum
                "timeframe_str": timeframe_map.get(timeframe, 'UNKNOWN'), # Store string for easy reading
                "signal": signal,
                "confidence": float(probability_tp_hit), # Ensure it's a standard float
                "timestamp": latest_bar_dt if 'latest_bar_dt' in locals() else datetime.datetime.now(MT5_TIMEZONE) # Use bar time if available, otherwise current time
            }
        print(f"[{datetime.datetime.now()}] Stored latest signal for {symbol} (enum: {timeframe}): {signal} ({probability_tp_hit:.4f})") # <--- บรรทัดนี้ Print ยืนยันการบันทึก

    # ... (โค้ดส่วนจัดการ Error ใน process_data_and_trade - ควรมี Logic การบันทึก Error Signal ด้วย) ...
    except Exception as e:
        # ... (Print Error) ...
        signal = "ERROR"
        probability_tp_hit = 0.0
        # Store ERROR signal in case of failure
        with signals_lock:
             signals_key = (symbol, timeframe)
             latest_signals_data[signals_key] = {
                "symbol": symbol,
                "timeframe": timeframe,
                "timeframe_str": timeframe_map.get(timeframe, 'UNKNOWN'),
                "signal": signal,
                "confidence": float(probability_tp_hit),
                "timestamp": latest_bar_dt if 'latest_bar_dt' in locals() else datetime.datetime.now(MT5_TIMEZONE)
             }
        print(f"[{datetime.datetime.now()}] Stored latest signal for {symbol} (enum: {timeframe}) with ERROR status.")

    print(f"[{datetime.datetime.now()}] Finished processing for {symbol} ({timeframe}).")

# --- HTTP Flask Route to receive data ---
@app.route('/data', methods=['POST'])
def receive_data():
    """Receives JSON data from MT5 EA via HTTP POST."""

    try:
        data = request.get_json(force=True, silent=False)

        symbol = data.get('symbol')
        timeframe_str = data.get('timeframe_str')
        bar_time_ts = data.get('time')
        bar_open = data.get('open')
        bar_high = data.get('high')
        bar_low = data.get('low')
        bar_close = data.get('close')
        bar_volume = data.get('tick_volume')

        # --- Print ข้อมูลที่ได้รับเพื่อตรวจสอบ (ปรับปรุงให้สั้นลง) ---
        try:
            received_bar_dt_print = datetime.datetime.fromtimestamp(bar_time_ts, tz=pytz.utc).astimezone(MT5_TIMEZONE) # Assume timestamp is UTC
            print(f"[{datetime.datetime.now()}] Received bar for {symbol} {timeframe_str} at {received_bar_dt_print} (Close: {bar_close:.5f})")
        except Exception:
            print(f"[{datetime.datetime.now()}] Received bar for {symbol} {timeframe_str} with invalid timestamp {bar_time_ts} (Close: {bar_close:.5f})")
        # --- จบส่วน Print ข้อมูลที่ได้รับ ---

        if not all([symbol, timeframe_str, bar_time_ts, bar_open, bar_high, bar_low, bar_close]):
            print("Received incomplete data after parsing")
            print(f"Received data: {data}")
            return jsonify({"status": "ERROR", "message": "Incomplete data received after parsing"}), 400

        timeframe_int = timeframe_code_map.get(timeframe_str) # timeframe str PERIOD_H1 enum 60
        if timeframe_int is None:
            print(f"Invalid or unknown timeframe string received: {timeframe_str}")
            return jsonify({"status": "ERROR", "message": f"Invalid or unknown timeframe {timeframe_str}"}), 400

        try:
            bar_dt = datetime.datetime.fromtimestamp(bar_time_ts, tz=pytz.utc).astimezone(MT5_TIMEZONE) # Assume timestamp is UTC
        except Exception as e:
            print(f"Error converting timestamp {bar_time_ts}: {e}")
            return jsonify({"status": "ERROR", "message": f"Invalid timestamp {bar_time_ts}"}), 400

        new_data_row = {
            'time': bar_dt,
            'open': bar_open,
            'high': bar_high,
            'low': bar_low,
            'close': bar_close,
            'volume': bar_volume if bar_volume is not None else 0
        }
        new_data = pd.DataFrame([new_data_row])
        new_data.set_index('time', inplace=True)

        with data_lock:
            cleaned_symbol = symbol.replace('#', '')
            key = (cleaned_symbol, timeframe_int)
            if key not in market_data_store:
                print(f"[{datetime.datetime.now()}] Initializing data store for {cleaned_symbol} ({timeframe_str})")
                market_data_store[key] = pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume']) # สร้าง DF ด้วยชื่อคอลัมน์ตัวพิมพ์เล็ก
                market_data_store[key].index.name = 'time'

            if new_data.index[0] not in market_data_store[key].index:
                market_data_store[key] = pd.concat([market_data_store[key], new_data])
                # จำกัดขนาดข้อมูล
                required_bars = 202 # *** ต้องตรงกับ required_bars ใน process data and trade ***
                market_data_store[key] = market_data_store[key].tail(max(required_bars + 50, 500)) # เก็บอย่างน้อย 500 หรือตาม required_bars + buffer
                print(f"[{datetime.datetime.now()}] Added new bar for {cleaned_symbol} ({timeframe_str}) at {bar_dt}. Total bars: {len(market_data_store[key])}")

            # else: ถ้าเวลาซ้ำ อาจจะเป็นการอัปเดตแท่งปัจจุบัน
            #      pass

        # เรียกใช้ฟังก์ชันประมวลผลใน Thread แยก.
        # ส่ง original symbol และ timeframe_enum ไปให้ process_data_and_trade
        # process_data_and_trade จะไปอัปเดต latest_signals_data ใน Thread ของมัน
        print(f"ก่อนส่งเข้า process data and trade : symbol {cleaned_symbol} timeframe str {timeframe_str} int {timeframe_int}")
        # *** ตรวจสอบว่าคุณส่ง original symbol และ timeframe_enum ไปตรงนี้ถูกต้อง ***
        processing_thread = threading.Thread(target=process_data_and_trade, args=(cleaned_symbol, timeframe_int))
        processing_thread.start()


        # --- ส่วนที่เพิ่ม Logic เพื่อดึง Signal และ Confidence จาก latest_signals_data และส่งกลับใน Response ---
        response_signal = "HOLD" # Default response
        response_confidence = 0.0
        response_message = "Data received and processing started. Signal processing in progress."
        response_signal_bar_timestamp = None # Timestamp ของแท่งที่เกี่ยวข้องกับ Signal ที่ส่งกลับ

        # ใช้ key เดียวกันในการเข้าถึง signals_data
        signals_key = (cleaned_symbol, timeframe_int)

        # ดึงข้อมูล Signal ล่าสุดออกมา โดยใช้ signals_lock
        with signals_lock: # *** บรรทัดนี้เริ่มต้นส่วนที่ดึง Signal โดยใช้ Lock ***
            latest_sig = latest_signals_data.get(signals_key, None) # *** บรรทัดนี้ดึงข้อมูลจาก Global Variable ***

            if latest_sig: # *** ถ้ามีข้อมูล Signal อยู่ ***
                response_signal = latest_sig["signal"] # *** ดึงค่า Signal ***
                response_confidence = latest_sig["confidence"] # *** ดึงค่า Confidence ***
                # แปลง timestamp เป็น unix timestamp เพื่อส่งกลับ
                if latest_sig["timestamp"]:
                    response_signal_bar_timestamp = latest_sig["timestamp"].timestamp()
                response_message = f"Latest signal: {response_signal} ({response_confidence:.4f}) for bar at {latest_sig['timestamp'].strftime('%Y.%m.%d %H:%M')}" # *** สร้าง Message Response ***
            # else: ถ้ายังไม่มี Signal ใน latest_signals_data ก็จะใช้ค่า Default ที่กำหนดไว้ก่อนหน้า

        # Return the JSON response including the latest known signal and confidence
        # MT5 EA จะต้อง Parse JSON response body นี้
        return jsonify({ # *** บรรทัดนี้คือส่วนที่สร้าง JSON Response ***
            "status": "OK",
            "message": response_message,
            "signal": response_signal, # *** ส่งค่า Signal กลับไป ***
            "confidence": response_confidence, # *** ส่งค่า Confidence กลับไป ***
            "bar_timestamp": bar_time_ts, # Timestamp ของแท่งที่ MT5 ส่งมา
            "signal_bar_timestamp": response_signal_bar_timestamp # Timestamp ของแท่งที่ Signal ที่ส่งกลับใช้คำนวณ
        }), 200 # *** จบ Response ***
        # --- จบส่วนที่เพิ่ม Logic ดึง Signal ---

    except BadRequest as e:
        print(f"[{datetime.datetime.now()}] Received data but failed due to Bad Request: {e}")
        print(f"Raw request data (bytes): {request.data}")
        return jsonify({"status": "ERROR", "message": f"Bad Request: {e}"}), 400

    except Exception as e:
        print(f"[{datetime.datetime.now()}] Error processing request: {e}")
        traceback.print_exc()
        return jsonify({"status": "ERROR", "message": f"Server Error: {e}"}), 500

# --- Optional: Endpoint to Get Signal ---
# ... (ไม่ได้เพิ่มในโค้ดนี้) ...

# --- Main Execution ---
if __name__ == "__main__":
    # initialize_mt5() # Uncomment ถ้า Python ต้องใช้ mt5.py ในการส่งคำสั่งเทรดเอง

    print(f"Starting HTTP Server on http://{HTTP_HOST}:{HTTP_PORT}")
    try:
        app.run(host=HTTP_HOST, port=HTTP_PORT, debug=False)
    except Exception as e:
        print(f"Failed to start Flask server: {e}")
        traceback.print_exc()

    print("Server stopped.")
    # mt5.shutdown() # Uncomment ถ้ามีการ initialize_mt5
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบระบบ Hyperparameter Tuning ที่ปรับปรุงแล้ว
ใช้ get_lgbm_params() เป็นฐานสำหรับสร้าง parameter distributions
"""

import os
import sys
import pandas as pd
import numpy as np

# เพิ่ม path สำหรับ import
sys.path.append('.')

def create_test_data():
    """สร้างข้อมูลทดสอบที่หลากหลาย"""
    np.random.seed(42)
    n_samples = 1000
    
    # สร้างข้อมูล features
    data = {
        'Date': pd.date_range('2023-01-01', periods=n_samples, freq='30min').strftime('%Y.%m.%d'),
        'Time': pd.date_range('2023-01-01', periods=n_samples, freq='30min').strftime('%H:%M'),
        'Open': np.random.uniform(1.0500, 1.1500, n_samples),
        'High': np.random.uniform(1.0500, 1.1500, n_samples),
        'Low': np.random.uniform(1.0500, 1.1500, n_samples),
        'Close': np.random.uniform(1.0500, 1.1500, n_samples),
        'Volume': np.random.randint(100, 1000, n_samples),
    }
    
    # สร้าง features เพิ่มเติม
    for i in range(20):
        data[f'Feature_{i}'] = np.random.uniform(-1, 1, n_samples)
    
    df = pd.DataFrame(data)
    
    # สร้าง target ที่มี class imbalance
    df['target'] = np.random.choice([0, 1], n_samples, p=[0.7, 0.3])
    
    return df

def test_scenario_param_distributions():
    """ทดสอบฟังก์ชัน get_scenario_param_distributions"""
    
    print("🧪 ทดสอบ get_scenario_param_distributions")
    print("="*60)
    
    try:
        # Import ฟังก์ชันที่ต้องการ
        from python_LightGBM_16_Signal import get_scenario_param_distributions, USE_MULTI_MODEL_ARCHITECTURE
        
        print(f"📊 USE_MULTI_MODEL_ARCHITECTURE = {USE_MULTI_MODEL_ARCHITECTURE}")
        
        if not USE_MULTI_MODEL_ARCHITECTURE:
            print("❌ กรุณาเปิด USE_MULTI_MODEL_ARCHITECTURE = True")
            return
        
        # สร้างข้อมูลทดสอบ
        print("📊 สร้างข้อมูลทดสอบ...")
        df = create_test_data()
        y = df['target']
        
        print(f"✅ ข้อมูลทดสอบ: {len(df)} samples")
        print(f"📊 Class distribution: {y.value_counts().to_dict()}")
        
        # ทดสอบกับ symbols และ timeframes ต่างๆ
        test_cases = [
            ('EURUSD', 30),
            ('EURUSD', 60),
            ('GOLD', 30),
            ('GOLD', 60),
        ]
        
        scenarios = ['trend_following', 'counter_trend']
        
        for symbol, timeframe in test_cases:
            print(f"\n🎯 ทดสอบ {symbol} {timeframe}min:")
            print("-" * 40)
            
            for scenario in scenarios:
                print(f"\n🤖 Scenario: {scenario}")
                
                try:
                    param_dist, base_params = get_scenario_param_distributions(
                        scenario_name=scenario,
                        symbol=symbol,
                        timeframe=timeframe,
                        y=y
                    )
                    
                    print(f"✅ สร้าง parameter distributions สำเร็จ")
                    print(f"📊 Base parameters keys: {list(base_params.keys())}")
                    print(f"📊 Parameter distributions keys: {list(param_dist.keys())}")
                    
                    # แสดงตัวอย่าง parameter ranges
                    print(f"📈 ตัวอย่าง ranges:")
                    for key in ['learning_rate', 'num_leaves', 'max_depth']:
                        if key in param_dist:
                            dist = param_dist[key]
                            if hasattr(dist, 'args'):
                                if len(dist.args) >= 2:
                                    low, high = dist.args[0], dist.args[0] + dist.args[1]
                                    print(f"   {key}: {low:.4f} - {high:.4f}")
                                else:
                                    print(f"   {key}: {dist.args}")
                            else:
                                print(f"   {key}: {type(dist)}")
                    
                except Exception as e:
                    print(f"❌ เกิดข้อผิดพลาดใน {scenario}: {e}")
                    import traceback
                    traceback.print_exc()
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def test_scenario_model_training():
    """ทดสอบการเทรนโมเดลด้วยระบบใหม่"""
    
    print(f"\n🧪 ทดสอบ Scenario Model Training")
    print("="*60)
    
    try:
        # Import ฟังก์ชันที่ต้องการ
        from python_LightGBM_16_Signal import train_scenario_model, USE_MULTI_MODEL_ARCHITECTURE
        
        if not USE_MULTI_MODEL_ARCHITECTURE:
            print("❌ กรุณาเปิด USE_MULTI_MODEL_ARCHITECTURE = True")
            return
        
        # สร้างข้อมูลทดสอบ
        print("📊 สร้างข้อมูลทดสอบ...")
        df = create_test_data()
        
        # แยกข้อมูล
        features = [col for col in df.columns if col.startswith('Feature_') or col in ['Open', 'High', 'Low', 'Close']]
        X = df[features]
        y = df['target']
        
        print(f"✅ ข้อมูลทดสอบ: {len(X)} samples, {len(features)} features")
        
        # ทดสอบทั้ง 2 scenarios
        scenarios = ['trend_following', 'counter_trend']
        
        for scenario in scenarios:
            print(f"\n🤖 ทดสอบ scenario: {scenario}")
            
            try:
                result = train_scenario_model(
                    X=X,
                    y=y,
                    scenario_name=scenario,
                    symbol='EURUSD',
                    timeframe=30
                )
                
                if result:
                    print(f"✅ {scenario} training สำเร็จ")
                    
                    # ตรวจสอบไฟล์ที่ถูกสร้าง
                    hyper_dir = "LightGBM_Hyper_Multi/030_EURUSD"
                    if os.path.exists(hyper_dir):
                        files = [f for f in os.listdir(hyper_dir) if scenario in f]
                        print(f"   📁 Hyperparameter files: {len(files)} files")
                        for file in files:
                            print(f"      ✅ {file}")
                    
                    model_dir = f"LightGBM_Multi/models/{scenario}"
                    if os.path.exists(model_dir):
                        model_files = [f for f in os.listdir(model_dir) if f.startswith('030_EURUSD')]
                        print(f"   📁 Model files: {len(model_files)} files")
                        for file in model_files:
                            print(f"      ✅ {file}")
                else:
                    print(f"❌ {scenario} training ล้มเหลว")
                    
            except Exception as e:
                print(f"❌ เกิดข้อผิดพลาดใน {scenario}: {e}")
                import traceback
                traceback.print_exc()
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def test_parameter_stability():
    """ทดสอบ parameter stability analysis"""
    
    print(f"\n🔬 ทดสอบ Parameter Stability Analysis...")
    
    try:
        # รัน check_parameter_stability.py
        import subprocess
        result = subprocess.run([
            sys.executable, "check_parameter_stability.py"
        ], capture_output=True, text=True, cwd=".", encoding='utf-8', errors='ignore')
        
        print("📊 ผลลัพธ์ Parameter Stability Analysis:")
        print(result.stdout)
        
        if result.stderr:
            print("⚠️ Warnings/Errors:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ ไม่สามารถรัน parameter stability analysis: {e}")

if __name__ == "__main__":
    print("🏗️ Improved Hyperparameter Tuning Test")
    print("="*50)
    
    # ทดสอบ parameter distributions
    test_scenario_param_distributions()
    
    # ทดสอบ scenario model training
    test_scenario_model_training()
    
    # ทดสอบ parameter stability analysis
    test_parameter_stability()
    
    print(f"\n✅ การทดสอบเสร็จสิ้น")

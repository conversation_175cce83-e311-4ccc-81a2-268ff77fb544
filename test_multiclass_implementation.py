#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Multi-class Implementation
ทดสอบการทำงานของ Multi-class Classification

Created: 2025-07-04
"""

import pandas as pd
import numpy as np
import sys
import os

# เพิ่ม path สำหรับ import main file
sys.path.append('.')

# Import functions from main file
from python_LightGBM_15_Tuning import (
    load_and_process_data,
    create_multiclass_target,
    process_trade_targets,
    get_lgbm_params,
    evaluate_multiclass_model,
    USE_MULTICLASS_TARGET,
    PROFIT_THRESHOLDS,
    CLASS_MAPPING
)

def test_multiclass_target_creation():
    """ทดสอบการสร้าง multi-class target"""
    print("="*60)
    print("🧪 ทดสอบการสร้าง Multi-class Target")
    print("="*60)
    
    # สร้างข้อมูลทดสอบ
    test_profits = np.array([200, 100, 75, 25, -25, -75, -100, -200])
    
    print(f"📊 Profit Thresholds:")
    for key, value in PROFIT_THRESHOLDS.items():
        print(f"  {key}: {value}")
    
    print(f"\n📊 Class Mapping:")
    for key, value in CLASS_MAPPING.items():
        print(f"  Class {key}: {value}")
    
    print(f"\n🔍 Test Profits: {test_profits}")
    
    # สร้าง multi-class target
    targets = create_multiclass_target(pd.Series(test_profits))
    
    print(f"\n📊 Results:")
    for i, (profit, target) in enumerate(zip(test_profits, targets)):
        class_name = CLASS_MAPPING.get(target, f"Unknown_{target}")
        print(f"  Profit: {profit:6.1f} → Class {target} ({class_name})")
    
    return targets

def test_data_loading_and_processing():
    """ทดสอบการโหลดและประมวลผลข้อมูล"""
    print("\n" + "="*60)
    print("🧪 ทดสอบการโหลดและประมวลผลข้อมูล")
    print("="*60)
    
    # ทดสอบกับ symbol หนึ่ง
    test_symbol = "GBPUSD"
    test_timeframe = "M30"
    
    print(f"📂 กำลังโหลดข้อมูล {test_symbol} {test_timeframe}...")
    
    try:
        # โหลดข้อมูล
        df = load_and_process_data(test_symbol, test_timeframe)
        
        if df is not None and len(df) > 0:
            print(f"✅ โหลดข้อมูลสำเร็จ: {len(df)} rows")
            
            # ประมวลผล targets
            print(f"\n🎯 กำลังประมวลผล targets...")
            processed_df = process_trade_targets(df)
            
            if processed_df is not None:
                print(f"✅ ประมวลผล targets สำเร็จ")
                
                # ตรวจสอบ columns ที่สร้างขึ้น
                target_columns = [col for col in processed_df.columns if 'Target' in col]
                print(f"\n📊 Target Columns ที่สร้างขึ้น: {target_columns}")
                
                # แสดงการกระจายของ targets
                for col in target_columns:
                    if col in processed_df.columns:
                        print(f"\n📊 {col} Distribution:")
                        print(processed_df[col].value_counts().sort_index())
                
                return processed_df
            else:
                print("❌ ไม่สามารถประมวลผล targets ได้")
                return None
        else:
            print("❌ ไม่สามารถโหลดข้อมูลได้")
            return None
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_lgbm_params():
    """ทดสอบการสร้าง LightGBM parameters สำหรับ multi-class"""
    print("\n" + "="*60)
    print("🧪 ทดสอบ LightGBM Parameters")
    print("="*60)
    
    # สร้างข้อมูล y ทดสอบ
    y_binary = np.array([0, 1, 0, 1, 1, 0])
    y_multiclass = np.array([0, 1, 2, 3, 4, 2, 1, 0])
    
    print("📊 Binary Classification Parameters:")
    binary_params = get_lgbm_params(y=y_binary)
    for key, value in binary_params.items():
        print(f"  {key}: {value}")
    
    print("\n📊 Multi-class Classification Parameters:")
    multiclass_params = get_lgbm_params(y=y_multiclass)
    for key, value in multiclass_params.items():
        print(f"  {key}: {value}")
    
    return binary_params, multiclass_params

def test_multiclass_evaluation():
    """ทดสอบการประเมินผล multi-class"""
    print("\n" + "="*60)
    print("🧪 ทดสอบการประเมินผล Multi-class")
    print("="*60)
    
    # สร้างข้อมูลทดสอบ
    np.random.seed(42)
    n_samples = 100
    n_classes = 5
    
    # สร้าง true labels
    y_true = np.random.randint(0, n_classes, n_samples)
    
    # สร้าง predicted labels (มี noise)
    y_pred = y_true.copy()
    noise_indices = np.random.choice(n_samples, size=int(0.2 * n_samples), replace=False)
    y_pred[noise_indices] = np.random.randint(0, n_classes, len(noise_indices))
    
    # สร้าง predicted probabilities
    y_pred_proba = np.random.dirichlet(np.ones(n_classes), n_samples)
    
    print(f"📊 Test Data:")
    print(f"  Samples: {n_samples}")
    print(f"  Classes: {n_classes}")
    print(f"  True labels distribution: {np.bincount(y_true)}")
    print(f"  Predicted labels distribution: {np.bincount(y_pred)}")
    
    # ประเมินผล
    metrics = evaluate_multiclass_model(y_true, y_pred, y_pred_proba)
    
    print(f"\n📊 Evaluation Results:")
    for key, value in metrics.items():
        if key != 'confusion_matrix':
            print(f"  {key}: {value}")
    
    print(f"\n📊 Confusion Matrix:")
    print(metrics['confusion_matrix'])
    
    return metrics

def main():
    """ฟังก์ชันหลักสำหรับการทดสอบ"""
    print("🚀 เริ่มการทดสอบ Multi-class Implementation")
    print(f"📊 USE_MULTICLASS_TARGET: {USE_MULTICLASS_TARGET}")
    
    # 1. ทดสอบการสร้าง multi-class target
    targets = test_multiclass_target_creation()
    
    # 2. ทดสอบการโหลดและประมวลผลข้อมูล
    processed_df = test_data_loading_and_processing()
    
    # 3. ทดสอบ LightGBM parameters
    binary_params, multiclass_params = test_lgbm_params()
    
    # 4. ทดสอบการประเมินผล multi-class
    metrics = test_multiclass_evaluation()
    
    print("\n" + "="*60)
    print("✅ การทดสอบเสร็จสมบูรณ์!")
    print("="*60)
    
    # สรุปผลการทดสอบ
    print(f"\n📊 สรุปผลการทดสอบ:")
    print(f"  ✅ Multi-class target creation: {'PASS' if targets is not None else 'FAIL'}")
    print(f"  ✅ Data loading and processing: {'PASS' if processed_df is not None else 'FAIL'}")
    print(f"  ✅ LightGBM parameters: {'PASS' if binary_params and multiclass_params else 'FAIL'}")
    print(f"  ✅ Multi-class evaluation: {'PASS' if metrics else 'FAIL'}")

if __name__ == "__main__":
    main()

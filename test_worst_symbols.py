#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบ 2 symbols ที่มีปัญหาสุด
"""

import sys
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

def test_nzdusd_m30():
    """ทดสอบ NZDUSD M30 - ปัญหาร้ายแรงสุด (CV_AUC = 0.5)"""
    print("🔥 ทดสอบ NZDUSD M30 - ปัญหาร้ายแรงสุด")
    print("=" * 60)
    
    try:
        # Import functions
        from python_LightGBM_15_Tuning import main
        
        print("📊 ผลลัพธ์เดิม:")
        print("   AUC: 0.862, F1: 0.621, CV_AUC: 0.500 ❌")
        print("   ปัญหา: CV_AUC = 0.5 (ไม่สามารถทำนายได้)")
        
        print(f"\n🎯 เป้าหมายการปรับปรุง:")
        print("   CV_AUC: 0.500 → 0.750+ (เพิ่ม 50%)")
        print("   F1: 0.621 → 0.650+ (เพิ่ม 5%)")
        print("   AUC: คงที่หรือดีขึ้น")
        
        print(f"\n🔍 เริ่มการทดสอบ...")
        print("⏱️ คาดว่าจะใช้เวลา 10-15 นาที (1 round)")
        
        # รันการทดสอบ
        print("\n" + "="*60)
        print("🚀 เริ่มรัน NZDUSD M30...")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {str(e)}")
        return False

def test_usdjpy_m30():
    """ทดสอบ USDJPY M30 - F1 Score ต่ำสุด (0.41)"""
    print("\n🔥 ทดสอบ USDJPY M30 - F1 Score ต่ำสุด")
    print("=" * 60)
    
    try:
        print("📊 ผลลัพธ์เดิม:")
        print("   AUC: 0.847, F1: 0.410, CV_AUC: 0.850")
        print("   ปัญหา: F1 Score ต่ำสุดในระบบ")
        
        print(f"\n🎯 เป้าหมายการปรับปรุง:")
        print("   F1: 0.410 → 0.600+ (เพิ่ม 46%)")
        print("   AUC: 0.847 → 0.860+ (เพิ่ม 1.5%)")
        print("   CV_AUC: คงที่หรือดีขึ้น")
        
        print(f"\n💡 การปรับปรุงที่คาดว่าจะช่วย:")
        print("   • Class weight ใหม่ (เพิ่มน้ำหนัก minority class)")
        print("   • Learning rate ที่สูงขึ้น (0.05 → 0.08)")
        print("   • Min_data_in_leaf ที่ลดลง (25 → 20)")
        print("   • Optimal threshold finding")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {str(e)}")
        return False

def analyze_expected_improvements():
    """วิเคราะห์การปรับปรุงที่คาดหวัง"""
    print("\n📊 วิเคราะห์การปรับปรุงที่คาดหวัง")
    print("=" * 60)
    
    improvements = [
        {
            "change": "Class Weight Enhancement",
            "old": "Auto-detect แต่อาจไม่เพียงพอ",
            "new": "เพิ่มน้ำหนัก minority class มากขึ้น",
            "impact": "F1 Score +10-20%"
        },
        {
            "change": "Learning Rate Increase", 
            "old": "0.05 (Conservative)",
            "new": "0.08 (F1-Focused)",
            "impact": "AUC +1-3%, F1 +5-10%"
        },
        {
            "change": "Model Complexity Balance",
            "old": "num_leaves=8, max_depth=4",
            "new": "num_leaves=10, max_depth=5", 
            "impact": "F1 +5-15%"
        },
        {
            "change": "Data Requirements",
            "old": "min_data_in_leaf=25",
            "new": "min_data_in_leaf=20",
            "impact": "F1 +3-8%"
        }
    ]
    
    print("🔧 การปรับปรุงหลัก:")
    for i, imp in enumerate(improvements, 1):
        print(f"{i}. {imp['change']}")
        print(f"   เดิม: {imp['old']}")
        print(f"   ใหม่: {imp['new']}")
        print(f"   ผลกระทบ: {imp['impact']}")
        print()

def create_success_criteria():
    """กำหนดเกณฑ์ความสำเร็จ"""
    print("🎯 เกณฑ์ความสำเร็จ")
    print("=" * 60)
    
    criteria = {
        "NZDUSD M30": {
            "critical": "CV_AUC > 0.75 (จาก 0.50)",
            "good": "F1 > 0.65 (จาก 0.62)",
            "excellent": "AUC > 0.87 (จาก 0.86)"
        },
        "USDJPY M30": {
            "critical": "F1 > 0.55 (จาก 0.41)",
            "good": "F1 > 0.60 (เป้าหมายหลัก)",
            "excellent": "F1 > 0.65 + AUC > 0.86"
        }
    }
    
    for symbol, crit in criteria.items():
        print(f"📊 {symbol}:")
        print(f"   🔥 Critical: {crit['critical']}")
        print(f"   ✅ Good: {crit['good']}")
        print(f"   🎉 Excellent: {crit['excellent']}")
        print()
    
    print("💡 การตัดสินใจ:")
    print("   • ถ้า 2/2 symbols ผ่าน Critical → รัน full training")
    print("   • ถ้า 1/2 symbols ผ่าน Critical → ปรับเพิ่มเติม")
    print("   • ถ้า 0/2 symbols ผ่าน Critical → ทบทวนแนวทาง")

def generate_test_commands():
    """สร้างคำสั่งทดสอบ"""
    print("\n💻 คำสั่งทดสอบ")
    print("=" * 60)
    
    print("🚀 วิธีการทดสอบ:")
    print("1. รัน python_LightGBM_15_Tuning.py (NUM_TRAINING_ROUNDS = 1)")
    print("2. ดูผลลัพธ์ NZDUSD M30 และ USDJPY M30 เป็นพิเศษ")
    print("3. เปรียบเทียบกับเกณฑ์ความสำเร็จ")
    
    print(f"\n📋 ลำดับการดู Output:")
    print("1. หา 'NZDUSD M30' ในผลลัพธ์")
    print("2. ดู CV_AUC, AUC, F1 Score")
    print("3. หา 'USDJPY M30' ในผลลัพธ์") 
    print("4. ดู F1 Score เป็นหลัก")
    print("5. เปรียบเทียบกับค่าเดิม")
    
    print(f"\n⏱️ Timeline คาดการณ์:")
    print("   • เริ่มต้น: 0-2 นาที (setup)")
    print("   • AUDUSD M30: 3-8 นาที")
    print("   • EURGBP M30: 9-14 นาที")
    print("   • EURUSD M30: 15-20 นาที")
    print("   • GBPUSD M30: 21-26 นาที")
    print("   • GOLD M30: 27-32 นาที")
    print("   • NZDUSD M30: 33-38 นาที ⭐ (ดูตรงนี้)")
    print("   • USDCAD M30: 39-44 นาที")
    print("   • USDJPY M30: 45-50 นาที ⭐ (ดูตรงนี้)")
    print("   • H1 timeframes: 51-80 นาที")
    print("   • สรุปผล: 81-85 นาที")

def main():
    """ฟังก์ชันหลัก"""
    print("🧪 ทดสอบ 2 Symbols ที่มีปัญหาสุด")
    print("=" * 80)
    
    print("🎯 เป้าหมาย:")
    print("   • ทดสอบการปรับปรุง F1 Score กับ symbols ที่มีปัญหาสุด")
    print("   • ใช้เวลาเพียง 1 round (80-90 นาที)")
    print("   • ตัดสินใจว่าควรรัน full training หรือไม่")
    
    # 1. ทดสอบ NZDUSD M30
    nzdusd_ready = test_nzdusd_m30()
    
    # 2. ทดสอบ USDJPY M30  
    usdjpy_ready = test_usdjpy_m30()
    
    # 3. วิเคราะห์การปรับปรุง
    analyze_expected_improvements()
    
    # 4. กำหนดเกณฑ์ความสำเร็จ
    create_success_criteria()
    
    # 5. สร้างคำสั่งทดสอบ
    generate_test_commands()
    
    # 6. สรุป
    print("\n🎉 สรุปการเตรียมการทดสอบ")
    print("=" * 80)
    
    if nzdusd_ready and usdjpy_ready:
        print("✅ พร้อมทดสอบ 2 symbols ที่มีปัญหาสุด")
        print("🚀 คำสั่งถัดไป: python python_LightGBM_15_Tuning.py")
        print("⏱️ เวลาคาดการณ์: 80-90 นาที")
        print("🎯 Focus: NZDUSD M30 (นาทีที่ 33-38) และ USDJPY M30 (นาทีที่ 45-50)")
    else:
        print("❌ ยังไม่พร้อมทดสอบ")
    
    print(f"\n📊 เกณฑ์ความสำเร็จสำคัญ:")
    print("   • NZDUSD M30: CV_AUC > 0.75 (จาก 0.50)")
    print("   • USDJPY M30: F1 > 0.55 (จาก 0.41)")
    print("   • ถ้าผ่าน 2/2 → รัน full training")

if __name__ == "__main__":
    main()

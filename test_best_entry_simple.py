#!/usr/bin/env python3
"""
ทดสอบระบบ best_entry.pkl แบบง่าย
"""

import os
import pickle
from datetime import datetime

def create_test_best_entry_files():
    """
    สร้างไฟล์ best_entry.pkl ทดสอบ
    """
    print("🧪 สร้างไฟล์ best_entry.pkl ทดสอบ")
    print("=" * 50)
    
    # สร้างโฟลเดอร์ทดสอบ
    test_folders = [
        "Test_LightGBM/results/M30",
        "Test_LightGBM/results/M60"
    ]
    
    for folder in test_folders:
        os.makedirs(folder, exist_ok=True)
        print(f"✅ สร้างโฟลเดอร์: {folder}")
    
    # สร้างไฟล์ทดสอบ
    test_data = [
        {
            "folder": "Test_LightGBM/results/M30",
            "filename": "030_EURUSD_best_entry.pkl",
            "data": {
                "entry_name": "entry_v2",
                "expectancy": 15.25,
                "win_rate": 0.65,
                "num_trades": 45,
                "timestamp": datetime.now().isoformat(),
                "symbol": "EURUSD",
                "timeframe": 30
            }
        },
        {
            "folder": "Test_LightGBM/results/M60",
            "filename": "060_USDJPY_best_entry.pkl",
            "data": {
                "entry_name": "entry_v3",
                "expectancy": 22.15,
                "win_rate": 0.72,
                "num_trades": 38,
                "timestamp": datetime.now().isoformat(),
                "symbol": "USDJPY",
                "timeframe": 60
            }
        },
        {
            "folder": "Test_LightGBM/results/M30",
            "filename": "030_GBPUSD_best_entry.pkl",
            "data": {
                "entry_name": "entry_v1",
                "expectancy": 8.75,
                "win_rate": 0.58,
                "num_trades": 52,
                "timestamp": datetime.now().isoformat(),
                "symbol": "GBPUSD",
                "timeframe": 30
            }
        }
    ]
    
    for item in test_data:
        file_path = os.path.join(item["folder"], item["filename"])
        try:
            with open(file_path, 'wb') as f:
                pickle.dump(item["data"], f)
            print(f"✅ สร้างไฟล์: {file_path}")
            print(f"   Entry: {item['data']['entry_name']}, Expectancy: {item['data']['expectancy']}")
        except Exception as e:
            print(f"❌ ไม่สามารถสร้างไฟล์ {file_path}: {e}")

def test_load_best_entry_standalone():
    """
    ทดสอบการโหลด best_entry.pkl แบบ standalone
    """
    print("\n🧪 ทดสอบการโหลด best_entry.pkl")
    print("=" * 50)
    
    def load_best_entry_condition_test(symbol, timeframe):
        """
        ฟังก์ชันทดสอบการโหลด best_entry
        """
        try:
            # ลองหาในโฟลเดอร์ตาม timeframe ก่อน
            timeframe_folder = f"M{timeframe}" if timeframe in [30, 60] else str(timeframe)
            best_entry_path = f"Test_LightGBM/results/{timeframe_folder}/{str(timeframe).zfill(3)}_{symbol}_best_entry.pkl"
            
            if not os.path.exists(best_entry_path):
                # ถ้าไม่พบ ลองหาในโฟลเดอร์หลัก
                best_entry_path = f"Test_LightGBM/results/{str(timeframe).zfill(3)}_{symbol}_best_entry.pkl"
            
            if os.path.exists(best_entry_path):
                with open(best_entry_path, 'rb') as f:
                    best_entry_info = pickle.load(f)
                
                entry_name = best_entry_info.get("entry_name", "entry_v1")
                expectancy = best_entry_info.get("expectancy", 0)
                timestamp = best_entry_info.get("timestamp", "unknown")
                
                print(f"✅ โหลด best_entry จาก: {best_entry_path}")
                print(f"   Entry: {entry_name}, Expectancy: {expectancy:.2f}, Updated: {timestamp}")
                
                return entry_name
            else:
                print(f"⚠️ ไม่พบไฟล์ best_entry: {best_entry_path}")
                return "entry_v1"  # fallback
                
        except Exception as e:
            print(f"⚠️ เกิดข้อผิดพลาดขณะโหลด best_entry: {e}")
            return "entry_v1"  # fallback
    
    # ทดสอบกับ symbol และ timeframe ต่างๆ
    test_cases = [
        ("EURUSD", 30),  # ควรได้ entry_v2
        ("USDJPY", 60),  # ควรได้ entry_v3
        ("GBPUSD", 30),  # ควรได้ entry_v1
        ("AUDUSD", 60)   # ควรได้ entry_v1 (fallback)
    ]
    
    for symbol, timeframe in test_cases:
        print(f"\n📋 ทดสอบ {symbol} {timeframe}:")
        best_entry = load_best_entry_condition_test(symbol, timeframe)
        print(f"   ผลลัพธ์: {best_entry}")

def verify_training_model_changes():
    """
    ตรวจสอบการเปลี่ยนแปลงใน training model
    """
    print("\n🧪 ตรวจสอบการเปลี่ยนแปลงใน Training Model")
    print("=" * 50)
    
    try:
        with open("python_LightGBM_15_Tuning.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # ตรวจสอบว่ามีการเพิ่มโค้ดบันทึก best_entry.pkl หรือไม่
        if "best_entry_info = {" in content:
            print("✅ พบการเพิ่มโค้ดบันทึก best_entry.pkl")
        else:
            print("❌ ไม่พบการเพิ่มโค้ดบันทึก best_entry.pkl")
        
        if "with open(best_entry_path, 'wb')" in content:
            print("✅ พบการบันทึกไฟล์ best_entry.pkl")
        else:
            print("❌ ไม่พบการบันทึกไฟล์ best_entry.pkl")
            
        if "timestamp" in content and "datetime.datetime.now().isoformat()" in content:
            print("✅ พบการบันทึก timestamp")
        else:
            print("❌ ไม่พบการบันทึก timestamp")
            
    except Exception as e:
        print(f"❌ ไม่สามารถตรวจสอบไฟล์ได้: {e}")

def verify_webserver_changes():
    """
    ตรวจสอบการเปลี่ยนแปลงใน WebRequest server
    """
    print("\n🧪 ตรวจสอบการเปลี่ยนแปลงใน WebRequest Server")
    print("=" * 50)
    
    try:
        with open("python_to_mt5_WebRequest_server_11_Tuning.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # ตรวจสอบว่ามีฟังก์ชันใหม่หรือไม่
        if "def load_best_entry_condition(" in content:
            print("✅ พบฟังก์ชัน load_best_entry_condition")
        else:
            print("❌ ไม่พบฟังก์ชัน load_best_entry_condition")
        
        if "def get_entry_conditions_functions(" in content:
            print("✅ พบฟังก์ชัน get_entry_conditions_functions")
        else:
            print("❌ ไม่พบฟังก์ชัน get_entry_conditions_functions")
        
        if "entry_v1_conditions" in content and "entry_v2_conditions" in content and "entry_v3_conditions" in content:
            print("✅ พบ entry conditions ทั้งหมด (v1, v2, v3)")
        else:
            print("❌ ไม่พบ entry conditions ครบถ้วน")
            
        if "load_best_entry_condition(symbol, timeframe)" in content:
            print("✅ พบการเรียกใช้ load_best_entry_condition")
        else:
            print("❌ ไม่พบการเรียกใช้ load_best_entry_condition")
            
    except Exception as e:
        print(f"❌ ไม่สามารถตรวจสอบไฟล์ได้: {e}")

def show_next_steps():
    """
    แสดงขั้นตอนต่อไป
    """
    print("\n💡 ขั้นตอนต่อไปในการใช้งาน")
    print("=" * 50)
    
    print("1. 🎯 รัน Training Model เพื่อสร้าง best_entry.pkl จริง:")
    print("   python python_LightGBM_15_Tuning.py")
    print("   - ระบบจะทดสอบ entry conditions ต่างๆ")
    print("   - เลือก entry condition ที่ดีที่สุด")
    print("   - บันทึกลงไฟล์ best_entry.pkl")
    
    print("\n2. 🌐 รัน WebRequest Server:")
    print("   python python_to_mt5_WebRequest_server_11_Tuning.py")
    print("   - ระบบจะโหลด best_entry.pkl อัตโนมัติ")
    print("   - ใช้ entry condition ที่ดีที่สุดจากการทดสอบ")
    print("   - Fallback เป็น entry_v1 ถ้าไม่พบไฟล์")
    
    print("\n3. 📊 ตรวจสอบผลลัพธ์:")
    print("   - ดูไฟล์ entry_summary.txt สำหรับผลการทดสอบ")
    print("   - ตรวจสอบไฟล์ best_entry.pkl ที่สร้างขึ้น")
    print("   - สังเกตการใช้ entry condition ใน WebRequest server")
    
    print("\n4. 🔄 การอัปเดตแบบ Real-time:")
    print("   - ทุกครั้งที่รัน training จะอัปเดต best_entry.pkl")
    print("   - WebRequest server จะใช้ entry condition ล่าสุด")
    print("   - ระบบจะแสดงข้อความ '🎯 ใช้ entry condition: xxx'")

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🔧 ทดสอบระบบ best_entry.pkl แบบง่าย")
    print("=" * 80)
    
    # 1. สร้างไฟล์ทดสอบ
    create_test_best_entry_files()
    
    # 2. ทดสอบการโหลด
    test_load_best_entry_standalone()
    
    # 3. ตรวจสอบการเปลี่ยนแปลงในไฟล์
    verify_training_model_changes()
    verify_webserver_changes()
    
    # 4. แสดงขั้นตอนต่อไป
    show_next_steps()
    
    print(f"\n{'='*80}")
    print("🎯 สรุปการปรับปรุง:")
    print("✅ เพิ่มการบันทึก best_entry.pkl ใน Training Model")
    print("✅ เพิ่มการโหลด best_entry.pkl ใน WebRequest Server")
    print("✅ สร้างระบบ Dynamic Entry Conditions")
    print("✅ ระบบมี Fallback เป็น entry_v1")
    print("✅ ระบบพร้อมใช้งานแล้ว!")

if __name__ == "__main__":
    main()

# 📊 การวิเคราะห์การตั้งค่าคงที่ระหว่างไฟล์

## 🔍 การตั้งค่าที่พบในแต่ละไฟล์

### 📁 python_LightGBM_16_Signal.py (ไฟล์หลัก)
```python
# การตั้งค่าพื้นฐาน
Plot_file = False
Steps_to_calculating = False
Steps_to_do = True

# เงื่อนไขการเทรด
input_initial_threshold = 0.50
input_rsi_level_in = 35
input_rsi_level_out = 30
input_stop_loss_atr = 1.5
input_take_profit = 2.5
input_pull_back = 0.40

# High-Quality Entry Filters
MIN_ATR_THRESHOLD = 0.0008
MIN_VOLUME_MULTIPLIER = 1.2
TREND_CONFIRMATION_PERIODS = 3

# Minimum Learning Requirements
MIN_TRAINING_SAMPLES = 200
MIN_POSITIVE_SAMPLES = 20
MIN_MODEL_ACCURACY = 0.60
MIN_MODEL_AUC = 0.70
MIN_WIN_RATE_TARGET = 0.40

# การเทรน
NUM_TRAINING_ROUNDS = 1
Save_File = True
do_hyperparameter_tuning = True

# Architecture Configuration
USE_MULTI_MODEL_ARCHITECTURE = True
USE_MULTICLASS_TARGET = True

# Profit Thresholds
PROFIT_THRESHOLDS = {
    'strong_buy': 60,
    'weak_buy': 20,
    'weak_sell': 20,
    'strong_sell': 60
}

# Symbol Info
symbol_info = {
    "GOLD": {"Spread": 25, "Digits": 2, "Points": 0.01},
    "AUDUSD": {"Spread": 15, "Digits": 5, "Points": 0.00001},
    # ... อื่นๆ
}

# Timeframe Mapping
timeframe_map = {"M1": 1, "M5": 5, "M15": 15, "M30": 30, "H1": 60, "H2": 120, "H4": 240, "D1": 1440}

# Folders
test_model = "LightGBM_Model"
test_folder = "LightGBM" / "LightGBM_Multi"
output_folder = f"{test_folder}/results"
```

### 📁 python_to_mt5_WebRequest_server_12_Signal.py (ไฟล์ใช้งาน)
```python
# Telegram
Telegram_Open = True
TOKEN = '**********************************************'
CHAT_ID = 6546140292

# เงื่อนไขการเทรด (ค่าต่างจากไฟล์หลัก!)
input_rsi_level_in = 35        # ✅ ตรงกัน
input_rsi_level_out = 30       # ✅ ตรงกัน
input_stop_loss_atr = 2.0      # ❌ ต่าง! (หลัก: 1.5)
input_take_profit = 1.0        # ❌ ต่าง! (หลัก: 2.5)
input_pull_back = 0.20         # ❌ ต่าง! (หลัก: 0.40)

# Server Configuration
HTTP_PORT = 54321
HTTP_HOST = '127.0.0.1'

# Timeframe Mapping (รูปแบบต่าง!)
timeframe_map = {
    "PERIOD_M1": mt5.TIMEFRAME_M1,
    "PERIOD_M2": mt5.TIMEFRAME_M2,
    # ...
}

timeframe_code_map = {
    "PERIOD_M1": 1, "PERIOD_M30": 30, "PERIOD_H1": 60
}

# Symbol Info (ซ้ำกับไฟล์หลัก!)
symbol_info_map = {
    "GOLD": {"Spread": 25, "Digits": 2, "Points": 0.01},
    "AUDUSD": {"Spread": 15, "Digits": 5, "Points": 0.00001},
    # ... เหมือนกับไฟล์หลัก
}

# Global Storage
market_data_store = {}
latest_signals_data = {}
loaded_models = {}
loaded_scenario_models = {}
```

## ⚠️ ปัญหาที่พบ

### 🔴 ค่าไม่ตรงกัน (Critical Issues)
1. **input_stop_loss_atr**: หลัก=1.5, Server=2.0
2. **input_take_profit**: หลัก=2.5, Server=1.0
3. **input_pull_back**: หลัก=0.40, Server=0.20

### 🟡 การซ้ำซ้อน (Redundancy Issues)
1. **symbol_info** ซ้ำกันทั้งสองไฟล์
2. **timeframe mapping** มีรูปแบบต่างกัน
3. **RSI levels** ซ้ำกัน

### 🟢 ค่าที่ Server ต้องการเฉพาะ
1. **Telegram settings** (TOKEN, CHAT_ID)
2. **HTTP server settings** (PORT, HOST)
3. **MT5 specific mappings**
4. **Threading locks และ storage**

## 💡 แนวทางแก้ไข

### 🎯 วิธีที่ 1: Central Config File
สร้างไฟล์ `trading_config.py` เป็นศูนย์กลาง

### 🎯 วิธีที่ 2: Import from Main File
ให้ Server import ค่าจากไฟล์หลัก

### 🎯 วิธีที่ 3: JSON Configuration
ใช้ไฟล์ JSON เก็บการตั้งค่า

## 🚀 คำแนะนำ (Recommended Solution)

**ใช้วิธีที่ 2: Import from Main File** เพราะ:
- ✅ ไฟล์หลักเป็น source of truth
- ✅ ไม่ต้องสร้างไฟล์เพิ่ม
- ✅ การเปลี่ยนแปลงทำที่เดียว
- ✅ ลดความซ้ำซ้อน

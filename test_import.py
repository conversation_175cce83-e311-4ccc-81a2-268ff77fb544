#!/usr/bin/env python3
# Test import script

try:
    print("Testing imports...")
    
    from python_LightGBM_17_Signal import (
        load_scenario_models, predict_with_scenario_model,
        detect_market_scenario, get_optimal_parameters,
        load_scenario_threshold, load_scenario_nbars, load_time_filters,
        USE_MULTI_MODEL_ARCHITECTURE, MARKET_SCENARIOS
    )
    
    print("✅ Import successful!")
    print(f"USE_MULTI_MODEL_ARCHITECTURE: {USE_MULTI_MODEL_ARCHITECTURE}")
    print(f"MARKET_SCENARIOS: {list(MARKET_SCENARIOS.keys())}")
    
    # Test server startup
    print("\nTesting server startup...")
    from flask import Flask
    app = Flask(__name__)
    
    @app.route('/test')
    def test():
        return {"status": "ok", "multi_model": USE_MULTI_MODEL_ARCHITECTURE}
    
    print("✅ Flask app created successfully!")
    
    # Test model loading
    print("\nTesting model loading...")
    MODEL_BASE_PATH = r'D:\test_gold\LightGBM_Multi\models'
    THRESHOLD_BASE_PATH = r'D:\test_gold\LightGBM_Multi\thresholds'
    
    import os
    print(f"Model path exists: {os.path.exists(MODEL_BASE_PATH)}")
    print(f"Threshold path exists: {os.path.exists(THRESHOLD_BASE_PATH)}")
    
    if os.path.exists(MODEL_BASE_PATH):
        print(f"Model path contents: {os.listdir(MODEL_BASE_PATH)}")
    
    print("✅ All tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการติดตั้ง libraries ทั้งหมดสำหรับ python_LightGBM_15_Tuning.py
"""

def test_imports():
    """ทดสอบ import libraries ทั้งหมด"""
    try:
        # Standard library
        import os, sys, json, datetime, math, traceback, time, pickle
        from collections import Counter
        print("✅ Standard libraries: OK")
        
        # Data science core
        import pandas as pd
        import numpy as np
        print(f"✅ Pandas {pd.__version__}: OK")
        print(f"✅ NumPy {np.__version__}: OK")
        
        # Technical analysis
        import pandas_ta as ta
        import talib
        print("✅ pandas-ta: OK")
        print("✅ TA-Lib: OK")
        
        # Machine learning
        import sklearn
        from sklearn.model_selection import train_test_split, StratifiedKFold, TimeSeriesSplit, RandomizedSearchCV, GridSearchCV, cross_validate
        from sklearn.preprocessing import StandardScaler
        from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_auc_score, f1_score, precision_score, recall_score, roc_curve, precision_recall_curve, average_precision_score, log_loss
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.impute import SimpleImputer
        print(f"✅ scikit-learn {sklearn.__version__}: OK")
        
        import lightgbm as lgb
        print(f"✅ LightGBM {lgb.__version__}: OK")
        
        from imblearn.over_sampling import SMOTE
        print("✅ imbalanced-learn: OK")
        
        import joblib
        print(f"✅ joblib {joblib.__version__}: OK")
        
        # Visualization
        import matplotlib.pyplot as plt
        import seaborn as sns
        import plotly.graph_objects as go
        import plotly.io as pio
        print("✅ Visualization libraries: OK")
        
        # Statistical analysis
        from statsmodels.tsa.stattools import adfuller
        print("✅ statsmodels: OK")
        
        # Windows sound (Windows only)
        try:
            import winsound
            print("✅ winsound: OK")
        except ImportError:
            print("⚠️ winsound: Not available (Linux/Mac)")
        
        print("\n🎉 การติดตั้งสำเร็จทั้งหมด!")
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        return False

def test_basic_functionality():
    """ทดสอบการทำงานพื้นฐาน"""
    try:
        import pandas as pd
        import numpy as np
        import lightgbm as lgb
        
        # สร้างข้อมูลทดสอบ
        data = pd.DataFrame({
            'feature1': np.random.randn(100),
            'feature2': np.random.randn(100),
            'target': np.random.randint(0, 2, 100)
        })
        
        # ทดสอบ LightGBM
        X = data[['feature1', 'feature2']]
        y = data['target']
        
        model = lgb.LGBMClassifier(n_estimators=10, random_state=42, verbose=-1)
        model.fit(X, y)
        predictions = model.predict(X)
        
        print("✅ LightGBM basic functionality: OK")
        print("✅ Data processing: OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Functionality test failed: {e}")
        return False

def test_technical_analysis():
    """ทดสอบ Technical Analysis libraries"""
    try:
        import pandas as pd
        import numpy as np
        import pandas_ta as ta
        import talib
        
        # สร้างข้อมูลราคาจำลอง
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        prices = pd.DataFrame({
            'Open': np.random.randn(100).cumsum() + 100,
            'High': np.random.randn(100).cumsum() + 102,
            'Low': np.random.randn(100).cumsum() + 98,
            'Close': np.random.randn(100).cumsum() + 100,
            'Volume': np.random.randint(1000, 10000, 100)
        }, index=dates)
        
        # ทดสอบ pandas-ta
        prices.ta.rsi(length=14, append=True)
        print("✅ pandas-ta RSI calculation: OK")
        
        # ทดสอบ TA-Lib
        rsi_talib = talib.RSI(prices['Close'].values, timeperiod=14)
        print("✅ TA-Lib RSI calculation: OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Technical Analysis test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 เริ่มทดสอบการติดตั้ง...")
    print("=" * 50)
    
    # ทดสอบ imports
    import_success = test_imports()
    
    if import_success:
        print("\n" + "=" * 50)
        print("🔧 ทดสอบการทำงานพื้นฐาน...")
        functionality_success = test_basic_functionality()
        
        print("\n" + "=" * 50)
        print("📈 ทดสอบ Technical Analysis...")
        ta_success = test_technical_analysis()
        
        if functionality_success and ta_success:
            print("\n🎉 ระบบพร้อมใช้งาน python_LightGBM_15_Tuning.py!")
            print("✅ สามารถรันไฟล์หลักได้แล้ว")
        else:
            print("\n⚠️ มีปัญหาในการทำงานพื้นฐาน")
    else:
        print("\n❌ การติดตั้งไม่สมบูรณ์")
        print("💡 กรุณาตรวจสอบการติดตั้ง libraries ตามคู่มือ")

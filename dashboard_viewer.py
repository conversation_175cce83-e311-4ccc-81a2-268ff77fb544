#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import pandas as pd
from datetime import datetime, timedelta
import glob

class TradingDashboard:
    def __init__(self):
        self.status_file = "system_status.json"
        self.log_pattern = "system_monitor_*.txt"
        self.mt5_log_pattern = "MT5_Trading_Log_*.txt"
    
    def load_system_status(self):
        """โหลดสถานะระบบล่าสุด"""
        try:
            if os.path.exists(self.status_file):
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {"error": "Status file not found"}
        except Exception as e:
            return {"error": f"Error loading status: {str(e)}"}
    
    def analyze_logs(self, hours_back=24):
        """วิเคราะห์ log ย้อนหลัง"""
        try:
            # หาไฟล์ log ทั้งหมด
            log_files = glob.glob(self.log_pattern)
            
            if not log_files:
                return {"error": "No log files found"}
            
            # อ่านและรวม log entries
            all_entries = []
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            
            for log_file in log_files:
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        for line in f:
                            if line.strip():
                                try:
                                    entry = json.loads(line.strip())
                                    entry_time = datetime.fromisoformat(entry['timestamp'])
                                    if entry_time >= cutoff_time:
                                        all_entries.append(entry)
                                except:
                                    continue
                except:
                    continue
            
            # วิเคราะห์ข้อมูล
            analysis = {
                "total_events": len(all_entries),
                "event_types": {},
                "server_checks": 0,
                "server_errors": 0,
                "signals": {"BUY": 0, "SELL": 0, "HOLD": 0, "ERROR": 0},
                "timeline": []
            }
            
            for entry in all_entries:
                event_type = entry.get('type', 'UNKNOWN')
                analysis["event_types"][event_type] = analysis["event_types"].get(event_type, 0) + 1
                
                if event_type == "SERVER_CHECK":
                    analysis["server_checks"] += 1
                    signal = entry.get('details', {}).get('signal', 'UNKNOWN')
                    if signal in analysis["signals"]:
                        analysis["signals"][signal] += 1
                elif event_type == "SERVER_ERROR":
                    analysis["server_errors"] += 1
                
                # เก็บ timeline สำหรับ 10 events ล่าสุด
                if len(analysis["timeline"]) < 10:
                    analysis["timeline"].append({
                        "time": entry['timestamp'],
                        "type": event_type,
                        "message": entry['message']
                    })
            
            # เรียงลำดับ timeline
            analysis["timeline"].sort(key=lambda x: x['time'], reverse=True)
            
            return analysis
            
        except Exception as e:
            return {"error": f"Error analyzing logs: {str(e)}"}
    
    def analyze_mt5_logs(self):
        """วิเคราะห์ MT5 logs"""
        try:
            mt5_files = glob.glob(self.mt5_log_pattern)
            
            if not mt5_files:
                return {"error": "No MT5 log files found"}
            
            # อ่านไฟล์ล่าสุด
            latest_file = max(mt5_files, key=os.path.getctime)
            
            analysis = {
                "file": latest_file,
                "total_lines": 0,
                "trades": {"BUY": 0, "SELL": 0},
                "server_communications": 0,
                "errors": 0,
                "recent_activities": []
            }
            
            with open(latest_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            analysis["total_lines"] = len(lines)
            
            # วิเคราะห์ 50 บรรทัดล่าสุด
            recent_lines = lines[-50:] if len(lines) > 50 else lines
            
            for line in recent_lines:
                if 'TRADE_BUY' in line:
                    analysis["trades"]["BUY"] += 1
                elif 'TRADE_SELL' in line:
                    analysis["trades"]["SELL"] += 1
                elif 'SERVER_' in line:
                    analysis["server_communications"] += 1
                elif 'ERROR' in line:
                    analysis["errors"] += 1
                
                # เก็บกิจกรรมล่าสุด
                if any(keyword in line for keyword in ['TRADE_', 'SERVER_', 'ERROR']):
                    if len(analysis["recent_activities"]) < 5:
                        analysis["recent_activities"].append(line.strip())
            
            return analysis
            
        except Exception as e:
            return {"error": f"Error analyzing MT5 logs: {str(e)}"}
    
    def generate_report(self):
        """สร้างรายงานสรุป"""
        print("🔍 Trading System Dashboard")
        print("=" * 60)
        
        # 1. สถานะระบบ
        status = self.load_system_status()
        if "error" not in status:
            stats = status.get("statistics", {})
            health = status.get("health_check", {})
            
            print(f"\n📊 System Status (Last Updated: {status.get('timestamp', 'Unknown')})")
            print("-" * 40)
            print(f"🟢 Status: {status.get('system_status', 'UNKNOWN')}")
            print(f"⏱️  Uptime: {stats.get('uptime_hours', 0)} hours")
            print(f"🖥️  Server Health: {health.get('server_health', 'UNKNOWN')}")
            print(f"📡 Total Requests: {stats.get('server_requests', 0)}")
            print(f"❌ Total Errors: {stats.get('server_errors', 0)}")
            print(f"📈 Error Rate: {health.get('error_rate', 0)}%")
            
            signals = stats.get('signals_generated', {})
            print(f"\n🎯 Signal Distribution:")
            for signal, count in signals.items():
                print(f"   {signal}: {count}")
        else:
            print(f"\n❌ System Status Error: {status['error']}")
        
        # 2. Log Analysis
        print(f"\n📝 Log Analysis (Last 24 Hours)")
        print("-" * 40)
        log_analysis = self.analyze_logs(24)
        
        if "error" not in log_analysis:
            print(f"📊 Total Events: {log_analysis['total_events']}")
            print(f"✅ Server Checks: {log_analysis['server_checks']}")
            print(f"❌ Server Errors: {log_analysis['server_errors']}")
            
            print(f"\n🎯 Signal Summary:")
            for signal, count in log_analysis['signals'].items():
                print(f"   {signal}: {count}")
            
            print(f"\n⏰ Recent Events:")
            for event in log_analysis['timeline'][:5]:
                print(f"   [{event['time']}] {event['type']}: {event['message']}")
        else:
            print(f"❌ Log Analysis Error: {log_analysis['error']}")
        
        # 3. MT5 Analysis
        print(f"\n🔧 MT5 Analysis")
        print("-" * 40)
        mt5_analysis = self.analyze_mt5_logs()
        
        if "error" not in mt5_analysis:
            print(f"📁 Log File: {mt5_analysis['file']}")
            print(f"📊 Total Lines: {mt5_analysis['total_lines']}")
            print(f"💰 Trades: BUY={mt5_analysis['trades']['BUY']}, SELL={mt5_analysis['trades']['SELL']}")
            print(f"📡 Server Communications: {mt5_analysis['server_communications']}")
            print(f"❌ Errors: {mt5_analysis['errors']}")
            
            if mt5_analysis['recent_activities']:
                print(f"\n⏰ Recent MT5 Activities:")
                for activity in mt5_analysis['recent_activities']:
                    print(f"   {activity}")
        else:
            print(f"❌ MT5 Analysis Error: {mt5_analysis['error']}")
        
        print("\n" + "=" * 60)
        print(f"📅 Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def save_daily_summary(self):
        """บันทึกสรุปรายวัน"""
        today = datetime.now().strftime('%Y%m%d')
        summary_file = f"daily_summary_{today}.json"
        
        summary = {
            "date": today,
            "timestamp": datetime.now().isoformat(),
            "system_status": self.load_system_status(),
            "log_analysis": self.analyze_logs(24),
            "mt5_analysis": self.analyze_mt5_logs()
        }
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"💾 Daily summary saved to: {summary_file}")

def main():
    dashboard = TradingDashboard()
    
    print("🚀 Trading System Dashboard")
    print("Choose an option:")
    print("1. View current status")
    print("2. Generate daily summary")
    print("3. Both")
    
    choice = input("\nEnter choice (1-3): ").strip()
    
    if choice in ['1', '3']:
        dashboard.generate_report()
    
    if choice in ['2', '3']:
        print("\n" + "=" * 60)
        dashboard.save_daily_summary()

if __name__ == "__main__":
    main()

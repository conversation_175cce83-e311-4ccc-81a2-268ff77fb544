#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขปัญหา CSV column mismatch
"""

import os
import sys
import pandas as pd
import numpy as np

# เพิ่ม path สำหรับ import
sys.path.append('.')

def check_csv_files():
    """ตรวจสอบไฟล์ CSV ทั้งหมด"""
    
    print("🔍 ตรวจสอบไฟล์ CSV ทั้งหมด")
    print("="*60)
    
    try:
        from python_LightGBM_16_Signal import test_groups
        
        all_files = []
        for group_name, files in test_groups.items():
            all_files.extend(files)
        
        print(f"📊 พบไฟล์ทั้งหมด: {len(all_files)} ไฟล์")
        
        for i, file in enumerate(all_files, 1):
            print(f"\n📁 ไฟล์ {i}/{len(all_files)}: {file}")
            
            if not os.path.exists(file):
                print(f"   ❌ ไฟล์ไม่พบ")
                continue
            
            # ตรวจสอบขนาดไฟล์
            file_size = os.path.getsize(file)
            print(f"   📊 ขนาดไฟล์: {file_size:,} bytes")
            
            # ลองอ่านด้วย separators ต่างๆ
            separators = [',', '\t', ';', '|']
            
            for sep_name, sep in [('comma', ','), ('tab', '\t'), ('semicolon', ';'), ('pipe', '|')]:
                try:
                    df = pd.read_csv(file, sep=sep, header=None, nrows=5)
                    print(f"   📊 {sep_name}: {df.shape[1]} คอลัมน์, {df.shape[0]} แถว (ตัวอย่าง)")
                    
                    if df.shape[1] > 1:
                        print(f"      ✅ ตัวอย่างข้อมูล:")
                        print(f"         {list(df.iloc[0].values)}")
                        
                except Exception as e:
                    print(f"   ❌ {sep_name}: {e}")
            
            # ตรวจสอบเนื้อหาไฟล์โดยตรง
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    first_lines = [f.readline().strip() for _ in range(3)]
                
                print(f"   📄 เนื้อหา 3 บรรทัดแรก:")
                for j, line in enumerate(first_lines, 1):
                    if line:
                        print(f"      {j}: {line[:100]}...")  # แสดง 100 ตัวอักษรแรก
                        
                        # นับ separators
                        comma_count = line.count(',')
                        tab_count = line.count('\t')
                        semi_count = line.count(';')
                        
                        print(f"         Separators: comma={comma_count}, tab={tab_count}, semicolon={semi_count}")
                        
            except Exception as e:
                print(f"   ❌ ไม่สามารถอ่านเนื้อหาไฟล์: {e}")
                
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def test_load_and_process_data():
    """ทดสอบฟังก์ชัน load_and_process_data ที่แก้ไขแล้ว"""
    
    print(f"\n🧪 ทดสอบฟังก์ชัน load_and_process_data")
    print("="*60)
    
    try:
        from python_LightGBM_16_Signal import load_and_process_data, test_groups
        
        # ทดสอบกับไฟล์แรกในแต่ละกลุ่ม
        for group_name, files in test_groups.items():
            if files:
                test_file = files[0]
                print(f"\n🎯 ทดสอบกลุ่ม {group_name}: {test_file}")
                
                if not os.path.exists(test_file):
                    print(f"   ❌ ไฟล์ไม่พบ: {test_file}")
                    continue
                
                try:
                    # เรียกใช้ฟังก์ชัน load_and_process_data
                    result = load_and_process_data(
                        file=test_file,
                        model_name=f"test_{group_name}",
                        symbol="TEST",
                        timeframe=30,
                        run_identifier=1,
                        model=None,
                        scaler=None,
                        nBars_SL=20,
                        confidence_threshold=0.5
                    )
                    
                    if result and len(result) == 6:
                        train_data, val_data, test_data, df, trade_df, stats = result
                        
                        if df is not None:
                            print(f"   ✅ โหลดข้อมูลสำเร็จ")
                            print(f"      📊 DataFrame shape: {df.shape}")
                            print(f"      📊 คอลัมน์: {list(df.columns)}")
                            print(f"      📊 ข้อมูลตัวอย่าง:")
                            print(f"         {df.head(2).to_string()}")
                        else:
                            print(f"   ❌ DataFrame เป็น None")
                    else:
                        print(f"   ❌ ผลลัพธ์ไม่ถูกต้อง")
                        
                except Exception as e:
                    print(f"   ❌ เกิดข้อผิดพลาด: {e}")
                    import traceback
                    traceback.print_exc()
                    
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def create_sample_csv_files():
    """สร้างไฟล์ CSV ตัวอย่างสำหรับการทดสอบ"""
    
    print(f"\n🏗️ สร้างไฟล์ CSV ตัวอย่าง")
    print("="*50)
    
    # สร้างข้อมูลตัวอย่าง
    dates = pd.date_range('2023-01-01', periods=100, freq='30min')
    
    data = {
        'Date': dates.strftime('%Y.%m.%d'),
        'Time': dates.strftime('%H:%M'),
        'Open': np.random.uniform(1.0500, 1.1500, 100),
        'High': np.random.uniform(1.0500, 1.1500, 100),
        'Low': np.random.uniform(1.0500, 1.1500, 100),
        'Close': np.random.uniform(1.0500, 1.1500, 100),
        'Volume': np.random.randint(100, 1000, 100),
    }
    
    df = pd.DataFrame(data)
    
    # สร้างไฟล์ตัวอย่างด้วย separators ต่างๆ
    test_files = [
        ('test_comma.csv', ','),
        ('test_tab.csv', '\t'),
        ('test_semicolon.csv', ';'),
    ]
    
    for filename, sep in test_files:
        filepath = os.path.join('CSV_Files_Fixed', filename)
        
        # สร้างโฟลเดอร์ถ้าไม่มี
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # บันทึกไฟล์
        df.to_csv(filepath, sep=sep, index=False, header=False)
        print(f"   ✅ สร้างไฟล์: {filepath} (separator: '{sep}')")
        
        # ตรวจสอบไฟล์ที่สร้าง
        try:
            test_df = pd.read_csv(filepath, sep=sep, header=None)
            print(f"      📊 ตรวจสอบ: {test_df.shape[1]} คอลัมน์, {test_df.shape[0]} แถว")
        except Exception as e:
            print(f"      ❌ ไม่สามารถอ่านไฟล์ที่สร้าง: {e}")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🏗️ Test CSV Fix")
    print("="*50)
    
    # ตรวจสอบไฟล์ CSV ที่มีอยู่
    check_csv_files()
    
    # สร้างไฟล์ตัวอย่าง
    create_sample_csv_files()
    
    # ทดสอบฟังก์ชัน load_and_process_data
    test_load_and_process_data()
    
    print(f"\n✅ การทดสอบเสร็จสิ้น")
    print(f"\n📋 สรุปการแก้ไข:")
    print(f"   ✅ เพิ่ม import traceback ใน run_main_analysis()")
    print(f"   ✅ ตรวจสอบจำนวนคอลัมน์ก่อนตั้งชื่อ")
    print(f"   ✅ ลองอ่านไฟล์ด้วย separators ต่างๆ")
    print(f"   ✅ จัดการกรณีที่คอลัมน์ไม่ครบ")
    print(f"   ✅ แสดงข้อมูล debug ที่ละเอียด")

if __name__ == "__main__":
    main()

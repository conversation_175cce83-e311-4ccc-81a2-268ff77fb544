#!/usr/bin/env python3
"""
Test MT5 Status Manager
ทดสอบการทำงานของ MT5 Status Manager

การทดสอบ:
1. ตรวจสอบโครงสร้างไฟล์
2. ตรวจสอบฟังก์ชันสำคัญ
3. ตรวจสอบการตั้งค่า Telegram
4. จำลองการทำงาน
"""

import re
import os
import requests
import json
import datetime

def check_status_manager_file():
    """ตรวจสอบไฟล์ MT5 Status Manager"""
    
    file_path = "MT5_Status_Manager.mq5"
    
    if not os.path.exists(file_path):
        print(f"❌ ไฟล์ {file_path} ไม่พบ")
        return False
    
    print("🔍 ตรวจสอบไฟล์ MT5 Status Manager")
    print("=" * 60)
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='utf-16') as f:
                content = f.read()
        except UnicodeDecodeError:
            with open(file_path, 'r', encoding='latin-1') as f:
                content = f.read()
    
    checks = []
    
    # 1. ตรวจสอบ Input Parameters
    print("\n📋 1. ตรวจสอบ Input Parameters")
    input_patterns = [
        r'input string TelegramBotToken',
        r'input string ChatID',
        r'input int\s+ReportInterval',
        r'input bool\s+EnableNewBarReport',
        r'input bool\s+EnableStatusReport',
        r'input bool\s+EnablePositionChange'
    ]
    
    input_found = 0
    for pattern in input_patterns:
        if re.search(pattern, content):
            input_found += 1
    
    if input_found >= 5:
        print("✅ Input Parameters ครบถ้วน")
        checks.append(True)
    else:
        print(f"❌ Input Parameters ไม่ครบ ({input_found}/6)")
        checks.append(False)
    
    # 2. ตรวจสอบฟังก์ชันหลัก
    print("\n📋 2. ตรวจสอบฟังก์ชันหลัก")
    main_functions = [
        r'void CheckNewBar\(\)',
        r'void CheckPositionChanges\(\)',
        r'void CheckPeriodicReport\(\)',
        r'string GetCurrentStatusReport\(\)',
        r'double GetTotalProfit\(\)',
        r'double GetPotentialLoss\(\)',
        r'void SendTelegram\('
    ]
    
    function_found = 0
    for pattern in main_functions:
        if re.search(pattern, content):
            function_found += 1
    
    if function_found >= 6:
        print("✅ ฟังก์ชันหลักครบถ้วน")
        checks.append(True)
    else:
        print(f"❌ ฟังก์ชันหลักไม่ครบ ({function_found}/7)")
        checks.append(False)
    
    # 3. ตรวจสอบการจัดการ Events
    print("\n📋 3. ตรวจสอบการจัดการ Events")
    event_patterns = [
        r'int OnInit\(\)',
        r'void OnDeinit\(',
        r'void OnTick\(\)'
    ]
    
    event_found = 0
    for pattern in event_patterns:
        if re.search(pattern, content):
            event_found += 1
    
    if event_found >= 3:
        print("✅ Event Handlers ครบถ้วน")
        checks.append(True)
    else:
        print(f"❌ Event Handlers ไม่ครบ ({event_found}/3)")
        checks.append(False)
    
    # 4. ตรวจสอบการคำนวณ
    print("\n📋 4. ตรวจสอบการคำนวณ")
    calculation_patterns = [
        r'PositionsTotal\(\)',
        r'PositionGetDouble\(POSITION_PROFIT\)',
        r'PositionGetDouble\(POSITION_SL\)',
        r'TerminalInfoInteger\(TERMINAL_TRADE_ALLOWED\)'
    ]
    
    calc_found = 0
    for pattern in calculation_patterns:
        if re.search(pattern, content):
            calc_found += 1
    
    if calc_found >= 3:
        print("✅ การคำนวณครบถ้วน")
        checks.append(True)
    else:
        print(f"❌ การคำนวณไม่ครบ ({calc_found}/4)")
        checks.append(False)
    
    # สรุปผลการตรวจสอบ
    print("\n" + "=" * 60)
    print("📊 สรุปผลการตรวจสอบ")
    
    all_ok = all(checks)
    passed_count = sum(checks)
    total_count = len(checks)
    
    if all_ok:
        print(f"🎉 ผ่านการตรวจสอบทั้งหมด ({passed_count}/{total_count})")
    else:
        print(f"⚠️ ผ่านการตรวจสอบ {passed_count}/{total_count} ข้อ")
    
    return all_ok

def test_telegram_connection():
    """ทดสอบการเชื่อมต่อ Telegram"""
    
    print("\n🔗 ทดสอบการเชื่อมต่อ Telegram")
    print("=" * 60)
    
    # ข้อมูลจากไฟล์
    bot_token = "7553787791:AAE-uAa0hyFQNGhN5edoQq5bFBge0LkMPbY"
    chat_id = "6546140292"
    
    print(f"🤖 Bot Token: {bot_token[:20]}...")
    print(f"💬 Chat ID: {chat_id}")
    
    # ทดสอบ getMe API
    try:
        url = f"https://api.telegram.org/bot{bot_token}/getMe"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('ok'):
                bot_info = data.get('result', {})
                print(f"✅ Bot ใช้งานได้: {bot_info.get('first_name', 'Unknown')}")
                print(f"   Username: @{bot_info.get('username', 'Unknown')}")
                return True
            else:
                print("❌ Bot Token ไม่ถูกต้อง")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Connection Error: {e}")
        return False

def simulate_status_reports():
    """จำลองรายงานสถานะต่างๆ"""
    
    print("\n📊 จำลองรายงานสถานะ")
    print("=" * 60)
    
    # จำลองข้อมูลสถานะ
    status_data = {
        "algo_trading": True,
        "total_positions": 3,
        "total_profit": 125.50,
        "potential_loss": -450.00,
        "positions": [
            {"symbol": "EURUSD", "type": "BUY", "volume": 0.10, "profit": 45.20},
            {"symbol": "GBPUSD", "type": "SELL", "volume": 0.05, "profit": 30.15},
            {"symbol": "USDJPY", "type": "BUY", "volume": 0.15, "profit": 50.15}
        ]
    }
    
    # 1. รายงานเริ่มต้น
    print("\n📋 1. รายงานเริ่มต้น:")
    initial_report = generate_initial_report(status_data)
    print(initial_report)
    
    # 2. รายงานแท่งใหม่
    print("\n📋 2. รายงานแท่งใหม่:")
    new_bar_report = generate_new_bar_report(status_data)
    print(new_bar_report)
    
    # 3. รายงานการเปลี่ยนแปลงออเดอร์
    print("\n📋 3. รายงานการเปลี่ยนแปลงออเดอร์:")
    position_change_report = generate_position_change_report(3, 4, status_data)
    print(position_change_report)
    
    # 4. รายงานหยุดทำงาน
    print("\n📋 4. รายงานหยุดทำงาน:")
    deinit_report = generate_deinit_report("ถูกลบออกจากชาร์ต")
    print(deinit_report)

def generate_initial_report(data):
    """สร้างรายงานเริ่มต้น"""
    
    report = "🚀 MT5 Status Manager เริ่มทำงาน\n\n"
    report += generate_status_section(data)
    return report

def generate_new_bar_report(data):
    """สร้างรายงานแท่งใหม่"""
    
    current_time = datetime.datetime.now()
    report = "📊 แท่งใหม่: EURUSD M30\n"
    report += f"⏰ เวลา: {current_time.strftime('%Y.%m.%d %H:%M')}\n\n"
    report += generate_status_section(data)
    return report

def generate_position_change_report(old_count, new_count, data):
    """สร้างรายงานการเปลี่ยนแปลงออเดอร์"""
    
    report = "🔄 การเปลี่ยนแปลงออเดอร์\n"
    report += f"จำนวนเดิม: {old_count} ไม้\n"
    report += f"จำนวนใหม่: {new_count} ไม้\n"
    
    if new_count > old_count:
        report += f"✅ เปิดออเดอร์ใหม่: {new_count - old_count} ไม้\n"
    elif new_count < old_count:
        report += f"❌ ปิดออเดอร์: {old_count - new_count} ไม้\n"
    
    report += "\n" + generate_status_section(data)
    return report

def generate_deinit_report(reason):
    """สร้างรายงานหยุดทำงาน"""
    
    report = "🔴 MT5 Status Manager หยุดทำงาน\n"
    report += f"เหตุผล: {reason}"
    return report

def generate_status_section(data):
    """สร้างส่วนสถานะ"""
    
    report = ""
    
    # สถานะ Algo Trading
    algo_status = "✅ เปิด" if data["algo_trading"] else "❌ ปิด"
    report += f"🤖 Algo Trading: {algo_status}\n"
    
    # จำนวนออเดอร์
    report += f"📊 จำนวนไม้: {data['total_positions']} ไม้\n"
    
    # กำไร/ขาดทุน
    profit = data["total_profit"]
    profit_text = "💰 กำไร: +" if profit >= 0 else "💸 ขาดทุน: "
    report += f"{profit_text}{profit:.2f} USD\n"
    
    # ขาดทุนสูงสุด
    if data["potential_loss"] < 0:
        report += f"⚠️ ขาดทุนสูงสุด: {data['potential_loss']:.2f} USD\n"
    
    # รายละเอียดออเดอร์
    if data["positions"]:
        report += "\n📋 รายละเอียดออเดอร์:\n"
        for pos in data["positions"]:
            profit_sign = "+" if pos["profit"] >= 0 else ""
            report += f"{pos['symbol']} {pos['type']} {pos['volume']:.2f} (P/L: {profit_sign}{pos['profit']:.2f})\n"
    
    return report

def show_usage_guide():
    """แสดงคู่มือการใช้งาน"""
    
    print("\n📖 คู่มือการใช้งาน MT5 Status Manager")
    print("=" * 60)
    
    print("\n🔧 ขั้นตอนการติดตั้ง:")
    print("1. คัดลอกไฟล์ MT5_Status_Manager.mq5 ไปยัง MT5/MQL5/Experts/")
    print("2. เปิด MetaEditor และคอมไพล์ไฟล์ (F7)")
    print("3. ตั้งค่า WebRequest ใน MT5:")
    print("   Tools → Options → Expert Advisors")
    print("   ✅ Allow WebRequest for listed URL")
    print("   เพิ่ม: https://api.telegram.org")
    
    print("\n📱 ขั้นตอนการใช้งาน:")
    print("1. ลาก EA ไปยังชาร์ต")
    print("2. ตั้งค่า Input Parameters:")
    print("   - TelegramBotToken: Token ของ Bot")
    print("   - ChatID: ID ของแชท")
    print("   - ReportInterval: ช่วงเวลารายงาน (วินาที)")
    print("   - Enable Flags: เปิด/ปิดการรายงานแต่ละประเภท")
    print("3. เปิด Auto Trading")
    print("4. กด OK")
    
    print("\n⚙️ การปรับแต่ง:")
    print("- ReportInterval = 300 (5 นาที)")
    print("- ReportInterval = 600 (10 นาที)")
    print("- ReportInterval = 1800 (30 นาที)")
    
    print("\n🔔 ประเภทการรายงาน:")
    print("📊 แท่งใหม่ - เมื่อเกิดแท่งใหม่")
    print("🔄 การเปลี่ยนแปลงออเดอร์ - เมื่อเปิด/ปิดไม้")
    print("⏰ รายงานตามช่วงเวลา - ตามที่กำหนด")
    print("🚀 เริ่มต้น/หยุดทำงาน - เมื่อ EA เริ่ม/หยุด")

def main():
    """ฟังก์ชันหลัก"""
    
    print("📊 MT5 Status Manager Test")
    print("ทดสอบการทำงานของ MT5 Status Manager")
    print("=" * 60)
    
    # ตรวจสอบไฟล์
    file_ok = check_status_manager_file()
    
    # ทดสอบการเชื่อมต่อ Telegram
    telegram_ok = test_telegram_connection()
    
    # จำลองรายงานสถานะ
    simulate_status_reports()
    
    # แสดงคู่มือการใช้งาน
    show_usage_guide()
    
    print("\n" + "=" * 60)
    print("📋 สรุปผลการทดสอบ")
    
    if file_ok and telegram_ok:
        print("🎉 ระบบพร้อมใช้งาน!")
        print("📋 ขั้นตอนต่อไป:")
        print("1. คอมไพล์ไฟล์ MT5")
        print("2. ติดตั้ง EA ในชาร์ต")
        print("3. ตั้งค่า WebRequest")
        print("4. ทดสอบการส่งข้อความ")
    else:
        print("⚠️ พบปัญหาที่ต้องแก้ไข:")
        if not file_ok:
            print("❌ ไฟล์มีปัญหา")
        if not telegram_ok:
            print("❌ การเชื่อมต่อ Telegram มีปัญหา")

if __name__ == "__main__":
    main()

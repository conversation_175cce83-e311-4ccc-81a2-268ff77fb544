#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข Duplicate Parameters และ Path Issues
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def test_duplicate_params_fix():
    """ทดสอบการแก้ไข duplicate parameters และ path issues"""
    
    print("🧪 ทดสอบการแก้ไข Duplicate Parameters และ Path Issues")
    print("="*80)
    
    # ข้อมูลการทดสอบ
    test_info = {
        "symbol": "GOLD",
        "timeframe": "H1",
        "file": "CSV_Files_Fixed/GOLD_H1_FIXED.csv"
    }
    
    print("📋 ขั้นตอนที่ 1: ตรวจสอบไฟล์ข้อมูล")
    file_path = test_info["file"]
    if os.path.exists(file_path):
        file_size = os.path.getsize(file_path)
        print(f"✅ ไฟล์พบ: {file_path}")
        print(f"📊 ขนาดไฟล์: {file_size:,} bytes")
    else:
        print(f"❌ ไม่พบไฟล์: {file_path}")
        return False
    
    print(f"\n📋 การตั้งค่าการทดสอบ:")
    print(f"  Symbol: {test_info['symbol']}")
    print(f"  Timeframe: {test_info['timeframe']}")
    print(f"  File: {test_info['file']}")
    print(f"  File Size: {file_size:,} bytes")
    
    print(f"\n🚀 เริ่มการทดสอบการเทรน...")
    print(f"⏰ เริ่มเมื่อ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # เรียกใช้ script หลัก
    try:
        result = subprocess.run(
            [sys.executable, "python_LightGBM_15_Tuning.py"],
            capture_output=True,
            text=True,
            timeout=600,  # 10 minutes timeout
            encoding='utf-8',
            errors='ignore'
        )
        
        print(f"\n📊 ผลการทดสอบ:")
        print(f"Return Code: {result.returncode}")
        
        # ตรวจสอบ errors ที่สำคัญ
        stderr_lines = result.stderr.split('\n') if result.stderr else []
        important_errors = []
        
        for line in stderr_lines:
            if any(keyword in line.lower() for keyword in [
                'duplicate', 'multiple values', 'random_state', 
                'lightgbm', 'fatal', 'error'
            ]):
                important_errors.append(line.strip())
        
        if important_errors:
            print(f"\n⚠️ Errors:")
            for error in important_errors[:10]:  # แสดงแค่ 10 errors แรก
                print(f"  {error}")
        else:
            print(f"\n✅ ไม่มี Critical Errors!")
        
        # ตรวจสอบ success indicators
        stdout_text = result.stdout.lower() if result.stdout else ""
        success_indicators = [
            "✅ ฝึกโมเดลหลักสำเร็จ",
            "✅ บันทึกโมเดล",
            "การเทรนเสร็จสิ้น"
        ]
        
        found_success = False
        for indicator in success_indicators:
            if indicator.lower() in stdout_text:
                found_success = True
                break
        
        if found_success:
            print(f"✅ พบสัญญาณความสำเร็จในการเทรน!")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print(f"⏰ การทดสอบหมดเวลา (10 นาที)")
        return False
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {str(e)}")
        return False

def check_generated_files():
    """ตรวจสอบไฟล์ที่ถูกสร้างขึ้น"""
    
    print(f"\n📋 ขั้นตอนที่ 2: ตรวจสอบไฟล์ที่สร้างขึ้น")
    
    results_dir = "Test_LightGBM/results"
    if not os.path.exists(results_dir):
        print(f"❌ ไม่พบโฟลเดอร์ results: {results_dir}")
        return False
    
    # หาไฟล์ที่สร้างล่าสุด
    all_files = []
    for root, dirs, files in os.walk(results_dir):
        for file in files:
            if file.endswith('.txt'):
                file_path = os.path.join(root, file)
                mtime = os.path.getmtime(file_path)
                size = os.path.getsize(file_path)
                all_files.append((file, mtime, size))
    
    if not all_files:
        print(f"❌ ไม่พบไฟล์ .txt ใน {results_dir}")
        return False
    
    # เรียงตามเวลาล่าสุด
    all_files.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\n🔍 ตรวจสอบไฟล์ที่ถูกสร้างขึ้น")
    print("="*60)
    print(f"📁 ไฟล์ล่าสุด (5 ไฟล์แรก):")
    
    for i, (filename, mtime, size) in enumerate(all_files[:5]):
        mtime_str = datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
        print(f"  {i+1}. {filename}")
        print(f"     แก้ไขล่าสุด: {mtime_str}")
        print(f"     ขนาด: {size:,} bytes")
    
    return True

def main():
    """ฟังก์ชันหลัก"""
    
    print("🧪 ทดสอบการแก้ไข Duplicate Parameters และ Path Issues")
    print("="*80)
    
    # ทดสอบการเทรน
    training_success = test_duplicate_params_fix()
    
    # ตรวจสอบไฟล์ที่สร้างขึ้น
    files_success = check_generated_files()
    
    print(f"\n🏁 สรุปผลการทดสอบ")
    print("="*80)
    
    if training_success:
        print("✅ การทดสอบสำเร็จ!")
        print("💡 การแก้ไขทำงานได้ถูกต้อง:")
        print("  1. ✅ แก้ไข duplicate random_state parameter")
        print("  2. ✅ แก้ไข file path issues")
        print("  3. ✅ ระบบเทรนทำงานได้")
        
        if files_success:
            print("  4. ✅ ระบบบันทึกไฟล์ทำงานได้")
        
        print(f"\n🚀 พร้อมสำหรับการเทรนแบบเต็มรูปแบบ:")
        print("  - ไม่มี duplicate parameter errors")
        print("  - File paths ทำงานได้ถูกต้อง")
        print("  - ระบบ fallback ทำงานได้")
        print("  - ไฟล์สรุปการเทรดถูกสร้างอัตโนมัติ")
        
    else:
        print("❌ การทดสอบล้มเหลว!")
        print("💡 ยังคงมีปัญหาที่ต้องแก้ไข")
    
    return training_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ตรวจสอบคุณภาพข้อมูลและแก้ไขปัญหา GBPUSD H1 (CV_AUC = 0.5)
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import mutual_info_classif
import warnings
warnings.filterwarnings('ignore')

def check_target_distribution(file_path):
    """ตรวจสอบการกระจายของ target variable"""
    print(f"🔍 ตรวจสอบ Target Distribution: {file_path}")
    print("-"*60)
    
    try:
        # โหลดข้อมูลจากไฟล์ผลลัพธ์ที่บันทึกไว้
        results_dir = "Test_LightGBM/results"
        symbol = file_path.split('#')[0]
        timeframe = "060" if "H1" in file_path else "030"
        
        # ค้นหาไฟล์ trade results
        import os
        import glob
        
        pattern = f"{results_dir}/*{symbol}*{timeframe}*trade_results*.csv"
        trade_files = glob.glob(pattern)
        
        if not trade_files:
            print(f"❌ ไม่พบไฟล์ trade results สำหรับ {symbol} {timeframe}")
            return None
        
        trade_file = trade_files[0]
        df = pd.read_csv(trade_file)
        
        print(f"📊 ข้อมูลพื้นฐาน:")
        print(f"  จำนวนแถว: {len(df):,}")
        print(f"  จำนวนคอลัมน์: {len(df.columns)}")
        
        # ตรวจสอบ Target
        if 'Target' in df.columns:
            target_dist = df['Target'].value_counts()
            target_pct = df['Target'].value_counts(normalize=True) * 100
            
            print(f"\n📈 การกระจายของ Target:")
            for val in sorted(target_dist.index):
                print(f"  Target {val}: {target_dist[val]:,} ({target_pct[val]:.1f}%)")
            
            # ตรวจสอบ class imbalance
            minority_pct = min(target_pct.values)
            if minority_pct < 10:
                print(f"🚨 Severe Class Imbalance: {minority_pct:.1f}%")
            elif minority_pct < 30:
                print(f"⚠️ Moderate Class Imbalance: {minority_pct:.1f}%")
            else:
                print(f"✅ Balanced Classes: {minority_pct:.1f}%")
        
        # ตรวจสอบ Trade Type distribution
        if 'Trade Type' in df.columns:
            trade_type_dist = df['Trade Type'].value_counts()
            print(f"\n💼 การกระจายของ Trade Type:")
            for trade_type, count in trade_type_dist.items():
                pct = count / len(df) * 100
                print(f"  {trade_type}: {count:,} ({pct:.1f}%)")
        
        # ตรวจสอบ Exit Condition
        if 'Exit Condition' in df.columns:
            exit_dist = df['Exit Condition'].value_counts()
            print(f"\n🚪 การกระจายของ Exit Condition:")
            for condition, count in exit_dist.items():
                pct = count / len(df) * 100
                print(f"  {condition}: {count:,} ({pct:.1f}%)")
        
        return df
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return None

def check_feature_correlation(df, threshold=0.95):
    """ตรวจสอบ feature correlation ที่สูงเกินไป"""
    print(f"\n🔗 ตรวจสอบ Feature Correlation (threshold > {threshold})")
    print("-"*60)
    
    # เลือกเฉพาะ numeric columns
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    numeric_cols = [col for col in numeric_cols if col not in ['Target', 'Profit', 'Risk', 'Reward']]
    
    if len(numeric_cols) < 2:
        print("❌ ไม่มี numeric features เพียงพอสำหรับการวิเคราะห์")
        return []
    
    # คำนวณ correlation matrix
    corr_matrix = df[numeric_cols].corr().abs()
    
    # หา highly correlated pairs
    high_corr_pairs = []
    for i in range(len(corr_matrix.columns)):
        for j in range(i+1, len(corr_matrix.columns)):
            if corr_matrix.iloc[i, j] > threshold:
                col1 = corr_matrix.columns[i]
                col2 = corr_matrix.columns[j]
                corr_val = corr_matrix.iloc[i, j]
                high_corr_pairs.append((col1, col2, corr_val))
    
    if high_corr_pairs:
        print(f"⚠️ พบ {len(high_corr_pairs)} คู่ features ที่มี correlation สูง:")
        for col1, col2, corr_val in sorted(high_corr_pairs, key=lambda x: x[2], reverse=True):
            print(f"  {col1} <-> {col2}: {corr_val:.3f}")
    else:
        print(f"✅ ไม่พบ features ที่มี correlation สูงเกิน {threshold}")
    
    return high_corr_pairs

def check_data_leakage(df):
    """ตรวจสอบ data leakage"""
    print(f"\n🚨 ตรวจสอบ Data Leakage")
    print("-"*60)
    
    leakage_suspects = []
    
    # ตรวจสอบ features ที่อาจมี perfect correlation กับ target
    if 'Target' in df.columns:
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        numeric_cols = [col for col in numeric_cols if col != 'Target']
        
        for col in numeric_cols:
            if col in df.columns:
                try:
                    # ตรวจสอบ correlation กับ target
                    corr = df[col].corr(df['Target'])
                    if abs(corr) > 0.9:
                        leakage_suspects.append((col, corr))
                    
                    # ตรวจสอบ perfect separation
                    if df[col].nunique() == 2 and df['Target'].nunique() == 2:
                        crosstab = pd.crosstab(df[col], df['Target'])
                        if (crosstab == 0).any().any():
                            leakage_suspects.append((col, "Perfect Separation"))
                
                except:
                    continue
    
    if leakage_suspects:
        print(f"🚨 พบ {len(leakage_suspects)} features ที่น่าสงสัยว่ามี data leakage:")
        for feature, issue in leakage_suspects:
            print(f"  {feature}: {issue}")
    else:
        print("✅ ไม่พบสัญญาณของ data leakage")
    
    return leakage_suspects

def check_feature_importance(df):
    """ตรวจสอบ feature importance"""
    print(f"\n📊 ตรวจสอบ Feature Importance")
    print("-"*60)
    
    if 'Target' not in df.columns:
        print("❌ ไม่พบ Target column")
        return None
    
    # เลือก numeric features
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    feature_cols = [col for col in numeric_cols if col not in ['Target', 'Profit', 'Risk', 'Reward']]
    
    if len(feature_cols) == 0:
        print("❌ ไม่พบ features สำหรับการวิเคราะห์")
        return None
    
    # เตรียมข้อมูล
    X = df[feature_cols].fillna(0)
    y = df['Target']
    
    # คำนวณ mutual information
    try:
        mi_scores = mutual_info_classif(X, y, random_state=42)
        feature_importance = pd.DataFrame({
            'feature': feature_cols,
            'importance': mi_scores
        }).sort_values('importance', ascending=False)
        
        print("🔝 Top 10 Most Important Features:")
        for i, (_, row) in enumerate(feature_importance.head(10).iterrows(), 1):
            print(f"  {i:2d}. {row['feature']}: {row['importance']:.4f}")
        
        print(f"\n🔻 Bottom 5 Least Important Features:")
        for i, (_, row) in enumerate(feature_importance.tail(5).iterrows(), 1):
            print(f"  {i:2d}. {row['feature']}: {row['importance']:.4f}")
        
        # ตรวจสอบ features ที่ไม่มีประโยชน์
        zero_importance = feature_importance[feature_importance['importance'] == 0]
        if len(zero_importance) > 0:
            print(f"\n⚠️ พบ {len(zero_importance)} features ที่ไม่มี importance:")
            for feature in zero_importance['feature'].head(10):
                print(f"  • {feature}")
        
        return feature_importance
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการคำนวณ feature importance: {e}")
        return None

def generate_recommendations(symbol, timeframe, target_dist, high_corr_pairs, leakage_suspects, feature_importance):
    """สร้างคำแนะนำการแก้ไข"""
    print(f"\n💡 คำแนะนำการแก้ไขสำหรับ {symbol} {timeframe}")
    print("="*80)
    
    recommendations = []
    
    # 1. Class Imbalance
    if target_dist is not None:
        target_pct = target_dist.value_counts(normalize=True) * 100
        minority_pct = min(target_pct.values)
        
        if minority_pct < 10:
            recommendations.append({
                "priority": "🚨 Critical",
                "issue": f"Severe Class Imbalance ({minority_pct:.1f}%)",
                "solution": "ใช้ SMOTE, class_weight='balanced', หรือ threshold optimization"
            })
        elif minority_pct < 30:
            recommendations.append({
                "priority": "⚠️ High",
                "issue": f"Moderate Class Imbalance ({minority_pct:.1f}%)",
                "solution": "ปรับ class_weight หรือ sampling strategy"
            })
    
    # 2. High Correlation
    if len(high_corr_pairs) > 0:
        recommendations.append({
            "priority": "🟡 Medium",
            "issue": f"High Feature Correlation ({len(high_corr_pairs)} pairs)",
            "solution": "ลบ features ที่ redundant หรือใช้ PCA"
        })
    
    # 3. Data Leakage
    if len(leakage_suspects) > 0:
        recommendations.append({
            "priority": "🚨 Critical",
            "issue": f"Potential Data Leakage ({len(leakage_suspects)} features)",
            "solution": "ตรวจสอบและลบ features ที่มี leakage"
        })
    
    # 4. Feature Quality
    if feature_importance is not None:
        zero_importance = len(feature_importance[feature_importance['importance'] == 0])
        if zero_importance > 0:
            recommendations.append({
                "priority": "🟡 Medium",
                "issue": f"Useless Features ({zero_importance} features)",
                "solution": "ลบ features ที่ไม่มี importance"
            })
    
    # แสดงคำแนะนำ
    if recommendations:
        for i, rec in enumerate(recommendations, 1):
            print(f"{i}. {rec['priority']} {rec['issue']}")
            print(f"   💡 Solution: {rec['solution']}")
            print()
    else:
        print("✅ ไม่พบปัญหาที่ต้องแก้ไขเร่งด่วน")
    
    return recommendations

def main():
    """ฟังก์ชันหลัก"""
    print("🔍 ตรวจสอบคุณภาพข้อมูลและแก้ไขปัญหา")
    print("="*80)
    
    # ไฟล์ที่มีปัญหา
    problem_files = [
        "GBPUSD#_H1_201305010000_202504302300.csv",  # CV_AUC = 0.5
        "USDJPY#_H1_201305010000_202504302300.csv",  # AUC ต่ำ
        "AUDUSD#_M30_201905010000_202504302330.csv"  # Overfitting
    ]
    
    for file_path in problem_files:
        print(f"\n{'='*80}")
        print(f"🔍 วิเคราะห์: {file_path}")
        print(f"{'='*80}")
        
        # 1. ตรวจสอบ target distribution
        df = check_target_distribution(file_path)
        
        if df is not None:
            # 2. ตรวจสอบ feature correlation
            high_corr_pairs = check_feature_correlation(df)
            
            # 3. ตรวจสอบ data leakage
            leakage_suspects = check_data_leakage(df)
            
            # 4. ตรวจสอบ feature importance
            feature_importance = check_feature_importance(df)
            
            # 5. สร้างคำแนะนำ
            symbol = file_path.split('#')[0]
            timeframe = "H1" if "H1" in file_path else "M30"
            
            recommendations = generate_recommendations(
                symbol, timeframe, df.get('Target'), 
                high_corr_pairs, leakage_suspects, feature_importance
            )
    
    print(f"\n🚀 ขั้นตอนถัดไป:")
    print("1. แก้ไขปัญหาตามลำดับความสำคัญ")
    print("2. รัน reduce_overfitting.py")
    print("3. รัน improve_f1_score.py")
    print("4. ทดสอบโมเดลใหม่")

if __name__ == "__main__":
    main()

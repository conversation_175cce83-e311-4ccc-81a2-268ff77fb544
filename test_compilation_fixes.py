#!/usr/bin/env python3
"""
Test Compilation Fixes
ทดสอบการแก้ไขข้อผิดพลาดการคอมไพล์

ข้อผิดพลาดที่แก้ไข:
1. 'lots' - arrays are passed by reference only
2. 'SendBuyOrder' - expression of 'void' type is illegal  
3. 'SendSellOrder' - expression of 'void' type is illegal
"""

import re
import os

def check_mt5_file_fixes():
    """ตรวจสอบการแก้ไขในไฟล์ MT5"""
    
    file_path = "mt5_to_python_08_lot.mq5"
    
    if not os.path.exists(file_path):
        print(f"❌ ไฟล์ {file_path} ไม่พบ")
        return False
    
    print("🔍 ตรวจสอบการแก้ไขข้อผิดพลาดการคอมไพล์")
    print("=" * 60)
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='utf-16') as f:
                content = f.read()
        except UnicodeDecodeError:
            with open(file_path, 'r', encoding='latin-1') as f:
                content = f.read()
    
    fixes_status = []
    
    # 1. ตรวจสอบการแก้ไข array reference
    print("\n📋 1. ตรวจสอบการส่ง Array โดย Reference")
    array_pattern = r'void CalculateMultiTradeLots\(double total_lot, double &lots\[\], int &trade_count\)'
    if re.search(array_pattern, content):
        print("✅ แก้ไขแล้ว: double &lots[] (ส่งโดย reference)")
        fixes_status.append(True)
    else:
        print("❌ ยังไม่แก้ไข: ต้องเป็น double &lots[]")
        fixes_status.append(False)
    
    # 2. ตรวจสอบการแก้ไข SendBuyOrder return type
    print("\n📋 2. ตรวจสอบ SendBuyOrder Return Type")
    buy_pattern = r'bool SendBuyOrder\(string symbol, double confidence, double lot, int magic\)'
    if re.search(buy_pattern, content):
        print("✅ แก้ไขแล้ว: bool SendBuyOrder(...)")
        
        # ตรวจสอบ return statements
        buy_return_patterns = [
            r'return false;.*confidence',
            r'return true;.*Successfully',
            r'return false;.*Failed'
        ]
        
        return_count = 0
        for pattern in buy_return_patterns:
            if re.search(pattern, content, re.DOTALL):
                return_count += 1
        
        if return_count >= 2:
            print("✅ มี return statements ครบถ้วน")
            fixes_status.append(True)
        else:
            print("❌ ขาด return statements")
            fixes_status.append(False)
    else:
        print("❌ ยังไม่แก้ไข: ต้องเป็น bool SendBuyOrder(...)")
        fixes_status.append(False)
    
    # 3. ตรวจสอบการแก้ไข SendSellOrder return type
    print("\n📋 3. ตรวจสอบ SendSellOrder Return Type")
    sell_pattern = r'bool SendSellOrder\(string symbol, double confidence, double lot, int magic\)'
    if re.search(sell_pattern, content):
        print("✅ แก้ไขแล้ว: bool SendSellOrder(...)")
        
        # ตรวจสอบ return statements
        sell_return_patterns = [
            r'return false;.*confidence',
            r'return true;.*Successfully',
            r'return false;.*Failed'
        ]
        
        return_count = 0
        for pattern in sell_return_patterns:
            if re.search(pattern, content, re.DOTALL):
                return_count += 1
        
        if return_count >= 2:
            print("✅ มี return statements ครบถ้วน")
            fixes_status.append(True)
        else:
            print("❌ ขาด return statements")
            fixes_status.append(False)
    else:
        print("❌ ยังไม่แก้ไข: ต้องเป็น bool SendSellOrder(...)")
        fixes_status.append(False)
    
    # 4. ตรวจสอบการใช้งาน return values
    print("\n📋 4. ตรวจสอบการใช้งาน Return Values")
    usage_patterns = [
        r'trade_success = SendBuyOrder\(',
        r'trade_success = SendSellOrder\('
    ]
    
    usage_found = 0
    for pattern in usage_patterns:
        if re.search(pattern, content):
            usage_found += 1
    
    if usage_found >= 2:
        print("✅ ใช้งาน return values ถูกต้อง")
        fixes_status.append(True)
    else:
        print("❌ ไม่ได้ใช้งาน return values")
        fixes_status.append(False)
    
    # สรุปผลการตรวจสอบ
    print("\n" + "=" * 60)
    print("📊 สรุปผลการแก้ไข")
    
    all_fixed = all(fixes_status)
    fixed_count = sum(fixes_status)
    total_count = len(fixes_status)
    
    if all_fixed:
        print(f"🎉 แก้ไขครบถ้วนแล้ว ({fixed_count}/{total_count})")
        print("✅ ไฟล์พร้อมคอมไพล์")
    else:
        print(f"⚠️ แก้ไขแล้ว {fixed_count}/{total_count} ข้อ")
        print("❌ ยังมีปัญหาที่ต้องแก้ไข")
    
    return all_fixed

def show_compilation_guide():
    """แสดงคู่มือการคอมไพล์"""
    
    print("\n🔧 คู่มือการคอมไพล์ MT5")
    print("=" * 60)
    
    print("\n📋 ขั้นตอนการคอมไพล์:")
    print("1. เปิด MetaEditor")
    print("2. เปิดไฟล์ mt5_to_python_08_lot.mq5")
    print("3. กด F7 หรือ Compile")
    print("4. ตรวจสอบ Errors และ Warnings")
    
    print("\n⚠️ ข้อผิดพลาดที่แก้ไขแล้ว:")
    print("✅ 'lots' - arrays are passed by reference only")
    print("   → แก้ไข: double &lots[] (เพิ่ม & สำหรับ reference)")
    
    print("✅ 'SendBuyOrder' - expression of 'void' type is illegal")
    print("   → แก้ไข: เปลี่ยนจาก void เป็น bool SendBuyOrder(...)")
    print("   → เพิ่ม: return true/false ตามผลลัพธ์")
    
    print("✅ 'SendSellOrder' - expression of 'void' type is illegal")
    print("   → แก้ไข: เปลี่ยนจาก void เป็น bool SendSellOrder(...)")
    print("   → เพิ่ม: return true/false ตามผลลัพธ์")
    
    print("\n🎯 ประโยชน์ของการแก้ไข:")
    print("• ฟังก์ชันสามารถ return ผลลัพธ์การทำงาน")
    print("• สามารถตรวจสอบความสำเร็จของการเปิดเทรด")
    print("• ใช้งานร่วมกับระบบ Multi-Trade ได้")
    print("• ลดข้อผิดพลาดในการคอมไพล์")

def show_enhanced_features():
    """แสดงฟีเจอร์ที่ปรับปรุงแล้ว"""
    
    print("\n🚀 ฟีเจอร์ที่ปรับปรุงแล้ว")
    print("=" * 60)
    
    features = [
        {
            "name": "🔧 Price Adjustment System",
            "description": "ชดเชยราคาเมื่อราคาเปลี่ยนแปลง",
            "function": "CalculatePriceAdjustment()"
        },
        {
            "name": "🎯 Multi-Trade System", 
            "description": "เทรดหลายไม้พร้อมอัตราส่วน TP ต่างกัน",
            "function": "OpenMultiTrades(), CalculateMultiTradeLots()"
        },
        {
            "name": "⚖️ Break Even System",
            "description": "เลื่อน SL ไปที่จุดเข้าเมื่อกำไร = SL",
            "function": "CheckAndApplyBreakEven()"
        },
        {
            "name": "📅 Friday Close System",
            "description": "ปิดไม้ทั้งหมดวันศุกร์ 21:00",
            "function": "CheckAndCloseFridayPositions()"
        },
        {
            "name": "📊 Enhanced Signal Processing",
            "description": "ประมวลผลสัญญาณแบบครบถ้วน",
            "function": "OnTimer() - Enhanced workflow"
        }
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"\n{i}. {feature['name']}")
        print(f"   📝 {feature['description']}")
        print(f"   🔧 Function: {feature['function']}")
    
    print(f"\n🎉 รวม {len(features)} ฟีเจอร์ใหม่ที่พร้อมใช้งาน!")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🔧 MT5 Compilation Fixes Test")
    print("ทดสอบการแก้ไขข้อผิดพลาดการคอมไพล์")
    print("=" * 60)
    
    # ตรวจสอบการแก้ไข
    fixes_ok = check_mt5_file_fixes()
    
    # แสดงคู่มือการคอมไพล์
    show_compilation_guide()
    
    # แสดงฟีเจอร์ที่ปรับปรุง
    show_enhanced_features()
    
    print("\n" + "=" * 60)
    if fixes_ok:
        print("🎉 ระบบพร้อมใช้งาน!")
        print("📋 ขั้นตอนต่อไป:")
        print("1. คอมไพล์ไฟล์ MT5")
        print("2. รัน Python server")
        print("3. ทดสอบระบบ")
    else:
        print("⚠️ ยังมีปัญหาที่ต้องแก้ไข")
        print("📋 กรุณาตรวจสอบข้อผิดพลาดและแก้ไขให้ครบถ้วน")

if __name__ == "__main__":
    main()

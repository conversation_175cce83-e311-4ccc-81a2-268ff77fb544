#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข CSV files และการอัปเดตไฟล์หลัก
"""

import os
import pandas as pd
import sys

def test_fixed_csv_files():
    """ทดสอบไฟล์ CSV ที่แก้ไขแล้ว"""
    
    print("🧪 ทดสอบไฟล์ CSV ที่แก้ไขแล้ว")
    print("=" * 80)
    
    fixed_dir = "CSV_Files_Fixed"
    
    if not os.path.exists(fixed_dir):
        print(f"❌ ไม่พบโฟลเดอร์: {fixed_dir}")
        return False
    
    test_files = [
        "NZDUSD_M30_FIXED.csv",
        "USDJPY_M30_FIXED.csv",
        "AUDUSD_M30_FIXED.csv"
    ]
    
    success_count = 0
    
    for file_name in test_files:
        file_path = os.path.join(fixed_dir, file_name)
        
        print(f"\n📂 ทดสอบ: {file_name}")
        
        if not os.path.exists(file_path):
            print(f"   ❌ ไม่พบไฟล์: {file_path}")
            continue
        
        try:
            # อ่านไฟล์
            df = pd.read_csv(file_path)
            
            print(f"   📊 ข้อมูล: {df.shape[0]} rows, {df.shape[1]} columns")
            print(f"   📋 Columns: {list(df.columns)}")
            
            # ตรวจสอบข้อมูลตัวอย่าง
            print(f"   📋 ตัวอย่างข้อมูล 3 แถวแรก:")
            print(df.head(3).to_string(index=False))
            
            # ตรวจสอบว่ามี columns ที่คาดหวัง
            expected_columns = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'TickVol', 'Vol']
            
            has_expected = all(col in df.columns for col in expected_columns)
            
            if has_expected:
                print(f"   ✅ มี columns ที่คาดหวังครบถ้วน")
                success_count += 1
            else:
                print(f"   ❌ ไม่มี columns ที่คาดหวัง")
                missing = [col for col in expected_columns if col not in df.columns]
                print(f"   📋 Columns ที่หายไป: {missing}")
            
        except Exception as e:
            print(f"   ❌ เกิดข้อผิดพลาด: {str(e)}")
    
    print(f"\n📊 สรุปการทดสอบ:")
    print(f"   • ไฟล์ทดสอบ: {len(test_files)}")
    print(f"   • ผ่านการทดสอบ: {success_count}")
    print(f"   • อัตราความสำเร็จ: {(success_count/len(test_files))*100:.1f}%")
    
    return success_count == len(test_files)

def test_main_file_updates():
    """ทดสอบการอัปเดตไฟล์หลัก"""
    
    print("\n🧪 ทดสอบการอัปเดตไฟล์หลัก")
    print("=" * 80)
    
    main_file = "python_LightGBM_15_Tuning.py"
    
    if not os.path.exists(main_file):
        print(f"❌ ไม่พบไฟล์หลัก: {main_file}")
        return False
    
    try:
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # ตรวจสอบ test_groups
        print("🔍 ตรวจสอบ test_groups:")
        
        if "CSV_Files_Fixed/" in content:
            print("   ✅ พบการใช้ไฟล์จาก CSV_Files_Fixed/")
        else:
            print("   ❌ ไม่พบการใช้ไฟล์จาก CSV_Files_Fixed/")
            return False
        
        if "AUDUSD_M30_FIXED.csv" in content:
            print("   ✅ พบไฟล์ M30 ที่แก้ไขแล้ว")
        else:
            print("   ❌ ไม่พบไฟล์ M30 ที่แก้ไขแล้ว")
            return False
        
        if "AUDUSD_H1_FIXED.csv" in content:
            print("   ✅ พบไฟล์ H1 ที่แก้ไขแล้ว")
        else:
            print("   ❌ ไม่พบไฟล์ H1 ที่แก้ไขแล้ว")
            return False
        
        # ตรวจสอบการอ่าน CSV
        print("\n🔍 ตรวจสอบการอ่าน CSV:")
        
        if "df = pd.read_csv(file)" in content:
            print("   ✅ พบการอ่าน CSV แบบใหม่")
        else:
            print("   ❌ ไม่พบการอ่าน CSV แบบใหม่")
            return False
        
        if "str.split('\\t'" not in content:
            print("   ✅ ไม่พบการใช้ str.split() แล้ว")
        else:
            print("   ⚠️ ยังพบการใช้ str.split() อยู่")
        
        # ตรวจสอบ syntax
        print("\n🔍 ตรวจสอบ syntax:")
        
        try:
            compile(content, main_file, "exec")
            print("   ✅ ไม่มี syntax error")
        except SyntaxError as e:
            print(f"   ❌ พบ syntax error: {e}")
            return False
        
        print("\n✅ ไฟล์หลักผ่านการทดสอบทั้งหมด")
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        return False

def test_data_loading():
    """ทดสอบการโหลดข้อมูลจริง"""
    
    print("\n🧪 ทดสอบการโหลดข้อมูลจริง")
    print("=" * 80)
    
    # เพิ่ม path ปัจจุบันเข้าไปใน sys.path
    current_dir = os.getcwd()
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    try:
        # Import ฟังก์ชันจากไฟล์หลัก
        import importlib.util
        spec = importlib.util.spec_from_file_location("main_module", "python_LightGBM_15_Tuning.py")
        main_module = importlib.util.module_from_spec(spec)
        
        # ลองโหลด test_groups
        spec.loader.exec_module(main_module)
        test_groups = main_module.test_groups
        
        print(f"📊 test_groups ที่โหลดได้:")
        for timeframe, files in test_groups.items():
            print(f"   {timeframe}: {len(files)} ไฟล์")
            for file_name in files[:2]:  # แสดงแค่ 2 ไฟล์แรก
                print(f"      • {file_name}")
            if len(files) > 2:
                print(f"      • ... และอีก {len(files)-2} ไฟล์")
        
        # ทดสอบโหลดไฟล์จริง
        print(f"\n🔍 ทดสอบโหลดไฟล์จริง:")
        
        test_file = test_groups["M30"][0]  # ไฟล์แรกใน M30
        
        if os.path.exists(test_file):
            df = pd.read_csv(test_file)
            print(f"   ✅ โหลดไฟล์สำเร็จ: {test_file}")
            print(f"   📊 ข้อมูล: {df.shape[0]} rows, {df.shape[1]} columns")
            print(f"   📋 Columns: {list(df.columns)}")
            
            # ลบแถวแรก (header) เหมือนในไฟล์หลัก
            df_processed = df.drop(index=0).reset_index(drop=True)
            print(f"   📊 หลังลบ header: {df_processed.shape[0]} rows, {df_processed.shape[1]} columns")
            
            # ตรวจสอบข้อมูลตัวอย่าง
            print(f"   📋 ตัวอย่างข้อมูล 3 แถวแรก:")
            print(df_processed.head(3).to_string(index=False))
            
            return True
        else:
            print(f"   ❌ ไม่พบไฟล์: {test_file}")
            return False
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    
    print("🧪 ทดสอบการแก้ไข CSV Files และการอัปเดตไฟล์หลัก")
    print("=" * 80)
    
    # ทดสอบทั้งหมด
    tests = [
        ("ไฟล์ CSV ที่แก้ไขแล้ว", test_fixed_csv_files),
        ("การอัปเดตไฟล์หลัก", test_main_file_updates),
        ("การโหลดข้อมูลจริง", test_data_loading)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: ผ่าน")
            else:
                print(f"❌ {test_name}: ไม่ผ่าน")
        except Exception as e:
            print(f"❌ {test_name}: เกิดข้อผิดพลาด - {e}")
            results.append((test_name, False))
    
    # สรุปผล
    print(f"\n🎯 สรุปผลการทดสอบ")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"📊 สถิติ:")
    print(f"   • การทดสอบทั้งหมด: {total}")
    print(f"   • ผ่านการทดสอบ: {passed}")
    print(f"   • ไม่ผ่านการทดสอบ: {total - passed}")
    print(f"   • อัตราความสำเร็จ: {(passed/total)*100:.1f}%")
    
    print(f"\n📋 รายละเอียด:")
    for test_name, result in results:
        status = "✅ ผ่าน" if result else "❌ ไม่ผ่าน"
        print(f"   • {test_name}: {status}")
    
    if passed == total:
        print(f"\n🎉 การแก้ไขสำเร็จทั้งหมด!")
        print(f"✅ พร้อมใช้งานไฟล์ที่แก้ไขแล้ว")
        print(f"💡 ทดสอบด้วยการรัน: python python_LightGBM_15_Tuning.py")
    else:
        print(f"\n⚠️ มีการทดสอบที่ไม่ผ่าน")
        print(f"🔍 ตรวจสอบข้อผิดพลาดด้านบน")

if __name__ == "__main__":
    main()

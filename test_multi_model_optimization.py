#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบฟังก์ชันการหา optimal threshold และ nBars_SL สำหรับ Multi-Model Architecture

Created: 2025-01-11
Author: AI Assistant
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
from pathlib import Path

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_load_scenario_models():
    """
    ทดสอบการโหลดโมเดลทั้ง 2 scenarios
    """
    print("🔍 ทดสอบการโหลดโมเดลทั้ง 2 scenarios")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import load_scenario_models
        
        symbol = "GOLD"  # ใช้ GOLD เพราะมีโมเดลอยู่
        timeframe = 60
        
        print(f"📊 กำลังโหลดโมเดลสำหรับ {symbol} M{timeframe}")
        
        loaded_models = load_scenario_models(symbol, timeframe)
        
        if loaded_models:
            print(f"✅ โหลดโมเดลสำเร็จ: {len(loaded_models)} scenarios")
            
            for scenario_name, model_info in loaded_models.items():
                print(f"\n📋 {scenario_name}:")
                print(f"   Model: {type(model_info['model']).__name__}")
                print(f"   Features: {len(model_info['features'])} features")
                print(f"   Has scaler: {'scaler' in model_info}")
                
                # แสดง features บางส่วน
                features_sample = model_info['features'][:5]
                print(f"   Sample features: {features_sample}")
            
            return loaded_models
        else:
            print("❌ ไม่สามารถโหลดโมเดลได้")
            return None
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return None
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_threshold_optimization_functions():
    """
    ทดสอบฟังก์ชันการหา optimal threshold
    """
    print("\n🔍 ทดสอบฟังก์ชันการหา optimal threshold")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import (
            find_optimal_threshold_multi_model,
            load_scenario_threshold
        )
        
        print("✅ Import ฟังก์ชัน threshold สำเร็จ")
        
        # ทดสอบการโหลด threshold (ควรใช้ default เพราะยังไม่มีไฟล์)
        symbol = "GOLD"
        timeframe = 60
        
        for scenario in ["trend_following", "counter_trend"]:
            threshold = load_scenario_threshold(symbol, timeframe, scenario)
            print(f"📊 {scenario} threshold: {threshold}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_nbars_optimization_functions():
    """
    ทดสอบฟังก์ชันการหา optimal nBars_SL
    """
    print("\n🔍 ทดสอบฟังก์ชันการหา optimal nBars_SL")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import (
            find_optimal_nbars_sl_multi_model,
            load_scenario_nbars
        )
        
        print("✅ Import ฟังก์ชัน nBars_SL สำเร็จ")
        
        # ทดสอบการโหลด nBars_SL (ควรใช้ default เพราะยังไม่มีไฟล์)
        symbol = "GOLD"
        timeframe = 60
        
        for scenario in ["trend_following", "counter_trend"]:
            nbars = load_scenario_nbars(symbol, timeframe, scenario)
            print(f"📊 {scenario} nBars_SL: {nbars}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def simulate_optimization_workflow():
    """
    จำลองขั้นตอนการหา optimal parameters
    """
    print("\n🔍 จำลองขั้นตอนการหา optimal parameters")
    print("="*50)
    
    # 1. โหลดโมเดล
    loaded_models = test_load_scenario_models()
    
    if not loaded_models:
        print("❌ ไม่สามารถโหลดโมเดลได้ - ข้ามการทดสอบ")
        return False
    
    # 2. สร้างข้อมูล validation ตัวอย่าง
    print(f"\n📊 สร้างข้อมูล validation ตัวอย่าง")
    
    # สร้างข้อมูลตัวอย่างที่มี features ที่โมเดลต้องการ
    first_model = list(loaded_models.values())[0]
    required_features = first_model['features']
    
    # สร้าง DataFrame ตัวอย่าง
    np.random.seed(42)
    n_samples = 1000
    
    val_data = {}
    for feature in required_features:
        if feature == 'Target':
            val_data[feature] = np.random.choice([0, 1], n_samples)
        else:
            val_data[feature] = np.random.randn(n_samples)
    
    # เพิ่มคอลัมน์ที่จำเป็นสำหรับการทดสอบ
    val_data.update({
        'High': np.random.uniform(1.0, 2.0, n_samples),
        'Low': np.random.uniform(0.5, 1.0, n_samples),
        'Close': np.random.uniform(0.75, 1.5, n_samples),
        'Profit': np.random.uniform(-100, 100, n_samples)
    })
    
    val_df = pd.DataFrame(val_data)
    
    print(f"✅ สร้างข้อมูล validation: {val_df.shape}")
    print(f"📋 Features: {len(required_features)} features")
    print(f"📋 Sample features: {required_features[:5]}")
    
    # 3. ทดสอบการหา optimal threshold (จำลอง)
    print(f"\n🎯 จำลองการหา optimal threshold")
    
    try:
        from python_LightGBM_16_Signal import find_optimal_threshold_multi_model
        
        # ในการใช้งานจริง จะเรียกฟังก์ชันนี้
        # optimal_thresholds = find_optimal_threshold_multi_model(
        #     models_dict=loaded_models,
        #     val_df=val_df,
        #     symbol="GOLD",
        #     timeframe=60
        # )
        
        print("📝 ฟังก์ชัน find_optimal_threshold_multi_model พร้อมใช้งาน")
        print("⚠️ ไม่รันจริงเพื่อประหยัดเวลา (ต้องการข้อมูล validation จริง)")
        
    except Exception as e:
        print(f"❌ Error in threshold optimization: {e}")
    
    # 4. ทดสอบการหา optimal nBars_SL (จำลอง)
    print(f"\n🎯 จำลองการหา optimal nBars_SL")
    
    try:
        from python_LightGBM_16_Signal import find_optimal_nbars_sl_multi_model
        
        # ในการใช้งานจริง จะเรียกฟังก์ชันนี้
        # optimal_nbars = find_optimal_nbars_sl_multi_model(
        #     models_dict=loaded_models,
        #     val_df=val_df,
        #     symbol="GOLD",
        #     timeframe=60
        # )
        
        print("📝 ฟังก์ชัน find_optimal_nbars_sl_multi_model พร้อมใช้งาน")
        print("⚠️ ไม่รันจริงเพื่อประหยัดเวลา (ต้องการข้อมูล validation จริง)")
        
    except Exception as e:
        print(f"❌ Error in nBars_SL optimization: {e}")
    
    return True

def check_expected_files():
    """
    ตรวจสอบไฟล์ที่คาดหวังหลังจากรันการหา optimal parameters
    """
    print("\n🔍 ตรวจสอบไฟล์ที่คาดหวัง")
    print("="*40)
    
    base_path = Path("LightGBM_Multi/thresholds")
    symbols = ["AUDUSD", "GOLD", "USDJPY"]
    scenarios = ["trend_following", "counter_trend"]
    timeframe = 60
    
    expected_files = []
    existing_files = []
    
    for symbol in symbols:
        for scenario in scenarios:
            # Threshold files
            threshold_file = f"{str(timeframe).zfill(3)}_{symbol}_{scenario}_optimal_threshold.pkl"
            threshold_path = base_path / threshold_file
            expected_files.append(threshold_file)
            
            if threshold_path.exists():
                existing_files.append(threshold_file)
                print(f"✅ {threshold_file}")
            else:
                print(f"❌ {threshold_file}")
            
            # nBars_SL files
            nbars_file = f"{str(timeframe).zfill(3)}_{symbol}_{scenario}_optimal_nBars_SL.pkl"
            nbars_path = base_path / nbars_file
            expected_files.append(nbars_file)
            
            if nbars_path.exists():
                existing_files.append(nbars_file)
                print(f"✅ {nbars_file}")
            else:
                print(f"❌ {nbars_file}")
    
    print(f"\n📊 สรุป:")
    print(f"   Expected files: {len(expected_files)}")
    print(f"   Existing files: {len(existing_files)}")
    print(f"   Coverage: {len(existing_files)/len(expected_files)*100:.1f}%")
    
    return len(existing_files), len(expected_files)

def provide_usage_instructions():
    """
    แสดงคำแนะนำการใช้งาน
    """
    print("\n📝 คำแนะนำการใช้งาน")
    print("="*40)
    
    print("🚀 ขั้นตอนการใช้งานจริง:")
    print("1. เทรนโมเดล Multi-Model Architecture:")
    print("   USE_MULTI_MODEL_ARCHITECTURE = True")
    print("   python python_LightGBM_16_Signal.py")
    
    print("\n2. หา optimal threshold:")
    print("   loaded_models = load_scenario_models(symbol, timeframe)")
    print("   optimal_thresholds = find_optimal_threshold_multi_model(")
    print("       models_dict=loaded_models,")
    print("       val_df=validation_data,")
    print("       symbol=symbol,")
    print("       timeframe=timeframe")
    print("   )")
    
    print("\n3. หา optimal nBars_SL:")
    print("   optimal_nbars = find_optimal_nbars_sl_multi_model(")
    print("       models_dict=loaded_models,")
    print("       val_df=validation_data,")
    print("       symbol=symbol,")
    print("       timeframe=timeframe")
    print("   )")
    
    print("\n4. ใช้งานใน Production:")
    print("   threshold = load_scenario_threshold(symbol, timeframe, scenario)")
    print("   nbars = load_scenario_nbars(symbol, timeframe, scenario)")

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🚀 ทดสอบฟังก์ชัน Multi-Model Optimization")
    print("="*60)
    
    # 1. ทดสอบการโหลดโมเดล
    loaded_models = test_load_scenario_models()
    
    # 2. ทดสอบฟังก์ชัน threshold
    threshold_ok = test_threshold_optimization_functions()
    
    # 3. ทดสอบฟังก์ชัน nBars_SL
    nbars_ok = test_nbars_optimization_functions()
    
    # 4. จำลองขั้นตอนการทำงาน
    if loaded_models and threshold_ok and nbars_ok:
        simulate_optimization_workflow()
    
    # 5. ตรวจสอบไฟล์ที่คาดหวัง
    existing, expected = check_expected_files()
    
    # 6. แสดงคำแนะนำ
    provide_usage_instructions()
    
    print("\n" + "="*60)
    print("✅ การทดสอบเสร็จสิ้น")
    
    if existing == 0:
        print("\n💡 ข้อแนะนำ:")
        print("- รันการเทรนโมเดล Multi-Model Architecture ก่อน")
        print("- จากนั้นรันการหา optimal parameters")
        print("- ตรวจสอบไฟล์ที่สร้างขึ้นใน thresholds/")

if __name__ == "__main__":
    main()

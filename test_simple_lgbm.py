#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบ LightGBM แบบง่าย
"""

import numpy as np
import pandas as pd
import lightgbm as lgb
from sklearn.model_selection import RandomizedSearchCV, TimeSeriesSplit

def get_lgbm_params_test(y=None, use_scale_pos_weight=True):
    """ฟังก์ชันทดสอบ get_lgbm_params"""
    
    # ตรวจสอบว่าเป็น multi-class หรือ binary classification
    unique_classes = np.unique(y) if y is not None else []
    num_unique_classes = len(unique_classes)
    
    # ตรวจสอบเงื่อนไขสำหรับ multi-class
    USE_MULTICLASS_TARGET = True  # สำหรับการทดสอบ
    is_multiclass = (USE_MULTICLASS_TARGET and
                    y is not None and
                    num_unique_classes > 2 and
                    num_unique_classes <= 10)
    
    print(f"  📊 จำนวน unique classes: {num_unique_classes}")
    print(f"  📊 Classes: {unique_classes}")
    print(f"  🎯 USE_MULTICLASS_TARGET: {USE_MULTICLASS_TARGET}")
    print(f"  🎯 Is Multi-class: {is_multiclass}")
    
    if is_multiclass:
        num_classes = len(np.unique(y))
        print(f"  🎯 Multi-class Classification: {num_classes} classes")
        
        params = {
            'objective': 'multiclass',
            'num_class': num_classes,
            'metric': ['multi_logloss', 'multi_error'],
            'boosting_type': 'gbdt',
            'learning_rate': 0.05,
            'num_leaves': 15,
            'max_depth': 6,
            'min_data_in_leaf': 15,
            'reg_alpha': 0.3,
            'reg_lambda': 0.3,
            'feature_fraction': 0.8,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'min_gain_to_split': 0.01,
            'max_bin': 255,
            'verbosity': -1,
            'random_state': 42,
            'n_jobs': -1
        }
    else:
        # Binary classification
        params = {
            'objective': 'binary',
            'metric': ['auc', 'binary_logloss', 'binary_error'],
            'boosting_type': 'gbdt',
            'learning_rate': 0.08,
            'num_leaves': 10,
            'max_depth': 5,
            'min_data_in_leaf': 20,
            'reg_alpha': 0.2,
            'reg_lambda': 0.2,
            'feature_fraction': 0.8,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'min_gain_to_split': 0.01,
            'max_bin': 255,
            'verbosity': -1,
            'random_state': 42,
            'n_jobs': -1
        }
    
    return params

def test_duplicate_params_fix():
    """ทดสอบการแก้ไข duplicate parameters"""
    
    print("🧪 ทดสอบการแก้ไข Duplicate Parameters")
    print("="*60)
    
    # สร้างข้อมูลทดสอบ multi-class
    np.random.seed(42)
    X = np.random.randn(1000, 10)
    y = np.random.randint(0, 5, 1000)  # 5 classes
    
    print(f"📊 ข้อมูลทดสอบ:")
    print(f"  - X shape: {X.shape}")
    print(f"  - y shape: {y.shape}")
    print(f"  - Unique classes: {np.unique(y)}")
    print(f"  - Class counts: {np.bincount(y)}")
    
    try:
        # ทดสอบ get_lgbm_params
        print(f"\n🔧 ทดสอบ get_lgbm_params()...")
        params = get_lgbm_params_test(y=y, use_scale_pos_weight=True)
        
        print(f"✅ Parameters ที่ได้:")
        for key, value in params.items():
            print(f"  - {key}: {value}")
        
        # ทดสอบการแก้ไข duplicate random_state
        print(f"\n🔧 ทดสอบการแก้ไข duplicate random_state...")
        
        # แยก random_state ออกจาก params เพื่อไม่ให้ซ้ำ
        tuning_params = params.copy()
        tuning_params.pop('random_state', None)
        
        lgb_estimator = lgb.LGBMClassifier(
            **tuning_params,
            n_estimators=100,
            random_state=42
        )
        
        print(f"✅ สร้าง LGBMClassifier สำเร็จ!")
        print(f"  - Objective: {lgb_estimator.objective}")
        print(f"  - Num_class: {getattr(lgb_estimator, 'num_class', 'N/A')}")
        
        # ทดสอบ RandomizedSearchCV
        print(f"\n🔧 ทดสอบ RandomizedSearchCV...")
        
        param_dist = {
            'learning_rate': [0.05, 0.1, 0.15],
            'num_leaves': [10, 15, 20]
        }
        
        # เลือก scoring metric ตาม classification type
        is_multiclass = params.get('objective') == 'multiclass'
        scoring_metric = 'f1_macro' if is_multiclass else 'roc_auc'
        
        search = RandomizedSearchCV(
            lgb_estimator,
            param_distributions=param_dist,
            n_iter=3,
            scoring=scoring_metric,
            cv=TimeSeriesSplit(n_splits=3),
            verbose=1,
            random_state=42,
            n_jobs=1
        )
        
        # Convert to DataFrame เพื่อหลีกเลี่ยง warning
        X_df = pd.DataFrame(X, columns=[f'feature_{i}' for i in range(X.shape[1])])
        
        search.fit(X_df, y)
        
        print(f"✅ RandomizedSearchCV สำเร็จ!")
        print(f"  - Best {scoring_metric}: {search.best_score_:.4f}")
        print(f"  - Best params: {search.best_params_}")
        
        # ทดสอบ fit โมเดลหลัก
        print(f"\n🔧 ทดสอบ fit โมเดลหลัก...")
        
        main_model = lgb.LGBMClassifier(
            **params,
            n_estimators=100
        )
        
        main_model.fit(X_df, y)
        print(f"✅ Fit โมเดลหลักสำเร็จ!")
        
        # ทดสอบ predict
        predictions = main_model.predict(X_df[:10])
        print(f"✅ Predict สำเร็จ!")
        print(f"  - Predictions: {predictions}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_binary_classification():
    """ทดสอบ binary classification"""
    
    print(f"\n🧪 ทดสอบ Binary Classification")
    print("="*60)
    
    # สร้างข้อมูลทดสอบ binary
    np.random.seed(42)
    X = np.random.randn(500, 5)
    y = np.random.randint(0, 2, 500)  # 2 classes
    
    print(f"📊 ข้อมูลทดสอบ:")
    print(f"  - X shape: {X.shape}")
    print(f"  - y shape: {y.shape}")
    print(f"  - Unique classes: {np.unique(y)}")
    print(f"  - Class counts: {np.bincount(y)}")
    
    try:
        params = get_lgbm_params_test(y=y, use_scale_pos_weight=True)
        
        # แยก random_state ออก
        tuning_params = params.copy()
        tuning_params.pop('random_state', None)
        
        lgb_estimator = lgb.LGBMClassifier(
            **tuning_params,
            n_estimators=50,
            random_state=42
        )
        
        # Convert to DataFrame
        X_df = pd.DataFrame(X, columns=[f'feature_{i}' for i in range(X.shape[1])])
        
        lgb_estimator.fit(X_df, y)
        print(f"✅ Binary Classification สำเร็จ!")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดใน Binary Classification: {str(e)}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    
    print("🧪 ทดสอบการแก้ไข LightGBM Duplicate Parameters")
    print("="*70)
    
    # ทดสอบ multi-class
    multiclass_success = test_duplicate_params_fix()
    
    # ทดสอบ binary
    binary_success = test_binary_classification()
    
    print(f"\n🏁 สรุปผลการทดสอบ")
    print("="*70)
    
    if multiclass_success and binary_success:
        print("✅ การทดสอบทั้งหมดสำเร็จ!")
        print("💡 การแก้ไข duplicate parameters ทำงานได้:")
        print("  1. ✅ Multi-class Classification ทำงานได้")
        print("  2. ✅ Binary Classification ทำงานได้")
        print("  3. ✅ RandomizedSearchCV ทำงานได้")
        print("  4. ✅ ไม่มี duplicate random_state errors")
        print("  5. ✅ Scoring metrics เลือกได้ถูกต้อง")
        
        print(f"\n🚀 พร้อมสำหรับการเทรนจริง:")
        print("  - Multi-class: ใช้ f1_macro scoring")
        print("  - Binary: ใช้ roc_auc scoring")
        print("  - ไม่มี parameter conflicts")
        
    else:
        print("❌ การทดสอบล้มเหลว!")
        if not multiclass_success:
            print("  - ปัญหาใน Multi-class Classification")
        if not binary_success:
            print("  - ปัญหาใน Binary Classification")
    
    return multiclass_success and binary_success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)

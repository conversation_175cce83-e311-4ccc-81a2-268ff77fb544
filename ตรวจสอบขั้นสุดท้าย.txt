ตรวจสอบขั้นสุดท้าย

ช่วยตรวจสอบอย่างละเอียด

1. เทรนโมเดล >> python_LightGBM_17_Signal.py
2. รับข้อมูล+วิเคราะห์+ส่งกลับ MT5 และส่ง telegram >> python_to_mt5_WebRequest_server_13_Signal.py
3. เปิดการซื้อ-ขาย และส่ง telegram >>mt5_to_python_10_lot.mq5

// โครงสร้างไฟล์ python_LightGBM_17_Signal.py

CSV_Files_Fixed << จัดเก็บข้อมูลทดสอบ csv
├─ {symbol}_H1_FIXED.csv
└─ {symbol}_M30_FIXED.csv

LightGBM_Multi << สรุปแนะนำการเทรดรายวัน
└─ results
       ├─ M60_daily_trading_schedule_summary.txt
       ├─ M30_daily_trading_schedule_summary.txt
       └─ daily_trading_schedule_summary.txt

กรณี ใช้ 2 โมเดล มีโครงสร้างไฟล์ดังนี้
LightGBM_Hyper_Multi
└─ {timeframe}_{symbol}
       ├─ {timeframe}_{symbol}_counter_trend_best_params.json
       ├─ {timeframe}_{symbol}_counter_trend_tuning_flag.json
       ├─ {timeframe}_{symbol}_trend_following_best_params.json
       └─ {timeframe}_{symbol}_trend_following_tuning_flag.json

LightGBM_Multi
├─ feature_importance
│   └─ {timeframe}_must_have_features.pkl
├─ models
│   ├─ counter_trend
│   │   ├─ {timeframe}_{symbol}_features.pkl
│   │   ├─ {timeframe}_{symbol}_scaler.pkl
│   │   └─ {timeframe}_{symbol}_trained.pkl
│   └─ trend_following
│         ├─ {timeframe}_{symbol}_features.pkl
│         ├─ {timeframe}_{symbol}_scaler.pkl
│         └─ {timeframe}_{symbol}_trained.pkl
└─ thresholds
       ├─ {timeframe}_{symbol}_counter_trend_optimal_nBars_SL.pkl
       ├─ {timeframe}_{symbol}_counter_trend_optimal_threshold.pkl
       ├─ {timeframe}_{symbol}_trend_following_optimal_nBars_SL.pkl
       ├─ {timeframe}_{symbol}_trend_following_optimal_threshold.pkl
       └─ {timeframe}_{symbol}_time_filters.pkl

เนื่องจากการใช้ multi-model 
+ trend_following
+ counter_trend

ตรวจสอบการจำแนก และการตัดสินใจ Buy/Sell 
ตรวจสอบ CLASS_MAPPING และ PROFIT_THRESHOLDS และการใช้ multiclass predictions
Multiclass Target Classes:
Class 0: strong_sell (ขาดทุนมาก)
Class 1: weak_sell (ขาดทุนปานกลาง)
Class 2: no_trade (ไม่ควรเทรด)
Class 3: weak_buy (กำไรปานกลาง)
Class 4: strong_buy (กำไรมาก)

ตรวจสอบการใช้งาน entry_conditions และการจำแนกเหตุการณ์
ตรวจสอบการตั้งชื่อไฟล์เพื่อ load / save / link

ไฟล์ที่เกี่ยวข้อง
best_params.json
tuning_flag.json 
features.pkl 
scaler.pkl 
trained.pkl 
nBars_SL.pkl 
threshold.pkl 
time_filters.pkl
* ไฟล์ที่เกี่ยวข้องทั้งหมด << ตรวจสอบการเรียกใช้งาน และการ load และการป้องกันเมื่อผิดพลาด หรือไม่พบไฟล์

เนื่องจากเปิดมา 1 สัปดาห์ไม่มีการเปิดการซื้อขาย มีแต่แจ้ง Class SELL ที่ MT5 ช่วยตรวจสอบเกิดจากอะไร
+ server โหลดไฟล์ที่เกี่ยวข้องไม่ได้
+ server ไม่มีการใช้งานไฟล์ที่เกี่ยวข้อง
+ การวิเคราะห์ที่ server ไม่สามารถวิเคราะห์ได้
+ การส่งข้อมูลจาก server ไปยัง MT5 ไม่ครบ
+ MT5 รับข้อมูลแล้วไม่เปิดการซื้อ-ขาย
** ช่วยหาสาเหตุและแก้ไข


+++

ช่วยหาวิธีการตรวจสอบอย่างเป็นแทนทาง หรือข้อๆ
เพื่อให้ตรวจสอบย้อนหลังได้
เช่น เปิดทิ้งไว้ แล้วตอนเที่ยงมาตรวจสอบการทำงาน
อาจเป็นการตรวจสอบการ รับ-ส่งข้อมูล
การให้สัญญาณ

+++

python vscode

โครงสร้างไฟล์
LightGBM_Multi
│
├─ feature_importance
│   └─ {timeframe}_must_have_features.pkl
│
├─ models
│   ├─ counter_trend
│   │   ├─ {timeframe}_{symbol}_features.pkl
│   │   ├─ {timeframe}_{symbol}_scaler.pkl
│   │   └─ {timeframe}_{symbol}_trained.pkl
│   └─ trend_following
│         ├─ {timeframe}_{symbol}_features.pkl
│         ├─ {timeframe}_{symbol}_scaler.pkl
│         └─ {timeframe}_{symbol}_trained.pkl
│
└─ thresholds
       ├─ {timeframe}_{symbol}_counter_trend_optimal_nBars_SL.pkl
       ├─ {timeframe}_{symbol}_counter_trend_optimal_threshold.pkl
       ├─ {timeframe}_{symbol}_trend_following_optimal_nBars_SL.pkl
       ├─ {timeframe}_{symbol}_trend_following_optimal_threshold.pkl
       └─ {timeframe}_{symbol}_time_filters.pkl

ต้องการสร้างฟังชั่นสรุปเงื่อนไข
+ {timeframe}_{symbol}_counter_trend_optimal_nBars_SL.pkl
+ {timeframe}_{symbol}_counter_trend_optimal_threshold.pkl
+ {timeframe}_{symbol}_trend_following_optimal_nBars_SL.pkl
+ {timeframe}_{symbol}_trend_following_optimal_threshold.pkl
+ {timeframe}_{symbol}_time_filters.pkl

แยกแต่ละ {timeframe}_{symbol

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
from datetime import datetime

def test_time_filters_enabled():
    """ทดสอบระบบเมื่อเปิดใช้ time filters"""
    
    url = 'http://127.0.0.1:54321/data'
    
    print("🔍 Testing with Time Filters ENABLED")
    print("="*50)
    
    # สร้างข้อมูลทดสอบ 210 แท่ง
    bars = []
    base_time = int(datetime.now().timestamp()) - (210 * 1800)
    base_price = 2650.0
    
    for i in range(210):
        bar = {
            "time": base_time + (i * 1800),
            "open": round(base_price + (i * 0.1), 2),
            "high": round(base_price + (i * 0.1) + 1.0, 2),
            "low": round(base_price + (i * 0.1) - 1.0, 2),
            "close": round(base_price + (i * 0.1) + 0.5, 2),
            "volume": 1000 + i,
            "tick_volume": 1000 + i,
            "spread": 5,
            "real_volume": 1000 + i
        }
        bars.append(bar)
    
    test_data = {
        "symbol": "GOLD",
        "timeframe_str": "PERIOD_M30",
        "bars": bars
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            
            print("📊 Server Response:")
            print(f"   Signal: {result.get('signal', 'N/A')}")
            print(f"   Confidence: {result.get('confidence', 0.0):.4f}")
            print(f"   Time Filters: {result.get('time_filters', 'N/A')}")
            
            # วิเคราะห์ time filters
            time_filters = result.get('time_filters', '')
            if time_filters:
                print(f"\n🕐 Time Filter Analysis:")
                print(f"   Raw: {time_filters}")
                
                # แยก days และ hours
                if 'Days:' in time_filters and 'Hours:' in time_filters:
                    days_part = time_filters.split('Days:')[1].split(',Hours:')[0]
                    hours_part = time_filters.split('Hours:')[1]
                    
                    print(f"   Days: {days_part}")
                    print(f"   Hours: {hours_part}")
                    
                    # ตรวจสอบเวลาปัจจุบัน
                    now = datetime.now()
                    current_hour = now.hour
                    current_day = now.weekday()  # 0=Monday
                    
                    print(f"\n⏰ Current Time Check:")
                    print(f"   Current Day: {current_day} ({['Mon','Tue','Wed','Thu','Fri','Sat','Sun'][current_day]})")
                    print(f"   Current Hour: {current_hour}")
                    
                    # ตรวจสอบว่าเวลาปัจจุบันผ่านเงื่อนไขหรือไม่
                    days_allowed = str(current_day) in days_part
                    hours_allowed = str(current_hour) in hours_part
                    
                    print(f"   Day Allowed: {days_allowed}")
                    print(f"   Hour Allowed: {hours_allowed}")
                    print(f"   Overall: {'✅ ALLOWED' if days_allowed and hours_allowed else '❌ BLOCKED'}")
            
            return result
            
        else:
            print(f"❌ Server error: HTTP {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return None

def show_configuration_guide():
    """แสดงคู่มือการตั้งค่า time filters"""
    
    print("\n📋 Time Filter Configuration Guide")
    print("="*60)
    
    print("\n🖥️  Python Server Settings:")
    print("   File: python_to_mt5_WebRequest_server_13_Signal.py")
    print("   Variable: ENABLE_TIME_FILTERS")
    print("   - True  = ใช้ time filters จากไฟล์ที่บันทึกไว้")
    print("   - False = เทรดได้ตลอดเวลา (ไม่สนใจ time filters)")
    print()
    print("   Variable: DEFAULT_TIME_FILTERS")
    print("   - ใช้เมื่อไม่พบไฟล์ time filters")
    print("   - Default: จันทร์-ศุกร์, 06:00-21:59")
    
    print("\n🔧 MT5 EA Settings:")
    print("   File: mt5_to_python_10_lot.mq5")
    print("   Variable: IgnoreTimeFilters")
    print("   - false = ใช้ time filters จาก server")
    print("   - true  = ไม่สนใจ time filters (เทรดได้ตลอดเวลา)")
    print()
    print("   Variable: ShowTimeFilterStatus")
    print("   - true  = แสดงสถานะ time filters ใน log")
    print("   - false = ไม่แสดงสถานะ")
    
    print("\n🎯 Recommended Configurations:")
    print()
    print("   1. Normal Trading (ใช้ time filters):")
    print("      Python: ENABLE_TIME_FILTERS = True")
    print("      MT5:    IgnoreTimeFilters = false")
    print()
    print("   2. 24/7 Trading (ไม่ใช้ time filters):")
    print("      Python: ENABLE_TIME_FILTERS = False")
    print("      MT5:    IgnoreTimeFilters = true")
    print()
    print("   3. Server filters only:")
    print("      Python: ENABLE_TIME_FILTERS = True")
    print("      MT5:    IgnoreTimeFilters = true")
    print()
    print("   4. MT5 filters only:")
    print("      Python: ENABLE_TIME_FILTERS = False")
    print("      MT5:    IgnoreTimeFilters = false")

def show_time_scenarios():
    """แสดงตัวอย่างการทำงานในเวลาต่างๆ"""
    
    print("\n🕐 Time Filter Behavior Examples")
    print("="*50)
    
    # ตัวอย่าง time filters ทั่วไป
    scenarios = [
        {
            "name": "Business Hours (Mon-Fri 6-21)",
            "filter": "Days:[0,1,2,3,4],Hours:[6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21]"
        },
        {
            "name": "24/7 Trading",
            "filter": "Days:[0,1,2,3,4,5,6],Hours:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23]"
        },
        {
            "name": "Office Hours (Mon-Fri 8-17)",
            "filter": "Days:[0,1,2,3,4],Hours:[8,9,10,11,12,13,14,15,16,17]"
        },
        {
            "name": "Conservative (Tue-Thu 10-16)",
            "filter": "Days:[1,2,3],Hours:[10,11,12,13,14,15,16]"
        }
    ]
    
    # เวลาทดสอบ
    test_times = [
        (0, 8, "Monday 8:00"),
        (1, 14, "Tuesday 14:00"),
        (4, 20, "Friday 20:00"),
        (5, 10, "Saturday 10:00"),
        (6, 15, "Sunday 15:00"),
        (2, 3, "Wednesday 3:00"),
        (3, 22, "Thursday 22:00"),
    ]
    
    print("📊 Filter Compatibility Matrix:")
    print()
    
    # Header
    header = "Time".ljust(16)
    for scenario in scenarios:
        header += scenario["name"][:12].ljust(14)
    print(header)
    print("-" * len(header))
    
    # Test each time
    for day, hour, time_desc in test_times:
        row = time_desc.ljust(16)
        
        for scenario in scenarios:
            filter_str = scenario["filter"]
            
            # Parse filter
            days_part = filter_str.split('Days:')[1].split(',Hours:')[0].strip('[]')
            hours_part = filter_str.split('Hours:')[1].strip('[]')
            
            days_allowed = str(day) in days_part
            hours_allowed = str(hour) in hours_part
            allowed = days_allowed and hours_allowed
            
            status = "✅ ALLOW" if allowed else "❌ BLOCK"
            row += status.ljust(14)
        
        print(row)

def main():
    print("🎯 Time Filters Control & Testing Tool")
    print("This tool helps test and configure time filter behavior")
    print()
    
    print("Available options:")
    print("1. Test current time filters (server must be running)")
    print("2. Show configuration guide")
    print("3. Show time filter scenarios")
    print("4. All information")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    if choice == '1':
        result = test_time_filters_enabled()
        if result:
            print("\n✅ Test completed successfully")
        else:
            print("\n❌ Test failed - check if server is running")
    
    elif choice == '2':
        show_configuration_guide()
    
    elif choice == '3':
        show_time_scenarios()
    
    elif choice == '4':
        result = test_time_filters_enabled()
        show_configuration_guide()
        show_time_scenarios()
    
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขปัญหา Timeframe Cross-contamination และ Symbol List
"""

import os
import sys

def test_timeframe_separation():
    """
    ทดสอบการแยก timeframe ในโฟลเดอร์ผลลัพธ์
    """
    print("🧪 ทดสอบการแยก Timeframe")
    print("=" * 50)
    
    results_folder = "Test_LightGBM/results"
    
    # ตรวจสอบโครงสร้างโฟลเดอร์
    if os.path.exists(results_folder):
        print(f"📁 โฟลเดอร์ผลลัพธ์: {results_folder}")
        
        # ตรวจสอบโฟลเดอร์ M30 และ M60
        for timeframe in ['M30', 'M60']:
            timeframe_folder = os.path.join(results_folder, timeframe)
            if os.path.exists(timeframe_folder):
                print(f"\n📂 โฟลเดอร์ {timeframe}:")
                files = os.listdir(timeframe_folder)
                
                # แยกไฟล์ตาม prefix
                m30_files = [f for f in files if f.startswith('030_')]
                m60_files = [f for f in files if f.startswith('060_')]
                
                print(f"  ไฟล์ M30 (030_): {len(m30_files)} ไฟล์")
                print(f"  ไฟล์ M60 (060_): {len(m60_files)} ไฟล์")
                
                # ตรวจสอบการปนเปื้อน
                if timeframe == 'M30' and m60_files:
                    print(f"  ⚠️ พบไฟล์ M60 ในโฟลเดอร์ M30: {len(m60_files)} ไฟล์")
                    print(f"     ตัวอย่าง: {m60_files[:3]}")
                    
                if timeframe == 'M60' and m30_files:
                    print(f"  ⚠️ พบไฟล์ M30 ในโฟลเดอร์ M60: {len(m30_files)} ไฟล์")
                    print(f"     ตัวอย่าง: {m30_files[:3]}")
                    
                if timeframe == 'M30' and not m60_files and m30_files:
                    print(f"  ✅ โฟลเดอร์ M30 มีเฉพาะไฟล์ M30")
                    
                if timeframe == 'M60' and not m30_files and m60_files:
                    print(f"  ✅ โฟลเดอร์ M60 มีเฉพาะไฟล์ M60")
            else:
                print(f"  ❌ ไม่พบโฟลเดอร์ {timeframe}")
    else:
        print(f"❌ ไม่พบโฟลเดอร์ผลลัพธ์: {results_folder}")

def test_symbol_extraction():
    """
    ทดสอบการดึงสัญลักษณ์จาก test_groups
    """
    print("\n🧪 ทดสอบการดึงสัญลักษณ์")
    print("=" * 50)
    
    # จำลอง test_groups
    test_groups = {
        "M30": [
            "CSV_Files_Fixed/AUDUSD_M30_FIXED.csv",
            "CSV_Files_Fixed/EURGBP_M30_FIXED.csv",
            "CSV_Files_Fixed/EURUSD_M30_FIXED.csv",
            "CSV_Files_Fixed/GBPUSD_M30_FIXED.csv",
            "CSV_Files_Fixed/GOLD_M30_FIXED.csv",
            "CSV_Files_Fixed/NZDUSD_M30_FIXED.csv",
            "CSV_Files_Fixed/USDCAD_M30_FIXED.csv",
            "CSV_Files_Fixed/USDJPY_M30_FIXED.csv"
        ],
        "M60": [
            "CSV_Files_Fixed/AUDUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/EURGBP_H1_FIXED.csv",
            "CSV_Files_Fixed/EURUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/GOLD_H1_FIXED.csv",
            "CSV_Files_Fixed/NZDUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/USDCAD_H1_FIXED.csv",
            "CSV_Files_Fixed/USDJPY_H1_FIXED.csv"
        ]
    }
    
    # ฟังก์ชันจำลอง parse_filename
    def parse_filename(file_path):
        filename = file_path.split('/')[-1]
        if '_M30_' in filename:
            symbol = filename.split('_M30_')[0]
            timeframe = 30
        elif '_H1_' in filename:
            symbol = filename.split('_H1_')[0]
            timeframe = 60
        else:
            raise ValueError(f"ไม่สามารถแยกข้อมูลจาก {filename}")
        
        return {
            "Name_Currency": symbol,
            "Timeframe_Currency": timeframe
        }
    
    symbols_from_files = []
    
    # ดึงสัญลักษณ์จาก test_groups
    for timeframe, files in test_groups.items():
        print(f"\n📊 ประมวลผล {timeframe}:")
        for file_path in files:
            try:
                info = parse_filename(file_path)
                symbol = info["Name_Currency"]
                if symbol not in symbols_from_files:
                    symbols_from_files.append(symbol)
                print(f"  ✅ {file_path} -> {symbol}")
            except Exception as e:
                print(f"  ❌ {file_path} -> Error: {e}")
    
    print(f"\n🔍 สัญลักษณ์ที่ดึงได้ทั้งหมด: {symbols_from_files}")
    print(f"📊 จำนวนสัญลักษณ์: {len(symbols_from_files)}")
    
    # ทดสอบการสร้างรายการแนะนำ
    print(f"\n🎯 ทดสอบการสร้างรายการแนะนำ:")
    for day_idx in range(5):  # วันจันทร์-ศุกร์
        if symbols_from_files:
            num_symbols = min(3, len(symbols_from_files))
            start_idx = day_idx % len(symbols_from_files)
            recommended_symbols = []
            for i in range(num_symbols):
                idx = (start_idx + i) % len(symbols_from_files)
                recommended_symbols.append(symbols_from_files[idx])
            print(f"  วันที่ {day_idx + 1}: {recommended_symbols}")
        else:
            print(f"  วันที่ {day_idx + 1}: ['GOLD', 'USDJPY'] (fallback)")

def test_daily_schedule_file():
    """
    ตรวจสอบไฟล์ daily trading schedule
    """
    print("\n🧪 ตรวจสอบไฟล์ Daily Trading Schedule")
    print("=" * 50)
    
    schedule_file = "Test_LightGBM/results/daily_trading_schedule_summary.txt"
    
    if os.path.exists(schedule_file):
        print(f"📄 พบไฟล์: {schedule_file}")
        
        with open(schedule_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📝 เนื้อหาไฟล์:")
        print("-" * 30)
        print(content[:1000])  # แสดง 1000 ตัวอักษรแรก
        print("-" * 30)
        
        # ตรวจสอบสัญลักษณ์ที่แสดง
        if "AUDUSD EURGBP" in content:
            print("⚠️ พบปัญหา: แสดงเฉพาะ 'AUDUSD EURGBP'")
        
        if "GOLD" in content and "USDJPY" in content:
            print("✅ พบสัญลักษณ์ที่คาดหวัง: GOLD, USDJPY")
            
    else:
        print(f"❌ ไม่พบไฟล์: {schedule_file}")

if __name__ == "__main__":
    print("🔧 ทดสอบการแก้ไขปัญหา Timeframe และ Symbol")
    print("=" * 60)
    
    test_timeframe_separation()
    test_symbol_extraction()
    test_daily_schedule_file()
    
    print("\n✅ การทดสอบเสร็จสิ้น")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
วิเคราะห์ผลลัพธ์การทดสอบ 2 symbols ที่มีปัญหาสุด
"""

import pandas as pd
import numpy as np

def analyze_results():
    """วิเคราะห์ผลลัพธ์การทดสอบ"""
    print("📊 วิเคราะห์ผลลัพธ์การทดสอบ F1 Score Improvements")
    print("=" * 80)
    
    # ผลลัพธ์เดิม (จากการรัน 10 rounds)
    old_results = {
        'NZDUSD M30': {
            'AUC': 0.862,
            'F1': 0.621,
            'CV_AUC': 0.500,
            'Accuracy': 0.913  # ประมาณการ
        },
        'USDJPY M30': {
            'AUC': 0.847,
            'F1': 0.410,
            'CV_AUC': 0.850,
            'Accuracy': 0.846  # ประมาณการ
        }
    }
    
    # ผลลัพธ์ใหม่ (จากการรัน 1 round ด้วย F1 improvements)
    new_results = {
        'NZDUSD M30': {
            'AUC': 0.889,
            'F1': 0.624,
            'CV_AUC': 0.500,
            'Accuracy': 0.914
        },
        'USDJPY M30': {
            'AUC': 0.842,
            'F1': 0.375,
            'CV_AUC': 0.800,
            'Accuracy': 0.405
        }
    }
    
    # เกณฑ์ความสำเร็จ
    success_criteria = {
        'NZDUSD M30': {
            'critical': {'CV_AUC': 0.75},
            'good': {'F1': 0.65},
            'excellent': {'AUC': 0.87}
        },
        'USDJPY M30': {
            'critical': {'F1': 0.55},
            'good': {'F1': 0.60},
            'excellent': {'F1': 0.65, 'AUC': 0.86}
        }
    }
    
    print("🔍 เปรียบเทียบผลลัพธ์:")
    print("=" * 60)
    
    total_success = 0
    total_criteria = 0
    
    for symbol in ['NZDUSD M30', 'USDJPY M30']:
        print(f"\n📊 {symbol}:")
        print("-" * 40)
        
        old = old_results[symbol]
        new = new_results[symbol]
        criteria = success_criteria[symbol]
        
        # แสดงการเปรียบเทียบ
        for metric in ['AUC', 'F1', 'CV_AUC', 'Accuracy']:
            old_val = old[metric]
            new_val = new[metric]
            change = ((new_val - old_val) / old_val) * 100
            
            if change > 0:
                status = "✅ ดีขึ้น"
                arrow = "↗️"
            elif change < -1:
                status = "❌ แย่ลง"
                arrow = "↘️"
            else:
                status = "➖ เท่าเดิม"
                arrow = "➡️"
            
            print(f"   {metric}: {old_val:.3f} → {new_val:.3f} ({change:+.1f}%) {arrow} {status}")
        
        # ตรวจสอบเกณฑ์ความสำเร็จ
        print(f"\n🎯 เกณฑ์ความสำเร็จ:")
        
        # Critical
        critical_passed = True
        for metric, target in criteria['critical'].items():
            current = new[metric]
            passed = current >= target
            total_criteria += 1
            if passed:
                total_success += 1
                print(f"   🔥 Critical {metric}: {current:.3f} >= {target:.3f} ✅")
            else:
                critical_passed = False
                print(f"   🔥 Critical {metric}: {current:.3f} < {target:.3f} ❌")
        
        # Good
        for metric, target in criteria['good'].items():
            current = new[metric]
            passed = current >= target
            if passed:
                print(f"   ✅ Good {metric}: {current:.3f} >= {target:.3f} ✅")
            else:
                print(f"   ✅ Good {metric}: {current:.3f} < {target:.3f} ❌")
        
        # Excellent
        excellent_passed = True
        for metric, target in criteria['excellent'].items():
            current = new[metric]
            passed = current >= target
            if passed:
                print(f"   🎉 Excellent {metric}: {current:.3f} >= {target:.3f} ✅")
            else:
                excellent_passed = False
                print(f"   🎉 Excellent {metric}: {current:.3f} < {target:.3f} ❌")
        
        # สรุปสถานะ
        if critical_passed:
            print(f"   📊 สถานะ: ✅ ผ่านเกณฑ์ Critical")
        else:
            print(f"   📊 สถานะ: ❌ ไม่ผ่านเกณฑ์ Critical")

def analyze_problems():
    """วิเคราะห์ปัญหาที่พบ"""
    print(f"\n🔍 วิเคราะห์ปัญหาที่พบ")
    print("=" * 60)
    
    problems = [
        {
            "symbol": "NZDUSD M30",
            "issue": "CV_AUC ยังคงเป็น 0.500",
            "cause": "ปัญหาโครงสร้างข้อมูลหรือ data leakage",
            "solution": "ตรวจสอบ CSV format และ feature engineering"
        },
        {
            "symbol": "USDJPY M30", 
            "issue": "F1 Score แย่ลงจาก 0.410 → 0.375",
            "cause": "Class weight หรือ threshold ไม่เหมาะสม",
            "solution": "ปรับ class weight และ threshold optimization"
        },
        {
            "symbol": "USDJPY M30",
            "issue": "Accuracy ต่ำมาก (0.405)",
            "cause": "โมเดลไม่สามารถเรียนรู้ pattern ได้ดี",
            "solution": "เพิ่ม feature engineering และปรับ parameters"
        }
    ]
    
    for i, problem in enumerate(problems, 1):
        print(f"{i}. {problem['symbol']}")
        print(f"   ❌ ปัญหา: {problem['issue']}")
        print(f"   🔍 สาเหตุ: {problem['cause']}")
        print(f"   💡 แนวทางแก้ไข: {problem['solution']}")
        print()

def generate_recommendations():
    """สร้างคำแนะนำ"""
    print("🎯 คำแนะนำการดำเนินการต่อ")
    print("=" * 60)
    
    success_rate = 0  # 0/2 symbols ผ่าน critical criteria
    
    print(f"📊 ผลการประเมิน: {success_rate}/2 symbols ผ่านเกณฑ์ Critical (0%)")
    
    print(f"\n❌ สถานะ: การปรับปรุงยังไม่เพียงพอ")
    print(f"🔧 จำเป็นต้องปรับปรุงเพิ่มเติมก่อนรัน full training")
    
    print(f"\n📋 แผนการปรับปรุงเพิ่มเติม:")
    
    improvements = [
        {
            "priority": "🔥 สูงสุด",
            "action": "แก้ไข NZDUSD M30 CV_AUC = 0.5",
            "method": "ตรวจสอบ data quality และ feature leakage",
            "time": "30-60 นาที"
        },
        {
            "priority": "🔥 สูง",
            "action": "ปรับปรุง USDJPY M30 F1 Score",
            "method": "เพิ่ม class weight และ threshold optimization",
            "time": "20-30 นาที"
        },
        {
            "priority": "📊 ปานกลาง",
            "action": "ทดสอบ Advanced Features",
            "method": "เพิ่ม technical indicators และ interaction features",
            "time": "45-60 นาที"
        },
        {
            "priority": "⚙️ ต่ำ",
            "action": "Fine-tune Parameters",
            "method": "ปรับ learning rate และ regularization",
            "time": "30-45 นาที"
        }
    ]
    
    for i, imp in enumerate(improvements, 1):
        print(f"{i}. {imp['priority']} - {imp['action']}")
        print(f"   วิธีการ: {imp['method']}")
        print(f"   เวลา: {imp['time']}")
        print()
    
    print(f"⏱️ เวลารวมที่คาดการณ์: 2-3 ชั่วโมง")
    print(f"🎯 เป้าหมาย: ให้ 2/2 symbols ผ่านเกณฑ์ Critical")

def create_next_steps():
    """สร้างขั้นตอนถัดไป"""
    print(f"\n🚀 ขั้นตอนถัดไป (เรียงตามลำดับความสำคัญ)")
    print("=" * 60)
    
    steps = [
        {
            "step": 1,
            "title": "แก้ไข NZDUSD M30 Critical Issue",
            "description": "ตรวจสอบและแก้ไข CV_AUC = 0.5",
            "commands": [
                "python fix_critical_issues.py",
                "ตรวจสอบ CSV format",
                "ทดสอบ data preprocessing"
            ]
        },
        {
            "step": 2,
            "title": "ปรับปรุง USDJPY M30 F1 Score",
            "description": "เพิ่ม class weight และ threshold optimization",
            "commands": [
                "ปรับ class weight ratio เป็น 10:1",
                "ทดสอบ threshold optimization",
                "ปรับ min_data_in_leaf เป็น 15"
            ]
        },
        {
            "step": 3,
            "title": "ทดสอบการปรับปรุงใหม่",
            "description": "รัน quick test กับ 2 symbols",
            "commands": [
                "python test_single_symbol.py",
                "ตรวจสอบผลลัพธ์",
                "เปรียบเทียบกับเกณฑ์"
            ]
        },
        {
            "step": 4,
            "title": "รัน Full Training (ถ้าผ่านเกณฑ์)",
            "description": "รัน training ทั้งหมด 8 symbols × 2 timeframes",
            "commands": [
                "เปลี่ยน NUM_TRAINING_ROUNDS = 10",
                "python python_LightGBM_15_Tuning.py",
                "รอผลลัพธ์ 3-4 ชั่วโมง"
            ]
        }
    ]
    
    for step in steps:
        print(f"Step {step['step']}: {step['title']}")
        print(f"   📝 {step['description']}")
        print(f"   💻 คำสั่ง:")
        for cmd in step['commands']:
            print(f"      • {cmd}")
        print()

def main():
    """ฟังก์ชันหลัก"""
    print("🧪 วิเคราะห์ผลลัพธ์การทดสอบ F1 Score Improvements")
    print("=" * 80)
    
    # 1. วิเคราะห์ผลลัพธ์
    analyze_results()
    
    # 2. วิเคราะห์ปัญหา
    analyze_problems()
    
    # 3. สร้างคำแนะนำ
    generate_recommendations()
    
    # 4. สร้างขั้นตอนถัดไป
    create_next_steps()
    
    # 5. สรุป
    print("🎉 สรุปการวิเคราะห์")
    print("=" * 80)
    
    print("✅ ผลลัพธ์ที่ดี:")
    print("   • NZDUSD M30: AUC ดีขึ้น 2.7% (0.862 → 0.889)")
    print("   • NZDUSD M30: F1 ดีขึ้นเล็กน้อย (0.621 → 0.624)")
    
    print(f"\n❌ ปัญหาที่ต้องแก้ไข:")
    print("   • NZDUSD M30: CV_AUC ยังคงเป็น 0.5 (Critical)")
    print("   • USDJPY M30: F1 แย่ลง 8.5% (0.410 → 0.375)")
    print("   • USDJPY M30: Accuracy ต่ำมาก (0.405)")
    
    print(f"\n🎯 ข้อสรุป:")
    print("   • การปรับปรุง F1 Score ยังไม่เพียงพอ")
    print("   • ต้องแก้ไขปัญหาเฉพาะ symbols ก่อน")
    print("   • ไม่แนะนำให้รัน full training ในขณะนี้")
    
    print(f"\n🚀 ขั้นตอนถัดไป:")
    print("   1. แก้ไข NZDUSD M30 CV_AUC = 0.5")
    print("   2. ปรับปรุง USDJPY M30 F1 Score")
    print("   3. ทดสอบใหม่กับ 2 symbols")
    print("   4. รัน full training เมื่อผ่านเกณฑ์")

if __name__ == "__main__":
    main()

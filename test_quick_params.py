#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบ LightGBM Parameters แบบเร็ว
"""

import numpy as np
import pandas as pd
import lightgbm as lgb
from sklearn.model_selection import RandomizedSearchCV, TimeSeriesSplit

# ทดสอบการสร้าง LightGBM Classifier ด้วย parameters ที่แก้ไขแล้ว
def test_lgbm_params():
    """ทดสอบการสร้าง LightGBM parameters"""
    
    print("🧪 ทดสอบ LightGBM Parameters")
    print("="*50)
    
    # สร้างข้อมูลทดสอบ
    np.random.seed(42)
    X = np.random.randn(1000, 10)
    y = np.random.randint(0, 5, 1000)  # 5 classes สำหรับ multi-class
    
    print(f"📊 ข้อมูลทดสอบ:")
    print(f"  - X shape: {X.shape}")
    print(f"  - y shape: {y.shape}")
    print(f"  - Unique classes: {np.unique(y)}")
    print(f"  - Class counts: {np.bincount(y)}")
    
    # ทดสอบ get_lgbm_params function
    try:
        # Import function จากไฟล์หลัก
        import sys
        sys.path.append('.')
        
        # ลองโหลดฟังก์ชัน
        exec(open('python_LightGBM_15_Tuning.py').read(), globals())
        
        print(f"\n🔧 ทดสอบ get_lgbm_params()...")
        params = get_lgbm_params(y=y, use_scale_pos_weight=True)
        
        print(f"✅ Parameters ที่ได้:")
        for key, value in params.items():
            print(f"  - {key}: {value}")
        
        # ทดสอบสร้าง LightGBM Classifier
        print(f"\n🔧 ทดสอบสร้าง LGBMClassifier...")
        
        # แยก random_state ออกจาก params
        tuning_params = params.copy()
        tuning_params.pop('random_state', None)
        
        lgb_estimator = lgb.LGBMClassifier(
            **tuning_params,
            n_estimators=100,  # ลดลงเพื่อทดสอบเร็ว
            random_state=42
        )
        
        print(f"✅ สร้าง LGBMClassifier สำเร็จ!")
        print(f"  - Objective: {lgb_estimator.objective}")
        print(f"  - Num_class: {getattr(lgb_estimator, 'num_class', 'N/A')}")
        
        # ทดสอบ fit
        print(f"\n🔧 ทดสอบ fit โมเดล...")
        lgb_estimator.fit(X, y)
        print(f"✅ Fit โมเดลสำเร็จ!")
        
        # ทดสอบ predict
        print(f"\n🔧 ทดสอบ predict...")
        predictions = lgb_estimator.predict(X[:10])
        print(f"✅ Predict สำเร็จ!")
        print(f"  - Predictions: {predictions}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_randomized_search():
    """ทดสอบ RandomizedSearchCV"""
    
    print(f"\n🧪 ทดสอบ RandomizedSearchCV")
    print("="*50)
    
    try:
        # สร้างข้อมูลทดสอบ
        np.random.seed(42)
        X = np.random.randn(500, 5)
        y = np.random.randint(0, 3, 500)  # 3 classes
        
        print(f"📊 ข้อมูลทดสอบ:")
        print(f"  - X shape: {X.shape}")
        print(f"  - y shape: {y.shape}")
        print(f"  - Unique classes: {np.unique(y)}")
        
        # สร้าง parameters
        params = {
            'objective': 'multiclass',
            'num_class': 3,
            'metric': ['multi_logloss'],
            'boosting_type': 'gbdt',
            'learning_rate': 0.1,
            'num_leaves': 10,
            'verbosity': -1
        }
        
        # แยก random_state ออก
        tuning_params = params.copy()
        tuning_params.pop('random_state', None)
        
        lgb_estimator = lgb.LGBMClassifier(
            **tuning_params,
            n_estimators=50,
            random_state=42
        )
        
        # ทดสอบ RandomizedSearchCV
        param_dist = {
            'learning_rate': [0.05, 0.1, 0.15],
            'num_leaves': [5, 10, 15]
        }
        
        search = RandomizedSearchCV(
            lgb_estimator,
            param_distributions=param_dist,
            n_iter=3,  # ลดลงเพื่อทดสอบเร็ว
            scoring='f1_macro',
            cv=TimeSeriesSplit(n_splits=3),
            verbose=1,
            random_state=42,
            n_jobs=1  # ใช้ 1 job เพื่อหลีกเลี่ยงปัญหา
        )
        
        print(f"\n🔧 เริ่ม RandomizedSearchCV...")
        search.fit(X, y)
        
        print(f"✅ RandomizedSearchCV สำเร็จ!")
        print(f"  - Best score: {search.best_score_:.4f}")
        print(f"  - Best params: {search.best_params_}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดใน RandomizedSearchCV: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """ฟังก์ชันหลัก"""
    
    print("🧪 ทดสอบ LightGBM Parameters แบบเร็ว")
    print("="*60)
    
    # ทดสอบ parameters
    params_success = test_lgbm_params()
    
    # ทดสอบ RandomizedSearchCV
    search_success = test_randomized_search()
    
    print(f"\n🏁 สรุปผลการทดสอบ")
    print("="*60)
    
    if params_success and search_success:
        print("✅ การทดสอบทั้งหมดสำเร็จ!")
        print("💡 การแก้ไข duplicate parameters ทำงานได้:")
        print("  1. ✅ get_lgbm_params() ทำงานได้")
        print("  2. ✅ LGBMClassifier สร้างได้")
        print("  3. ✅ RandomizedSearchCV ทำงานได้")
        print("  4. ✅ ไม่มี duplicate parameter errors")
    else:
        print("❌ การทดสอบล้มเหลว!")
        if not params_success:
            print("  - ปัญหาใน get_lgbm_params() หรือ LGBMClassifier")
        if not search_success:
            print("  - ปัญหาใน RandomizedSearchCV")
    
    return params_success and search_success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)

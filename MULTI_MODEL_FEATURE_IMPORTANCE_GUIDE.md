# คู่มือการใช้งาน Feature Importance ใน Multi-Model Architecture

## 📋 สรุปการตรวจสอบ

### ✅ โครงสร้างไฟล์ที่พบ

```
LightGBM_Multi/
├─ feature_importance/
│   └─ 060_must_have_features.pkl (4 features)
└─ results/
    ├─ M60/
    │   ├─ 060_AUDUSD_feature_importance.csv (216 features)
    │   ├─ 060_GOLD_feature_importance.csv (216 features)
    │   ├─ 060_USDJPY_feature_importance.csv (216 features)
    │   └─ *_feature_importance_comparison.csv
    ├─ trend_following/
    │   └─ *_feature_importance_comparison.csv (3 files)
    └─ counter_trend/
        └─ *_feature_importance_comparison.csv (3 files)
```

### 🔍 ปัญหาที่พบ

1. **ไม่มีไฟล์ feature_importance.csv ใน scenario folders**
   - `trend_following/` และ `counter_trend/` มีเฉพาะ comparison files
   - ไม่มี individual feature importance files สำหรับแต่ละ scenario

2. **analyze_cross_asset_feature_importance() ใช้งานได้เฉพาะ M60 folder**
   - ฟังก์ชันหา feature importance files ใน `importance_files_dir`
   - ปัจจุบันมีไฟล์ครบใน M60 folder เท่านั้น

## 🛠️ การแก้ไขและปรับปรุง

### 1. แก้ไขการสร้าง Feature Importance ใน Multi-Model Architecture

ปัญหา: ใน scenario training ไม่มีการสร้าง individual feature importance files

**แก้ไข:**
```python
# ใน train_scenario_model() ต้องเพิ่มการสร้าง feature importance file
def train_scenario_model(scenario_name, scenario_config, X, y, symbol, timeframe):
    # ... existing training code ...
    
    # เพิ่มการสร้าง Feature Importance สำหรับ scenario
    scenario_results_folder = f"{test_folder}/results/{scenario_name}"
    os.makedirs(scenario_results_folder, exist_ok=True)
    
    # สร้าง individual feature importance file
    importance_df = plot_feature_importance(
        model=model,
        features=list(X.columns),
        model_name=f"{scenario_name}_{symbol}_{timeframe}",
        symbol=symbol,
        timeframe=timeframe,
        output_folder=scenario_results_folder
    )
    
    return {
        'model': model,
        'feature_importance': importance_df,
        # ... other results
    }
```

### 2. ปรับปรุงการเรียกใช้ analyze_cross_asset_feature_importance()

**ปัจจุบัน:**
```python
# ใช้ M60 folder เท่านั้น
importance_results_directory = os.path.join(f'{test_folder}', 'results', 'M60')
```

**ควรปรับเป็น:**
```python
# ใช้ scenario-specific folder หรือ combined approach
if USE_MULTI_MODEL_ARCHITECTURE:
    # วิเคราะห์แยกตาม scenario
    for scenario_name in MARKET_SCENARIOS.keys():
        scenario_importance_dir = os.path.join(f'{test_folder}', 'results', scenario_name)
        scenario_pickle_file = os.path.join(feature_importance_analysis_dir, 
                                          f'{str(timeframe).zfill(3)}_{scenario_name}_must_have_features.pkl')
        
        analyze_cross_asset_feature_importance(
            input_files=input_files,
            importance_files_dir=scenario_importance_dir,
            pickle_output_path=scenario_pickle_file,
            num_top_features_per_asset=15,
            min_assets_threshold=2,
            overall_top_n=8
        )
else:
    # ใช้ M60 folder แบบเดิม
    importance_results_directory = os.path.join(f'{test_folder}', 'results', 'M60')
```

### 3. การใช้งาน must_have_features.pkl

**ปัจจุบัน:**
```python
# โหลด must_have_features
must_have_pickle_path = os.path.join(f'{test_folder}', 'feature_importance', 
                                   f'{str(timeframe).zfill(3)}_must_have_features.pkl')

if os.path.exists(must_have_pickle_path):
    with open(must_have_pickle_path, 'rb') as f:
        must_have_features_in_model = pickle.load(f)
```

**ผลลัพธ์ปัจจุบัน:**
- 4 features: ['Target', 'RSI_ROC_i8', 'MACD_line_x_PriceMove', 'Volume_Change_2']
- ครอบคลุม 2-3 assets ต่อ feature
- ความสำคัญเฉลี่ยสูงสุดคือ Target (0.2284)

## 📊 การวิเคราะห์ Feature Importance ปัจจุบัน

### Top Features จาก Cross-Asset Analysis:

| Rank | Feature | Asset Count | Avg Importance | Symbols |
|------|---------|-------------|----------------|---------|
| 1 | Target | 2 | 0.2284 | GOLD, USDJPY |
| 2 | RSI_ROC_i8 | 2 | 0.0776 | AUDUSD, USDJPY |
| 3 | MACD_line_x_PriceMove | 2 | 0.0211 | GOLD, USDJPY |
| 4 | Volume_Change_2 | 2 | 0.0171 | GOLD, USDJPY |

### Asset-Specific Top Features:

**AUDUSD:**
1. RSI_ROC_i8 (0.1552)
2. Low_Lag_20 (0.1149)
3. Bar_longwick (0.1098)
4. BB_width_Lag_2 (0.1002)
5. RSI_ROC_i6 (0.0930)

**GOLD:**
1. Target (0.2568)
2. Volume_Change_5 (0.0329)
3. STOCHk_14_3_3_Lag_5 (0.0328)
4. Volume_Lag_10 (0.0327)
5. STOCHk_14_3_3_Lag_1 (0.0324)

**USDJPY:**
1. Target (0.2000)
2. Volume_Lag_30 (0.0422)
3. Ratio_Buy (0.0422)
4. MACD_line_x_PriceMove (0.0422)
5. Close_Std_5 (0.0422)

## 🚀 ขั้นตอนการใช้งาน

### 1. การเทรนโมเดลและสร้าง Feature Importance

```python
# รัน Multi-Model Architecture training
USE_MULTI_MODEL_ARCHITECTURE = True
python python_LightGBM_16_Signal.py
```

### 2. การโหลด must_have_features สำหรับการใช้งาน

```python
import pickle
import os

def load_must_have_features(timeframe, test_folder="LightGBM_Multi"):
    """โหลด must_have_features สำหรับ timeframe ที่กำหนด"""
    pickle_path = os.path.join(test_folder, 'feature_importance', 
                              f'{str(timeframe).zfill(3)}_must_have_features.pkl')
    
    if os.path.exists(pickle_path):
        with open(pickle_path, 'rb') as f:
            features = pickle.load(f)
        print(f"✅ โหลด {len(features)} must-have features สำหรับ M{timeframe}")
        return features
    else:
        print(f"⚠️ ไม่พบไฟล์ {pickle_path}")
        return []

# ตัวอย่างการใช้งาน
must_have_features = load_must_have_features(60)
print("Must-have features:", must_have_features)
```

### 3. การใช้ must_have_features ในการเทรนโมเดลใหม่

```python
def filter_features_by_must_have(df, must_have_features):
    """กรอง DataFrame ให้เหลือเฉพาะ must-have features"""
    available_features = [f for f in must_have_features if f in df.columns]
    missing_features = [f for f in must_have_features if f not in df.columns]
    
    if missing_features:
        print(f"⚠️ ไม่พบ features: {missing_features}")
    
    print(f"✅ ใช้ {len(available_features)} must-have features")
    return df[available_features]

# ตัวอย่างการใช้งาน
filtered_df = filter_features_by_must_have(df, must_have_features)
```

### 4. การอัปเดต must_have_features

```python
# รันการวิเคราะห์ใหม่เมื่อมีข้อมูลเพิ่ม
def update_must_have_features(input_files, timeframe, test_folder="LightGBM_Multi"):
    """อัปเดต must_have_features จากข้อมูลใหม่"""
    
    importance_files_dir = os.path.join(test_folder, 'results', 'M60')
    pickle_output_path = os.path.join(test_folder, 'feature_importance', 
                                    f'{str(timeframe).zfill(3)}_must_have_features.pkl')
    
    # รันการวิเคราะห์ใหม่
    from python_LightGBM_16_Signal import analyze_cross_asset_feature_importance
    
    new_features = analyze_cross_asset_feature_importance(
        input_files=input_files,
        importance_files_dir=importance_files_dir,
        pickle_output_path=pickle_output_path,
        num_top_features_per_asset=15,
        min_assets_threshold=2,
        overall_top_n=8
    )
    
    return new_features
```

## 📝 สรุปและข้อแนะนำ

### ✅ สิ่งที่ทำงานได้ดี:
1. **Cross-Asset Analysis ทำงานได้ปกติ** จากไฟล์ใน M60 folder
2. **must_have_features.pkl สร้างและใช้งานได้** (4 features ที่มีคุณภาพ)
3. **Feature Importance files ครบถ้วน** สำหรับ M60 scenario

### ⚠️ สิ่งที่ต้องปรับปรุง:
1. **เพิ่มการสร้าง individual feature importance files** ใน scenario folders
2. **ปรับปรุงการเรียกใช้ analyze_cross_asset_feature_importance()** ให้รองรับ multi-scenario
3. **พิจารณาสร้าง scenario-specific must_have_features** สำหรับแต่ละ strategy

### 🎯 การใช้งานที่แนะนำ:
1. **ใช้ must_have_features ปัจจุบัน** สำหรับการเทรนโมเดลใหม่
2. **ตรวจสอบ feature availability** ก่อนใช้งาน
3. **อัปเดต must_have_features** เมื่อมีข้อมูลใหม่หรือเปลี่ยน strategy
4. **ใช้ scenario-specific analysis** เมื่อต้องการ features เฉพาะสำหรับแต่ละ strategy

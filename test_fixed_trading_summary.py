#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขการคำนวณสถิติการเทรดในไฟล์จริง
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

def test_fixed_calculation():
    """ทดสอบการแก้ไขการคำนวณ"""
    
    print("🧪 ทดสอบการแก้ไขการคำนวณสถิติการเทรดในไฟล์จริง")
    print("="*70)
    
    # ตั้งค่าการทดสอบ
    symbol = "GOLD"
    timeframe = "H1"
    
    print(f"📋 การตั้งค่าการทดสอบ:")
    print(f"  Symbol: {symbol}")
    print(f"  Timeframe: {timeframe}")
    
    # ตรวจสอบไฟล์ข้อมูล
    csv_file = f"CSV_Files_Fixed/{symbol}_{timeframe}_FIXED.csv"
    if not os.path.exists(csv_file):
        print(f"❌ ไม่พบไฟล์: {csv_file}")
        return False
    
    file_size = os.path.getsize(csv_file)
    print(f"  File: {csv_file}")
    print(f"  File Size: {file_size:,} bytes")
    
    try:
        # เรียกใช้โค้ดหลักแบบจำกัดเวลา
        print(f"\n🚀 เริ่มการทดสอบการเทรน...")
        print(f"⏰ เริ่มเมื่อ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Import และเรียกใช้ฟังก์ชันหลัก
        sys.path.append('.')
        
        # เปลี่ยนการตั้งค่าให้เทรนเร็วขึ้น
        os.environ['QUICK_TEST'] = '1'
        
        # เรียกใช้โค้ดหลัก
        import subprocess
        result = subprocess.run([
            'python', 'python_LightGBM_15_Tuning.py'
        ], capture_output=True, text=True, timeout=300, encoding='utf-8', errors='ignore')
        
        print(f"⏰ เสร็จเมื่อ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        if result.returncode == 0:
            print(f"✅ การเทรนเสร็จสิ้น!")
        else:
            print(f"⚠️ การเทรนมีปัญหา (return code: {result.returncode})")
            if result.stderr:
                print(f"Error: {result.stderr[:500]}...")
        
        # ตรวจสอบไฟล์ผลลัพธ์
        print(f"\n🔍 ตรวจสอบไฟล์ผลลัพธ์...")
        
        # หาไฟล์ trading summary ล่าสุด
        summary_files = []
        for file in os.listdir('.'):
            if file.endswith('_trading_summary.txt') and 'GOLD' in file:
                summary_files.append(file)
        
        if not summary_files:
            print(f"❌ ไม่พบไฟล์ trading summary")
            return False
        
        # เรียงตามเวลาแก้ไขล่าสุด
        summary_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        latest_file = summary_files[0]
        
        print(f"📁 ไฟล์ล่าสุด: {latest_file}")
        print(f"📅 แก้ไขล่าสุด: {datetime.fromtimestamp(os.path.getmtime(latest_file)).strftime('%Y-%m-%d %H:%M:%S')}")
        
        # อ่านและวิเคราะห์ไฟล์
        with open(latest_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"\n📊 วิเคราะห์เนื้อหาไฟล์:")
        
        # หาข้อมูลสถิติการเทรด
        lines = content.split('\n')
        trading_stats = {}
        
        for line in lines:
            if 'จำนวนเทรดทั้งหมด:' in line:
                trading_stats['total_trades'] = int(line.split(':')[1].strip())
            elif 'เทรดที่ชนะ:' in line:
                trading_stats['winning_trades'] = int(line.split(':')[1].strip())
            elif 'เทรดที่แพ้:' in line:
                trading_stats['losing_trades'] = int(line.split(':')[1].strip())
            elif 'Win Rate:' in line:
                win_rate_str = line.split(':')[1].strip().replace('%', '')
                trading_stats['win_rate'] = float(win_rate_str)
            elif 'กำไรรวม:' in line:
                trading_stats['total_profit'] = float(line.split(':')[1].strip())
            elif 'กำไรเฉลี่ยต่อเทรด:' in line:
                trading_stats['avg_profit_per_trade'] = float(line.split(':')[1].strip())
            elif 'Expectancy:' in line:
                trading_stats['expectancy'] = float(line.split(':')[1].strip())
        
        # แสดงผลลัพธ์
        print(f"  📈 สถิติการเทรดที่พบ:")
        for key, value in trading_stats.items():
            print(f"    - {key}: {value}")
        
        # ตรวจสอบความถูกต้อง
        print(f"\n🔍 ตรวจสอบความถูกต้อง:")
        
        issues = []
        
        # 1. ตรวจสอบจำนวนเทรด
        if 'total_trades' in trading_stats and 'winning_trades' in trading_stats and 'losing_trades' in trading_stats:
            total = trading_stats['total_trades']
            wins = trading_stats['winning_trades']
            losses = trading_stats['losing_trades']
            
            if wins + losses != total:
                issues.append(f"ผลรวมเทรดไม่ตรง: {wins} + {losses} ≠ {total}")
            else:
                print(f"  ✅ ผลรวมเทรดถูกต้อง: {wins} + {losses} = {total}")
            
            # ตรวจสอบ win rate
            if 'win_rate' in trading_stats:
                calculated_win_rate = (wins / total) * 100 if total > 0 else 0
                reported_win_rate = trading_stats['win_rate']
                
                if abs(calculated_win_rate - reported_win_rate) > 1:
                    issues.append(f"Win Rate ไม่ตรง: คำนวณได้ {calculated_win_rate:.2f}% แต่รายงาน {reported_win_rate:.2f}%")
                else:
                    print(f"  ✅ Win Rate ถูกต้อง: {reported_win_rate:.2f}%")
        
        # 2. ตรวจสอบค่าที่สมเหตุสมผล
        if 'winning_trades' in trading_stats:
            wins = trading_stats['winning_trades']
            if wins > 10000:  # ค่าที่ผิดปกติ
                issues.append(f"จำนวนเทรดที่ชนะผิดปกติ: {wins}")
            else:
                print(f"  ✅ จำนวนเทรดที่ชนะสมเหตุสมผล: {wins}")
        
        if 'losing_trades' in trading_stats:
            losses = trading_stats['losing_trades']
            if losses < 0 or losses > 10000:  # ค่าที่ผิดปกติ
                issues.append(f"จำนวนเทรดที่แพ้ผิดปกติ: {losses}")
            else:
                print(f"  ✅ จำนวนเทรดที่แพ้สมเหตุสมผล: {losses}")
        
        if 'win_rate' in trading_stats:
            win_rate = trading_stats['win_rate']
            if win_rate < 0 or win_rate > 100:
                issues.append(f"Win Rate ผิดปกติ: {win_rate}%")
            else:
                print(f"  ✅ Win Rate อยู่ในช่วงปกติ: {win_rate:.2f}%")
        
        # สรุปผล
        if issues:
            print(f"\n❌ พบปัญหา:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        else:
            print(f"\n✅ การแก้ไขสำเร็จ! ไม่พบปัญหาในการคำนวณ")
            return True
            
    except subprocess.TimeoutExpired:
        print(f"⏰ การทดสอบหมดเวลา (5 นาที)")
        return False
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {str(e)}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    
    print("🧪 ทดสอบการแก้ไขการคำนวณสถิติการเทรด")
    print("="*70)
    
    success = test_fixed_calculation()
    
    print(f"\n🏁 สรุปผลการทดสอบ")
    print("="*70)
    
    if success:
        print("✅ การแก้ไขการคำนวณสถิติการเทรดสำเร็จ!")
        print("💡 ปัญหาที่แก้ไขแล้ว:")
        print("  1. ✅ เทรดที่ชนะ: แสดงจำนวนที่ถูกต้อง (ไม่ใช่ 6427)")
        print("  2. ✅ เทรดที่แพ้: แสดงจำนวนที่ถูกต้อง (ไม่ใช่ -6282)")
        print("  3. ✅ Win Rate: แสดงเปอร์เซ็นต์ปกติ 0-100% (ไม่ใช่ 4433%)")
        print("  4. ✅ กำไรรวม: คำนวณจากข้อมูลจริง")
        print("  5. ✅ กำไรเฉลี่ย: คำนวณจากข้อมูลจริง")
        
        print(f"\n🚀 ระบบพร้อมใช้งาน:")
        print("  - การคำนวณสถิติการเทรดถูกต้อง")
        print("  - ไฟล์ trading summary แสดงผลที่สมเหตุสมผล")
        print("  - ไม่มีค่าผิดปกติในรายงาน")
        
    else:
        print("❌ การแก้ไขยังไม่สมบูรณ์!")
        print("💡 ต้องตรวจสอบเพิ่มเติม:")
        print("  - การคำนวณ winning_trades และ losing_trades")
        print("  - การแปลง win_rate จากเปอร์เซ็นต์เป็นทศนิยม")
        print("  - การคำนวณ total_profit และ avg_profit_per_trade")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)

//+------------------------------------------------------------------+
//|                                              HttpRequestSender.mq5 |
//|                      Sends Bar Data to Python via HTTP WebRequest |\
//+------------------------------------------------------------------+
#property copyright "Satapat Jitchana"
#property link      "Your Link"
#property version   "1.00"
#property strict

// --- INCLUDE HEADERS ที่จำเป็น (สำคัญมากสำหรับการแก้ Error) ---
#include <Trade\Trade.mqh> // *** ต้องมีบรรทัดนี้! *** สำหรับฟังก์ชันจัดการ Position และการซื้อขาย
// อาจมี #include อื่นๆ เช่น <JSON.mqh> ถ้าใช้ JSON Library

// *** กำหนดจำนวนแท่งย้อนหลังที่ Python ต้องการสำหรับคำนวณ Indicator ***
#define BARS_FOR_PYTHON 210 // *** ตั้งค่านี้ให้ตรงกับ required_bars ใน Python Server ***

//--- Input parameters
input string InpPythonServerUrl = "http://127.0.0.1:54321/data"; // URL ของ Python Server ที่จะรับข้อมูล (ใส่ Path ด้วย เช่น /data)
input int    InpUpdateFrequencySec = 1;                          // ความถี่ในการเช็คบาร์ใหม่ (วินาที) - ใช้ Timer
input int    InpWebRequestTimeout = 5;                           // Timeout สำหรับ Web Request (วินาที)

input string TelegramBotToken  = "**********************************************";
input string ChatID            = "6546140292";
string MessageText             = "Hello MT5";

//--- Logging Configuration
input bool   EnableDetailedLogging = true;     // เปิด/ปิด การบันทึก log แบบละเอียด
input bool   EnableFileLogging = true;         // เปิด/ปิด การบันทึกลงไฟล์
input string LogFileName = "MT5_Trading_Log";  // ชื่อไฟล์ log (จะเพิ่ม timestamp อัตโนมัติ)

//--- Time Filters Configuration
input bool   IgnoreTimeFilters = true;        // true = ไม่สนใจ time filters จาก server, false = ใช้ time filters
input bool   ShowTimeFilterStatus = true;     // แสดงสถานะ time filters ใน log

//--- Global variables
double Order_Lot = 0.01;

double Order_Lot_cal = 0.01;
double Order_risk = 0.50;

// Global variables to store received signal details
string received_signal = "";
string received_class = "";
double received_confidence = 0.0;
double received_bar_timestamp = 0.0;
double received_signal_bar_timestamp = 0.0;
string received_response_symbol = "";
string received_response_timeframe_str = "";
double received_entry_price = 0.0;
double received_sl_price = 0.0;
double received_tp_price = 0.0;
double received_best_entry = 0.0;
int received_nBars_SL = 0;
double received_threshold = 0.0;
string received_time_filters = "";
int received_spread = 0;

// *** เพิ่มตัวแปรสำหรับ Multi-Model Analysis ***
string received_market_condition = "";
string received_action_type = "";
string received_scenario_used = "";

// *** เพิ่มตัวแปรสำหรับแสดงผลทั้ง 2 ระบบ ***
double received_trend_following_threshold = 0.5;
int received_trend_following_nbars = 6;
double received_counter_trend_threshold = 0.5;
int received_counter_trend_nbars = 6;

// *** เพิ่มตัวแปรสำหรับ confidence ของทั้ง 2 ระบบ ***
double received_trend_following_buy_confidence = 0.0;
double received_trend_following_sell_confidence = 0.0;
double received_counter_trend_buy_confidence = 0.0;
double received_counter_trend_sell_confidence = 0.0;

// Global variables for price adjustment and trade management
double original_sl_size = 0.0;  // ขนาด SL เดิมจาก Python
double original_tp_size = 0.0;  // ขนาด TP เดิมจาก Python

// Global variables for logging
string log_file_handle = "";
datetime last_log_time = 0;
bool price_adjustment_enabled = true;  // เปิด/ปิดการชดเชยราคา
bool multi_trade_enabled = true;  // เปิด/ปิดการเทรดหลายไม้
bool breakeven_enabled = true;  // เปิด/ปิด Break Even
bool friday_close_enabled = true;  // เปิด/ปิดการปิดไม้วันศุกร์
datetime friday_close_time = D'1970.01.01 21:00';  // เวลาปิดไม้วันศุกร์ (21:00)

datetime G_last_bar_time = 0;
string G_symbol;
ENUM_TIMEFRAMES G_period;
string G_period_str;

// --- Global Constant สำหรับชื่อ Object ที่จะแสดงค่าบน Chart ---
const string DISPLAY_LABEL_NAME = "PythonSignalConfidenceLabel";

//--- Input parameter for EA Version
input double EA_Version = 1.0; // กำหนดค่าเริ่มต้นของเวอร์ชั่น EA

//--- Global variables (Optional, can also be local within a function)
string FXBase;
string FXQuote;
string PostFix;
double FXMG1, FXMG2, FXMG3;
int    FXMagic;

int FXSpread = 0;
int FXSlippage = 0;

double FXPV = 0.0;
double ACContract = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_CONTRACT_SIZE);

//+------------------------------------------------------------------+
//| Logging Functions                                                |
//+------------------------------------------------------------------+
void WriteLog(string message, string log_type = "INFO")
{
   datetime current_time = TimeCurrent();
   string timestamp = TimeToString(current_time, TIME_DATE|TIME_MINUTES|TIME_SECONDS);
   string formatted_message = StringFormat("[%s] %s: %s", timestamp, log_type, message);

   // แสดงใน Expert log
   if(EnableDetailedLogging)
   {
      Print(formatted_message);
   }

   // บันทึกลงไฟล์
   if(EnableFileLogging)
   {
      string filename = LogFileName + "_" + TimeToString(current_time, TIME_DATE) + ".txt";
      filename = StringReplace(filename, ".", "_");
      filename = StringReplace(filename, " ", "_");

      int file_handle = FileOpen(filename, FILE_WRITE|FILE_READ|FILE_TXT|FILE_ANSI);
      if(file_handle != INVALID_HANDLE)
      {
         FileSeek(file_handle, 0, SEEK_END);
         FileWriteString(file_handle, formatted_message + "\r\n");
         FileClose(file_handle);
      }
   }
}

void WriteTradeLog(string action, string signal, double confidence, double entry, double sl, double tp, double lot)
{
   string message = StringFormat("TRADE_%s: Signal=%s, Confidence=%.4f, Entry=%.5f, SL=%.5f, TP=%.5f, Lot=%.2f",
                                action, signal, confidence, entry, sl, tp, lot);
   WriteLog(message, "TRADE");
}

void WriteServerLog(string action, int response_code, string signal, double confidence)
{
   string message = StringFormat("SERVER_%s: HTTP=%d, Signal=%s, Confidence=%.4f",
                                action, response_code, signal, confidence);
   WriteLog(message, "SERVER");
}

void WriteSystemLog(string component, string status, string details = "")
{
   string message = StringFormat("SYSTEM_%s: %s %s", component, status, details);
   WriteLog(message, "SYSTEM");
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Get symbol and period info
   G_symbol = Symbol();
   G_period = Period();
   G_period_str = EnumToString(G_period); // แปลง Timeframe เป็น String

   // เริ่มต้น logging system
   WriteSystemLog("INIT", "STARTING", StringFormat("Symbol=%s, Timeframe=%s, URL=%s", G_symbol, G_period_str, InpPythonServerUrl));

   PrintFormat("Initializing HttpRequestSender for %s %s", G_symbol, G_period_str);
   PrintFormat("Sending data to URL: %s", InpPythonServerUrl);
   
   //--- Setup Base and Quote currencies
   Setup_Base_Quote();
   
   //--- Setup Magic Number components and calculate FXMagic
   Setup_Magic();
   
   GetSpread(_Symbol, PostFix);
   GetPV(FXBase, FXQuote, PostFix);
   Print("symbol ",_Symbol," ACContract ",ACContract," spread ",FXSpread," slippage ",FXSlippage," pv ",FXPV);
   
   //Print("Symbol: ", _Symbol);
   //Print("Base Currency: ", FXBase); //FXBase+FXQuote
   //Print("Quote Currency: ", FXQuote);
   //Print("Postfix: ", PostFix);
   //Print("Current Timeframe Name: ", EnumToString(Period()));
   //Print("EA Version: ", EA_Version);
   //Print("Calculated Magic Number: ", FXMagic);
   Print("Symbol ", _Symbol," Base ", FXBase," Quote ", FXQuote," Timeframe ", EnumToString(Period())," : Version ", EA_Version," Magic ", FXMagic);
   
   //--- ตรวจสอบว่าอนุญาต WebRequest
   //Print("Please ensure 'Allow WebRequests' is checked in EA properties -> Common and terminal options -> Expert Advisors.");
   
   // --- ส่วนสำหรับส่งข้อมูลย้อนหลังในครั้งแรก (เป็น Batch) ---
   MqlRates history_rates[BARS_FOR_PYTHON]; // Array สำหรับเก็บข้อมูลย้อนหลัง N แท่ง
   
   // ดึงข้อมูลย้อนหลัง N แท่งล่าสุดที่ปิดแล้ว (index 0 คือแท่งล่าสุดที่ปิด)
   int count_copied_history = CopyRates(G_symbol, G_period, 0, BARS_FOR_PYTHON, history_rates);

   if(count_copied_history > 0)
   {
      PrintFormat("Successfully copied %d historical bars for initial batch send (%s %s).", count_copied_history, G_symbol, G_period_str);
      
      // --- สร้าง JSON Payload ที่เป็น Array ของ Bar Objects ---
      string json_payload = "{"; // เริ่มต้น JSON Object หลัก
      json_payload += "\"symbol\":\"" + G_symbol + "\",";
      json_payload += "\"timeframe_str\":\"" + G_period_str + "\",";
      json_payload += "\"bars\":["; // เริ่ม Array ของบาร์
      
      // วนลูปสร้าง JSON สำหรับแต่ละแท่งใน history_rates Array (จากเก่าสุดไปใหม่สุด)
      for(int i = BARS_FOR_PYTHON - 1; i >= 0; i--) {
         json_payload += "{";
         json_payload += "\"time\":" + IntegerToString(history_rates[i].time) + ",";
         json_payload += "\"open\":" + DoubleToString(history_rates[i].open, Digits()) + ",";
         json_payload += "\"high\":" + DoubleToString(history_rates[i].high, Digits()) + ",";
         json_payload += "\"low\":" + DoubleToString(history_rates[i].low, Digits()) + ",";
         json_payload += "\"close\":" + DoubleToString(history_rates[i].close, Digits()) + ",";
         json_payload += "\"tick_volume\":" + IntegerToString(history_rates[i].tick_volume);
         json_payload += "}";
         if (i > 0) json_payload += ",";
     }
      
      json_payload += "]"; // ปิด Array ของบาร์
      json_payload += "}"; // ปิด JSON Object หลัก
      
      // --- ส่ง WebRequest เพียงครั้งเดียวสำหรับ Batch นี้ ---
      PrintFormat("Sending initial batch of %d historical bars...", count_copied_history);
      // ใน OnInit นี้เราอาจจะยังไม่ต้องการแสดงค่าบนจอ เพราะยังไม่มี Signal จาก Python
      // ดังนั้น เราแค่เรียก SendHttpRequest โดยไม่สนใจค่า return signal/confidence มากนักในรอบแรก
      // ค่า received_signal และ received_confidence จะถูกตั้งค่าใน SendHttpRequest อยู่ดี
      if (!SendHttpRequest("POST", InpPythonServerUrl, json_payload)) {
        Print("Failed to send initial historical data batch to Python server.");
        // จัดการกรณีส่ง Batch แรกไม่สำเร็จ
        return(INIT_FAILED); // อาจจะให้ Init Failed ไปเลย
      } else {
         PrintFormat("Initial historical data batch sent successfully.");
         // ตั้งค่า G_last_bar_time เป็นเวลาของแท่งล่าสุดที่ส่งไป (คือแท่งแรกใน history_rates ที่ CopyRates คัดลอกมา)
         // ซึ่งคือแท่งล่าสุดที่ปิดไปก่อน Init จะทำงาน
         G_last_bar_time = history_rates[(BARS_FOR_PYTHON - 1)].time; // แท่งล่าสุดที่ปิดแล้วใน Batch แรก
         PrintFormat("OnInit: G_last_bar_time set to %s (%d)", TimeToString(G_last_bar_time, TIME_DATE|TIME_MINUTES|TIME_SECONDS), G_last_bar_time); // เพิ่ม Log เพื่อยืนยันค่า
      }
   } else {
      PrintFormat("Failed to copy historical rates for initial batch send: %d. G_last_bar_time remains 0.", GetLastError()); // เพิ่ม Log กรณีคัดลอกไม่ได้
      return(INIT_FAILED); // Init Failed ถ้าคัดลอกข้อมูลไม่ได้
   }
   // --- จบส่วนส่งข้อมูลย้อนหลัง Batch แรก ---
   
   //--- Set timer for periodic checks (เริ่ม Timer หลังจากส่งข้อมูลย้อนหลัง Batch แรกเสร็จ หรือ Init Failed)
   EventSetTimer(InpUpdateFrequencySec);
   
   Get_Order();
   Print(" Order Buy ",order_count_buy," Sell ",order_count_sell);
   if(order_count_buy>0.0) {Print(" Order Buy ",order_count_buy," : OP ",order_open_buy," SL ",order_stoploss_buy);}
   if(order_count_sell>0.0) {Print(" Order Sell ",order_count_sell," : OP ",order_open_sell," SL ",order_stoploss_sell);}
   
   //Print("symbol ", G_symbol, " signal ", received_signal, " confidence ", received_confidence);
   
   //string current_symbol = _Symbol;
   //int current_timeframe = Period();
   //if(FXBase=="GOLD") {
   //   Print(" current : symbol ",FXBase," timeframe ",EnumToString(Period())," : receive ",received_response_symbol," ",received_response_timeframe_str);
   //   if(FXBase==received_response_symbol) {Print("symbol == received_symbol");}
   //   else                                 {Print("symbol != received_symbol");}
   //}
   //else {
   //   Print(" current : symbol ",FXBase,FXQuote," timeframe ",EnumToString(Period())," : receive ",received_response_symbol," ",received_response_timeframe_str);
   //   if((FXBase+FXQuote)==received_response_symbol) {Print("symbol == received_symbol");}
   //   else                                           {Print("symbol != received_symbol");}
   //}
   //if(EnumToString(Period())==received_response_timeframe_str) {Print("timeframe == received_timeframe");}
   //else                                                        {Print("timeframe != received_timeframe");}
   
   //double balance  = AccountInfoDouble(ACCOUNT_BALANCE);
   //double equity   = AccountInfoDouble(ACCOUNT_EQUITY);
   //double capital  = MathMin(balance, equity);
   //double lot_min  = SymbolInfoDouble(_Symbol,SYMBOL_VOLUME_MIN);
   //double lot_max  = SymbolInfoDouble(_Symbol,SYMBOL_VOLUME_MAX);
   //double lot_step = SymbolInfoDouble(_Symbol,SYMBOL_VOLUME_STEP);
   
   //double stopsize = 100;
   //double lot_cal  = (capital*(Order_risk/100))/stopsize;
   //Order_Lot_cal = NormalizeDouble(MathFloor(MathMin(MathMax(lot_cal,lot_min),lot_max)/lot_step)*lot_step,2);
   //PrintFormat("Balance: %.2f | Equity: %.2f | Capital %.2f | Lot min:max:step %.3f %.3f %.3f | %.3f %.3f", balance, equity, capital, lot_min, lot_max, lot_step, lot_cal, Order_Lot_cal);
      
   //double pricr_bid = SymbolInfoDouble(_Symbol,SYMBOL_BID);
   //double point = SymbolInfoDouble(_Symbol,SYMBOL_POINT);
   //double stops_level = SymbolInfoInteger(_Symbol,SYMBOL_TRADE_STOPS_LEVEL);
   //double digit = SymbolInfoInteger(_Symbol,SYMBOL_DIGITS);
   //Print(" Bid ",DoubleToString(pricr_bid,digit)," Stop ",stops_level," Point ",point," digit ",digit," Stop*Point ",DoubleToString(stops_level*point,digit)," Bid-Stop*Point ",DoubleToString(pricr_bid-stops_level*point,digit));
   
   //if (received_signal == "BUY" && received_confidence > 0.50) {
   //   SendBuyOrder(G_symbol, received_confidence, Order_Lot, FXMagic);
   //}
   //if (received_signal == "SELL" && received_confidence > 0.50) {
   //   SendSellOrder(G_symbol, received_confidence, Order_Lot, FXMagic);
   //}
   
   //double close = iClose(Symbol(), Period(), 1);
   //double ema50 = GetEMA50(Symbol(), Period(), 1);
   //double rsi14 = GetRSI14(Symbol(), Period(), 1);
   //Print(" (i-1) : Close ",DoubleToString(close,Digits())," EMA ",DoubleToString(ema50,Digits()+1)," RSI ",DoubleToString(rsi14,2));
   
   //CloseBuyOrder(_Symbol, FXMagic);
   //CloseSellOrder(_Symbol, FXMagic);

   // --- ตรวจสอบ Objects ที่มีอยู่ก่อนเริ่มทำงาน ---
   Print("🔍 Checking existing objects before EA starts...");
   CheckEAObjects();

    //--- Success
    return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Stop timer
   EventKillTimer();
   Print("🔄 EA Deinitialization started. Reason: ", reason);

   // --- ลบ Comment ที่เกี่ยวข้องกับ EA นี้ ---
   CleanupComments();

   // --- ลบ Objects ที่เกี่ยวข้องกับ Symbol และ Timeframe นี้เท่านั้น ---
   CleanupObjects();

   // --- แสดงสถิติการลบ ---
   Print("✅ EA Cleanup completed successfully.");
   Print("🛑 HttpRequestSender stopped.");
}

//+------------------------------------------------------------------+
//| ฟังก์ชันลบ Comments ที่เกี่ยวข้องกับ EA นี้                        |
//+------------------------------------------------------------------+
void CleanupComments()
{
   Print("🧹 Cleaning up Comments...");

   // ลบ Comment ทั้งหมด (เนื่องจาก Comment() เป็น global function)
   // ตรวจสอบว่ามี Comment อยู่หรือไม่ก่อนลบ
   Comment(""); // ลบ Comment ทั้งหมด

   Print("✅ Comments cleared successfully.");
}

//+------------------------------------------------------------------+
//| ฟังก์ชันลบ Objects ที่เกี่ยวข้องกับ Symbol และ Timeframe นี้เท่านั้น |
//+------------------------------------------------------------------+
void CleanupObjects()
{
   Print("🧹 Cleaning up Objects for Symbol: ", _Symbol, " Timeframe: ", EnumToString(_Period));

   // สร้าง prefix สำหรับ Objects ที่เกี่ยวข้องกับ EA นี้
   string current_symbol = _Symbol;
   string current_timeframe = EnumToString(_Period);
   string magic_str = DoubleToString(FXMagic, 0);

   // รายการ prefix ที่ต้องลบ
   string prefixes_to_delete[];
   ArrayResize(prefixes_to_delete, 4);
   prefixes_to_delete[0] = "text_display_Python_" + current_symbol + "_" + current_timeframe;
   prefixes_to_delete[1] = "text_display_MT5_" + current_symbol + "_" + magic_str;
   prefixes_to_delete[2] = "comment_display_Python_" + current_symbol + "_" + current_timeframe;
   prefixes_to_delete[3] = "label_display_MT5_" + current_symbol + "_" + magic_str;

   int totalObjects = ObjectsTotal(0);
   int deletedCount = 0;
   int checkedCount = 0;

   Print("📊 Total Objects on chart: ", totalObjects);

   // วนลูปจากท้ายไปหน้าเพื่อหลีกเลี่ยงปัญหา index shifting
   for(int i = totalObjects - 1; i >= 0; i--)
   {
      string objectName = ObjectName(0, i);
      checkedCount++;

      // ตรวจสอบว่า Object นี้เป็นของ EA นี้หรือไม่
      bool shouldDelete = false;
      string matchedPrefix = "";

      for(int j = 0; j < ArraySize(prefixes_to_delete); j++)
      {
         if(StringFind(objectName, prefixes_to_delete[j]) == 0) // เริ่มต้นด้วย prefix นี้
         {
            shouldDelete = true;
            matchedPrefix = prefixes_to_delete[j];
            break;
         }
      }

      if(shouldDelete)
      {
         int objectType = (int)ObjectGetInteger(0, objectName, OBJPROP_TYPE);

         if(ObjectDelete(0, objectName))
         {
            deletedCount++;
            Print("🗑️ Deleted Object: ", objectName, " (Type: ", objectType, ", Prefix: ", matchedPrefix, ")");
         }
         else
         {
            Print("❌ Failed to delete Object: ", objectName, " Error: ", GetLastError());
         }
      }
   }

   // แสดงสถิติ
   Print("📊 Cleanup Statistics:");
   Print("   Objects checked: ", checkedCount);
   Print("   Objects deleted: ", deletedCount);
   Print("   Objects remaining: ", ObjectsTotal(0));

   if(deletedCount > 0)
   {
      ChartRedraw(); // สั่งให้ Chart วาดใหม่เพื่อนำ Object ออก
      Print("🔄 Chart redrawn after object cleanup.");
   }

   Print("✅ Object cleanup completed successfully.");
}

//+------------------------------------------------------------------+
//| ฟังก์ชันแสดงรายการ Objects ทั้งหมดบน Chart (สำหรับ Debug)          |
//+------------------------------------------------------------------+
void ListAllObjects()
{
   int totalObjects = ObjectsTotal(0);
   Print("📋 Listing all Objects on chart (Total: ", totalObjects, "):");
   Print("═══════════════════════════════════════════════════════════");

   if(totalObjects == 0)
   {
      Print("   (No objects found)");
      return;
   }

   for(int i = 0; i < totalObjects; i++)
   {
      string objectName = ObjectName(0, i);
      int objectType = (int)ObjectGetInteger(0, objectName, OBJPROP_TYPE);
      string objectTypeStr = GetObjectTypeString(objectType);

      Print(StringFormat("   [%d] %s (Type: %s)", i, objectName, objectTypeStr));
   }
   Print("═══════════════════════════════════════════════════════════");
}

//+------------------------------------------------------------------+
//| ฟังก์ชันแปลง Object Type เป็น String                             |
//+------------------------------------------------------------------+
string GetObjectTypeString(int objectType)
{
   switch(objectType)
   {
      case OBJ_VLINE:        return "Vertical Line";
      case OBJ_HLINE:        return "Horizontal Line";
      case OBJ_TREND:        return "Trend Line";
      case OBJ_TRENDBYANGLE: return "Trend by Angle";
      case OBJ_CYCLES:       return "Cycles";
      case OBJ_CHANNEL:      return "Channel";
      case OBJ_STDDEVCHANNEL: return "Std Dev Channel";
      case OBJ_REGRESSION:   return "Regression";
      case OBJ_PITCHFORK:    return "Pitchfork";
      case OBJ_GANNLINE:     return "Gann Line";
      case OBJ_GANNFAN:      return "Gann Fan";
      case OBJ_GANNGRID:     return "Gann Grid";
      case OBJ_FIBO:         return "Fibonacci";
      case OBJ_FIBOTIMES:    return "Fibonacci Times";
      case OBJ_FIBOFAN:      return "Fibonacci Fan";
      case OBJ_FIBOARC:      return "Fibonacci Arc";
      case OBJ_FIBOCHANNEL:  return "Fibonacci Channel";
      case OBJ_EXPANSION:    return "Expansion";
      case OBJ_ELLIOTWAVE5:  return "Elliott Wave 5";
      case OBJ_ELLIOTWAVE3:  return "Elliott Wave 3";
      case OBJ_RECTANGLE:    return "Rectangle";
      case OBJ_TRIANGLE:     return "Triangle";
      case OBJ_ELLIPSE:      return "Ellipse";
      case OBJ_ARROW_THUMB_UP: return "Thumb Up";
      case OBJ_ARROW_THUMB_DOWN: return "Thumb Down";
      case OBJ_ARROW_UP:     return "Arrow Up";
      case OBJ_ARROW_DOWN:   return "Arrow Down";
      case OBJ_ARROW_STOP:   return "Stop Sign";
      case OBJ_ARROW_CHECK:  return "Check Mark";
      case OBJ_ARROW_LEFT_PRICE: return "Left Price";
      case OBJ_ARROW_RIGHT_PRICE: return "Right Price";
      case OBJ_ARROW_BUY:    return "Buy Arrow";
      case OBJ_ARROW_SELL:   return "Sell Arrow";
      case OBJ_ARROW:        return "Arrow";
      case OBJ_TEXT:         return "Text";
      case OBJ_LABEL:        return "Label";
      case OBJ_BUTTON:       return "Button";
      case OBJ_CHART:        return "Chart";
      case OBJ_BITMAP:       return "Bitmap";
      case OBJ_BITMAP_LABEL: return "Bitmap Label";
      case OBJ_EDIT:         return "Edit";
      case OBJ_EVENT:        return "Event";
      case OBJ_RECTANGLE_LABEL: return "Rectangle Label";
      default:               return "Unknown (" + IntegerToString(objectType) + ")";
   }
}

//+------------------------------------------------------------------+
//| ฟังก์ชันตรวจสอบ Objects ที่เกี่ยวข้องกับ EA นี้                    |
//+------------------------------------------------------------------+
void CheckEAObjects()
{
   string current_symbol = _Symbol;
   string current_timeframe = EnumToString(_Period);
   string magic_str = DoubleToString(FXMagic, 0);

   Print("🔍 Checking EA-related Objects for Symbol: ", current_symbol, " Timeframe: ", current_timeframe);
   Print("═══════════════════════════════════════════════════════════");

   // รายการ prefix ที่ต้องตรวจสอบ
   string prefixes_to_check[];
   ArrayResize(prefixes_to_check, 4);
   prefixes_to_check[0] = "text_display_Python_" + current_symbol + "_" + current_timeframe;
   prefixes_to_check[1] = "text_display_MT5_" + current_symbol + "_" + magic_str;
   prefixes_to_check[2] = "comment_display_Python_" + current_symbol + "_" + current_timeframe;
   prefixes_to_check[3] = "label_display_MT5_" + current_symbol + "_" + magic_str;

   int totalObjects = ObjectsTotal(0);
   int foundCount = 0;

   for(int i = 0; i < totalObjects; i++)
   {
      string objectName = ObjectName(0, i);

      for(int j = 0; j < ArraySize(prefixes_to_check); j++)
      {
         if(StringFind(objectName, prefixes_to_check[j]) == 0)
         {
            int objectType = (int)ObjectGetInteger(0, objectName, OBJPROP_TYPE);
            string objectTypeStr = GetObjectTypeString(objectType);
            foundCount++;

            Print(StringFormat("   ✅ Found: %s (Type: %s)", objectName, objectTypeStr));
            break;
         }
      }
   }

   if(foundCount == 0)
   {
      Print("   (No EA-related objects found)");
   }
   else
   {
      Print("📊 Total EA-related objects found: ", foundCount);
   }
   Print("═══════════════════════════════════════════════════════════");
}

//+------------------------------------------------------------------+
//| ฟังก์ชันทดสอบการลบ Objects (สำหรับ Debug)                        |
//+------------------------------------------------------------------+
void TestCleanup()
{
   Print("🧪 Testing Cleanup Functions...");
   Print("═══════════════════════════════════════════════════════════");

   // แสดง Objects ทั้งหมดก่อนลบ
   Print("📋 Objects before cleanup:");
   ListAllObjects();

   // แสดง EA Objects ที่จะถูกลบ
   Print("🎯 EA Objects to be deleted:");
   CheckEAObjects();

   // ทดสอบการลบ
   Print("🧹 Testing cleanup process...");
   CleanupComments();
   CleanupObjects();

   // แสดง Objects ที่เหลือหลังลบ
   Print("📋 Objects after cleanup:");
   ListAllObjects();

   Print("✅ Cleanup test completed.");
   Print("═══════════════════════════════════════════════════════════");
}

//+------------------------------------------------------------------+
//| ฟังก์ชันสร้าง Test Objects (สำหรับทดสอบ)                         |
//+------------------------------------------------------------------+
void CreateTestObjects()
{
   Print("🏗️ Creating test objects...");

   string current_symbol = _Symbol;
   string current_timeframe = EnumToString(_Period);
   string magic_str = DoubleToString(FXMagic, 0);

   // สร้าง Test Objects
   string test_objects[];
   ArrayResize(test_objects, 4);
   test_objects[0] = "test_text_display_Python_" + current_symbol + "_" + current_timeframe;
   test_objects[1] = "test_text_display_MT5_" + current_symbol + "_" + magic_str;
   test_objects[2] = "test_comment_display_Python_" + current_symbol + "_" + current_timeframe;
   test_objects[3] = "test_label_display_MT5_" + current_symbol + "_" + magic_str;

   for(int i = 0; i < ArraySize(test_objects); i++)
   {
      if(ObjectCreate(0, test_objects[i], OBJ_LABEL, 0, 0, 0))
      {
         ObjectSetString(0, test_objects[i], OBJPROP_TEXT, "Test Object " + IntegerToString(i));
         ObjectSetInteger(0, test_objects[i], OBJPROP_CORNER, CORNER_LEFT_UPPER);
         ObjectSetInteger(0, test_objects[i], OBJPROP_XDISTANCE, 10);
         ObjectSetInteger(0, test_objects[i], OBJPROP_YDISTANCE, 30 + i * 20);
         ObjectSetInteger(0, test_objects[i], OBJPROP_COLOR, clrRed);

         Print("✅ Created test object: ", test_objects[i]);
      }
      else
      {
         Print("❌ Failed to create test object: ", test_objects[i], " Error: ", GetLastError());
      }
   }

   ChartRedraw();
   Print("🏗️ Test objects creation completed.");
}
//+------------------------------------------------------------------+
//| Expert timer function                                            |
//+------------------------------------------------------------------+
void OnTimer()
{
   // --- เพิ่มตรวจสอบ: ถ้า G_last_bar_time ยังเป็น 0 แสดงว่า OnInit ยังไม่ได้ตั้งค่า หรือตั้งค่าไม่สำเร็จ ไม่ต้องประมวลผล OnTimer จนกว่าจะมีค่า
   // นี่เป็นการป้องกันการส่งข้อมูลหาก Batch แรกใน OnInit ไม่สำเร็จ
   if (G_last_bar_time == 0) {
     return;
   }
   
   //--- ตรวจสอบบาร์ล่าสุด
   MqlRates rates[];
   // เราต้องการข้อมูลอย่างน้อย 2 แท่ง: แท่งปัจจุบัน [0] และแท่งที่ปิดแล้ว [1]
   if(CopyRates(G_symbol, G_period, 0, 2, rates) != 2)
   {
      return; // ไม่สามารถคัดลอกข้อมูลได้เพียงพอ
   }
   
   datetime current_bar_time = rates[0].time; // เวลาของแท่งปัจจุบัน (ยังไม่ปิด)
   datetime last_closed_bar_time = rates[1].time; // เวลาของแท่งที่ปิดแล้วล่าสุด (index 1)
      
   // --- ตรวจสอบว่ามีบาร์ใหม่ปิดแล้วหรือไม่ ---
   // เปรียบเทียบเวลาของแท่งที่ปิดแล้วล่าสุด กับเวลาของบาร์ล่าสุดที่เราเคยส่งไป (เก็บใน G_last_bar_time)
   // เงื่อนไข last_closed_bar_time > G_last_bar_time นี้ถูกต้องแล้วในการตรวจหาแท่งใหม่
   if (last_closed_bar_time > G_last_bar_time)
   {
      // --- มีบาร์ใหม่ปิดแล้ว! ---
      PrintFormat("New closed bar detected at %s. Previous last bar sent time: %s. Preparing to send data to Python.",
                 TimeToString(last_closed_bar_time, TIME_DATE|TIME_MINUTES),
                 TimeToString(G_last_bar_time, TIME_DATE|TIME_MINUTES));
      
      // คัดลอกข้อมูล N แท่งย้อนหลัง (รวมแท่งที่เพิ่งปิด) เพื่อส่งให้ Python
      // (ส่วนนี้ย้ายมาจากด้านบนเพื่อให้ชัดเจนว่าทำหลังตรวจพบบาร์ใหม่)
      MqlRates bars_for_python[BARS_FOR_PYTHON];
      if(CopyRates(G_symbol, G_period, 0, BARS_FOR_PYTHON, bars_for_python) != BARS_FOR_PYTHON)
      {
         PrintFormat("Error copying %d recent bars for Python processing in OnTimer.", BARS_FOR_PYTHON);
         return; // ไม่สามารถคัดลอกข้อมูลได้เพียงพอ
      }
      
      // --- เตรียมข้อมูล JSON Payload เป็น Array ของ Bar Objects ---
      string json_payload = "{";
      json_payload += "\"symbol\":\"" + G_symbol + "\",";
      json_payload += "\"timeframe_str\":\"" + G_period_str + "\",";
      json_payload += "\"bars\":[";
      
      for(int i = BARS_FOR_PYTHON - 1; i >= 0; i--)
      {
         json_payload += "{";
         json_payload += "\"time\":" + IntegerToString(bars_for_python[i].time) + ",";
         json_payload += "\"open\":" + DoubleToString(bars_for_python[i].open, Digits()) + ",";
         json_payload += "\"high\":" + DoubleToString(bars_for_python[i].high, Digits()) + ",";
         json_payload += "\"low\":" + DoubleToString(bars_for_python[i].low, Digits()) + ",";
         json_payload += "\"close\":" + DoubleToString(bars_for_python[i].close, Digits()) + ",";
         json_payload += "\"tick_volume\":" + IntegerToString(bars_for_python[i].tick_volume);
         json_payload += "}";
         if (i > 0) json_payload += ",";
      }
      
      json_payload += "]";
      json_payload += "}";
      
      // --- เรียกฟังก์ชันส่ง Web Request และรับ Signal/Confidence กลับมา --
      if (!SendHttpRequest("POST", InpPythonServerUrl, json_payload))
      {
        Print("Failed to send data or receive signal from Python server for new bar.");
        // หากส่งไม่สำเร็จ G_last_bar_time จะยังคงเป็นค่าเดิม (ของแท่งก่อนหน้า)
        // Timer รอบถัดไปจะเห็นว่า last_closed_bar_time ยัง > G_last_bar_time อยู่ และจะพยายามส่งแท่งนี้ใหม่
      }
      else
      {
         // ส่ง Request สำเร็จ และค่า received_signal/confidence ถูกอัปเดตแล้วใน SendHttpRequest
         // --- อัปเดต G_last_bar_time เมื่อส่งสำเร็จแล้วเท่านั้น ---
         G_last_bar_time = last_closed_bar_time; // อัปเดต Global Variable เมื่อส่งสำเร็จ
         PrintFormat("OnTimer: Successfully sent data for bar %s. G_last_bar_time updated to %s (%d).",
                     TimeToString(last_closed_bar_time, TIME_DATE|TIME_MINUTES),
                     TimeToString(G_last_bar_time, TIME_DATE|TIME_MINUTES|TIME_SECONDS), G_last_bar_time); // เพิ่ม Log เพื่อยืนยันค่า
         
         // --- ส่วนที่เพิ่ม: เปิดออเดอร์ตาม Signal และ Confidence (สำหรับ MQL5) ---
         // ตรวจสอบ Signal และ Confidence ที่ได้รับ (ค่าถูกอัปเดตใน SendHttpRequest แล้ว)
         
         bool currency_check = false;
         if(FXBase=="GOLD") {
            Print(" current : symbol ",FXBase," timeframe ",EnumToString(Period())," : receive ",received_response_symbol," ",received_response_timeframe_str);
            
            //if(FXBase==received_response_symbol) {Print("symbol == received_symbol");}
            //else                                 {Print("symbol != received_symbol");}
            //if(EnumToString(Period())==received_response_timeframe_str) {Print("timeframe == received_timeframe");}
            //else                                                        {Print("timeframe != received_timeframe");}
            
            if(FXBase==received_response_symbol && EnumToString(Period())==received_response_timeframe_str) {
               currency_check = true;
           }
         }
         else {
            Print(" current : symbol ",FXBase,"/",FXQuote," timeframe ",EnumToString(Period())," : receive ",received_response_symbol," ",received_response_timeframe_str);
            
            //if((FXBase+FXQuote)==received_response_symbol) {Print("symbol == received_symbol");}
            //else                                           {Print("symbol != received_symbol");}
            //if(EnumToString(Period())==received_response_timeframe_str) {Print("timeframe == received_timeframe");}
            //else                                                        {Print("timeframe != received_timeframe");}
            
            if((FXBase+FXQuote)==received_response_symbol && EnumToString(Period())==received_response_timeframe_str) {
               currency_check = true;
           }
         }
         
         //+------------------------------------------------------------------+
         
         double close_p = iClose(Symbol(), Period(), 1);
         bool   has_ema = true;
         double ema50_p = GetEMA50(Symbol(), Period(), 1);
         bool   has_rsi = true;
         int    rsi_level_out = 30;
         double rsi14_p = GetRSI14(Symbol(), Period(), 1);
         Print(" i-1 : Close ",DoubleToString(close_p,Digits())," EMA ",DoubleToString(ema50_p,Digits()+1)," RSI ",DoubleToString(rsi14_p,2));
         
         Get_Order();
         
         if (order_count_buy > 0.0 && ShouldExitBuy(close_p, ema50_p, rsi14_p, rsi_level_out, has_ema, has_rsi)) {
             CloseBuyOrder(G_symbol, FXMagic);
         }
         
         if (order_count_sell > 0.0 && ShouldExitSell(close_p, ema50_p, rsi14_p, rsi_level_out, has_ema, has_rsi)) {
             CloseSellOrder(G_symbol, FXMagic);
         }
         
         //+------------------------------------------------------------------+
         
         Print("currency_check ",currency_check," : received : signal ",received_signal," confidence ",DoubleToString(received_confidence,3));

         // ตรวจสอบ Time Filters
         bool time_filter_ok = true;
         if(!IgnoreTimeFilters && StringLen(received_time_filters) > 0)
         {
            time_filter_ok = CheckTimeFilters(received_time_filters);
            if(ShowTimeFilterStatus)
            {
               WriteLog(StringFormat("TIME_FILTER_CHECK: %s, Filters: %s",
                       time_filter_ok ? "ALLOWED" : "BLOCKED", received_time_filters), "TIME");
            }
         }
         else if(IgnoreTimeFilters)
         {
            if(ShowTimeFilterStatus)
            {
               WriteLog("TIME_FILTER_IGNORED: Trading allowed anytime", "TIME");
            }
         }

         if(currency_check==true && time_filter_ok) {
            if (received_signal == "BUY" || received_signal == "SELL") {

               // === ขั้นตอนที่ 0: ตรวจสอบ Scenario และ Threshold ที่เหมาะสม ===
               bool scenario_check = ValidateScenarioAndThreshold(received_signal, received_confidence,
                                                                 received_scenario_used, received_market_condition);

               if(!scenario_check) {
                  Print("=== Scenario Validation Failed ===");
                  Print("Signal: ", received_signal, " blocked by scenario validation");
                  Print("Scenario: ", received_scenario_used, " Market: ", received_market_condition);
                  Print("Confidence: ", DoubleToString(received_confidence, 4));
                  return; // ออกจากการประมวลผลทันที
               }

               // === ขั้นตอนที่ 1: คำนวณการชดเชยราคา ===
               double adjusted_entry, adjusted_sl, adjusted_tp;
               if(price_adjustment_enabled)
               {
                  CalculatePriceAdjustment(received_signal, received_entry_price, received_sl_price, received_tp_price,
                                         adjusted_entry, adjusted_sl, adjusted_tp);
               }
               else
               {
                  adjusted_entry = received_entry_price;
                  adjusted_sl = received_sl_price;
                  adjusted_tp = received_tp_price;
               }

               Print("=== Enhanced Trading Decision ===");
               Print("Signal: ", received_signal, " (", received_class, ")");
               Print("Scenario: ", received_scenario_used, " Market: ", received_market_condition);
               Print("Confidence: ", DoubleToString(received_confidence, 4));
               Print("Original Entry: ", DoubleToString(received_entry_price, _Digits));
               Print("Adjusted Entry: ", DoubleToString(adjusted_entry, _Digits));
               Print("Adjusted SL: ", DoubleToString(adjusted_sl, _Digits));
               Print("Adjusted TP: ", DoubleToString(adjusted_tp, _Digits));
               Print("Total Lot: ", DoubleToString(Order_Lot, 2));

               // === ขั้นตอนที่ 2: เปิดเทรดหลายไม้ ===
               bool trade_success = false;
               if(multi_trade_enabled && Order_Lot > 0.01)
               {
                  trade_success = OpenMultiTrades(received_signal, adjusted_entry, adjusted_sl, adjusted_tp, Order_Lot);
               }
               else
               {
                  // เปิดเทรดไม้เดียวแบบเดิม
                  if (received_signal == "BUY") {
                     trade_success = SendBuyOrder(G_symbol, received_confidence, Order_Lot, FXMagic);
                  }
                  
                  if (received_signal == "SELL") {
                     trade_success = SendSellOrder(G_symbol, received_confidence, Order_Lot, FXMagic);
                  }
               }

               if(trade_success)
               {
                  Print("=== Trade Execution Success ===");
                  Print("Multi-Trade Mode: ", multi_trade_enabled ? "Enabled" : "Disabled");
                  Print("Price Adjustment: ", price_adjustment_enabled ? "Enabled" : "Disabled");
                  Print("Break Even: ", breakeven_enabled ? "Enabled" : "Disabled");
                  Print("Friday Close: ", friday_close_enabled ? "Enabled" : "Disabled");
               }
               else
               {
                  Print("=== Trade Execution Failed ===");
               }
            }
            else {
               // Signal ไม่ตรงเงื่อนไข หรือ Confidence ต่ำกว่าที่กำหนด
               PrintFormat("Received signal '%s' with confidence %.2f. No order opened.", received_signal, received_confidence);
            }
         }
         else if(!time_filter_ok) {
            WriteLog(StringFormat("TRADE_BLOCKED: Signal=%s, Confidence=%.4f blocked by time filters: %s",
                    received_signal, received_confidence, received_time_filters), "TRADE");
            Print("Trading blocked by time filters: ", received_time_filters);
         }
         else {
            Print("Currency check failed or other conditions not met");
         }
      }
   }
}
//+------------------------------------------------------------------+
//| Function to extract Base and Quote currencies and Postfix        |
//+------------------------------------------------------------------+
void Setup_Base_Quote()
{
   string symbol = _Symbol; // Get current symbol name
   
   if(StringSubstr(symbol, 0, 4) == "GOLD")
      {
         FXBase  = "GOLD";
         FXQuote = "USD"; // Assuming GOLD is typically quoted against USD
         PostFix = StringSubstr(symbol, 4, StringLen(symbol) - 4);
     }
   else
      {
         // Standard currency pair format (e.g., EURUSD, GBPJPY)
         if (StringLen(symbol) >= 6) // Ensure symbol is long enough
            {
                FXBase  = StringSubstr(symbol, 0, 3);
                FXQuote = StringSubstr(symbol, 3, 3);
                PostFix = StringSubstr(symbol, 6, StringLen(symbol) - 6);
           }
         else // Handle symbols shorter than 6 characters if necessary
            {
                 FXBase  = symbol; // Or handle this case based on expected symbols
                 FXQuote = "";
                 PostFix = "";
                 Print("Warning: Symbol '", symbol, "' is shorter than expected standard currency pair length.");
           }
     }
}
//+------------------------------------------------------------------+
void GetSpread(string symbol, string postfix)
{
   FXSlippage = 5;
   
   if(symbol == "AUDUSD"+postfix)      {FXSpread = 15;}
   else if(symbol == "EURGBP"+postfix) {FXSpread = 22;}
   else if(symbol == "EURUSD"+postfix) {FXSpread = 13;}
   else if(symbol == "GBPUSD"+postfix) {FXSpread = 25;}
   else if(symbol == "NZDUSD"+postfix) {FXSpread = 22;}
   else if(symbol == "USDCAD"+postfix) {FXSpread = 28;}
   else if(symbol == "USDJPY"+postfix) {FXSpread = 16;}
   else if(symbol == "GOLD"+postfix)   {FXSpread = 25;}
}
//+------------------------------------------------------------------+
void GetPV(string base, string quote, string postfix)
{
   double bid           = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double contract_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_CONTRACT_SIZE);
   double point         = SymbolInfoDouble(_Symbol, SYMBOL_POINT);

   if (quote == "USD") {
      if(base == "GOLD") {FXPV = 1.0;}
      else               {FXPV = NormalizeDouble(point * ACContract, 4);}
   } 
   else {
      string pair;
      double rate;
   
      if (base == "AUD")      {pair = "AUDUSD"+postfix; rate = iOpen(pair, PERIOD_CURRENT, 0); FXPV = NormalizeDouble((point / bid) * rate * ACContract, 4);}
      else if (base == "CAD") {pair = "USDCAD"+postfix; rate = iOpen(pair, PERIOD_CURRENT, 0); FXPV = NormalizeDouble((point / bid) * (1 / rate) * ACContract, 4);}
      else if (base == "CHF") {pair = "USDCHF"+postfix; rate = iOpen(pair, PERIOD_CURRENT, 0); FXPV = NormalizeDouble((point / bid) * (1 / rate) * ACContract, 4);}
      else if (base == "EUR") {pair = "EURUSD"+postfix; rate = iOpen(pair, PERIOD_CURRENT, 0); FXPV = NormalizeDouble((point / bid) * rate * ACContract, 4);}
      else if (base == "GBP") {pair = "GBPUSD"+postfix; rate = iOpen(pair, PERIOD_CURRENT, 0); FXPV = NormalizeDouble((point / bid) * rate * ACContract, 4);}
      else if (base == "JPY") {pair = "USDJPY"+postfix; rate = iOpen(pair, PERIOD_CURRENT, 0); FXPV = NormalizeDouble((point / bid) * (1 / rate) * ACContract, 4);}
      else if (base == "NZD") {pair = "NZDUSD"+postfix; rate = iOpen(pair, PERIOD_CURRENT, 0); FXPV = NormalizeDouble((point / bid) * rate * ACContract, 4);}
      else if (base == "USD") {FXPV = NormalizeDouble((point / bid) * ACContract, 4);}
      else                    {FXPV = NormalizeDouble(point * ACContract, 4);}
   }
}
//+------------------------------------------------------------------+
//| Function to setup Magic Number components and calculate FXMagic  |
//+------------------------------------------------------------------+
void Setup_Magic()
{
   //--- Assign numerical value based on Base Currency
   if(FXBase == "AUD")        {FXMG1 = 1;}
   else if(FXBase == "CAD")   {FXMG1 = 2;}
   else if(FXBase == "CHF")   {FXMG1 = 3;}
   else if(FXBase == "EUR")   {FXMG1 = 4;}
   else if(FXBase == "GBP")   {FXMG1 = 5;}
   else if(FXBase == "JPY")   {FXMG1 = 6;}
   else if(FXBase == "NZD")   {FXMG1 = 7;}
   else if(FXBase == "USD")   {FXMG1 = 8;}
   else if(FXBase == "GOLD")  {FXMG1 = 9;} // Added GOLD as a specific value
   else                       {FXMG1 = 9;} // Default for other Base Currencies
   
   //--- Assign numerical value based on Quote Currency
   if(FXQuote == "AUD")       {FXMG2 = 1;}
   else if(FXQuote == "CAD")  {FXMG2 = 2;}
   else if(FXQuote == "CHF")  {FXMG2 = 3;}
   else if(FXQuote == "EUR")  {FXMG2 = 4;}
   else if(FXQuote == "GBP")  {FXMG2 = 5;}
   else if(FXQuote == "JPY")  {FXMG2 = 6;}
   else if(FXQuote == "NZD")  {FXMG2 = 7;}
   else if(FXQuote == "USD")  {FXMG2 = 8;}
   else                       {FXMG2 = 9;} // Default for other Quote Currencies
   
   //--- Assign numerical value based on Timeframe (Period in minutes)
   // MT5's _Period variable gives the period in minutes directly
   ENUM_TIMEFRAMES currentPeriod = Period();
    
   if(currentPeriod == PERIOD_D1)      {FXMG3 = 14;} // Daily (D1)
   else if(currentPeriod == PERIOD_H4) {FXMG3 = 24;} // H4
   else if(currentPeriod == PERIOD_H1) {FXMG3 = 60;} // H1
   else if(currentPeriod == PERIOD_M30){FXMG3 = 30;} // M30
   else if(currentPeriod == PERIOD_M15){FXMG3 = 15;} // M15
   else if(currentPeriod == PERIOD_M5) {FXMG3 = 05;} // M5
   else if(currentPeriod == PERIOD_M1) {FXMG3 = 01;} // M1
   else                                {FXMG3 = 99;} // Default for other timeframes
   
   //--- Calculate the final Magic Number
   // Ensure EA_Version is cast to int or adjusted if needed for precision
   // The formula is (int)(EA_Version * 10000 + FXMG3 * 100 + FXMG1 * 10 + FXMG2)
   // Let's assume EA_Version input is like 1.0, 1.1, etc.
   // To incorporate the decimal part, you might need to adjust the multiplier.
   // For simplicity and consistency with the original integer conversion,
   // we'll keep the structure but ensure the multiplier is appropriate.
   // If EA_Version is 1.0, EA_Version * 10000 is 10000.
   // If EA_Version is 1.1, EA_Version * 10000 is 11000.
   // This seems to follow the intent.
   
   FXMagic = (int)(EA_Version * 10000 + FXMG3 * 100 + FXMG1 * 10 + FXMG2);
}
//+------------------------------------------------------------------+
int total_positions = 0;
int    order_count_buy = 0;
double order_open_buy = 0;
double order_stoploss_buy = 0;
int    order_count_sell = 0;
double order_open_sell = 0;
double order_stoploss_sell = 0;
//+------------------------------------------------------------------+
void Get_Order()
{
   total_positions = PositionsTotal();
   order_count_buy = 0;
   order_open_buy = 0;
   order_stoploss_buy = 0;
   
   order_count_sell = 0;
   order_open_sell = 0;
   order_stoploss_sell = 0;
   
   string current_symbol = _Symbol;
   int current_timeframe = Period();
   Print(" Order : symbol ",current_symbol," timeframe ",current_timeframe," : receive ",received_response_symbol," ",received_response_timeframe_str," : Order total ",total_positions);
   
   for(int i = 0; i < total_positions; i++)
   {
      ulong ticket = PositionGetTicket(i);
      if(PositionSelectByTicket(ticket))
      {
         // ตรวจสอบว่า Symbol และ FXMagic ตรงกัน
         string pos_symbol = PositionGetString(POSITION_SYMBOL);
         ulong pos_magic  = (ulong)PositionGetInteger(POSITION_MAGIC);
         
         Print(" Order : total ",total_positions," ticket ",ticket," symbol ",pos_symbol," magic ",pos_magic);
   
         if(pos_symbol == current_symbol && pos_magic == FXMagic)
         {
            int pos_type = (int)PositionGetInteger(POSITION_TYPE);
            Print(" Order Info : Type ",pos_type," Buy= ",POSITION_TYPE_BUY," Sell= ",POSITION_TYPE_SELL);
   
            if(pos_type == POSITION_TYPE_BUY) {
                  order_count_buy++;
                  order_open_buy     = PositionGetDouble(POSITION_PRICE_OPEN);
                  order_stoploss_buy = PositionGetDouble(POSITION_SL);
                  Print(" Order Info : Ticket=", ticket, 
                        " Type=BUY",
                        " OpenPrice=", DoubleToString(order_open_buy, _Digits),
                        " StopLoss=", DoubleToString(order_stoploss_buy, _Digits));
            }

            if(pos_type == POSITION_TYPE_SELL) {
               order_count_sell++;
               order_open_sell     = PositionGetDouble(POSITION_PRICE_OPEN);
               order_stoploss_sell = PositionGetDouble(POSITION_SL);
               Print(" Order Info : Ticket=", ticket, 
                     " Type=SELL",
                     " OpenPrice=", DoubleToString(order_open_sell, _Digits),
                     " StopLoss=", DoubleToString(order_stoploss_sell, _Digits));
            }
        }
     }
   }
   
   Print(" Order : Symbol ", current_symbol,
         " Buy: ", order_count_buy,
         " Sell: ", order_count_sell,
         " Magic: ", FXMagic);
}
//+------------------------------------------------------------------+
//| Web Request function                                             |
//+------------------------------------------------------------------+
bool SendHttpRequest(string method, string url, string data)
{
   int data_len = StringLen(data);
   
   if (data_len <= 0)
   {
      Print("JSON Payload String is empty or null.");
      return false;
   }
   
   uchar post_data[];
   if (ArraySize(post_data) != data_len)
   {
      if (ArrayResize(post_data, data_len) != data_len)
      {
         PrintFormat("Error resizing post_data array to %d", data_len);
         return false;
      }
   }
   
   int copied_len = StringToCharArray(data, post_data, 0, data_len, CP_UTF8);
   
   if(copied_len != data_len)
   {
      PrintFormat("Warning: Failed to copy expected number of bytes for WebRequest. Copied %d, expected %d", copied_len, data_len);
      if (copied_len <= 0) {
         Print("Error: Data copy failed for WebRequest.");
         return false;
      }
   }
   
   string headers = "Content-Type: application/json";
   string cookie = "";
   
   uchar result_buffer[];
   string result_headers;
   
   // WebRequest Timeout is in milliseconds
   int result_code = WebRequest(method, url, headers, cookie, InpWebRequestTimeout * 1000, post_data, data_len, result_buffer, result_headers);
   
   if(result_code != 200) // Check HTTP status code (200 OK is expected)
   {
      PrintFormat("Error sending Web Request: HTTP status code %d", result_code);
      string response_body = CharArrayToString(result_buffer, 0, WHOLE_ARRAY, CP_UTF8);
      PrintFormat("Response Body (Error): %s", response_body); // แสดง Response Body เมื่อมี Error
      
      // หากมี Error ในการส่ง ให้ clear ค่า signal/confidence หรือตั้งค่าเป็นค่าเริ่มต้น
      received_signal = "ERROR";
      received_class = "ERROR";
      received_confidence = 0.0;
      // *** เรียกฟังก์ชันอัปเดตการแสดงผล แม้จะมี Error เพื่อบอกให้ผู้ใช้ทราบ ***
      // ใช้ค่า default สำหรับ error case
      UpdateCombinedDisplay(received_signal, received_class, received_confidence, received_response_symbol, received_response_timeframe_str,
                           received_best_entry, 6, 0.50, received_time_filters, received_spread,
                           FXMagic, FXSpread, FXPV, "", "", "");
      
      return false; // ส่งไม่สำเร็จ
   }
   
   // --- ส่วนที่เพิ่ม: อ่านและ Print Response Body เมื่อส่งสำเร็จ ---
   string response_body_str = CharArrayToString(result_buffer, 0, WHOLE_ARRAY, CP_UTF8);
   PrintFormat("Web Request successful! Status 200.");
   PrintFormat("Received Response Body: %s", response_body_str);
   // --- จบส่วนที่เพิ่ม ---
   
   // --- แก้ไข: Logic ในการ Parse JSON String เพื่อดึง Signal, Confidence, Timestamps, Symbol, Timeframe และข้อมูลเพิ่มเติม ---
   received_signal = "N/A";
   received_class = "N/A";
   received_confidence = 0.0;
   received_entry_price = 0.0;
   received_sl_price = 0.0;
   received_tp_price = 0.0;
   received_bar_timestamp = 0.0;
   received_signal_bar_timestamp = 0.0;
   received_response_symbol = "";
   received_response_timeframe_str = "";
   received_best_entry = 0.0;
   received_nBars_SL = 0;
   received_threshold = 0.0;
   received_time_filters = "";
   received_spread = 0;
   
   // Parse Signal
   int signal_key_start = StringFind(response_body_str, "\"signal\":", 0);
   received_signal = "N/A"; // ตั้งค่าเริ่มต้นเผื่อหาไม่เจอ
   if (signal_key_start != -1) {
      int signal_value_start = StringFind(response_body_str, "\"", signal_key_start + StringLen("\"signal\":"));
      if (signal_value_start != -1) {
         signal_value_start++;
         int signal_value_end = StringFind(response_body_str, "\"", signal_value_start);
         if (signal_value_end != -1) {
            received_signal = StringSubstr(response_body_str, signal_value_start, signal_value_end - signal_value_start);
         }
      }
   }
   
   // Parse Confidence
   int confidence_key_start = StringFind(response_body_str, "\"confidence\":", 0);
   received_confidence = 0.0; // ตั้งค่าเริ่มต้นเผื่อหาไม่เจอ
   if(confidence_key_start != -1) {
      int confidence_value_start = confidence_key_start + StringLen("\"confidence\":");
      while (confidence_value_start < StringLen(response_body_str) && StringGetCharacter(response_body_str, confidence_value_start) == ' ') confidence_value_start++;
      int confidence_value_end_comma = StringFind(response_body_str, ",", confidence_value_start);
      int confidence_value_end_brace = StringFind(response_body_str, "}", confidence_value_start);
      int confidence_value_end = -1;
      if(confidence_value_end_comma != -1 && confidence_value_end_brace != -1) confidence_value_end = MathMin(confidence_value_end_comma, confidence_value_end_brace);
      else if(confidence_value_end_comma != -1) confidence_value_end = confidence_value_end_comma;
      else if(confidence_value_end_brace != -1) confidence_value_end = confidence_value_end_brace;
      
      if(confidence_value_end != -1 && confidence_value_end > confidence_value_start) {
         string extracted_conf_str = StringSubstr(response_body_str, confidence_value_start, confidence_value_end - confidence_value_start);
         received_confidence = StringToDouble(extracted_conf_str);
      }
   }

   // Parse bar_timestamp
   int bar_ts_key_start = StringFind(response_body_str, "\"bar_timestamp\":", 0);
   if (bar_ts_key_start != -1) {/* ... parse bar_timestamp ... */
      int bar_ts_value_start = bar_ts_key_start + StringLen("\"bar_timestamp\":");
      while (bar_ts_value_start < StringLen(response_body_str) && StringGetCharacter(response_body_str, bar_ts_value_start) == ' ') bar_ts_value_start++;
      int bar_ts_value_end_comma = StringFind(response_body_str, ",", bar_ts_value_start);
      int bar_ts_value_end_brace = StringFind(response_body_str, "}", bar_ts_value_start);
      int bar_ts_value_end = -1;
      if(bar_ts_value_end_comma != -1 && bar_ts_value_end_brace != -1) bar_ts_value_end = MathMin(bar_ts_value_end_comma, bar_ts_value_end_brace);
      else if(bar_ts_value_end_comma != -1) bar_ts_value_end = bar_ts_value_end_comma;
      else if(bar_ts_value_end_brace != -1) bar_ts_value_end = bar_ts_value_end_brace;
      
      if (bar_ts_value_end != -1 && bar_ts_value_end > bar_ts_value_start) {
         string bar_ts_str = StringSubstr(response_body_str, bar_ts_value_start, bar_ts_value_end - bar_ts_value_start);
         received_bar_timestamp = StringToDouble(bar_ts_str);
      }
   }
   
   // Parse signal_bar_timestamp
   int sig_bar_ts_key_start = StringFind(response_body_str, "\"signal_bar_timestamp\":", 0);
   if (sig_bar_ts_key_start != -1) {/* ... parse signal_bar_timestamp ... */
      int sig_bar_ts_value_start = sig_bar_ts_key_start + StringLen("\"signal_bar_timestamp\":");
      while (sig_bar_ts_value_start < StringLen(response_body_str) && StringGetCharacter(response_body_str, sig_bar_ts_value_start) == ' ') sig_bar_ts_value_start++;
      int sig_bar_ts_value_end_comma = StringFind(response_body_str, ",", sig_bar_ts_value_start);
      int sig_bar_ts_value_end_brace = StringFind(response_body_str, "}", sig_bar_ts_value_start);
      int sig_bar_ts_value_end = -1;
      if(sig_bar_ts_value_end_comma != -1 && sig_bar_ts_value_end_brace != -1) sig_bar_ts_value_end = MathMin(sig_bar_ts_value_end_comma, sig_bar_ts_value_end_brace);
      else if(sig_bar_ts_value_end_comma != -1) sig_bar_ts_value_end = sig_bar_ts_value_end_comma;
      else if(sig_bar_ts_value_end_brace != -1) sig_bar_ts_value_end = sig_bar_ts_value_end_brace;
      
      if (sig_bar_ts_value_end != -1 && sig_bar_ts_value_end > sig_bar_ts_value_start) {
         string sig_bar_ts_str = StringSubstr(response_body_str, sig_bar_ts_value_start, sig_bar_ts_value_end - sig_bar_ts_value_start);
         received_signal_bar_timestamp = StringToDouble(sig_bar_ts_str);
      }
   }
   
   // Parse symbol
   int symbol_key_start = StringFind(response_body_str, "\"symbol\":", 0);
   if (symbol_key_start != -1) {
      int symbol_value_start = StringFind(response_body_str, "\"", symbol_key_start + StringLen("\"symbol\":"));
      if (symbol_value_start != -1) {
         symbol_value_start++;
         int symbol_value_end = StringFind(response_body_str, "\"", symbol_value_start);
         if (symbol_value_end != -1) {
            received_response_symbol = StringSubstr(response_body_str, symbol_value_start, symbol_value_end - symbol_value_start);
         }
      }
   }

   // Parse timeframe_str
   int timeframe_key_start = StringFind(response_body_str, "\"timeframe_str\":", 0);
   if (timeframe_key_start != -1) {
      int timeframe_value_start = StringFind(response_body_str, "\"", timeframe_key_start + StringLen("\"timeframe_str\":"));
      if (timeframe_value_start != -1) {
         timeframe_value_start++;
         int timeframe_value_end = StringFind(response_body_str, "\"", timeframe_value_start);
         if (timeframe_value_end != -1) {
            received_response_timeframe_str = StringSubstr(response_body_str, timeframe_value_start, timeframe_value_end - timeframe_value_start);
         }
      }
   }

   // Parse sl_price
   int sl_key_start = StringFind(response_body_str, "\"sl_price\":", 0);
   received_sl_price = 0.0; // ตั้งค่าเริ่มต้นเผื่อหาไม่เจอ
   if(sl_key_start != -1) {
      int sl_value_start = sl_key_start + StringLen("\"sl_price\":");
      while (sl_value_start < StringLen(response_body_str) && StringGetCharacter(response_body_str, sl_value_start) == ' ') sl_value_start++;
      int sl_value_end_comma = StringFind(response_body_str, ",", sl_value_start);
      int sl_value_end_brace = StringFind(response_body_str, "}", sl_value_start);
      int sl_value_end = -1;
      if(sl_value_end_comma != -1 && sl_value_end_brace != -1) sl_value_end = MathMin(sl_value_end_comma, sl_value_end_brace);
      else if(sl_value_end_comma != -1)                        sl_value_end = sl_value_end_comma;
      else if(sl_value_end_brace != -1)                        sl_value_end = sl_value_end_brace;
      
      if(sl_value_end != -1 && sl_value_end > sl_value_start) {
         string extracted_sl_str = StringSubstr(response_body_str, sl_value_start, sl_value_end - sl_value_start);
         received_sl_price = StringToDouble(extracted_sl_str);
      }
   }
    
   // Parse tp_price
   int tp_key_start = StringFind(response_body_str, "\"tp_price\":", 0);
   received_tp_price = 0.0; // ตั้งค่าเริ่มต้นเผื่อหาไม่เจอ
   if(tp_key_start != -1) {
      int tp_value_start = tp_key_start + StringLen("\"tp_price\":");
      while (tp_value_start < StringLen(response_body_str) && StringGetCharacter(response_body_str, tp_value_start) == ' ') tp_value_start++;
      int tp_value_end_comma = StringFind(response_body_str, ",", tp_value_start);
      int tp_value_end_brace = StringFind(response_body_str, "}", tp_value_start);
      int tp_value_end = -1;
      if(tp_value_end_comma != -1 && tp_value_end_brace != -1) tp_value_end = MathMin(tp_value_end_comma, tp_value_end_brace);
      else if(tp_value_end_comma != -1) tp_value_end = tp_value_end_comma;
      else if(tp_value_end_brace != -1) tp_value_end = tp_value_end_brace;
      
      if(tp_value_end != -1 && tp_value_end > tp_value_start) {
         string extracted_tp_str = StringSubstr(response_body_str, tp_value_start, tp_value_end - tp_value_start);
         received_tp_price = StringToDouble(extracted_tp_str);
     }
  }

   // Parse class (ระดับ class ของ signal)
   int class_key_start = StringFind(response_body_str, "\"class\":", 0);
   if (class_key_start != -1) {
      int class_value_start = StringFind(response_body_str, "\"", class_key_start + StringLen("\"class\":"));
      if (class_value_start != -1) {
         class_value_start++;
         int class_value_end = StringFind(response_body_str, "\"", class_value_start);
         if (class_value_end != -1) {
            received_class = StringSubstr(response_body_str, class_value_start, class_value_end - class_value_start);
         }
      }
   }

   // Parse entry_price
   int entry_key_start = StringFind(response_body_str, "\"entry_price\":", 0);
   if(entry_key_start != -1) {
      int entry_value_start = entry_key_start + StringLen("\"entry_price\":");
      while (entry_value_start < StringLen(response_body_str) && StringGetCharacter(response_body_str, entry_value_start) == ' ') entry_value_start++;
      int entry_value_end_comma = StringFind(response_body_str, ",", entry_value_start);
      int entry_value_end_brace = StringFind(response_body_str, "}", entry_value_start);
      int entry_value_end = -1;
      if(entry_value_end_comma != -1 && entry_value_end_brace != -1) entry_value_end = MathMin(entry_value_end_comma, entry_value_end_brace);
      else if(entry_value_end_comma != -1) entry_value_end = entry_value_end_comma;
      else if(entry_value_end_brace != -1) entry_value_end = entry_value_end_brace;

      if(entry_value_end != -1 && entry_value_end > entry_value_start) {
         string extracted_entry_str = StringSubstr(response_body_str, entry_value_start, entry_value_end - entry_value_start);
         received_entry_price = StringToDouble(extracted_entry_str);
      }
   }

   // Parse best_entry
   int best_entry_key_start = StringFind(response_body_str, "\"best_entry\":", 0);
   if(best_entry_key_start != -1) {
      int best_entry_value_start = best_entry_key_start + StringLen("\"best_entry\":");
      while (best_entry_value_start < StringLen(response_body_str) && StringGetCharacter(response_body_str, best_entry_value_start) == ' ') best_entry_value_start++;
      int best_entry_value_end_comma = StringFind(response_body_str, ",", best_entry_value_start);
      int best_entry_value_end_brace = StringFind(response_body_str, "}", best_entry_value_start);
      int best_entry_value_end = -1;
      if(best_entry_value_end_comma != -1 && best_entry_value_end_brace != -1) best_entry_value_end = MathMin(best_entry_value_end_comma, best_entry_value_end_brace);
      else if(best_entry_value_end_comma != -1) best_entry_value_end = best_entry_value_end_comma;
      else if(best_entry_value_end_brace != -1) best_entry_value_end = best_entry_value_end_brace;

      if(best_entry_value_end != -1 && best_entry_value_end > best_entry_value_start) {
         string extracted_best_entry_str = StringSubstr(response_body_str, best_entry_value_start, best_entry_value_end - best_entry_value_start);
         received_best_entry = StringToDouble(extracted_best_entry_str);
      }
   }

   // Parse nBars_SL
   int nBars_SL_key_start = StringFind(response_body_str, "\"nBars_SL\":", 0);
   if(nBars_SL_key_start != -1) {
      int nBars_SL_value_start = nBars_SL_key_start + StringLen("\"nBars_SL\":");
      while (nBars_SL_value_start < StringLen(response_body_str) && StringGetCharacter(response_body_str, nBars_SL_value_start) == ' ') nBars_SL_value_start++;
      int nBars_SL_value_end_comma = StringFind(response_body_str, ",", nBars_SL_value_start);
      int nBars_SL_value_end_brace = StringFind(response_body_str, "}", nBars_SL_value_start);
      int nBars_SL_value_end = -1;
      if(nBars_SL_value_end_comma != -1 && nBars_SL_value_end_brace != -1) nBars_SL_value_end = MathMin(nBars_SL_value_end_comma, nBars_SL_value_end_brace);
      else if(nBars_SL_value_end_comma != -1) nBars_SL_value_end = nBars_SL_value_end_comma;
      else if(nBars_SL_value_end_brace != -1) nBars_SL_value_end = nBars_SL_value_end_brace;

      if(nBars_SL_value_end != -1 && nBars_SL_value_end > nBars_SL_value_start) {
         string extracted_nBars_SL_str = StringSubstr(response_body_str, nBars_SL_value_start, nBars_SL_value_end - nBars_SL_value_start);
         received_nBars_SL = (int)StringToInteger(extracted_nBars_SL_str);
      }
   }

   // Parse threshold
   int threshold_key_start = StringFind(response_body_str, "\"threshold\":", 0);
   if(threshold_key_start != -1) {
      int threshold_value_start = threshold_key_start + StringLen("\"threshold\":");
      while (threshold_value_start < StringLen(response_body_str) && StringGetCharacter(response_body_str, threshold_value_start) == ' ') threshold_value_start++;
      int threshold_value_end_comma = StringFind(response_body_str, ",", threshold_value_start);
      int threshold_value_end_brace = StringFind(response_body_str, "}", threshold_value_start);
      int threshold_value_end = -1;
      if(threshold_value_end_comma != -1 && threshold_value_end_brace != -1) threshold_value_end = MathMin(threshold_value_end_comma, threshold_value_end_brace);
      else if(threshold_value_end_comma != -1) threshold_value_end = threshold_value_end_comma;
      else if(threshold_value_end_brace != -1) threshold_value_end = threshold_value_end_brace;

      if(threshold_value_end != -1 && threshold_value_end > threshold_value_start) {
         string extracted_threshold_str = StringSubstr(response_body_str, threshold_value_start, threshold_value_end - threshold_value_start);
         received_threshold = StringToDouble(extracted_threshold_str);
      }
   }

   // Parse spread
   int spread_key_start = StringFind(response_body_str, "\"spread\":", 0);
   if(spread_key_start != -1) {
      int spread_value_start = spread_key_start + StringLen("\"spread\":");
      while (spread_value_start < StringLen(response_body_str) && StringGetCharacter(response_body_str, spread_value_start) == ' ') spread_value_start++;
      int spread_value_end_comma = StringFind(response_body_str, ",", spread_value_start);
      int spread_value_end_brace = StringFind(response_body_str, "}", spread_value_start);
      int spread_value_end = -1;
      if(spread_value_end_comma != -1 && spread_value_end_brace != -1) spread_value_end = MathMin(spread_value_end_comma, spread_value_end_brace);
      else if(spread_value_end_comma != -1) spread_value_end = spread_value_end_comma;
      else if(spread_value_end_brace != -1) spread_value_end = spread_value_end_brace;

      if(spread_value_end != -1 && spread_value_end > spread_value_start) {
         string extracted_spread_str = StringSubstr(response_body_str, spread_value_start, spread_value_end - spread_value_start);
         received_spread = (int)StringToInteger(extracted_spread_str);
      }
   }

   // Parse time_filters
   int time_filters_key_start = StringFind(response_body_str, "\"time_filters\":", 0);
   if (time_filters_key_start != -1) {
      int time_filters_value_start = StringFind(response_body_str, "\"", time_filters_key_start + StringLen("\"time_filters\":"));
      if (time_filters_value_start != -1) {
         time_filters_value_start++;
         int time_filters_value_end = StringFind(response_body_str, "\"", time_filters_value_start);
         if (time_filters_value_end != -1) {
            received_time_filters = StringSubstr(response_body_str, time_filters_value_start, time_filters_value_end - time_filters_value_start);
         }
      }
   }

   // *** เพิ่ม: Parse Multi-Model Analysis ***
   // Parse market_condition
   int market_condition_key_start = StringFind(response_body_str, "\"market_condition\":", 0);
   if (market_condition_key_start != -1) {
      int market_condition_value_start = StringFind(response_body_str, "\"", market_condition_key_start + StringLen("\"market_condition\":"));
      if (market_condition_value_start != -1) {
         market_condition_value_start++;
         int market_condition_value_end = StringFind(response_body_str, "\"", market_condition_value_start);
         if (market_condition_value_end != -1) {
            received_market_condition = StringSubstr(response_body_str, market_condition_value_start, market_condition_value_end - market_condition_value_start);
         }
      }
   }

   // Parse action_type
   int action_type_key_start = StringFind(response_body_str, "\"action_type\":", 0);
   if (action_type_key_start != -1) {
      int action_type_value_start = StringFind(response_body_str, "\"", action_type_key_start + StringLen("\"action_type\":"));
      if (action_type_value_start != -1) {
         action_type_value_start++;
         int action_type_value_end = StringFind(response_body_str, "\"", action_type_value_start);
         if (action_type_value_end != -1) {
            received_action_type = StringSubstr(response_body_str, action_type_value_start, action_type_value_end - action_type_value_start);
         }
      }
   }

   // Parse scenario_used
   int scenario_used_key_start = StringFind(response_body_str, "\"scenario_used\":", 0);
   if (scenario_used_key_start != -1) {
      int scenario_used_value_start = StringFind(response_body_str, "\"", scenario_used_key_start + StringLen("\"scenario_used\":"));
      if (scenario_used_value_start != -1) {
         scenario_used_value_start++;
         int scenario_used_value_end = StringFind(response_body_str, "\"", scenario_used_value_start);
         if (scenario_used_value_end != -1) {
            received_scenario_used = StringSubstr(response_body_str, scenario_used_value_start, scenario_used_value_end - scenario_used_value_start);
         }
      }
   }

   // *** Parse ข้อมูลทั้ง 2 ระบบ ***
   // Parse trend_following_threshold
   int tf_threshold_key_start = StringFind(response_body_str, "\"trend_following_threshold\":");
   if (tf_threshold_key_start != -1) {
      int tf_threshold_value_start = tf_threshold_key_start + StringLen("\"trend_following_threshold\":");
      int tf_threshold_value_end = StringFind(response_body_str, ",", tf_threshold_value_start);
      if (tf_threshold_value_end == -1) tf_threshold_value_end = StringFind(response_body_str, "}", tf_threshold_value_start);
      if (tf_threshold_value_end != -1) {
         string tf_threshold_str = StringSubstr(response_body_str, tf_threshold_value_start, tf_threshold_value_end - tf_threshold_value_start);
         StringTrimLeft(tf_threshold_str);
         StringTrimRight(tf_threshold_str);
         received_trend_following_threshold = StringToDouble(tf_threshold_str);
      }
   }

   // Parse trend_following_nbars
   int tf_nbars_key_start = StringFind(response_body_str, "\"trend_following_nbars\":");
   if (tf_nbars_key_start != -1) {
      int tf_nbars_value_start = tf_nbars_key_start + StringLen("\"trend_following_nbars\":");
      int tf_nbars_value_end = StringFind(response_body_str, ",", tf_nbars_value_start);
      if (tf_nbars_value_end == -1) tf_nbars_value_end = StringFind(response_body_str, "}", tf_nbars_value_start);
      if (tf_nbars_value_end != -1) {
         string tf_nbars_str = StringSubstr(response_body_str, tf_nbars_value_start, tf_nbars_value_end - tf_nbars_value_start);
         StringTrimLeft(tf_nbars_str);
         StringTrimRight(tf_nbars_str);
         received_trend_following_nbars = (int)StringToInteger(tf_nbars_str);
      }
   }

   // Parse counter_trend_threshold
   int ct_threshold_key_start = StringFind(response_body_str, "\"counter_trend_threshold\":");
   if (ct_threshold_key_start != -1) {
      int ct_threshold_value_start = ct_threshold_key_start + StringLen("\"counter_trend_threshold\":");
      int ct_threshold_value_end = StringFind(response_body_str, ",", ct_threshold_value_start);
      if (ct_threshold_value_end == -1) ct_threshold_value_end = StringFind(response_body_str, "}", ct_threshold_value_start);
      if (ct_threshold_value_end != -1) {
         string ct_threshold_str = StringSubstr(response_body_str, ct_threshold_value_start, ct_threshold_value_end - ct_threshold_value_start);
         StringTrimLeft(ct_threshold_str);
         StringTrimRight(ct_threshold_str);
         received_counter_trend_threshold = StringToDouble(ct_threshold_str);
      }
   }

   // Parse counter_trend_nbars
   int ct_nbars_key_start = StringFind(response_body_str, "\"counter_trend_nbars\":");
   if (ct_nbars_key_start != -1) {
      int ct_nbars_value_start = ct_nbars_key_start + StringLen("\"counter_trend_nbars\":");
      int ct_nbars_value_end = StringFind(response_body_str, ",", ct_nbars_value_start);
      if (ct_nbars_value_end == -1) ct_nbars_value_end = StringFind(response_body_str, "}", ct_nbars_value_start);
      if (ct_nbars_value_end != -1) {
         string ct_nbars_str = StringSubstr(response_body_str, ct_nbars_value_start, ct_nbars_value_end - ct_nbars_value_start);
         StringTrimLeft(ct_nbars_str);
         StringTrimRight(ct_nbars_str);
         received_counter_trend_nbars = (int)StringToInteger(ct_nbars_str);
      }
   }

   // *** Parse confidence ของทั้ง 2 ระบบ ***
   // Parse trend_following_buy_confidence
   int tf_buy_conf_key_start = StringFind(response_body_str, "\"trend_following_buy_confidence\":");
   if (tf_buy_conf_key_start != -1) {
      int tf_buy_conf_value_start = tf_buy_conf_key_start + StringLen("\"trend_following_buy_confidence\":");
      int tf_buy_conf_value_end = StringFind(response_body_str, ",", tf_buy_conf_value_start);
      if (tf_buy_conf_value_end == -1) tf_buy_conf_value_end = StringFind(response_body_str, "}", tf_buy_conf_value_start);
      if (tf_buy_conf_value_end != -1) {
         string tf_buy_conf_str = StringSubstr(response_body_str, tf_buy_conf_value_start, tf_buy_conf_value_end - tf_buy_conf_value_start);
         StringTrimLeft(tf_buy_conf_str);
         StringTrimRight(tf_buy_conf_str);
         received_trend_following_buy_confidence = StringToDouble(tf_buy_conf_str);
      }
   }

   // Parse trend_following_sell_confidence
   int tf_sell_conf_key_start = StringFind(response_body_str, "\"trend_following_sell_confidence\":");
   if (tf_sell_conf_key_start != -1) {
      int tf_sell_conf_value_start = tf_sell_conf_key_start + StringLen("\"trend_following_sell_confidence\":");
      int tf_sell_conf_value_end = StringFind(response_body_str, ",", tf_sell_conf_value_start);
      if (tf_sell_conf_value_end == -1) tf_sell_conf_value_end = StringFind(response_body_str, "}", tf_sell_conf_value_start);
      if (tf_sell_conf_value_end != -1) {
         string tf_sell_conf_str = StringSubstr(response_body_str, tf_sell_conf_value_start, tf_sell_conf_value_end - tf_sell_conf_value_start);
         StringTrimLeft(tf_sell_conf_str);
         StringTrimRight(tf_sell_conf_str);
         received_trend_following_sell_confidence = StringToDouble(tf_sell_conf_str);
      }
   }

   // Parse counter_trend_buy_confidence
   int ct_buy_conf_key_start = StringFind(response_body_str, "\"counter_trend_buy_confidence\":");
   if (ct_buy_conf_key_start != -1) {
      int ct_buy_conf_value_start = ct_buy_conf_key_start + StringLen("\"counter_trend_buy_confidence\":");
      int ct_buy_conf_value_end = StringFind(response_body_str, ",", ct_buy_conf_value_start);
      if (ct_buy_conf_value_end == -1) ct_buy_conf_value_end = StringFind(response_body_str, "}", ct_buy_conf_value_start);
      if (ct_buy_conf_value_end != -1) {
         string ct_buy_conf_str = StringSubstr(response_body_str, ct_buy_conf_value_start, ct_buy_conf_value_end - ct_buy_conf_value_start);
         StringTrimLeft(ct_buy_conf_str);
         StringTrimRight(ct_buy_conf_str);
         received_counter_trend_buy_confidence = StringToDouble(ct_buy_conf_str);
      }
   }

   // Parse counter_trend_sell_confidence
   int ct_sell_conf_key_start = StringFind(response_body_str, "\"counter_trend_sell_confidence\":");
   if (ct_sell_conf_key_start != -1) {
      int ct_sell_conf_value_start = ct_sell_conf_key_start + StringLen("\"counter_trend_sell_confidence\":");
      int ct_sell_conf_value_end = StringFind(response_body_str, ",", ct_sell_conf_value_start);
      if (ct_sell_conf_value_end == -1) ct_sell_conf_value_end = StringFind(response_body_str, "}", ct_sell_conf_value_start);
      if (ct_sell_conf_value_end != -1) {
         string ct_sell_conf_str = StringSubstr(response_body_str, ct_sell_conf_value_start, ct_sell_conf_value_end - ct_sell_conf_value_start);
         StringTrimLeft(ct_sell_conf_str);
         StringTrimRight(ct_sell_conf_str);
         received_counter_trend_sell_confidence = StringToDouble(ct_sell_conf_str);
      }
   }

   // แปลง timestamp ที่ได้รับมา เป็น String วันที่และเวลา
   string bar_timestamp_str = TimeToString(received_bar_timestamp, TIME_DATE|TIME_MINUTES);
   string signal_bar_timestamp_str = TimeToString(received_signal_bar_timestamp, TIME_DATE|TIME_MINUTES);
   
   // แก้ไข Print Format เพื่อแสดงค่า Timestamp ที่แปลงเป็น String แล้ว พร้อม Symbol และ Timeframe ที่ได้รับ
   PrintFormat("%s %s: Parsed Response - Signal: '%s', Confidence: %.4f, SL: %.5f, TP: %.5f, Response Symbol: '%s', Response Timeframe: '%s', Bar Time: %s, Signal Bar Time: %s",
                  Symbol(), Period(),
                  received_signal, received_confidence, received_sl_price, received_tp_price,
                  received_response_symbol, received_response_timeframe_str,
                  bar_timestamp_str, signal_bar_timestamp_str);
                 
   // --- นำค่า Signal และ Confidence ที่ได้ ไปใช้ใน Logic การเทรดของคุณ ---
   // ค่า received_signal และ received_confidence ถูกอัปเดตแล้วในฟังก์ชันนี้
   
   // --- *** สำคัญ: เรียกฟังก์ชันอัปเดตการแสดงผลบน Chart ที่นี่ *** ---
   // ใช้ฟังก์ชันแสดงผลรวมแบบใหม่ พร้อมข้อมูล Multi-Model Analysis

   // *** ปรับปรุง: ใช้ threshold และ nBars_SL ที่เหมาะสมตาม scenario ปัจจุบัน ***
   double current_threshold = GetRequiredThreshold(received_signal);
   int current_nbars_sl = GetRequiredNBarsSL(received_signal);

   UpdateCombinedDisplay(received_signal, received_class, received_confidence, received_response_symbol, received_response_timeframe_str,
                        received_best_entry, current_nbars_sl, current_threshold, received_time_filters, received_spread,
                        FXMagic, FXSpread, FXPV, received_market_condition, received_action_type, received_scenario_used);

   return true; // ส่งสำเร็จและประมวลผล response ได้
}
//+------------------------------------------------------------------+
void SendTelegram(string message)
{
   string url = "https://api.telegram.org/bot" + TelegramBotToken + "/sendMessage";
   string data = "chat_id=" + ChatID + "&text=" + MessageText;
   
   char post[];
   StringToCharArray(data, post);
   
   char result[];
   string headers;
   int timeout = 5000;
   
   ResetLastError();
   string result_headers = "";
   int res = WebRequest("POST", url, headers, timeout, post, result, result_headers);
   
   if (res == -1) {
      Print("Failed to send message: ", GetLastError());
   } else {
      Print("Message sent successfully :", CharArrayToString(result));
   }
}
//+------------------------------------------------------------------+
double GetEMA50(string symbol, ENUM_TIMEFRAMES timeframe, int shift) {
   int ema_handle = iMA(symbol, timeframe, 50, 0, MODE_EMA, PRICE_CLOSE);
   if (ema_handle == INVALID_HANDLE) {
      Print("❌ Failed to create EMA handle");
      return -1;
   }

   double ema[];
   if (CopyBuffer(ema_handle, 0, shift, 1, ema) <= 0) {
      Print("❌ Failed to get EMA50 data");
      return -1;
   }

   return ema[0];
}
//+------------------------------------------------------------------+
double GetRSI14(string symbol, ENUM_TIMEFRAMES timeframe, int shift) {
   int rsi_handle = iRSI(symbol, timeframe, 14, PRICE_CLOSE);
   if (rsi_handle == INVALID_HANDLE) {
      Print("❌ Failed to create RSI handle");
      return -1;
   }

   double rsi[];
   if (CopyBuffer(rsi_handle, 0, shift, 1, rsi) <= 0) {
      Print("❌ Failed to get RSI14 data");
      return -1;
   }

   return rsi[0];
}
//+------------------------------------------------------------------+
//| Open Order function                                             |
//+------------------------------------------------------------------+
// ฟังก์ชัน: ทดสอบหา filling mode ที่ใช้ได้กับ symbol ปัจจุบัน
int GetSupportedFillingMode(string symbol, int magic)
{
   MqlTradeRequest test_request;
   MqlTradeResult test_result;
   ZeroMemory(test_request);
   ZeroMemory(test_result);
   
   double price = SymbolInfoDouble(symbol, SYMBOL_BID);
   test_request.action   = TRADE_ACTION_DEAL;
   test_request.symbol   = symbol;
   test_request.volume   = 0.0; // ไม่เปิด order จริง
   test_request.type     = ORDER_TYPE_BUY;
   test_request.price    = price;
   test_request.magic    = magic;
   test_request.deviation= 5;
   test_request.comment  = "Test filling mode";
   
   int try_modes[] = {ORDER_FILLING_IOC, ORDER_FILLING_FOK, ORDER_FILLING_RETURN};
   for(int i = 0; i < ArraySize(try_modes); i++)
   {
      test_request.type_filling = try_modes[i];
      if(OrderSend(test_request, test_result) && test_result.retcode == TRADE_RETCODE_DONE)
      {
         PrintFormat("✅ Found supported filling mode: %d", try_modes[i]);
         return try_modes[i];
     }
   }
  
   Print("⚠️ No supported filling mode worked. Defaulting to IOC.");
   return ORDER_FILLING_IOC;
}
//+------------------------------------------------------------------+
bool SendBuyOrder(string symbol, double confidence, double lot, int magic)
{
   Setup_Magic(); //FXMagic
   Get_Order();

   // *** ใช้ threshold ที่เหมาะสมตาม scenario แทนค่าคงที่ 0.50 ***
   double required_threshold = GetRequiredThreshold("BUY");

   if(confidence <= required_threshold || order_count_buy > 0.0) {
      Print("Order Buy : Failed to open order : confidence ",DoubleToString(confidence,3)," required_threshold ",DoubleToString(required_threshold,3)," order_count_buy ",DoubleToString(order_count_buy,0));
      return false;
   }
   else {Print("Order Buy : open order : confidence ",DoubleToString(confidence,3)," required_threshold ",DoubleToString(required_threshold,3)," order_count_buy ",DoubleToString(order_count_buy,0));}

   //Print("symbol ", symbol, " signal BUY confidence ", confidence);

   MqlTradeRequest request;
   MqlTradeResult result;
   ZeroMemory(request);
   ZeroMemory(result);

   int filling_mode = GetSupportedFillingMode(symbol, magic);

   double order_open_buy = SymbolInfoDouble(symbol,SYMBOL_ASK);
   double point = SymbolInfoDouble(symbol,SYMBOL_POINT);
   double stops_level = (double)(SymbolInfoInteger(symbol,SYMBOL_TRADE_STOPS_LEVEL));
   int digit = (int)(SymbolInfoInteger(symbol,SYMBOL_DIGITS));
   //Print(" BuyOrder : Ask ",DoubleToString(order_open_buy,digit)," Stop ",stops_level," Point ",point," digit ",digit," Stop*Point ",DoubleToString(stops_level*point,digit)," Bid-Stop*Point ",DoubleToString(pricr_bid-stops_level*point,digit));

   double balance  = AccountInfoDouble(ACCOUNT_BALANCE);
   double equity   = AccountInfoDouble(ACCOUNT_EQUITY);
   double capital  = MathMin(balance, equity);
   double lot_min  = SymbolInfoDouble(_Symbol,SYMBOL_VOLUME_MIN);
   double lot_max  = SymbolInfoDouble(_Symbol,SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(_Symbol,SYMBOL_VOLUME_STEP);
   
   double stopsize = (order_open_buy-received_sl_price)/Point();
   double lot_cal  = (capital*(Order_risk/100))/stopsize;
   Order_Lot_cal = NormalizeDouble(MathFloor(MathMin(MathMax(lot_cal,lot_min),lot_max)/lot_step)*lot_step,2);
   Print("OP ",DoubleToString(order_open_buy,Digits())," SL ",DoubleToString(received_sl_price,Digits())," TP ",DoubleToString(received_tp_price,Digits())," Size ",DoubleToString(stopsize,2));
   PrintFormat("Balance: %.2f | Equity: %.2f | Capital %.2f | risk %0.2f pv %.3f | Lot min:max:step %.3f %.3f %.3f | %.3f %.3f", balance, equity, capital, Order_risk, FXPV, lot_min, lot_max, lot_step, lot_cal, Order_Lot_cal);

   request.action        = TRADE_ACTION_DEAL;
   request.symbol        = symbol;
   request.volume        = Order_Lot_cal;
   request.type          = ORDER_TYPE_BUY;
   request.price         = order_open_buy;
   request.type_filling  = filling_mode;
   request.magic         = magic;
   request.deviation     = 5;
   request.comment       = "Python Buy";

   if (received_sl_price > 0) {
      // *** สำคัญ: ตรวจสอบและปรับค่า SL ตามกฎของ Broker (SYMBOL_TRADE_STOPS_LEVEL) ***
      //double min_sl_distance = SymbolInfoDouble(symbol, SYMBOL_TRADE_STOPS_LEVEL) * SymbolInfoDouble(symbol, SYMBOL_POINT);
      
      double min_sl_distance = stops_level * point;
      double adjusted_sl = received_sl_price;
      
      // สำหรับ BUY, SL ต้องต่ำกว่า Entry Price อย่างน้อย min_sl_distance
      // ถ้า received_sl_price อยู่ในระยะที่ห้าม หรือสูงกว่า Entry Price
      if (order_open_buy - received_sl_price < min_sl_distance || received_sl_price >= order_open_buy) {
        // ปรับ SL ให้ต่ำกว่าราคาปัจจุบัน + min_sl_distance
        adjusted_sl = order_open_buy - min_sl_distance;
        PrintFormat("%s: Adjusting BUY SL from %.5f to %.5f due to min_sl_distance or invalid level.", Symbol(), received_sl_price, adjusted_sl);
      }
      
      // ตรวจสอบอีกครั้งเผื่อค่าที่ปรับแล้วยังไม่ถูกต้อง (เช่น ต่ำกว่า 0 หรือสูงกว่า Entry Price)
      if (adjusted_sl <= 0 || adjusted_sl >= order_open_buy) {
         request.sl = 0; // ไม่ใส่ SL ถ้าค่าที่ปรับแล้วยังไม่ถูกต้อง
         PrintFormat("%s: Adjusted BUY SL %.5f is still invalid. Not setting SL.", Symbol(), adjusted_sl);
      } else {
         request.sl = adjusted_sl; // กำหนด SL ที่ปรับแล้ว
      }
   } else {
      request.sl = 0; // ไม่ใส่ SL ถ้าค่าที่ได้รับเป็น 0
      PrintFormat("%s: Received BUY SL price 0. Not setting SL.", Symbol());
   }

   if (received_tp_price > 0) {
      // *** สำคัญ: ตรวจสอบและปรับค่า TP ตามกฎของ Broker (SYMBOL_TRADE_STOPS_LEVEL) ***
      //double min_tp_distance = SymbolInfoDouble(symbol, SYMBOL_TRADE_STOPS_LEVEL) * SymbolInfoDouble(symbol, SYMBOL_POINT);
      
      double min_tp_distance = stops_level * point;
      double adjusted_tp = received_tp_price;
      
      // สำหรับ BUY, TP ต้องสูงกว่า Entry Price อย่างน้อย min_tp_distance
      if (received_tp_price - order_open_buy < min_tp_distance || received_tp_price <= order_open_buy) {
         // ปรับ TP ให้สูงกว่าราคาปัจจุบัน + min_tp_distance
         adjusted_tp = order_open_buy + min_tp_distance;
         PrintFormat("%s: Adjusting BUY TP from %.5f to %.5f due to min_tp_distance or invalid level.", Symbol(), received_tp_price, adjusted_tp);
      }
      
      // ตรวจสอบอีกครั้งเผื่อค่าที่ปรับแล้วยังไม่ถูกต้อง (เช่น สูงกว่า max double หรือต่ำกว่า Entry Price)
      if (adjusted_tp <= 0 || adjusted_tp <= order_open_buy) {
         request.tp = 0; // ไม่ใส่ TP ถ้าค่าที่ปรับแล้วยังไม่ถูกต้อง
         PrintFormat("%s: Adjusted BUY TP %.5f is still invalid. Not setting TP.", Symbol(), adjusted_tp);
      } else {
         request.tp = adjusted_tp; // กำหนด TP ที่ปรับแล้ว
      }
   }
   else {
      request.tp = 0; // ไม่ใส่ TP ถ้าค่าที่ได้รับเป็น 0
      PrintFormat("%s: Received BUY TP price 0. Not setting TP.", Symbol());
   }

   bool success = OrderSend(request, result);

   if (success && result.retcode == TRADE_RETCODE_DONE) {
      PrintFormat("✅ Successfully sent Buy order for %s. Deal: %I64d, Order: %I64d (Confidence: %.2f).",
                  symbol, result.deal, result.order, confidence);

      MessageText = "MT5 Buy : " + symbol + " " + DoubleToString(magic,0) + " " + DoubleToString(confidence,3);
      SendTelegram(MessageText);
      return true;
   } else {
      PrintFormat("❌ Failed to send Buy order for %s. Retcode: %d, Confidence: %.2f. Comment: %s",
                  symbol, result.retcode, confidence, result.comment);
      return false;
   }
}
//+------------------------------------------------------------------+
//void SendSellOrder(string symbol, double confidence, double lot, int magic) {}
bool SendSellOrder(string symbol, double confidence, double lot, int magic)
{
   Get_Order();

   // *** ใช้ threshold ที่เหมาะสมตาม scenario แทนค่าคงที่ 0.50 ***
   double required_threshold = GetRequiredThreshold("SELL");

   if(confidence <= required_threshold || order_count_sell > 0.0) {
      Print("Order Sell : Failed to open order : confidence ",DoubleToString(confidence,3)," required_threshold ",DoubleToString(required_threshold,3)," order_count_sell ",DoubleToString(order_count_sell,0));
      return false;
   } else {Print("Order Sell : open order : confidence ",DoubleToString(confidence,3)," required_threshold ",DoubleToString(required_threshold,3)," order_count_sell ",DoubleToString(order_count_sell,0));}

   //Print("symbol ", symbol, " signal SELL confidence ", confidence);

   MqlTradeRequest request;
   MqlTradeResult result;
   ZeroMemory(request);
   ZeroMemory(result);

   int filling_mode = GetSupportedFillingMode(symbol, magic);

   double order_open_sell = SymbolInfoDouble(symbol,SYMBOL_BID);
   double point = SymbolInfoDouble(symbol,SYMBOL_POINT);
   double stops_level = (double)(SymbolInfoInteger(symbol,SYMBOL_TRADE_STOPS_LEVEL));
   int digit = (int)(SymbolInfoInteger(symbol,SYMBOL_DIGITS));
   //Print(" Bid ",DoubleToString(order_open_sell,digit)," Stop ",stops_level," Point ",point," digit ",digit," Stop*Point ",DoubleToString(stops_level*point,digit)," Bid-Stop*Point ",DoubleToString(pricr_bid-stops_level*point,digit));

   double balance  = AccountInfoDouble(ACCOUNT_BALANCE);
   double equity   = AccountInfoDouble(ACCOUNT_EQUITY);
   double capital  = MathMin(balance, equity);
   double lot_min  = SymbolInfoDouble(_Symbol,SYMBOL_VOLUME_MIN);
   double lot_max  = SymbolInfoDouble(_Symbol,SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(_Symbol,SYMBOL_VOLUME_STEP);
   
   double stopsize = (received_sl_price-order_open_sell)/Point();
   double lot_cal  = (capital*(Order_risk/100))/stopsize;
   Order_Lot_cal = NormalizeDouble(MathFloor(MathMin(MathMax(lot_cal,lot_min),lot_max)/lot_step)*lot_step,2);
   Print("OP ",DoubleToString(order_open_sell,Digits())," SL ",DoubleToString(received_sl_price,Digits())," TP ",DoubleToString(received_tp_price,Digits())," Size ",DoubleToString(stopsize,2));
   PrintFormat("Balance: %.2f | Equity: %.2f | Capital %.2f | risk %0.2f pv %.3f | Lot min:max:step %.3f %.3f %.3f | %.3f %.3f", balance, equity, capital, Order_risk, FXPV, lot_min, lot_max, lot_step, lot_cal, Order_Lot_cal);

   request.action        = TRADE_ACTION_DEAL;
   request.symbol        = symbol;
   request.volume        = Order_Lot_cal;
   request.type          = ORDER_TYPE_SELL;
   request.price         = order_open_sell;
   request.type_filling  = filling_mode;
   request.magic         = magic;
   request.deviation     = 5;
   request.comment       = "Python Sell";

   if (received_sl_price > 0) {
      // *** สำคัญ: ตรวจสอบและปรับค่า SL ตามกฎของ Broker (SYMBOL_TRADE_STOPS_LEVEL) ***
      
      double min_sl_distance = stops_level * point;
      double adjusted_sl = received_sl_price;
      
      // สำหรับ SELL, SL ต้องสูงกว่า Entry Price อย่างน้อย min_sl_distance
      // ถ้า received_sl_price อยู่ในระยะที่ห้าม หรือต่ำกว่า Entry Price
      if (received_sl_price - order_open_sell < min_sl_distance || received_sl_price <= order_open_sell)
      {
         // ปรับ SL ให้สูงกว่าราคาปัจจุบัน + min_sl_distance
         adjusted_sl = order_open_sell + min_sl_distance;
         PrintFormat("%s: Adjusting SELL SL from %.5f to %.5f due to min_sl_distance or invalid level.", Symbol(), received_sl_price, adjusted_sl);
      }
      
      // ตรวจสอบอีกครั้งเผื่อค่าที่ปรับแล้วยังไม่ถูกต้อง (เช่น ต่ำกว่า Entry Price)
      if (adjusted_sl <= order_open_sell) {
         request.sl = 0; // ไม่ใส่ SL ถ้าค่าที่ปรับแล้วยังไม่ถูกต้อง
         PrintFormat("%s: Adjusted SELL SL %.5f is still invalid. Not setting SL.", Symbol(), adjusted_sl);
      } else {
         request.sl = adjusted_sl; // กำหนด SL ที่ปรับแล้ว
      }
   } else {
      request.sl = 0; // ไม่ใส่ SL ถ้าค่าที่ได้รับเป็น 0
      PrintFormat("%s: Received SELL SL price 0. Not setting SL.", Symbol());
   }

   if (received_tp_price > 0)
   {
      // *** สำคัญ: ตรวจสอบและปรับค่า TP ตามกฎของ Broker (SYMBOL_TRADE_STOPS_LEVEL) ***
      double min_tp_distance = stops_level * point;
      double adjusted_tp = received_tp_price;
      
      // สำหรับ SELL, TP ต้องต่ำกว่า Entry Price อย่างน้อย min_tp_distance
      if (order_open_sell - received_tp_price < min_tp_distance || received_tp_price >= order_open_sell) {
        // ปรับ TP ให้ต่ำกว่าราคาปัจจุบัน - min_tp_distance
        adjusted_tp = order_open_sell - min_tp_distance;
        PrintFormat("%s: Adjusting SELL TP from %.5f to %.5f due to min_tp_distance or invalid level.", Symbol(), received_tp_price, adjusted_tp);
      }
      
      // ตรวจสอบอีกครั้งเผื่อค่าที่ปรับแล้วยังไม่ถูกต้อง (เช่น สูงกว่า Entry Price)
      if (adjusted_tp >= order_open_sell) {
        request.tp = 0; // ไม่ใส่ TP ถ้าค่าที่ปรับแล้วยังไม่ถูกต้อง
        PrintFormat("%s: Adjusted SELL TP %.5f is still invalid. Not setting TP.", Symbol(), adjusted_tp);
      } else {
        request.tp = adjusted_tp; // กำหนด TP ที่ปรับแล้ว
      }
   } else {
      request.tp = 0; // ไม่ใส่ TP ถ้าค่าที่ได้รับเป็น 0
      PrintFormat("%s: Received SELL TP price 0. Not setting TP.", Symbol());
   }

   bool success = OrderSend(request, result);

   if (success && result.retcode == TRADE_RETCODE_DONE)
   {
      PrintFormat("✅ Successfully sent Sell order for %s. Deal: %I64d, Order: %I64d (Confidence: %.2f).",
                  symbol, result.deal, result.order, confidence);

      MessageText = "MT5 Sell : " + symbol + " " + DoubleToString(magic,0) + " " + DoubleToString(confidence,3);
      SendTelegram(MessageText);
      return true;
   }
   else
   {
      PrintFormat("❌ Failed to send Sell order for %s. Retcode: %d, Confidence: %.2f. Comment: %s",
                  symbol, result.retcode, confidence, result.comment);
      return false;
   }
}
//+------------------------------------------------------------------+
bool ShouldExitBuy(double close, double ema50, double rsi14, int rsi_level_out, bool has_ema, bool has_rsi) 
{
   if ((has_ema && close < ema50) || (has_rsi && rsi14 < rsi_level_out)) {
       return true;
   }
   return false;
}
//+------------------------------------------------------------------+
bool ShouldExitSell(double close, double ema50, double rsi14, int rsi_level_out, bool has_ema, bool has_rsi) {
   if ((has_ema && close > ema50) || (has_rsi && rsi14 > (100 - rsi_level_out))) {
       return true;
   }
   return false;
}
//+------------------------------------------------------------------+
void CloseBuyOrder(string symbol, int magic) {
   ulong ticket;
   double volume;
   double bid_price = SymbolInfoDouble(symbol, SYMBOL_BID);
   int total = PositionsTotal();
   bool found = false;

   for (int i = 0; i < total; i++) {
      if (PositionGetSymbol(i) == symbol && PositionGetInteger(POSITION_MAGIC) == magic && PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) {
         ticket = PositionGetInteger(POSITION_TICKET);
         volume = PositionGetDouble(POSITION_VOLUME);
         found = true;
         break;
      }
   }

   if (!found) {
      PrintFormat("⚠️ No open BUY position found for %s with magic %d.", symbol, magic);
      return;
   }

   MqlTradeRequest request;
   MqlTradeResult  result;
   ZeroMemory(request);
   ZeroMemory(result);

   int filling_mode = GetSupportedFillingMode(symbol, magic);

   request.action       = TRADE_ACTION_DEAL;
   request.symbol       = symbol;
   request.position     = ticket;
   request.volume       = volume;
   request.price        = bid_price;
   request.type         = ORDER_TYPE_SELL; // ปิด BUY = SELL
   request.magic        = magic;
   request.type_filling = filling_mode;
   request.deviation    = 5;
   request.comment      = "Close Buy";

   bool success = OrderSend(request, result);

   if (success && result.retcode == TRADE_RETCODE_DONE) {
      PrintFormat("✅ Successfully closed BUY order for %s. Deal: %I64d, Volume: %.2f", symbol, result.deal, volume);
      MessageText = "MT5 Close BUY : " + symbol + " " + DoubleToString(magic, 0);
      SendTelegram(MessageText);
   } else {
      PrintFormat("❌ Failed to close BUY order for %s. Retcode: %d, Comment: %s", symbol, result.retcode, result.comment);
   }
}
//+------------------------------------------------------------------+
void CloseSellOrder(string symbol, int magic) {
   ulong ticket;
   double volume;
   double ask_price = SymbolInfoDouble(symbol, SYMBOL_ASK);
   int total = PositionsTotal();
   bool found = false;

   for (int i = 0; i < total; i++) {
      if (PositionGetSymbol(i) == symbol && PositionGetInteger(POSITION_MAGIC) == magic && PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL) {
         ticket = PositionGetInteger(POSITION_TICKET);
         volume = PositionGetDouble(POSITION_VOLUME);
         found = true;
         break;
      }
   }

   if (!found) {
      PrintFormat("⚠️ No open SELL position found for %s with magic %d.", symbol, magic);
      return;
   }

   MqlTradeRequest request;
   MqlTradeResult  result;
   ZeroMemory(request);
   ZeroMemory(result);

   int filling_mode = GetSupportedFillingMode(symbol, magic);

   request.action       = TRADE_ACTION_DEAL;
   request.symbol       = symbol;
   request.position     = ticket;
   request.volume       = volume;
   request.price        = ask_price;
   request.type         = ORDER_TYPE_BUY; // ปิด SELL = BUY
   request.magic        = magic;
   request.type_filling = filling_mode;
   request.deviation    = 5;
   request.comment      = "Close Sell";

   bool success = OrderSend(request, result);

   if (success && result.retcode == TRADE_RETCODE_DONE) {
      PrintFormat("✅ Successfully closed SELL order for %s. Deal: %I64d, Volume: %.2f", symbol, result.deal, volume);
      MessageText = "MT5 Close SELL : " + symbol + " " + DoubleToString(magic, 0);
      SendTelegram(MessageText);
   } else {
      PrintFormat("❌ Failed to close SELL order for %s. Retcode: %d, Comment: %s", symbol, result.retcode, result.comment);
   }
}
//+------------------------------------------------------------------+
//| ฟังก์ชันตรวจสอบ Scenario และ Threshold ที่เหมาะสม                 |
//+------------------------------------------------------------------+
bool ValidateScenarioAndThreshold(string signal, double confidence, string scenario, string market_condition)
{
   Print("🔍 Validating Scenario and Threshold...");
   Print("   Signal: ", signal);
   Print("   Confidence: ", DoubleToString(confidence, 4));
   Print("   Scenario: ", scenario);
   Print("   Market: ", market_condition);

   // ตรวจสอบข้อมูลพื้นฐาน
   if(signal != "BUY" && signal != "SELL") {
      Print("❌ Invalid signal: ", signal);
      return false;
   }

   if(scenario == "" || scenario == "none" || scenario == "N/A") {
      Print("❌ No scenario specified");
      return false;
   }

   // *** ปรับปรุง: ใช้ระบบ threshold ที่ยืดหยุ่นมากขึ้น ***
   double required_threshold = GetRequiredThreshold(signal);
   double scenario_confidence = confidence; // ใช้ confidence หลักที่ได้รับมา

   // ดึงข้อมูล confidence เฉพาะของแต่ละ scenario เพื่อการแสดงผล
   double specific_scenario_confidence = 0.0;
   if(scenario == "trend_following") {
      if(signal == "BUY") {
         specific_scenario_confidence = received_trend_following_buy_confidence;
      } else {
         specific_scenario_confidence = received_trend_following_sell_confidence;
      }
   }
   else if(scenario == "counter_trend") {
      if(signal == "BUY") {
         specific_scenario_confidence = received_counter_trend_buy_confidence;
      } else {
         specific_scenario_confidence = received_counter_trend_sell_confidence;
      }
   }

   Print("   Required Threshold: ", DoubleToString(required_threshold, 4));
   Print("   Main Confidence: ", DoubleToString(confidence, 4));
   Print("   Specific Scenario Confidence: ", DoubleToString(specific_scenario_confidence, 4));

   // ตรวจสอบ confidence หลัก (ที่ Python ส่งมา) กับ threshold
   if(confidence < required_threshold) {
      Print("❌ Main confidence (", DoubleToString(confidence, 4),
            ") below required threshold (", DoubleToString(required_threshold, 4), ")");
      return false;
   }

   // ตรวจสอบเพิ่มเติม: specific scenario confidence ควรใกล้เคียงกับ main confidence
   if(specific_scenario_confidence > 0.0) {
      double confidence_diff = MathAbs(confidence - specific_scenario_confidence);
      if(confidence_diff > 0.1) { // ต่างกันเกิน 0.1
         Print("⚠️ Large confidence difference detected: Main=", DoubleToString(confidence, 4),
               " Specific=", DoubleToString(specific_scenario_confidence, 4), " Diff=", DoubleToString(confidence_diff, 4));
      }
   }

   // ตรวจสอบความสอดคล้องของ scenario กับ market condition
   bool scenario_market_match = ValidateScenarioMarketMatch(signal, scenario, market_condition);
   if(!scenario_market_match) {
      Print("❌ Scenario-Market mismatch detected");
      return false;
   }

   // ตรวจสอบว่ามี order ประเภทเดียวกันอยู่แล้วหรือไม่
   Get_Order();
   if(signal == "BUY" && order_count_buy > 0) {
      Print("❌ BUY order already exists (", order_count_buy, ")");
      return false;
   }
   if(signal == "SELL" && order_count_sell > 0) {
      Print("❌ SELL order already exists (", order_count_sell, ")");
      return false;
   }

   Print("✅ Scenario validation passed");
   return true;
}

//+------------------------------------------------------------------+
//| ฟังก์ชันคืนค่า Threshold ที่เหมาะสมตาม Scenario ปัจจุบัน          |
//+------------------------------------------------------------------+
double GetRequiredThreshold(string signal_type)
{
   Print("🔍 Getting required threshold for signal: ", signal_type);
   Print("   Current scenario: ", received_scenario_used);

   // ถ้าไม่มี scenario ที่ชัดเจน ใช้ threshold ที่ต่ำกว่าระหว่าง 2 scenario
   if(received_scenario_used == "" || received_scenario_used == "none" || received_scenario_used == "N/A")
   {
      double min_threshold = MathMin(received_trend_following_threshold, received_counter_trend_threshold);
      Print("   No specific scenario - using minimum threshold: ", DoubleToString(min_threshold, 4));
      return min_threshold;
   }

   double required_threshold = 0.50; // default fallback

   // เลือก threshold ตาม scenario ที่ใช้
   if(received_scenario_used == "trend_following")
   {
      required_threshold = received_trend_following_threshold;
      Print("   Using trend_following threshold: ", DoubleToString(required_threshold, 4));
   }
   else if(received_scenario_used == "counter_trend")
   {
      required_threshold = received_counter_trend_threshold;
      Print("   Using counter_trend threshold: ", DoubleToString(required_threshold, 4));
   }
   else
   {
      // กรณีที่ไม่รู้จัก scenario ให้ใช้ threshold ที่สูงกว่าเพื่อความปลอดภัย
      required_threshold = MathMax(received_trend_following_threshold, received_counter_trend_threshold);
      Print("   Unknown scenario (", received_scenario_used, ") - using maximum threshold: ", DoubleToString(required_threshold, 4));
   }

   // ตรวจสอบค่าที่ได้ว่าสมเหตุสมผลหรือไม่
   if(required_threshold <= 0.0 || required_threshold > 1.0)
   {
      Print("⚠️ Invalid threshold detected: ", DoubleToString(required_threshold, 4), " - using fallback 0.50");
      required_threshold = 0.50;
   }

   Print("   Final required threshold: ", DoubleToString(required_threshold, 4));
   return required_threshold;
}

//+------------------------------------------------------------------+
//| ฟังก์ชันคืนค่า nBars_SL ที่เหมาะสมตาม Scenario ปัจจุบัน           |
//+------------------------------------------------------------------+
int GetRequiredNBarsSL(string signal_type)
{
   Print("🔍 Getting required nBars_SL for signal: ", signal_type);
   Print("   Current scenario: ", received_scenario_used);

   // ถ้าไม่มี scenario ที่ชัดเจน ใช้ nBars_SL ที่ต่ำกว่าระหว่าง 2 scenario
   if(received_scenario_used == "" || received_scenario_used == "none" || received_scenario_used == "N/A")
   {
      int min_nbars = MathMin(received_trend_following_nbars, received_counter_trend_nbars);
      Print("   No specific scenario - using minimum nBars_SL: ", min_nbars);
      return min_nbars;
   }

   int required_nbars = 6; // default fallback

   // เลือก nBars_SL ตาม scenario ที่ใช้
   if(received_scenario_used == "trend_following")
   {
      required_nbars = received_trend_following_nbars;
      Print("   Using trend_following nBars_SL: ", required_nbars);
   }
   else if(received_scenario_used == "counter_trend")
   {
      required_nbars = received_counter_trend_nbars;
      Print("   Using counter_trend nBars_SL: ", required_nbars);
   }
   else
   {
      // กรณีที่ไม่รู้จัก scenario ให้ใช้ nBars_SL ที่สูงกว่าเพื่อความปลอดภัย
      required_nbars = MathMax(received_trend_following_nbars, received_counter_trend_nbars);
      Print("   Unknown scenario (", received_scenario_used, ") - using maximum nBars_SL: ", required_nbars);
   }

   // ตรวจสอบค่าที่ได้ว่าสมเหตุสมผลหรือไม่
   if(required_nbars <= 0 || required_nbars > 50)
   {
      Print("⚠️ Invalid nBars_SL detected: ", required_nbars, " - using fallback 6");
      required_nbars = 6;
   }

   Print("   Final required nBars_SL: ", required_nbars);
   return required_nbars;
}

//+------------------------------------------------------------------+
//| ฟังก์ชันตรวจสอบความสอดคล้องของ Scenario กับ Market Condition      |
//+------------------------------------------------------------------+
bool ValidateScenarioMarketMatch(string signal, string scenario, string market_condition)
{
   Print("🔄 Checking scenario-market match...");

   // กรณี uptrend
   if(market_condition == "uptrend") {
      if(scenario == "trend_following" && signal == "BUY") {
         Print("✅ Uptrend + Trend Following BUY = Valid");
         return true;
      }
      if(scenario == "counter_trend" && signal == "SELL") {
         Print("✅ Uptrend + Counter Trend SELL = Valid");
         return true;
      }
   }
   // กรณี downtrend
   else if(market_condition == "downtrend") {
      if(scenario == "trend_following" && signal == "SELL") {
         Print("✅ Downtrend + Trend Following SELL = Valid");
         return true;
      }
      if(scenario == "counter_trend" && signal == "BUY") {
         Print("✅ Downtrend + Counter Trend BUY = Valid");
         return true;
      }
   }
   // กรณี sideways - อนุญาทุกแบบ
   else if(market_condition == "sideways" || market_condition == "unknown") {
      Print("✅ Sideways/Unknown market - All scenarios allowed");
      return true;
   }

   Print("⚠️ Scenario-Market mismatch: ", scenario, " ", signal, " in ", market_condition);
   return false; // ไม่ตรงตามกฎ
}

//+------------------------------------------------------------------+
//| ฟังก์ชันตรวจสอบ Time Filters                                      |
//+------------------------------------------------------------------+
bool CheckTimeFilters(string time_filters)
{
   if(StringLen(time_filters) == 0) return true;

   datetime current_time = TimeCurrent();
   MqlDateTime dt;
   TimeToStruct(current_time, dt);
   int current_hour = dt.hour;
   int current_day = dt.day_of_week;

   // แปลง MT5 day (0=Sunday) เป็น Python day (0=Monday)
   int python_day = (current_day + 6) % 7;

   bool day_allowed = false;
   bool hour_allowed = false;

   // ตรวจสอบ Days
   int days_start = StringFind(time_filters, "Days:[");
   if(days_start >= 0)
   {
      int days_end = StringFind(time_filters, "]", days_start);
      if(days_end > days_start)
      {
         string days_part = StringSubstr(time_filters, days_start + 6, days_end - days_start - 6);

         // ตรวจสอบว่าวันปัจจุบันอยู่ในรายการหรือไม่
         if(StringFind(days_part, IntegerToString(python_day)) >= 0)
         {
            day_allowed = true;
         }
      }
   }

   // ตรวจสอบ Hours
   int hours_start = StringFind(time_filters, "Hours:[");
   if(hours_start >= 0)
   {
      int hours_end = StringFind(time_filters, "]", hours_start);
      if(hours_end > hours_start)
      {
         string hours_part = StringSubstr(time_filters, hours_start + 7, hours_end - hours_start - 7);

         // ตรวจสอบว่าชั่วโมงปัจจุบันอยู่ในรายการหรือไม่
         if(StringFind(hours_part, IntegerToString(current_hour)) >= 0)
         {
            hour_allowed = true;
         }
      }
   }

   bool result = day_allowed && hour_allowed;

   if(ShowTimeFilterStatus)
   {
      Print(StringFormat("Time Filter Check: Day=%d(%s), Hour=%d(%s), Result=%s",
            python_day, day_allowed ? "OK" : "NO",
            current_hour, hour_allowed ? "OK" : "NO",
            result ? "ALLOWED" : "BLOCKED"));
   }

   return result;
}

//+------------------------------------------------------------------+
//| ฟังก์ชันแปลง Time Filters ให้อ่านง่าย                            |
//+------------------------------------------------------------------+
string FormatTimeFilters(string time_filters)
{
   string formatted = "";

   // แยก Days และ Hours
   string days_part = "";
   string hours_part = "";

   // หา Days part
   int days_start = StringFind(time_filters, "Days:[");
   if(days_start >= 0)
   {
      int days_end = StringFind(time_filters, "]", days_start);
      if(days_end > days_start)
      {
         days_part = StringSubstr(time_filters, days_start + 6, days_end - days_start - 6);
      }
   }

   // หา Hours part
   int hours_start = StringFind(time_filters, "Hours:[");
   if(hours_start >= 0)
   {
      int hours_end = StringFind(time_filters, "]", hours_start);
      if(hours_end > hours_start)
      {
         hours_part = StringSubstr(time_filters, hours_start + 7, hours_end - hours_start - 7);
      }
   }

   // แปลง Days
   string days_formatted = FormatDays(days_part);

   // แปลง Hours
   string hours_formatted = FormatHours(hours_part);

   // รวมผลลัพธ์
   if(days_formatted != "" && hours_formatted != "")
   {
      formatted = days_formatted + ", " + hours_formatted;
   }
   else if(days_formatted != "")
   {
      formatted = days_formatted;
   }
   else if(hours_formatted != "")
   {
      formatted = hours_formatted;
   }
   else
   {
      formatted = "No filters";
   }

   return formatted;
}

//+------------------------------------------------------------------+
//| ฟังก์ชันแปลง Days ให้อ่านง่าย                                    |
//+------------------------------------------------------------------+
string FormatDays(string days_str)
{
   if(days_str == "" || days_str == " ") return "";

   // ตรวจสอบว่าเป็นทุกวันหรือไม่ (0,1,2,3,4,5,6)
   if(StringFind(days_str, "0,1,2,3,4,5,6") >= 0 ||
      StringFind(days_str, "0, 1, 2, 3, 4, 5, 6") >= 0)
   {
      return "Every Day";
   }

   // ตรวจสอบวันทำงาน (0,1,2,3,4)
   if(StringFind(days_str, "0,1,2,3,4") >= 0 ||
      StringFind(days_str, "0, 1, 2, 3, 4") >= 0)
   {
      return "Weekdays";
   }

   // ตรวจสอบวันหุ้น (5,6)
   if((StringFind(days_str, "5,6") >= 0 || StringFind(days_str, "5, 6") >= 0) &&
      StringLen(days_str) <= 4)
   {
      return "Weekends";
   }

   // แปลงเป็นชื่อวัน
   string day_names[] = {"Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"};
   string result = "";

   for(int i = 0; i < 7; i++)
   {
      string day_num = IntegerToString(i);
      if(StringFind(days_str, day_num) >= 0)
      {
         if(result != "") result += ",";
         result += day_names[i];
      }
   }

   return result != "" ? result : days_str;
}

//+------------------------------------------------------------------+
//| ฟังก์ชันแปลง Hours ให้อ่านง่าย                                   |
//+------------------------------------------------------------------+
string FormatHours(string hours_str)
{
   if(hours_str == "" || hours_str == " ") return "";

   // ตรวจสอบว่าเป็นทุกชั่วโมงหรือไม่ (0-23)
   bool all_hours = true;
   for(int i = 0; i < 24; i++)
   {
      string hour_str = IntegerToString(i);
      // ใช้การตรวจสอบที่แม่นยำมากขึ้น
      if(StringFind(hours_str, hour_str + ",") < 0 &&
         StringFind(hours_str, hour_str + "]") < 0 &&
         StringFind(hours_str, hour_str + " ") < 0 &&
         hours_str != hour_str)
      {
         all_hours = false;
         break;
      }
   }

   if(all_hours)
   {
      return "24/7";
   }

   // แปลงเป็น ranges
   int hours[];
   ArrayResize(hours, 24);
   int hour_count = 0;

   // Extract hours from string - ใช้วิธีที่แม่นยำมากขึ้น
   string clean_str = hours_str;
   StringReplace(clean_str, " ", ""); // ลบช่องว่าง
   StringReplace(clean_str, "[", ""); // ลบ [
   StringReplace(clean_str, "]", ""); // ลบ ]

   // แยกด้วย comma
   string hour_parts[];
   int parts_count = StringSplit(clean_str, ',', hour_parts);

   for(int i = 0; i < parts_count; i++)
   {
      int hour_val = (int)StringToInteger(hour_parts[i]);
      if(hour_val >= 0 && hour_val <= 23)
      {
         hours[hour_count] = hour_val;
         hour_count++;
      }
   }

   if(hour_count == 0) return hours_str;

   // Sort hours (simple bubble sort)
   for(int i = 0; i < hour_count - 1; i++)
   {
      for(int j = 0; j < hour_count - i - 1; j++)
      {
         if(hours[j] > hours[j + 1])
         {
            int temp = hours[j];
            hours[j] = hours[j + 1];
            hours[j + 1] = temp;
         }
      }
   }

   // Create ranges
   string result = "";
   int range_start = hours[0];
   int range_end = hours[0];

   for(int i = 1; i < hour_count; i++)
   {
      if(hours[i] == range_end + 1)
      {
         range_end = hours[i];
      }
      else
      {
         // Add current range to result
         if(result != "") result += ", ";
         if(range_start == range_end)
         {
            result += IntegerToString(range_start);
         }
         else
         {
            result += IntegerToString(range_start) + "-" + IntegerToString(range_end);
         }

         range_start = hours[i];
         range_end = hours[i];
      }
   }

   // Add final range
   if(result != "") result += ", ";
   if(range_start == range_end)
   {
      result += IntegerToString(range_start);
   }
   else
   {
      result += IntegerToString(range_start) + "-" + IntegerToString(range_end);
   }

   return result;
}

//+------------------------------------------------------------------+
//| ฟังก์ชันแสดงผลรวมทั้ง Python และ MT5 แบบกะทัดรัด                   |
//+------------------------------------------------------------------+
void UpdateCombinedDisplay(string py_signal, string py_class, double py_confidence, string py_symbol, string py_timeframe,
                          double py_best_entry, int py_nBars_SL, double py_threshold, string py_time_filters, int py_spread,
                          double mt5_magic, int mt5_spread, double mt5_pv,
                          string py_market_condition = "", string py_action_type = "", string py_scenario_used = "")
{
   // ป้องกัน error และตั้งค่าเริ่มต้น
   if(py_symbol == "" || py_symbol == "N/A") py_symbol = _Symbol;
   if(py_signal == "" || py_signal == "N/A") py_signal = "WAIT";
   if(py_class == "" || py_class == "N/A") py_class = "HOLD";
   if(py_market_condition == "" || py_market_condition == "unknown") py_market_condition = "neutral";
   if(py_action_type == "" || py_action_type == "none") py_action_type = "wait";
   if(py_scenario_used == "" || py_scenario_used == "none") py_scenario_used = "default";

   // ป้องกันค่าที่ผิดปกติ
   if(py_confidence < 0) py_confidence = 0;
   if(py_confidence > 1) py_confidence = 1;
   if(py_threshold < 0) py_threshold = 0.5;
   if(py_nBars_SL < 1) py_nBars_SL = 6;

   // แปลง timeframe แบบกะทัดรัด และป้องกัน error
   string tf = "M30"; // default
   if(StringLen(py_timeframe) > 7) {
      tf = StringSubstr(py_timeframe, 7); // ตัด "PERIOD_" ออก
   }

   // แปลง time_filters ให้อ่านง่าย และป้องกัน error
   string time_filter = "";
   if(py_time_filters != "") {
      time_filter = FormatTimeFilters(py_time_filters);
      if(StringLen(time_filter) > 30) {
         time_filter = StringSubstr(time_filter, 0, 30) + "...";
      }
   }

   // สร้างข้อความแสดงผลแบบกะทัดรัดและสวยงาม
   string display = "";

   // จัดรูปแบบข้อมูลให้เหมาะสมและเพิ่มสัญลักษณ์
   string symbol_tf = StringFormat("%-7s %s", py_symbol, tf);

   // เพิ่มสัญลักษณ์ตาม signal
   string signal_icon = "●";
   if(py_signal == "BUY") signal_icon = "▲";
   else if(py_signal == "SELL") signal_icon = "▼";
   else if(py_signal == "HOLD") signal_icon = "■";

   string signal_info = StringFormat("%s %s %.3f", signal_icon, py_signal, py_confidence);

   // ตัดข้อความให้พอดีและเพิ่มสัญลักษณ์
   string market = StringSubstr(py_market_condition + "        ", 0, 8);
   string action = StringSubstr(py_action_type + "    ", 0, 4);
   string scenario = StringSubstr(py_scenario_used + "           ", 0, 11);

   display += "╔══════════════════════════════════════════════╗\n";
   display += StringFormat("║ %s │ %s │ %s ║\n", symbol_tf, signal_info, py_class);
   display += StringFormat("║ Market:%s │ Action:%s │ Scenario:%s ║\n", market, action, scenario);

   // *** แสดงข้อมูลทั้ง 2 ระบบพร้อม confidence ***
   display += "║ ┌─ Trend Following ─┐ ┌─ Counter Trend ─┐ ║\n";
   display += StringFormat("║ │ T:%.3f SL:%2d     │ │ T:%.3f SL:%2d    │ ║\n",
                          received_trend_following_threshold, received_trend_following_nbars,
                          received_counter_trend_threshold, received_counter_trend_nbars);
   display += StringFormat("║ │ B:%.3f S:%.3f   │ │ B:%.3f S:%.3f  │ ║\n",
                          received_trend_following_buy_confidence, received_trend_following_sell_confidence,
                          received_counter_trend_buy_confidence, received_counter_trend_sell_confidence);
   display += "║ └───────────────────┘ └─────────────────┘ ║\n";

   // แสดงข้อมูลระบบที่ใช้งานปัจจุบัน
   display += StringFormat("║ Current: T:%.3f SL:%d SP:%d │ M:%.0f PV:%.1f ║\n",
                          py_threshold, py_nBars_SL, py_spread, mt5_magic, mt5_pv);

   // แสดงราคาเฉพาะเมื่อมี signal
   if(py_signal == "BUY" || py_signal == "SELL") {
      display += StringFormat("║ Entry: %-35.5f ║\n", py_best_entry);
   }

   // แสดง Time Filters (เฉพาะเมื่อไม่ใช่ทุกวันทุกเวลา)
   if(time_filter != "" && time_filter != "Every Day, All Day") {
      display += StringFormat("║ Time: %-38s ║\n", time_filter);
   }

   display += "╚══════════════════════════════════════════════╝";

   // แสดงผล
   Comment(display);
   Print("Display: ", py_symbol, " ", py_signal, " ", DoubleToString(py_confidence, 3));
}
//+------------------------------------------------------------------+
//| ฟังก์ชันคำนวณราคาที่ชดเชยตามการเปลี่ยนแปลงของราคา                    |
//+------------------------------------------------------------------+
void CalculatePriceAdjustment(string signal, double python_entry, double python_sl, double python_tp,
                             double &adjusted_entry, double &adjusted_sl, double &adjusted_tp)
{
   double current_bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double current_ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double points = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);

   Print("=== Price Adjustment Calculation ===");
   Print("Python Entry: ", DoubleToString(python_entry, digits));
   Print("Python SL: ", DoubleToString(python_sl, digits));
   Print("Python TP: ", DoubleToString(python_tp, digits));
   Print("Current Bid: ", DoubleToString(current_bid, digits));
   Print("Current Ask: ", DoubleToString(current_ask, digits));

   if(signal == "BUY")
   {
      // BUY: เข้าที่ Ask, ออกที่ Bid
      adjusted_entry = current_ask;

      // คำนวณขนาด SL และ TP เดิมจาก Python
      original_sl_size = python_entry - python_sl;  // ขนาด SL (เป็นบวก)
      original_tp_size = python_tp - python_entry;  // ขนาด TP (เป็นบวก)

      // SL ใช้ค่าเดิมจาก Python (ไม่เปลี่ยน)
      adjusted_sl = python_sl;

      // TP ชดเชยตามราคาปัจจุบัน
      adjusted_tp = adjusted_entry + original_tp_size;

      Print("BUY Adjustment:");
      Print("  Original SL Size: ", DoubleToString(original_sl_size, digits));
      Print("  Original TP Size: ", DoubleToString(original_tp_size, digits));
      Print("  Adjusted Entry (Ask): ", DoubleToString(adjusted_entry, digits));
      Print("  Adjusted SL (Keep): ", DoubleToString(adjusted_sl, digits));
      Print("  Adjusted TP (Entry+TPSize): ", DoubleToString(adjusted_tp, digits));
   }
   else if(signal == "SELL")
   {
      // SELL: เข้าที่ Bid, ออกที่ Ask
      adjusted_entry = current_bid;

      // คำนวณขนาด SL และ TP เดิมจาก Python
      original_sl_size = python_sl - python_entry;  // ขนาด SL (เป็นบวก)
      original_tp_size = python_entry - python_tp;  // ขนาด TP (เป็นบวก)

      // SL ใช้ค่าเดิมจาก Python (ไม่เปลี่ยน)
      adjusted_sl = python_sl;

      // TP ชดเชยตามราคาปัจจุบัน
      adjusted_tp = adjusted_entry - original_tp_size;

      Print("SELL Adjustment:");
      Print("  Original SL Size: ", DoubleToString(original_sl_size, digits));
      Print("  Original TP Size: ", DoubleToString(original_tp_size, digits));
      Print("  Adjusted Entry (Bid): ", DoubleToString(adjusted_entry, digits));
      Print("  Adjusted SL (Keep): ", DoubleToString(adjusted_sl, digits));
      Print("  Adjusted TP (Entry-TPSize): ", DoubleToString(adjusted_tp, digits));
   }
   else
   {
      // HOLD หรือสัญญาณอื่นๆ ไม่ต้องชดเชย
      adjusted_entry = python_entry;
      adjusted_sl = python_sl;
      adjusted_tp = python_tp;
      original_sl_size = 0.0;
      original_tp_size = 0.0;
   }

   Print("=== End Price Adjustment ===");
}

//+------------------------------------------------------------------+
//| ฟังก์ชันคำนวณการแบ่ง Lot สำหรับการเทรดหลายไม้                      |
//+------------------------------------------------------------------+
void CalculateMultiTradeLots(double total_lot, double &lots[], int &trade_count)
{
   trade_count = 0;

   if(total_lot <= 0.0)
   {
      Print("Invalid total lot: ", total_lot);
      return;
   }

   Print("=== Multi Trade Lot Calculation ===");
   Print("Total Lot: ", DoubleToString(total_lot, 2));

   if(total_lot <= 0.01)
   {
      // กรณี 0.01: เปิด 1 ไม้
      trade_count = 1;
      lots[0] = total_lot;
      Print("Case 1 Trade: Lot[0] = ", DoubleToString(lots[0], 2));
   }
   else if(total_lot <= 0.02)
   {
      // กรณี 0.02: เปิด 2 ไม้
      trade_count = 2;
      lots[0] = 0.01;
      lots[1] = total_lot - 0.01;
      Print("Case 2 Trades: Lot[0] = ", DoubleToString(lots[0], 2), ", Lot[1] = ", DoubleToString(lots[1], 2));
   }
   else if(total_lot <= 0.03)
   {
      // กรณี 0.03: เปิด 3 ไม้
      trade_count = 3;
      lots[0] = 0.01;
      lots[1] = 0.01;
      lots[2] = total_lot - 0.02;
      Print("Case 3 Trades: Lot[0] = ", DoubleToString(lots[0], 2), ", Lot[1] = ", DoubleToString(lots[1], 2), ", Lot[2] = ", DoubleToString(lots[2], 2));
   }
   else
   {
      // กรณี > 0.03: เปิด 3 ไม้ แบ่งเฉลี่ย
      trade_count = 3;
      double base_lot = total_lot / 3.0;
      double remainder = total_lot - (base_lot * 3.0);

      lots[0] = base_lot;
      lots[1] = base_lot;
      lots[2] = base_lot + remainder;

      // ปรับให้ lot แรกใหญ่ที่สุด
      if(remainder > 0.005)  // ถ้าเศษมากกว่า 0.005
      {
         lots[0] = base_lot + remainder;
         lots[2] = base_lot;
      }

      Print("Case >3 Trades: Lot[0] = ", DoubleToString(lots[0], 2), ", Lot[1] = ", DoubleToString(lots[1], 2), ", Lot[2] = ", DoubleToString(lots[2], 2));
   }

   Print("=== End Multi Trade Calculation ===");
}

//+------------------------------------------------------------------+
//| ฟังก์ชันเปิดเทรดหลายไม้พร้อมอัตราส่วน TP ที่แตกต่างกัน              |
//+------------------------------------------------------------------+
bool OpenMultiTrades(string signal, double entry_price, double sl_price, double tp_price, double total_lot)
{
   if(!multi_trade_enabled || signal == "HOLD")
   {
      Print("Multi trade disabled or HOLD signal");
      return false;
   }

   double lots[3];
   int trade_count = 0;
   CalculateMultiTradeLots(total_lot, lots, trade_count);

   if(trade_count == 0)
   {
      Print("No trades to open");
      return false;
   }

   ENUM_ORDER_TYPE order_type = (signal == "BUY") ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
   double price = (signal == "BUY") ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);

   // คำนวณขนาด SL และ TP เดิม
   double sl_size = (signal == "BUY") ? (entry_price - sl_price) : (sl_price - entry_price);
   double tp_size = (signal == "BUY") ? (tp_price - entry_price) : (entry_price - tp_price);

   int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
   int successful_trades = 0;

   Print("=== Opening Multi Trades ===");
   Print("Signal: ", signal, " Entry: ", DoubleToString(entry_price, digits));
   Print("SL Size: ", DoubleToString(sl_size, digits), " TP Size: ", DoubleToString(tp_size, digits));

   for(int i = 0; i < trade_count; i++)
   {
      if(lots[i] <= 0.0) continue;

      // คำนวณ TP สำหรับแต่ละไม้ (อัตราส่วน 1:1, 1:2, 1:3)
      double tp_ratio = (double)(i + 1);  // ไม้ที่ 1 = 1:1, ไม้ที่ 2 = 1:2, ไม้ที่ 3 = 1:3
      double individual_tp;

      if(signal == "BUY")
      {
         individual_tp = entry_price + (tp_size * tp_ratio);
      }
      else
      {
         individual_tp = entry_price - (tp_size * tp_ratio);
      }

      // สร้าง comment สำหรับแต่ละไม้
      string comment = StringFormat("Multi_%d_Ratio_1_%d", i+1, (int)tp_ratio);

      MqlTradeRequest request;
      MqlTradeResult result;
      ZeroMemory(request);
      ZeroMemory(result);

      request.action = TRADE_ACTION_DEAL;
      request.symbol = _Symbol;
      request.volume = lots[i];
      request.type = order_type;
      request.price = price;
      request.sl = sl_price;  // SL เหมือนกันทุกไม้
      request.tp = individual_tp;  // TP แตกต่างกันตามอัตราส่วน
      request.deviation = 10;
      request.magic = FXMagic;
      request.comment = comment;

      Print("Trade ", i+1, ": Lot=", DoubleToString(lots[i], 2),
            " TP=", DoubleToString(individual_tp, digits),
            " Ratio=1:", (int)tp_ratio);

      if(OrderSend(request, result))
      {
         Print("Trade ", i+1, " Success: Ticket=", result.order,
               " Lot=", DoubleToString(lots[i], 2),
               " Entry=", DoubleToString(result.price, digits),
               " SL=", DoubleToString(sl_price, digits),
               " TP=", DoubleToString(individual_tp, digits));
         successful_trades++;
      }
      else
      {
         Print("Trade ", i+1, " Failed: Error=", result.retcode, " ", result.comment);
      }

      Sleep(100);  // รอเล็กน้อยระหว่างการเปิดเทรด
   }

   Print("=== Multi Trade Summary ===");
   Print("Total Trades Attempted: ", trade_count);
   Print("Successful Trades: ", successful_trades);
   Print("Failed Trades: ", trade_count - successful_trades);

   return (successful_trades > 0);
}

//+------------------------------------------------------------------+
//| ฟังก์ชันตรวจสอบและจัดการ Break Even                                |
//+------------------------------------------------------------------+
void CheckAndApplyBreakEven()
{
   if(!breakeven_enabled) return;

   int total_positions = PositionsTotal();
   if(total_positions == 0) return;

   double current_bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double current_ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);

   for(int i = 0; i < total_positions; i++)
   {
      if(PositionSelectByTicket(PositionGetTicket(i)))
      {
         if(PositionGetString(POSITION_SYMBOL) != _Symbol) continue;
         if(PositionGetInteger(POSITION_MAGIC) != FXMagic) continue;

         double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
         double current_sl = PositionGetDouble(POSITION_SL);
         double current_tp = PositionGetDouble(POSITION_TP);
         ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
         ulong ticket = PositionGetInteger(POSITION_TICKET);

         bool should_move_to_breakeven = false;
         double new_sl = current_sl;

         if(pos_type == POSITION_TYPE_BUY)
         {
            // BUY: ตรวจสอบว่าราคาวิ่งไป + เท่ากับขนาด SL หรือไม่
            double sl_size = open_price - current_sl;
            double profit_target = open_price + sl_size;  // จุดที่ต้องการให้เลื่อน SL

            if(current_bid >= profit_target && current_sl < open_price)
            {
               should_move_to_breakeven = true;
               new_sl = open_price;  // เลื่อน SL ไปที่จุดเข้า
               Print("BUY Break Even: Ticket=", ticket, " Current Bid=", DoubleToString(current_bid, digits),
                     " Target=", DoubleToString(profit_target, digits), " Moving SL to Entry=", DoubleToString(new_sl, digits));
            }
         }
         else if(pos_type == POSITION_TYPE_SELL)
         {
            // SELL: ตรวจสอบว่าราคาวิ่งไป - เท่ากับขนาด SL หรือไม่
            double sl_size = current_sl - open_price;
            double profit_target = open_price - sl_size;  // จุดที่ต้องการให้เลื่อน SL

            if(current_ask <= profit_target && current_sl > open_price)
            {
               should_move_to_breakeven = true;
               new_sl = open_price;  // เลื่อน SL ไปที่จุดเข้า
               Print("SELL Break Even: Ticket=", ticket, " Current Ask=", DoubleToString(current_ask, digits),
                     " Target=", DoubleToString(profit_target, digits), " Moving SL to Entry=", DoubleToString(new_sl, digits));
            }
         }

         // ทำการเลื่อน SL ถ้าจำเป็น
         if(should_move_to_breakeven)
         {
            MqlTradeRequest request;
            MqlTradeResult result;
            ZeroMemory(request);
            ZeroMemory(result);

            request.action = TRADE_ACTION_SLTP;
            request.symbol = _Symbol;
            request.position = ticket;
            request.sl = new_sl;
            request.tp = current_tp;

            if(OrderSend(request, result))
            {
               Print("Break Even Success: Ticket=", ticket, " New SL=", DoubleToString(new_sl, digits));
            }
            else
            {
               Print("Break Even Failed: Ticket=", ticket, " Error=", result.retcode);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| ฟังก์ชันตรวจสอบและปิดไม้ทั้งหมดในวันศุกร์                           |
//+------------------------------------------------------------------+
void CheckAndCloseFridayPositions()
{
   if(!friday_close_enabled) return;

   datetime current_time = TimeCurrent();
   MqlDateTime dt;
   TimeToStruct(current_time, dt);

   // ตรวจสอบว่าเป็นวันศุกร์หรือไม่ (5 = Friday)
   if(dt.day_of_week != 5) return;

   // ตรวจสอบเวลา (21:00 หรือหลังจากนั้น)
   if(dt.hour < 21) return;

   Print("=== Friday Close Check: ", TimeToString(current_time), " ===");

   int total_positions = PositionsTotal();
   if(total_positions == 0)
   {
      Print("No positions to close on Friday");
      return;
   }

   int closed_count = 0;

   for(int i = total_positions - 1; i >= 0; i--)  // วนจากหลังไปหน้าเพื่อป้องกันปัญหา index
   {
      if(PositionSelectByTicket(PositionGetTicket(i)))
      {
         if(PositionGetString(POSITION_SYMBOL) != _Symbol) continue;
         if(PositionGetInteger(POSITION_MAGIC) != FXMagic) continue;

         ulong ticket = PositionGetInteger(POSITION_TICKET);
         double volume = PositionGetDouble(POSITION_VOLUME);
         ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

         MqlTradeRequest request;
         MqlTradeResult result;
         ZeroMemory(request);
         ZeroMemory(result);

         request.action = TRADE_ACTION_DEAL;
         request.symbol = _Symbol;
         request.position = ticket;
         request.volume = volume;
         request.type = (pos_type == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
         request.price = (pos_type == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
         request.deviation = 10;
         request.magic = FXMagic;
         request.comment = "Friday Close";

         if(OrderSend(request, result))
         {
            Print("Friday Close Success: Ticket=", ticket, " Type=", EnumToString(pos_type), " Volume=", volume);
            closed_count++;
         }
         else
         {
            Print("Friday Close Failed: Ticket=", ticket, " Error=", result.retcode);
         }
      }
   }

   Print("Friday Close Summary: Closed ", closed_count, " positions");
}

//+------------------------------------------------------------------+
//| OnTick function - เพิ่มการตรวจสอบ Break Even และ Friday Close      |
//+------------------------------------------------------------------+
void OnTick()
{
   // ตรวจสอบว่าขึ้นแท่งใหม่หรือไม่
   static datetime last_bar_time = 0;
   datetime current_bar_time = iTime(_Symbol, _Period, 0);

   bool is_new_bar = (current_bar_time != last_bar_time);

   if(is_new_bar)
   {
      last_bar_time = current_bar_time;
      Print("🕒 New bar detected at: ", TimeToString(current_bar_time, TIME_DATE|TIME_MINUTES));
   }

   // ตรวจสอบว่ามี order BUY หรือ SELL หรือไม่
   bool has_buy_orders = HasBuyOrders(_Symbol, FXMagic);
   bool has_sell_orders = HasSellOrders(_Symbol, FXMagic);
   bool has_any_orders = has_buy_orders || has_sell_orders;

   // เรียกใช้ฟังก์ชันเฉพาะเมื่อขึ้นแท่งใหม่ และมี order
   if(is_new_bar && has_any_orders)
   {
      Print("🔄 Processing new bar with active orders...");

      // แสดงสถานะ orders ปัจจุบัน
      ShowOrderStatus(_Symbol, FXMagic);

      // ตรวจสอบและจัดการ Break Even
      Print("🎯 Checking Break Even conditions...");
      CheckAndApplyBreakEven();

      // ตรวจสอบและปิดไม้วันศุกร์
      Print("📅 Checking Friday close conditions...");
      CheckAndCloseFridayPositions();

      Print("✅ Order management completed for new bar");
   }
   else if(is_new_bar && !has_any_orders)
   {
      Print("🔄 New bar detected but no active orders - skipping order management");
   }

   // แสดงสถานะ orders ทุก 100 ticks (เพื่อไม่ให้ spam log มากเกินไป)
   static int tick_counter = 0;
   tick_counter++;

   if(tick_counter >= 100 && has_any_orders)
   {
      tick_counter = 0;
      Print("📊 Periodic order status check (every 100 ticks):");
      CountTotalOrders(_Symbol, FXMagic);
   }
}

//+------------------------------------------------------------------+
//| ฟังก์ชันตรวจสอบว่ามี BUY Orders หรือไม่                           |
//+------------------------------------------------------------------+
bool HasBuyOrders(string symbol, double magic)
{
   int total_positions = PositionsTotal();

   for(int i = 0; i < total_positions; i++)
   {
      if(PositionGetTicket(i) > 0)
      {
         if(PositionGetString(POSITION_SYMBOL) == symbol &&
            PositionGetInteger(POSITION_MAGIC) == magic &&
            PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
         {
            return true;
         }
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| ฟังก์ชันตรวจสอบว่ามี SELL Orders หรือไม่                          |
//+------------------------------------------------------------------+
bool HasSellOrders(string symbol, double magic)
{
   int total_positions = PositionsTotal();

   for(int i = 0; i < total_positions; i++)
   {
      if(PositionGetTicket(i) > 0)
      {
         if(PositionGetString(POSITION_SYMBOL) == symbol &&
            PositionGetInteger(POSITION_MAGIC) == magic &&
            PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
         {
            return true;
         }
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| ฟังก์ชันนับจำนวน Orders ทั้งหมด                                   |
//+------------------------------------------------------------------+
int CountTotalOrders(string symbol, double magic)
{
   int buy_count = 0;
   int sell_count = 0;
   int total_positions = PositionsTotal();

   for(int i = 0; i < total_positions; i++)
   {
      if(PositionGetTicket(i) > 0)
      {
         if(PositionGetString(POSITION_SYMBOL) == symbol &&
            PositionGetInteger(POSITION_MAGIC) == magic)
         {
            if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
               buy_count++;
            else if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
               sell_count++;
         }
      }
   }

   Print("📊 Order Count - BUY: ", buy_count, ", SELL: ", sell_count, ", Total: ", buy_count + sell_count);
   return buy_count + sell_count;
}

//+------------------------------------------------------------------+
//| ฟังก์ชันแสดงสถานะ Orders ปัจจุบัน                                |
//+------------------------------------------------------------------+
void ShowOrderStatus(string symbol, double magic)
{
   int total_positions = PositionsTotal();
   int buy_count = 0;
   int sell_count = 0;
   double total_profit = 0.0;

   Print("📋 Current Order Status for ", symbol, " (Magic: ", magic, "):");
   Print("═══════════════════════════════════════════════════════════");

   for(int i = 0; i < total_positions; i++)
   {
      if(PositionGetTicket(i) > 0)
      {
         if(PositionGetString(POSITION_SYMBOL) == symbol &&
            PositionGetInteger(POSITION_MAGIC) == magic)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double volume = PositionGetDouble(POSITION_VOLUME);
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double current_price = PositionGetDouble(POSITION_PRICE_CURRENT);
            double profit = PositionGetDouble(POSITION_PROFIT);
            double sl = PositionGetDouble(POSITION_SL);
            double tp = PositionGetDouble(POSITION_TP);

            string type_str = (type == POSITION_TYPE_BUY) ? "BUY" : "SELL";

            Print(StringFormat("   [%d] %s %.2f lots @ %.5f | Current: %.5f | P&L: %.2f",
                              ticket, type_str, volume, open_price, current_price, profit));
            Print(StringFormat("        SL: %.5f | TP: %.5f", sl, tp));

            if(type == POSITION_TYPE_BUY)
               buy_count++;
            else
               sell_count++;

            total_profit += profit;
         }
      }
   }

   if(buy_count == 0 && sell_count == 0)
   {
      Print("   (No active positions)");
   }
   else
   {
      Print("═══════════════════════════════════════════════════════════");
      Print(StringFormat("📊 Summary: BUY: %d, SELL: %d, Total P&L: %.2f",
                        buy_count, sell_count, total_profit));
   }
   Print("═══════════════════════════════════════════════════════════");
}
//+------------------------------------------------------------------+
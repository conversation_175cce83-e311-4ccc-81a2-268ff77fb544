#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
แก้ไขปัญหา NZDUSD M30 CV_AUC = 0.5 (Critical Issue)
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

def diagnose_nzdusd_data():
    """ตรวจสอบข้อมูล NZDUSD M30"""
    print("🔍 ตรวจสอบข้อมูล NZDUSD M30")
    print("=" * 60)
    
    try:
        # โหลดข้อมูล NZDUSD M30
        file_path = "NZDUSD#_M30_201905010000_202504302330.csv"
        print(f"📂 โหลดไฟล์: {file_path}")
        
        df = pd.read_csv(file_path)
        print(f"✅ โหลดสำเร็จ: {len(df)} rows, {len(df.columns)} columns")
        
        # ตรวจสอบ columns
        print(f"\n📊 Columns ({len(df.columns)}):")
        for i, col in enumerate(df.columns):
            print(f"   {i+1}. {col}")
        
        # ตรวจสอบ data types
        print(f"\n📊 Data Types:")
        print(df.dtypes.head(10))
        
        # ตรวจสอบ missing values
        missing = df.isnull().sum()
        missing_cols = missing[missing > 0]
        print(f"\n📊 Missing Values:")
        if len(missing_cols) > 0:
            print(missing_cols.head(10))
        else:
            print("   ไม่มี missing values")
        
        # ตรวจสอบ target column
        if 'target' in df.columns:
            target_counts = df['target'].value_counts()
            print(f"\n📊 Target Distribution:")
            print(target_counts)
            
            ratio = target_counts.max() / target_counts.min()
            print(f"📊 Imbalance Ratio: {ratio:.1f}:1")
            
            if ratio > 20:
                print("⚠️ Class imbalance รุนแรงมาก!")
            elif ratio > 10:
                print("⚠️ Class imbalance รุนแรง")
            elif ratio > 5:
                print("⚠️ Class imbalance ปานกลาง")
            else:
                print("✅ Class balance ดี")
        else:
            print("❌ ไม่พบ target column!")
        
        # ตรวจสอบ data quality
        print(f"\n📊 Data Quality Check:")
        
        # ตรวจสอบ duplicate rows
        duplicates = df.duplicated().sum()
        print(f"   Duplicate rows: {duplicates}")
        
        # ตรวจสอบ infinite values
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        inf_count = 0
        for col in numeric_cols:
            inf_count += np.isinf(df[col]).sum()
        print(f"   Infinite values: {inf_count}")
        
        # ตรวจสอบ constant columns
        constant_cols = []
        for col in numeric_cols:
            if df[col].nunique() <= 1:
                constant_cols.append(col)
        print(f"   Constant columns: {len(constant_cols)}")
        if constant_cols:
            print(f"      {constant_cols[:5]}")
        
        return df
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {str(e)}")
        return None

def test_feature_engineering():
    """ทดสอบ feature engineering สำหรับ NZDUSD"""
    print(f"\n🔧 ทดสอบ Feature Engineering")
    print("=" * 60)
    
    try:
        from python_LightGBM_15_Tuning import load_and_prepare_data
        
        print("📊 โหลดและเตรียมข้อมูล...")
        df = load_and_prepare_data('NZDUSD', 30)
        
        if df is None:
            print("❌ ไม่สามารถโหลดข้อมูลได้")
            return None
        
        print(f"✅ เตรียมข้อมูลสำเร็จ: {len(df)} rows")
        
        # ตรวจสอบ features
        feature_cols = [col for col in df.columns if col not in ['target', 'datetime']]
        print(f"📊 Features: {len(feature_cols)} columns")
        
        # ตรวจสอบ target distribution หลัง preprocessing
        if 'target' in df.columns:
            target_counts = df['target'].value_counts()
            print(f"📊 Target Distribution (หลัง preprocessing):")
            print(target_counts)
            
            ratio = target_counts.max() / target_counts.min()
            print(f"📊 Imbalance Ratio: {ratio:.1f}:1")
        
        # ตรวจสอบ feature correlation กับ target
        if 'target' in df.columns and len(feature_cols) > 0:
            correlations = df[feature_cols].corrwith(df['target']).abs().sort_values(ascending=False)
            print(f"\n📊 Top 10 Feature Correlations กับ Target:")
            print(correlations.head(10))
            
            # ตรวจสอบ features ที่มี correlation สูง
            high_corr = correlations[correlations > 0.1]
            print(f"\n📊 Features ที่มี correlation > 0.1: {len(high_corr)}")
            
            if len(high_corr) == 0:
                print("⚠️ ไม่มี features ที่มี correlation สูงกับ target!")
                print("💡 อาจเป็นสาเหตุของ CV_AUC = 0.5")
        
        return df
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {str(e)}")
        return None

def test_cross_validation():
    """ทดสอบ cross validation สำหรับ NZDUSD"""
    print(f"\n🧪 ทดสอบ Cross Validation")
    print("=" * 60)
    
    try:
        from python_LightGBM_15_Tuning import load_and_prepare_data
        from sklearn.model_selection import TimeSeriesSplit
        from sklearn.metrics import roc_auc_score
        import lightgbm as lgb
        
        # โหลดข้อมูล
        df = load_and_prepare_data('NZDUSD', 30)
        if df is None:
            print("❌ ไม่สามารถโหลดข้อมูลได้")
            return
        
        # เตรียม features และ target
        feature_cols = [col for col in df.columns if col not in ['target', 'datetime']]
        X = df[feature_cols]
        y = df['target']
        
        print(f"📊 X shape: {X.shape}")
        print(f"📊 y shape: {y.shape}")
        print(f"📊 y distribution: {y.value_counts().to_dict()}")
        
        # ทดสอบ TimeSeriesSplit
        tscv = TimeSeriesSplit(n_splits=5)
        cv_scores = []
        
        print(f"\n🔍 ทดสอบ Cross Validation...")
        
        for fold, (train_idx, val_idx) in enumerate(tscv.split(X)):
            print(f"   Fold {fold+1}:")
            
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            print(f"      Train: {len(X_train)}, Val: {len(X_val)}")
            print(f"      Train target: {y_train.value_counts().to_dict()}")
            print(f"      Val target: {y_val.value_counts().to_dict()}")
            
            # ตรวจสอบว่า validation set มี class ครบหรือไม่
            if len(y_val.unique()) < 2:
                print(f"      ⚠️ Validation set มี class เดียว!")
                cv_scores.append(0.5)
                continue
            
            # เทรนโมเดลง่ายๆ
            try:
                model = lgb.LGBMClassifier(
                    objective='binary',
                    n_estimators=50,
                    learning_rate=0.1,
                    random_state=42,
                    verbose=-1
                )
                
                model.fit(X_train, y_train)
                y_pred_proba = model.predict_proba(X_val)[:, 1]
                
                auc = roc_auc_score(y_val, y_pred_proba)
                cv_scores.append(auc)
                print(f"      AUC: {auc:.4f}")
                
            except Exception as e:
                print(f"      ❌ Error: {str(e)}")
                cv_scores.append(0.5)
        
        # สรุปผล CV
        mean_cv = np.mean(cv_scores)
        std_cv = np.std(cv_scores)
        
        print(f"\n📊 Cross Validation Results:")
        print(f"   CV Scores: {[f'{score:.4f}' for score in cv_scores]}")
        print(f"   Mean CV AUC: {mean_cv:.4f}")
        print(f"   Std CV AUC: {std_cv:.4f}")
        
        if mean_cv <= 0.55:
            print(f"❌ CV AUC ต่ำมาก - ปัญหาร้ายแรง!")
            print(f"💡 สาเหตุที่เป็นไปได้:")
            print(f"   • Features ไม่มี predictive power")
            print(f"   • Data leakage หรือ target encoding ผิด")
            print(f"   • Class imbalance รุนแรงเกินไป")
            print(f"   • Time series split ไม่เหมาะสม")
        
        return cv_scores
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {str(e)}")
        return None

def create_fix_solutions():
    """สร้างแนวทางแก้ไข"""
    print(f"\n💡 แนวทางแก้ไข NZDUSD M30 CV_AUC = 0.5")
    print("=" * 60)
    
    solutions = [
        {
            "issue": "Features ไม่มี predictive power",
            "solution": "เพิ่ม advanced technical indicators",
            "code": "เพิ่ม RSI, MACD, Bollinger Bands, ATR"
        },
        {
            "issue": "Class imbalance รุนแรง",
            "solution": "ปรับ class weight เป็น 15:1",
            "code": "class_weight = {0: 1, 1: 15}"
        },
        {
            "issue": "Data leakage",
            "solution": "ตรวจสอบ feature engineering",
            "code": "ลบ features ที่ใช้ future information"
        },
        {
            "issue": "Time series split ไม่เหมาะสม",
            "solution": "ปรับ n_splits และ test_size",
            "code": "TimeSeriesSplit(n_splits=3, test_size=0.2)"
        }
    ]
    
    for i, sol in enumerate(solutions, 1):
        print(f"{i}. {sol['issue']}")
        print(f"   แก้ไข: {sol['solution']}")
        print(f"   Code: {sol['code']}")
        print()

def main():
    """ฟังก์ชันหลัก"""
    print("🔥 แก้ไขปัญหา NZDUSD M30 CV_AUC = 0.5")
    print("=" * 80)
    
    # 1. ตรวจสอบข้อมูล
    df_raw = diagnose_nzdusd_data()
    
    # 2. ทดสอบ feature engineering
    df_processed = test_feature_engineering()
    
    # 3. ทดสอบ cross validation
    cv_scores = test_cross_validation()
    
    # 4. สร้างแนวทางแก้ไข
    create_fix_solutions()
    
    # 5. สรุป
    print("🎯 สรุปการตรวจสอบ NZDUSD M30")
    print("=" * 80)
    
    if df_raw is not None:
        print("✅ โหลดข้อมูลดิบสำเร็จ")
    else:
        print("❌ ไม่สามารถโหลดข้อมูลดิบได้")
    
    if df_processed is not None:
        print("✅ Feature engineering สำเร็จ")
    else:
        print("❌ Feature engineering ไม่สำเร็จ")
    
    if cv_scores is not None:
        mean_cv = np.mean(cv_scores)
        print(f"📊 CV AUC: {mean_cv:.4f}")
        if mean_cv <= 0.55:
            print("❌ ยืนยันปัญหา CV_AUC ต่ำ")
        else:
            print("✅ CV AUC ปกติ")
    else:
        print("❌ ไม่สามารถทดสอบ CV ได้")
    
    print(f"\n🚀 ขั้นตอนถัดไป:")
    print("1. ปรับ class weight เป็น 15:1")
    print("2. เพิ่ม advanced features")
    print("3. ปรับ cross validation parameters")
    print("4. ทดสอบใหม่")

if __name__ == "__main__":
    main()

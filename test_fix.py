#!/usr/bin/env python3
"""
Test the fix for trading schedule summary
"""

import os
import sys
sys.path.append('.')

from python_LightGBM_17_Signal import (
    generate_trading_schedule_summary, 
    generate_all_trading_schedule_summaries,
    test_folder
)

def test_fix():
    print("🔍 Testing Trading Schedule Fix")
    print("=" * 40)
    
    # ตรวจสอบ path
    print(f"test_folder: {test_folder}")
    thresholds_dir = f"{test_folder}/thresholds"
    print(f"thresholds_dir: {thresholds_dir}")
    print(f"exists: {os.path.exists(thresholds_dir)}")
    
    if os.path.exists(thresholds_dir):
        files = [f for f in os.listdir(thresholds_dir) if f.endswith('_time_filters.pkl')]
        print(f"time_filters files: {len(files)}")
        for f in files[:3]:
            print(f"  - {f}")
    
    # ทดสอบ generate_trading_schedule_summary
    print("\nTesting generate_trading_schedule_summary...")
    summary = generate_trading_schedule_summary(None)
    print(f"Summary generated: {summary is not None}")
    
    if summary:
        print(f"Days: {len(summary)}")
        for day_idx, day_data in summary.items():
            day_name = day_data["day_name"]
            win_rate = day_data["avg_win_rate"]
            symbols = day_data["recommended_symbols"]
            combinations = day_data["total_combinations"]
            print(f"  {day_name}: WR={win_rate:.4f}, Symbols={symbols}, Combinations={combinations}")
    
    # ทดสอบการสร้างไฟล์
    print("\nTesting file generation...")
    try:
        generate_all_trading_schedule_summaries("test_output")
        print("✅ File generation completed")
        
        # ตรวจสอบไฟล์ที่สร้าง
        summary_file = "test_output/daily_trading_schedule_summary.txt"
        if os.path.exists(summary_file):
            with open(summary_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"File size: {len(content)} characters")
            
            # ตรวจสอบปัญหาเดิม
            if "42.00%" in content:
                print("❌ Still has mock data (42.00%)")
            else:
                print("✅ No mock data found")
                
            if "030 060" in content:
                print("❌ Still shows timeframe instead of symbols")
            else:
                print("✅ No timeframe codes found")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_fix()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแสดง time_filters ในขั้นตอนสรุปผล

Created: 2025-01-11
Author: AI Assistant
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
import json
from pathlib import Path

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_time_filters_formatting():
    """
    ทดสอบการแปลง time_filters เป็นข้อความที่อ่านง่าย
    """
    print("🔍 ทดสอบการแปลง time_filters เป็นข้อความที่อ่านง่าย")
    print("="*60)
    
    try:
        from python_LightGBM_16_Signal import format_time_filters_display
        
        # ทดสอบกรณีต่างๆ
        test_cases = [
            {
                'name': 'ทุกวัน ทุกชั่วโมง',
                'filters': {'days': list(range(7)), 'hours': list(range(24))}
            },
            {
                'name': 'วันจันทร์-ศุกร์ ทุกชั่วโมง',
                'filters': {'days': [0, 1, 2, 3, 4], 'hours': list(range(24))}
            },
            {
                'name': 'วันเสาร์-อาทิตย์ ทุกชั่วโมง',
                'filters': {'days': [5, 6], 'hours': list(range(24))}
            },
            {
                'name': 'ทุกวัน ช่วงเวลาเทรด (7-21)',
                'filters': {'days': list(range(7)), 'hours': [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]}
            },
            {
                'name': 'วันจันทร์-ศุกร์ ช่วงเวลาเทรด (9-17)',
                'filters': {'days': [0, 1, 2, 3, 4], 'hours': [9, 10, 11, 12, 13, 14, 15, 16, 17]}
            },
            {
                'name': 'วันเฉพาะ ชั่วโมงเฉพาะ',
                'filters': {'days': [1, 3, 5], 'hours': [7, 8, 11, 21]}
            },
            {
                'name': 'ไม่มีข้อมูล',
                'filters': None
            },
            {
                'name': 'ไม่มีวัน',
                'filters': {'days': [], 'hours': [9, 10, 11]}
            },
            {
                'name': 'ไม่มีชั่วโมง',
                'filters': {'days': [0, 1, 2], 'hours': []}
            }
        ]
        
        for test_case in test_cases:
            result = format_time_filters_display(test_case['filters'])
            print(f"✅ {test_case['name']}: {result}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_load_existing_time_filters():
    """
    ทดสอบการโหลด time_filters ที่มีอยู่จริง
    """
    print("\n📁 ทดสอบการโหลด time_filters ที่มีอยู่จริง")
    print("="*60)
    
    try:
        from python_LightGBM_16_Signal import load_time_filters, format_time_filters_display
        
        # ทดสอบกับสัญลักษณ์ที่มีอยู่
        test_symbols = [
            ("GOLD", 30),
            ("GOLD", 60),
            ("EURUSD", 60),
            ("USDJPY", 60)
        ]
        
        for symbol, timeframe in test_symbols:
            print(f"\n🔸 ทดสอบ {symbol} M{timeframe}:")
            
            try:
                time_filters = load_time_filters(symbol, timeframe)
                time_filters_text = format_time_filters_display(time_filters)
                
                print(f"   Raw data: {time_filters}")
                print(f"   Formatted: {time_filters_text}")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_optimization_with_time_filters():
    """
    ทดสอบการแสดง time_filters ในขั้นตอน optimization
    """
    print("\n🚀 ทดสอบการแสดง time_filters ในขั้นตอน optimization")
    print("="*60)
    
    try:
        from python_LightGBM_16_Signal import run_multi_model_optimization
        
        # ทดสอบกับ GOLD M60
        symbol = "GOLD"
        timeframe = 60
        
        print(f"📊 ทดสอบกับ {symbol} M{timeframe}")
        
        result = run_multi_model_optimization(symbol, timeframe)
        
        if result:
            print(f"\n✅ Optimization สำเร็จ!")
            print(f"📊 ผลลัพธ์:")
            print(f"   Symbol: {result.get('symbol')}")
            print(f"   Timeframe: {result.get('timeframe')}")
            print(f"   Scenarios: {result.get('scenarios')}")
            
            # แสดง optimal parameters
            for scenario in result.get('scenarios', []):
                threshold = result.get('optimal_thresholds', {}).get(scenario, 'N/A')
                nbars = result.get('optimal_nbars', {}).get(scenario, 'N/A')
                print(f"   {scenario}:")
                print(f"     Threshold: {threshold}")
                print(f"     nBars_SL: {nbars}")
            
            # แสดง time_filters
            time_filters_display = result.get('time_filters_display', 'ไม่มีข้อมูล')
            print(f"   time_filters: {time_filters_display}")
            
            return True
        else:
            print(f"❌ Optimization ล้มเหลว")
            return False
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def create_sample_time_filters():
    """
    สร้าง time_filters ตัวอย่างสำหรับทดสอบ
    """
    print("\n📝 สร้าง time_filters ตัวอย่างสำหรับทดสอบ")
    print("="*60)
    
    try:
        import pickle
        
        # สร้างโฟลเดอร์
        thresholds_dir = Path("LightGBM_Multi/thresholds")
        thresholds_dir.mkdir(parents=True, exist_ok=True)
        
        # ตัวอย่าง time_filters ต่างๆ
        sample_filters = {
            "060_GOLD_time_filters.pkl": {
                'days': [0, 1, 2, 3, 4],  # จันทร์-ศุกร์
                'hours': [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]  # 7:00-21:59
            },
            "030_GOLD_time_filters.pkl": {
                'days': [1, 2, 3, 4],  # อังคาร-ศุกร์
                'hours': [8, 9, 10, 11, 14, 15, 16, 17, 20, 21]  # ช่วงเวลาเฉพาะ
            },
            "060_EURUSD_time_filters.pkl": {
                'days': [0, 1, 2, 3, 4],  # จันทร์-ศุกร์
                'hours': [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]  # 6:00-20:59
            },
            "060_USDJPY_time_filters.pkl": {
                'days': [0, 1, 2, 3, 4, 6],  # จันทร์-ศุกร์ + อาทิตย์
                'hours': [0, 1, 2, 3, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]  # เกือบทุกชั่วโมง
            }
        }
        
        for filename, filters in sample_filters.items():
            file_path = thresholds_dir / filename
            
            with open(file_path, 'wb') as f:
                pickle.dump(filters, f)
            
            print(f"✅ สร้างไฟล์: {filename}")
            
            # แสดงผลการแปลง
            from python_LightGBM_16_Signal import format_time_filters_display
            formatted = format_time_filters_display(filters)
            print(f"   {formatted}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🚀 ทดสอบการแสดง time_filters ในขั้นตอนสรุปผล")
    print("="*80)
    
    # Test 1: ทดสอบการแปลง time_filters
    format_success = test_time_filters_formatting()
    
    # Test 2: สร้าง time_filters ตัวอย่าง
    create_success = create_sample_time_filters()
    
    # Test 3: ทดสอบการโหลด time_filters ที่มีอยู่
    load_success = test_load_existing_time_filters()
    
    # Test 4: ทดสอบ optimization พร้อม time_filters
    optimization_success = test_optimization_with_time_filters()
    
    print("\n" + "="*80)
    print("📊 สรุปผลการทดสอบ")
    print("="*80)
    
    print(f"✅ Test 1 - Format Time Filters: {'ผ่าน' if format_success else 'ล้มเหลว'}")
    print(f"✅ Test 2 - Create Sample Filters: {'ผ่าน' if create_success else 'ล้มเหลว'}")
    print(f"✅ Test 3 - Load Existing Filters: {'ผ่าน' if load_success else 'ล้มเหลว'}")
    print(f"✅ Test 4 - Optimization with Filters: {'ผ่าน' if optimization_success else 'ล้มเหลว'}")
    
    overall_success = all([format_success, create_success, load_success, optimization_success])
    
    if overall_success:
        print(f"\n🎉 การเพิ่มการแสดง time_filters สำเร็จ!")
        print(f"🚀 ระบบแสดง time_filters ในขั้นตอนสรุปผลแล้ว")
        
        print(f"\n💡 ตัวอย่างการแสดงผล:")
        print(f"   • GOLD_60:")
        print(f"     - trend_following: threshold=0.5, nBars_SL=7")
        print(f"     - counter_trend: threshold=0.5, nBars_SL=7")
        print(f"     - time_filters: วันจันทร์-ศุกร์ (07:00-21:59)")
    else:
        print(f"\n⚠️ ยังมีปัญหาบางส่วนที่ต้องแก้ไข")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ตัวอย่างการใช้งานระบบสรุปผลการเทรน
Training Summary System Usage Examples

วิธีใช้:
1. รันไฟล์นี้หลังจากเทรนโมเดลแล้ว
2. เปลี่ยนค่า symbol และ timeframe ตามต้องการ
3. ดูผลลัพธ์ในโฟลเดอร์ Test_LightGBM/training_summaries/
"""

import sys
import os

# เพิ่ม path สำหรับ import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import ฟังก์ชันจากไฟล์หลัก
try:
    from python_LightGBM_17_Signal import (
        view_training_summary_reports,
        compare_training_progress,
        create_training_summary_system
    )
    print("✅ Import ฟังก์ชันสำเร็จ")
except ImportError as e:
    print(f"❌ ไม่สามารถ import ฟังก์ชันได้: {e}")
    print("💡 กรุณาตรวจสอบว่าไฟล์ python_LightGBM_17_Signal.py อยู่ในโฟลเดอร์เดียวกัน")
    sys.exit(1)

def main():
    """ฟังก์ชันหลักสำหรับทดสอบระบบสรุป"""
    
    print("🚀 เริ่มทดสอบระบบสรุปผลการเทรน")
    print("=" * 60)
    
    # 1. ตรวจสอบว่ามีข้อมูลสรุปหรือไม่
    print("\n1️⃣ ตรวจสอบระบบสรุป...")
    summary_folder = create_training_summary_system()
    print(f"📁 โฟลเดอร์สรุป: {summary_folder}")
    
    # ตรวจสอบไฟล์ที่มีอยู่
    if os.path.exists(summary_folder):
        files = os.listdir(summary_folder)
        if files:
            print(f"📄 ไฟล์ที่พบ: {len(files)} ไฟล์")
            for file in sorted(files):
                print(f"   • {file}")
        else:
            print("⚠️ ไม่พบไฟล์สรุป - กรุณาเทรนโมเดลก่อน")
            return
    else:
        print("⚠️ ไม่พบโฟลเดอร์สรุป - กรุณาเทรนโมเดลก่อน")
        return
    
    # 2. ดูสรุปทั้งหมด
    print("\n2️⃣ ดูสรุปผลการเทรนทั้งหมด...")
    print("-" * 40)
    view_training_summary_reports(show_details=False)
    
    # 3. ดูรายละเอียดเฉพาะ symbol
    print("\n3️⃣ ดูรายละเอียดเฉพาะ GOLD...")
    print("-" * 40)
    view_training_summary_reports(symbol="GOLD", show_details=True)
    
    # 4. ดูรายละเอียดเฉพาะ timeframe
    print("\n4️⃣ ดูรายละเอียดเฉพาะ M30...")
    print("-" * 40)
    view_training_summary_reports(timeframe=30, show_details=True)
    
    # 5. เปรียบเทียบความก้าวหน้า
    print("\n5️⃣ เปรียบเทียบความก้าวหน้า GOLD M30...")
    print("-" * 40)
    compare_training_progress("GOLD", 30, show_chart=False)
    
    # 6. สร้างกราฟ (ถ้าต้องการ)
    print("\n6️⃣ สร้างกราฟความก้าวหน้า...")
    print("-" * 40)
    try:
        compare_training_progress("GOLD", 30, show_chart=True)
        print("✅ สร้างกราฟสำเร็จ")
    except Exception as e:
        print(f"⚠️ ไม่สามารถสร้างกราฟได้: {e}")
    
    print("\n✅ ทดสอบระบบสรุปเสร็จสิ้น")
    print("=" * 60)

def demo_advanced_analysis():
    """ตัวอย่างการวิเคราะห์ขั้นสูง"""
    
    print("\n🔬 การวิเคราะห์ขั้นสูง")
    print("=" * 40)
    
    try:
        import pandas as pd
        
        # อ่านไฟล์สรุปรวม
        summary_folder = "Test_LightGBM/training_summaries"
        master_file = os.path.join(summary_folder, "master_training_history.csv")
        
        if not os.path.exists(master_file):
            print("⚠️ ไม่พบไฟล์สรุปรวม")
            return
        
        df = pd.read_csv(master_file)
        
        if len(df) == 0:
            print("⚠️ ไม่มีข้อมูลในไฟล์สรุป")
            return
        
        print(f"📊 จำนวนข้อมูล: {len(df)} รายการ")
        
        # วิเคราะห์ correlation
        print("\n🔗 Correlation Analysis:")
        numeric_cols = ['performance_score', 'test_total_win_rate', 'test_total_expectancy', 
                       'accuracy', 'auc', 'f1', 'threshold']
        
        available_cols = [col for col in numeric_cols if col in df.columns]
        
        if len(available_cols) > 1:
            correlation = df[available_cols].corr()['performance_score'].sort_values(ascending=False)
            
            print("ความสัมพันธ์กับ Performance Score:")
            for col, corr in correlation.items():
                if col != 'performance_score':
                    print(f"   {col}: {corr:.3f}")
        
        # วิเคราะห์ตาม Architecture
        if 'architecture' in df.columns:
            print("\n🏗️ เปรียบเทียบ Architecture:")
            arch_stats = df.groupby('architecture').agg({
                'performance_score': ['mean', 'std', 'count'],
                'test_total_win_rate': 'mean'
            }).round(2)
            
            print(arch_stats)
        
        # หาโมเดลที่ดีที่สุด
        print("\n🏆 โมเดลที่ดีที่สุด:")
        best_models = df.nlargest(3, 'performance_score')
        
        for i, (_, model) in enumerate(best_models.iterrows(), 1):
            print(f"   {i}. {model.get('symbol', 'N/A')} M{model.get('timeframe', 0):03d}: "
                  f"Score {model['performance_score']:.1f}")
        
        # วิเคราะห์แนวโน้ม
        if len(df) > 1:
            print("\n📈 แนวโน้มการพัฒนา:")
            
            # จัดกลุ่มตาม symbol/timeframe และดูการเปลี่ยนแปลง
            for (symbol, timeframe), group in df.groupby(['symbol', 'timeframe']):
                if len(group) > 1:
                    group = group.sort_values('timestamp')
                    first_score = group.iloc[0]['performance_score']
                    latest_score = group.iloc[-1]['performance_score']
                    change = latest_score - first_score
                    
                    if abs(change) > 1:  # แสดงเฉพาะที่เปลี่ยนแปลงมาก
                        trend = "📈" if change > 0 else "📉"
                        print(f"   {symbol} M{timeframe:03d}: {trend} {change:+.1f} คะแนน")
        
    except ImportError:
        print("⚠️ ต้องการ pandas สำหรับการวิเคราะห์ขั้นสูง")
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดในการวิเคราะห์: {e}")

def create_custom_report():
    """สร้างรายงานแบบกำหนดเอง"""
    
    print("\n📋 สร้างรายงานแบบกำหนดเอง")
    print("=" * 40)
    
    try:
        import pandas as pd
        from datetime import datetime
        
        summary_folder = "Test_LightGBM/training_summaries"
        master_file = os.path.join(summary_folder, "master_training_history.csv")
        
        if not os.path.exists(master_file):
            print("⚠️ ไม่พบไฟล์สรุปรวม")
            return
        
        df = pd.read_csv(master_file)
        
        # สร้างรายงานสรุป
        report_lines = []
        report_lines.append("📊 รายงานสรุปผลการเทรนแบบกำหนดเอง")
        report_lines.append("=" * 60)
        report_lines.append(f"📅 สร้างเมื่อ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # สถิติรวม
        latest_data = df.groupby(['symbol', 'timeframe']).last().reset_index()
        
        report_lines.append("📈 สถิติรวม:")
        report_lines.append(f"   จำนวนโมเดล: {len(latest_data)}")
        report_lines.append(f"   คะแนนเฉลี่ย: {latest_data['performance_score'].mean():.2f}/100")
        report_lines.append(f"   คะแนนสูงสุด: {latest_data['performance_score'].max():.2f}/100")
        report_lines.append(f"   คะแนนต่ำสุด: {latest_data['performance_score'].min():.2f}/100")
        report_lines.append("")
        
        # โมเดลที่ดีที่สุด
        top_3 = latest_data.nlargest(3, 'performance_score')
        report_lines.append("🏆 โมเดลที่ดีที่สุด 3 อันดับ:")
        
        for i, (_, model) in enumerate(top_3.iterrows(), 1):
            report_lines.append(f"   {i}. {model['symbol']} M{model['timeframe']:03d}")
            report_lines.append(f"      Score: {model['performance_score']:.1f}/100")
            report_lines.append(f"      Test Win Rate: {model['test_total_win_rate']:.1f}%")
            report_lines.append(f"      Test Expectancy: {model['test_total_expectancy']:.2f}")
        
        report_lines.append("")
        
        # โมเดลที่ต้องปรับปรุง
        poor_models = latest_data[latest_data['performance_score'] < 50]
        
        if len(poor_models) > 0:
            report_lines.append("⚠️ โมเดลที่ต้องปรับปรุง (Score < 50):")
            for _, model in poor_models.iterrows():
                report_lines.append(f"   • {model['symbol']} M{model['timeframe']:03d}: "
                                  f"Score {model['performance_score']:.1f}")
        else:
            report_lines.append("✅ ทุกโมเดลมีคะแนน >= 50")
        
        report_lines.append("")
        report_lines.append("=" * 60)
        
        # บันทึกรายงาน
        report_file = os.path.join(summary_folder, "custom_report.txt")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        print(f"✅ สร้างรายงานแบบกำหนดเองที่: {report_file}")
        
        # แสดงบางส่วน
        print("\n📄 ตัวอย่างรายงาน:")
        for line in report_lines[:15]:
            print(line)
        
        if len(report_lines) > 15:
            print("... (ดูเพิ่มเติมในไฟล์)")
        
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดในการสร้างรายงาน: {e}")

if __name__ == "__main__":
    print("🎯 ตัวอย่างการใช้งานระบบสรุปผลการเทรน")
    print("=" * 60)
    
    # เรียกใช้ฟังก์ชันต่างๆ
    main()
    demo_advanced_analysis()
    create_custom_report()
    
    print("\n🎉 เสร็จสิ้นการทดสอบทั้งหมด!")
    print("\n💡 คำแนะนำ:")
    print("   1. ดูไฟล์ในโฟลเดอร์ Test_LightGBM/training_summaries/")
    print("   2. ใช้ Excel เปิดไฟล์ .csv เพื่อวิเคราะห์เพิ่มเติม")
    print("   3. อ่านไฟล์ .txt เพื่อดูรายงานที่อ่านง่าย")
    print("   4. ดูกราฟ .png เพื่อเห็นแนวโน้มการพัฒนา")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบฟังก์ชันสร้างสรุปการเทรดรายวันที่ปรับปรุงแล้ว
- ลบวันเสาร์และวันอาทิตย์
- สร้างไฟล์แยกตาม timeframe (M30, M60, รวม)
"""

import os
import sys

# เพิ่ม path ของไฟล์หลัก
sys.path.append('.')

# Import ฟังก์ชันที่ต้องการทดสอบ
from python_LightGBM_15_Tuning import (
    generate_trading_schedule_summary,
    print_trading_schedule_summary,
    generate_all_trading_schedule_summaries
)

def test_enhanced_trading_schedule():
    """
    ทดสอบฟังก์ชันสร้างสรุปการเทรดรายวันที่ปรับปรุงแล้ว
    """
    print("🧪 เริ่มทดสอบฟังก์ชันสรุปการเทรดรายวันที่ปรับปรุงแล้ว")
    print("=" * 80)
    
    # กำหนดโฟลเดอร์ทดสอบ
    test_output_folder = "Test_LightGBM/results_test_enhanced"
    
    try:
        # สร้างโฟลเดอร์ทดสอบ
        os.makedirs(test_output_folder, exist_ok=True)
        print(f"📁 สร้างโฟลเดอร์ทดสอบ: {test_output_folder}")
        
        print("\n" + "=" * 50)
        print("🔍 ทดสอบ 1: สร้างสรุปเฉพาะ M30")
        print("=" * 50)
        
        # ทดสอบสร้างสรุปเฉพาะ M30
        summary_m30 = generate_trading_schedule_summary(group_name="M30")
        print(f"✅ สร้างข้อมูลสรุป M30 สำเร็จ: {len(summary_m30)} วัน")
        
        # บันทึกไฟล์ M30
        print_trading_schedule_summary(test_output_folder, group_name="M30")
        
        print("\n" + "=" * 50)
        print("🔍 ทดสอบ 2: สร้างสรุปเฉพาะ M60")
        print("=" * 50)
        
        # ทดสอบสร้างสรุปเฉพาะ M60
        summary_m60 = generate_trading_schedule_summary(group_name="M60")
        print(f"✅ สร้างข้อมูลสรุป M60 สำเร็จ: {len(summary_m60)} วัน")
        
        # บันทึกไฟล์ M60
        print_trading_schedule_summary(test_output_folder, group_name="M60")
        
        print("\n" + "=" * 50)
        print("🔍 ทดสอบ 3: สร้างสรุปรวม M30+M60")
        print("=" * 50)
        
        # ทดสอบสร้างสรุปรวม
        summary_combined = generate_trading_schedule_summary(group_name=None)
        print(f"✅ สร้างข้อมูลสรุปรวม สำเร็จ: {len(summary_combined)} วัน")
        
        # บันทึกไฟล์รวม
        print_trading_schedule_summary(test_output_folder, group_name=None)
        
        print("\n" + "=" * 50)
        print("🔍 ทดสอบ 4: สร้างไฟล์ทั้งหมดพร้อมกัน")
        print("=" * 50)
        
        # ทดสอบสร้างไฟล์ทั้งหมด
        generate_all_trading_schedule_summaries(test_output_folder)
        
        print("\n" + "=" * 50)
        print("📋 ตรวจสอบไฟล์ที่สร้าง")
        print("=" * 50)
        
        # ตรวจสอบไฟล์ที่สร้าง
        expected_files = [
            "M30_daily_trading_schedule_summary.txt",
            "M60_daily_trading_schedule_summary.txt", 
            "daily_trading_schedule_summary.txt"
        ]
        
        for filename in expected_files:
            filepath = os.path.join(test_output_folder, filename)
            if os.path.exists(filepath):
                file_size = os.path.getsize(filepath)
                print(f"✅ {filename} - ขนาด: {file_size} bytes")
                
                # อ่านบรรทัดแรกเพื่อตรวจสอบ
                with open(filepath, 'r', encoding='utf-8') as f:
                    first_line = f.readline().strip()
                    print(f"   📄 บรรทัดแรก: {first_line}")
            else:
                print(f"❌ ไม่พบไฟล์: {filename}")
        
        print("\n" + "=" * 50)
        print("🔍 ตรวจสอบเนื้อหาไฟล์ตัวอย่าง")
        print("=" * 50)
        
        # แสดงเนื้อหาไฟล์ M30 เป็นตัวอย่าง
        m30_file = os.path.join(test_output_folder, "M30_daily_trading_schedule_summary.txt")
        if os.path.exists(m30_file):
            print(f"\n📄 เนื้อหาไฟล์ M30 (10 บรรทัดแรก):")
            with open(m30_file, 'r', encoding='utf-8') as f:
                for i, line in enumerate(f):
                    if i >= 10:
                        break
                    print(f"   {i+1:2d}: {line.rstrip()}")
        
        print("\n" + "=" * 80)
        print("✅ การทดสอบเสร็จสิ้น - ฟังก์ชันทำงานถูกต้อง")
        print("🎯 ผลการทดสอบ:")
        print("   - ลบวันเสาร์และวันอาทิตย์แล้ว (เหลือ 5 วัน)")
        print("   - สร้างไฟล์แยกตาม timeframe สำเร็จ")
        print("   - สร้างไฟล์รวม M30+M60 สำเร็จ")
        print(f"   - ไฟล์ทั้งหมดบันทึกใน: {test_output_folder}")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_trading_schedule()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import numpy as np
import time

def test_210_bars():
    """ทดสอบ server ด้วยข้อมูล 210 แท่งเพื่อดู error"""
    
    url = 'http://127.0.0.1:54321/data'
    
    print("🔍 Creating 210 bars test data...")
    
    # สร้างข้อมูลทดสอบ 210 แท่ง
    bars = []
    base_time = 1737021600
    base_price = 2650.0
    
    for i in range(210):
        price_change = np.random.normal(0, 1.0)
        open_price = base_price + price_change
        high_price = open_price + abs(np.random.normal(1, 0.5))
        low_price = open_price - abs(np.random.normal(1, 0.5))
        close_price = open_price + np.random.normal(0, 0.5)
        
        # ปรับให้ high/low สมเหตุสมผล
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        bar = {
            "time": base_time + (i * 1800),  # 30 minutes apart
            "open": round(open_price, 2),
            "high": round(high_price, 2),
            "low": round(low_price, 2),
            "close": round(close_price, 2),
            "volume": 1000 + i,
            "tick_volume": 1000 + i,
            "spread": 5,
            "real_volume": 1000 + i
        }
        bars.append(bar)
        base_price = close_price
    
    test_data = {
        "symbol": "GOLD",
        "timeframe_str": "PERIOD_M30",
        "bars": bars
    }
    
    print(f"✅ Created {len(bars)} bars")
    print("🚀 Sending to server...")
    
    try:
        response = requests.post(url, json=test_data, timeout=120)
        
        print(f"✅ Server response: HTTP {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            signal = result.get('signal', 'N/A')
            print(f"📊 Signal: {signal}")
            
            if signal == 'ERROR':
                print("❌ Got ERROR signal - checking logs...")
                
                # รอสักครู่แล้วอ่าน error log
                time.sleep(3)
                
                try:
                    with open('server_error.log', 'r', encoding='utf-8') as f:
                        error_content = f.read()
                    print("\n📝 Error log content:")
                    print(error_content[-2000:])  # แสดง 2000 ตัวอักษรสุดท้าย
                except Exception as e:
                    print(f"❌ Cannot read error log: {e}")
                    
                try:
                    with open('server_debug.log', 'r', encoding='utf-8') as f:
                        debug_content = f.read()
                    print("\n📝 Debug log content:")
                    print(debug_content[-1000:])  # แสดง 1000 ตัวอักษรสุดท้าย
                except Exception as e:
                    print(f"❌ Cannot read debug log: {e}")
            else:
                print(f"✅ Got signal: {signal}")
        else:
            print(f"❌ Server error: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Request error: {e}")

if __name__ == "__main__":
    test_210_bars()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ปรับปรุง F1 Score ของโมเดล LightGBM Trading
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score, accuracy_score, f1_score, precision_score, recall_score
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.utils.class_weight import compute_class_weight
from imblearn.over_sampling import SMOTE
from imblearn.under_sampling import RandomUnderSampler
from imblearn.combine import SMOTEENN
import warnings
warnings.filterwarnings('ignore')

def calculate_optimal_threshold(y_true, y_proba):
    """คำนวณ threshold ที่เหมาะสมสำหรับ F1 Score"""
    thresholds = np.arange(0.1, 0.9, 0.01)
    f1_scores = []
    
    for threshold in thresholds:
        y_pred = (y_proba >= threshold).astype(int)
        f1 = f1_score(y_true, y_pred, zero_division=0)
        f1_scores.append(f1)
    
    optimal_idx = np.argmax(f1_scores)
    optimal_threshold = thresholds[optimal_idx]
    optimal_f1 = f1_scores[optimal_idx]
    
    return optimal_threshold, optimal_f1

def test_class_weight_strategies():
    """ทดสอบกลยุทธ์ class weight ต่างๆ"""
    print("⚖️ ทดสอบกลยุทธ์ Class Weight")
    print("-"*60)
    
    # สร้างข้อมูลตัวอย่างที่ imbalanced
    np.random.seed(42)
    n_samples = 1000
    n_features = 20
    
    # สร้าง imbalanced dataset (20% positive class)
    X = pd.DataFrame(np.random.randn(n_samples, n_features), 
                     columns=[f'feature_{i}' for i in range(n_features)])
    
    # สร้าง target ที่ imbalanced
    important_features = X.iloc[:, :3].sum(axis=1)
    noise = np.random.randn(n_samples) * 0.3
    y_continuous = important_features + noise
    y = pd.Series((y_continuous > np.percentile(y_continuous, 80)).astype(int))  # 20% positive
    
    print(f"📊 Target distribution: {y.value_counts().to_dict()}")
    print(f"📈 Positive class: {y.sum()/len(y)*100:.1f}%")
    
    # ทดสอบ class weight strategies
    strategies = {
        "No Weight": None,
        "Balanced": "balanced",
        "Custom 1:3": {0: 1, 1: 3},
        "Custom 1:5": {0: 1, 1: 5},
        "Custom 1:2": {0: 1, 1: 2}
    }
    
    results = {}
    
    for strategy_name, class_weight in strategies.items():
        print(f"\n🧪 ทดสอบ: {strategy_name}")
        
        # สร้างโมเดล
        model = lgb.LGBMClassifier(
            n_estimators=500,
            learning_rate=0.05,
            max_depth=4,
            num_leaves=8,
            min_data_in_leaf=20,
            class_weight=class_weight,
            random_state=42,
            verbose=-1
        )
        
        # Cross-validation
        tscv = TimeSeriesSplit(n_splits=3)
        cv_results = {
            'f1': [], 'precision': [], 'recall': [], 'auc': [],
            'optimal_threshold': [], 'optimal_f1': []
        }
        
        for train_idx, val_idx in tscv.split(X):
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            # Scale ข้อมูล
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_val_scaled = scaler.transform(X_val)
            
            # ฝึกโมเดล
            model.fit(X_train_scaled, y_train)
            
            # ทำนาย
            y_proba = model.predict_proba(X_val_scaled)[:, 1]
            y_pred_default = model.predict(X_val_scaled)
            
            # หา optimal threshold
            optimal_threshold, optimal_f1 = calculate_optimal_threshold(y_val, y_proba)
            y_pred_optimal = (y_proba >= optimal_threshold).astype(int)
            
            # คำนวณ metrics
            cv_results['f1'].append(f1_score(y_val, y_pred_default))
            cv_results['precision'].append(precision_score(y_val, y_pred_default, zero_division=0))
            cv_results['recall'].append(recall_score(y_val, y_pred_default, zero_division=0))
            cv_results['auc'].append(roc_auc_score(y_val, y_proba))
            cv_results['optimal_threshold'].append(optimal_threshold)
            cv_results['optimal_f1'].append(optimal_f1)
        
        # คำนวณค่าเฉลี่ย
        avg_results = {metric: np.mean(values) for metric, values in cv_results.items()}
        results[strategy_name] = avg_results
        
        print(f"  F1 (default): {avg_results['f1']:.4f}")
        print(f"  F1 (optimal): {avg_results['optimal_f1']:.4f}")
        print(f"  Precision: {avg_results['precision']:.4f}")
        print(f"  Recall: {avg_results['recall']:.4f}")
        print(f"  AUC: {avg_results['auc']:.4f}")
        print(f"  Optimal Threshold: {avg_results['optimal_threshold']:.3f}")
    
    # หากลยุทธ์ที่ดีที่สุด
    best_strategy = max(results.items(), key=lambda x: x[1]['optimal_f1'])
    print(f"\n🏆 กลยุทธ์ที่ดีที่สุด: {best_strategy[0]}")
    print(f"  F1 Score: {best_strategy[1]['optimal_f1']:.4f}")
    
    return results

def test_sampling_strategies():
    """ทดสอบกลยุทธ์ sampling ต่างๆ"""
    print(f"\n🔄 ทดสอบกลยุทธ์ Sampling")
    print("-"*60)
    
    # สร้างข้อมูลตัวอย่าง
    np.random.seed(42)
    n_samples = 800
    n_features = 15
    
    X = pd.DataFrame(np.random.randn(n_samples, n_features), 
                     columns=[f'feature_{i}' for i in range(n_features)])
    
    # สร้าง highly imbalanced target (10% positive)
    important_features = X.iloc[:, :3].sum(axis=1)
    noise = np.random.randn(n_samples) * 0.2
    y_continuous = important_features + noise
    y = (y_continuous > np.percentile(y_continuous, 90)).astype(int)
    
    print(f"📊 Original distribution: {pd.Series(y).value_counts().to_dict()}")
    
    # แบ่งข้อมูล train/test
    split_idx = int(0.8 * len(X))
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]
    
    sampling_strategies = {
        "No Sampling": None,
        "SMOTE": SMOTE(random_state=42),
        "Random Under": RandomUnderSampler(random_state=42),
        "SMOTEENN": SMOTEENN(random_state=42)
    }
    
    results = {}
    
    for strategy_name, sampler in sampling_strategies.items():
        print(f"\n🧪 ทดสอบ: {strategy_name}")
        
        # เตรียมข้อมูล
        if sampler is None:
            X_train_resampled, y_train_resampled = X_train, y_train
        else:
            X_train_resampled, y_train_resampled = sampler.fit_resample(X_train, y_train)
        
        print(f"  ข้อมูลหลัง sampling: {pd.Series(y_train_resampled).value_counts().to_dict()}")
        
        # Scale ข้อมูล
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train_resampled)
        X_test_scaled = scaler.transform(X_test)
        
        # สร้างและฝึกโมเดล
        model = lgb.LGBMClassifier(
            n_estimators=500,
            learning_rate=0.05,
            max_depth=4,
            num_leaves=8,
            min_data_in_leaf=15,
            random_state=42,
            verbose=-1
        )
        
        model.fit(X_train_scaled, y_train_resampled)
        
        # ทำนาย
        y_proba = model.predict_proba(X_test_scaled)[:, 1]
        y_pred_default = model.predict(X_test_scaled)
        
        # หา optimal threshold
        optimal_threshold, optimal_f1 = calculate_optimal_threshold(y_test, y_proba)
        y_pred_optimal = (y_proba >= optimal_threshold).astype(int)
        
        # คำนวณ metrics
        results[strategy_name] = {
            'f1_default': f1_score(y_test, y_pred_default),
            'f1_optimal': optimal_f1,
            'precision': precision_score(y_test, y_pred_optimal, zero_division=0),
            'recall': recall_score(y_test, y_pred_optimal, zero_division=0),
            'auc': roc_auc_score(y_test, y_proba),
            'optimal_threshold': optimal_threshold
        }
        
        print(f"  F1 (default): {results[strategy_name]['f1_default']:.4f}")
        print(f"  F1 (optimal): {results[strategy_name]['f1_optimal']:.4f}")
        print(f"  Precision: {results[strategy_name]['precision']:.4f}")
        print(f"  Recall: {results[strategy_name]['recall']:.4f}")
        print(f"  AUC: {results[strategy_name]['auc']:.4f}")
    
    # หากลยุทธ์ที่ดีที่สุด
    best_strategy = max(results.items(), key=lambda x: x[1]['f1_optimal'])
    print(f"\n🏆 กลยุทธ์ที่ดีที่สุด: {best_strategy[0]}")
    print(f"  F1 Score: {best_strategy[1]['f1_optimal']:.4f}")
    
    return results

def generate_f1_improvement_code():
    """สร้างโค้ดสำหรับปรับปรุง F1 Score"""
    print(f"\n🔧 โค้ดสำหรับปรับปรุง F1 Score")
    print("="*80)
    
    code_snippets = {
        "1. Class Weight Function": '''
def get_optimal_class_weight(y):
    """คำนวณ class weight ที่เหมาะสม"""
    class_counts = pd.Series(y).value_counts()
    minority_class = class_counts.idxmin()
    majority_class = class_counts.idxmax()
    
    ratio = class_counts[majority_class] / class_counts[minority_class]
    
    if ratio > 5:  # Severe imbalance
        return {majority_class: 1, minority_class: 5}
    elif ratio > 3:  # Moderate imbalance
        return {majority_class: 1, minority_class: 3}
    else:
        return "balanced"
''',
        
        "2. Optimal Threshold Function": '''
def find_optimal_threshold(y_true, y_proba, metric='f1'):
    """หา threshold ที่เหมาะสมสำหรับ metric ที่กำหนด"""
    thresholds = np.arange(0.1, 0.9, 0.01)
    scores = []
    
    for threshold in thresholds:
        y_pred = (y_proba >= threshold).astype(int)
        if metric == 'f1':
            score = f1_score(y_true, y_pred, zero_division=0)
        elif metric == 'precision':
            score = precision_score(y_true, y_pred, zero_division=0)
        elif metric == 'recall':
            score = recall_score(y_true, y_pred, zero_division=0)
        scores.append(score)
    
    optimal_idx = np.argmax(scores)
    return thresholds[optimal_idx], scores[optimal_idx]
''',
        
        "3. Enhanced Model Training": '''
def train_model_with_f1_optimization(X_train, y_train, X_val, y_val):
    """ฝึกโมเดลพร้อมการปรับปรุง F1 Score"""
    
    # คำนวณ class weight
    class_weight = get_optimal_class_weight(y_train)
    
    # สร้างโมเดล
    model = lgb.LGBMClassifier(
        n_estimators=2000,
        learning_rate=0.05,
        max_depth=4,
        num_leaves=8,
        min_data_in_leaf=25,
        reg_alpha=0.2,
        reg_lambda=0.2,
        class_weight=class_weight,
        random_state=42,
        verbose=-1
    )
    
    # ฝึกโมเดล
    model.fit(
        X_train, y_train,
        eval_set=[(X_val, y_val)],
        eval_metric='auc',
        callbacks=[
            lgb.early_stopping(stopping_rounds=150, verbose=False)
        ]
    )
    
    # หา optimal threshold
    y_proba = model.predict_proba(X_val)[:, 1]
    optimal_threshold, optimal_f1 = find_optimal_threshold(y_val, y_proba, 'f1')
    
    return model, optimal_threshold, optimal_f1
'''
    }
    
    for title, code in code_snippets.items():
        print(f"\n{title}:")
        print(code)
    
    return code_snippets

def main():
    """ฟังก์ชันหลัก"""
    print("🎯 ปรับปรุง F1 Score ของโมเดล LightGBM Trading")
    print("="*80)
    
    # 1. ทดสอบ class weight strategies
    class_weight_results = test_class_weight_strategies()
    
    # 2. ทดสอบ sampling strategies
    sampling_results = test_sampling_strategies()
    
    # 3. สร้างโค้ดสำหรับปรับปรุง
    code_snippets = generate_f1_improvement_code()
    
    # 4. สรุปคำแนะนำ
    print(f"\n🚀 คำแนะนำการปรับปรุง F1 Score:")
    print("="*80)
    print("1. เพิ่มฟังก์ชัน get_optimal_class_weight() ใน python_LightGBM_15_Tuning.py")
    print("2. เพิ่มฟังก์ชัน find_optimal_threshold() สำหรับหา threshold ที่เหมาะสม")
    print("3. ปรับปรุงการฝึกโมเดลให้ใช้ class_weight อัตโนมัติ")
    print("4. เพิ่มการหา optimal threshold ในการประเมินผล")
    print("5. พิจารณาใช้ SMOTE สำหรับข้อมูลที่ imbalance มาก")
    
    print(f"\n📊 เป้าหมาย F1 Score:")
    print("  • F1 Score: 0.68 → 0.85+ (เพิ่ม 25%)")
    print("  • Precision: > 0.80")
    print("  • Recall: > 0.75")
    print("  • Balanced Performance across all symbols")

if __name__ == "__main__":
    main()

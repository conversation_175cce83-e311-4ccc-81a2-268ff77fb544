#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

def test_server_debug():
    """ทดสอบ server และดู debug log"""
    
    url = 'http://127.0.0.1:54321/data'
    
    # สร้างข้อมูลทดสอบง่ายๆ
    test_data = {
        "symbol": "GOLD",
        "timeframe_str": "PERIOD_M30",
        "bars": [
            {
                "time": 1737021600,
                "open": 2650.0,
                "high": 2655.0,
                "low": 2648.0,
                "close": 2652.0,
                "volume": 1000,
                "tick_volume": 1000,
                "spread": 5,
                "real_volume": 1000
            }
        ]
    }
    
    try:
        print("🔍 Sending simple test data to server...")
        print(f"URL: {url}")
        print(f"Data: {len(test_data['bars'])} bars")
        
        # ส่งข้อมูลไป server
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"✅ Server response: HTTP {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("\n📊 Server response:")
                print(f"  Signal: {result.get('signal', 'N/A')}")
                print(f"  Class: {result.get('class', 'N/A')}")
                print(f"  Confidence: {result.get('confidence', 'N/A')}")
                
                return result.get('signal') != 'ERROR'
                
            except json.JSONDecodeError:
                print(f"❌ Cannot decode JSON: {response.text[:200]}...")
                return False
        else:
            print(f"❌ Server error: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing server with simple data...")
    
    success = test_server_debug()
    
    if success:
        print("\n✅ Simple test successful")
    else:
        print("\n❌ Simple test failed")
        
    # รอสักครู่แล้วดู log
    print("\n⏳ Waiting for server logs...")
    time.sleep(5)
    
    # อ่าน log file
    try:
        with open('server_debug.log', 'r', encoding='utf-8') as f:
            log_content = f.read()
        print("\n📝 Server debug log:")
        print(log_content[-2000:])  # แสดง 2000 ตัวอักษรสุดท้าย
    except Exception as e:
        print(f"❌ Cannot read log file: {e}")

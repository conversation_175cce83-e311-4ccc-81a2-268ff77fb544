📋 สรุปการ Mapping ไฟล์ CSV สำหรับเทรนโมเดล
================================================================
วันที่สร้าง: 2025-07-12
ไฟล์ต้นฉบับ: MT5_250711/
ไฟล์ที่แก้ไขแล้ว: CSV_Files_Fixed/

🔄 การแปลงชื่อไฟล์:
================================================================

📊 M30 Timeframe (8 ไฟล์):
----------------------------------------------------------------
MT5_250711/AUDUSD#_M30_201907080000_202507112330.csv
  → CSV_Files_Fixed/AUDUSD_M30_FIXED.csv

MT5_250711/EURGBP#_M30_201907080000_202507112330.csv
  → CSV_Files_Fixed/EURGBP_M30_FIXED.csv

MT5_250711/EURUSD#_M30_201907080000_202507112330.csv
  → CSV_Files_Fixed/EURUSD_M30_FIXED.csv

MT5_250711/GBPUSD#_M30_201907080000_202507112330.csv
  → CSV_Files_Fixed/GBPUSD_M30_FIXED.csv

MT5_250711/GOLD#_M30_201907080100_202507112330.csv
  → CSV_Files_Fixed/GOLD_M30_FIXED.csv

MT5_250711/NZDUSD#_M30_201907080000_202507112330.csv
  → CSV_Files_Fixed/NZDUSD_M30_FIXED.csv

MT5_250711/USDCAD#_M30_201907080000_202507112330.csv
  → CSV_Files_Fixed/USDCAD_M30_FIXED.csv

MT5_250711/USDJPY#_M30_201907080000_202507112330.csv
  → CSV_Files_Fixed/USDJPY_M30_FIXED.csv

📊 H1 (M60) Timeframe (8 ไฟล์):
----------------------------------------------------------------
MT5_250711/AUDUSD#_H1_201307080000_202507112300.csv
  → CSV_Files_Fixed/AUDUSD_H1_FIXED.csv

MT5_250711/EURGBP#_H1_201307080000_202507112300.csv
  → CSV_Files_Fixed/EURGBP_H1_FIXED.csv

MT5_250711/EURUSD#_H1_201307080000_202507112300.csv
  → CSV_Files_Fixed/EURUSD_H1_FIXED.csv

MT5_250711/GBPUSD#_H1_201307080000_202507112300.csv
  → CSV_Files_Fixed/GBPUSD_H1_FIXED.csv

MT5_250711/GOLD#_H1_201307080000_202507112300.csv
  → CSV_Files_Fixed/GOLD_H1_FIXED.csv

MT5_250711/NZDUSD#_H1_201307080000_202507112300.csv
  → CSV_Files_Fixed/NZDUSD_H1_FIXED.csv

MT5_250711/USDCAD#_H1_201307080000_202507112300.csv
  → CSV_Files_Fixed/USDCAD_H1_FIXED.csv

MT5_250711/USDJPY#_H1_201307080000_202507112300.csv
  → CSV_Files_Fixed/USDJPY_H1_FIXED.csv

🎯 การใช้งานใน python_LightGBM_16_Signal.py:
================================================================
test_groups = {
    "M30": [
        "CSV_Files_Fixed/AUDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/EURGBP_M30_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GBPUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GOLD_M30_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_M30_FIXED.csv"
    ],
    "M60": [
        "CSV_Files_Fixed/AUDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/EURGBP_H1_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/GOLD_H1_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_H1_FIXED.csv"
    ]
}

📝 หมายเหตุ:
================================================================
• ไฟล์ต้นฉบับจะถูกสำรองไว้ใน CSV_Files_Backup/
• ไฟล์ที่แก้ไขแล้วจะอยู่ใน CSV_Files_Fixed/
• รูปแบบไฟล์ใหม่: {SYMBOL}_{TIMEFRAME}_FIXED.csv
• ข้อมูล M30 เริ่มตั้งแต่ 2019-07-08
• ข้อมูล H1 เริ่มตั้งแต่ 2013-07-08
• ข้อมูลทั้งหมดถึง 2025-07-11
• รวม 16 ไฟล์ (8 คู่สกุลเงิน x 2 timeframes)

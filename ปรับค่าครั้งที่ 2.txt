ปรับค่าครั้งที่ 2
แก้ไข code ตามผลการทดสอบให้มีประสิทธิภาพมากขึ้น

Parameter Stability Analysis (Multi-Architecture Support)
======================================================================
พบ Model Architectures: ['Multi']

โหลดพารามิเตอร์จาก Multi-Model Architecture...
โหลดได้ 32 โมเดลจาก Multi Architecture
วิเคราะห์ Parameter Stability
============================================================
จำนวน models ที่พบ: 32

ข้อมูลพื้นฐาน:
Symbols: ['AUDUSD', 'EURGBP', 'EURUSD', 'GBPUSD', 'GOLD', 'NZDUSD', 'USDCAD', 'USDJPY']
Timeframes: [30, 60]
Architectures: ['Multi']

Multi Architecture:
   จำนวนโมเดล: 32
   Scenarios: ['counter_trend', 'trend_following']
     - counter_trend: 16 โมเดล
     - trend_following: 16 โมเดล

การกระจายของพารามิเตอร์ (รวมทุก Architecture):
------------------------------------------------------------
learning_rate       : Mean=0.0529, Std=0.0414, CV=78.3%
num_leaves          : Mean=22.7500, Std=8.9551, CV=39.4%
max_depth           : Mean=5.3125, Std=1.6547, CV=31.1%
min_data_in_leaf    : Mean=11.8438, Std=4.4075, CV=37.2%
feature_fraction    : Mean=0.8316, Std=0.0995, CV=12.0%
bagging_fraction    : Mean=0.7912, Std=0.0932, CV=11.8%

การกระจายของพารามิเตอร์ใน Multi Architecture:
------------------------------------------------------------
learning_rate       : Mean=0.0529, Std=0.0414, CV=78.3% (Low)
num_leaves          : Mean=22.7500, Std=8.9551, CV=39.4% (Medium)
max_depth           : Mean=5.3125, Std=1.6547, CV=31.1% (Medium)
min_data_in_leaf    : Mean=11.8438, Std=4.4075, CV=37.2% (Medium)
feature_fraction    : Mean=0.8316, Std=0.0995, CV=12.0% (High)
bagging_fraction    : Mean=0.7912, Std=0.0932, CV=11.8% (High)

การกระจายของพารามิเตอร์ใน counter_trend scenario:
--------------------------------------------------
learning_rate       : Mean=0.0636, Std=0.0485, CV=76.3% (Low)
num_leaves          : Mean=27.1250, Std=10.8804, CV=40.1% (Medium)
max_depth           : Mean=4.7500, Std=1.4832, CV=31.2% (Medium)
min_data_in_leaf    : Mean=11.5625, Std=4.7745, CV=41.3% (Medium)
feature_fraction    : Mean=0.8300, Std=0.1230, CV=14.8% (High)
bagging_fraction    : Mean=0.7622, Std=0.1003, CV=13.2% (High)

การกระจายของพารามิเตอร์ใน trend_following scenario:
--------------------------------------------------
learning_rate       : Mean=0.0422, Std=0.0307, CV=72.7% (Low)
num_leaves          : Mean=18.3750, Std=2.5528, CV=13.9% (High)
max_depth           : Mean=5.8750, Std=1.6683, CV=28.4% (Medium)
min_data_in_leaf    : Mean=12.1250, Std=4.1453, CV=34.2% (Medium)
feature_fraction    : Mean=0.8333, Std=0.0730, CV=8.8% (High)
bagging_fraction    : Mean=0.8203, Std=0.0780, CV=9.5% (High)

การวิเคราะห์ตาม Timeframe:
------------------------------------------------------------

Timeframe 30 (16 models):
  learning_rate: 0.0669 +/- 0.0447
  num_leaves: 22.9375 +/- 9.4901

Timeframe 60 (16 models):
  learning_rate: 0.0389 +/- 0.0335
  num_leaves: 22.5625 +/- 8.6946

การวิเคราะห์ตาม Symbol Type:
------------------------------------------------------------

Forex (28 models):
  learning_rate: 0.0454 +/- 0.0360
  num_leaves: 22.4643 +/- 9.0410

Commodities (4 models):
  learning_rate: 0.1050 +/- 0.0439
  num_leaves: 24.7500 +/- 9.3229

สรุปความเสถียรของพารามิเตอร์:
------------------------------------------------------------
High Stability (CV < 20%): feature_fraction, bagging_fraction
Medium Stability (CV 20-50%): num_leaves, max_depth, min_data_in_leaf
Low Stability (CV > 50%): learning_rate

แนะนำการปรับปรุง:
------------------------------------------------------------
1. พารามิเตอร์ที่ไม่เสถียร (learning_rate):
   - ลองใช้ค่าเฉลี่ยเป็น default
   - ลดช่วงการค้นหาใน param_dist
   - เพิ่มข้อมูลสำหรับ training

รายงานเปรียบเทียบ Architecture:
============================================================

เปรียบเทียบ Performance:
----------------------------------------
Multi          : Avg Score=0.4385 +/- 0.0399 (32 models)

เปรียบเทียบ Parameter Stability:
----------------------------------------

learning_rate:
  Multi       : CV= 77.0% (Low)

num_leaves:
  Multi       : CV= 38.7% (Medium)

max_depth:
  Multi       : CV= 30.7% (Medium)

min_data_in_leaf:
  Multi       : CV= 36.6% (Medium)

แนะนำการอัปเดต param_dist:
============================================================
แนะนำการปรับปรุง param_dist:
```python
param_dist = {
    'learning_rate': [0.01, 0.02, 0.05, 0.1],
    'feature_fraction': [0.7000000000000001, 0.8, 0.9],
    'bagging_fraction': [0.7000000000000001, 0.8, 0.9],
}
```

การวิเคราะห์เสร็จสิ้น
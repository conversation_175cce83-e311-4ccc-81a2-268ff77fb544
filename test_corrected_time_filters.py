#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแปลง Time Filters ที่แก้ไขแล้ว
"""

def format_hours_corrected(hours_str):
    """แปลง Hours ให้อ่านง่าย (เวอร์ชันที่แก้ไขแล้ว)"""
    if not hours_str or hours_str.strip() == "":
        return ""
    
    # ทำความสะอาด string
    clean_str = hours_str.replace(" ", "").replace("[", "").replace("]", "")
    
    # แยกด้วย comma
    try:
        hour_parts = [int(x.strip()) for x in clean_str.split(",") if x.strip().isdigit()]
        hour_parts = [h for h in hour_parts if 0 <= h <= 23]  # กรองเฉพาะ 0-23
    except:
        return hours_str
    
    if not hour_parts:
        return hours_str
    
    # ตรวจสอบว่าเป็นทุกชั่วโมงหรือไม่
    if len(hour_parts) == 24 and set(hour_parts) == set(range(24)):
        return "24/7"
    
    # Sort hours
    hour_parts.sort()
    
    # Create ranges
    ranges = []
    start = hour_parts[0]
    end = hour_parts[0]
    
    for i in range(1, len(hour_parts)):
        if hour_parts[i] == end + 1:
            end = hour_parts[i]
        else:
            # Add current range
            if start == end:
                ranges.append(str(start))
            else:
                ranges.append(f"{start}-{end}")
            start = hour_parts[i]
            end = hour_parts[i]
    
    # Add final range
    if start == end:
        ranges.append(str(start))
    else:
        ranges.append(f"{start}-{end}")
    
    return ", ".join(ranges)

def format_days_corrected(days_str):
    """แปลง Days ให้อ่านง่าย (เวอร์ชันที่แก้ไขแล้ว)"""
    if not days_str or days_str.strip() == "":
        return ""
    
    # ทำความสะอาด string
    clean_str = days_str.replace(" ", "").replace("[", "").replace("]", "")
    
    # แยกด้วย comma
    try:
        day_parts = [int(x.strip()) for x in clean_str.split(",") if x.strip().isdigit()]
        day_parts = [d for d in day_parts if 0 <= d <= 6]  # กรองเฉพาะ 0-6
    except:
        return days_str
    
    if not day_parts:
        return days_str
    
    # ตรวจสอบรูปแบบพิเศษ
    day_set = set(day_parts)
    
    if day_set == {0, 1, 2, 3, 4, 5, 6}:
        return "Every Day"
    elif day_set == {0, 1, 2, 3, 4}:
        return "Weekdays"
    elif day_set == {5, 6}:
        return "Weekends"
    
    # แปลงเป็นชื่อวัน
    day_names = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
    result = []
    
    day_parts.sort()
    for day in day_parts:
        result.append(day_names[day])
    
    return ",".join(result)

def format_time_filters_corrected(time_filters):
    """แปลง Time Filters ให้อ่านง่าย (เวอร์ชันที่แก้ไขแล้ว)"""
    if not time_filters:
        return "No filters"
    
    # แยก Days และ Hours
    days_part = ""
    hours_part = ""
    
    # หา Days part
    if "Days:[" in time_filters:
        start = time_filters.find("Days:[") + 6
        end = time_filters.find("]", start)
        if end > start:
            days_part = time_filters[start:end]
    
    # หา Hours part
    if "Hours:[" in time_filters:
        start = time_filters.find("Hours:[") + 7
        end = time_filters.find("]", start)
        if end > start:
            hours_part = time_filters[start:end]
    
    # แปลง Days และ Hours
    days_formatted = format_days_corrected(days_part)
    hours_formatted = format_hours_corrected(hours_part)
    
    # รวมผลลัพธ์
    if days_formatted and hours_formatted:
        return f"{days_formatted}, {hours_formatted}"
    elif days_formatted:
        return days_formatted
    elif hours_formatted:
        return hours_formatted
    else:
        return "No filters"

def test_corrected_formatting():
    """ทดสอบการแปลงที่แก้ไขแล้ว"""
    
    print("🔧 ทดสอบการแปลง Time Filters ที่แก้ไขแล้ว")
    print("="*60)
    
    test_cases = [
        {
            "input": "Days:[0,1,2,3,4,5,6],Hours:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23]",
            "description": "ทุกวัน ทุกชั่วโมง",
            "expected": "Every Day, 24/7"
        },
        {
            "input": "Days:[0,1,2,3,4,5,6],Hours:[7,8,11,21]",
            "description": "ทุกวัน ช่วงเวลาเฉพาะ",
            "expected": "Every Day, 7-8, 11, 21"
        },
        {
            "input": "Days:[0,1,2,3,4],Hours:[9,10,11,12,13,14,15,16]",
            "description": "วันทำงาน เวลาทำงาน",
            "expected": "Weekdays, 9-16"
        },
        {
            "input": "Days:[5,6],Hours:[10,11,12,13,14,15,16,17,18,19,20]",
            "description": "วันหยุด",
            "expected": "Weekends, 10-20"
        },
        {
            "input": "Days:[1,3,5],Hours:[6,7,8,9,10,11,12,19,20,21]",
            "description": "วันเฉพาะ ช่วงเวลาเฉพาะ",
            "expected": "Tue,Wed,Sat, 6-12, 19-21"
        },
        {
            "input": "Days:[0,1,2,3,4],Hours:[8,9,10,14,15,16,20,21,22]",
            "description": "วันทำงาน ช่วงเวลาแยกกัน",
            "expected": "Weekdays, 8-10, 14-16, 20-22"
        },
        {
            "input": "Days:[0, 1, 2, 3, 4, 5, 6],Hours:[7, 8, 11, 21]",
            "description": "ตัวอย่างจากระบบจริง (มีช่องว่าง)",
            "expected": "Every Day, 7-8, 11, 21"
        }
    ]
    
    print("📊 ผลการทดสอบ:")
    print("-" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        input_str = test_case["input"]
        description = test_case["description"]
        expected = test_case["expected"]
        actual = format_time_filters_corrected(input_str)
        
        print(f"\n{i}. {description}")
        print(f"   Input:    {input_str}")
        print(f"   Expected: {expected}")
        print(f"   Actual:   {actual}")
        
        if actual == expected:
            print(f"   ✅ ผ่าน")
        else:
            print(f"   ❌ ไม่ผ่าน")
        
        # ตรวจสอบความยาว
        if len(actual) <= 23:
            print(f"   📏 ความยาว: {len(actual)} ตัวอักษร (OK)")
        else:
            print(f"   ⚠️  ความยาว: {len(actual)} ตัวอักษร (เกิน 23)")

def preview_mt5_display_corrected():
    """แสดงตัวอย่างการแสดงผลใน MT5 ที่แก้ไขแล้ว"""
    
    print("\n" + "="*60)
    print("🎨 ตัวอย่างการแสดงผลใน MT5 EA (แก้ไขแล้ว)")
    print("="*60)
    
    # ตัวอย่างข้อมูลต่างๆ
    examples = [
        {
            "name": "HOLD Signal - ทุกวัน ช่วงเวลาเฉพาะ",
            "data": {
                'signal': 'HOLD',
                'class': 'HOLD',
                'confidence': 0.4522,
                'time_filters_raw': 'Days:[0,1,2,3,4,5,6],Hours:[7,8,11,21]'
            }
        },
        {
            "name": "BUY Signal - วันทำงาน เวลาทำงาน",
            "data": {
                'signal': 'BUY',
                'class': 'STRONG_BUY',
                'confidence': 0.7850,
                'time_filters_raw': 'Days:[0,1,2,3,4],Hours:[9,10,11,12,13,14,15,16]'
            }
        },
        {
            "name": "SELL Signal - วันหยุด",
            "data": {
                'signal': 'SELL',
                'class': 'WEAK_SELL',
                'confidence': 0.6200,
                'time_filters_raw': 'Days:[5,6],Hours:[10,11,12,13,14,15,16,17,18,19,20]'
            }
        }
    ]
    
    for example in examples:
        name = example["name"]
        data = example["data"]
        
        print(f"\n📱 {name}:")
        print("-" * 50)
        
        # แปลง time_filters
        formatted_filters = format_time_filters_corrected(data['time_filters_raw'])
        
        print("╔═══════════════════════════════════════╗")
        print("║           🐍 PYTHON ANALYSIS          ║")
        print("╠═══════════════════════════════════════╣")
        print(f"║ Symbol: GOLD      Timeframe: H1      ║")
        print(f"║ Signal: {data['signal']:<8}  Class: {data['class']:<8}    ║")
        print(f"║ Confidence: {data['confidence']:<6.3f}  Threshold: 0.500  ║")
        print(f"║ Spread: 25        SL_Bars: 6         ║")
        
        # แสดงราคาเฉพาะเมื่อมี signal
        if data['signal'] in ['BUY', 'SELL']:
            print(f"║ Entry Price: 2652.75000              ║")
        
        # แสดง Time Filters
        if len(formatted_filters) <= 23:
            print(f"║ Time Filters: {formatted_filters:<23} ║")
        else:
            line1 = formatted_filters[:23]
            line2 = formatted_filters[23:]
            print(f"║ Time Filters: {line1:<23} ║")
            if line2:
                print(f"║               {line2:<23} ║")
        
        print("╠═══════════════════════════════════════╣")
        print("║           🔧 MT5 CONFIGURATION        ║")
        print("╠═══════════════════════════════════════╣")
        print("║ Symbol: GOLD#    Magic: 16098        ║")
        print("║ MT5 Spread: 25      Point Value: 1.000  ║")
        print("║ Python Spread: 25      SL_Bars: 6        ║")
        print("║ Python Threshold: 0.500              ║")
        print("╚═══════════════════════════════════════╝")
        
        print(f"📏 Time Filters Length: {len(formatted_filters)} characters")

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 การแก้ไขการแปลง Time Filters ใน MT5 EA")
    print("="*60)
    print("🎯 ปัญหาที่แก้ไข:")
    print("   ❌ การแปลง Hours ผิดพลาด")
    print("   ❌ การตรวจสอบ string ไม่แม่นยำ")
    print("   ❌ การแยก comma ไม่ถูกต้อง")
    print()
    print("✅ วิธีแก้ไข:")
    print("   🔧 ใช้ StringSplit() แทน StringFind()")
    print("   🔧 ทำความสะอาด string ก่อนประมวลผล")
    print("   🔧 ตรวจสอบค่าที่ถูกต้อง (0-23 สำหรับ Hours)")
    print("   🔧 ใช้ set() เพื่อตรวจสอบรูปแบบพิเศษ")
    print("="*60)
    
    test_corrected_formatting()
    preview_mt5_display_corrected()
    
    print("\n" + "="*60)
    print("🎉 สรุปการแก้ไข:")
    print("   ✅ Days:[0,1,2,3,4,5,6] → Every Day")
    print("   ✅ Days:[0,1,2,3,4] → Weekdays")
    print("   ✅ Days:[5,6] → Weekends")
    print("   ✅ Hours:[0-23] → 24/7")
    print("   ✅ Hours:[7,8,11,21] → 7-8, 11, 21")
    print("   ✅ Hours:[9,10,11,12,13,14,15,16] → 9-16")
    print("   ✅ จัดการข้อความยาวด้วยการแบ่งบรรทัด")
    print("   ✅ แปลงได้ถูกต้องแม้มีช่องว่าง")

if __name__ == "__main__":
    main()

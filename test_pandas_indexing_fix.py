#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขปัญหา pandas indexing ใน predict_with_scenario_model
"""

import pandas as pd
import numpy as np

def test_pandas_reindex():
    """ทดสอบการใช้ pandas reindex เพื่อแก้ไขปัญหา missing features"""
    print("🔍 ทดสอบการแก้ไขปัญหา pandas indexing...")
    
    # สร้างข้อมูลทดสอบ
    # จำลองข้อมูลที่มีอยู่ (220 features)
    available_features = [f"feature_{i}" for i in range(220)]
    row_data = {feature: np.random.random() for feature in available_features}
    row = pd.Series(row_data)
    
    # จำลอง features ที่โมเดลต้องการ (216 features + 11 features ที่ขาดหายไป)
    required_features = [f"feature_{i}" for i in range(216)]  # features ที่มีอยู่
    missing_features = [
        'RSI_Divergence_i6', 
        'MACD_signal_x_RSI14', 
        'RSI14_x_StochK', 
        'RSI14_x_StochD', 
        'MACD_line_x_PriceMove',
        'ADX_14_x_RollingVol15',
        'RSI14_x_Volume',
        'ATR_x_PriceRange',
        'RSI14_x_PullBack_Up',
        'RSI14_x_PullBack_Down',
        'Target'
    ]
    
    features = required_features + missing_features  # รวม 227 features
    
    print(f"📊 ข้อมูลทดสอบ:")
    print(f"   Available features: {len(row.index)}")
    print(f"   Required features: {len(features)}")
    print(f"   Missing features: {len(missing_features)}")
    
    # ทดสอบวิธีเดิม (จะเกิด error)
    print(f"\n🚨 ทดสอบวิธีเดิม (ควรเกิด error):")
    try:
        missing_check = [f for f in features if f not in row.index]
        if missing_check:
            # วิธีเดิมที่จะเกิด error
            complete_row_old = pd.Series(0.0, index=features)
            available_features_subset = [f for f in features if f in row.index]
            # บรรทัดนี้จะเกิด error: cannot set using a list-like indexer with a different length than the value
            # complete_row_old[available_features_subset] = row[available_features_subset]
            print("❌ วิธีเดิมจะเกิด error เมื่อใช้ list indexing")
        else:
            print("✅ ไม่มี missing features")
    except Exception as e:
        print(f"❌ Error ตามที่คาดหวัง: {e}")
    
    # ทดสอบวิธีใหม่ (ใช้ reindex)
    print(f"\n✅ ทดสอบวิธีใหม่ (ใช้ reindex):")
    try:
        missing_check = [f for f in features if f not in row.index]
        if missing_check:
            print(f"⚠️ Missing features ({len(missing_check)}): {missing_check[:5]}...")
            print(f"📊 Available features: {len(row.index)} features")
            
            # วิธีใหม่ใช้ reindex
            complete_row = row.reindex(features, fill_value=0.0)
            
            available_features_count = len([f for f in features if f in row.index])
            print(f"✅ ใช้ {available_features_count} features ที่มีอยู่, เติม 0 สำหรับ {len(missing_check)} features ที่ขาดหายไป")
            
            # ตรวจสอบผลลัพธ์
            print(f"📊 Complete row shape: {complete_row.shape}")
            print(f"📊 Complete row length: {len(complete_row)}")
            print(f"📊 Features length: {len(features)}")
            
            # ตรวจสอบค่าที่เติม
            missing_values = complete_row[missing_features]
            print(f"📊 Missing features values: {missing_values.tolist()}")
            
            # เตรียมข้อมูลสำหรับโมเดล
            X = complete_row.values.reshape(1, -1)
            print(f"✅ X shape: {X.shape}")
            
            return True
        else:
            print("✅ ไม่มี missing features")
            X = row[features].values.reshape(1, -1)
            print(f"✅ X shape: {X.shape}")
            return True
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในวิธีใหม่: {e}")
        return False

def test_scenario_model_prediction():
    """ทดสอบการทำนายด้วย scenario model ที่แก้ไขแล้ว"""
    print(f"\n🤖 ทดสอบการทำนายด้วย scenario model...")
    
    try:
        from python_LightGBM_16_Signal import (
            load_scenario_models,
            predict_with_scenario_model
        )
        
        symbol = "GOLD"
        timeframe = 60
        
        # โหลดโมเดล
        scenario_models = load_scenario_models(symbol, timeframe)
        
        if not scenario_models:
            print("❌ ไม่สามารถโหลดโมเดลได้")
            return False
        
        # สร้างข้อมูลทดสอบ (จำลองข้อมูลจาก MT5)
        test_data = pd.Series({
            'Close': 2650.50,
            'High': 2655.00,
            'Low': 2645.00,
            'Open': 2648.00,
            'EMA200': 2640.00,
            'RSI14': 65.5,
            'ATR': 15.2,
            'Volume': 1000,
            'EMA50': 2645.00,
            'EMA_diff': 10.50,
            'BB_width': 25.0,
            'MACD_12_26_9': 2.5,
            'MACDs_12_26_9': 1.8,
            'MACDh_12_26_9': 0.7,
            'STOCHk_14_3_3': 70.2,
            'STOCHd_14_3_3': 68.5,
            'ADX_14': 25.8,
            'DMP_14': 28.5,
            'DMN_14': 15.2
        })
        
        # เพิ่มข้อมูล features เพิ่มเติมเพื่อให้ใกล้เคียงกับข้อมูลจริง
        for i in range(200):
            test_data[f'feature_{i}'] = np.random.random()
        
        print(f"📊 Test data features: {len(test_data)}")
        
        # ทดสอบการทำนาย BUY
        print(f"\n🔍 ทดสอบการทำนาย BUY:")
        should_trade_buy, confidence_buy, model_used_buy = predict_with_scenario_model(
            test_data, 'buy', scenario_models, 0.5
        )
        
        print(f"   Should trade: {should_trade_buy}")
        print(f"   Confidence: {confidence_buy:.4f}")
        print(f"   Model used: {model_used_buy}")
        
        # ทดสอบการทำนาย SELL
        print(f"\n🔍 ทดสอบการทำนาย SELL:")
        should_trade_sell, confidence_sell, model_used_sell = predict_with_scenario_model(
            test_data, 'sell', scenario_models, 0.5
        )
        
        print(f"   Should trade: {should_trade_sell}")
        print(f"   Confidence: {confidence_sell:.4f}")
        print(f"   Model used: {model_used_sell}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """ฟังก์ชันหลักสำหรับทดสอบ"""
    print("🚀 เริ่มทดสอบการแก้ไขปัญหา pandas indexing")
    print("="*80)
    
    # ทดสอบ pandas reindex
    if not test_pandas_reindex():
        print("❌ การทดสอบ pandas reindex ล้มเหลว")
        return
    
    # ทดสอบการทำนายด้วย scenario model
    if not test_scenario_model_prediction():
        print("❌ การทดสอบ scenario model prediction ล้มเหลว")
        return
    
    print("\n" + "="*80)
    print("✅ การทดสอบทั้งหมดผ่านเรียบร้อย!")
    print("🎉 การแก้ไขปัญหา pandas indexing สำเร็จ")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการปรับปรุงกับ 1 symbol ที่มีปัญหา F1 Score
"""

import sys
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Import functions จากไฟล์หลัก
try:
    from python_LightGBM_15_Tuning import (
        load_and_prepare_data,
        perform_hyperparameter_tuning,
        train_and_evaluate_model,
        get_lgbm_params
    )
    print("✅ Import functions สำเร็จ")
except ImportError as e:
    print(f"❌ Import ไม่สำเร็จ: {str(e)}")
    sys.exit(1)

def test_usdjpy_m30():
    """ทดสอบ USDJPY M30 ที่มี F1 Score ต่ำ (0.41)"""
    print("🧪 ทดสอบ USDJPY M30 (F1 Score ปัญหา)")
    print("=" * 60)
    
    symbol = 'USDJPY'
    timeframe = 30
    
    try:
        print(f"📊 โหลดข้อมูล {symbol} M{timeframe}...")
        
        # โหลดข้อมูล
        df = load_and_prepare_data(symbol, timeframe)
        
        if df is None or len(df) == 0:
            print(f"❌ ไม่สามารถโหลดข้อมูล {symbol} M{timeframe}")
            return None
        
        print(f"✅ โหลดข้อมูลสำเร็จ: {len(df)} rows")
        
        # ตรวจสอบ target distribution
        if 'target' in df.columns:
            target_counts = df['target'].value_counts()
            ratio = target_counts.max() / target_counts.min()
            print(f"📊 Target distribution: {target_counts.to_dict()}")
            print(f"📊 Imbalance ratio: {ratio:.1f}:1")
        
        # ทดสอบ hyperparameter tuning
        print(f"\n🔍 เริ่ม Hyperparameter Tuning...")
        print(f"⏱️ คาดว่าจะใช้เวลา 15-20 นาที...")
        
        # รัน hyperparameter tuning
        best_params, best_score, cv_results = perform_hyperparameter_tuning(
            df, symbol, timeframe, n_iter=20  # ลดจาก 50 เป็น 20 เพื่อประหยัดเวลา
        )
        
        if best_params is None:
            print(f"❌ Hyperparameter tuning ไม่สำเร็จ")
            return None
        
        print(f"✅ Hyperparameter tuning สำเร็จ!")
        print(f"🎯 Best CV Score: {best_score:.4f}")
        print(f"📊 Best Parameters:")
        for key, value in best_params.items():
            print(f"   {key}: {value}")
        
        # ทดสอบโมเดลด้วยพารามิเตอร์ที่ดีที่สุด
        print(f"\n🧪 ทดสอบโมเดลด้วยพารามิเตอร์ที่ดีที่สุด...")
        
        # เทรนและประเมินโมเดล
        results = train_and_evaluate_model(df, symbol, timeframe, best_params)
        
        if results:
            print(f"✅ การทดสอบโมเดลสำเร็จ!")
            print(f"📊 ผลลัพธ์:")
            
            # แสดงผลลัพธ์หลัก
            key_metrics = ['AUC', 'F1', 'Precision', 'Recall', 'CV_AUC']
            for metric in key_metrics:
                if metric in results:
                    print(f"   {metric}: {results[metric]:.4f}")
            
            # เปรียบเทียบกับผลลัพธ์เดิม
            old_results = {
                'AUC': 0.846807,
                'F1': 0.409931,
                'CV_AUC': 0.850475
            }
            
            print(f"\n📈 เปรียบเทียบกับผลลัพธ์เดิม:")
            for metric in ['AUC', 'F1', 'CV_AUC']:
                if metric in results and metric in old_results:
                    old_val = old_results[metric]
                    new_val = results[metric]
                    improvement = ((new_val - old_val) / old_val) * 100
                    status = "✅ ดีขึ้น" if new_val > old_val else "⚠️ แย่ลง"
                    print(f"   {metric}: {old_val:.3f} → {new_val:.3f} ({improvement:+.1f}%) {status}")
            
            return results
        else:
            print(f"❌ การทดสอบโมเดลไม่สำเร็จ")
            return None
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {str(e)}")
        return None

def test_eurgbp_h1():
    """ทดสอบ EURGBP H1 ที่มี F1 Score ต่ำ (0.45)"""
    print("\n🧪 ทดสอบ EURGBP H1 (F1 Score ปัญหา)")
    print("=" * 60)
    
    symbol = 'EURGBP'
    timeframe = 60
    
    try:
        print(f"📊 โหลดข้อมูล {symbol} H{timeframe//60}...")
        
        # โหลดข้อมูล
        df = load_and_prepare_data(symbol, timeframe)
        
        if df is None or len(df) == 0:
            print(f"❌ ไม่สามารถโหลดข้อมูล {symbol} H{timeframe//60}")
            return None
        
        print(f"✅ โหลดข้อมูลสำเร็จ: {len(df)} rows")
        
        # ตรวจสอบ target distribution
        if 'target' in df.columns:
            target_counts = df['target'].value_counts()
            ratio = target_counts.max() / target_counts.min()
            print(f"📊 Target distribution: {target_counts.to_dict()}")
            print(f"📊 Imbalance ratio: {ratio:.1f}:1")
        
        # ทดสอบ hyperparameter tuning (quick version)
        print(f"\n🔍 เริ่ม Quick Hyperparameter Tuning...")
        print(f"⏱️ คาดว่าจะใช้เวลา 10-15 นาที...")
        
        # รัน hyperparameter tuning
        best_params, best_score, cv_results = perform_hyperparameter_tuning(
            df, symbol, timeframe, n_iter=15  # ลดเป็น 15 เพื่อประหยัดเวลา
        )
        
        if best_params is None:
            print(f"❌ Hyperparameter tuning ไม่สำเร็จ")
            return None
        
        print(f"✅ Hyperparameter tuning สำเร็จ!")
        print(f"🎯 Best CV Score: {best_score:.4f}")
        print(f"📊 Best Parameters:")
        for key, value in best_params.items():
            print(f"   {key}: {value}")
        
        # เปรียบเทียบกับผลลัพธ์เดิม
        old_results = {
            'AUC': 0.883323,
            'F1': 0.445178,
            'CV_AUC': 0.849967
        }
        
        print(f"\n📈 เปรียบเทียบ CV Score:")
        old_cv_auc = old_results['CV_AUC']
        improvement = ((best_score - old_cv_auc) / old_cv_auc) * 100
        status = "✅ ดีขึ้น" if best_score > old_cv_auc else "⚠️ แย่ลง"
        print(f"   CV_AUC: {old_cv_auc:.3f} → {best_score:.3f} ({improvement:+.1f}%) {status}")
        
        return {'CV_AUC': best_score, 'best_params': best_params}
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {str(e)}")
        return None

def generate_recommendations(usdjpy_results, eurgbp_results):
    """สร้างคำแนะนำตามผลการทดสอบ"""
    print("\n🎯 คำแนะนำตามผลการทดสอบ")
    print("=" * 60)
    
    success_count = 0
    total_tests = 0
    
    if usdjpy_results:
        total_tests += 1
        if usdjpy_results.get('F1', 0) > 0.5:  # เป้าหมาย F1 > 0.5
            success_count += 1
    
    if eurgbp_results:
        total_tests += 1
        if eurgbp_results.get('CV_AUC', 0) > 0.85:  # เป้าหมาย CV_AUC > 0.85
            success_count += 1
    
    success_rate = (success_count / total_tests * 100) if total_tests > 0 else 0
    
    print(f"📊 ผลการทดสอบ: {success_count}/{total_tests} สำเร็จ ({success_rate:.0f}%)")
    
    if success_rate >= 50:
        print(f"\n✅ การปรับปรุงให้ผลดี - แนะนำให้ดำเนินการต่อ")
        print(f"🚀 ขั้นตอนถัดไป:")
        print(f"   1. รัน full training สำหรับทุก symbols")
        print(f"   2. คาดหวัง F1 Score เฉลี่ย > 0.65")
        print(f"   3. คาดหวัง AUC คงที่หรือดีขึ้น")
        print(f"   4. Overfitting gap ยังคงต่ำ")
        
        print(f"\n💻 คำสั่งที่แนะนำ:")
        print(f"   python python_LightGBM_15_Tuning.py")
        print(f"   (คาดว่าจะใช้เวลา 3-4 ชั่วโมง)")
        
    else:
        print(f"\n⚠️ การปรับปรุงยังไม่เพียงพอ - ต้องปรับเพิ่มเติม")
        print(f"🔧 แนะนำการปรับปรุงเพิ่มเติม:")
        print(f"   1. เพิ่ม class weight มากขึ้น")
        print(f"   2. ลด learning rate เพิ่มเติม")
        print(f"   3. เพิ่ม advanced features")
        print(f"   4. ปรับ threshold optimization")

def main():
    """ฟังก์ชันหลัก"""
    print("🧪 ทดสอบการปรับปรุง F1 Score กับ Symbols จริง")
    print("=" * 80)
    
    print("📋 แผนการทดสอบ:")
    print("1. USDJPY M30 (F1 Score ปัญหา: 0.41)")
    print("2. EURGBP H1 (F1 Score ปัญหา: 0.45)")
    print("3. วิเคราะห์ผลลัพธ์และให้คำแนะนำ")
    print()
    
    # ทดสอบ USDJPY M30
    usdjpy_results = test_usdjpy_m30()
    
    # ทดสอบ EURGBP H1
    eurgbp_results = test_eurgbp_h1()
    
    # สร้างคำแนะนำ
    generate_recommendations(usdjpy_results, eurgbp_results)
    
    # สรุปผลการทดสอบ
    print(f"\n🎉 สรุปการทดสอบ")
    print("=" * 80)
    
    if usdjpy_results:
        print(f"✅ USDJPY M30: ทดสอบสำเร็จ")
        if 'F1' in usdjpy_results:
            print(f"   F1 Score: {usdjpy_results['F1']:.3f}")
    else:
        print(f"❌ USDJPY M30: ทดสอบไม่สำเร็จ")
    
    if eurgbp_results:
        print(f"✅ EURGBP H1: ทดสอบสำเร็จ")
        if 'CV_AUC' in eurgbp_results:
            print(f"   CV_AUC: {eurgbp_results['CV_AUC']:.3f}")
    else:
        print(f"❌ EURGBP H1: ทดสอบไม่สำเร็จ")
    
    print(f"\n🎯 สรุป:")
    print("การปรับปรุง F1 Score ได้รับการทดสอบแล้ว")
    print("ผลลัพธ์จะช่วยตัดสินใจว่าควรรัน full training หรือไม่")

if __name__ == "__main__":
    main()

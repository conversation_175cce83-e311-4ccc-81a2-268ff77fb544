#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการทำงานของ run_main_analysis() กับระบบการจับเวลาใหม่
"""

import os
import sys
import time
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

def backup_current_time_files():
    """สำรองไฟล์ time summary ปัจจุบัน"""
    
    print("💾 สำรองไฟล์ time summary ปัจจุบัน")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import test_folder, test_groups
        
        backup_folder = f"{test_folder}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(backup_folder, exist_ok=True)
        
        for group_name in test_groups.keys():
            summary_file = os.path.join(test_folder, f"{group_name}_time_summary.txt")
            if os.path.exists(summary_file):
                backup_file = os.path.join(backup_folder, f"{group_name}_time_summary.txt")
                
                with open(summary_file, "r", encoding="utf-8") as src:
                    content = src.read()
                
                with open(backup_file, "w", encoding="utf-8") as dst:
                    dst.write(content)
                
                print(f"✅ สำรอง: {summary_file} -> {backup_file}")
        
        print(f"✅ สำรองเสร็จสิ้นใน: {backup_folder}")
        return backup_folder
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการสำรอง: {e}")
        return None

def test_single_round():
    """ทดสอบการรันแค่ 1 รอบ"""
    
    print("🧪 ทดสอบการรัน run_main_analysis() แค่ 1 รอบ")
    print("="*60)
    
    try:
        # Import ฟังก์ชันที่ต้องการ
        from python_LightGBM_16_Signal import (
            run_main_analysis, 
            NUM_TRAINING_ROUNDS,
            test_groups,
            test_folder
        )
        
        print(f"📊 การตั้งค่าปัจจุบัน:")
        print(f"   NUM_TRAINING_ROUNDS: {NUM_TRAINING_ROUNDS}")
        print(f"   test_groups: {list(test_groups.keys())}")
        
        # ตรวจสอบว่า NUM_TRAINING_ROUNDS = 1
        if NUM_TRAINING_ROUNDS != 1:
            print(f"⚠️ NUM_TRAINING_ROUNDS = {NUM_TRAINING_ROUNDS}, แนะนำให้เปลี่ยนเป็น 1 สำหรับการทดสอบ")
            response = input("ต้องการดำเนินการต่อหรือไม่? (y/N): ")
            if response.lower() not in ['y', 'yes']:
                print("❌ ยกเลิกการทดสอบ")
                return
        
        # ลบไฟล์ time summary เก่า
        print(f"\n🗑️ ลบไฟล์ time summary เก่า...")
        for group_name in test_groups.keys():
            summary_file = os.path.join(test_folder, f"{group_name}_time_summary.txt")
            if os.path.exists(summary_file):
                os.remove(summary_file)
                print(f"   ❌ ลบ: {summary_file}")
        
        # เริ่มจับเวลาการทดสอบ
        test_start_time = time.perf_counter()
        
        print(f"\n🚀 เริ่มรัน run_main_analysis()...")
        print(f"⏰ เวลาเริ่ม: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # รัน run_main_analysis()
        run_main_analysis()
        
        # หยุดจับเวลาการทดสอบ
        test_end_time = time.perf_counter()
        test_duration = test_end_time - test_start_time
        
        print(f"\n✅ run_main_analysis() เสร็จสิ้น")
        print(f"⏰ เวลาสิ้นสุด: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️ เวลารวมการทดสอบ: {test_duration:.4f} วินาที")
        
        # ตรวจสอบไฟล์ที่ถูกสร้าง
        print(f"\n📊 ตรวจสอบไฟล์ที่ถูกสร้าง:")
        for group_name in test_groups.keys():
            summary_file = os.path.join(test_folder, f"{group_name}_time_summary.txt")
            if os.path.exists(summary_file):
                print(f"   ✅ {summary_file}")
                
                # แสดงเนื้อหาไฟล์
                try:
                    with open(summary_file, "r", encoding="utf-8") as f:
                        content = f.read()
                    
                    lines = content.strip().split('\n')
                    print(f"      📄 เนื้อหา ({len(lines)} บรรทัด):")
                    for line in lines[-5:]:  # แสดง 5 บรรทัดสุดท้าย
                        if line.strip():
                            print(f"         {line}")
                            
                except Exception as e:
                    print(f"      ❌ ไม่สามารถอ่านไฟล์: {e}")
            else:
                print(f"   ❌ {summary_file} (ไม่พบ)")
        
        # ตรวจสอบไฟล์รายงานวิเคราะห์
        analysis_file = os.path.join(test_folder, "training_time_analysis.txt")
        if os.path.exists(analysis_file):
            print(f"\n📈 ไฟล์รายงานวิเคราะห์:")
            print(f"   ✅ {analysis_file}")
            
            try:
                with open(analysis_file, "r", encoding="utf-8") as f:
                    content = f.read()
                
                print(f"      📄 เนื้อหารายงาน:")
                lines = content.strip().split('\n')
                for line in lines[:15]:  # แสดง 15 บรรทัดแรก
                    print(f"         {line}")
                    
            except Exception as e:
                print(f"      ❌ ไม่สามารถอ่านไฟล์รายงาน: {e}")
        else:
            print(f"\n❌ ไม่พบไฟล์รายงานวิเคราะห์: {analysis_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_timing_results():
    """วิเคราะห์ผลลัพธ์การจับเวลา"""
    
    print(f"\n📊 วิเคราะห์ผลลัพธ์การจับเวลา")
    print("="*60)
    
    try:
        from python_LightGBM_16_Signal import analyze_time_summary_files
        
        # รันการวิเคราะห์
        stats = analyze_time_summary_files()
        
        if stats:
            print(f"\n📈 สรุปผลการวิเคราะห์:")
            
            total_time = 0
            total_files = 0
            
            for group_name, group_stats in stats.items():
                group_total_time = group_stats['avg_time'] * group_stats['total_runs']
                total_time += group_total_time
                total_files += group_stats['total_files'] * group_stats['total_runs']
                
                print(f"\n🎯 กลุ่ม {group_name}:")
                print(f"   📊 จำนวนรอบ: {group_stats['total_runs']}")
                print(f"   📁 ไฟล์ต่อรอบ: {group_stats['total_files']}")
                print(f"   ⏱️ เวลาเฉลี่ยต่อรอบ: {group_stats['avg_time']:.4f} วินาที")
                print(f"   ⚡ เวลาเร็วสุด: {group_stats['min_time']:.4f} วินาที")
                print(f"   🐌 เวลาช้าสุด: {group_stats['max_time']:.4f} วินาที")
                print(f"   📄 เวลาต่อไฟล์: {group_stats['avg_time']/group_stats['total_files']:.4f} วินาที")
                print(f"   🚀 ประสิทธิภาพ: {group_stats['total_files']/group_stats['avg_time']:.4f} ไฟล์/วินาที")
            
            print(f"\n🏆 สรุปรวม:")
            print(f"   ⏱️ เวลารวมทั้งหมด: {total_time:.4f} วินาที ({total_time/60:.2f} นาที)")
            print(f"   📁 ไฟล์รวมทั้งหมด: {total_files}")
            if total_time > 0:
                print(f"   🚀 ประสิทธิภาพรวม: {total_files/total_time:.4f} ไฟล์/วินาที")
        
        else:
            print(f"❌ ไม่พบข้อมูลสถิติ")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการวิเคราะห์: {e}")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🏗️ Test run_main_analysis() with New Timing System")
    print("="*60)
    
    # สำรองไฟล์ปัจจุบัน
    backup_folder = backup_current_time_files()
    
    # ทดสอบการรัน
    success = test_single_round()
    
    if success:
        # วิเคราะห์ผลลัพธ์
        analyze_timing_results()
        
        print(f"\n✅ การทดสอบเสร็จสิ้นสำเร็จ")
        
        if backup_folder:
            print(f"💾 ไฟล์สำรองอยู่ที่: {backup_folder}")
    else:
        print(f"\n❌ การทดสอบล้มเหลว")
    
    print(f"\n📋 สรุปการปรับปรุงระบบการจับเวลา:")
    print(f"   ✅ จับเวลาแต่ละรอบแยกกัน")
    print(f"   ✅ บันทึกผลลัพธ์ทันทีหลังแต่ละรอบ")
    print(f"   ✅ คำนวณสถิติที่ถูกต้อง (เฉลี่ย, min, max)")
    print(f"   ✅ สร้างรายงานสรุปรวม")
    print(f"   ✅ แยกไฟล์ตามกลุ่ม (M30, M60)")
    print(f"   ✅ รองรับการวิเคราะห์ย้อนหลัง")

if __name__ == "__main__":
    main()

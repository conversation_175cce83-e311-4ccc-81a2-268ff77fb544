#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ไฟล์ทดสอบ Multi-Model Architecture สำหรับ LightGBM Trading System
ทดสอบการทำงานของ 4 โมเดลแยกตามสถานการณ์ตลาด

Created: 2025-01-08
Author: AI Assistant
"""

import pandas as pd
import numpy as np
import os
import sys

# เพิ่ม path สำหรับ import จากไฟล์หลัก
sys.path.append('.')

# Import functions จากไฟล์หลัก
try:
    from python_LightGBM_16_Signal import (
        MARKET_SCENARIOS,
        USE_MULTI_MODEL_ARCHITECTURE,
        MIN_TRAINING_SAMPLES,
        detect_market_scenario,
        get_applicable_scenarios,
        filter_data_by_scenario,
        add_market_scenario_column,
        prepare_scenario_data,
        train_scenario_model,
        train_all_scenario_models,
        load_scenario_models,
        select_appropriate_model,
        predict_with_scenario_model
    )
    print("✅ Import สำเร็จ")
    print(f"📊 MIN_TRAINING_SAMPLES: {MIN_TRAINING_SAMPLES}")
except ImportError as e:
    print(f"❌ Import ล้มเหลว: {e}")
    sys.exit(1)

def create_sample_data():
    """สร้างข้อมูลตัวอย่างสำหรับทดสอบ"""
    print("\n📊 สร้างข้อมูลตัวอย่าง...")
    
    # สร้างข้อมูลราคา
    np.random.seed(42)
    n_samples = 1000
    
    # สร้างราคาพื้นฐาน
    base_price = 1.2000
    price_changes = np.random.normal(0, 0.001, n_samples)
    prices = [base_price]
    
    for change in price_changes:
        new_price = prices[-1] + change
        prices.append(max(new_price, 0.5))  # ป้องกันราคาติดลบ
    
    prices = prices[1:]  # ลบราคาเริ่มต้น
    
    # สร้าง OHLC
    data = []
    for i in range(n_samples):
        open_price = prices[i]
        high_price = open_price + abs(np.random.normal(0, 0.0005))
        low_price = open_price - abs(np.random.normal(0, 0.0005))
        close_price = open_price + np.random.normal(0, 0.0003)
        
        data.append({
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': np.random.randint(1000, 10000)
        })
    
    df = pd.DataFrame(data)
    
    # เพิ่ม EMA200
    df['ema200'] = df['close'].ewm(span=200).mean()
    
    # เพิ่ม indicators อื่นๆ
    df['ema50'] = df['close'].ewm(span=50).mean()
    df['rsi14'] = calculate_rsi(df['close'], 14)
    df['macd_signal'] = np.random.choice([-1, 0, 1], n_samples)
    df['volume_ma20'] = df['volume'].rolling(20).mean()
    df['pullback_buy'] = np.random.uniform(0, 1, n_samples)
    df['pullback_sell'] = np.random.uniform(0, 1, n_samples)
    df['ratio_buy'] = np.random.uniform(1, 10, n_samples)
    df['ratio_sell'] = np.random.uniform(1, 10, n_samples)
    
    # เพิ่ม Target สำหรับ multi-class
    df['Target_Multiclass'] = np.random.choice([0, 1, 2, 3, 4], n_samples, 
                                              p=[0.1, 0.2, 0.4, 0.2, 0.1])
    
    print(f"✅ สร้างข้อมูลตัวอย่าง: {len(df)} rows")
    return df

def calculate_rsi(prices, period=14):
    """คำนวณ RSI"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi.fillna(50)

def test_market_scenario_detection():
    """ทดสอบการตรวจจับสถานการณ์ตลาด"""
    print("\n🔍 ทดสอบการตรวจจับสถานการณ์ตลาด")
    print("="*50)
    
    # สร้างข้อมูลทดสอบ
    df = create_sample_data()
    
    # เพิ่มคอลัมน์ market_scenario
    df_with_scenario = add_market_scenario_column(df)
    
    # แสดงผลการกระจาย
    scenario_counts = df_with_scenario['market_scenario'].value_counts()
    print(f"\n📈 การกระจายสถานการณ์ตลาด:")
    for scenario, count in scenario_counts.items():
        percentage = count / len(df_with_scenario) * 100
        print(f"  {scenario}: {count} ({percentage:.1f}%)")
    
    return df_with_scenario

def test_scenario_filtering():
    """ทดสอบการกรองข้อมูลตาม scenario"""
    print("\n🔍 ทดสอบการกรองข้อมูลตาม scenario")
    print("="*50)
    
    df = create_sample_data()
    
    for scenario_name in MARKET_SCENARIOS.keys():
        print(f"\n📊 ทดสอบ {scenario_name}:")
        filtered_df = filter_data_by_scenario(df, scenario_name)
        print(f"   ข้อมูลที่กรองได้: {len(filtered_df)}/{len(df)} rows")

def test_model_training():
    """ทดสอบการเทรนโมเดล"""
    print("\n🔍 ทดสอบการเทรนโมเดล")
    print("="*50)
    
    df = create_sample_data()
    
    # ทดสอบการเทรนโมเดลทั้ง 4 scenarios
    try:
        results = train_all_scenario_models(df, "EURUSD", 30, "Target_Multiclass")
        
        if results:
            print(f"\n✅ เทรนโมเดลสำเร็จ: {len(results)} scenarios")
            for scenario, result in results.items():
                print(f"  {scenario}: Accuracy={result['accuracy']:.3f}, AUC={result['auc']:.3f}")
        else:
            print("❌ ไม่สามารถเทรนโมเดลได้")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการเทรน: {e}")

def test_model_selection():
    """ทดสอบการเลือกโมเดล"""
    print("\n🔍 ทดสอบการเลือกโมเดล")
    print("="*50)
    
    # สร้างข้อมูลตัวอย่าง
    df = create_sample_data()
    
    # สร้าง mock loaded_models
    loaded_models = {}
    for scenario in MARKET_SCENARIOS.keys():
        loaded_models[scenario] = {
            'model': f"mock_model_{scenario}",
            'features': ['close', 'high', 'low', 'ema200']
        }
    
    # ทดสอบการเลือกโมเดลในสถานการณ์ต่างๆ
    test_cases = [
        {'close': 1.2100, 'high': 1.2110, 'low': 1.2090, 'ema200': 1.2000, 'action': 'buy'},
        {'close': 1.1900, 'high': 1.1910, 'low': 1.1890, 'ema200': 1.2000, 'action': 'sell'},
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📊 Test Case {i}:")
        row = pd.Series(test_case)
        action = test_case['action']
        
        market_condition = detect_market_scenario(row)
        applicable_scenarios = get_applicable_scenarios(market_condition, action)
        
        print(f"  Market Condition: {market_condition}")
        print(f"  Action: {action}")
        print(f"  Applicable Scenarios: {applicable_scenarios}")
        
        selected_model = select_appropriate_model(row, action, loaded_models)
        if selected_model:
            print(f"  Selected Model: Available")
        else:
            print(f"  Selected Model: None")

def main():
    """ฟังก์ชันหลักสำหรับทดสอบ"""
    print("🚀 เริ่มทดสอบ Multi-Model Architecture")
    print("="*60)
    
    print(f"📋 การตั้งค่า:")
    print(f"  USE_MULTI_MODEL_ARCHITECTURE: {USE_MULTI_MODEL_ARCHITECTURE}")
    print(f"  จำนวน MARKET_SCENARIOS: {len(MARKET_SCENARIOS)}")
    
    try:
        # ทดสอบแต่ละส่วน
        test_market_scenario_detection()
        test_scenario_filtering()
        test_model_training()
        test_model_selection()
        
        print(f"\n✅ การทดสอบเสร็จสิ้น")
        
    except Exception as e:
        print(f"\n❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการปรับปรุงใน python_LightGBM_15_Tuning.py
"""

import sys
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Import functions จากไฟล์หลัก
try:
    from python_LightGBM_15_Tuning import (
        get_optimal_class_weight,
        find_optimal_threshold,
        get_lgbm_params,
        create_market_regime_features,
        param_dist
    )
    print("✅ Import functions สำเร็จ")
except ImportError as e:
    print(f"❌ Import ไม่สำเร็จ: {str(e)}")
    sys.exit(1)

def test_class_weight_function():
    """ทดสอบฟังก์ชัน get_optimal_class_weight"""
    print("\n🧪 ทดสอบ get_optimal_class_weight")
    print("-" * 50)
    
    # Test case 1: Balanced data
    y_balanced = np.array([0, 1, 0, 1, 0, 1] * 100)
    weight_balanced = get_optimal_class_weight(y_balanced)
    print(f"  Balanced data (50:50): {weight_balanced}")
    
    # Test case 2: Moderate imbalance
    y_moderate = np.array([0] * 300 + [1] * 100)
    weight_moderate = get_optimal_class_weight(y_moderate)
    print(f"  Moderate imbalance (75:25): {weight_moderate}")
    
    # Test case 3: Severe imbalance
    y_severe = np.array([0] * 800 + [1] * 100)
    weight_severe = get_optimal_class_weight(y_severe)
    print(f"  Severe imbalance (89:11): {weight_severe}")
    
    return True

def test_optimal_threshold_function():
    """ทดสอบฟังก์ชัน find_optimal_threshold"""
    print("\n🧪 ทดสอบ find_optimal_threshold")
    print("-" * 50)
    
    # สร้างข้อมูลทดสอบ
    np.random.seed(42)
    y_true = np.random.choice([0, 1], size=1000, p=[0.7, 0.3])
    y_proba = np.random.beta(2, 5, size=1000)  # Skewed probabilities
    
    # ทดสอบ F1 optimization
    threshold_f1, score_f1 = find_optimal_threshold(y_true, y_proba, 'f1')
    print(f"  Optimal F1 threshold: {threshold_f1:.3f}, F1 score: {score_f1:.3f}")
    
    # ทดสอบ Precision optimization
    threshold_prec, score_prec = find_optimal_threshold(y_true, y_proba, 'precision')
    print(f"  Optimal Precision threshold: {threshold_prec:.3f}, Precision: {score_prec:.3f}")
    
    # ทดสอบ Recall optimization
    threshold_rec, score_rec = find_optimal_threshold(y_true, y_proba, 'recall')
    print(f"  Optimal Recall threshold: {threshold_rec:.3f}, Recall: {score_rec:.3f}")
    
    return True

def test_updated_params():
    """ทดสอบพารามิเตอร์ที่อัปเดตแล้ว"""
    print("\n🧪 ทดสอบ Updated Parameters")
    print("-" * 50)
    
    # ทดสอบ param_dist
    print("📊 Updated param_dist:")
    for key, values in param_dist.items():
        print(f"  {key}: {values}")
    
    # ทดสอบ get_lgbm_params
    print("\n📊 Updated get_lgbm_params:")
    
    # สร้างข้อมูล y ทดสอบ
    y_test = np.array([0] * 700 + [1] * 300)  # 70:30 imbalance
    
    params = get_lgbm_params(y=y_test)
    
    print("  Key parameters:")
    key_params = ['learning_rate', 'num_leaves', 'max_depth', 'min_data_in_leaf', 
                  'reg_alpha', 'reg_lambda', 'feature_fraction', 'bagging_fraction']
    
    for param in key_params:
        if param in params:
            print(f"    {param}: {params[param]}")
    
    if 'class_weight' in params:
        print(f"    class_weight: {params['class_weight']}")
    
    return True

def test_market_regime_features():
    """ทดสอบฟังก์ชัน create_market_regime_features"""
    print("\n🧪 ทดสอบ create_market_regime_features")
    print("-" * 50)
    
    # สร้างข้อมูล OHLC ทดสอบ
    np.random.seed(42)
    n_samples = 1000
    
    # สร้าง realistic OHLC data
    base_price = 100
    returns = np.random.randn(n_samples) * 0.01
    close_prices = base_price * np.exp(np.cumsum(returns))
    
    df_test = pd.DataFrame({
        'Open': close_prices * (1 + np.random.randn(n_samples) * 0.001),
        'High': close_prices * (1 + abs(np.random.randn(n_samples)) * 0.005),
        'Low': close_prices * (1 - abs(np.random.randn(n_samples)) * 0.005),
        'Close': close_prices,
        'Volume': np.random.randint(1000, 10000, n_samples)
    })
    
    # ปรับ High/Low ให้สมเหตุสมผล
    df_test['High'] = df_test[['Open', 'Close', 'High']].max(axis=1)
    df_test['Low'] = df_test[['Open', 'Close', 'Low']].min(axis=1)
    
    print(f"  📊 ข้อมูลเริ่มต้น: {len(df_test)} rows, {len(df_test.columns)} columns")
    
    # เรียกใช้ฟังก์ชัน
    df_enhanced = create_market_regime_features(df_test.copy())
    
    # ตรวจสอบ features ใหม่
    new_features = [col for col in df_enhanced.columns if col not in df_test.columns]
    print(f"  ✅ เพิ่ม features ใหม่: {len(new_features)} features")
    
    for feature in new_features:
        non_null_count = df_enhanced[feature].notna().sum()
        print(f"    {feature}: {non_null_count}/{len(df_enhanced)} non-null values")
    
    return True

def test_parameter_comparison():
    """เปรียบเทียบพารามิเตอร์เดิมกับใหม่"""
    print("\n📊 เปรียบเทียบพารามิเตอร์เดิม vs ใหม่")
    print("=" * 60)
    
    # พารามิเตอร์เดิม
    old_params = {
        'learning_rate': 0.139,
        'num_leaves': 13,
        'max_depth': -1,
        'min_data_in_leaf': 12,
        'reg_alpha': 0.1,
        'reg_lambda': 0.1
    }
    
    # พารามิเตอร์ใหม่
    y_test = np.array([0] * 700 + [1] * 300)
    new_params = get_lgbm_params(y=y_test)
    
    print("Parameter Comparison:")
    print("-" * 60)
    print(f"{'Parameter':<20} {'Old Value':<15} {'New Value':<15} {'Change'}")
    print("-" * 60)
    
    for param in old_params.keys():
        old_val = old_params[param]
        new_val = new_params.get(param, 'N/A')
        
        if isinstance(old_val, (int, float)) and isinstance(new_val, (int, float)):
            if old_val != 0:
                change = f"{((new_val - old_val) / old_val) * 100:+.1f}%"
            else:
                change = "N/A"
        else:
            change = "Changed" if old_val != new_val else "Same"
        
        print(f"{param:<20} {str(old_val):<15} {str(new_val):<15} {change}")
    
    # แสดง features ใหม่
    new_features = ['class_weight', 'feature_fraction', 'bagging_fraction']
    print(f"\nNew Features:")
    for feature in new_features:
        if feature in new_params:
            print(f"  {feature}: {new_params[feature]}")

def main():
    """ฟังก์ชันหลัก"""
    print("🧪 ทดสอบการปรับปรุง python_LightGBM_15_Tuning.py")
    print("=" * 80)
    
    tests = [
        ("Class Weight Function", test_class_weight_function),
        ("Optimal Threshold Function", test_optimal_threshold_function),
        ("Updated Parameters", test_updated_params),
        ("Market Regime Features", test_market_regime_features),
        ("Parameter Comparison", test_parameter_comparison)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            success = test_func()
            results.append((test_name, "✅ PASS" if success else "❌ FAIL"))
        except Exception as e:
            print(f"❌ Error in {test_name}: {str(e)}")
            results.append((test_name, f"❌ ERROR: {str(e)}"))
    
    # สรุปผลลัพธ์
    print(f"\n🎯 สรุปผลการทดสอบ")
    print("=" * 80)
    
    for test_name, result in results:
        print(f"  {test_name}: {result}")
    
    passed_tests = sum(1 for _, result in results if "✅" in result)
    total_tests = len(results)
    
    print(f"\n📊 ผลลัพธ์รวม: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 การปรับปรุงสำเร็จทั้งหมด!")
        print("\n🚀 ขั้นตอนถัดไป:")
        print("1. รัน quick_tuning_test.py เพื่อทดสอบ hyperparameter tuning")
        print("2. เปรียบเทียบผลลัพธ์กับเดิม")
        print("3. รัน hyperparameter tuning ใหม่สำหรับทุก symbols")
    else:
        print("⚠️ มีการทดสอบที่ไม่ผ่าน กรุณาตรวจสอบและแก้ไข")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 ทดสอบความสอดคล้องของการตั้งค่าระหว่างไฟล์
"""

import sys
import os

def test_current_config():
    """ทดสอบการตั้งค่าปัจจุบันในไฟล์เดิม"""
    print("🔍 ตรวจสอบการตั้งค่าปัจจุบัน")
    print("=" * 60)
    
    try:
        # Import จากไฟล์หลัก
        sys.path.append('.')
        from python_LightGBM_16_Signal import (
            input_rsi_level_in as main_rsi_in,
            input_rsi_level_out as main_rsi_out,
            input_stop_loss_atr as main_sl_atr,
            input_take_profit as main_tp,
            input_pull_back as main_pullback,
            symbol_info as main_symbol_info
        )
        
        print("✅ โหลดการตั้งค่าจากไฟล์หลักสำเร็จ")
        print(f"📊 Main File Settings:")
        print(f"   RSI Level In: {main_rsi_in}")
        print(f"   RSI Level Out: {main_rsi_out}")
        print(f"   Stop Loss ATR: {main_sl_atr}")
        print(f"   Take Profit: {main_tp}")
        print(f"   Pull Back: {main_pullback}")
        
    except Exception as e:
        print(f"❌ ไม่สามารถโหลดจากไฟล์หลักได้: {e}")
        return False
    
    try:
        # Import จากไฟล์ server
        from python_to_mt5_WebRequest_server_12_Signal import (
            input_rsi_level_in as server_rsi_in,
            input_rsi_level_out as server_rsi_out,
            input_stop_loss_atr as server_sl_atr,
            input_take_profit as server_tp,
            input_pull_back as server_pullback,
            symbol_info_map as server_symbol_info
        )
        
        print("✅ โหลดการตั้งค่าจากไฟล์ server สำเร็จ")
        print(f"🌐 Server File Settings:")
        print(f"   RSI Level In: {server_rsi_in}")
        print(f"   RSI Level Out: {server_rsi_out}")
        print(f"   Stop Loss ATR: {server_sl_atr}")
        print(f"   Take Profit: {server_tp}")
        print(f"   Pull Back: {server_pullback}")
        
    except Exception as e:
        print(f"❌ ไม่สามารถโหลดจากไฟล์ server ได้: {e}")
        return False
    
    # เปรียบเทียบค่า
    print(f"\n🔍 การเปรียบเทียบค่า:")
    print("=" * 60)
    
    issues = []
    
    # ตรวจสอบ RSI Level In
    if main_rsi_in == server_rsi_in:
        print(f"✅ RSI Level In: {main_rsi_in} (ตรงกัน)")
    else:
        print(f"❌ RSI Level In: Main={main_rsi_in}, Server={server_rsi_in} (ไม่ตรงกัน!)")
        issues.append("RSI Level In")
    
    # ตรวจสอบ RSI Level Out
    if main_rsi_out == server_rsi_out:
        print(f"✅ RSI Level Out: {main_rsi_out} (ตรงกัน)")
    else:
        print(f"❌ RSI Level Out: Main={main_rsi_out}, Server={server_rsi_out} (ไม่ตรงกัน!)")
        issues.append("RSI Level Out")
    
    # ตรวจสอบ Stop Loss ATR
    if main_sl_atr == server_sl_atr:
        print(f"✅ Stop Loss ATR: {main_sl_atr} (ตรงกัน)")
    else:
        print(f"❌ Stop Loss ATR: Main={main_sl_atr}, Server={server_sl_atr} (ไม่ตรงกัน!)")
        issues.append("Stop Loss ATR")
    
    # ตรวจสอบ Take Profit
    if main_tp == server_tp:
        print(f"✅ Take Profit: {main_tp} (ตรงกัน)")
    else:
        print(f"❌ Take Profit: Main={main_tp}, Server={server_tp} (ไม่ตรงกัน!)")
        issues.append("Take Profit")
    
    # ตรวจสอบ Pull Back
    if main_pullback == server_pullback:
        print(f"✅ Pull Back: {main_pullback} (ตรงกัน)")
    else:
        print(f"❌ Pull Back: Main={main_pullback}, Server={server_pullback} (ไม่ตรงกัน!)")
        issues.append("Pull Back")
    
    # ตรวจสอบ Symbol Info
    print(f"\n🏦 ตรวจสอบ Symbol Info:")
    symbol_issues = []
    for symbol in ["GOLD", "AUDUSD", "EURUSD"]:
        if symbol in main_symbol_info and symbol in server_symbol_info:
            main_spread = main_symbol_info[symbol]["Spread"]
            server_spread = server_symbol_info[symbol]["Spread"]
            if main_spread == server_spread:
                print(f"✅ {symbol} Spread: {main_spread} (ตรงกัน)")
            else:
                print(f"❌ {symbol} Spread: Main={main_spread}, Server={server_spread} (ไม่ตรงกัน!)")
                symbol_issues.append(f"{symbol} Spread")
    
    # สรุปผล
    print(f"\n📋 สรุปผลการตรวจสอบ:")
    print("=" * 60)
    
    if not issues and not symbol_issues:
        print("🎉 ทุกการตั้งค่าตรงกันหมด!")
        return True
    else:
        print(f"⚠️  พบปัญหา {len(issues + symbol_issues)} รายการ:")
        for issue in issues + symbol_issues:
            print(f"   • {issue}")
        print(f"\n💡 แนะนำ: ใช้ไฟล์ trading_config.py เพื่อจัดการการตั้งค่าส่วนกลาง")
        return False

def test_new_config():
    """ทดสอบการตั้งค่าใหม่จาก trading_config.py"""
    print(f"\n🆕 ทดสอบการตั้งค่าใหม่จาก trading_config.py")
    print("=" * 60)

    try:
        import trading_config as config
        
        print("✅ โหลด trading_config.py สำเร็จ")
        print(f"📊 New Config Settings:")
        print(f"   RSI Level In: {config.INPUT_RSI_LEVEL_IN}")
        print(f"   RSI Level Out: {config.INPUT_RSI_LEVEL_OUT}")
        print(f"   Stop Loss ATR: {config.INPUT_STOP_LOSS_ATR}")
        print(f"   Take Profit: {config.INPUT_TAKE_PROFIT}")
        print(f"   Pull Back: {config.INPUT_PULL_BACK}")
        print(f"   Multi-Model Architecture: {config.USE_MULTI_MODEL_ARCHITECTURE}")
        print(f"   Multi-class Target: {config.USE_MULTICLASS_TARGET}")
        print(f"   Test Folder: {config.get_test_folder()}")

        # ทดสอบ functions
        print(f"\n🔧 ทดสอบ Utility Functions:")
        print(f"   Output Folder: {config.get_output_folder()}")

        # ทดสอบ Symbol Info
        gold_info = config.get_symbol_info("GOLD")
        print(f"   GOLD Info: Spread={gold_info['Spread']}, Digits={gold_info['Digits']}")

        print(f"\n📁 ทดสอบการสร้างโฟลเดอร์:")
        config.create_required_folders()
        
        return True
        
    except Exception as e:
        print(f"❌ ไม่สามารถโหลด trading_config.py ได้: {e}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🎯 ทดสอบความสอดคล้องของการตั้งค่า")
    print("=" * 60)
    
    # ทดสอบการตั้งค่าปัจจุบัน
    current_ok = test_current_config()
    
    # ทดสอบการตั้งค่าใหม่
    new_ok = test_new_config()
    
    # สรุปผลรวม
    print(f"\n🎯 สรุปผลการทดสอบ:")
    print("=" * 60)
    
    if current_ok:
        print("✅ การตั้งค่าปัจจุบันสอดคล้องกัน")
    else:
        print("❌ การตั้งค่าปัจจุบันไม่สอดคล้องกัน")
    
    if new_ok:
        print("✅ ไฟล์ trading_config.py ทำงานได้ปกติ")
    else:
        print("❌ ไฟล์ trading_config.py มีปัญหา")
    
    if not current_ok and new_ok:
        print(f"\n💡 คำแนะนำ:")
        print("1. ใช้ไฟล์ trading_config.py แทนการตั้งค่าแยกกัน")
        print("2. แก้ไขไฟล์เดิมให้ import จาก trading_config")
        print("3. ทดสอบอีกครั้งหลังจากแก้ไข")
    elif current_ok:
        print(f"\n✅ ระบบพร้อมใช้งาน!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple debug for Sell signals
"""

import os
import sys
import pandas as pd

# เพิ่ม path สำหรับ import
sys.path.append('.')

def check_parameters():
    """ตรวจสอบพารามิเตอร์"""
    
    print("=== Check Parameters ===")
    
    try:
        from python_LightGBM_16_Signal import (
            input_rsi_level_in,
            input_pull_back,
            input_take_profit
        )
        
        print(f"RSI Level: {input_rsi_level_in}")
        print(f"Pull Back: {input_pull_back}")
        print(f"Take Profit Ratio: {input_take_profit}")

        # คำนวณเกณฑ์
        rsi_threshold = 100 - input_rsi_level_in
        ratio_threshold = input_take_profit * 3.0
        
        print(f"RSI Threshold (>): {rsi_threshold}")
        print(f"Ratio Threshold (>): {ratio_threshold}")
        print(f"PullBack Threshold (>): {input_pull_back}")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False

def run_simple_test():
    """รันการทดสอบแบบง่าย"""
    
    print("\n=== Run Simple Test ===")
    
    try:
        from python_LightGBM_16_Signal import test_groups
        
        if 'M30' in test_groups and test_groups['M30']:
            test_file = test_groups['M30'][0]
            print(f"Test file: {test_file}")
            
            if os.path.exists(test_file):
                print("File exists - OK")
                
                # ลองรันแค่ส่วนเล็กๆ
                from python_LightGBM_16_Signal import load_and_process_data
                
                result = load_and_process_data(
                    file=test_file,
                    modelname="test_model",
                    symbol="AUDUSD",
                    timeframe=30,
                    identifier=1,
                    model=None,
                    scaler=None,
                    nBars_SL=20,
                    confidence_threshold=0.5
                )
                
                if result and len(result) == 6:
                    train_data, val_data, test_data, df, trade_df, stats = result
                    print(f"Data loaded successfully: {df.shape if df is not None else 'None'}")
                    
                    if df is not None:
                        print(f"Columns: {list(df.columns)}")
                        
                        # ตรวจสอบข้อมูลที่เกี่ยวข้องกับ Sell
                        if len(df) > 10:
                            sample = df.tail(10)
                            
                            # ตรวจสอบ Bearish candles
                            bearish = sample[sample['Close'] < sample['Open']]
                            print(f"Bearish candles in last 10: {len(bearish)}")
                            
                            # ตรวจสอบ RSI ถ้ามี
                            rsi_cols = [col for col in df.columns if 'rsi' in col.lower()]
                            if rsi_cols:
                                print(f"RSI columns: {rsi_cols}")
                                rsi_col = rsi_cols[0]
                                high_rsi = sample[sample[rsi_col] > 65]
                                print(f"High RSI (>65) in last 10: {len(high_rsi)}")
                            
                            print("Sample data:")
                            print(sample[['Date', 'Time', 'Open', 'Close']].to_string())
                else:
                    print("Failed to load data")
            else:
                print("File not found")
        else:
            print("No M30 test files")
            
    except Exception as e:
        print(f"Error in test: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("Simple Debug for Sell Signals")
    print("=" * 40)
    
    # ตรวจสอบพารามิเตอร์
    if check_parameters():
        # รันการทดสอบ
        run_simple_test()
    
    print("\nDebug completed")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 แก้ไขการตั้งค่าใน python_to_mt5_WebRequest_server_12_Signal.py
ให้ใช้ค่าจาก trading_config.py แทน
"""

import os
import shutil
from datetime import datetime

def backup_server_file():
    """สำรองไฟล์ server ก่อนแก้ไข"""
    server_file = "python_to_mt5_WebRequest_server_12_Signal.py"
    backup_file = f"python_to_mt5_WebRequest_server_12_Signal_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
    
    if os.path.exists(server_file):
        shutil.copy2(server_file, backup_file)
        print(f"📁 สำรองไฟล์: {backup_file}")
        return True
    else:
        print(f"❌ ไม่พบไฟล์: {server_file}")
        return False

def read_server_file():
    """อ่านไฟล์ server"""
    server_file = "python_to_mt5_WebRequest_server_12_Signal.py"
    
    try:
        with open(server_file, 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except Exception as e:
        print(f"❌ ไม่สามารถอ่านไฟล์ได้: {e}")
        return None

def fix_server_config():
    """แก้ไขการตั้งค่าในไฟล์ server"""
    print("🔧 เริ่มแก้ไขการตั้งค่าในไฟล์ server")
    print("=" * 60)
    
    # สำรองไฟล์ก่อน
    if not backup_server_file():
        return False
    
    # อ่านไฟล์
    content = read_server_file()
    if content is None:
        return False
    
    # แก้ไขการ import
    print("📝 เพิ่มการ import trading_config")
    
    # หาตำแหน่งที่จะเพิ่ม import
    import_pos = content.find("import requests")
    if import_pos != -1:
        # เพิ่ม import หลังจาก import requests
        new_import = "\nfrom trading_config import *\n"
        content = content[:import_pos + len("import requests")] + new_import + content[import_pos + len("import requests"):]
    
    # แก้ไขการตั้งค่าเดิม
    replacements = [
        # Trading Parameters
        ("input_rsi_level_in = 35", "# input_rsi_level_in = 35  # ใช้จาก trading_config: INPUT_RSI_LEVEL_IN"),
        ("input_rsi_level_out = 30", "# input_rsi_level_out = 30  # ใช้จาก trading_config: INPUT_RSI_LEVEL_OUT"),
        ("input_stop_loss_atr = 2.0", "# input_stop_loss_atr = 2.0  # ใช้จาก trading_config: INPUT_STOP_LOSS_ATR"),
        ("input_take_profit = 1.0", "# input_take_profit = 1.0  # ใช้จาก trading_config: INPUT_TAKE_PROFIT"),
        ("input_pull_back = 0.20", "# input_pull_back = 0.20  # ใช้จาก trading_config: INPUT_PULL_BACK"),
        
        # Server Configuration
        ("HTTP_PORT = 54321", "# HTTP_PORT = 54321  # ใช้จาก trading_config"),
        ("HTTP_HOST = '127.0.0.1'", "# HTTP_HOST = '127.0.0.1'  # ใช้จาก trading_config"),
        
        # Telegram Configuration
        ("Telegram_Open = True", "# Telegram_Open = True  # ใช้จาก trading_config: TELEGRAM_OPEN"),
        ("TOKEN = '**********************************************'", "# TOKEN = '...'  # ใช้จาก trading_config: TELEGRAM_TOKEN"),
        ("CHAT_ID = 6546140292", "# CHAT_ID = 6546140292  # ใช้จาก trading_config: TELEGRAM_CHAT_ID"),
        
        # Symbol Info
        ("symbol_info_map = {", "# symbol_info_map = {  # ใช้จาก trading_config: SYMBOL_INFO"),
    ]
    
    print("🔄 แทนที่การตั้งค่าเดิม...")
    for old, new in replacements:
        if old in content:
            content = content.replace(old, new)
            print(f"   ✅ แทนที่: {old[:30]}...")
    
    # เพิ่มการใช้ค่าจาก config
    config_usage = """
# ==============================================
# 🎯 ใช้การตั้งค่าจาก trading_config.py
# ==============================================

# Trading Parameters
input_rsi_level_in = INPUT_RSI_LEVEL_IN
input_rsi_level_out = INPUT_RSI_LEVEL_OUT
input_stop_loss_atr = INPUT_STOP_LOSS_ATR
input_take_profit = INPUT_TAKE_PROFIT
input_pull_back = INPUT_PULL_BACK

# Server Configuration
HTTP_PORT = HTTP_PORT
HTTP_HOST = HTTP_HOST

# Telegram Configuration
Telegram_Open = TELEGRAM_OPEN
TOKEN = TELEGRAM_TOKEN
CHAT_ID = TELEGRAM_CHAT_ID

# Symbol Information
symbol_info_map = SYMBOL_INFO

# Timeframe Mapping
timeframe_map = MT5_TIMEFRAME_MAP
timeframe_code_map = TIMEFRAME_CODE_MAP

"""
    
    # หาตำแหน่งที่จะเพิ่ม config usage
    config_pos = content.find("# --- Configuration ---")
    if config_pos != -1:
        # เพิ่มหลังจาก Configuration comment
        end_pos = content.find("\n", config_pos)
        content = content[:end_pos] + config_usage + content[end_pos:]
    
    # บันทึกไฟล์ที่แก้ไขแล้ว
    try:
        with open("python_to_mt5_WebRequest_server_12_Signal.py", 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ บันทึกไฟล์ที่แก้ไขแล้วสำเร็จ")
        return True
    except Exception as e:
        print(f"❌ ไม่สามารถบันทึกไฟล์ได้: {e}")
        return False

def test_fixed_config():
    """ทดสอบการตั้งค่าหลังจากแก้ไข"""
    print("\n🧪 ทดสอบการตั้งค่าหลังจากแก้ไข")
    print("=" * 60)
    
    try:
        # ลอง import ไฟล์ที่แก้ไขแล้ว
        import importlib
        import sys
        
        # ลบ module เก่าออกจาก cache
        if 'python_to_mt5_WebRequest_server_12_Signal' in sys.modules:
            del sys.modules['python_to_mt5_WebRequest_server_12_Signal']
        
        # Import ใหม่
        server_module = importlib.import_module('python_to_mt5_WebRequest_server_12_Signal')
        
        print("✅ Import ไฟล์ server ที่แก้ไขแล้วสำเร็จ")
        
        # ตรวจสอบค่าต่างๆ
        print(f"📊 ค่าที่ได้จากไฟล์ที่แก้ไขแล้ว:")
        print(f"   RSI Level In: {getattr(server_module, 'input_rsi_level_in', 'ไม่พบ')}")
        print(f"   RSI Level Out: {getattr(server_module, 'input_rsi_level_out', 'ไม่พบ')}")
        print(f"   Stop Loss ATR: {getattr(server_module, 'input_stop_loss_atr', 'ไม่พบ')}")
        print(f"   Take Profit: {getattr(server_module, 'input_take_profit', 'ไม่พบ')}")
        print(f"   Pull Back: {getattr(server_module, 'input_pull_back', 'ไม่พบ')}")
        
        return True
        
    except Exception as e:
        print(f"❌ การทดสอบล้มเหลว: {e}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🎯 แก้ไขการตั้งค่าใน Server File")
    print("=" * 60)
    
    # ตรวจสอบว่ามีไฟล์ trading_config.py หรือไม่
    if not os.path.exists("trading_config.py"):
        print("❌ ไม่พบไฟล์ trading_config.py")
        print("💡 กรุณาสร้างไฟล์ trading_config.py ก่อน")
        return
    
    # แก้ไขไฟล์ server
    if fix_server_config():
        print("✅ แก้ไขไฟล์ server สำเร็จ")
        
        # ทดสอบการตั้งค่าใหม่
        if test_fixed_config():
            print("\n🎉 การแก้ไขเสร็จสมบูรณ์!")
            print("💡 แนะนำ: รันไฟล์ test_config_consistency.py เพื่อตรวจสอบอีกครั้ง")
        else:
            print("\n⚠️ การแก้ไขเสร็จแล้ว แต่การทดสอบมีปัญหา")
            print("💡 กรุณาตรวจสอบไฟล์ที่แก้ไขแล้วด้วยตนเอง")
    else:
        print("❌ การแก้ไขล้มเหลว")

if __name__ == "__main__":
    main()

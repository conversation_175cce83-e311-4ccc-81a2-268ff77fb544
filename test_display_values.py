#!/usr/bin/env python3
"""
ทดสอบการแสดงผลค่า threshold และ nBars_SL ใน MT5 display
"""

import requests
import json
import pandas as pd
import numpy as np
from datetime import datetime

def create_test_data():
    """สร้างข้อมูลทดสอบ"""
    dates = pd.date_range(start='2024-01-01', periods=220, freq='30min')
    base_price = 2600.0
    trend = np.linspace(0, 50, 220)
    noise = np.random.normal(0, 2, 220)
    prices = base_price + trend + noise
    
    data = []
    for i, date in enumerate(dates):
        open_price = prices[i]
        high_price = open_price + abs(np.random.normal(0, 1))
        low_price = open_price - abs(np.random.normal(0, 1))
        close_price = open_price + np.random.normal(0, 0.5)
        volume = np.random.randint(1000, 5000)
        
        bar_data = {
            'time': int(date.timestamp()),
            'open': round(open_price, 5),
            'high': round(high_price, 5),
            'low': round(low_price, 5),
            'close': round(close_price, 5),
            'tick_volume': volume,
            'spread': 25,
            'real_volume': volume
        }
        data.append(bar_data)
    
    return data

def test_display_values(symbol, expected_values):
    """ทดสอบการแสดงผลค่า threshold และ nBars_SL"""
    print(f"\n{'='*80}")
    print(f"🧪 ทดสอบการแสดงผล: {symbol}")
    print(f"📊 Expected Values: {expected_values}")
    print(f"{'='*80}")
    
    test_data = create_test_data()
    payload = {
        'symbol': symbol,
        'timeframe_str': 'PERIOD_M30',
        'bars': test_data
    }
    
    try:
        response = requests.post(
            'http://127.0.0.1:54321/data',
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"📊 ผลลัพธ์สำหรับ {symbol}:")
            print(f"   Signal: {result.get('signal', 'N/A')}")
            print(f"   Confidence: {result.get('confidence', 0):.4f}")
            print(f"   Scenario: {result.get('scenario_used', 'N/A')}")
            print(f"   Market: {result.get('market_condition', 'N/A')}")
            
            # ข้อมูลที่ Python ส่งมา (เดิม)
            original_threshold = result.get('threshold', 0.5)
            original_nbars = result.get('nBars_SL', 6)
            
            # ข้อมูลของแต่ละ scenario
            tf_threshold = result.get('trend_following_threshold', 0.5)
            tf_nbars = result.get('trend_following_nbars', 6)
            ct_threshold = result.get('counter_trend_threshold', 0.5)
            ct_nbars = result.get('counter_trend_nbars', 6)
            
            scenario_used = result.get('scenario_used', 'none')
            
            print(f"\n📈 ข้อมูลที่ Python ส่งมา (เดิม):")
            print(f"   Original Threshold: {original_threshold:.4f}")
            print(f"   Original nBars_SL: {original_nbars}")
            
            print(f"\n🎯 ข้อมูลแต่ละ Scenario:")
            print(f"   Trend Following: T={tf_threshold:.4f}, nBars={tf_nbars}")
            print(f"   Counter Trend: T={ct_threshold:.4f}, nBars={ct_nbars}")
            
            # คำนวณค่าที่ควรแสดงใน MT5 display
            if scenario_used == "trend_following":
                expected_display_threshold = tf_threshold
                expected_display_nbars = tf_nbars
            elif scenario_used == "counter_trend":
                expected_display_threshold = ct_threshold
                expected_display_nbars = ct_nbars
            else:
                # ไม่มี scenario ชัดเจน ใช้ค่าต่ำสุด
                expected_display_threshold = min(tf_threshold, ct_threshold)
                expected_display_nbars = min(tf_nbars, ct_nbars)
            
            print(f"\n🖥️ ค่าที่ควรแสดงใน MT5 Display:")
            print(f"   Current Scenario: {scenario_used}")
            print(f"   Expected Display Threshold: {expected_display_threshold:.4f}")
            print(f"   Expected Display nBars_SL: {expected_display_nbars}")
            
            # เปรียบเทียบกับค่าที่คาดหวัง
            print(f"\n🔍 การเปรียบเทียบ:")
            if expected_values:
                exp_tf_threshold = expected_values.get('trend_following_threshold', 0.5)
                exp_tf_nbars = expected_values.get('trend_following_nbars', 6)
                exp_ct_threshold = expected_values.get('counter_trend_threshold', 0.5)
                exp_ct_nbars = expected_values.get('counter_trend_nbars', 6)
                
                print(f"   TF Threshold: {tf_threshold:.4f} vs Expected {exp_tf_threshold:.4f} {'✅' if abs(tf_threshold - exp_tf_threshold) < 0.001 else '❌'}")
                print(f"   TF nBars: {tf_nbars} vs Expected {exp_tf_nbars} {'✅' if tf_nbars == exp_tf_nbars else '❌'}")
                print(f"   CT Threshold: {ct_threshold:.4f} vs Expected {exp_ct_threshold:.4f} {'✅' if abs(ct_threshold - exp_ct_threshold) < 0.001 else '❌'}")
                print(f"   CT nBars: {ct_nbars} vs Expected {exp_ct_nbars} {'✅' if ct_nbars == exp_ct_nbars else '❌'}")
            
            # สรุปผลกระทบต่อการเปิด order
            analyze_trading_impact(result, expected_display_threshold, expected_display_nbars)
            
            return result
            
        else:
            print(f"❌ Server error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def analyze_trading_impact(result, expected_threshold, expected_nbars):
    """วิเคราะห์ผลกระทบต่อการเปิด order"""
    print(f"\n🎯 ผลกระทบต่อการเปิด Order:")
    
    signal = result.get('signal', 'N/A')
    confidence = result.get('confidence', 0.0)
    scenario = result.get('scenario_used', 'N/A')
    
    if signal in ['BUY', 'SELL']:
        print(f"   Signal: {signal}")
        print(f"   Confidence: {confidence:.4f}")
        print(f"   Expected Threshold: {expected_threshold:.4f}")
        print(f"   Expected nBars_SL: {expected_nbars}")
        
        # ตรวจสอบว่าจะเปิด order ได้หรือไม่
        will_trade = confidence >= expected_threshold
        print(f"   Will Trade: {'✅ YES' if will_trade else '❌ NO'} (Confidence {'≥' if will_trade else '<'} Threshold)")
        
        if will_trade:
            print(f"   📝 MT5 Action: Open {signal} order")
            print(f"   📊 Display Values: T:{expected_threshold:.3f} SL:{expected_nbars}")
        else:
            print(f"   📝 MT5 Action: Reject order (Low confidence)")
            print(f"   📊 Display Values: T:{expected_threshold:.3f} SL:{expected_nbars} (Still correct)")
    else:
        print(f"   Signal: {signal} (No trading action)")
        print(f"   📊 Display Values: T:{expected_threshold:.3f} SL:{expected_nbars} (Informational)")

def main():
    """ทดสอบการแสดงผลกับ symbol ต่างๆ"""
    print("🚀 เริ่มทดสอบการแสดงผล Threshold และ nBars_SL")
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # ข้อมูลที่คาดหวังสำหรับแต่ละ symbol
    test_symbols = {
        'EURGBP': {
            'trend_following_threshold': 0.3500,
            'trend_following_nbars': 8,
            'counter_trend_threshold': 0.2500,
            'counter_trend_nbars': 6
        },
        'GOLD': {
            'trend_following_threshold': 0.5000,
            'trend_following_nbars': 12,
            'counter_trend_threshold': 0.5500,
            'counter_trend_nbars': 12
        },
        'AUDUSD': {
            'trend_following_threshold': 0.1000,
            'trend_following_nbars': 7,
            'counter_trend_threshold': 0.1000,
            'counter_trend_nbars': 7
        }
    }
    
    results = {}
    
    # ทดสอบแต่ละ symbol
    for symbol, expected_values in test_symbols.items():
        result = test_display_values(symbol, expected_values)
        results[symbol] = result
        
        # รอสักครู่ระหว่างการทดสอบ
        import time
        time.sleep(2)
    
    # สรุปผลการทดสอบ
    print(f"\n{'='*80}")
    print("📋 สรุปผลการทดสอบการแสดงผล")
    print(f"{'='*80}")
    
    for symbol, result in results.items():
        if result:
            scenario = result.get('scenario_used', 'none')
            tf_threshold = result.get('trend_following_threshold', 0.5)
            tf_nbars = result.get('trend_following_nbars', 6)
            ct_threshold = result.get('counter_trend_threshold', 0.5)
            ct_nbars = result.get('counter_trend_nbars', 6)
            
            # คำนวณค่าที่ควรแสดง
            if scenario == "trend_following":
                display_threshold = tf_threshold
                display_nbars = tf_nbars
            elif scenario == "counter_trend":
                display_threshold = ct_threshold
                display_nbars = ct_nbars
            else:
                display_threshold = min(tf_threshold, ct_threshold)
                display_nbars = min(tf_nbars, ct_nbars)
            
            print(f"💰 {symbol}:")
            print(f"   Scenario: {scenario}")
            print(f"   MT5 Display Should Show: T:{display_threshold:.3f} SL:{display_nbars}")
            print(f"   TF: T:{tf_threshold:.3f} SL:{tf_nbars} | CT: T:{ct_threshold:.3f} SL:{ct_nbars}")
        else:
            print(f"💰 {symbol}: ❌ Test Failed")
    
    print(f"\n✅ การทดสอบเสร็จสิ้น")
    print("🎯 การแก้ไขจะทำให้:")
    print("   • MT5 display แสดงค่า threshold และ nBars_SL ที่ถูกต้องตาม scenario")
    print("   • การเปิด order ใช้เกณฑ์ที่เหมาะสมกับสถานการณ์จริง")
    print("   • ไม่มีความสับสนระหว่างค่าที่แสดงกับค่าที่ใช้งานจริง")

if __name__ == "__main__":
    main()

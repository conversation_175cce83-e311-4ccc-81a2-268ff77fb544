#!/usr/bin/env python3
"""
ทดสอบระบบ Dynamic Threshold ที่ปรับตาม Symbol และ Scenario
"""

import requests
import json
import pandas as pd
import numpy as np
from datetime import datetime

def create_test_data():
    """สร้างข้อมูลทดสอบ"""
    dates = pd.date_range(start='2024-01-01', periods=220, freq='30min')
    base_price = 2600.0
    trend = np.linspace(0, 50, 220)
    noise = np.random.normal(0, 2, 220)
    prices = base_price + trend + noise
    
    data = []
    for i, date in enumerate(dates):
        open_price = prices[i]
        high_price = open_price + abs(np.random.normal(0, 1))
        low_price = open_price - abs(np.random.normal(0, 1))
        close_price = open_price + np.random.normal(0, 0.5)
        volume = np.random.randint(1000, 5000)
        
        bar_data = {
            'time': int(date.timestamp()),
            'open': round(open_price, 5),
            'high': round(high_price, 5),
            'low': round(low_price, 5),
            'close': round(close_price, 5),
            'tick_volume': volume,
            'spread': 25,
            'real_volume': volume
        }
        data.append(bar_data)
    
    return data

def test_symbol_threshold(symbol, expected_thresholds):
    """ทดสอบ threshold ของแต่ละ symbol"""
    print(f"\n{'='*80}")
    print(f"🧪 ทดสอบ Symbol: {symbol}")
    print(f"📊 Expected Thresholds: {expected_thresholds}")
    print(f"{'='*80}")
    
    test_data = create_test_data()
    payload = {
        'symbol': symbol,
        'timeframe_str': 'PERIOD_M30',
        'bars': test_data
    }
    
    try:
        response = requests.post(
            'http://127.0.0.1:54321/data',
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"📊 ผลลัพธ์สำหรับ {symbol}:")
            print(f"   Signal: {result.get('signal', 'N/A')}")
            print(f"   Confidence: {result.get('confidence', 0):.4f}")
            print(f"   Scenario: {result.get('scenario_used', 'N/A')}")
            print(f"   Market: {result.get('market_condition', 'N/A')}")
            
            # ตรวจสอบ threshold ที่ได้รับ
            tf_threshold = result.get('trend_following_threshold', 0.5)
            ct_threshold = result.get('counter_trend_threshold', 0.5)
            
            print(f"\n🎯 Threshold Analysis:")
            print(f"   Trend Following: {tf_threshold:.4f} (Expected: {expected_thresholds.get('trend_following', 'N/A')})")
            print(f"   Counter Trend: {ct_threshold:.4f} (Expected: {expected_thresholds.get('counter_trend', 'N/A')})")
            
            # ตรวจสอบ confidence ของแต่ละ scenario
            tf_buy = result.get('trend_following_buy_confidence', 0.0)
            tf_sell = result.get('trend_following_sell_confidence', 0.0)
            ct_buy = result.get('counter_trend_buy_confidence', 0.0)
            ct_sell = result.get('counter_trend_sell_confidence', 0.0)
            
            print(f"\n📈 Confidence Analysis:")
            print(f"   Trend Following - BUY: {tf_buy:.4f} {'✅' if tf_buy >= tf_threshold else '❌'}")
            print(f"   Trend Following - SELL: {tf_sell:.4f} {'✅' if tf_sell >= tf_threshold else '❌'}")
            print(f"   Counter Trend - BUY: {ct_buy:.4f} {'✅' if ct_buy >= ct_threshold else '❌'}")
            print(f"   Counter Trend - SELL: {ct_sell:.4f} {'✅' if ct_sell >= ct_threshold else '❌'}")
            
            # วิเคราะห์ว่าควรเปิด order หรือไม่
            analyze_trading_decision(result)
            
            return result
            
        else:
            print(f"❌ Server error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def analyze_trading_decision(result):
    """วิเคราะห์การตัดสินใจเทรด"""
    print(f"\n🔍 Trading Decision Analysis:")
    
    signal = result.get('signal', 'N/A')
    confidence = result.get('confidence', 0.0)
    scenario = result.get('scenario_used', 'N/A')
    market = result.get('market_condition', 'N/A')
    
    if signal in ['BUY', 'SELL']:
        # หา threshold ที่เหมาะสม
        if scenario == 'trend_following':
            required_threshold = result.get('trend_following_threshold', 0.5)
        elif scenario == 'counter_trend':
            required_threshold = result.get('counter_trend_threshold', 0.5)
        else:
            required_threshold = 0.5
        
        print(f"   Signal: {signal}")
        print(f"   Confidence: {confidence:.4f}")
        print(f"   Required Threshold: {required_threshold:.4f}")
        print(f"   Threshold Check: {'✅ PASS' if confidence >= required_threshold else '❌ FAIL'}")
        
        # ตรวจสอบ scenario-market match
        scenario_valid = check_scenario_market_match(signal, scenario, market)
        print(f"   Scenario-Market Match: {'✅ VALID' if scenario_valid else '❌ INVALID'}")
        
        # สรุปการตัดสินใจ
        should_trade = (confidence >= required_threshold) and scenario_valid
        print(f"   🎯 Final Decision: {'✅ SHOULD TRADE' if should_trade else '❌ SHOULD NOT TRADE'}")
        
        if should_trade:
            print(f"   📝 MT5 Action: Open {signal} order with confidence {confidence:.4f}")
        else:
            reasons = []
            if confidence < required_threshold:
                reasons.append(f"Low confidence ({confidence:.4f} < {required_threshold:.4f})")
            if not scenario_valid:
                reasons.append(f"Invalid scenario-market combination")
            print(f"   📝 MT5 Action: Reject order - {', '.join(reasons)}")
    else:
        print(f"   Signal: {signal} (No trading action)")

def check_scenario_market_match(signal, scenario, market):
    """ตรวจสอบความสอดคล้องของ scenario กับ market"""
    if market == "uptrend":
        return (scenario == "trend_following" and signal == "BUY") or \
               (scenario == "counter_trend" and signal == "SELL")
    elif market == "downtrend":
        return (scenario == "trend_following" and signal == "SELL") or \
               (scenario == "counter_trend" and signal == "BUY")
    else:  # sideways or unknown
        return True

def main():
    """ทดสอบ Dynamic Threshold กับ Symbol ต่างๆ"""
    print("🚀 เริ่มทดสอบ Dynamic Threshold System")
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # ข้อมูล threshold ที่คาดหวังสำหรับแต่ละ symbol
    symbol_thresholds = {
        'AUDUSD': {'trend_following': 0.1000, 'counter_trend': 0.1000},
        'EURGBP': {'trend_following': 0.1000, 'counter_trend': 0.1000},
        'EURUSD': {'trend_following': 0.1000, 'counter_trend': 0.1000},
        'GBPUSD': {'trend_following': 0.1000, 'counter_trend': 0.1000},
        'GOLD': {'trend_following': 0.5000, 'counter_trend': 0.5000},
        'NZDUSD': {'trend_following': 0.1000, 'counter_trend': 0.4500},
        'USDCAD': {'trend_following': 0.1000, 'counter_trend': 0.1000},
        'USDJPY': {'trend_following': 0.5000, 'counter_trend': 0.5000}
    }
    
    results = {}
    
    # ทดสอบแต่ละ symbol
    for symbol, expected_thresholds in symbol_thresholds.items():
        result = test_symbol_threshold(symbol, expected_thresholds)
        results[symbol] = result
        
        # รอสักครู่ระหว่างการทดสอบ
        import time
        time.sleep(2)
    
    # สรุปผลการทดสอบ
    print(f"\n{'='*80}")
    print("📋 สรุปผลการทดสอบ Dynamic Threshold")
    print(f"{'='*80}")
    
    for symbol, result in results.items():
        if result:
            signal = result.get('signal', 'N/A')
            confidence = result.get('confidence', 0.0)
            scenario = result.get('scenario_used', 'N/A')
            tf_threshold = result.get('trend_following_threshold', 0.5)
            ct_threshold = result.get('counter_trend_threshold', 0.5)
            
            print(f"💰 {symbol}:")
            print(f"   Signal: {signal} (Confidence: {confidence:.4f}, Scenario: {scenario})")
            print(f"   Thresholds: TF={tf_threshold:.4f}, CT={ct_threshold:.4f}")
        else:
            print(f"💰 {symbol}: ❌ Test Failed")
    
    print(f"\n✅ การทดสอบเสร็จสิ้น")
    print("🎯 ระบบ Dynamic Threshold จะช่วยให้:")
    print("   • Symbol ที่มี threshold ต่ำ (0.1) สามารถเทรดได้ง่ายขึ้น")
    print("   • Symbol ที่มี threshold สูง (0.5) จะมีการกรองที่เข้มงวดขึ้น")
    print("   • แต่ละ scenario ใช้ threshold ที่เหมาะสมกับตัวเอง")

if __name__ == "__main__":
    main()

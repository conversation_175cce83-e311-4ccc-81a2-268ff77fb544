#!/usr/bin/env python3
"""
ทดสอบสมมุติเหตุการณ์การเปิดออเดอร์ในกรณีต่างๆ
"""

import requests
import json
import pandas as pd
import numpy as np
from datetime import datetime

def create_test_data():
    """สร้างข้อมูลทดสอบ"""
    dates = pd.date_range(start='2024-01-01', periods=220, freq='30min')
    base_price = 2600.0
    trend = np.linspace(0, 50, 220)
    noise = np.random.normal(0, 2, 220)
    prices = base_price + trend + noise
    
    data = []
    for i, date in enumerate(dates):
        open_price = prices[i]
        high_price = open_price + abs(np.random.normal(0, 1))
        low_price = open_price - abs(np.random.normal(0, 1))
        close_price = open_price + np.random.normal(0, 0.5)
        volume = np.random.randint(1000, 5000)
        
        bar_data = {
            'time': int(date.timestamp()),
            'open': round(open_price, 5),
            'high': round(high_price, 5),
            'low': round(low_price, 5),
            'close': round(close_price, 5),
            'tick_volume': volume,
            'spread': 25,
            'real_volume': volume
        }
        data.append(bar_data)
    
    return data

def test_scenario(scenario_name, description, expected_result):
    """ทดสอบสมมุติเหตุการณ์"""
    print(f"\n{'='*80}")
    print(f"🧪 ทดสอบ: {scenario_name}")
    print(f"📝 รายละเอียด: {description}")
    print(f"🎯 ผลที่คาดหวัง: {expected_result}")
    print(f"{'='*80}")
    
    test_data = create_test_data()
    payload = {
        'symbol': 'GOLD',
        'timeframe_str': 'PERIOD_M30',
        'bars': test_data
    }
    
    try:
        response = requests.post(
            'http://127.0.0.1:54321/data',
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"📊 ผลลัพธ์:")
            print(f"   Signal: {result.get('signal', 'N/A')}")
            print(f"   Class: {result.get('class', 'N/A')}")
            print(f"   Confidence: {result.get('confidence', 0):.4f}")
            print(f"   Market Condition: {result.get('market_condition', 'N/A')}")
            print(f"   Scenario Used: {result.get('scenario_used', 'N/A')}")
            
            print(f"\n📈 Dual System Analysis:")
            print(f"   Trend Following:")
            print(f"     - Threshold: {result.get('trend_following_threshold', 0.5):.3f}")
            print(f"     - BUY: {result.get('trend_following_buy_confidence', 0.0):.4f}")
            print(f"     - SELL: {result.get('trend_following_sell_confidence', 0.0):.4f}")
            
            print(f"   Counter Trend:")
            print(f"     - Threshold: {result.get('counter_trend_threshold', 0.5):.3f}")
            print(f"     - BUY: {result.get('counter_trend_buy_confidence', 0.0):.4f}")
            print(f"     - SELL: {result.get('counter_trend_sell_confidence', 0.0):.4f}")
            
            # วิเคราะห์ผลลัพธ์
            analyze_result(result, expected_result)
            
            return result
            
        else:
            print(f"❌ Server error: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def analyze_result(result, expected_result):
    """วิเคราะห์ผลลัพธ์"""
    print(f"\n🔍 การวิเคราะห์:")
    
    signal = result.get('signal', 'N/A')
    confidence = result.get('confidence', 0.0)
    scenario = result.get('scenario_used', 'N/A')
    market = result.get('market_condition', 'N/A')
    
    # ตรวจสอบ threshold ที่เหมาะสม
    if scenario == 'trend_following':
        required_threshold = result.get('trend_following_threshold', 0.5)
        if signal == 'BUY':
            scenario_confidence = result.get('trend_following_buy_confidence', 0.0)
        else:
            scenario_confidence = result.get('trend_following_sell_confidence', 0.0)
    elif scenario == 'counter_trend':
        required_threshold = result.get('counter_trend_threshold', 0.5)
        if signal == 'BUY':
            scenario_confidence = result.get('counter_trend_buy_confidence', 0.0)
        else:
            scenario_confidence = result.get('counter_trend_sell_confidence', 0.0)
    else:
        required_threshold = 0.5
        scenario_confidence = confidence
    
    print(f"   Required Threshold: {required_threshold:.3f}")
    print(f"   Scenario Confidence: {scenario_confidence:.4f}")
    print(f"   Threshold Check: {'✅ PASS' if scenario_confidence >= required_threshold else '❌ FAIL'}")
    
    # ตรวจสอบความสอดคล้องของ scenario กับ market
    scenario_market_valid = check_scenario_market_match(signal, scenario, market)
    print(f"   Scenario-Market Match: {'✅ VALID' if scenario_market_valid else '❌ INVALID'}")
    
    # สรุปผลการทดสอบ
    if signal in ['BUY', 'SELL']:
        if scenario_confidence >= required_threshold and scenario_market_valid:
            print(f"   🎯 MT5 Action: ✅ SHOULD OPEN {signal} ORDER")
        else:
            print(f"   🎯 MT5 Action: ❌ SHOULD REJECT {signal} ORDER")
    else:
        print(f"   🎯 MT5 Action: ⏸️ NO ORDER (HOLD)")

def check_scenario_market_match(signal, scenario, market):
    """ตรวจสอบความสอดคล้องของ scenario กับ market"""
    if market == "uptrend":
        return (scenario == "trend_following" and signal == "BUY") or \
               (scenario == "counter_trend" and signal == "SELL")
    elif market == "downtrend":
        return (scenario == "trend_following" and signal == "SELL") or \
               (scenario == "counter_trend" and signal == "BUY")
    else:  # sideways or unknown
        return True

def main():
    """ทดสอบสมมุติเหตุการณ์ต่างๆ"""
    print("🚀 เริ่มทดสอบสมมุติเหตุการณ์การเปิดออเดอร์")
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # รายการสมมุติเหตุการณ์
    scenarios = [
        {
            "name": "Scenario 1: Trend Following BUY (Valid)",
            "description": "Uptrend + Trend Following BUY + High Confidence",
            "expected": "ควรเปิด BUY order"
        },
        {
            "name": "Scenario 2: Counter Trend SELL (Valid)", 
            "description": "Uptrend + Counter Trend SELL + High Confidence",
            "expected": "ควรเปิด SELL order"
        },
        {
            "name": "Scenario 3: Low Confidence",
            "description": "Signal มี confidence ต่ำกว่า threshold",
            "expected": "ไม่ควรเปิด order"
        },
        {
            "name": "Scenario 4: Scenario Mismatch",
            "description": "Trend Following SELL ใน Uptrend (ไม่สอดคล้อง)",
            "expected": "ไม่ควรเปิด order"
        },
        {
            "name": "Scenario 5: HOLD Signal",
            "description": "ไม่มี signal ที่ชัดเจน",
            "expected": "ไม่เปิด order"
        }
    ]
    
    results = []
    for scenario in scenarios:
        result = test_scenario(scenario["name"], scenario["description"], scenario["expected"])
        results.append(result)
        
        # รอสักครู่ระหว่างการทดสอบ
        import time
        time.sleep(2)
    
    print(f"\n{'='*80}")
    print("📋 สรุปผลการทดสอบ")
    print(f"{'='*80}")
    
    for i, (scenario, result) in enumerate(zip(scenarios, results)):
        if result:
            signal = result.get('signal', 'N/A')
            confidence = result.get('confidence', 0.0)
            scenario_used = result.get('scenario_used', 'N/A')
            print(f"{i+1}. {scenario['name']}")
            print(f"   Result: {signal} (Confidence: {confidence:.4f}, Scenario: {scenario_used})")
        else:
            print(f"{i+1}. {scenario['name']}")
            print(f"   Result: ❌ Test Failed")
    
    print(f"\n✅ การทดสอบเสร็จสิ้น")

if __name__ == "__main__":
    main()

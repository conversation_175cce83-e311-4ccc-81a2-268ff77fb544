#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ตรวจสอบสถานะการ hyperparameter tuning ก่อนรัน
"""

import os
import json
from datetime import datetime

def check_symbol_tuning_status():
    """ตรวจสอบสถานะการ tuning ของแต่ละ symbol"""
    print("🔍 ตรวจสอบสถานะ Hyperparameter Tuning")
    print("="*80)
    
    models_dir = "Test_LightGBM/models"
    all_symbols = ['AUDUSD', 'EURGBP', 'EURUSD', 'GBPUSD', 'GOLD', 'NZDUSD', 'USDCAD', 'USDJPY']
    timeframes = [30, 60]
    
    status_summary = {
        'ready_for_tuning': [],
        'has_params': [],
        'missing_data': []
    }
    
    print("📊 สถานะตาม Symbol และ Timeframe:")
    print("-"*80)
    
    for symbol in all_symbols:
        print(f"\n💱 {symbol}:")
        
        for timeframe in timeframes:
            folder_name = f"{str(timeframe).zfill(3)}_{symbol}"
            folder_path = os.path.join(models_dir, folder_name)
            
            # ตรวจสอบไฟล์ต่างๆ
            flag_file = os.path.join(folder_path, f"{folder_name}_tuning_flag.json")
            param_file = os.path.join(folder_path, f"{folder_name}_best_params.json")
            
            # ตรวจสอบข้อมูล CSV
            if timeframe == 30:
                csv_file = f"{symbol}#_M30_201905010000_202504302330.csv"
                if symbol == "GOLD":
                    csv_file = f"{symbol}#_M30_201905010100_202504302330.csv"
            else:  # timeframe == 60
                csv_file = f"{symbol}#_H1_201305010000_202504302300.csv"
            
            csv_exists = os.path.exists(csv_file)
            
            # วิเคราะห์สถานะ
            has_flag = os.path.exists(flag_file)
            has_params = os.path.exists(param_file)
            
            if has_flag:
                try:
                    with open(flag_file, 'r') as f:
                        flag_data = json.load(f)
                    will_tune = flag_data.get("do_hyperparameter_tuning", True)
                except:
                    will_tune = True
            else:
                will_tune = True  # ไม่มี flag = จะ tune
            
            # แสดงสถานะ
            status_icon = "🔄" if will_tune else "✅"
            csv_icon = "📁" if csv_exists else "❌"
            
            print(f"  {status_icon} M{timeframe}: ", end="")
            
            if will_tune:
                print("จะทำ hyperparameter tuning", end="")
                status_summary['ready_for_tuning'].append(f"{symbol}_M{timeframe}")
            else:
                if has_params:
                    print("ใช้ best_params ที่มีอยู่", end="")
                    status_summary['has_params'].append(f"{symbol}_M{timeframe}")
                else:
                    print("⚠️ ไม่มี best_params", end="")
                    status_summary['missing_data'].append(f"{symbol}_M{timeframe}")
            
            print(f" {csv_icon}")
            
            # แสดงรายละเอียด best_params ถ้ามี
            if has_params and not will_tune:
                try:
                    with open(param_file, 'r') as f:
                        param_data = json.load(f)
                    
                    if "best_params" in param_data:
                        params = param_data["best_params"]
                        score = param_data.get("best_score", "N/A")
                        tuning_date = param_data.get("tuning_date", "N/A")
                        
                        print(f"    📈 AUC: {score}")
                        print(f"    🔧 LR: {params.get('learning_rate', 'N/A')}, Leaves: {params.get('num_leaves', 'N/A')}")
                        if tuning_date != "N/A":
                            try:
                                date_obj = datetime.fromisoformat(tuning_date.replace('Z', '+00:00'))
                                formatted_date = date_obj.strftime("%Y-%m-%d %H:%M")
                                print(f"    📅 Tuned: {formatted_date}")
                            except:
                                print(f"    📅 Tuned: {tuning_date}")
                    
                except Exception as e:
                    print(f"    ⚠️ ไม่สามารถอ่าน params: {e}")
            
            if not csv_exists:
                print(f"    ❌ ไม่พบไฟล์: {csv_file}")
    
    # สรุปภาพรวม
    print(f"\n🎯 สรุปภาพรวม")
    print("="*60)
    
    total_combinations = len(all_symbols) * len(timeframes)
    ready_count = len(status_summary['ready_for_tuning'])
    has_params_count = len(status_summary['has_params'])
    missing_count = len(status_summary['missing_data'])
    
    print(f"📊 จำนวนทั้งหมด: {total_combinations} combinations")
    print(f"🔄 จะทำ hyperparameter tuning: {ready_count} combinations")
    print(f"✅ มี best_params แล้ว: {has_params_count} combinations")
    print(f"⚠️ ขาดข้อมูล: {missing_count} combinations")
    
    if ready_count > 0:
        print(f"\n🔄 Symbols ที่จะทำ hyperparameter tuning:")
        for item in status_summary['ready_for_tuning']:
            print(f"  - {item}")
    
    if missing_count > 0:
        print(f"\n⚠️ Combinations ที่มีปัญหา:")
        for item in status_summary['missing_data']:
            print(f"  - {item}")
    
    # ประเมินเวลาที่ใช้
    if ready_count > 0:
        estimated_time = ready_count * 5  # ประมาณ 5 นาทีต่อ combination
        print(f"\n⏰ ประเมินเวลาที่ใช้: ~{estimated_time} นาที ({estimated_time/60:.1f} ชั่วโมง)")
        print(f"   (ประมาณ 5 นาที/combination สำหรับ n_iter=100)")
    
    return status_summary

def check_global_settings():
    """ตรวจสอบการตั้งค่าใน global variables"""
    print(f"\n⚙️ ตรวจสอบการตั้งค่า Global Variables")
    print("="*60)
    
    try:
        with open("python_LightGBM_15_Tuning.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # ตรวจสอบการตั้งค่าสำคัญ
        settings = {}
        
        # NUM_TRAINING_ROUNDS
        if "NUM_TRAINING_ROUNDS = " in content:
            for line in content.split('\n'):
                if line.strip().startswith("NUM_TRAINING_ROUNDS = "):
                    settings['NUM_TRAINING_ROUNDS'] = line.strip().split('=')[1].strip()
                    break
        
        # Save_File
        if "Save_File = " in content:
            for line in content.split('\n'):
                if line.strip().startswith("Save_File = "):
                    settings['Save_File'] = line.strip().split('=')[1].strip()
                    break
        
        # do_hyperparameter_tuning
        if "do_hyperparameter_tuning = " in content:
            for line in content.split('\n'):
                if line.strip().startswith("do_hyperparameter_tuning = "):
                    settings['do_hyperparameter_tuning'] = line.strip().split('=')[1].strip()
                    break
        
        print("📋 การตั้งค่าปัจจุบัน:")
        for key, value in settings.items():
            icon = "✅" if key == "do_hyperparameter_tuning" and "True" in value else "ℹ️"
            print(f"  {icon} {key}: {value}")
        
        # คำแนะนำ
        if settings.get('do_hyperparameter_tuning') and "False" in settings['do_hyperparameter_tuning']:
            print(f"\n⚠️ คำแนะนำ: do_hyperparameter_tuning = False")
            print("   ระบบจะใช้ flag ตาม symbol แทน global flag")
        
        return settings
        
    except Exception as e:
        print(f"❌ ไม่สามารถตรวจสอบการตั้งค่าได้: {e}")
        return {}

def main():
    """ฟังก์ชันหลัก"""
    print("🚀 ตรวจสอบสถานะก่อนรัน Hyperparameter Tuning")
    print("="*80)
    
    # 1. ตรวจสอบสถานะ symbols
    status_summary = check_symbol_tuning_status()
    
    # 2. ตรวจสอบการตั้งค่า global
    global_settings = check_global_settings()
    
    # 3. ให้คำแนะนำ
    print(f"\n💡 คำแนะนำ")
    print("="*60)
    
    ready_count = len(status_summary['ready_for_tuning'])
    
    if ready_count > 0:
        print(f"✅ พร้อมรัน hyperparameter tuning สำหรับ {ready_count} combinations")
        print(f"🚀 รันคำสั่ง: python python_LightGBM_15_Tuning.py")
        
        if ready_count > 4:
            print(f"⏰ การ tuning จะใช้เวลานาน แนะนำให้รันในช่วงที่ว่าง")
        
    else:
        print(f"ℹ️ ไม่มี symbols ที่ต้องทำ hyperparameter tuning")
        print(f"✅ ทุก symbols มี best_params แล้ว")
    
    missing_count = len(status_summary['missing_data'])
    if missing_count > 0:
        print(f"⚠️ มี {missing_count} combinations ที่ขาดข้อมูล")
        print(f"💡 ตรวจสอบไฟล์ CSV และ best_params")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการปรับปรุงพารามิเตอร์หลังจากการวิเคราะห์ parameter stability
"""

import sys
import os
import pandas as pd
import numpy as np
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split, RandomizedSearchCV, TimeSeriesSplit
from sklearn.metrics import roc_auc_score
import lightgbm as lgb
import json
import time

# เพิ่ม path สำหรับ import ฟังก์ชันจากไฟล์หลัก
sys.path.append('.')

def create_test_data(n_samples=1500, random_state=42):
    """สร้างข้อมูลทดสอบที่ใกล้เคียงกับข้อมูลจริง"""
    X, y = make_classification(
        n_samples=n_samples,
        n_features=20,
        n_informative=14,
        n_redundant=4,
        n_clusters_per_class=2,
        weights=[0.85, 0.15],  # 15% positive class (คล้าย financial data)
        flip_y=0.02,
        random_state=random_state
    )
    
    feature_names = [f"Feature_{i:02d}" for i in range(20)]
    X_df = pd.DataFrame(X, columns=feature_names)
    
    return X_df, y

def test_new_default_parameters():
    """ทดสอบ default parameters ใหม่"""
    print("🧪 ทดสอบ Default Parameters ใหม่")
    print("="*60)
    
    try:
        from python_LightGBM_15_Tuning import get_lgbm_params
        
        # สร้างข้อมูลทดสอบ class imbalance
        y_test = np.array([0] * 850 + [1] * 150)  # 5.7:1 ratio
        params = get_lgbm_params(y=y_test, use_scale_pos_weight=True)
        
        print("📊 Default Parameters ใหม่:")
        print("-"*40)
        key_params = ['learning_rate', 'num_leaves', 'min_data_in_leaf', 'feature_fraction']
        
        for param in key_params:
            if param in params:
                print(f"{param:20}: {params[param]}")
        
        return params
        
    except Exception as e:
        print(f"❌ ไม่สามารถโหลดพารามิเตอร์ได้: {e}")
        return None

def test_new_param_dist():
    """ทดสอบ param_dist ใหม่"""
    print(f"\n🔧 ทดสอบ param_dist ใหม่")
    print("="*60)
    
    try:
        from python_LightGBM_15_Tuning import param_dist
        
        print("📋 param_dist ที่ปรับปรุงแล้ว:")
        print("-"*40)
        
        key_params = ['learning_rate', 'num_leaves', 'min_data_in_leaf', 'feature_fraction', 'bagging_fraction']
        
        for param in key_params:
            if param in param_dist:
                values = param_dist[param]
                print(f"{param:20}: {values}")
        
        # คำนวณจำนวนการผสมผสาน
        total_combinations = 1
        for param, values in param_dist.items():
            total_combinations *= len(values)
        
        print(f"\n📊 สถิติ:")
        print(f"  - จำนวนพารามิเตอร์: {len(param_dist)}")
        print(f"  - จำนวนการผสมผสาน: {total_combinations:,}")
        print(f"  - RandomizedSearchCV จะทดสอบ: 100 การผสมผสาน")
        
        return param_dist
        
    except Exception as e:
        print(f"❌ ไม่สามารถโหลด param_dist ได้: {e}")
        return None

def test_parameter_diversity():
    """ทดสอบความหลากหลายของพารามิเตอร์ที่ค้นหาได้"""
    print(f"\n🎲 ทดสอบความหลากหลายของพารามิเตอร์")
    print("="*60)
    
    # สร้างข้อมูลทดสอบ
    X, y = create_test_data(n_samples=1000)
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    print(f"📊 ข้อมูลทดสอบ: Train={len(X_train)}, Test={len(X_test)}")
    print(f"📊 Class distribution: {np.bincount(y_test)}")
    
    try:
        from python_LightGBM_15_Tuning import param_dist
        
        # ลด param_dist สำหรับการทดสอบเร็ว
        quick_param_dist = {
            'learning_rate': param_dist['learning_rate'],
            'num_leaves': param_dist['num_leaves'],
            'min_data_in_leaf': param_dist['min_data_in_leaf'],
            'feature_fraction': param_dist['feature_fraction'][:3],  # ลดตัวเลือก
            'lambda_l1': [0, 0.1],  # ลดตัวเลือก
            'lambda_l2': [0, 0.1]   # ลดตัวเลือก
        }
        
        print(f"\n🔍 ทดสอบ parameter search (ใช้เวลา 1-2 นาที)...")
        start_time = time.time()
        
        # Base model
        lgb_estimator = lgb.LGBMClassifier(
            objective='binary',
            metric='auc',
            n_estimators=100,
            random_state=42,
            verbosity=-1
        )
        
        # RandomizedSearchCV
        search = RandomizedSearchCV(
            lgb_estimator,
            param_distributions=quick_param_dist,
            n_iter=20,  # ลดลงเพื่อความเร็ว
            scoring='roc_auc',
            cv=3,  # ใช้ KFold แทน TimeSeriesSplit เพื่อความเร็ว
            verbose=0,
            random_state=42,
            n_jobs=-1,
            return_train_score=True
        )
        
        # Fit
        search.fit(X_train, y_train)
        
        elapsed_time = time.time() - start_time
        print(f"⏱️ Search เสร็จใน {elapsed_time:.1f} วินาที")
        
        # วิเคราะห์ความหลากหลาย
        results_df = pd.DataFrame(search.cv_results_)
        
        print(f"\n📈 ผลลัพธ์การค้นหา:")
        print("-"*40)
        print(f"Best AUC: {search.best_score_:.4f}")
        print(f"Best params: {search.best_params_}")
        
        # ดู Top 5 combinations
        top_5_idx = results_df['rank_test_score'] <= 5
        top_5_results = results_df[top_5_idx].sort_values('rank_test_score')
        
        print(f"\n🏆 Top 5 Parameter Combinations:")
        print("-"*40)
        
        for i, (idx, row) in enumerate(top_5_results.iterrows(), 1):
            params = row['params']
            score = row['mean_test_score']
            print(f"{i}. AUC: {score:.4f}")
            for key in ['learning_rate', 'num_leaves', 'min_data_in_leaf']:
                if key in params:
                    print(f"   {key}: {params[key]}")
            print()
        
        # วิเคราะห์ความหลากหลาย
        unique_lr = len(set(result['params']['learning_rate'] for result in search.cv_results_['params']))
        unique_leaves = len(set(result['params']['num_leaves'] for result in search.cv_results_['params']))
        
        print(f"🎯 ความหลากหลายของพารามิเตอร์:")
        print(f"  - Learning rates ที่ทดสอบ: {unique_lr} ค่า")
        print(f"  - Num leaves ที่ทดสอบ: {unique_leaves} ค่า")
        print(f"  - Total combinations ที่ทดสอบ: {len(search.cv_results_['params'])}")
        
        # ทดสอบกับ test set
        best_model = search.best_estimator_
        test_pred = best_model.predict_proba(X_test)[:, 1]
        test_auc = roc_auc_score(y_test, test_pred)
        
        print(f"\n📊 Test Set Performance:")
        print(f"  - Test AUC: {test_auc:.4f}")
        print(f"  - CV vs Test gap: {abs(search.best_score_ - test_auc):.4f}")
        
        return {
            'best_params': search.best_params_,
            'best_cv_score': search.best_score_,
            'test_auc': test_auc,
            'unique_lr': unique_lr,
            'unique_leaves': unique_leaves,
            'search_time': elapsed_time
        }
        
    except Exception as e:
        print(f"❌ Error in parameter diversity test: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_stability_improvement():
    """เปรียบเทียบการปรับปรุงจากผล stability analysis"""
    print(f"\n📊 เปรียบเทียบการปรับปรุง")
    print("="*60)
    
    # ค่าที่ได้จาก stability analysis
    stable_params = {
        'learning_rate': 0.15,
        'num_leaves': 10,
        'min_data_in_leaf': 10,
        'feature_fraction': 0.7,
        'bagging_fraction': 0.9
    }
    
    print("📋 ค่าที่เสถียรจาก stability analysis:")
    print("-"*40)
    for param, value in stable_params.items():
        print(f"{param:20}: {value}")
    
    # ตรวจสอบว่าค่าเหล่านี้อยู่ใน param_dist ใหม่หรือไม่
    try:
        from python_LightGBM_15_Tuning import param_dist
        
        print(f"\n✅ การครอบคลุมใน param_dist ใหม่:")
        print("-"*40)
        
        coverage = {}
        for param, stable_value in stable_params.items():
            if param in param_dist:
                values = param_dist[param]
                is_covered = stable_value in values
                coverage[param] = is_covered
                status = "✅" if is_covered else "❌"
                print(f"{param:20}: {status} ({stable_value} in {values})")
        
        covered_count = sum(coverage.values())
        total_count = len(coverage)
        
        print(f"\n🎯 สรุป: {covered_count}/{total_count} พารามิเตอร์ถูกครอบคลุม")
        
        if covered_count == total_count:
            print("✅ param_dist ใหม่ครอบคลุมค่าที่เสถียรทั้งหมด!")
        else:
            print("⚠️ ควรปรับ param_dist ให้ครอบคลุมค่าที่เสถียรมากขึ้น")
        
        return coverage
        
    except Exception as e:
        print(f"❌ ไม่สามารถตรวจสอบได้: {e}")
        return None

def main():
    """ฟังก์ชันหลัก"""
    print("🚀 ทดสอบการปรับปรุงพารามิเตอร์")
    print("="*80)
    
    # 1. ทดสอบ default parameters ใหม่
    new_defaults = test_new_default_parameters()
    
    # 2. ทดสอบ param_dist ใหม่
    new_param_dist = test_new_param_dist()
    
    # 3. เปรียบเทียบการปรับปรุง
    coverage = compare_stability_improvement()
    
    # 4. ทดสอบความหลากหลาย
    print(f"\n⚠️ การทดสอบความหลากหลายจะใช้เวลา 1-2 นาที")
    user_input = input("ต้องการทดสอบหรือไม่? (y/n): ")
    
    if user_input.lower() == 'y':
        diversity_results = test_parameter_diversity()
    else:
        diversity_results = None
    
    # สรุปผลลัพธ์
    print(f"\n🎯 สรุปการทดสอบ")
    print("="*60)
    
    if new_defaults:
        print("✅ Default parameters: โหลดสำเร็จ")
    else:
        print("❌ Default parameters: ล้มเหลว")
    
    if new_param_dist:
        print("✅ param_dist: โหลดสำเร็จ")
    else:
        print("❌ param_dist: ล้มเหลว")
    
    if coverage:
        covered = sum(coverage.values())
        total = len(coverage)
        print(f"✅ Parameter coverage: {covered}/{total}")
    else:
        print("❌ Parameter coverage: ไม่สามารถตรวจสอบได้")
    
    if diversity_results:
        print(f"✅ Diversity test: สำเร็จ")
        print(f"   - Best AUC: {diversity_results['best_cv_score']:.4f}")
        print(f"   - Test AUC: {diversity_results['test_auc']:.4f}")
        print(f"   - Unique LR: {diversity_results['unique_lr']}")
        print(f"   - Unique Leaves: {diversity_results['unique_leaves']}")
    else:
        print("⏭️ Diversity test: ข้าม")
    
    print(f"\n🚀 พร้อมสำหรับการใช้งานจริงที่ปรับปรุงแล้ว!")

if __name__ == "__main__":
    main()

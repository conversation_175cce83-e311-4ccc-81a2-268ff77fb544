# ตั้งค่าข้อมูลคู่เงิน
symbol_info = {
    "EURUSD": {"Spread": 20, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": -10},
    "USDJPY": {"Spread": 20, "Digits": 3, "Points": 0.001, "Swap_Long": 2, "Swap_Short": -5},
    "GOLD":   {"Spread": 20, "Digits": 2, "Points": 0.01, "Swap_Long": 0, "Swap_Short": -5},
}

# แปลง timeframe เป็นตัวเลขนาที
timeframe_map = {"M1": 1, "M5": 5, "M15": 15, "M30": 30, "H1": 60, "H2": 120, "H4": 240, "D1": 1440}

# ฟังก์ชันแยกข้อมูล
def parse_filename(name):
    parts = name.split("_")
    pair_tf = parts[0]  # เช่น EURUSD#
    timeframe_str = parts[1]  # เช่น H1

    name_currency = pair_tf.replace("#", "")
    
    # กรณีพิเศษ: ถ้าเป็น GOLD ให้ base = GOLD, quote = USD
    if name_currency == "GOLD":
        base_currency = "GOLD"
        quote_currency = "USD"
    else:
        base_currency = name_currency[:3]
        quote_currency = name_currency[3:]

    tf_value = timeframe_map.get(timeframe_str, None)
    if tf_value is None:
        raise ValueError(f"Unknown timeframe: {timeframe_str}")

    info = symbol_info.get(name_currency)
    if info is None:
        raise ValueError(f"Unknown symbol info for: {name_currency}")

    result = {
        "Base_Currency": base_currency,
        "Quote_Currency": quote_currency,
        "Name_Currency": name_currency,
        "Timeframe_Currency": tf_value,
        "Spread": info["Spread"],
        "Digits": info["Digits"],
        "Points": info["Points"],
        "Swap_Long": info["Swap_Long"],
        "Swap_Short": info["Swap_Short"],
    }

    return result

# # ตัวอย่างการใช้งาน
# filenames = [
#     "EURUSD#_H1_202001020900_202503311900",
#     "EURUSD#_M5_202001020900_202503311900",
#     "USDJPY#_H1_202001020900_202503311900",
#     "USDJPY#_M15_202001020900_202503311900",
#     "GOLD#_H1_202001020900_202503311900",
#     "GOLD#_H2_202001020900_202503311900"
# ]

# for filename in filenames:
#     info = parse_filename(filename)
#     print(f"\n{filename}")
#     for key, value in info.items():
#         print(f"{key} = {value}")

symbol = "EURUSD#_M15_202001020900_202503311900"

info = parse_filename(symbol)  # ใช้ฟังก์ชันที่เราสร้างไว้ก่อนหน้านี้
base_currency = info["Base_Currency"]
timeframe = info["Timeframe_Currency"]
points = info["Points"]

print("Base_Currency:", base_currency)
print("timeframe:", timeframe) # number 15
print("timeframe:", str(timeframe).zfill(3)) # string 015
print("Points:", points) # 1e-05
print("Points:", f"{points:.10f}".rstrip('0').rstrip('.')) # 0.00001
# 📊 MT5 Status Manager - ฟีเจอร์การแสดงผลบนหน้าต่าง

## 🎯 **ฟีเจอร์ใหม่ที่เพิ่ม**

### **📺 การแสดงข้อความบนหน้าต่าง MT5**
- **แสดงสถานะแบบ Real-time** บนหน้าต่างชาร์ต
- **อัพเดทอัตโนมัติ** ทุก 5 วินาที
- **ข้อมูลครบถ้วน** ในหน้าเดียว
- **ดีไซน์สวยงาม** ด้วย Emoji และเส้นขีด

## 📋 **ข้อมูลที่แสดงบนหน้าต่าง**

### **🔝 ส่วนหัว**:
```
🤖 MT5 Status Manager
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

### **⏰ ข้อมูลเวลาและสถานะ**:
- **เวลาปัจจุบัน**: แสดงวันที่และเวลา
- **สถานะ Algo Trading**: ✅ เปิด / ❌ ปิด
- **จำนวนไม้**: จำนวนออเดอร์ที่เปิดอยู่

### **💰 ข้อมูลการเงิน**:
- **กำไร/ขาดทุนรวม**: คำนวณจาก Profit + Swap
- **ขาดทุนสูงสุด**: คำนวณตาม Stop Loss

### **📋 รายละเอียดออเดอร์**:
- **แสดงสูงสุด 5 ไม้** เพื่อไม่ให้หน้าจอแออัด
- **ข้อมูลแต่ละไม้**: Symbol, Type (BUY/SELL), Volume, P/L
- **ถ้ามีเกิน 5 ไม้**: แสดง "... และอีก X ไม้"

### **📱 สถานะการรายงาน**:
- **Telegram**: สถานะการส่งรายงาน
- **แท่งใหม่**: สถานะการแจ้งเตือนแท่งใหม่
- **ออเดอร์**: สถานะการแจ้งเตือนการเปลี่ยนแปลงออเดอร์
- **เวลารายงานล่าสุด**: เวลาที่ส่งรายงานครั้งล่าสุด

## 🔧 **ฟังก์ชันที่เพิ่มใหม่**

### **1. UpdateChartComment()**:
```mql5
void UpdateChartComment()
{
   datetime currentTime = TimeCurrent();
   
   // อัพเดทข้อความทุก 5 วินาที
   if(currentTime - LastCommentUpdate >= CommentUpdateInterval)
   {
      ChartComment = BuildChartComment();
      Comment(ChartComment);
      LastCommentUpdate = currentTime;
   }
}
```

### **2. BuildChartComment()**:
```mql5
string BuildChartComment()
{
   string comment = "";
   
   // สร้างข้อความแสดงสถานะครบถ้วน
   comment += "🤖 MT5 Status Manager\n";
   comment += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
   
   // เพิ่มข้อมูลต่างๆ
   // ... (รายละเอียดตามโค้ด)
   
   return comment;
}
```

### **3. GetPositionDetailsForChart()**:
```mql5
string GetPositionDetailsForChart()
{
   string details = "";
   int maxDisplay = 5; // แสดงสูงสุด 5 ไม้
   
   // วนลูปแสดงออเดอร์
   for(int i = 0; i < PositionsTotal() && count < maxDisplay; i++)
   {
      // ... (รายละเอียดตามโค้ด)
   }
   
   return details;
}
```

## ⚙️ **ตัวแปรใหม่ที่เพิ่ม**

### **Global Variables**:
```mql5
//--- Chart display variables
string ChartComment = "";
int CommentUpdateInterval = 5; // อัพเดทข้อความทุก 5 วินาที
datetime LastCommentUpdate = 0;
```

## 📱 **ตัวอย่างการแสดงผล**

### **📺 หน้าต่าง MT5 จะแสดง**:
```
🤖 MT5 Status Manager
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏰ เวลา: 2025.07.05 17:13
🤖 Algo Trading: ✅ เปิด
📊 จำนวนไม้: 3 ไม้
💰 กำไร: +125.50 USD
⚠️ ขาดทุนสูงสุด: -450.00 USD

📋 รายละเอียดออเดอร์:
• EURUSD BUY 0.10 (P/L: +45.20)
• GBPUSD SELL 0.05 (P/L: +30.15)
• USDJPY BUY 0.15 (P/L: +50.15)

📱 Telegram: ✅ เปิด | 📊 แท่งใหม่: ✅ | 🔄 ออเดอร์: ✅
📤 รายงานล่าสุด: 17:10
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

## 🎯 **ประโยชน์ของฟีเจอร์นี้**

### **👀 ติดตามสถานะได้ง่าย**:
- **ไม่ต้องเปิด Telegram** เพื่อดูสถานะ
- **ข้อมูลอัพเดทแบบ Real-time** บนหน้าจอ
- **เห็นภาพรวม** ของการเทรดในหน้าเดียว

### **📊 ข้อมูลครบถ้วน**:
- **สถานะระบบ**: Algo Trading, การรายงาน
- **ข้อมูลการเงิน**: กำไร/ขาดทุน, ความเสี่ยง
- **รายละเอียดออเดอร์**: แต่ละไม้ที่เปิดอยู่

### **⚡ ประสิทธิภาพสูง**:
- **อัพเดทเฉพาะเมื่อจำเป็น** (ทุก 5 วินาที)
- **ไม่กระทบประสิทธิภาพ** การเทรด
- **แสดงข้อมูลสำคัญ** เท่านั้น

## 🔧 **การปรับแต่ง**

### **เปลี่ยนความถี่การอัพเดท**:
```mql5
int CommentUpdateInterval = 5;  // 5 วินาที (เริ่มต้น)
int CommentUpdateInterval = 10; // 10 วินาที (ช้าลง)
int CommentUpdateInterval = 1;  // 1 วินาที (เร็วขึ้น)
```

### **เปลี่ยนจำนวนออเดอร์ที่แสดง**:
```mql5
int maxDisplay = 5;  // แสดง 5 ไม้ (เริ่มต้น)
int maxDisplay = 10; // แสดง 10 ไม้
int maxDisplay = 3;  // แสดง 3 ไม้
```

## 📋 **การใช้งาน**

### **1. คอมไพล์และติดตั้ง**:
```bash
# คอมไพล์ไฟล์ MT5_Status_Manager.mq5
# ติดตั้ง EA ในชาร์ต
# ข้อความจะแสดงอัตโนมัติ
```

### **2. ตำแหน่งการแสดงผล**:
- **มุมซ้ายบน** ของหน้าต่างชาร์ต
- **ไม่บังกราฟ** ราคา
- **สามารถเลื่อน** หน้าต่างได้

### **3. การปิด/เปิดการแสดงผล**:
```mql5
// ปิดการแสดงผล
Comment("");

// เปิดการแสดงผลใหม่
UpdateChartComment();
```

## 🎉 **สรุป**

ฟีเจอร์การแสดงผลบนหน้าต่างทำให้:
- **ติดตามสถานะได้ง่ายขึ้น** 👀
- **ข้อมูลครบถ้วนในหน้าเดียว** 📊
- **ไม่ต้องพึ่งพา Telegram** เพียงอย่างเดียว 📱
- **ประสิทธิภาพสูง** และไม่กระทบการเทรด ⚡

ระบบพร้อมใช้งานแล้ว! 🚀

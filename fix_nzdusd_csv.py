#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
แก้ไข NZDUSD CSV format problem
"""

import pandas as pd
import numpy as np

def fix_nzdusd_csv():
    """แก้ไข NZDUSD CSV format"""
    print("🔧 แก้ไข NZDUSD CSV Format Problem")
    print("=" * 60)
    
    try:
        # อ่านไฟล์ด้วย tab separator
        file_path = "NZDUSD#_M30_201905010000_202504302330.csv"
        print(f"📂 อ่านไฟล์: {file_path}")
        
        # อ่านด้วย tab separator
        df = pd.read_csv(file_path, sep='\t')
        print(f"✅ อ่านสำเร็จ: {len(df)} rows, {len(df.columns)} columns")
        
        # แสดง columns
        print(f"\n📊 Columns:")
        for i, col in enumerate(df.columns):
            print(f"   {i+1}. {col}")
        
        # แสดงข้อมูลตัวอย่าง
        print(f"\n📊 ข้อมูลตัวอย่าง:")
        print(df.head())
        
        # เปลี่ยนชื่อ columns ให้เป็นมาตรฐาน
        column_mapping = {
            '<DATE>': 'Date',
            '<TIME>': 'Time', 
            '<OPEN>': 'Open',
            '<HIGH>': 'High',
            '<LOW>': 'Low',
            '<CLOSE>': 'Close',
            '<TICKVOL>': 'TickVol',
            '<VOL>': 'Vol',
            '<SPREAD>': 'Spread'
        }
        
        df = df.rename(columns=column_mapping)
        print(f"\n✅ เปลี่ยนชื่อ columns สำเร็จ")
        
        # สร้าง datetime column
        df['datetime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])
        print(f"✅ สร้าง datetime column สำเร็จ")
        
        # ตรวจสอบ data types
        print(f"\n📊 Data Types:")
        print(df.dtypes)
        
        # บันทึกไฟล์ใหม่
        new_file_path = "NZDUSD_M30_FIXED.csv"
        df.to_csv(new_file_path, index=False)
        print(f"\n✅ บันทึกไฟล์ใหม่: {new_file_path}")
        
        return df
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {str(e)}")
        return None

def test_fixed_data():
    """ทดสอบข้อมูลที่แก้ไขแล้ว"""
    print(f"\n🧪 ทดสอบข้อมูลที่แก้ไขแล้ว")
    print("=" * 60)
    
    try:
        # อ่านไฟล์ที่แก้ไขแล้ว
        df = pd.read_csv("NZDUSD_M30_FIXED.csv")
        print(f"✅ อ่านไฟล์แก้ไขสำเร็จ: {len(df)} rows")
        
        # ตรวจสอบ price data
        price_cols = ['Open', 'High', 'Low', 'Close']
        print(f"\n📊 Price Data Summary:")
        print(df[price_cols].describe())
        
        # ตรวจสอบ missing values
        missing = df.isnull().sum()
        print(f"\n📊 Missing Values:")
        print(missing[missing > 0])
        
        # สร้าง basic features สำหรับทดสอบ
        print(f"\n🔧 สร้าง Basic Features...")
        
        # Price features
        df['HL_ratio'] = df['High'] / df['Low']
        df['OC_ratio'] = df['Open'] / df['Close']
        df['price_range'] = (df['High'] - df['Low']) / df['Close']
        
        # Moving averages
        df['MA_5'] = df['Close'].rolling(5).mean()
        df['MA_20'] = df['Close'].rolling(20).mean()
        df['MA_ratio'] = df['MA_5'] / df['MA_20']
        
        # Returns
        df['return_1'] = df['Close'].pct_change(1)
        df['return_5'] = df['Close'].pct_change(5)
        
        # Volatility
        df['volatility'] = df['return_1'].rolling(20).std()
        
        # สร้าง simple target (price up in next period)
        df['target'] = (df['Close'].shift(-1) > df['Close']).astype(int)
        
        # ลบ rows ที่มี NaN
        df_clean = df.dropna()
        print(f"✅ สร้าง features สำเร็จ: {len(df_clean)} rows (หลังลบ NaN)")
        
        # ตรวจสอบ target distribution
        if 'target' in df_clean.columns:
            target_counts = df_clean['target'].value_counts()
            print(f"\n📊 Target Distribution:")
            print(target_counts)
            
            ratio = target_counts.max() / target_counts.min()
            print(f"📊 Imbalance Ratio: {ratio:.1f}:1")
        
        return df_clean
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {str(e)}")
        return None

def test_simple_model():
    """ทดสอบโมเดลง่ายๆ"""
    print(f"\n🤖 ทดสอบโมเดลง่ายๆ")
    print("=" * 60)
    
    try:
        # โหลดข้อมูล
        df = pd.read_csv("NZDUSD_M30_FIXED.csv")
        
        # สร้าง features แบบง่าย
        df['return_1'] = df['Close'].pct_change(1)
        df['return_5'] = df['Close'].pct_change(5)
        df['MA_5'] = df['Close'].rolling(5).mean()
        df['MA_20'] = df['Close'].rolling(20).mean()
        df['MA_ratio'] = df['MA_5'] / df['MA_20']
        df['volatility'] = df['return_1'].rolling(20).std()
        df['target'] = (df['Close'].shift(-1) > df['Close']).astype(int)
        
        # ลบ NaN
        df_clean = df.dropna()
        
        # เตรียม features
        feature_cols = ['return_1', 'return_5', 'MA_ratio', 'volatility']
        X = df_clean[feature_cols]
        y = df_clean['target']
        
        print(f"📊 Features: {feature_cols}")
        print(f"📊 X shape: {X.shape}")
        print(f"📊 y distribution: {y.value_counts().to_dict()}")
        
        # ทดสอบ cross validation
        from sklearn.model_selection import TimeSeriesSplit
        from sklearn.metrics import roc_auc_score
        import lightgbm as lgb
        
        tscv = TimeSeriesSplit(n_splits=5)
        cv_scores = []
        
        print(f"\n🔍 Cross Validation Test...")
        
        for fold, (train_idx, val_idx) in enumerate(tscv.split(X)):
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            # ตรวจสอบ class balance
            if len(y_val.unique()) < 2:
                print(f"   Fold {fold+1}: ⚠️ Single class in validation")
                cv_scores.append(0.5)
                continue
            
            # เทรนโมเดล
            model = lgb.LGBMClassifier(
                objective='binary',
                n_estimators=50,
                learning_rate=0.1,
                random_state=42,
                verbose=-1
            )
            
            model.fit(X_train, y_train)
            y_pred_proba = model.predict_proba(X_val)[:, 1]
            
            auc = roc_auc_score(y_val, y_pred_proba)
            cv_scores.append(auc)
            print(f"   Fold {fold+1}: AUC = {auc:.4f}")
        
        # สรุปผล
        mean_cv = np.mean(cv_scores)
        print(f"\n📊 CV Results:")
        print(f"   Mean CV AUC: {mean_cv:.4f}")
        print(f"   CV Scores: {[f'{score:.4f}' for score in cv_scores]}")
        
        if mean_cv > 0.6:
            print(f"✅ CV AUC ดี - ปัญหาได้รับการแก้ไข!")
        elif mean_cv > 0.55:
            print(f"⚠️ CV AUC ปานกลาง - ต้องปรับปรุงเพิ่มเติม")
        else:
            print(f"❌ CV AUC ยังต่ำ - ต้องแก้ไขเพิ่มเติม")
        
        return mean_cv
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {str(e)}")
        return None

def main():
    """ฟังก์ชันหลัก"""
    print("🔥 แก้ไข NZDUSD M30 Critical Issue")
    print("=" * 80)
    
    # 1. แก้ไข CSV format
    df_fixed = fix_nzdusd_csv()
    
    # 2. ทดสอบข้อมูลที่แก้ไขแล้ว
    if df_fixed is not None:
        df_test = test_fixed_data()
    else:
        df_test = None
    
    # 3. ทดสอบโมเดลง่ายๆ
    if df_test is not None:
        cv_auc = test_simple_model()
    else:
        cv_auc = None
    
    # 4. สรุป
    print(f"\n🎯 สรุปการแก้ไข NZDUSD M30")
    print("=" * 80)
    
    if df_fixed is not None:
        print("✅ แก้ไข CSV format สำเร็จ")
    else:
        print("❌ แก้ไข CSV format ไม่สำเร็จ")
    
    if df_test is not None:
        print("✅ สร้าง features สำเร็จ")
    else:
        print("❌ สร้าง features ไม่สำเร็จ")
    
    if cv_auc is not None:
        print(f"📊 CV AUC: {cv_auc:.4f}")
        if cv_auc > 0.6:
            print("✅ ปัญหาได้รับการแก้ไข!")
        else:
            print("⚠️ ต้องปรับปรุงเพิ่มเติม")
    else:
        print("❌ ไม่สามารถทดสอบโมเดลได้")
    
    print(f"\n🚀 ขั้นตอนถัดไป:")
    if cv_auc and cv_auc > 0.6:
        print("1. อัปเดต main script ให้ใช้ tab separator สำหรับ NZDUSD")
        print("2. ทดสอบ USDJPY M30 F1 Score")
        print("3. รัน full training")
    else:
        print("1. ปรับปรุง feature engineering เพิ่มเติม")
        print("2. ปรับ class weight")
        print("3. ทดสอบใหม่")

if __name__ == "__main__":
    main()

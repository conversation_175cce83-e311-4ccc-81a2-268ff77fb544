#!/usr/bin/env python3
"""
Test Enhanced Signal Transmission System
ทดสอบระบบการส่งสัญญาณที่ปรับปรุงแล้วระหว่าง Python และ MT5

การทดสอบ:
1. ตรวจสอบการส่งข้อมูลเพิ่มเติม (class, best_entry, nBars_SL, threshold, time_filters, spread)
2. ตรวจสอบการแสดงผลใน MT5 ที่ปรับปรุงแล้ว
3. ตรวจสอบประสิทธิภาพการส่งข้อมูล
"""

import requests
import json
import time
import datetime

# Configuration
PYTHON_SERVER_URL = "http://127.0.0.1:5000/data"
TEST_SYMBOLS = ["EURUSD", "GBPUSD", "USDJPY"]
TEST_TIMEFRAMES = ["PERIOD_M30", "PERIOD_H1"]

def create_test_data(symbol, timeframe):
    """สร้างข้อมูลทดสอบสำหรับส่งไปยัง Python server"""
    current_time = datetime.datetime.now()
    
    # สร้างข้อมูลแท่งเทียนจำลอง
    bars_data = []
    for i in range(5):  # สร้าง 5 แท่งข้อมูล
        bar_time = current_time - datetime.timedelta(minutes=30*i)
        bar_data = {
            "time": int(bar_time.timestamp()),
            "open": 1.1000 + (i * 0.0001),
            "high": 1.1010 + (i * 0.0001),
            "low": 1.0990 + (i * 0.0001),
            "close": 1.1005 + (i * 0.0001),
            "tick_volume": 1000 + (i * 100)
        }
        bars_data.append(bar_data)
    
    test_data = {
        "symbol": symbol,
        "timeframe_str": timeframe,
        "time": int(current_time.timestamp()),
        "open": 1.1005,
        "high": 1.1015,
        "low": 1.0995,
        "close": 1.1010,
        "tick_volume": 1500,
        "bars": bars_data
    }
    
    return test_data

def test_enhanced_signal_transmission():
    """ทดสอบการส่งสัญญาณที่ปรับปรุงแล้ว"""
    print("🚀 เริ่มทดสอบระบบการส่งสัญญาณที่ปรับปรุงแล้ว")
    print("=" * 80)
    
    for symbol in TEST_SYMBOLS:
        for timeframe in TEST_TIMEFRAMES:
            print(f"\n📊 ทดสอบ {symbol} {timeframe}")
            print("-" * 50)
            
            # สร้างข้อมูลทดสอบ
            test_data = create_test_data(symbol, timeframe)
            
            try:
                # ส่งข้อมูลไปยัง Python server
                start_time = time.time()
                response = requests.post(
                    PYTHON_SERVER_URL,
                    json=test_data,
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )
                end_time = time.time()
                
                response_time = (end_time - start_time) * 1000  # แปลงเป็น milliseconds
                
                if response.status_code == 200:
                    response_data = response.json()
                    
                    print(f"✅ การส่งสำเร็จ (เวลา: {response_time:.2f}ms)")
                    print(f"📈 Signal: {response_data.get('signal', 'N/A')}")
                    print(f"🎯 Class: {response_data.get('class', 'N/A')}")
                    print(f"📊 Confidence: {response_data.get('confidence', 0.0):.4f}")
                    print(f"💰 Entry Price: {response_data.get('entry_price', 0.0):.5f}")
                    print(f"🛑 SL Price: {response_data.get('sl_price', 0.0):.5f}")
                    print(f"🎯 TP Price: {response_data.get('tp_price', 0.0):.5f}")
                    print(f"⭐ Best Entry: {response_data.get('best_entry', 0.0):.5f}")
                    print(f"📏 nBars SL: {response_data.get('nBars_SL', 0)}")
                    print(f"🎚️ Threshold: {response_data.get('threshold', 0.0):.4f}")
                    print(f"⏰ Time Filters: {response_data.get('time_filters', 'N/A')}")
                    print(f"📊 Spread: {response_data.get('spread', 0)}")
                    
                    # ตรวจสอบข้อมูลที่จำเป็น
                    required_fields = ['signal', 'class', 'confidence', 'best_entry', 'nBars_SL', 'threshold', 'time_filters', 'spread']
                    missing_fields = [field for field in required_fields if field not in response_data]
                    
                    if missing_fields:
                        print(f"⚠️ ข้อมูลที่ขาดหายไป: {', '.join(missing_fields)}")
                    else:
                        print("✅ ข้อมูลครบถ้วนทั้งหมด")
                        
                else:
                    print(f"❌ การส่งล้มเหลว: HTTP {response.status_code}")
                    print(f"📄 Response: {response.text}")
                    
            except requests.exceptions.Timeout:
                print("⏰ การส่งข้อมูลใช้เวลานานเกินไป (Timeout)")
            except requests.exceptions.ConnectionError:
                print("🔌 ไม่สามารถเชื่อมต่อกับ Python server ได้")
            except Exception as e:
                print(f"❌ เกิดข้อผิดพลาด: {e}")
            
            time.sleep(1)  # รอ 1 วินาทีก่อนทดสอบครั้งต่อไป

def test_performance():
    """ทดสอบประสิทธิภาพการส่งข้อมูล"""
    print("\n🏃‍♂️ ทดสอบประสิทธิภาพการส่งข้อมูล")
    print("=" * 80)
    
    symbol = "EURUSD"
    timeframe = "PERIOD_M30"
    num_tests = 10
    
    response_times = []
    
    for i in range(num_tests):
        test_data = create_test_data(symbol, timeframe)
        
        try:
            start_time = time.time()
            response = requests.post(
                PYTHON_SERVER_URL,
                json=test_data,
                headers={'Content-Type': 'application/json'},
                timeout=5
            )
            end_time = time.time()
            
            if response.status_code == 200:
                response_time = (end_time - start_time) * 1000
                response_times.append(response_time)
                print(f"Test {i+1:2d}: {response_time:6.2f}ms")
            else:
                print(f"Test {i+1:2d}: Failed (HTTP {response.status_code})")
                
        except Exception as e:
            print(f"Test {i+1:2d}: Error - {e}")
        
        time.sleep(0.1)  # รอเล็กน้อยระหว่างการทดสอบ
    
    if response_times:
        avg_time = sum(response_times) / len(response_times)
        min_time = min(response_times)
        max_time = max(response_times)
        
        print(f"\n📊 สถิติประสิทธิภาพ:")
        print(f"   เวลาเฉลี่ย: {avg_time:.2f}ms")
        print(f"   เวลาเร็วสุด: {min_time:.2f}ms")
        print(f"   เวลาช้าสุด: {max_time:.2f}ms")
        print(f"   จำนวนทดสอบสำเร็จ: {len(response_times)}/{num_tests}")
        
        if avg_time < 500:
            print("✅ ประสิทธิภาพดีมาก (< 500ms)")
        elif avg_time < 1000:
            print("⚠️ ประสิทธิภาพปานกลาง (500-1000ms)")
        else:
            print("❌ ประสิทธิภาพต้องปรับปรุง (> 1000ms)")

def main():
    """ฟังก์ชันหลักสำหรับการทดสอบ"""
    print("🔧 Enhanced Signal Transmission Test")
    print(f"⏰ เวลาทดสอบ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 Python Server: {PYTHON_SERVER_URL}")
    
    # ทดสอบการส่งสัญญาณที่ปรับปรุงแล้ว
    test_enhanced_signal_transmission()
    
    # ทดสอบประสิทธิภาพ
    test_performance()
    
    print("\n🎉 การทดสอบเสร็จสิ้น")
    print("=" * 80)
    print("📋 สรุปการปรับปรุง:")
    print("   ✅ เพิ่มข้อมูล class level (STRONG_BUY, BUY, HOLD, SELL, STRONG_SELL)")
    print("   ✅ เพิ่มข้อมูล best_entry สำหรับจุดเข้าที่ดีที่สุด")
    print("   ✅ เพิ่มข้อมูล nBars_SL สำหรับจำนวนแท่งสำหรับ SL")
    print("   ✅ เพิ่มข้อมูล threshold สำหรับเกณฑ์การตัดสินใจ")
    print("   ✅ เพิ่มข้อมูล time_filters สำหรับการกรองเวลา")
    print("   ✅ เพิ่มข้อมูล spread สำหรับค่า spread")
    print("   ✅ ปรับปรุงการแสดงผลใน MT5 ให้ครบถ้วนและชัดเจนขึ้น")

if __name__ == "__main__":
    main()

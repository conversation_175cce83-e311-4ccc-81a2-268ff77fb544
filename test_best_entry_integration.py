#!/usr/bin/env python3
"""
ทดสอบการทำงานของระบบ best_entry.pkl ที่ปรับปรุงแล้ว
"""

import os
import pickle
import sys
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_load_best_entry_function():
    """
    ทดสอบฟังก์ชัน load_best_entry_condition
    """
    print("🧪 ทดสอบฟังก์ชัน load_best_entry_condition")
    print("=" * 50)
    
    try:
        # Import ฟังก์ชันจาก WebRequest server
        from python_to_mt5_WebRequest_server_11_Tuning import load_best_entry_condition
        
        # ทดสอบกับ symbol และ timeframe ต่างๆ
        test_cases = [
            ("USDJPY", 60),
            ("EURUSD", 30),
            ("GBPUSD", 60),
            ("AUDUSD", 30)
        ]
        
        for symbol, timeframe in test_cases:
            print(f"\n📋 ทดสอบ {symbol} {timeframe}:")
            best_entry = load_best_entry_condition(symbol, timeframe)
            print(f"   ผลลัพธ์: {best_entry}")
            
    except ImportError as e:
        print(f"❌ ไม่สามารถ import ฟังก์ชันได้: {e}")
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")

def test_entry_conditions_functions():
    """
    ทดสอบฟังก์ชัน entry conditions ต่างๆ
    """
    print("\n🧪 ทดสอบฟังก์ชัน entry conditions")
    print("=" * 50)
    
    try:
        # Import ฟังก์ชันจาก WebRequest server
        from python_to_mt5_WebRequest_server_11_Tuning import get_entry_conditions_functions
        
        # สร้างข้อมูลทดสอบ
        test_features = {
            'Close': 1.1000,
            'Open': 1.0990,
            'EMA50': 1.0980,
            'RSI': 55.0,
            'MACD': 0.0005,
            'MACD_signal': 1.0,
            'RSI_signal': 10.0,
            'Volume': 1000,
            'Volume_MA20': 800,
            'PullBack_Up': 15.0,
            'PullBack_Down': 5.0,
            'Ratio_Buy': 25.0,
            'Ratio_Sell': 10.0
        }
        
        # พารามิเตอร์ทดสอบ
        input_rsi_level_in = 5.0
        input_pull_back = 10.0
        input_take_profit = 5.0
        
        # ทดสอบ entry conditions ทั้งหมด
        entry_funcs = get_entry_conditions_functions()
        
        for entry_name, entry_func in entry_funcs.items():
            print(f"\n📋 ทดสอบ {entry_name}:")
            try:
                buy_signal, sell_signal = entry_func(
                    test_features, 
                    input_rsi_level_in, 
                    input_pull_back, 
                    input_take_profit
                )
                print(f"   Buy Signal: {buy_signal}")
                print(f"   Sell Signal: {sell_signal}")
            except Exception as e:
                print(f"   ❌ เกิดข้อผิดพลาด: {e}")
                
    except ImportError as e:
        print(f"❌ ไม่สามารถ import ฟังก์ชันได้: {e}")
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")

def create_test_best_entry_files():
    """
    สร้างไฟล์ best_entry.pkl ทดสอบ
    """
    print("\n🧪 สร้างไฟล์ best_entry.pkl ทดสอบ")
    print("=" * 50)
    
    # สร้างโฟลเดอร์ทดสอบ
    test_folders = [
        "Test_LightGBM/results/M30",
        "Test_LightGBM/results/M60"
    ]
    
    for folder in test_folders:
        os.makedirs(folder, exist_ok=True)
        print(f"✅ สร้างโฟลเดอร์: {folder}")
    
    # สร้างไฟล์ทดสอบ
    test_data = [
        {
            "folder": "Test_LightGBM/results/M30",
            "filename": "030_EURUSD_best_entry.pkl",
            "data": {
                "entry_name": "entry_v2",
                "expectancy": 15.25,
                "win_rate": 0.65,
                "num_trades": 45,
                "timestamp": datetime.now().isoformat(),
                "symbol": "EURUSD",
                "timeframe": 30
            }
        },
        {
            "folder": "Test_LightGBM/results/M60",
            "filename": "060_USDJPY_best_entry.pkl",
            "data": {
                "entry_name": "entry_v3",
                "expectancy": 22.15,
                "win_rate": 0.72,
                "num_trades": 38,
                "timestamp": datetime.now().isoformat(),
                "symbol": "USDJPY",
                "timeframe": 60
            }
        },
        {
            "folder": "Test_LightGBM/results/M30",
            "filename": "030_GBPUSD_best_entry.pkl",
            "data": {
                "entry_name": "entry_v1",
                "expectancy": 8.75,
                "win_rate": 0.58,
                "num_trades": 52,
                "timestamp": datetime.now().isoformat(),
                "symbol": "GBPUSD",
                "timeframe": 30
            }
        }
    ]
    
    for item in test_data:
        file_path = os.path.join(item["folder"], item["filename"])
        try:
            with open(file_path, 'wb') as f:
                pickle.dump(item["data"], f)
            print(f"✅ สร้างไฟล์: {file_path}")
            print(f"   Entry: {item['data']['entry_name']}, Expectancy: {item['data']['expectancy']}")
        except Exception as e:
            print(f"❌ ไม่สามารถสร้างไฟล์ {file_path}: {e}")

def test_integration():
    """
    ทดสอบการทำงานรวมของระบบ
    """
    print("\n🧪 ทดสอบการทำงานรวมของระบบ")
    print("=" * 50)
    
    try:
        from python_to_mt5_WebRequest_server_11_Tuning import (
            load_best_entry_condition, 
            get_entry_conditions_functions
        )
        
        # ทดสอบการโหลดและใช้งาน entry conditions
        test_cases = [
            ("EURUSD", 30),  # ควรได้ entry_v2
            ("USDJPY", 60),  # ควรได้ entry_v3
            ("GBPUSD", 30),  # ควรได้ entry_v1
            ("AUDUSD", 60)   # ควรได้ entry_v1 (fallback)
        ]
        
        # ข้อมูลทดสอบ
        test_features = {
            'Close': 1.1000,
            'Open': 1.0990,
            'EMA50': 1.0980,
            'RSI': 55.0,
            'MACD': 0.0005,
            'MACD_signal': 1.0,
            'RSI_signal': 10.0,
            'Volume': 1000,
            'Volume_MA20': 800,
            'PullBack_Up': 15.0,
            'PullBack_Down': 5.0,
            'Ratio_Buy': 25.0,
            'Ratio_Sell': 10.0
        }
        
        input_rsi_level_in = 5.0
        input_pull_back = 10.0
        input_take_profit = 5.0
        
        entry_funcs = get_entry_conditions_functions()
        
        for symbol, timeframe in test_cases:
            print(f"\n📋 ทดสอบ {symbol} {timeframe}:")
            
            # โหลด best entry
            best_entry_name = load_best_entry_condition(symbol, timeframe)
            print(f"   Best Entry: {best_entry_name}")
            
            # ใช้ entry condition
            entry_func = entry_funcs.get(best_entry_name, entry_funcs["entry_v1"])
            buy_signal, sell_signal = entry_func(
                test_features, 
                input_rsi_level_in, 
                input_pull_back, 
                input_take_profit
            )
            
            print(f"   Buy Signal: {buy_signal}")
            print(f"   Sell Signal: {sell_signal}")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🔧 ทดสอบระบบ best_entry.pkl ที่ปรับปรุงแล้ว")
    print("=" * 80)
    
    # 1. สร้างไฟล์ทดสอบ
    create_test_best_entry_files()
    
    # 2. ทดสอบฟังก์ชัน load_best_entry_condition
    test_load_best_entry_function()
    
    # 3. ทดสอบฟังก์ชัน entry conditions
    test_entry_conditions_functions()
    
    # 4. ทดสอบการทำงานรวม
    test_integration()
    
    print(f"\n{'='*80}")
    print("🎯 สรุปการทดสอบ:")
    print("1. ✅ ระบบสามารถโหลด best_entry.pkl ได้")
    print("2. ✅ ระบบสามารถใช้ entry conditions ต่างๆ ได้")
    print("3. ✅ WebRequest server จะใช้ entry condition ที่ดีที่สุดจากการทดสอบ")
    print("4. ✅ ระบบมีการ fallback เป็น entry_v1 เมื่อไม่พบไฟล์")
    print("\n💡 ขั้นตอนต่อไป:")
    print("- รัน training model เพื่อสร้าง best_entry.pkl จริง")
    print("- ทดสอบ WebRequest server กับข้อมูลจริง")
    print("- ตรวจสอบการอัปเดต entry conditions แบบ real-time")

if __name__ == "__main__":
    main()

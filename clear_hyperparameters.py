#!/usr/bin/env python3
"""
สคริปต์สำหรับลบไฟล์ hyperparameter เพื่อให้เทรนใหม่
หลังจากปรับปรุง Parameter Distribution ตามผลการวิเคราะห์ Parameter Stability
"""

import os
import glob
from pathlib import Path

def clear_hyperparameter_files():
    """
    ลบไฟล์ hyperparameter ทั้งหมดเพื่อให้เทรนใหม่
    """
    print("🧹 เริ่มลบไฟล์ hyperparameter...")
    
    # กำหนดโฟลเดอร์
    hyper_folder = "LightGBM_Hyper_Multi"
    
    if not os.path.exists(hyper_folder):
        print(f"❌ ไม่พบโฟลเดอร์ {hyper_folder}")
        return
    
    # รายการไฟล์ที่จะลบ
    patterns_to_delete = [
        "*_best_params.json",
        "*_tuning_flag.json"
    ]
    
    deleted_count = 0
    
    for pattern in patterns_to_delete:
        files = glob.glob(os.path.join(hyper_folder, "**", pattern), recursive=True)
        
        for file_path in files:
            try:
                os.remove(file_path)
                print(f"✅ ลบแล้ว: {file_path}")
                deleted_count += 1
            except Exception as e:
                print(f"❌ ไม่สามารถลบ {file_path}: {e}")
    
    print(f"\n📊 สรุป: ลบไฟล์ทั้งหมด {deleted_count} ไฟล์")
    
    # แสดงไฟล์ที่เหลือ
    remaining_files = glob.glob(os.path.join(hyper_folder, "**", "*"), recursive=True)
    remaining_files = [f for f in remaining_files if os.path.isfile(f)]
    
    if remaining_files:
        print(f"\n📁 ไฟล์ที่เหลือใน {hyper_folder}:")
        for file_path in remaining_files:
            print(f"   {file_path}")
    else:
        print(f"\n✅ โฟลเดอร์ {hyper_folder} ว่างเปล่าแล้ว")

def show_file_structure():
    """
    แสดงโครงสร้างไฟล์ปัจจุบัน
    """
    print("\n📁 โครงสร้างไฟล์ปัจจุบัน:")
    print("=" * 50)
    
    folders_to_check = [
        "LightGBM_Hyper_Multi",
        "LightGBM_Multi"
    ]
    
    for folder in folders_to_check:
        if os.path.exists(folder):
            print(f"\n📂 {folder}/")
            
            # แสดงไฟล์ในโฟลเดอร์
            for root, dirs, files in os.walk(folder):
                level = root.replace(folder, '').count(os.sep)
                indent = '│   ' * level
                subindent = '│   ' * (level + 1)
                
                if level == 0:
                    for dir_name in dirs:
                        print(f"{indent}├─ {dir_name}/")
                
                for file_name in files:
                    if file_name.endswith(('.json', '.pkl')):
                        print(f"{subindent}├─ {file_name}")
        else:
            print(f"\n❌ ไม่พบโฟลเดอร์ {folder}")

def backup_hyperparameters():
    """
    สำรองไฟล์ hyperparameter ก่อนลบ (ถ้าต้องการ)
    """
    import shutil
    from datetime import datetime
    
    hyper_folder = "LightGBM_Hyper_Multi"
    backup_folder = f"LightGBM_Hyper_Multi_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    if os.path.exists(hyper_folder):
        try:
            shutil.copytree(hyper_folder, backup_folder)
            print(f"✅ สำรองข้อมูลไปยัง: {backup_folder}")
            return True
        except Exception as e:
            print(f"❌ ไม่สามารถสำรองข้อมูล: {e}")
            return False
    else:
        print(f"❌ ไม่พบโฟลเดอร์ {hyper_folder} ที่จะสำรอง")
        return False

if __name__ == "__main__":
    print("🔄 การลบไฟล์ Hyperparameter หลังปรับปรุง Parameter Distribution")
    print("=" * 70)
    
    # แสดงโครงสร้างไฟล์ปัจจุบัน
    show_file_structure()
    
    # ถามผู้ใช้ว่าต้องการสำรองหรือไม่
    backup_choice = input("\n❓ ต้องการสำรองไฟล์ hyperparameter ก่อนลบหรือไม่? (y/n): ").lower()
    
    if backup_choice == 'y':
        if backup_hyperparameters():
            print("✅ สำรองข้อมูลเรียบร้อย")
        else:
            print("⚠️ ไม่สามารถสำรองข้อมูลได้")
    
    # ถามยืนยันการลบ
    confirm = input("\n❓ ยืนยันการลบไฟล์ hyperparameter? (y/n): ").lower()
    
    if confirm == 'y':
        clear_hyperparameter_files()
        print("\n✅ เสร็จสิ้น! ตอนนี้สามารถเทรนใหม่ด้วยพารามิเตอร์ที่ปรับปรุงแล้ว")
        print("\n📋 ขั้นตอนต่อไป:")
        print("   1. รันสคริปต์เทรนโมเดล")
        print("   2. ระบบจะใช้ parameter distribution ใหม่")
        print("   3. จะได้พารามิเตอร์ที่เหมาะสมกว่าเดิม")
    else:
        print("❌ ยกเลิกการลบไฟล์")
    
    # แสดงโครงสร้างไฟล์หลังการลบ
    if confirm == 'y':
        show_file_structure()

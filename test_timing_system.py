#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบระบบการจับเวลาการเทรนที่ปรับปรุงแล้ว
"""

import os
import sys
import time
import json
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

def simulate_training_process():
    """จำลองกระบวนการเทรนเพื่อทดสอบการจับเวลา"""
    
    print("🧪 ทดสอบระบบการจับเวลาการเทรน")
    print("="*60)
    
    try:
        # Import ฟังก์ชันที่ต้องการ
        from python_LightGBM_16_Signal import (
            test_groups, 
            NUM_TRAINING_ROUNDS,
            test_folder,
            analyze_time_summary_files
        )
        
        print(f"📊 การตั้งค่าปัจจุบัน:")
        print(f"   NUM_TRAINING_ROUNDS: {NUM_TRAINING_ROUNDS}")
        print(f"   test_folder: {test_folder}")
        print(f"   test_groups: {list(test_groups.keys())}")
        
        # ตรวจสอบโครงสร้างไฟล์
        print(f"\n📁 ตรวจสอบโครงสร้างไฟล์:")
        for group_name, files in test_groups.items():
            print(f"   {group_name}: {len(files)} ไฟล์")
            for file in files:
                exists = "✅" if os.path.exists(file) else "❌"
                print(f"      {exists} {file}")
        
        # ตรวจสอบไฟล์ time summary ที่มีอยู่
        print(f"\n📊 ไฟล์ time summary ที่มีอยู่:")
        time_summary_files = []
        for group_name in test_groups.keys():
            summary_file = os.path.join(test_folder, f"{group_name}_time_summary.txt")
            if os.path.exists(summary_file):
                print(f"   ✅ {summary_file}")
                time_summary_files.append(summary_file)
                
                # แสดงเนื้อหาไฟล์
                try:
                    with open(summary_file, "r", encoding="utf-8") as f:
                        content = f.read()
                    
                    lines = content.strip().split('\n')
                    recent_lines = lines[-10:] if len(lines) > 10 else lines
                    
                    print(f"      📄 เนื้อหาล่าสุด (10 บรรทัดสุดท้าย):")
                    for line in recent_lines:
                        if line.strip():
                            print(f"         {line}")
                            
                except Exception as e:
                    print(f"      ❌ ไม่สามารถอ่านไฟล์: {e}")
            else:
                print(f"   ❌ {summary_file} (ไม่พบ)")
        
        # ทดสอบฟังก์ชันวิเคราะห์เวลา
        if time_summary_files:
            print(f"\n🔍 ทดสอบการวิเคราะห์ไฟล์ time summary:")
            try:
                stats = analyze_time_summary_files()
                
                if stats:
                    print(f"✅ วิเคราะห์สำเร็จ:")
                    for group_name, group_stats in stats.items():
                        print(f"   📊 {group_name}:")
                        print(f"      รอบทั้งหมด: {group_stats['total_runs']}")
                        print(f"      เวลาเฉลี่ย: {group_stats['avg_time']:.4f} วินาที")
                        print(f"      ไฟล์ต่อรอบ: {group_stats['total_files']}")
                        
                        if group_stats['avg_time'] > 0 and group_stats['total_files'] > 0:
                            efficiency = group_stats['total_files'] / group_stats['avg_time']
                            print(f"      ประสิทธิภาพ: {efficiency:.2f} ไฟล์/วินาที")
                else:
                    print(f"⚠️ ไม่พบข้อมูลสถิติ")
                    
            except Exception as e:
                print(f"❌ เกิดข้อผิดพลาดในการวิเคราะห์: {e}")
                import traceback
                traceback.print_exc()
        else:
            print(f"⚠️ ไม่พบไฟล์ time summary สำหรับการทดสอบ")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def create_sample_time_summary():
    """สร้างไฟล์ time summary ตัวอย่างสำหรับการทดสอบ"""
    
    print(f"\n🏗️ สร้างไฟล์ time summary ตัวอย่าง")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import test_folder, test_groups
        
        # สร้างข้อมูลตัวอย่าง
        sample_data = {
            'M30': [
                {'run': 1, 'time': 120.5, 'files': 2},
                {'run': 2, 'time': 115.3, 'files': 2},
                {'run': 3, 'time': 118.7, 'files': 2},
            ],
            'M60': [
                {'run': 1, 'time': 95.2, 'files': 1},
                {'run': 2, 'time': 98.1, 'files': 1},
                {'run': 3, 'time': 92.8, 'files': 1},
            ]
        }
        
        for group_name, runs in sample_data.items():
            if group_name in test_groups:
                summary_file = os.path.join(test_folder, f"{group_name}_time_summary.txt")
                
                print(f"📝 สร้างไฟล์: {summary_file}")
                
                with open(summary_file, "w", encoding="utf-8") as f:
                    for run_data in runs:
                        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        f.write(f"Run {run_data['run']} | {timestamp}\n")
                        f.write(f"Total time: {run_data['time']:.4f} sec | Files: {run_data['files']} | Rounds: 1\n")
                        f.write(f"Avg time per round: {run_data['time']:.4f} sec\n")
                        f.write("="*50 + "\n")
                    
                    # เพิ่มสรุปรวม
                    total_time = sum(run['time'] for run in runs)
                    avg_time = total_time / len(runs)
                    min_time = min(run['time'] for run in runs)
                    max_time = max(run['time'] for run in runs)
                    
                    f.write(f"\n=== SUMMARY FOR GROUP {group_name} ===\n")
                    f.write(f"Total rounds: {len(runs)}\n")
                    f.write(f"Total files: {runs[0]['files']}\n")
                    f.write(f"Total group time: {total_time:.4f} sec\n")
                    f.write(f"Avg time per round: {avg_time:.4f} sec\n")
                    f.write(f"Min time: {min_time:.4f} sec\n")
                    f.write(f"Max time: {max_time:.4f} sec\n")
                    f.write(f"Avg time per file per round: {avg_time/runs[0]['files']:.4f} sec\n")
                    f.write(f"Completed at: {timestamp}\n")
                    f.write("="*60 + "\n\n")
                
                print(f"   ✅ สร้างเสร็จสิ้น: {len(runs)} รอบ")
        
        print(f"✅ สร้างไฟล์ตัวอย่างเสร็จสิ้น")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการสร้างไฟล์ตัวอย่าง: {e}")

def test_timing_format():
    """ทดสอบรูปแบบการแสดงผลเวลา"""
    
    print(f"\n🕐 ทดสอบรูปแบบการแสดงผลเวลา")
    print("="*50)
    
    # ตัวอย่างเวลาต่างๆ
    test_times = [45.1234, 120.5678, 3600.9876, 7200.1234]
    
    for test_time in test_times:
        print(f"⏱️ {test_time:.4f} วินาที")
        
        # แปลงเป็นนาทีและชั่วโมง
        minutes = test_time / 60
        hours = test_time / 3600
        
        if test_time < 60:
            print(f"   📊 {test_time:.2f} วินาที")
        elif test_time < 3600:
            print(f"   📊 {minutes:.2f} นาที ({test_time:.2f} วินาที)")
        else:
            print(f"   📊 {hours:.2f} ชั่วโมง ({minutes:.2f} นาที)")
        print()

def compare_timing_systems():
    """เปรียบเทียบระบบการจับเวลาเดิมและใหม่"""
    
    print(f"\n🔄 เปรียบเทียบระบบการจับเวลา")
    print("="*60)
    
    print(f"📊 ระบบเดิม:")
    print(f"   ❌ จับเวลาเฉพาะกลุ่ม ไม่ครอบคลุมแต่ละรอบ")
    print(f"   ❌ การบันทึกไฟล์ไม่ถูกต้อง (ใช้ NUM_TRAINING_ROUNDS แทน run number)")
    print(f"   ❌ การคำนวณเวลาเฉลี่ยผิด (หารด้วย rounds แทน total operations)")
    print(f"   ❌ ไม่มีการวิเคราะห์สถิติเชิงลึก")
    
    print(f"\n📈 ระบบใหม่:")
    print(f"   ✅ จับเวลาครอบคลุมทั้งกลุ่มและแต่ละรอบ")
    print(f"   ✅ บันทึกผลลัพธ์แต่ละรอบแยกกัน")
    print(f"   ✅ คำนวณสถิติที่ถูกต้อง (เฉลี่ย, min, max)")
    print(f"   ✅ วิเคราะห์ประสิทธิภาพ (ไฟล์/วินาที)")
    print(f"   ✅ สร้างรายงานสรุปรวม")
    print(f"   ✅ รองรับการวิเคราะห์ย้อนหลัง")
    
    print(f"\n💡 ประโยชน์ของระบบใหม่:")
    print(f"   🎯 ติดตามประสิทธิภาพการเทรนแต่ละรอบ")
    print(f"   📊 เปรียบเทียบเวลาระหว่างกลุ่ม M30 และ M60")
    print(f"   🔍 ตรวจสอบความผิดปกติในเวลาการเทรน")
    print(f"   📈 วิเคราะห์แนวโน้มการปรับปรุงประสิทธิภาพ")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🏗️ Timing System Test")
    print("="*50)
    
    # ทดสอบระบบปัจจุบัน
    simulate_training_process()
    
    # สร้างไฟล์ตัวอย่าง (ถ้าไม่มี)
    create_sample_time_summary()
    
    # ทดสอบการวิเคราะห์อีกครั้งหลังสร้างไฟล์ตัวอย่าง
    simulate_training_process()
    
    # ทดสอบรูปแบบการแสดงผล
    test_timing_format()
    
    # เปรียบเทียบระบบ
    compare_timing_systems()
    
    print(f"\n✅ การทดสอบระบบการจับเวลาเสร็จสิ้น")

if __name__ == "__main__":
    main()

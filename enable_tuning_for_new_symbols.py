#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
เปิด hyperparameter tuning เฉพาะสำหรับ symbols ใหม่ที่ยังไม่มี best_params
"""

import os
import json
import shutil
from pathlib import Path

def check_existing_params():
    """ตรวจสอบ symbols ที่มี best_params แล้ว"""
    print("🔍 ตรวจสอบ symbols ที่มี best_params แล้ว")
    print("="*60)
    
    models_dir = "Test_LightGBM/models"
    existing_symbols = set()
    
    if os.path.exists(models_dir):
        for folder in os.listdir(models_dir):
            folder_path = os.path.join(models_dir, folder)
            if os.path.isdir(folder_path):
                # ตรวจสอบว่ามีไฟล์ best_params หรือไม่
                param_file = os.path.join(folder_path, f"{folder}_best_params.json")
                if os.path.exists(param_file):
                    # แยก symbol จาก folder name (format: 030_SYMBOL)
                    if "_" in folder:
                        timeframe_str, symbol = folder.split("_", 1)
                        existing_symbols.add(symbol)
                        print(f"✅ {symbol} (timeframe {timeframe_str}): มี best_params แล้ว")
    
    print(f"\n📊 สรุป: พบ {len(existing_symbols)} symbols ที่มี best_params แล้ว")
    return existing_symbols

def enable_tuning_for_symbols(target_symbols, timeframes=[30, 60]):
    """เปิด hyperparameter tuning สำหรับ symbols ที่ระบุ"""
    print(f"\n🔧 เปิด hyperparameter tuning สำหรับ symbols: {target_symbols}")
    print("="*60)
    
    models_dir = "Test_LightGBM/models"
    actions_taken = []
    
    for timeframe in timeframes:
        for symbol in target_symbols:
            folder_name = f"{str(timeframe).zfill(3)}_{symbol}"
            folder_path = os.path.join(models_dir, folder_name)
            
            # สร้างโฟลเดอร์ถ้ายังไม่มี
            os.makedirs(folder_path, exist_ok=True)
            
            # ลบ tuning_flag ถ้ามี (เพื่อให้ระบบทำ tuning ใหม่)
            flag_file = os.path.join(folder_path, f"{folder_name}_tuning_flag.json")
            if os.path.exists(flag_file):
                os.remove(flag_file)
                actions_taken.append(f"🗑️ ลบ flag: {flag_file}")
            
            # ลบ best_params ถ้ามี (เพื่อให้ระบบทำ tuning ใหม่)
            param_file = os.path.join(folder_path, f"{folder_name}_best_params.json")
            if os.path.exists(param_file):
                # สำรองไฟล์เก่าก่อนลบ
                backup_file = param_file + ".backup"
                shutil.copy2(param_file, backup_file)
                os.remove(param_file)
                actions_taken.append(f"🗑️ ลบ params (สำรองที่ {backup_file}): {param_file}")
            
            actions_taken.append(f"✅ เปิด tuning สำหรับ: {folder_name}")
    
    print(f"\n📋 การดำเนินการที่ทำ:")
    for action in actions_taken:
        print(f"  {action}")
    
    return len(actions_taken)

def reset_global_tuning_flag():
    """รีเซ็ต global tuning flag ในไฟล์หลัก"""
    print(f"\n🔄 ตรวจสอบ global tuning flag")
    print("="*40)
    
    # ตรวจสอบค่าปัจจุบันในไฟล์
    try:
        with open("python_LightGBM_15_Tuning.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        if "do_hyperparameter_tuning = False" in content:
            print("⚠️ พบ do_hyperparameter_tuning = False ในไฟล์หลัก")
            print("💡 แนะนำให้เปลี่ยนเป็น True หรือใช้ระบบ flag ตาม symbol")
            return False
        elif "do_hyperparameter_tuning = True" in content:
            print("✅ do_hyperparameter_tuning = True อยู่แล้ว")
            return True
        else:
            print("ℹ️ ไม่พบการตั้งค่า global flag (ใช้ระบบ flag ตาม symbol)")
            return True
            
    except Exception as e:
        print(f"❌ ไม่สามารถตรวจสอบไฟล์ได้: {e}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🚀 เปิด Hyperparameter Tuning สำหรับ Symbols ใหม่")
    print("="*80)
    
    # 1. ตรวจสอบ symbols ที่มี best_params แล้ว
    existing_symbols = check_existing_params()
    
    # 2. กำหนด symbols ทั้งหมดที่ต้องการ
    all_symbols = ['AUDUSD', 'EURGBP', 'EURUSD', 'GBPUSD', 'GOLD', 'NZDUSD', 'USDCAD', 'USDJPY']
    
    # 3. หา symbols ใหม่ที่ยังไม่มี best_params
    new_symbols = [symbol for symbol in all_symbols if symbol not in existing_symbols]
    
    print(f"\n🆕 Symbols ใหม่ที่ต้องทำ hyperparameter tuning:")
    print("-"*60)
    if new_symbols:
        for symbol in new_symbols:
            print(f"  📈 {symbol}")
    else:
        print("  ✅ ไม่มี symbols ใหม่ (ทุก symbols มี best_params แล้ว)")
    
    # 4. ถามผู้ใช้ว่าต้องการดำเนินการหรือไม่
    if new_symbols:
        print(f"\n❓ ต้องการเปิด hyperparameter tuning สำหรับ {len(new_symbols)} symbols ใหม่หรือไม่?")
        print("   (จะลบ flag และ best_params ของ symbols เหล่านี้เพื่อให้ทำ tuning ใหม่)")
        
        choice = input("พิมพ์ 'y' เพื่อดำเนินการ หรือ 'n' เพื่อยกเลิก: ").lower().strip()
        
        if choice == 'y':
            # 5. เปิด tuning สำหรับ symbols ใหม่
            actions_count = enable_tuning_for_symbols(new_symbols)
            
            # 6. ตรวจสอบ global flag
            global_flag_ok = reset_global_tuning_flag()
            
            # 7. สรุปผลลัพธ์
            print(f"\n🎯 สรุปการดำเนินการ")
            print("="*60)
            print(f"✅ เปิด tuning สำหรับ {len(new_symbols)} symbols ใหม่")
            print(f"✅ ดำเนินการทั้งหมด {actions_count} รายการ")
            
            if global_flag_ok:
                print("✅ Global tuning flag: พร้อมใช้งาน")
            else:
                print("⚠️ Global tuning flag: ต้องตรวจสอบ")
            
            print(f"\n🚀 ขั้นตอนถัดไป:")
            print("1. รัน: python python_LightGBM_15_Tuning.py")
            print("2. ระบบจะทำ hyperparameter tuning เฉพาะ symbols ใหม่")
            print("3. Symbols เก่าจะใช้ best_params ที่มีอยู่แล้ว")
            
        else:
            print("❌ ยกเลิกการดำเนินการ")
    
    else:
        print(f"\n✅ ทุก symbols มี best_params แล้ว - ไม่ต้องดำเนินการ")
        
        # ตรวจสอบ global flag อยู่ดี
        global_flag_ok = reset_global_tuning_flag()
        
        if not global_flag_ok:
            print(f"\n💡 แนะนำ: เปลี่ยน do_hyperparameter_tuning = True")
            print("   เพื่อให้ระบบใช้ flag ตาม symbol แทน global flag")

if __name__ == "__main__":
    main()

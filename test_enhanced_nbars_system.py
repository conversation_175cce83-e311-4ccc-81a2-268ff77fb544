#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced nBars_SL System Testing Script
ทดสอบระบบหา nBars_SL ที่ปรับปรุงใหม่สำหรับ Multi-Model Architecture

Features:
1. Enhanced Backtest - ทดสอบ nBars_SL ด้วยการ backtest จริง
2. Market Condition Analysis - วิเคราะห์สภาวะตลาด
3. Scenario-specific Logic - ปรับตาม trend_following/counter_trend
4. Multi-method Scoring - รวมผลลัพธ์จากหลายวิธี
5. Comprehensive Reporting - รายงานผลละเอียด
"""

import pandas as pd
import numpy as np
import os
import sys
import pickle
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

# Import functions from main script
try:
    from python_LightGBM_16_Signal import (
        find_optimal_nbars_sl_multi_model,
        analyze_market_conditions_for_nbars,
        find_optimal_nbars_enhanced_backtest,
        find_optimal_nbars_simple,
        find_optimal_nbars_scenario_specific,
        select_best_nbars_with_scoring,
        print_nbars_analysis_summary,
        symbol_info
    )
    print("✅ Successfully imported enhanced nBars_SL functions")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("⚠️ Please make sure python_LightGBM_16_Signal.py is in the current directory")
    sys.exit(1)

def create_sample_data(symbol="EURUSD", num_rows=500):
    """
    สร้างข้อมูลตัวอย่างสำหรับการทดสอบ
    """
    print(f"📊 Creating sample data for {symbol} ({num_rows} rows)")
    
    # สร้างข้อมูล OHLC แบบสุ่มที่มีลักษณะคล้ายข้อมูลจริง
    np.random.seed(42)  # For reproducible results
    
    # Base price
    base_price = 1.1000 if 'EUR' in symbol else 1.2500
    
    # Generate price movements
    returns = np.random.normal(0, 0.001, num_rows)  # Daily returns with 0.1% volatility
    prices = [base_price]
    
    for ret in returns:
        new_price = prices[-1] * (1 + ret)
        prices.append(new_price)
    
    prices = np.array(prices[1:])  # Remove first element
    
    # Create OHLC data
    data = []
    for i in range(num_rows):
        close = prices[i]
        
        # Generate realistic OHLC
        daily_range = abs(np.random.normal(0, 0.0005))  # Daily range
        high = close + daily_range * np.random.uniform(0.3, 1.0)
        low = close - daily_range * np.random.uniform(0.3, 1.0)
        open_price = low + (high - low) * np.random.uniform(0.2, 0.8)
        
        # Ensure OHLC relationships
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        data.append({
            'Date': f"2024-01-{(i % 30) + 1:02d}",
            'Time': f"{(i % 24):02d}:00",
            'Open': round(open_price, 5),
            'High': round(high, 5),
            'Low': round(low, 5),
            'Close': round(close, 5),
            'Volume': np.random.randint(1000, 10000)
        })
    
    df = pd.DataFrame(data)
    
    # Add basic indicators
    df['EMA50'] = df['Close'].ewm(span=50).mean()
    df['EMA200'] = df['Close'].ewm(span=200).mean()
    
    # Add RSI
    delta = df['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['RSI14'] = 100 - (100 / (1 + rs))
    df['RSI14'] = df['RSI14'].fillna(50)
    
    # Add Volume MA
    df['Volume_MA_20'] = df['Volume'].rolling(20).mean()
    
    # Add ATR
    tr1 = df['High'] - df['Low']
    tr2 = abs(df['High'] - df['Close'].shift(1))
    tr3 = abs(df['Low'] - df['Close'].shift(1))
    true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    df['ATR'] = true_range.rolling(14).mean()
    
    # Fill NaN values
    df = df.fillna(method='bfill').fillna(method='ffill')
    
    print(f"✅ Created sample data: {len(df)} rows, {len(df.columns)} columns")
    return df

def test_market_analysis(val_df, symbol):
    """
    ทดสอบการวิเคราะห์ market conditions
    """
    print(f"\n🔍 Testing Market Condition Analysis for {symbol}")
    print("-" * 50)
    
    market_analysis = analyze_market_conditions_for_nbars(val_df, symbol)
    
    print("📊 Market Analysis Results:")
    for key, value in market_analysis.items():
        if isinstance(value, dict):
            print(f"   {key}:")
            for sub_key, sub_value in value.items():
                print(f"      {sub_key}: {sub_value}")
        else:
            print(f"   {key}: {value}")
    
    return market_analysis

def test_individual_methods(val_df, symbol, scenario_name, market_analysis):
    """
    ทดสอบแต่ละวิธีการหา nBars_SL
    """
    print(f"\n🧪 Testing Individual Methods for {scenario_name}")
    print("-" * 50)
    
    # Test Enhanced Backtest
    print("1. Enhanced Backtest:")
    enhanced_result = find_optimal_nbars_enhanced_backtest(
        val_df, symbol, 60, scenario_name, market_analysis
    )
    print(f"   Result: {enhanced_result}")
    
    # Test Statistical Method
    print("\n2. Statistical Method:")
    statistical_result = find_optimal_nbars_simple(val_df, scenario_name)
    print(f"   Result: {statistical_result}")
    
    # Test Scenario-specific Method
    print("\n3. Scenario-specific Method:")
    scenario_result = find_optimal_nbars_scenario_specific(
        val_df, scenario_name, market_analysis
    )
    print(f"   Result: {scenario_result}")
    
    return enhanced_result, statistical_result, scenario_result

def test_selection_logic(enhanced_result, statistical_result, scenario_result, scenario_name, market_analysis):
    """
    ทดสอบ logic การเลือกผลลัพธ์ที่ดีที่สุด
    """
    print(f"\n🏆 Testing Selection Logic for {scenario_name}")
    print("-" * 50)
    
    best_nbars, selection_reason = select_best_nbars_with_scoring(
        enhanced_result, statistical_result, scenario_result, 
        scenario_name, market_analysis
    )
    
    print(f"✅ Selected nBars_SL: {best_nbars}")
    print(f"📝 Reason: {selection_reason}")
    
    return best_nbars, selection_reason

def test_full_system(symbol="EURUSD", timeframe=60):
    """
    ทดสอบระบบทั้งหมด
    """
    print(f"\n🚀 Testing Full Enhanced nBars_SL System")
    print("=" * 80)
    print(f"Symbol: {symbol}, Timeframe: M{timeframe}")
    print("=" * 80)
    
    # สร้างข้อมูลตัวอย่าง
    val_df = create_sample_data(symbol, 300)
    
    # สร้าง mock models dict
    models_dict = {
        'trend_following': {'model': 'mock_trend_model'},
        'counter_trend': {'model': 'mock_counter_model'}
    }
    
    # ทดสอบระบบทั้งหมด
    print(f"\n🔄 Running Enhanced Multi-Model nBars_SL Optimization...")
    
    try:
        optimal_nbars = find_optimal_nbars_sl_multi_model(
            models_dict=models_dict,
            val_df=val_df,
            symbol=symbol,
            timeframe=timeframe
        )
        
        print(f"\n✅ System Test Completed Successfully!")
        print(f"📊 Final Results: {optimal_nbars}")
        
        return optimal_nbars
        
    except Exception as e:
        print(f"❌ System test failed: {e}")
        return None

def run_comprehensive_test():
    """
    รันการทดสอบแบบครอบคลุม
    """
    print("🧪 Enhanced nBars_SL System - Comprehensive Test")
    print("=" * 80)
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Test different symbols and scenarios
    test_cases = [
        {"symbol": "EURUSD", "timeframe": 60},
        {"symbol": "GBPUSD", "timeframe": 30},
        {"symbol": "GOLD", "timeframe": 60}
    ]
    
    all_results = {}
    
    for test_case in test_cases:
        symbol = test_case["symbol"]
        timeframe = test_case["timeframe"]
        
        print(f"\n📋 Testing {symbol} M{timeframe}")
        print("-" * 40)
        
        try:
            result = test_full_system(symbol, timeframe)
            all_results[f"{symbol}_M{timeframe}"] = result
            
        except Exception as e:
            print(f"❌ Test failed for {symbol} M{timeframe}: {e}")
            all_results[f"{symbol}_M{timeframe}"] = None
    
    # สรุปผลการทดสอบ
    print(f"\n📊 Test Summary")
    print("=" * 80)
    
    for test_name, result in all_results.items():
        if result:
            print(f"✅ {test_name}: {result}")
        else:
            print(f"❌ {test_name}: Failed")
    
    print("\n🎉 Comprehensive Test Completed!")
    return all_results

if __name__ == "__main__":
    # รันการทดสอบ
    results = run_comprehensive_test()
    
    print(f"\n📝 Test completed. Results saved in memory.")
    print(f"💡 You can now use the enhanced nBars_SL system in your trading scripts!")

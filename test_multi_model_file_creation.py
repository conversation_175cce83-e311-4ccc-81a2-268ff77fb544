#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการสร้างไฟล์ของ train_all_scenario_models()
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# เพิ่ม path สำหรับ import
sys.path.append('.')

def create_test_data():
    """สร้างข้อมูลทดสอบ"""
    print("🔧 สร้างข้อมูลทดสอบ...")
    
    # สร้างข้อมูลจำลอง
    np.random.seed(42)
    n_samples = 1000
    
    # สร้าง datetime index
    start_date = datetime(2023, 1, 1)
    dates = [start_date + timedelta(hours=i) for i in range(n_samples)]
    
    # สร้าง features
    features = {}
    feature_names = [
        'Close', 'High', 'Low', 'Volume', 'EMA200',
        'RSI_14', 'MACD', 'MACD_Signal', 'ATR_14',
        'BB_Upper', 'BB_Lower', 'Stoch_K', 'Stoch_D'
    ]

    for feature in feature_names:
        if feature == 'Close':
            # สร้างราคาที่มี trend
            features[feature] = 1800 + np.cumsum(np.random.randn(n_samples) * 0.5)
        elif feature == 'High':
            features[feature] = features['Close'] + np.random.uniform(0.5, 2.0, n_samples)
        elif feature == 'Low':
            features[feature] = features['Close'] - np.random.uniform(0.5, 2.0, n_samples)
        elif feature == 'Volume':
            features[feature] = np.random.uniform(1000, 10000, n_samples)
        elif feature == 'EMA200':
            features[feature] = features['Close'] * np.random.uniform(0.98, 1.02, n_samples)
        else:
            features[feature] = np.random.randn(n_samples)
    
    # สร้าง DataFrame
    df = pd.DataFrame(features)
    df['DateTime'] = dates
    
    # สร้าง Target
    df['Target'] = np.random.choice([0, 1], n_samples, p=[0.6, 0.4])
    df['Target_Multiclass'] = np.random.choice([0, 1, 2, 3, 4], n_samples, p=[0.3, 0.2, 0.2, 0.2, 0.1])
    
    print(f"✅ สร้างข้อมูลทดสอบ: {len(df)} samples, {len(feature_names)} features")
    return df

def test_train_all_scenario_models():
    """ทดสอบ train_all_scenario_models()"""
    print("\n🚀 เริ่มทดสอบ train_all_scenario_models()")
    print("="*60)
    
    try:
        # Import ฟังก์ชันที่ต้องการ
        from python_LightGBM_16_Signal import (
            train_all_scenario_models,
            MARKET_SCENARIOS,
            USE_MULTICLASS_TARGET
        )
        
        print(f"✅ Import สำเร็จ")
        print(f"📊 MARKET_SCENARIOS: {list(MARKET_SCENARIOS.keys())}")
        print(f"📊 USE_MULTICLASS_TARGET: {USE_MULTICLASS_TARGET}")
        
        # สร้างข้อมูลทดสอบ
        test_df = create_test_data()
        
        # กำหนดพารามิเตอร์
        symbol = "GOLD"
        timeframe = 60
        target_column = 'Target_Multiclass' if USE_MULTICLASS_TARGET else 'Target'
        output_folder = "Test_LightGBM/results/test_run"
        
        print(f"\n📋 พารามิเตอร์การทดสอบ:")
        print(f"   Symbol: {symbol}")
        print(f"   Timeframe: {timeframe}")
        print(f"   Target Column: {target_column}")
        print(f"   Output Folder: {output_folder}")
        
        # ลบโฟลเดอร์เก่า (ถ้ามี)
        import shutil
        if os.path.exists("Test_LightGBM/results"):
            print(f"🗑️ ลบโฟลเดอร์เก่า...")
            shutil.rmtree("Test_LightGBM/results")
        
        # เรียกใช้ฟังก์ชัน
        print(f"\n🔄 เรียกใช้ train_all_scenario_models()...")
        results = train_all_scenario_models(
            df=test_df,
            symbol=symbol,
            timeframe=timeframe,
            target_column=target_column,
            output_folder=output_folder
        )
        
        print(f"\n✅ train_all_scenario_models() เสร็จสิ้น")
        print(f"📊 ผลลัพธ์: {len(results)} scenarios")
        
        # ตรวจสอบไฟล์ที่สร้าง
        check_created_files(symbol, timeframe)
        
        return results
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_created_files(symbol, timeframe):
    """ตรวจสอบไฟล์ที่ถูกสร้าง"""
    print(f"\n📁 ตรวจสอบไฟล์ที่สร้าง:")
    print("="*50)
    
    # รายการไฟล์ที่ควรถูกสร้าง
    expected_files = [
        # ใน Test_LightGBM/results/
        f"Test_LightGBM/results/{str(timeframe).zfill(3)}_{symbol}_random_forest_feature_importance.csv",
        "Test_LightGBM/results/multi_scenario_performance_analysis.txt",
        "Test_LightGBM/results/multi_scenario_cv_results.json",
        
        # ใน Test_LightGBM/results/plots/
        "Test_LightGBM/results/plots/performance_auc_comparison.png",
        "Test_LightGBM/results/plots/performance_f1_score_comparison.png",
        "Test_LightGBM/results/plots/performance_accuracy_comparison.png",
        "Test_LightGBM/results/plots/performance_combined_comparison.png",
        "Test_LightGBM/results/plots/plots_created.txt",
    ]
    
    # ตรวจสอบไฟล์ในโฟลเดอร์ group
    for scenario in ['trend_following', 'counter_trend']:
        expected_files.extend([
            f"Test_LightGBM/results/{scenario}/final_results.csv",
            f"Test_LightGBM/results/{scenario}/training_results.csv",
            f"Test_LightGBM/results/{scenario}/{str(timeframe).zfill(3)}_{symbol}_feature_importance_comparison.csv",
            f"Test_LightGBM/results/{scenario}/{str(timeframe).zfill(3)}_{symbol}_feature_importance_comparison.png",
            f"Test_LightGBM/results/{scenario}/{str(timeframe).zfill(3)}_{symbol}/{str(timeframe).zfill(3)}_{symbol}_evaluation_report.csv",
            f"Test_LightGBM/results/{scenario}/{str(timeframe).zfill(3)}_{symbol}/{str(timeframe).zfill(3)}_{symbol}_performance_curves.png",
        ])
    
    # ตรวจสอบแต่ละไฟล์
    created_files = []
    missing_files = []
    
    for file_path in expected_files:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            created_files.append((file_path, file_size))
            print(f"✅ {file_path} ({file_size} bytes)")
        else:
            missing_files.append(file_path)
            print(f"❌ {file_path} (ไม่พบ)")
    
    # สรุปผลลัพธ์
    print(f"\n📊 สรุปผลลัพธ์:")
    print(f"   ไฟล์ที่สร้างสำเร็จ: {len(created_files)}/{len(expected_files)}")
    print(f"   ไฟล์ที่ขาดหายไป: {len(missing_files)}")
    
    if missing_files:
        print(f"\n⚠️ ไฟล์ที่ขาดหายไป:")
        for file_path in missing_files:
            print(f"   - {file_path}")
    
    # ตรวจสอบโฟลเดอร์ที่สร้าง
    print(f"\n📂 โฟลเดอร์ที่สร้าง:")
    base_path = "Test_LightGBM/results"
    if os.path.exists(base_path):
        for root, dirs, files in os.walk(base_path):
            level = root.replace(base_path, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                print(f"{subindent}{file} ({file_size} bytes)")

if __name__ == "__main__":
    print("🧪 ทดสอบการสร้างไฟล์ของ Multi-Model Architecture")
    print("="*60)
    
    # ทดสอบ
    results = test_train_all_scenario_models()
    
    if results:
        print(f"\n🎉 การทดสอบเสร็จสิ้น!")
        print(f"📊 ผลลัพธ์: {len(results)} scenarios ถูกเทรนสำเร็จ")
    else:
        print(f"\n💥 การทดสอบล้มเหลว!")
    
    print(f"\n" + "="*60)

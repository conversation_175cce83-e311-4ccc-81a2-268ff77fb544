#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ตัวอย่างการใช้งาน Multi-Model Architecture ใน Production
สำหรับ MT5 WebRequest Server หรือระบบ Trading อื่นๆ

Created: 2025-01-11
Author: AI Assistant
"""

import os
import sys
import pandas as pd
import numpy as np
import joblib
import threading
from datetime import datetime

# Global variables สำหรับ cache โมเดล
loaded_multi_models = {}
model_lock = threading.Lock()

# Configuration
MODEL_BASE_PATH = "LightGBM_Multi/models"
USE_MULTI_MODEL_ARCHITECTURE = True

# Market Scenarios (copy จากไฟล์หลัก)
MARKET_SCENARIOS = {
    'trend_following': {
        'description': 'Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend)',
        'condition': lambda row: (
            (row['Close'] > row['EMA200'] and row['Low'] > row['EMA200']) or
            (row['Close'] < row['EMA200'] and row['High'] < row['EMA200'])
        ),
        'actions': ['buy', 'sell'],
        'type': 'trend_following',
        'strategy': 'momentum'
    },
    'counter_trend': {
        'description': 'Counter Trend Strategy (Sell ใน Uptrend + Buy ใน Downtrend)',
        'condition': lambda row: (
            (row['Close'] > row['EMA200'] and row['Low'] > row['EMA200']) or
            (row['Close'] < row['EMA200'] and row['High'] < row['EMA200'])
        ),
        'actions': ['buy', 'sell'],
        'type': 'counter_trend',
        'strategy': 'mean_reversion'
    }
}

def detect_market_scenario(row):
    """
    ตรวจจับสถานการณ์ตลาดสำหรับการเลือกโมเดลที่เหมาะสม
    """
    close = row['Close']
    high = row['High']
    low = row['Low']
    ema200 = row['EMA200']

    if close > ema200 and low > ema200:
        return 'uptrend'
    elif close < ema200 and high < ema200:
        return 'downtrend'
    else:
        return 'sideways'

def get_applicable_scenarios(market_condition, action_type):
    """
    หา scenarios ที่เหมาะสมตามสถานการณ์ตลาดและประเภทการเทรด
    """
    applicable_scenarios = []

    if market_condition == 'uptrend':
        if action_type == 'buy':
            applicable_scenarios.append('trend_following')
        elif action_type == 'sell':
            applicable_scenarios.append('counter_trend')
    elif market_condition == 'downtrend':
        if action_type == 'buy':
            applicable_scenarios.append('counter_trend')
        elif action_type == 'sell':
            applicable_scenarios.append('trend_following')
    elif market_condition == 'sideways':
        applicable_scenarios.extend(['trend_following', 'counter_trend'])

    return applicable_scenarios

def load_multi_model_components(symbol, timeframe):
    """
    โหลดโมเดลทั้ง 2 scenarios สำหรับ symbol และ timeframe ที่กำหนด
    ใช้ cache เพื่อหลีกเลี่ยงการโหลดซ้ำ
    
    Args:
        symbol: สัญลักษณ์ (เช่น AUDUSD, GOLD, USDJPY)
        timeframe: timeframe (เช่น 60 สำหรับ H1)
    
    Returns:
        dict: โมเดลทั้ง 2 scenarios หรือ None ถ้าโหลดไม่สำเร็จ
    """
    key = (symbol, timeframe)
    
    with model_lock:
        # ตรวจสอบ cache ก่อน
        if key in loaded_multi_models:
            print(f"Loading multi-model components from cache for {symbol} M{timeframe}")
            return loaded_multi_models[key]
        
        print(f"Loading multi-model components for {symbol} M{timeframe}")
        
        models = {}
        
        for scenario_name in MARKET_SCENARIOS.keys():
            scenario_folder = os.path.join(MODEL_BASE_PATH, scenario_name)
            
            model_path = os.path.join(scenario_folder, f"{str(timeframe).zfill(3)}_{symbol}_trained.pkl")
            features_path = os.path.join(scenario_folder, f"{str(timeframe).zfill(3)}_{symbol}_features.pkl")
            scaler_path = os.path.join(scenario_folder, f"{str(timeframe).zfill(3)}_{symbol}_scaler.pkl")
            
            print(f"Attempting to load {scenario_name} model from: {scenario_folder}")
            
            # ตรวจสอบว่าไฟล์มีอยู่
            if not all(os.path.exists(path) for path in [model_path, features_path, scaler_path]):
                missing_files = []
                if not os.path.exists(model_path):
                    missing_files.append("trained.pkl")
                if not os.path.exists(features_path):
                    missing_files.append("features.pkl")
                if not os.path.exists(scaler_path):
                    missing_files.append("scaler.pkl")
                
                print(f"Missing files for {scenario_name}: {', '.join(missing_files)}")
                continue
            
            try:
                # โหลดโมเดลและ components
                model = joblib.load(model_path)
                features_list = joblib.load(features_path)
                scaler = joblib.load(scaler_path)
                
                models[scenario_name] = {
                    'model': model,
                    'features': features_list,
                    'scaler': scaler,
                    'model_path': model_path,
                    'features_path': features_path,
                    'scaler_path': scaler_path
                }
                
                print(f"Successfully loaded {scenario_name} model ({len(features_list)} features)")
                
            except Exception as e:
                print(f"Error loading {scenario_name} model: {e}")
                continue
        
        if len(models) == 0:
            print(f"Failed to load any models for {symbol} M{timeframe}")
            return None
        
        # เก็บใน cache
        loaded_multi_models[key] = models
        
        print(f"Successfully loaded {len(models)}/{len(MARKET_SCENARIOS)} models for {symbol} M{timeframe}")
        return models

def select_appropriate_model(row, action_type, loaded_models):
    """
    เลือกโมเดลที่เหมาะสมตามสถานการณ์ตลาดและประเภทการเทรด
    """
    if not loaded_models:
        return None
    
    # ตรวจจับสถานการณ์ตลาด
    market_condition = detect_market_scenario(row)
    
    # หา scenarios ที่เหมาะสม
    applicable_scenarios = get_applicable_scenarios(market_condition, action_type)
    
    # เลือกโมเดลที่มีอยู่จาก scenarios ที่เหมาะสม
    for scenario in applicable_scenarios:
        if scenario in loaded_models:
            print(f"Selected model: {scenario} (market: {market_condition}, action: {action_type})")
            return loaded_models[scenario]
    
    # ถ้าไม่มีโมเดลที่เหมาะสม ให้เลือกโมเดลใดก็ได้ที่มี
    if loaded_models:
        fallback_scenario = list(loaded_models.keys())[0]
        print(f"Using fallback model: {fallback_scenario}")
        return loaded_models[fallback_scenario]
    
    return None

def predict_with_multi_model(symbol, timeframe, market_data, action_type, confidence_threshold=0.5):
    """
    ทำนายด้วย Multi-Model Architecture
    
    Args:
        symbol: สัญลักษณ์
        timeframe: timeframe
        market_data: dict หรือ pandas Series ที่มีข้อมูลตลาด
        action_type: 'buy' หรือ 'sell'
        confidence_threshold: เกณฑ์ความเชื่อมั่น
    
    Returns:
        dict: ผลการทำนาย
    """
    try:
        # โหลดโมเดล
        loaded_models = load_multi_model_components(symbol, timeframe)
        
        if not loaded_models:
            return {
                'success': False,
                'error': 'Failed to load models',
                'prediction': False,
                'confidence': 0.0,
                'model_used': None
            }
        
        # แปลงข้อมูลเป็น pandas Series ถ้าจำเป็น
        if isinstance(market_data, dict):
            row = pd.Series(market_data)
        else:
            row = market_data
        
        # เลือกโมเดลที่เหมาะสม
        selected_model_info = select_appropriate_model(row, action_type, loaded_models)
        
        if selected_model_info is None:
            return {
                'success': False,
                'error': 'No suitable model found',
                'prediction': False,
                'confidence': 0.0,
                'model_used': None
            }
        
        model = selected_model_info['model']
        features = selected_model_info['features']
        scaler = selected_model_info['scaler']
        
        # เตรียมข้อมูลสำหรับทำนาย
        try:
            X = row[features].values.reshape(1, -1)
        except KeyError as e:
            missing_features = [f for f in features if f not in row.index]
            return {
                'success': False,
                'error': f'Missing features: {missing_features[:5]}...',  # แสดงแค่ 5 features แรก
                'prediction': False,
                'confidence': 0.0,
                'model_used': None
            }
        
        # ใช้ scaler
        X_scaled = scaler.transform(X)
        
        # ทำนาย
        prediction = model.predict(X_scaled)[0]
        probabilities = model.predict_proba(X_scaled)[0]
        
        # คำนวณ confidence ตาม action_type
        if action_type == 'buy':
            # สำหรับ multiclass: ดู probability ของ weak_buy (3) และ strong_buy (4)
            if len(probabilities) > 4:
                confidence = probabilities[3] + probabilities[4]
            else:
                confidence = probabilities[-1] if len(probabilities) > 1 else probabilities[0]
        else:  # sell
            # สำหรับ multiclass: ดู probability ของ weak_sell (1) และ strong_sell (0)
            if len(probabilities) > 1:
                confidence = probabilities[0] + probabilities[1]
            else:
                confidence = probabilities[0]
        
        should_trade = confidence > confidence_threshold
        
        # หาชื่อ scenario ที่ใช้
        model_used = None
        for scenario_name, model_info in loaded_models.items():
            if model_info == selected_model_info:
                model_used = scenario_name
                break
        
        return {
            'success': True,
            'prediction': should_trade,
            'confidence': float(confidence),
            'model_used': model_used,
            'market_condition': detect_market_scenario(row),
            'action_type': action_type,
            'raw_prediction': int(prediction),
            'probabilities': probabilities.tolist()
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'prediction': False,
            'confidence': 0.0,
            'model_used': None
        }

def example_usage():
    """
    ตัวอย่างการใช้งาน
    """
    print("🚀 ตัวอย่างการใช้งาน Multi-Model Architecture ใน Production")
    print("="*70)
    
    # ข้อมูลตัวอย่าง (ในการใช้งานจริงจะมาจาก MT5 หรือ data source อื่น)
    market_data = {
        'Close': 0.6800,
        'High': 0.6810,
        'Low': 0.6790,
        'EMA200': 0.6750,
        # ... features อื่นๆ ที่โมเดลต้องการ
        # ในการใช้งานจริงต้องมี features ครบตามที่โมเดลเทรนไว้
    }
    
    symbol = "AUDUSD"
    timeframe = 60
    
    # ทดสอบการทำนาย Buy
    print(f"\n🔮 ทดสอบการทำนาย BUY สำหรับ {symbol} M{timeframe}")
    result_buy = predict_with_multi_model(symbol, timeframe, market_data, 'buy', 0.6)
    
    print(f"Result: {result_buy}")
    
    # ทดสอบการทำนาย Sell
    print(f"\n🔮 ทดสอบการทำนาย SELL สำหรับ {symbol} M{timeframe}")
    result_sell = predict_with_multi_model(symbol, timeframe, market_data, 'sell', 0.6)
    
    print(f"Result: {result_sell}")

if __name__ == "__main__":
    example_usage()

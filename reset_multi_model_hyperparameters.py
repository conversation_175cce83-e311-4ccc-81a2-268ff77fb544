#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
สคริปต์สำหรับรีเซ็ต hyperparameter files ใน Multi-Model Architecture
เพื่อบังคับให้ทำ hyperparameter tuning ใหม่ด้วย scenario names ที่ถูกต้อง
"""

import os
import shutil
import glob

def reset_multi_model_hyperparameters():
    """ลบไฟล์ hyperparameter เก่าและเตรียมสำหรับ Multi-Model Architecture"""
    
    print("🔄 เริ่มรีเซ็ต Hyperparameter files สำหรับ Multi-Model Architecture")
    print("="*70)
    
    # โฟลเดอร์ที่ต้องตรวจสอบ
    hyper_dirs = [
        "LightGBM_Hyper_Multi",
        "LightGBM_Hyper_Single"
    ]
    
    # ลบไฟล์เก่าที่ไม่มี scenario name
    for hyper_dir in hyper_dirs:
        if os.path.exists(hyper_dir):
            print(f"\n📁 ตรวจสอบโฟลเดอร์: {hyper_dir}")
            
            # หาโฟลเดอร์ย่อยทั้งหมด
            for symbol_folder in os.listdir(hyper_dir):
                symbol_path = os.path.join(hyper_dir, symbol_folder)
                
                if os.path.isdir(symbol_path):
                    print(f"   📂 ตรวจสอบ: {symbol_folder}")
                    
                    # หาไฟล์ที่ไม่มี scenario name
                    old_files = []
                    for file in os.listdir(symbol_path):
                        if file.endswith('.json'):
                            # ตรวจสอบว่าไฟล์ไม่มี scenario name
                            if not ('trend_following' in file or 'counter_trend' in file):
                                old_files.append(os.path.join(symbol_path, file))
                    
                    # ลบไฟล์เก่า
                    for old_file in old_files:
                        try:
                            os.remove(old_file)
                            print(f"      ❌ ลบไฟล์เก่า: {os.path.basename(old_file)}")
                        except Exception as e:
                            print(f"      ⚠️ ไม่สามารถลบไฟล์ {old_file}: {e}")
    
    print(f"\n✅ รีเซ็ต Hyperparameter files เสร็จสิ้น")
    print("📝 ตอนนี้เมื่อรัน training จะทำ hyperparameter tuning ใหม่ด้วย scenario names ที่ถูกต้อง")
    print("   - {timeframe}_{symbol}_trend_following_tuning_flag.json")
    print("   - {timeframe}_{symbol}_trend_following_best_params.json")
    print("   - {timeframe}_{symbol}_counter_trend_tuning_flag.json")
    print("   - {timeframe}_{symbol}_counter_trend_best_params.json")

def check_current_files():
    """ตรวจสอบไฟล์ที่มีอยู่ปัจจุบัน"""
    
    print("\n🔍 ตรวจสอบไฟล์ Hyperparameter ปัจจุบัน:")
    print("="*50)
    
    hyper_dirs = ["LightGBM_Hyper_Multi", "LightGBM_Hyper_Single"]
    
    for hyper_dir in hyper_dirs:
        if os.path.exists(hyper_dir):
            print(f"\n📁 {hyper_dir}:")
            
            for symbol_folder in os.listdir(hyper_dir):
                symbol_path = os.path.join(hyper_dir, symbol_folder)
                
                if os.path.isdir(symbol_path):
                    files = [f for f in os.listdir(symbol_path) if f.endswith('.json')]
                    
                    if files:
                        print(f"   📂 {symbol_folder}:")
                        for file in sorted(files):
                            if 'trend_following' in file or 'counter_trend' in file:
                                print(f"      ✅ {file}")
                            else:
                                print(f"      ❌ {file} (ไม่มี scenario name)")
                    else:
                        print(f"   📂 {symbol_folder}: (ไม่มีไฟล์)")

if __name__ == "__main__":
    print("🏗️ Multi-Model Hyperparameter Reset Tool")
    print("="*50)
    
    # ตรวจสอบไฟล์ปัจจุบัน
    check_current_files()
    
    # ถามผู้ใช้
    response = input("\n❓ ต้องการลบไฟล์เก่าและรีเซ็ต hyperparameters หรือไม่? (y/N): ")
    
    if response.lower() in ['y', 'yes']:
        reset_multi_model_hyperparameters()
        print("\n🔄 ตรวจสอบไฟล์หลังรีเซ็ต:")
        check_current_files()
    else:
        print("❌ ยกเลิกการรีเซ็ต")

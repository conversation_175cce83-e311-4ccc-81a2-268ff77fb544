#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ตัวอย่างการใช้งาน must_have_features.pkl ในการเทรนโมเดลและการใช้งาน

Created: 2025-01-11
Author: AI Assistant
"""

import os
import sys
import pandas as pd
import pickle
import numpy as np
from pathlib import Path

# Configuration
TEST_FOLDER = "LightGBM_Multi"
TIMEFRAME = 60

def load_must_have_features(timeframe, test_folder=TEST_FOLDER):
    """
    โหลด must_have_features สำหรับ timeframe ที่กำหนด
    
    Args:
        timeframe: timeframe ในหน่วยนาที (เช่น 60 สำหรับ H1)
        test_folder: โฟลเดอร์ที่เก็บไฟล์
    
    Returns:
        list: รายชื่อ features ที่จำเป็น
    """
    pickle_path = os.path.join(test_folder, 'feature_importance', 
                              f'{str(timeframe).zfill(3)}_must_have_features.pkl')
    
    print(f"🔍 กำลังโหลด must_have_features จาก: {pickle_path}")
    
    if os.path.exists(pickle_path):
        try:
            with open(pickle_path, 'rb') as f:
                features = pickle.load(f)
            
            print(f"✅ โหลดสำเร็จ: {len(features)} must-have features สำหรับ M{timeframe}")
            print(f"📋 Features: {features}")
            return features
            
        except Exception as e:
            print(f"❌ เกิดข้อผิดพลาดในการโหลดไฟล์: {e}")
            return []
    else:
        print(f"⚠️ ไม่พบไฟล์: {pickle_path}")
        return []

def filter_dataframe_by_must_have_features(df, must_have_features, verbose=True):
    """
    กรอง DataFrame ให้เหลือเฉพาะ must-have features
    
    Args:
        df: pandas DataFrame ที่มี features
        must_have_features: list ของ features ที่จำเป็น
        verbose: แสดงรายละเอียดหรือไม่
    
    Returns:
        pandas DataFrame: DataFrame ที่กรองแล้ว
    """
    if verbose:
        print(f"\n🔍 กำลังกรอง DataFrame ด้วย must-have features")
        print(f"📊 DataFrame เดิม: {df.shape[1]} columns, {df.shape[0]} rows")
        print(f"📋 Must-have features: {len(must_have_features)} features")
    
    # ตรวจสอบว่า features ไหนมีใน DataFrame
    available_features = [f for f in must_have_features if f in df.columns]
    missing_features = [f for f in must_have_features if f not in df.columns]
    
    if verbose:
        print(f"✅ Features ที่พบ: {len(available_features)}")
        print(f"❌ Features ที่ไม่พบ: {len(missing_features)}")
        
        if missing_features:
            print(f"⚠️ Features ที่ขาดหาย: {missing_features}")
        
        if available_features:
            print(f"📋 Features ที่จะใช้: {available_features}")
    
    if not available_features:
        print("❌ ไม่พบ must-have features ใดๆ ใน DataFrame")
        return pd.DataFrame()
    
    # กรอง DataFrame
    filtered_df = df[available_features].copy()
    
    if verbose:
        print(f"✅ DataFrame หลังกรอง: {filtered_df.shape[1]} columns, {filtered_df.shape[0]} rows")
    
    return filtered_df

def check_feature_availability_in_csv(csv_file, must_have_features):
    """
    ตรวจสอบว่าไฟล์ CSV มี must-have features หรือไม่
    
    Args:
        csv_file: path ไปยังไฟล์ CSV
        must_have_features: list ของ features ที่จำเป็น
    
    Returns:
        dict: ผลการตรวจสอบ
    """
    print(f"\n🔍 ตรวจสอบไฟล์: {csv_file}")
    
    if not os.path.exists(csv_file):
        return {
            'file_exists': False,
            'error': 'File not found'
        }
    
    try:
        # อ่านเฉพาะ header
        df_sample = pd.read_csv(csv_file, nrows=0)
        columns = df_sample.columns.tolist()
        
        available_features = [f for f in must_have_features if f in columns]
        missing_features = [f for f in must_have_features if f not in columns]
        
        coverage = len(available_features) / len(must_have_features) * 100 if must_have_features else 0
        
        result = {
            'file_exists': True,
            'total_columns': len(columns),
            'must_have_features': len(must_have_features),
            'available_features': len(available_features),
            'missing_features': len(missing_features),
            'coverage_percent': coverage,
            'available_feature_list': available_features,
            'missing_feature_list': missing_features,
            'usable': len(available_features) > 0
        }
        
        print(f"📊 Total columns: {result['total_columns']}")
        print(f"✅ Available must-have features: {result['available_features']}/{result['must_have_features']} ({coverage:.1f}%)")
        
        if missing_features:
            print(f"❌ Missing features: {missing_features}")
        
        return result
        
    except Exception as e:
        return {
            'file_exists': True,
            'error': str(e)
        }

def demo_training_with_must_have_features():
    """
    สาธิตการใช้ must_have_features ในการเทรนโมเดล
    """
    print("\n🚀 สาธิตการใช้ must_have_features ในการเทรนโมเดล")
    print("="*60)
    
    # 1. โหลด must_have_features
    must_have_features = load_must_have_features(TIMEFRAME)
    
    if not must_have_features:
        print("❌ ไม่สามารถโหลด must_have_features ได้")
        return
    
    # 2. ตรวจสอบไฟล์ CSV ที่มีอยู่
    csv_files = [
        "CSV_Files_Fixed/AUDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/GOLD_H1_FIXED.csv", 
        "CSV_Files_Fixed/USDJPY_H1_FIXED.csv"
    ]
    
    print(f"\n📁 ตรวจสอบไฟล์ CSV ที่มีอยู่:")
    
    usable_files = []
    for csv_file in csv_files:
        result = check_feature_availability_in_csv(csv_file, must_have_features)
        
        if result.get('usable', False):
            usable_files.append({
                'file': csv_file,
                'coverage': result['coverage_percent'],
                'available_features': result['available_features']
            })
    
    print(f"\n📊 สรุป:")
    print(f"   ไฟล์ที่ตรวจสอบ: {len(csv_files)}")
    print(f"   ไฟล์ที่ใช้งานได้: {len(usable_files)}")
    
    if usable_files:
        print(f"\n✅ ไฟล์ที่แนะนำสำหรับการเทรน:")
        for file_info in sorted(usable_files, key=lambda x: x['coverage'], reverse=True):
            print(f"   - {file_info['file']}")
            print(f"     Coverage: {file_info['coverage']:.1f}% ({file_info['available_features']} features)")
    
    # 3. ตัวอย่างการโหลดและกรองข้อมูล
    if usable_files:
        best_file = usable_files[0]['file']
        print(f"\n🔧 ตัวอย่างการโหลดและกรองข้อมูลจาก: {best_file}")
        
        try:
            # โหลดข้อมูลตัวอย่าง (100 rows)
            df_sample = pd.read_csv(best_file, nrows=100)
            print(f"📊 โหลดข้อมูลตัวอย่าง: {df_sample.shape}")
            
            # กรองด้วย must_have_features
            filtered_df = filter_dataframe_by_must_have_features(df_sample, must_have_features)
            
            if not filtered_df.empty:
                print(f"\n📈 ข้อมูลหลังกรอง:")
                print(f"   Shape: {filtered_df.shape}")
                print(f"   Columns: {list(filtered_df.columns)}")
                
                # แสดงสถิติพื้นฐาน
                print(f"\n📊 สถิติพื้นฐาน:")
                print(filtered_df.describe())
                
                # ตรวจสอบ missing values
                missing_counts = filtered_df.isnull().sum()
                if missing_counts.sum() > 0:
                    print(f"\n⚠️ Missing values:")
                    for col, count in missing_counts.items():
                        if count > 0:
                            print(f"   {col}: {count} ({count/len(filtered_df)*100:.1f}%)")
                else:
                    print(f"\n✅ ไม่มี missing values")
            
        except Exception as e:
            print(f"❌ เกิดข้อผิดพลาดในการโหลดข้อมูล: {e}")

def create_must_have_features_summary():
    """
    สร้างสรุปข้อมูล must_have_features
    """
    print("\n📋 สรุปข้อมูล must_have_features")
    print("="*40)
    
    must_have_features = load_must_have_features(TIMEFRAME)
    
    if not must_have_features:
        return
    
    print(f"\n🎯 Must-Have Features สำหรับ M{TIMEFRAME}:")
    print(f"   จำนวน: {len(must_have_features)} features")
    print(f"   รายชื่อ:")
    
    for i, feature in enumerate(must_have_features, 1):
        print(f"     {i}. {feature}")
    
    # วิเคราะห์ประเภทของ features
    feature_types = {
        'Target': [],
        'RSI': [],
        'MACD': [],
        'Volume': [],
        'Price': [],
        'Other': []
    }
    
    for feature in must_have_features:
        if 'Target' in feature:
            feature_types['Target'].append(feature)
        elif 'RSI' in feature:
            feature_types['RSI'].append(feature)
        elif 'MACD' in feature:
            feature_types['MACD'].append(feature)
        elif 'Volume' in feature:
            feature_types['Volume'].append(feature)
        elif any(price_term in feature for price_term in ['Close', 'High', 'Low', 'Open']):
            feature_types['Price'].append(feature)
        else:
            feature_types['Other'].append(feature)
    
    print(f"\n📊 การจำแนกประเภท features:")
    for category, features in feature_types.items():
        if features:
            print(f"   {category}: {len(features)} features")
            for feature in features:
                print(f"     - {feature}")

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🚀 ตัวอย่างการใช้งาน must_have_features.pkl")
    print("="*60)
    
    # 1. สร้างสรุปข้อมูล must_have_features
    create_must_have_features_summary()
    
    # 2. สาธิตการใช้งานในการเทรนโมเดล
    demo_training_with_must_have_features()
    
    print("\n" + "="*60)
    print("✅ การสาธิตเสร็จสิ้น")
    
    print(f"\n📝 สรุปการใช้งาน:")
    print(f"1. โหลด must_have_features ด้วย load_must_have_features()")
    print(f"2. ตรวจสอบความพร้อมใช้งานด้วย check_feature_availability_in_csv()")
    print(f"3. กรองข้อมูลด้วย filter_dataframe_by_must_have_features()")
    print(f"4. ใช้ข้อมูลที่กรองแล้วในการเทรนโมเดล")
    
    print(f"\n⚠️ ข้อควรระวัง:")
    print(f"- ตรวจสอบ missing values ก่อนเทรน")
    print(f"- ตรวจสอบ feature coverage ในแต่ละไฟล์")
    print(f"- อัปเดต must_have_features เมื่อมีข้อมูลใหม่")

if __name__ == "__main__":
    main()

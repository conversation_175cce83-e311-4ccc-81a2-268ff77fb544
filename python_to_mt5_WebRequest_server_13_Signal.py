#+------------------------------------------------------------------+
#|                                     Python Server Endpoint.py    |
#|                      Receives Bar Data from MT5 via HTTP (Flask) |
#+------------------------------------------------------------------+
from flask import Flask, request, jsonify
import json
import time
import datetime
import MetaTrader5 as mt5
import pandas as pd
import threading
import pandas_ta as ta # Make sure pandas_ta is installed
from werkzeug.exceptions import BadRequest # นำเข้า BadRequest เพื่อจับ Error จาก Flask/Werkzeug
import os # Import os for path joining and existence checks
import joblib # Import joblib for loading models and scalers
import numpy as np # Import numpy for numerical operations
import traceback # Import traceback for detailed error logging
import pickle # Import pickle library (สำหรับโหลด features_list ถ้าบันทึกด้วย pickle)
import sys # Import sys
import pytz # Import pytz for timezone handling (recommended for timestamps)
import logging
from logging.handlers import RotatingFileHandler

import requests

Telegram_Open = True

TOKEN = '**********************************************'
CHAT_ID = 6546140292
MESSAGE = "-"

url = f"https://api.telegram.org/bot{TOKEN}/sendMessage"

# กำหนดเงื่อนไขการเทรด
input_rsi_level_in = 35
input_rsi_level_out = 30
input_stop_loss_atr = 2.0
input_take_profit = 1.0
input_pull_back = 0.20

# การตั้งค่า Time Filters
ENABLE_TIME_FILTERS = False  # True = ใช้ time filters, False = เทรดได้ตลอดเวลา
DEFAULT_TIME_FILTERS = {
    'days': [0, 1, 2, 3, 4],  # จันทร์-ศุกร์ (0=จันทร์, 6=อาทิตย์)
    'hours': list(range(6, 22))  # 06:00-21:59
}

# การตั้งค่าเงื่อนไขทางเทคนิค
ENABLE_TECHNICAL_CONDITIONS = True  # True = ใช้เงื่อนไขทางเทคนิค, False = ใช้เฉพาะ Model Prediction

# --- Import necessary components from python_LightGBM.py ---
try:
    # เพิ่ม Path ของโฟลเดอร์ที่เก็บ python_LightGBM.py เข้าไปใน sys.path ชั่วคราว
    # แก้ไข path นี้ให้ชี้ไปยังโฟลเดอร์ที่เก็บไฟล์ python_LightGBM.py ของคุณ
    python_lightgbm_folder = r'D:\test_gold' # *** แก้ Path นี้ ***
    if python_lightgbm_folder not in sys.path:
        sys.path.append(python_lightgbm_folder)
        print(f"Added {python_lightgbm_folder} to sys.path")

    # Import specific functions needed for Multi-Model Architecture
    from python_LightGBM_17_Signal import (
        load_scenario_models, predict_with_scenario_model, detect_market_scenario, 
        get_optimal_parameters, load_scenario_threshold, load_scenario_nbars, load_time_filters,
        USE_MULTI_MODEL_ARCHITECTURE, MARKET_SCENARIOS
        )

    # Define the model confidence threshold
    # model_confidence_threshold = 0.50 # ใช้ค่า Threshold จากโมเดลของคุณ

    # Define the base path for your models - ปรับให้รองรับ Multi-Model Architecture
    if USE_MULTI_MODEL_ARCHITECTURE:
        MODEL_BASE_PATH = r'D:\test_gold\LightGBM_Multi\models' # *** แก้ Path นี้ ***
        THRESHOLD_BASE_PATH = r'D:\test_gold\LightGBM_Multi\thresholds'
        print(f"🔄 Using Multi-Model Architecture")
        print(f"Model base path set to: {MODEL_BASE_PATH}")
        print(f"Threshold base path set to: {THRESHOLD_BASE_PATH}")
        print(f"Available scenarios: {list(MARKET_SCENARIOS.keys())}")
    else:
        MODEL_BASE_PATH = r'D:\test_gold\LightGBM_Single\models' # *** แก้ไขเป็น LightGBM_Single ***
        THRESHOLD_BASE_PATH = r'D:\test_gold\LightGBM_Single\thresholds'
        print(f"📊 Using Single Model Architecture")
        print(f"Model base path set to: {MODEL_BASE_PATH}")
        print(f"Threshold base path set to: {THRESHOLD_BASE_PATH}")

    # กำหนด Timezone ของ MT5 Server (มักจะเป็น UTC)
    MT5_TIMEZONE = pytz.utc # หรือ pytz.timezone('Etc/UTC') หรือ Timezone ของ Server Broker

except ImportError as e:
    print(f"Error: Could not import components from python_LightGBM.py - {e}")
    print("Please ensure python_LightGBM.py is in the specified path or in Python's path.")
    print("Exiting server initialization.")
    exit()
except Exception as e:
    print(f"An unexpected error occurred during import from python_LightGBM.py: {e}")
    traceback.print_exc()
    print("Exiting server initialization.")
    exit()

# --- Configuration ---
HTTP_PORT = 54321
HTTP_HOST = '127.0.0.1'

# --- Add Timeframe Mapping ---
timeframe_map = {
    "PERIOD_M1": mt5.TIMEFRAME_M1,
    "PERIOD_M2": mt5.TIMEFRAME_M2,
    "PERIOD_M3": mt5.TIMEFRAME_M3,
    "PERIOD_M4": mt5.TIMEFRAME_M4,
    "PERIOD_M5": mt5.TIMEFRAME_M5,
    "PERIOD_M6": mt5.TIMEFRAME_M6,
    "PERIOD_M10": mt5.TIMEFRAME_M10,
    "PERIOD_M12": mt5.TIMEFRAME_M12,
    "PERIOD_M15": mt5.TIMEFRAME_M15,
    "PERIOD_M20": mt5.TIMEFRAME_M20,
    "PERIOD_M30": mt5.TIMEFRAME_M30,
    "PERIOD_H1": mt5.TIMEFRAME_H1,
    "PERIOD_H2": mt5.TIMEFRAME_H2,
    "PERIOD_H3": mt5.TIMEFRAME_H3,
    "PERIOD_H4": mt5.TIMEFRAME_H4,
    "PERIOD_H6": mt5.TIMEFRAME_H6,
    "PERIOD_H8": mt5.TIMEFRAME_H8,
    "PERIOD_H12": mt5.TIMEFRAME_H12,
    "PERIOD_D1": mt5.TIMEFRAME_D1,
    "PERIOD_W1": mt5.TIMEFRAME_W1,
    "PERIOD_MN1": mt5.TIMEFRAME_MN1,
}

timeframe_code_map = {
    "PERIOD_M1": 1, "PERIOD_M2": 5, "PERIOD_M3": 15, "PERIOD_M30": 30, "PERIOD_H1": 60, "PERIOD_H4": 240
    # เพิ่ม Timeframe ที่เหลือตาม folder structure ของคุณ
}

# ตั้งค่าข้อมูลคู่เงิน
symbol_info_map = {
    "GOLD":   {"Spread": 25, "Digits": 2, "Points": 0.01, "Swap_Long": 0, "Swap_Short": 0},
    "AUDUSD": {"Spread": 15, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "EURGBP": {"Spread": 22, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "EURUSD": {"Spread": 13, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "GBPUSD": {"Spread": 25, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "NZDUSD": {"Spread": 22, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "USDCAD": {"Spread": 28, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "USDJPY": {"Spread": 16, "Digits": 3, "Points": 0.001, "Swap_Long": 0, "Swap_Short": 0}
}

# --- Global Data Storage ---
market_data_store = {}
data_lock = threading.Lock()

# --- Dictionary to store the latest signal and confidence for each symbol/timeframe ---
# This will be used to send the signal back in the HTTP response.
# Key: (cleaned_symbol, timeframe_int)
# Value: {"signal": str, "confidence": float, "timestamp": datetime}
latest_signals_data = {}
signals_lock = threading.Lock() # Lock for accessing latest_signals_data

# --- Flask App Setup ---
app = Flask(__name__)

# Setup logging
def setup_logging():
    """ตั้งค่า logging system สำหรับ server"""
    # สร้าง logger
    logger = logging.getLogger('trading_server')
    logger.setLevel(logging.INFO)

    # สร้าง formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # File handler สำหรับ log ทั่วไป
    file_handler = RotatingFileHandler(
        'server_trading.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.INFO)

    # File handler สำหรับ signals
    signal_handler = RotatingFileHandler(
        'server_signals.log',
        maxBytes=5*1024*1024,   # 5MB
        backupCount=3,
        encoding='utf-8'
    )
    signal_handler.setFormatter(formatter)
    signal_handler.setLevel(logging.INFO)

    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)

    # เพิ่ม handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    # สร้าง signal logger แยก
    signal_logger = logging.getLogger('trading_signals')
    signal_logger.setLevel(logging.INFO)
    signal_logger.addHandler(signal_handler)
    signal_logger.addHandler(console_handler)

    return logger, signal_logger

# ตั้งค่า logging
server_logger, signal_logger = setup_logging()

# --- Model Loading Cache ---
loaded_models = {}  # สำหรับ Single Model
loaded_scenario_models = {}  # สำหรับ Multi-Model Architecture
model_lock = threading.Lock()

def load_best_entry_condition(symbol, timeframe):
    """
    โหลด entry condition ที่ดีที่สุดจากการทดสอบ
    """
    try:
        # ลองหาในโฟลเดอร์ตาม timeframe ก่อน
        timeframe_folder = f"M{timeframe}" if timeframe in [30, 60] else str(timeframe)
        best_entry_path = f"Test_LightGBM/results/{timeframe_folder}/{str(timeframe).zfill(3)}_{symbol}_best_entry.pkl"

        if not os.path.exists(best_entry_path):
            # ถ้าไม่พบ ลองหาในโฟลเดอร์หลัก
            best_entry_path = f"Test_LightGBM/results/{str(timeframe).zfill(3)}_{symbol}_best_entry.pkl"

        if os.path.exists(best_entry_path):
            with open(best_entry_path, 'rb') as f:
                best_entry_info = pickle.load(f)

            entry_name = best_entry_info.get("entry_name", "entry_v1")
            expectancy = best_entry_info.get("expectancy", 0)
            timestamp = best_entry_info.get("timestamp", "unknown")

            print(f"✅ โหลด best_entry จาก: {best_entry_path}")
            print(f"   Entry: {entry_name}, Expectancy: {expectancy:.2f}, Updated: {timestamp}")

            return entry_name
        else:
            print(f"⚠️ ไม่พบไฟล์ best_entry: {best_entry_path}")
            return "entry_v1"  # fallback

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดขณะโหลด best_entry: {e}")
        return "entry_v1"  # fallback

def get_entry_conditions_functions():
    """
    ส่งคืน dictionary ของฟังก์ชัน entry conditions ต่างๆ
    """
    def default_entry_conditions(latest_features_dict_all_i2, input_rsi_level_in, input_pull_back, input_take_profit):
        # เงื่อนไขทางเทคนิคสำหรับ Buy (default)
        tech_signal_buy = (
            (latest_features_dict_all_i2['Close'] > latest_features_dict_all_i2['Open']) and
            (latest_features_dict_all_i2['MACD_signal'] == 1.0) and
            (latest_features_dict_all_i2['RSI_signal'] > input_rsi_level_in) and
            (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80) and
            (latest_features_dict_all_i2['PullBack_Up'] > input_pull_back) and
            (latest_features_dict_all_i2['Ratio_Buy'] > (input_take_profit * 3.0))
        )

        # เงื่อนไขทางเทคนิคสำหรับ Sell (default)
        tech_signal_sell = (
            (latest_features_dict_all_i2['Close'] < latest_features_dict_all_i2['Open']) and
            (latest_features_dict_all_i2['MACD_signal'] == -1.0) and
            (latest_features_dict_all_i2['RSI_signal'] < -input_rsi_level_in) and
            (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80) and
            (latest_features_dict_all_i2['PullBack_Down'] > input_pull_back) and
            (latest_features_dict_all_i2['Ratio_Sell'] > (input_take_profit * 3.0))
        )

        return tech_signal_buy, tech_signal_sell

    def entry_v1_conditions(latest_features_dict_all_i2, input_rsi_level_in, input_pull_back, input_take_profit):
        # เงื่อนไขทางเทคนิคสำหรับ Buy (entry_v1: เพิ่มเงื่อนไข close > ema50)
        tech_signal_buy = (
            (latest_features_dict_all_i2['Close'] > latest_features_dict_all_i2['Open']) and
            (latest_features_dict_all_i2['Close'] > latest_features_dict_all_i2['EMA50']) and  # entry_v1: เพิ่มเงื่อนไข close > ema50
            (latest_features_dict_all_i2['MACD_signal'] == 1.0) and
            (latest_features_dict_all_i2['RSI_signal'] > input_rsi_level_in) and
            (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80) and
            (latest_features_dict_all_i2['PullBack_Up'] > input_pull_back) and
            (latest_features_dict_all_i2['Ratio_Buy'] > (input_take_profit * 3.0))
        )

        # เงื่อนไขทางเทคนิคสำหรับ Sell (entry_v1: เพิ่มเงื่อนไข close < ema50)
        tech_signal_sell = (
            (latest_features_dict_all_i2['Close'] < latest_features_dict_all_i2['Open']) and
            (latest_features_dict_all_i2['Close'] < latest_features_dict_all_i2['EMA50']) and  # entry_v1: เพิ่มเงื่อนไข close < ema50
            (latest_features_dict_all_i2['MACD_signal'] == -1.0) and
            (latest_features_dict_all_i2['RSI_signal'] < -input_rsi_level_in) and
            (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80) and
            (latest_features_dict_all_i2['PullBack_Down'] > input_pull_back) and
            (latest_features_dict_all_i2['Ratio_Sell'] > (input_take_profit * 3.0))
        )

        return tech_signal_buy, tech_signal_sell

    def entry_v2_conditions(latest_features_dict_all_i2, input_rsi_level_in, input_pull_back, input_take_profit):
        # เงื่อนไขทางเทคนิคสำหรับ Buy (entry_v2: เพิ่มเงื่อนไข RSI > 50)
        tech_signal_buy = (
            (latest_features_dict_all_i2['Close'] > latest_features_dict_all_i2['Open']) and
            (latest_features_dict_all_i2['Close'] > latest_features_dict_all_i2['EMA50']) and
            (latest_features_dict_all_i2['RSI'] > 50) and  # entry_v2: เพิ่มเงื่อนไข RSI > 50
            (latest_features_dict_all_i2['MACD_signal'] == 1.0) and
            (latest_features_dict_all_i2['RSI_signal'] > input_rsi_level_in) and
            (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80) and
            (latest_features_dict_all_i2['PullBack_Up'] > input_pull_back) and
            (latest_features_dict_all_i2['Ratio_Buy'] > (input_take_profit * 3.0))
        )

        # เงื่อนไขทางเทคนิคสำหรับ Sell (entry_v2: เพิ่มเงื่อนไข RSI < 50)
        tech_signal_sell = (
            (latest_features_dict_all_i2['Close'] < latest_features_dict_all_i2['Open']) and
            (latest_features_dict_all_i2['Close'] < latest_features_dict_all_i2['EMA50']) and
            (latest_features_dict_all_i2['RSI'] < 50) and  # entry_v2: เพิ่มเงื่อนไข RSI < 50
            (latest_features_dict_all_i2['MACD_signal'] == -1.0) and
            (latest_features_dict_all_i2['RSI_signal'] < -input_rsi_level_in) and
            (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80) and
            (latest_features_dict_all_i2['PullBack_Down'] > input_pull_back) and
            (latest_features_dict_all_i2['Ratio_Sell'] > (input_take_profit * 3.0))
        )

        return tech_signal_buy, tech_signal_sell

    def entry_v3_conditions(latest_features_dict_all_i2, input_rsi_level_in, input_pull_back, input_take_profit):
        # เงื่อนไขทางเทคนิคสำหรับ Buy (entry_v3: เพิ่มเงื่อนไข MACD > 0)
        tech_signal_buy = (
            (latest_features_dict_all_i2['Close'] > latest_features_dict_all_i2['Open']) and
            (latest_features_dict_all_i2['Close'] > latest_features_dict_all_i2['EMA50']) and
            (latest_features_dict_all_i2['RSI'] > 50) and
            (latest_features_dict_all_i2['MACD'] > 0) and  # entry_v3: เพิ่มเงื่อนไข MACD > 0
            (latest_features_dict_all_i2['MACD_signal'] == 1.0) and
            (latest_features_dict_all_i2['RSI_signal'] > input_rsi_level_in) and
            (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80) and
            (latest_features_dict_all_i2['PullBack_Up'] > input_pull_back) and
            (latest_features_dict_all_i2['Ratio_Buy'] > (input_take_profit * 3.0))
        )

        # เงื่อนไขทางเทคนิคสำหรับ Sell (entry_v3: เพิ่มเงื่อนไข MACD < 0)
        tech_signal_sell = (
            (latest_features_dict_all_i2['Close'] < latest_features_dict_all_i2['Open']) and
            (latest_features_dict_all_i2['Close'] < latest_features_dict_all_i2['EMA50']) and
            (latest_features_dict_all_i2['RSI'] < 50) and
            (latest_features_dict_all_i2['MACD'] < 0) and  # entry_v3: เพิ่มเงื่อนไข MACD < 0
            (latest_features_dict_all_i2['MACD_signal'] == -1.0) and
            (latest_features_dict_all_i2['RSI_signal'] < -input_rsi_level_in) and
            (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80) and
            (latest_features_dict_all_i2['PullBack_Down'] > input_pull_back) and
            (latest_features_dict_all_i2['Ratio_Sell'] > (input_take_profit * 3.0))
        )

        return tech_signal_buy, tech_signal_sell

    return {
        "default": default_entry_conditions,
        "entry_v1": entry_v1_conditions,
        "entry_v2": entry_v2_conditions,
        "entry_v3": entry_v3_conditions
    }

def load_multi_model_components(symbol, timeframe):
    """
    Loads Multi-Model Architecture components (2 scenarios: trend_following, counter_trend)
    Uses cache to avoid reloading.
    """
    key = (symbol, timeframe)
    with model_lock:
        if key in loaded_scenario_models:
            print(f"🔄 Loading Multi-Model components from cache for {symbol} M{timeframe}")
            return loaded_scenario_models[key]

        print(f"📊 Loading Multi-Model components for {symbol} M{timeframe}")

        # ใช้ฟังก์ชันจาก python_LightGBM_17_Signal.py
        scenario_models = load_scenario_models(symbol, timeframe)

        if scenario_models:
            loaded_scenario_models[key] = scenario_models
            print(f"✅ Successfully loaded {len(scenario_models)} scenario models for {symbol} M{timeframe}")
            return scenario_models
        else:
            print(f"❌ Failed to load Multi-Model components for {symbol} M{timeframe}")
            return None

def load_model_components(symbol, timeframe):
    """
    Loads model components - supports both Single Model and Multi-Model Architecture
    """
    if USE_MULTI_MODEL_ARCHITECTURE:
        return load_multi_model_components(symbol, timeframe)
    else:
        # Original single model loading logic (for backward compatibility)
        key = (symbol, timeframe)
        with model_lock:
            if key in loaded_models:
                return loaded_models[key]

            # Import legacy functions if needed
            try:
                from python_LightGBM_17_Signal import load_model, load_scaler
            except ImportError:
                print("❌ Cannot import legacy functions for single model")
                return None, None, None

            model_name = "LightGBM"
            model_dir = os.path.join(MODEL_BASE_PATH, f"{str(timeframe).zfill(3)}_{symbol}")

            # ปรับรูปแบบชื่อไฟล์ให้ตรงกับไฟล์ที่มีอยู่จริง
            model_path = os.path.join(model_dir, f"{str(timeframe).zfill(3)}_{symbol}_trained.pkl")
            scaler_path = os.path.join(model_dir, f"{str(timeframe).zfill(3)}_{symbol}_scaler.pkl")
            features_list_path = os.path.join(model_dir, f"{str(timeframe).zfill(3)}_{symbol}_features.pkl")

            print(f"📊 Loading Single Model from: {model_dir}")
            print(f"   Model: {model_path}")
            print(f"   Scaler: {scaler_path}")
            print(f"   Features: {features_list_path}")

            try:
                # โหลดไฟล์โดยตรงแทนการใช้ legacy functions
                if os.path.exists(model_path) and os.path.exists(scaler_path) and os.path.exists(features_list_path):
                    model = joblib.load(model_path)
                    scaler = joblib.load(scaler_path)

                    with open(features_list_path, 'rb') as f:
                        try:
                            features_list = pickle.load(f)
                        except Exception:
                            f.seek(0)
                            features_list = joblib.load(f)

                    loaded_models[key] = (model, scaler, features_list)
                    print(f"✅ Successfully loaded Single Model components")
                    return model, scaler, features_list
                else:
                    missing_files = []
                    if not os.path.exists(model_path):
                        missing_files.append("trained.pkl")
                    if not os.path.exists(scaler_path):
                        missing_files.append("scaler.pkl")
                    if not os.path.exists(features_list_path):
                        missing_files.append("features.pkl")
                    print(f"❌ Missing Single Model files: {', '.join(missing_files)}")
                    return None, None, None

            except Exception as e:
                print(f"❌ Error loading single model components: {e}")
                return None, None, None

def load_single_model_threshold(symbol, timeframe, default=0.5):
    """โหลด threshold สำหรับ Single Model Architecture"""
    threshold_file = os.path.join(THRESHOLD_BASE_PATH, f"{str(timeframe).zfill(3)}_{symbol}_optimal_threshold.pkl")

    try:
        with open(threshold_file, 'rb') as f:
            threshold = pickle.load(f)
        print(f"✅ โหลด Single Model threshold สำเร็จ: {threshold:.4f}")
        return threshold
    except Exception as e:
        print(f"⚠️ ไม่พบไฟล์ Single Model threshold, ใช้ค่า default: {default} ({e})")
        return default

def load_single_model_nbars(symbol, timeframe, default=6):
    """โหลด nBars_SL สำหรับ Single Model Architecture"""
    nbars_file = os.path.join(THRESHOLD_BASE_PATH, f"{str(timeframe).zfill(3)}_{symbol}_optimal_nBars_SL.pkl")

    try:
        with open(nbars_file, 'rb') as f:
            nbars = pickle.load(f)
        print(f"✅ โหลด Single Model nBars_SL สำเร็จ: {nbars}")
        return nbars
    except Exception as e:
        print(f"⚠️ ไม่พบไฟล์ Single Model nBars_SL, ใช้ค่า default: {default} ({e})")
        return default

def load_single_model_time_filters(symbol, timeframe):
    """โหลด time_filters สำหรับ Single Model Architecture"""
    # ตรวจสอบว่าเปิดใช้ time_filters หรือไม่
    if not ENABLE_TIME_FILTERS:
        print(f"⚠️ Time filters ถูกปิดใช้งาน - เทรดได้ตลอดเวลา")
        return {'days': list(range(7)), 'hours': list(range(24))}

    filters_file = os.path.join(THRESHOLD_BASE_PATH, f"{str(timeframe).zfill(3)}_{symbol}_time_filters.pkl")

    try:
        with open(filters_file, 'rb') as f:
            filters = pickle.load(f)
        print(f"✅ โหลด Single Model time_filters สำเร็จ")
        return filters
    except Exception as e:
        print(f"⚠️ ไม่พบไฟล์ Single Model time_filters, ใช้ค่า default: {e}")
        # ใช้ค่า default ที่กำหนดไว้
        return DEFAULT_TIME_FILTERS.copy()

def prepare_analysis_summary(analysis_results):
    """แปลงผลการวิเคราะห์ให้เป็น JSON serializable"""
    if not analysis_results:
        return {}

    summary = {}

    try:
        for scenario in ['trend_following', 'counter_trend']:
            if scenario in analysis_results:
                summary[scenario] = {}
                for action in ['buy', 'sell']:
                    if action in analysis_results[scenario] and analysis_results[scenario][action]:
                        result = analysis_results[scenario][action]
                        # แปลงค่าทั้งหมดให้เป็น Python native types
                        should_trade = result.get('should_trade', False)
                        confidence = result.get('confidence', 0.0)

                        # แปลงเป็น Python native types
                        if hasattr(should_trade, 'item'):  # numpy types
                            should_trade = should_trade.item()
                        if hasattr(confidence, 'item'):  # numpy types
                            confidence = confidence.item()

                        summary[scenario][action] = {
                            'should_trade': bool(should_trade),
                            'confidence': float(confidence),
                            'model_info': {
                                'scenario': str(scenario),
                                'action': str(action),
                                'available': True
                            }
                        }
                    else:
                        summary[scenario][action] = {
                            'should_trade': False,
                            'confidence': 0.0,
                            'model_info': {
                                'scenario': str(scenario),
                                'action': str(action),
                                'available': False
                            }
                        }

        return summary

    except Exception as e:
        print(f"⚠️ Error preparing analysis summary: {e}")
        import traceback
        traceback.print_exc()
        return {
            'error': str(e),
            'trend_following': {'buy': {'should_trade': False, 'confidence': 0.0}, 'sell': {'should_trade': False, 'confidence': 0.0}},
            'counter_trend': {'buy': {'should_trade': False, 'confidence': 0.0}, 'sell': {'should_trade': False, 'confidence': 0.0}}
        }

# --- Data Processing Function (จะเรียกใช้ใน Thread แยก) ---
def process_data_and_trade(symbol, timeframe):
    """
    Calculates indicators, runs model, determines trade signal and confidence.
    This runs in a separate thread.
    """

    print(f"\n[{datetime.datetime.now()}] 🔄 Starting process_data_and_trade for {symbol} M{timeframe}")

    df = None
    with data_lock:
        key = (symbol, timeframe)
        if key in market_data_store and len(market_data_store[key]) > 0:
            df = market_data_store[key].copy()
            latest_bar_dt = df.index[-1] # Get the timestamp of the latest bar
            print(f"[{datetime.datetime.now()}] 📊 Retrieved {len(df)} bars from data store")
            print(f"[{datetime.datetime.now()}] 🕒 Latest bar time: {latest_bar_dt}")
        else:
            print(f"[{datetime.datetime.now()}] ❌ No sufficient data yet for {symbol} ({timeframe}). Waiting for more bars.")
            return

    # --- แก้ไข: เปลี่ยนชื่อคอลัมน์ให้เป็นตัวพิมพ์ใหญ่เพื่อให้ตรงกับโค้ด Indicator ---
    print(f"[{datetime.datetime.now()}] 🔄 Renaming columns for indicator calculation")
    # คอลัมน์ที่ต้องการเปลี่ยน: 'open', 'high', 'low', 'close', 'volume' ให้เป็น: 'Open', 'High', 'Low', 'Close', 'Volume'
    rename_map = {
        'open': 'Open',
        'high': 'High',
        'low': 'Low',
        'close': 'Close',
        'volume': 'Volume',
        'tick_volume': 'Volume'  # เพิ่มการ map tick_volume เป็น Volume
    }
    # ใช้ .rename() เพื่อเปลี่ยนชื่อคอลัมน์ โดยใช้ errors='ignore' เพื่อไม่ให้ error ถ้าคอลัมน์ไม่มี
    df_ft = df.rename(columns=rename_map, errors='ignore')

    # *** แก้ไขปัญหา duplicate columns ***
    print(f"[{datetime.datetime.now()}] 🔍 Checking for duplicate columns")
    if df_ft.columns.duplicated().any():
        duplicate_cols = df_ft.columns[df_ft.columns.duplicated()].tolist()
        print(f"[{datetime.datetime.now()}] ⚠️ Found duplicate columns: {duplicate_cols}")
        # ลบ duplicate columns โดยเก็บคอลัมน์แรก
        df_ft = df_ft.loc[:, ~df_ft.columns.duplicated()]
        print(f"[{datetime.datetime.now()}] ✅ Removed duplicate columns")

    print(f"[{datetime.datetime.now()}] ✅ Final columns: {list(df_ft.columns)}")

    # ตรวจสอบว่าเปลี่ยนชื่อคอลัมน์หลักสำเร็จหรือไม่
    required_price_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
    missing_cols = [col for col in required_price_cols if col not in df_ft.columns]
    if missing_cols:
        print(f"❌ Error: Missing required price columns after renaming: {missing_cols}")
        # Print คอลัมน์ที่มีอยู่เพื่อ Debug
        print(f"Available columns in df_ft: {df_ft.columns.tolist()}")
        return # หยุดการประมวลผลถ้าคอลัมน์หลักไม่ครบ
    print(f"[{datetime.datetime.now()}] ✅ All required price columns present")
    # --- จบการแก้ไข ---

    # --- ตรวจสอบจำนวนข้อมูลที่เพียงพอสำหรับการคำนวณ Indicator ---
    required_bars = 210 # *** ต้องตรงกับ BARS_FOR_PYTHON ใน MQL5 ***
    if len(df_ft) < required_bars:
        print(f"[{datetime.datetime.now()}] Not enough data ({len(df_ft)} bars) for {symbol} (enum: {timeframe}). Minimum required: {required_bars}. Skipping processing.")
        # *** ถ้าข้อมูลน้อยกว่าที่ต้องการ อาจจะคืนค่า Signal เป็น HOLD/ERROR ***

        signal = "HOLD"
        probability_tp_hit = 0.0
        entry_price = 0.0
        sl_price = 0.0 # Default SL
        tp_price = 0.0 # Default TP

        with signals_lock:
            signals_key = (symbol, timeframe)
            latest_signals_data[signals_key] = {
                "symbol": symbol,
                "timeframe_enum": timeframe,
                "timeframe_str": timeframe_map.get(timeframe, 'UNKNOWN'),
                "signal": signal,
                "confidence": float(probability_tp_hit),
                "timestamp": latest_bar_dt if 'latest_bar_dt' in locals() else datetime.datetime.now(MT5_TIMEZONE),
                "sl_price": sl_price, # เพิ่ม SL เข้าไป
                "tp_price": tp_price  # เพิ่ม TP เข้าไป
            }

        print(f"[{datetime.datetime.now()}] Stored default signal due to insufficient data for {symbol} (enum: {timeframe}).")
        return # หยุดการประมวลผล

    signal = "HOLD" # Default signal
    probability_tp_hit = 0.0 # Default confidence
    entry_price = 0.0
    sl_price = 0.0
    tp_price = 0.0

    # --- โหลดโมเดลและพารามิเตอร์ ---
    if USE_MULTI_MODEL_ARCHITECTURE:
        # โหลด Multi-Model components
        scenario_models = load_model_components(symbol, timeframe)
        time_filters = load_time_filters(symbol, timeframe)

        # สำหรับ Multi-Model จะใช้ default values ก่อน แล้วจะปรับตาม scenario ในภายหลัง
        model_confidence_threshold = 0.5  # Default threshold
        num_nBars_SL = 6  # Default nBars_SL

        if not scenario_models:
            print(f"❌ ไม่สามารถโหลด Multi-Model components สำหรับ {symbol} M{timeframe}")
            return

        print(f"✅ โหลด Multi-Model components สำเร็จ: {len(scenario_models)} scenarios")

    else:
        # โหลด Single Model components (ปรับปรุงใหม่)
        model, scaler, features_list = load_model_components(symbol, timeframe)

        # ใช้ฟังก์ชันใหม่สำหรับ Single Model
        time_filters = load_single_model_time_filters(symbol, timeframe)
        model_confidence_threshold = load_single_model_threshold(symbol, timeframe)
        num_nBars_SL = load_single_model_nbars(symbol, timeframe)

        if model is None or scaler is None or features_list is None:
            print(f"❌ ไม่สามารถโหลด Single Model components สำหรับ {symbol} M{timeframe}")
            return

        print(f"✅ โหลด Single Model components สำเร็จ")
        print(f"   Model: {type(model).__name__}")
        print(f"   Features: {len(features_list)} features")
        print(f"   Threshold: {model_confidence_threshold}")
        print(f"   nBars_SL: {num_nBars_SL}")

    try:
        print(f"[{datetime.datetime.now()}] 🔄 Starting indicators calculation")
        # --- 1. คำนวณ Indicators และสร้าง Features ทั้งหมดที่ใช้ในโมเดล ---
        # คัดลอก Logic จาก load_and_process_data ส่วน "3. สร้าง technical indicators" มาที่นี่
        # ใช้ df_ft ที่เปลี่ยนชื่อคอลัมน์เป็นตัวพิมพ์ใหญ่แล้ว

        # แสดงผล
        # print("\n✅ ข้อมูล : df_ft")
        # print(df_ft.info())
        # print(df_ft.head())
        # print(df_ft.tail())

        # --- เพิ่มฟีเจอร์วันและเวลา ---
        # ใน Server, Bar Time มาเป็น datetime index อยู่แล้ว
        df_ft['DateTime'] = df_ft.index

        # print("\n✅ ตรวจสอบ DateTime")
        # print(df_ft.head())

        # print(f"\n✅ เริ่มคำนวณ Time action")
        df_ft['Entry_DayOfWeek'] = df_ft.index.dayofweek
        df_ft['Entry_Hour'] = df_ft.index.hour

        # --- สร้างฟีเจอร์สำหรับช่วงเวลาที่สำคัญ ---
        df_ft['IsMorning'] = ((df_ft['Entry_Hour'] >= 8) & (df_ft['Entry_Hour'] < 12)).astype(int)
        df_ft['IsAfternoon'] = ((df_ft['Entry_Hour'] >= 12) & (df_ft['Entry_Hour'] < 16)).astype(int)
        df_ft['IsEvening'] = ((df_ft['Entry_Hour'] >= 16) & (df_ft['Entry_Hour'] < 20)).astype(int)
        df_ft['IsNight'] = ((df_ft['Entry_Hour'] >= 20) | (df_ft['Entry_Hour'] < 4)).astype(int) # ตรวจสอบ Logic ช่วงเวลาอีกครั้งให้ถูกต้อง

        df_ft["Bar_CLp"] = df_ft['Close'].shift(1)

        # --- Price action Features ---
        # print(f"✅ เริ่มคำนวณ Price action")
        df_ft["Bar_CL"] = 0.0
        df_ft.loc[df_ft['Close'] > df_ft['Open'], "Bar_CL"] = 1.0
        df_ft.loc[df_ft['Close'] < df_ft['Open'], "Bar_CL"] = -1.0

        df_ft["Bar_CL_OC"] = 0.0
        df_ft.loc[df_ft['Close'] > np.maximum(df_ft['Open'].shift(1), df_ft['Close'].shift(1)), "Bar_CL_OC"] = 1.0
        df_ft.loc[df_ft['Close'] < np.minimum(df_ft['Open'].shift(1), df_ft['Close'].shift(1)), "Bar_CL_OC"] = -1.0

        df_ft["Bar_CL_HL"] = 0.0
        df_ft.loc[(df_ft['Close'] > df_ft['High'].shift(1)) & (df_ft['Close'] > df_ft['Open']), "Bar_CL_HL"] = 1.0
        df_ft.loc[(df_ft['Close'] < df_ft['Low'].shift(1)) & (df_ft['Close'] < df_ft['Open']), "Bar_CL_HL"] = -1.0

        df_ft["Bar_SW"] = 0.0
        df_ft.loc[(df_ft['Close'] > df_ft['Open']) & (df_ft['High'] > df_ft['High'].shift(1)) & (df_ft['Low'] > df_ft['Low'].shift(1)), "Bar_SW"] = 1.0
        df_ft.loc[(df_ft['Close'] < df_ft['Open']) & (df_ft['High'] < df_ft['High'].shift(1)) & (df_ft['Low'] < df_ft['Low'].shift(1)), "Bar_SW"] = -1.0

        df_ft["Bar_TL"] = 0.0
        df_ft.loc[(df_ft['Close'] > df_ft['Open']) & (df_ft['High'] < df_ft['High'].shift(1)) & (df_ft['Low'] < df_ft['Low'].shift(1)), "Bar_TL"] = 1.0
        df_ft.loc[(df_ft['Close'] < df_ft['Open']) & (df_ft['High'] > df_ft['High'].shift(1)) & (df_ft['Low'] > df_ft['Low'].shift(1)), "Bar_TL"] = 1.0

        df_ft["Bar_DTB"] = 0.0
        df_ft.loc[(df_ft['Close'] > df_ft['Open']) & (df_ft['Low'] == df_ft['Low'].shift(1)), "Bar_DTB"] = 1.0
        df_ft.loc[(df_ft['Close'] < df_ft['Open']) & (df_ft['High'] == df_ft['High'].shift(1)), "Bar_DTB"] = -1.0

        df_ft["Bar_OSB"] = 0.0
        df_ft.loc[(df_ft['Close'] > df_ft['Open']) & (df_ft['High'] > df_ft['High'].shift(1)) & (df_ft['Low'] < df_ft['Low'].shift(1)), "Bar_OSB"] = 1.0
        df_ft.loc[(df_ft['Close'] < df_ft['Open']) & (df_ft['High'] > df_ft['High'].shift(1)) & (df_ft['Low'] < df_ft['Low'].shift(1)), "Bar_OSB"] = -1.0

        df_ft["Bar_FVG"] = 0.0
        df_ft.loc[(df_ft["Low"] > df_ft["High"].shift(2)) & (df_ft["Close"] > df_ft["Open"]), "Bar_FVG"] = 1.0
        df_ft.loc[(df_ft["High"] < df_ft["Low"].shift(2)) & (df_ft["Close"] < df_ft["Open"]), "Bar_FVG"] = -1.0

        # print(f"✅ เริ่มคำนวณ Pin Bar")
        df_ft["Bar_longwick"] = 0.0
        epsilon = 1e-9
        lower_wick = (np.minimum(df_ft['Open'], df_ft['Close']) - df_ft['Low']).replace(0, epsilon)
        upper_wick = (df_ft['High'] - np.maximum(df_ft['Open'], df_ft['Close'])).replace(0, epsilon)
        pinbar_up = lower_wick / (df_ft['High'] - np.minimum(df_ft['Open'], df_ft['Close']))
        pinbar_down = upper_wick / (np.maximum(df_ft['Open'], df_ft['Close']) - df_ft['Low'])
        df_ft.loc[pinbar_up > pinbar_down, "Bar_longwick"] = pinbar_up
        df_ft.loc[pinbar_down > pinbar_up, "Bar_longwick"] = -1 * pinbar_down
        
        # print(f"✅ เริ่มคำนวณ Range Bar")
        df_ft['Price_Range'] = df_ft["High"] - df_ft["Low"]
        df_ft['Price_Move'] = df_ft["Close"] - df_ft["Open"]

        # print(f"✅ เริ่มคำนวณ Price Strangth")
        df_ft['Price_Strangth'] = 0.0
        body_size_oc = np.abs(df_ft["Close"] - df_ft["Open"])
        body_size_ocp = np.abs(df_ft["Close"].shift(1) - df_ft["Open"].shift(1))
        body_size_hl = np.abs(df_ft["High"] - df_ft["Low"])
        body_size_hlp = np.abs(df_ft["High"].shift(1) - df_ft["Low"].shift(1))
        
        df_ft.loc[(body_size_oc < body_size_ocp) & (body_size_hl < body_size_hlp) & (df_ft["Close"] < df_ft["Open"]), "Price_Strangth"] = 1
        df_ft.loc[(body_size_oc < body_size_ocp) & (body_size_hl < body_size_hlp) & (df_ft["Close"] > df_ft["Open"]), "Price_Strangth"] = -1

        # --- Volume_MA20, Volume_Spike ---
        print(f"[{datetime.datetime.now()}] 🔄 Calculating Volume indicators")
        try:
            # ตรวจสอบว่า Volume column มี duplicate หรือไม่
            if 'Volume' in df_ft.columns:
                # ถ้ามี duplicate columns ให้เลือกคอลัมน์แรก
                if df_ft.columns.duplicated().any() and df_ft.columns.tolist().count('Volume') > 1:
                    print(f"[{datetime.datetime.now()}] ⚠️ Found duplicate Volume columns, using first one")
                    volume_series = df_ft['Volume'].iloc[:, 0] if hasattr(df_ft['Volume'], 'iloc') else df_ft['Volume']
                else:
                    volume_series = df_ft['Volume']

                df_ft['Volume_MA20'] = volume_series.rolling(20, min_periods=1).mean()
                df_ft['Volume_MA20'] = df_ft['Volume_MA20'].fillna(volume_series.mean())
                df_ft['Volume_Spike'] = volume_series / (df_ft['Volume_MA20'] + 1e-10)
                print(f"[{datetime.datetime.now()}] ✅ Volume indicators calculated")
            else:
                print(f"[{datetime.datetime.now()}] ❌ Volume column not found")
                df_ft['Volume_MA20'] = 1000.0  # default value
                df_ft['Volume_Spike'] = 1.0    # default value
        except Exception as e:
            print(f"[{datetime.datetime.now()}] ❌ Error calculating Volume indicators: {e}")
            df_ft['Volume_MA20'] = 1000.0  # default value
            df_ft['Volume_Spike'] = 1.0    # default value

        # --- EMA Calculation ---
        # print(f"✅ เริ่มคำนวณ EMA")
        df_ft['EMA50'] = df_ft['Close'].ewm(span=50, min_periods=1).mean()
        df_ft['EMA100'] = df_ft['Close'].ewm(span=100, min_periods=1).mean()
        df_ft['EMA200'] = df_ft['Close'].ewm(span=200, min_periods=1).mean()

        # --- EMA Related Features ---
        df_ft['EMA_diff'] = (df_ft['EMA50'] - df_ft['EMA200'])

        df_ft['MA_Cross'] = 0.0
        df_ft.loc[(df_ft['EMA50'] > df_ft['EMA200']), "MA_Cross"] = 1.0
        df_ft.loc[(df_ft['EMA50'] < df_ft['EMA200']), "MA_Cross"] = -1.0

        df_ft["Price_above_EMA50"] = 0.0
        df_ft.loc[(df_ft["Close"] > df_ft["EMA50"]), "Price_above_EMA50"] = 1.0
        df_ft.loc[(df_ft["Close"] < df_ft["EMA50"]), "Price_above_EMA50"] = -1.0

        # --- ความผันผวนระยะสั้น ---
        df_ft['Rolling_Vol_5'] = df_ft['Close'].pct_change().rolling(5, min_periods=1).std()
        df_ft['Rolling_Vol_15'] = df_ft['Close'].pct_change().rolling(15, min_periods=1).std()

        # --- ระยะทางจาก EMA ---
        df_ft['Dist_EMA50'] = (df_ft['Close'] - df_ft['EMA50']) / (df_ft['EMA50'] + 1e-10) # ป้องกันหารด้วย 0
        df_ft['Dist_EMA100'] = (df_ft['Close'] - df_ft['EMA100']) / (df_ft['EMA100'] + 1e-10) # ป้องกันหารด้วย 0
        df_ft['Dist_EMA200'] = (df_ft['Close'] - df_ft['EMA200']) / (df_ft['EMA200'] + 1e-10) # ป้องกันหารด้วย 0

        # --- RSI Calculation ---
        # print(f"✅ เริ่มคำนวณ RSI df")
        window_rsi = 14
        delta = df_ft["Close"].diff(1)
        gain = pd.Series(np.where(delta > 0, delta, 0), index=df_ft.index)
        loss = pd.Series(np.where(delta < 0, -delta, 0), index=df_ft.index)

        avg_gain = gain.ewm(span=window_rsi, adjust=False).mean() # มักใช้ EWM สำหรับ RSI Calculation
        avg_loss = loss.ewm(span=window_rsi, adjust=False).mean() # มักใช้ EWM สำหรับ RSI Calculation
        # Handle division by zero explicitly for rs calculation
        rs = avg_gain / avg_loss
        # Replace inf values with NaN to handle division by zero if avg_loss is 0
        rs = rs.replace([np.inf, -np.inf], np.nan)
        # Replace NaN in rs resulting from division by zero or initial periods
        rs = rs.fillna(0) # Or another appropriate value if needed
        # ปรับการคำนวณ RSI14 ให้ใช้ EWM และจัดการค่า NaN/inf
        df_ft["RSI14"] = 100 - (100 / (1 + rs))

        # df_ft["RSI14"].iloc[:window_rsi-1] = np.nan
        df_ft.loc[df_ft.index[:window_rsi-1], "RSI14"] = np.nan

        # --- Indicator Calculation (MACD, RSI, Stochastic, BB, ADX, ATR, SR) ---
        # คำนวณ Indicators หลักก่อน
        print(f"[{datetime.datetime.now()}] 🔄 Calculating technical indicators (MACD, Stochastic, ADX)")
        try:
            macd = ta.macd(df_ft["Close"])
            print(f"[{datetime.datetime.now()}] ✅ MACD calculated")
        except Exception as e:
            print(f"[{datetime.datetime.now()}] ❌ Error calculating MACD: {e}")
            macd = pd.DataFrame()

        try:
            stoch = ta.stoch(high=df_ft["High"], low=df_ft["Low"], close=df_ft["Close"])
            print(f"[{datetime.datetime.now()}] ✅ Stochastic calculated")
        except Exception as e:
            print(f"[{datetime.datetime.now()}] ❌ Error calculating Stochastic: {e}")
            stoch = pd.DataFrame()

        try:
            adx = ta.adx(high=df_ft["High"], low=df_ft["Low"], close=df_ft["Close"])
            print(f"[{datetime.datetime.now()}] ✅ ADX calculated")
        except Exception as e:
            print(f"[{datetime.datetime.now()}] ❌ Error calculating ADX: {e}")
            adx = pd.DataFrame()

        window_bb = 20
        rolling_mean_bb = df_ft["Close"].rolling(window=window_bb, min_periods=1).mean()
        rolling_std_bb = df_ft["Close"].rolling(window=window_bb, min_periods=1).std()
        bb_upper = (rolling_mean_bb + (rolling_std_bb * 2))
        bb_lower = (rolling_mean_bb - (rolling_std_bb * 2))
        bb_width = bb_upper - bb_lower

        window_atr = 14
        if all(col in df_ft.columns for col in ['High', 'Low', 'Close']):
            tr1 = df_ft['High'] - df_ft['Low']
            if len(df_ft) > 1:
                tr2 = (df_ft['High'] - df_ft['Close'].shift()).abs()
                tr3 = (df_ft['Low'] - df_ft['Close'].shift()).abs()
                true_range_df = pd.concat([tr1, tr2, tr3], axis=1) # สามารถ concat ตรงนี้ได้
                true_range = true_range_df.max(axis=1)
            else:
                true_range = pd.Series(np.nan, index=df_ft.index)

            atr = true_range.rolling(window_atr, min_periods=1).mean()
        else:
            atr = pd.Series(np.nan, index=df_ft.index) # สร้าง Series ว่างถ้าคอลัมน์ไม่ครบ

        lookback_sr = 50
        if all(col in df_ft.columns for col in ['Low', 'High']):
            support = df_ft['Low'].rolling(lookback_sr, min_periods=1).min()
            resistance = df_ft['High'].rolling(lookback_sr, min_periods=1).max()
        else:
            support = pd.Series(np.nan, index=df_ft.index)
            resistance = pd.Series(np.nan, index=df_ft.index)

        # print("\n✅ ข้อมูล df_ft ก่อนทำ indicator_features")
        # print(df_ft.info())
        # print(df_ft.head())

        # print("\n✅ ตรวจอบข้อมูล จากราคาปิดก่อนหน้า 1 แท่ง")
        # print(df_ft[["Open","High","Low","Close","Volume","Bar_CL","Bar_CLp"]].head())
        # print(df_ft[["Open","High","Low","Close","Volume","Bar_CL","Bar_CLp"]].tail())

        # --- สร้าง Features ที่ใช้ Indicators ---
        # สร้าง DataFrame เปล่าสำหรับเก็บ Features ที่คำนวณจาก Indicators
        indicator_features = pd.DataFrame(index=df_ft.index)

        # print(f"✅ เริ่มคำนวณ MACD Features")
        macd_line_col = 'MACD_12_26_9'
        macd_signal_col = 'MACDs_12_26_9'
        macd_hist_col = 'MACDh_12_26_9'

        # เพิ่ม MACD columns ลงใน df_ft เพื่อให้สอดคล้องกับ training model
        df_ft["MACD_12_26_9"] = macd[macd_line_col] if macd_line_col in macd.columns else np.nan
        df_ft["MACDs_12_26_9"] = macd[macd_signal_col] if macd_signal_col in macd.columns else np.nan
        df_ft["MACDh_12_26_9"] = macd[macd_hist_col] if macd_hist_col in macd.columns else (df_ft["MACD_12_26_9"] - df_ft["MACDs_12_26_9"])

        # MACD Features
        indicator_features["MACD_12_26_9"] = df_ft["MACD_12_26_9"]
        indicator_features["MACDs_12_26_9"] = df_ft["MACDs_12_26_9"]
        indicator_features["MACDh_12_26_9"] = df_ft["MACDh_12_26_9"]

        if macd_line_col in macd.columns:
            indicator_features["MACD_line"] = (macd[macd_line_col] > 0.0).astype(int) - (macd[macd_line_col] < 0.0).astype(int) # Convert to 1, 0, -1
        if macd_line_col in macd.columns and not macd[macd_line_col].shift(1).isnull().all():
            indicator_features["MACD_deep"] = (macd[macd_line_col] > macd[macd_line_col].shift(1)).astype(int) - (macd[macd_line_col] < macd[macd_line_col].shift(1)).astype(int)
        if macd_line_col in macd.columns and macd_signal_col in macd.columns:
            indicator_features["MACD_signal"] = (macd[macd_line_col] > macd[macd_signal_col]).astype(int) - (macd[macd_line_col] < macd[macd_signal_col]).astype(int)

        # print(f"✅ เริ่มคำนวณ RSI Features")
        if 'RSI14' in df_ft.columns: # RSI ถูกคำนวณไว้ใน df_ft ตรงๆ ก่อนหน้านี้
            indicator_features["RSI_signal"] = np.select(
                [df_ft["RSI14"] < 30, df_ft["RSI14"] > 70],
                [-1, 1],
                default=0
            )

            # RSI_Overbought / Oversold
            # การใช้ np.select แบบนี้สำหรับเงื่อนไขเดียวอาจทำให้สับสน
            # indicator_features['RSI_Overbought'] = np.select((df_ft['RSI14'] > 70).astype(int), 1, default = 0) # <-- โค้ดเดิม
            # indicator_features['RSI_Oversold'] = np.select((df_ft['RSI14'] < 30).astype(int), 1, default = 0) # <-- โค้ดเดิม
            # แก้ไขเป็นวิธีที่ชัดเจนกว่า
            indicator_features['RSI_Overbought'] = (df_ft['RSI14'] > 70).astype(int)
            indicator_features['RSI_Oversold'] = (df_ft['RSI14'] < 30).astype(int)

            # RSI_ROC
            divisor = df_ft['RSI14'] + 1e-10
            indicator_features['RSI_ROC_i2'] = (df_ft['RSI14'] - df_ft['RSI14'].shift(2)) / divisor
            indicator_features['RSI_ROC_i4'] = (df_ft['RSI14'] - df_ft['RSI14'].shift(4)) / divisor
            indicator_features['RSI_ROC_i6'] = (df_ft['RSI14'] - df_ft['RSI14'].shift(6)) / divisor
            indicator_features['RSI_ROC_i8'] = (df_ft['RSI14'] - df_ft['RSI14'].shift(8)) / divisor

            # RSI_Divergence
            # เงื่อนไข: ตรวจสอบว่า Close.shift(X) และ RSI14.shift(X) ไม่เป็น NaN ก่อนคำนวณ Divergence
            # แก้ไข: ชื่อคอลัมน์ df_ft['RSI'] เป็น df_ft['RSI14']
            # แก้ไข: ใน else block ให้กำหนดค่าเริ่มต้น (เช่น 0 หรือ np.nan)

            close_shift_2 = df_ft['Close'].shift(2)
            rsi14_shift_2 = df_ft['RSI14'].shift(2)
            # ใช้ np.isnan().any() เพื่อตรวจสอบ NaN ใน Series ที่ได้จากการ shift (แม้ว่า .isnull().all() ก็ใช้ได้ แต่ .any() อาจชัดเจนกว่าในบริบทนี้)
            if not (pd.isna(close_shift_2).any() or pd.isna(rsi14_shift_2).any()): # ตรวจสอบว่าไม่มี NaN ในแถวที่จะเปรียบเทียบ
                indicator_features['RSI_Divergence_i2'] = np.where(
                    (df_ft['Close'] > close_shift_2) & (df_ft['RSI14'] < rsi14_shift_2), # แก้ไข: ใช้ rsi14_shift_2
                        1, np.where(
                            (df_ft['Close'] < close_shift_2) & (df_ft['RSI14'] > rsi14_shift_2), # แก้ไข: ใช้ rsi14_shift_2
                            -1, 0
                    )
                )
            else:
                indicator_features['RSI_Divergence_i2'] = 0 # หรือ np.nan ขึ้นอยู่กับความต้องการ

            close_shift_4 = df_ft['Close'].shift(4)
            rsi14_shift_4 = df_ft['RSI14'].shift(4)
            if not (pd.isna(close_shift_4).any() or pd.isna(rsi14_shift_4).any()):
                indicator_features['RSI_Divergence_i4'] = np.where(
                    (df_ft['Close'] > close_shift_4) & (df_ft['RSI14'] < rsi14_shift_4),
                        1, np.where(
                            (df_ft['Close'] < close_shift_4) & (df_ft['RSI14'] > rsi14_shift_4),
                            -1, 0
                    )
                )
            else:
                indicator_features['RSI_Divergence_i4'] = 0

            close_shift_6 = df_ft['Close'].shift(6)
            rsi14_shift_6 = df_ft['RSI14'].shift(6)
            if not (pd.isna(close_shift_6).any() or pd.isna(rsi14_shift_6).any()):
                indicator_features['RSI_Divergence_i4'] = np.where(
                    (df_ft['Close'] > close_shift_6) & (df_ft['RSI14'] < rsi14_shift_6),
                        1, np.where(
                            (df_ft['Close'] < close_shift_6) & (df_ft['RSI14'] > rsi14_shift_6),
                            -1, 0
                    )
                )
            else:
                indicator_features['RSI_Divergence_i4'] = 0

        # Stochastic Features
        # print(f"✅ เริ่มคำนวณ STO")
        stoch_k_col = 'STOCHk_14_3_3'
        stoch_d_col = 'STOCHd_14_3_3'

        # เพิ่ม Stochastic columns ลงใน df_ft เพื่อให้สอดคล้องกับ training model
        df_ft["STOCHk_14_3_3"] = stoch[stoch_k_col] if stoch_k_col in stoch.columns else np.nan
        df_ft["STOCHd_14_3_3"] = stoch[stoch_d_col] if stoch_d_col in stoch.columns else np.nan

        if stoch_k_col in stoch.columns and stoch_d_col in stoch.columns:
            indicator_features["STO_cross"] = (stoch[stoch_k_col] > stoch[stoch_d_col]).astype(int) - (stoch[stoch_k_col] < stoch[stoch_d_col]).astype(int)
            indicator_features["STO_zone"] = (stoch[stoch_k_col] > 50).astype(int) - (stoch[stoch_k_col] < 50).astype(int)
            indicator_features["STO_overbought"] = (stoch[stoch_k_col] > 80).astype(int)
            indicator_features["STO_Oversold"] = (stoch[stoch_k_col] < 20).astype(int)

        # ADX Features
        # print(f"✅ เริ่มคำนวณ ADX")
        adx_col = 'ADX_14'
        dmp_col = 'DMP_14'
        dmn_col = 'DMN_14'

        # เพิ่ม ADX columns ลงใน df_ft เพื่อให้สอดคล้องกับ training model
        df_ft["ADX_14"] = adx[adx_col] if adx_col in adx.columns else np.nan
        df_ft["DMP_14"] = adx[dmp_col] if dmp_col in adx.columns else np.nan
        df_ft["DMN_14"] = adx[dmn_col] if dmn_col in adx.columns else np.nan

        indicator_features["ADX_14"] = df_ft["ADX_14"]
        indicator_features["DMP_14"] = df_ft["DMP_14"]
        indicator_features["DMN_14"] = df_ft["DMN_14"]

        indicator_features["ADX_Deep"] = (indicator_features["ADX_14"] > indicator_features["ADX_14"].shift(1)).astype(int) - (indicator_features["ADX_14"] < indicator_features["ADX_14"].shift(1)).astype(int)

        indicator_features["ADX_zone_25"] = (indicator_features["ADX_14"] > 25).astype(int)
        indicator_features["ADX_zone_15"] = (indicator_features["ADX_14"] > 15).astype(int)

        if dmp_col in adx.columns and dmn_col in adx.columns:
            indicator_features["ADX_cross"] = (adx[dmp_col] > adx[dmn_col]).astype(int) - (adx[dmp_col] < adx[dmn_col]).astype(int)

        # ATR Feature
        # print(f"✅ เริ่มคำนวณ ATR")
        indicator_features['ATR'] = atr # ATR Series ที่คำนวณไว้ก่อนหน้า

        indicator_features['ATR_Deep'] = (indicator_features["ATR"] > indicator_features["ATR"].shift(1)).astype(int) - (indicator_features["ATR"] < indicator_features["ATR"].shift(1)).astype(int)

        indicator_features['ATR_ROC_i2'] = (indicator_features['ATR'] - indicator_features['ATR'].shift(2)) / (indicator_features['ATR'] + 1e-10)
        indicator_features['ATR_ROC_i4'] = (indicator_features['ATR'] - indicator_features['ATR'].shift(4)) / (indicator_features['ATR'] + 1e-10)
        indicator_features['ATR_ROC_i6'] = (indicator_features['ATR'] - indicator_features['ATR'].shift(6)) / (indicator_features['ATR'] + 1e-10)
        indicator_features['ATR_ROC_i8'] = (indicator_features['ATR'] - indicator_features['ATR'].shift(8)) / (indicator_features['ATR'] + 1e-10)

        # BB Width Feature
        # print(f"✅ เริ่มคำนวณ BB")
        # เพิ่ม Bollinger Bands columns ลงใน df_ft เพื่อให้สอดคล้องกับ training model
        df_ft['Upper_BB'] = bb_upper
        df_ft['Lower_BB'] = bb_lower
        df_ft['BB_width'] = bb_width

        indicator_features['BB_width'] = bb_width # BB_width Series ที่คำนวณไว้ก่อนหน้า

        # SR Features
        # print(f"✅ เริ่มคำนวณ S and R")
        indicator_features['Support'] = support # 'Low'
        indicator_features['Resistance'] = resistance # 'High'

        indicator_features['PullBack_Up'] = (indicator_features['Resistance'] - df_ft['Close']) / (indicator_features['Resistance'] - indicator_features['Support'] + 1e-10)
        indicator_features['PullBack_Down'] = (df_ft['Close'] - indicator_features['Support']) / (indicator_features['Resistance'] - indicator_features['Support'] + 1e-10)

        df_ft['Low_Prev_Min'] = df_ft['Low'].shift(1).rolling(window=num_nBars_SL).min()
        df_ft['High_Prev_Max'] = df_ft['High'].shift(1).rolling(window=num_nBars_SL).max()

        epsilon = 1e-9
        Points = symbol_info_map[symbol]["Points"] if symbol in symbol_info_map else 0.00001

        df_ft["SL_Buy"] = np.minimum(
            df_ft["Open"] - 100 * Points,
            np.maximum(df_ft["Low_Prev_Min"], indicator_features['Support'] if 'Support' in indicator_features else df_ft['Low_Prev_Min'])
        ) + epsilon

        df_ft["SL_Sell"] = np.maximum(
            df_ft["Open"] + 100 * Points,
            np.minimum(df_ft["High_Prev_Max"], indicator_features['Resistance'] if 'Resistance' in indicator_features else df_ft['High_Prev_Max'])
        ) + epsilon

        df_ft["Ratio_Buy"] = (indicator_features['Resistance'] - df_ft["Open"]) / (df_ft["Open"] - df_ft["SL_Buy"])
        df_ft["Ratio_Sell"] = (df_ft["Open"] - indicator_features['Support']) / (df_ft["SL_Sell"] - df_ft["Open"])

        # print("\n✅ ข้อมูล indicator_features ก่อนทำ Lag Features")
        # print(indicator_features.info())
        # print(indicator_features.head())

        # --- กำหนด Lag periods ที่ต้องการ ---
        # print(f"✅ เริ่มคำนวณ Lag Features")
        # ใช้ timeframe_enum ที่รับมาตรงๆ
        if timeframe >= mt5.TIMEFRAME_H4: # 240 เป็นค่า Enum สำหรับ H4
            lags = [1, 2, 3, 5, 10]
        else: # รวมถึง H1 (Enum 60) และ Timeframe ที่เล็กกว่า
            lags = [1, 2, 3, 5, 10, 15, 20, 30, 50]

        # --- สร้าง Lag Features (ปรับปรุงเพื่อ Performance) ---
        # สร้าง DataFrame เปล่าสำหรับเก็บ Lag Features
        lag_features = pd.DataFrame(index=df_ft.index)

        # Lag Features สำหรับคอลัมน์ราคา/Volume
        price_cols_upper = ['Close', 'Open', 'High', 'Low', 'Volume']
        for col in price_cols_upper:
            if col in df_ft.columns:
                for lag in lags:
                    lag_features[f'{col}_Lag_{lag}'] = df_ft[col].shift(lag)
            else:
                print(f"Warning: Price column '{col}' not found in df_ft for Lag Features.")

        # # Lag Features สำหรับ Indicators
        # # ใช้ชื่อคอลัมน์ Indicators หลักที่คำนวณได้ก่อนหน้านี้ (ไม่ใช่ Features ที่สร้างจาก Indicators)
        # indicator_cols_for_lag = [
        #     'RSI14',
        #     'EMA50', 'EMA100', 'EMA200',
        #     'ATR', 'BB_width', # Indicators ที่คำนวณตรงๆ
        #     # Indicators ที่ได้จาก pandas_ta (ต้องตรวจสอบว่าคอลัมน์มีอยู่)
        #     macd_line_col, macd_signal_col, # MACD Lines
        #     stoch_k_col, stoch_d_col, # Stoch Lines
        #     adx_col, dmp_col, dmn_col # ADX Lines
        # ]

        # กรองเฉพาะคอลัมน์ที่มีอยู่จริงใน df_ft หรือ DataFrame indicator_features
        # หรือใน Series ที่คำนวณ Indicators หลัก
        existing_indicator_cols_for_lag = [
            'RSI14' if 'RSI14' in df_ft.columns else None,
            'EMA50' if 'EMA50' in df_ft.columns else None,
            'EMA100' if 'EMA100' in df_ft.columns else None,
            'EMA200' if 'EMA200' in df_ft.columns else None,
            'ATR' if 'ATR' in indicator_features.columns else None, # หรือ atr.name ถ้า atr เป็น Series
            'BB_width' if 'BB_width' in indicator_features.columns else None, # หรือ bb_width.name
            macd_line_col if macd_line_col in macd.columns else None,
            macd_signal_col if macd_signal_col in macd.columns else None,
            stoch_k_col if stoch_k_col in stoch.columns else None,
            stoch_d_col if stoch_d_col in stoch.columns else None,
            adx_col if adx_col in adx.columns else None,
            dmp_col if dmp_col in adx.columns else None,
            dmn_col if dmn_col in adx.columns else None
        ]
        existing_indicator_cols_for_lag = [col for col in existing_indicator_cols_for_lag if col is not None] # กรอง None ออก

        # รวม df_ft และ indicator_features (และ Series indicators หลัก) ชั่วคราวเพื่อทำ Lag
        # หรือเข้าถึง Series indicators หลักโดยตรง
        temp_df_for_lag = pd.concat([df_ft[['Close', 'Open', 'High', 'Low', 'Volume', 'RSI14', 'EMA50', 'EMA100', 'EMA200']],
                                    macd, stoch, adx, atr.rename('ATR'), bb_width.rename('BB_width'), support.rename('Support'), resistance.rename('Resistance')], axis=1)

        for indicator in existing_indicator_cols_for_lag:
            # ตรวจสอบว่าคอลัมน์ Indicator มีอยู่จริงใน temp_df_for_lag
            if indicator in temp_df_for_lag.columns:
                for lag in [1, 2, 3, 5]: # ใช้ lag periods ที่ใช้กับ indicators
                    lag_features[f'{indicator}_Lag_{lag}'] = temp_df_for_lag[indicator].shift(lag)
            else:
                print(f"Warning: Indicator column '{indicator}' not found in combined data for Lag Features.")

        # print(f"✅ เริ่มคำนวณ returns_changes_features")

        # --- สร้าง Features แบบเปลี่ยนแปลงจาก Lag (Returns, Changes) (ปรับปรุงเพื่อ Performance) ---
        # สร้าง DataFrame เปล่าสำหรับเก็บ Features Returns/Changes
        returns_changes_features = pd.DataFrame(index=df_ft.index)

        for lag in [1, 2, 3, 5]: # ใช้ lag periods ที่ใช้กับ returns/changes
            if 'Close' in df_ft.columns:
                returns_changes_features[f'Close_Return_{lag}'] = df_ft['Close'].pct_change(lag)
            else:
                print(f"Warning: 'Close' column not found for Close_Return_{lag}.")
                returns_changes_features[f'Close_Return_{lag}'] = np.nan

            if 'Volume' in df_ft.columns:
                returns_changes_features[f'Volume_Change_{lag}'] = df_ft['Volume'].diff(lag) / (df_ft['Volume'].shift(lag) + 1e-10)
            else:
                print(f"Warning: 'Volume' column not found for Volume_Change_{lag}.")
                returns_changes_features[f'Volume_Change_{lag}'] = np.nan

        # print(f"✅ เริ่มคำนวณ Rolling Features")

        # --- สร้าง Rolling Features (ปรับปรุงเพื่อ Performance) ---
        # สร้าง DataFrame เปล่าสำหรับเก็บ Rolling Features
        rolling_features = pd.DataFrame(index=df_ft.index)

        for window in [3, 5, 10, 20]: # ใช้ window sizes ที่ใช้กับ rolling features
            if 'Close' in df_ft.columns:
                rolling_features[f'Close_MA_{window}'] = df_ft['Close'].rolling(window, min_periods=1).mean().shift(1)
                rolling_features[f'Close_Std_{window}'] = df_ft['Close'].rolling(window, min_periods=1).std().shift(1)
            else:
                print(f"Warning: 'Close' column not found for Close Rolling Features (window {window}).")
                rolling_features[f'Close_MA_{window}'] = np.nan
                rolling_features[f'Close_Std_{window}'] = np.nan

            if 'Volume' in df_ft.columns:
                rolling_features[f'Volume_MA_{window}'] = df_ft['Volume'].rolling(window, min_periods=1).mean().shift(1)
            else:
                print(f"Warning: 'Volume' column not found for Volume_MA_{window}.")
                rolling_features[f'Volume_MA_{window}'] = np.nan

        # --- เพิ่ม Interaction Features (ตรงกับ python_LightGBM_17_Signal.py) ---
        # print(f"✅ เริ่มคำนวณ Interaction Features")
        interaction_features = pd.DataFrame(index=df_ft.index)

        # Interaction Features ตรงกับ training model
        interaction_features['RSI_x_VolumeSpike'] = df_ft['RSI14'] * df_ft['Volume_Spike']
        interaction_features['EMA_diff_x_ATR'] = df_ft['EMA_diff'] * indicator_features['ATR']
        interaction_features['Momentum5_x_Volatility10'] = (df_ft['Close'] - df_ft['Close'].shift(5)) * df_ft['Close'].rolling(10).std()
        interaction_features['RSI14_x_BBwidth'] = df_ft['RSI14'] * df_ft['BB_width']
        interaction_features['MACD_signal_x_ADX'] = indicator_features['MACD_signal'] * df_ft['ADX_14']
        interaction_features['Price_above_EMA50_x_RSI_signal'] = df_ft['Price_above_EMA50'] * indicator_features['RSI_signal']
        interaction_features['RSI14_x_ATR'] = df_ft['RSI14'] * indicator_features['ATR']
        interaction_features['RSI14_x_PriceMove'] = df_ft['RSI14'] * df_ft['Price_Move']
        interaction_features['EMA50_x_RollingVol5'] = df_ft['EMA50'] * df_ft['Rolling_Vol_5']
        interaction_features['EMA_diff_x_BBwidth'] = df_ft['EMA_diff'] * df_ft['BB_width']
        interaction_features['ADX_14_x_ATR'] = df_ft['ADX_14'] * indicator_features['ATR']

        # เพิ่ม Interaction Features เพิ่มเติม
        interaction_features['RSI14_x_MACD_12_26_9'] = df_ft['RSI14'] * df_ft['MACD_12_26_9']
        interaction_features['EMA_diff_x_RSI14'] = df_ft['EMA_diff'] * df_ft['RSI14']
        interaction_features['ADX_14_x_BBwidth'] = df_ft['ADX_14'] * df_ft['BB_width']

        # --- รวม Features ที่สร้างขึ้นทั้งหมดเข้าด้วยกัน ---
        # ตรวจสอบและแก้ไข duplicate columns ก่อนการ concat
        print(f"🔍 ตรวจสอบ duplicate columns ก่อนรวม features...")

        # ตรวจสอบ duplicate columns ใน df_ft
        if df_ft.columns.duplicated().any():
            print(f"⚠️ พบ duplicate columns ใน df_ft: {df_ft.columns[df_ft.columns.duplicated()].tolist()}")
            df_ft = df_ft.loc[:, ~df_ft.columns.duplicated()]
            print(f"✅ แก้ไข duplicate columns ใน df_ft แล้ว")

        # ตรวจสอบ duplicate columns ใน indicator_features
        if indicator_features.columns.duplicated().any():
            print(f"⚠️ พบ duplicate columns ใน indicator_features: {indicator_features.columns[indicator_features.columns.duplicated()].tolist()}")
            indicator_features = indicator_features.loc[:, ~indicator_features.columns.duplicated()]
            print(f"✅ แก้ไข duplicate columns ใน indicator_features แล้ว")

        # ตรวจสอบ duplicate columns ใน lag_features
        if lag_features.columns.duplicated().any():
            print(f"⚠️ พบ duplicate columns ใน lag_features: {lag_features.columns[lag_features.columns.duplicated()].tolist()}")
            lag_features = lag_features.loc[:, ~lag_features.columns.duplicated()]
            print(f"✅ แก้ไข duplicate columns ใน lag_features แล้ว")

        # รวม Features โดยระวัง duplicate columns
        print(f"[{datetime.datetime.now()}] 🔄 Combining all features")
        try:
            # ตรวจสอบว่าคอลัมน์ที่ต้องการมีอยู่หรือไม่
            required_base_cols = ['DateTime','Open', 'High', 'Low', 'Close', 'Volume']
            missing_base_cols = [col for col in required_base_cols if col not in df_ft.columns]
            if missing_base_cols:
                print(f"❌ Missing base columns: {missing_base_cols}")
                return

            df_ft_combined = pd.concat([df_ft[['DateTime','Open', 'High', 'Low', 'Close', 'Volume', # ราคาเดิม
                                                        'Entry_DayOfWeek', 'Entry_Hour', # Features เวลา
                                                        'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', # ช่วงเวลา
                                                        'Bar_CLp', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', # Price Action
                                                        'Price_Range', 'Price_Move', 'Price_Strangth', # Price Movement
                                                        'Volume_MA20', 'Volume_Spike', # Volume Features
                                                        'EMA50', 'EMA100', 'EMA200', 'EMA_diff', 'MA_Cross', 'Price_above_EMA50', # EMA Features
                                                        'Rolling_Vol_5', 'Rolling_Vol_15', # Volatility
                                                        'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', # Distance to EMA
                                                        'RSI14', # RSI Calculation
                                                        'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', # MACD
                                                        'STOCHk_14_3_3', 'STOCHd_14_3_3', # Stochastic
                                                        'ADX_14', 'DMP_14', 'DMN_14', # ADX
                                                        'Upper_BB', 'Lower_BB', 'BB_width', # Bollinger Bands
                                                        'Ratio_Buy', 'Ratio_Sell'
                                                        ]], # เลือกคอลัมน์เดิมที่ยังต้องการเก็บไว้ก่อน concat
                                                    indicator_features, # Features จาก Indicators หลัก (ที่สร้างไว้ใน indicator_features DF)
                                                    lag_features, # Lag Features
                                                    returns_changes_features, # Returns/Changes Features
                                                    rolling_features, # Rolling Features
                                                    interaction_features # Interaction Features
                                                ], axis=1)

            # ตรวจสอบ duplicate columns หลัง concat
            if df_ft_combined.columns.duplicated().any():
                print(f"⚠️ พบ duplicate columns หลัง concat: {df_ft_combined.columns[df_ft_combined.columns.duplicated()].tolist()}")
                df_ft_combined = df_ft_combined.loc[:, ~df_ft_combined.columns.duplicated()]
                print(f"✅ แก้ไข duplicate columns หลัง concat แล้ว")

            print(f"✅ รวม features สำเร็จ: {len(df_ft_combined.columns)} columns")

        except Exception as concat_error:
            print(f"❌ เกิดข้อผิดพลาดในการรวม features: {concat_error}")
            # Fallback: ใช้เฉพาะ df_ft
            df_ft_combined = df_ft.copy()
            print(f"🔄 ใช้ fallback: เฉพาะ df_ft ({len(df_ft_combined.columns)} columns)")

        # ตรวจสอบข้อมูลหลังสร้าง Features
        # print(f"\n✅ ข้อมูล df_ft_combined หลังสร้าง Features:")
        # print(df_ft_combined.info())
        # print(df_ft_combined.head())

        # --- ลบแถวที่มีค่า NaN ที่เกิดจากการสร้าง Indicators และ Features ---
        initial_rows = len(df_ft_combined)
        if not df_ft_combined.empty:
            df_ft = df_ft_combined.dropna() # ตอนนี้ df_ft จะเป็น DF ที่รวม features แล้ว
            print(f"✅ สร้าง Indicators และ Features เรียบร้อย (ลบแถวที่ขาดหายไป {initial_rows - len(df_ft)} จาก {initial_rows} แถว)")
        else:
            print(f"Warning: df_ft_combined is empty before dropna.")
            return

        if df_ft.empty:
            print(f"Warning: No data left in df_ft after dropna.")
            return

        # print(f"\n✅ ข้อมูล df_ft หลังสร้างรวม df_ft_combined")
        # print(df_ft.info())
        # print(df_ft.columns.tolist())
        # print(df_ft.head())
        # print(df_ft.tail())

        # ตรวจสอบการโหลดโมเดลตาม Architecture ที่ใช้
        if USE_MULTI_MODEL_ARCHITECTURE:
            if scenario_models is None:
                print(f"[{datetime.datetime.now()}] Failed to load Multi-Model components for {symbol} ({timeframe}). Cannot make prediction.")
                return
        else:
            if model is None or scaler is None or features_list is None:
                print(f"[{datetime.datetime.now()}] Failed to load Single Model components for {symbol} ({timeframe}). Cannot make prediction.")
                return

        # --- 3. เตรียมข้อมูลและทำนายตาม Architecture ---
        if USE_MULTI_MODEL_ARCHITECTURE:
            # === Multi-Model Architecture Prediction ===
            try:
                # เตรียมข้อมูลสำหรับการทำนาย (ใช้แท่งก่อนสุดท้าย)
                latest_bar_for_prediction = df_ft.iloc[-2:-1]  # แท่งก่อนสุดท้าย
                latest_bar_for_decision = df_ft.tail(1)  # แท่งสุดท้าย

                # สร้าง Series สำหรับการตรวจจับ scenario
                prediction_row = latest_bar_for_prediction.iloc[0]

                # ตรวจจับสถานการณ์ตลาด
                market_condition = detect_market_scenario(prediction_row)
                print(f"🔍 Market Condition: {market_condition}")

                # *** ปรับปรุงใหม่: ตรวจสอบทั้ง 2 แบบตามสถานการณ์ตลาด ***
                # กรณี uptrend: ตรวจสอบ trend_following (buy) และ counter_trend (sell)
                # กรณี downtrend: ตรวจสอบ trend_following (sell) และ counter_trend (buy)

                # เก็บผลการวิเคราะห์ทั้ง 2 แบบ
                analysis_results = {
                    'trend_following': {'buy': None, 'sell': None},
                    'counter_trend': {'buy': None, 'sell': None}
                }

                # ตรวจสอบ trend_following scenarios
                if 'trend_following' in scenario_models:
                    print(f"🔄 Analyzing trend_following model...")

                    # ทำนาย BUY สำหรับ trend_following
                    should_trade_tf_buy, confidence_tf_buy, model_used_tf_buy = predict_with_scenario_model(
                        prediction_row, 'buy', {'trend_following': scenario_models['trend_following']}, 0.5
                    )
                    analysis_results['trend_following']['buy'] = {
                        'should_trade': should_trade_tf_buy,
                        'confidence': confidence_tf_buy,
                        'model_used': model_used_tf_buy
                    }

                    # ทำนาย SELL สำหรับ trend_following
                    should_trade_tf_sell, confidence_tf_sell, model_used_tf_sell = predict_with_scenario_model(
                        prediction_row, 'sell', {'trend_following': scenario_models['trend_following']}, 0.5
                    )
                    analysis_results['trend_following']['sell'] = {
                        'should_trade': should_trade_tf_sell,
                        'confidence': confidence_tf_sell,
                        'model_used': model_used_tf_sell
                    }

                    print(f"   📊 Trend Following - BUY: {confidence_tf_buy:.4f}, SELL: {confidence_tf_sell:.4f}")

                # ตรวจสอบ counter_trend scenarios
                if 'counter_trend' in scenario_models:
                    print(f"🔄 Analyzing counter_trend model...")

                    # ทำนาย BUY สำหรับ counter_trend
                    should_trade_ct_buy, confidence_ct_buy, model_used_ct_buy = predict_with_scenario_model(
                        prediction_row, 'buy', {'counter_trend': scenario_models['counter_trend']}, 0.5
                    )
                    analysis_results['counter_trend']['buy'] = {
                        'should_trade': should_trade_ct_buy,
                        'confidence': confidence_ct_buy,
                        'model_used': model_used_ct_buy
                    }

                    # ทำนาย SELL สำหรับ counter_trend
                    should_trade_ct_sell, confidence_ct_sell, model_used_ct_sell = predict_with_scenario_model(
                        prediction_row, 'sell', {'counter_trend': scenario_models['counter_trend']}, 0.5
                    )
                    analysis_results['counter_trend']['sell'] = {
                        'should_trade': should_trade_ct_sell,
                        'confidence': confidence_ct_sell,
                        'model_used': model_used_ct_sell
                    }

                    print(f"   📊 Counter Trend - BUY: {confidence_ct_buy:.4f}, SELL: {confidence_ct_sell:.4f}")

                # *** เลือก signal ที่ดีที่สุดตามสถานการณ์ตลาด ***
                best_signal = "HOLD"
                best_confidence = 0.0
                best_model_used = "none"
                best_scenario = "none"
                best_action = "none"

                # รวบรวมตัวเลือกทั้งหมด
                candidates = []

                if market_condition == "uptrend":
                    print(f"🔺 Uptrend detected - Prioritizing trend_following BUY and counter_trend SELL")
                    # uptrend: trend_following buy, counter_trend sell
                    if analysis_results['trend_following']['buy']:
                        tf_buy = analysis_results['trend_following']['buy']
                        if tf_buy['should_trade']:
                            candidates.append(('BUY', tf_buy['confidence'], tf_buy['model_used'], 'trend_following'))

                    if analysis_results['counter_trend']['sell']:
                        ct_sell = analysis_results['counter_trend']['sell']
                        if ct_sell['should_trade']:
                            candidates.append(('SELL', ct_sell['confidence'], ct_sell['model_used'], 'counter_trend'))

                elif market_condition == "downtrend":
                    print(f"🔻 Downtrend detected - Prioritizing trend_following SELL and counter_trend BUY")
                    # downtrend: trend_following sell, counter_trend buy
                    if analysis_results['trend_following']['sell']:
                        tf_sell = analysis_results['trend_following']['sell']
                        if tf_sell['should_trade']:
                            candidates.append(('SELL', tf_sell['confidence'], tf_sell['model_used'], 'trend_following'))

                    if analysis_results['counter_trend']['buy']:
                        ct_buy = analysis_results['counter_trend']['buy']
                        if ct_buy['should_trade']:
                            candidates.append(('BUY', ct_buy['confidence'], ct_buy['model_used'], 'counter_trend'))

                else:  # sideways หรือ unknown
                    print(f"↔️ Sideways/Unknown market - Checking all possibilities")
                    # sideways: ตรวจสอบทุกตัวเลือก
                    for scenario in ['trend_following', 'counter_trend']:
                        for action in ['buy', 'sell']:
                            if analysis_results[scenario][action] and analysis_results[scenario][action]['should_trade']:
                                signal_name = action.upper()
                                candidates.append((
                                    signal_name,
                                    analysis_results[scenario][action]['confidence'],
                                    analysis_results[scenario][action]['model_used'],
                                    scenario
                                ))

                # เลือกตัวเลือกที่มี confidence สูงสุด
                if candidates:
                    best_candidate = max(candidates, key=lambda x: x[1])  # เรียงตาม confidence
                    best_signal, best_confidence, best_model_used, best_scenario = best_candidate
                    best_action = best_signal.lower()

                    print(f"🎯 Best candidate: {best_signal} (Confidence: {best_confidence:.4f}, Scenario: {best_scenario})")
                else:
                    print(f"❌ No valid trading candidates found")

                # กำหนดค่าสำหรับการส่งต่อ
                predicted_signal = best_signal
                probability_tp_hit = best_confidence
                model_used = best_model_used
                scenario_name = best_scenario
                action_type = best_action

                if predicted_signal == "BUY":
                    prediction_class = 3
                elif predicted_signal == "SELL":
                    prediction_class = 1
                else:
                    prediction_class = 2

                print(f"🤖 Final Multi-Model Decision: {predicted_signal} (Confidence: {probability_tp_hit:.4f}, Scenario: {scenario_name})")

                # โหลดพารามิเตอร์ตาม scenario ที่เลือก
                if scenario_name != "none":
                    try:
                        scenario_threshold = load_scenario_threshold(symbol, timeframe, scenario_name)
                        scenario_nbars = load_scenario_nbars(symbol, timeframe, scenario_name)

                        if scenario_threshold is not None:
                            model_confidence_threshold = scenario_threshold
                            print(f"🎯 Using {scenario_name} threshold: {model_confidence_threshold}")

                        if scenario_nbars is not None:
                            num_nBars_SL = scenario_nbars
                            print(f"🎯 Using {scenario_name} nBars_SL: {num_nBars_SL}")

                    except Exception as e:
                        print(f"⚠️ Error loading scenario parameters for {scenario_name}: {e}")

                # ดึง Timestamp ของแท่งล่าสุด
                latest_bar_dt = latest_bar_for_decision.index[0]

            except Exception as e:
                print(f"❌ Error in Multi-Model prediction: {e}")
                traceback.print_exc()
                return

        else:
            # === Single Model Architecture Prediction (Legacy) ===
            try:
                if df_ft.empty:
                    print(f"Warning: df_ft is empty when attempting to select features.")
                    return

                if not isinstance(features_list, (list, tuple)) or not features_list:
                        print(f"Error: Loaded features_list is invalid or empty: {features_list}")
                        return

                missing_cols = [col for col in features_list if col not in df_ft.columns]
                if missing_cols:
                    print(f"Error: Missing required feature columns: {missing_cols}")
                    return

                # เตรียม DataFrame สำหรับทำนาย (ใช้แท่งก่อนสุดท้าย)
                prediction_features_df = df_ft.iloc[-2:-1][features_list]
                latest_bar_for_decision_df = df_ft.tail(1)

                # Scale features
                features_array = prediction_features_df.values
                scaled_features_array = scaler.transform(features_array)
                scaled_features_df = pd.DataFrame(scaled_features_array, columns=features_list, index=prediction_features_df.index)

                # ทำนายด้วย Single Model
                prediction_proba_all = model.predict_proba(scaled_features_df)
                prediction_class = model.predict(scaled_features_df)[0]
                probability_tp_hit = prediction_proba_all[0].max()

                # แปลง class เป็น signal
                class_to_signal = {0: "STRONG_SELL", 1: "SELL", 2: "HOLD", 3: "BUY", 4: "STRONG_BUY"}
                predicted_signal = class_to_signal.get(prediction_class, "HOLD")

                # ดึง Timestamp ของแท่งล่าสุด
                latest_bar_dt = latest_bar_for_decision_df.index[0]

                print(f"📊 Single Model Prediction: Class={prediction_class}, Signal={predicted_signal}, Confidence={probability_tp_hit:.4f}")

            except Exception as e:
                print(f"❌ Error in Single Model prediction: {e}")
                traceback.print_exc()
                return

        # แปลง class เป็น signal
        # Class mapping: 0=Strong_Sell, 1=Sell, 2=Hold, 3=Buy, 4=Strong_Buy
        class_to_signal = {0: "STRONG_SELL", 1: "SELL", 2: "HOLD", 3: "BUY", 4: "STRONG_BUY"}
        predicted_signal = class_to_signal.get(prediction_class, "HOLD")

        # print(f"\n✅ Multi-class prediction: Class={prediction_class}, Signal={predicted_signal}, Confidence={probability_tp_hit:.4f}")

        # --- 5. ตัดสินใจ Signal ---
        signal = "HOLD" # Default
        waiting_for = "" # เพิ่มตัวแปรสำหรับเก็บข้อมูลการรอคอย

        # ดึงข้อมูลแถวสุดท้าย (ล่าสุด) ที่มีค่า Feature ครบถ้วน
        latest_bar_features_df_all_i2 = df_ft.tail(2) # ดึงทั้งหมดที่มีใน df_ft
        latest_features_dict_all_i2 = latest_bar_features_df_all_i2.iloc[0].to_dict()
        # print(f"\n✅ latest features dict (dictionary): {latest_features_dict_all_i2}") # แสดง Feature ที่เลือกใช้ (จะเห็นเป็น dictionary แล้ว)

        # ดึงข้อมูลแถวสุดท้าย (ล่าสุด) ที่มีค่า Feature ครบถ้วน
        latest_bar_features_df_all_i1 = df_ft.tail(1) # ดึงทั้งหมดที่มีใน df_ft
        latest_features_dict_all_i1 = latest_bar_features_df_all_i1.iloc[0].to_dict()
        # print(f"\n✅ latest features dict (dictionary): {latest_features_dict_all_i1}") # แสดง Feature ที่เลือกใช้ (จะเห็นเป็น dictionary แล้ว)

        # ตรวจอบช่วงเวลา ที่มีประสิทธิภาพสูงสุด
        # print(f"ทดสอบโหลดฟิวเตอร์ time_filters")
        # time_filters = load_time_filters(symbol, timeframe)

        hour = latest_features_dict_all_i1['Entry_Hour']
        day_of_week = latest_features_dict_all_i1['Entry_DayOfWeek']
        
        # ป้องกันกรณี time_filters ไม่มี key หรือเป็น list ว่าง
        days_filter = time_filters.get('days', list(range(7)))
        hours_filter = time_filters.get('hours', list(range(24)))
        if not days_filter:
            days_filter = list(range(7))
        if not hours_filter:
            hours_filter = list(range(24))

        # ตรวจสอบเงื่อนไข time_filters
        if ENABLE_TIME_FILTERS:
            time_condition = (
                6 <= latest_features_dict_all_i1['Entry_Hour'] <= 20 and
                day_of_week in days_filter and
                hour in hours_filter
            )
            print(f"[{datetime.datetime.now()}] ⏰ Time filters enabled - Day: {day_of_week} in {days_filter}, Hour: {hour} in {hours_filter}, Condition: {time_condition}")
        else:
            time_condition = True  # เทรดได้ตลอดเวลา
            print(f"[{datetime.datetime.now()}] ⏰ Time filters disabled - Trading allowed anytime")

        # print(f"\n✅ แสดงข้อมูลที่ model tp_hit {probability_tp_hit} time {time_condition}")
        # print(f"\n✅ แสดงข้อมูลที่ close {latest_features_dict_all_i2.get('Close', -float('inf'))} open {latest_features_dict_all_i2.get('Open', float('inf'))}")

        # ตรวจสอบเงื่อนไขทางเทคนิคสำหรับ Multi-Model Architecture
        # ⚠️ ใช้เงื่อนไขตาม 4 scenarios จาก python_LightGBM_17_Signal.py

        # ตรวจสอบสถานการณ์ตลาด
        close = latest_features_dict_all_i2.get('Close', 0.0)
        ema200 = latest_features_dict_all_i2.get('EMA200', close)  # fallback ถ้าไม่มี EMA200

        # เงื่อนไขพื้นฐานสำหรับ Multi-Model (ลดความเข้มงวดตาม training model)
        tech_signal_buy_conditions = {
            'Close > Open (Prev Bar)': latest_features_dict_all_i2.get('Close', -float('inf')) > latest_features_dict_all_i2.get('Open', float('inf')),
            'MACD_signal == 1.0 (Prev Bar)': latest_features_dict_all_i2.get('MACD_signal', 0.0) == 1.0,
            'RSI_signal > input_rsi_level_in * 0.8 (Prev Bar)': latest_features_dict_all_i2.get('RSI_signal', -float('inf')) > (input_rsi_level_in * 0.8),  # ลดความเข้มงวด
            'Volume > Volume_MA20 * 0.50 (Prev Bar)': latest_features_dict_all_i2.get('Volume', 0.0) > latest_features_dict_all_i2.get('Volume_MA20', 0.0) * 0.50,
            'prev_pullback_buy > input_pull_back * 0.5': latest_features_dict_all_i2.get('PullBack_Up', 0.0) > (input_pull_back * 0.5),
            'prev_ratio_buy > input_take_profit * 0.8': latest_features_dict_all_i2.get('Ratio_Buy', 0.0) > (input_take_profit * 0.8)  # ลดจาก 1.5 เป็น 0.8 ตาม Multi-Model
        }

        tech_signal_sell_conditions = {
            'Close < Open (Prev Bar)': latest_features_dict_all_i2.get('Close', float('inf')) < latest_features_dict_all_i2.get('Open', -float('inf')),
            'MACD_signal == -1.0 (Prev Bar)': latest_features_dict_all_i2.get('MACD_signal', 0.0) == -1.0,
            'RSI_signal < (100-input_rsi_level_in * 0.8) (Prev Bar)': latest_features_dict_all_i2.get('RSI_signal', float('inf')) < (100 - input_rsi_level_in * 0.8),  # ลดความเข้มงวด
            'Volume > Volume_MA20 * 0.50 (Prev Bar)': latest_features_dict_all_i2.get('Volume', 0.0) > latest_features_dict_all_i2.get('Volume_MA20', 0.0) * 0.50,
            'prev_pullback_sell > input_pull_back * 0.5': latest_features_dict_all_i2.get('PullBack_Down', 0.0) > (input_pull_back * 0.5),
            'prev_ratio_sell > input_take_profit * 0.8': latest_features_dict_all_i2.get('Ratio_Sell', 0.0) > (input_take_profit * 0.8)  # ลดจาก 1.5 เป็น 0.8 ตาม Multi-Model
        }

        tech_signal_buy = all(tech_signal_buy_conditions.values())
        tech_signal_sell = all(tech_signal_sell_conditions.values())

        # 🔍 แสดงผลการตรวจสอบเงื่อนไขทางเทคนิค
        print(f"\n🔍 Technical Analysis Check for {symbol} M{timeframe}:")
        print(f"📊 Model Prediction: {predicted_signal} (Confidence: {probability_tp_hit:.4f})")
        print(f"⚙️ Technical Conditions: {'ENABLED' if ENABLE_TECHNICAL_CONDITIONS else 'DISABLED'}")

        # แสดงค่าจริงของ features ที่สำคัญ
        print(f"📈 Key Features Values:")
        print(f"   Close: {latest_features_dict_all_i2.get('Close', 'N/A')}")
        print(f"   Open: {latest_features_dict_all_i2.get('Open', 'N/A')}")
        print(f"   EMA50: {latest_features_dict_all_i2.get('EMA50', 'N/A')}")
        print(f"   MACD_signal: {latest_features_dict_all_i2.get('MACD_signal', 'N/A')}")
        print(f"   RSI_signal: {latest_features_dict_all_i2.get('RSI_signal', 'N/A')}")
        print(f"   Volume: {latest_features_dict_all_i2.get('Volume', 'N/A')}")
        print(f"   Volume_MA20: {latest_features_dict_all_i2.get('Volume_MA20', 'N/A')}")
        print(f"   PullBack_Up: {latest_features_dict_all_i2.get('PullBack_Up', 'N/A')}")
        print(f"   PullBack_Down: {latest_features_dict_all_i2.get('PullBack_Down', 'N/A')}")
        print(f"   Ratio_Buy: {latest_features_dict_all_i2.get('Ratio_Buy', 'N/A')}")
        print(f"   Ratio_Sell: {latest_features_dict_all_i2.get('Ratio_Sell', 'N/A')}")

        # แสดงเงื่อนไขทางเทคนิคเฉพาะเมื่อเปิดใช้งาน
        if ENABLE_TECHNICAL_CONDITIONS:
            if predicted_signal in ["BUY", "STRONG_BUY"]:
                print(f"🟢 BUY Technical Conditions:")
                for condition, result in tech_signal_buy_conditions.items():
                    status = "✅" if result else "❌"
                    print(f"   {status} {condition}: {result}")
                print(f"🟢 Overall BUY Tech Signal: {tech_signal_buy}")

            if predicted_signal in ["SELL", "STRONG_SELL"]:
                print(f"🔴 SELL Technical Conditions:")
                for condition, result in tech_signal_sell_conditions.items():
                    status = "✅" if result else "❌"
                    print(f"   {status} {condition}: {result}")
                print(f"🔴 Overall SELL Tech Signal: {tech_signal_sell}")
        else:
            print(f"⚠️ Technical conditions are DISABLED - Using Model Prediction only")

        # ทดสอบระบบ ส่งสัญญาณ Buy หรือ Sell
        # probability_tp_hit = 0.678910
        # time_condition = True

        # *** กำหนดค่า symbol parameters ก่อนการใช้งาน ***
        symbol_spread = symbol_info_map.get(symbol, {}).get('Spread', 15)
        symbol_digits = symbol_info_map.get(symbol, {}).get('Digits', 5)
        symbol_points = symbol_info_map.get(symbol, {}).get('Points', 0.00001)

        if probability_tp_hit >= model_confidence_threshold:

            if time_condition:

                # print(f"probability_tp_hit {probability_tp_hit} time_condition {time_condition}")
                # print(f"Multi-class prediction: Class={prediction_class}, Signal={predicted_signal}")

                # ใช้ predicted_signal จาก multi-class model เป็นหลัก
                # แต่ยังคงตรวจสอบเงื่อนไขทางเทคนิคเพิ่มเติม

                # ดึงข้อมูลเพื่อตรวจสอบ : ก่อนการซื้อ-ขาย
                # print(f"\n✅ ทดสอบข้อมูลแท่ก่อนหน้า 1 แท่ง : latest_features_dict_all_i2 (tail(2))")
                # if 'DateTime' in latest_features_dict_all_i2:    print(f"time {latest_features_dict_all_i2['DateTime']}")
                # if 'Entry_Hour' in latest_features_dict_all_i2:  print(f"entry hour {latest_features_dict_all_i2['Entry_Hour']}")
                # if 'Open' in latest_features_dict_all_i2:        print(f"op {latest_features_dict_all_i2['Open']}")
                # if 'High' in latest_features_dict_all_i2:        print(f"hh {latest_features_dict_all_i2['High']}")
                # if 'Low' in latest_features_dict_all_i2:         print(f"ll {latest_features_dict_all_i2['Low']}")
                # if 'Close' in latest_features_dict_all_i2:       print(f"cl {latest_features_dict_all_i2['Close']}")
                # if 'Bar_CLp' in latest_features_dict_all_i2:     print(f"clp {latest_features_dict_all_i2['Bar_CLp']}")
                # if 'Volume' in latest_features_dict_all_i2:      print(f"vol {latest_features_dict_all_i2['Volume']}")

                # ดึงข้อมูลเพื่อตรวจสอบ : ก่อนการซื้อ-ขาย
                # print(f"\n✅ ทดสอบข้อมูลแท่ปัจจุบัน : latest_features_dict_all_i1 (tail(1))")
                # if 'DateTime' in latest_features_dict_all_i1:    print(f"time {latest_features_dict_all_i1['DateTime']}")
                # if 'Entry_Hour' in latest_features_dict_all_i1:  print(f"entry hour {latest_features_dict_all_i1['Entry_Hour']}")
                # if 'Open' in latest_features_dict_all_i1:        print(f"op {latest_features_dict_all_i1['Open']}")
                # if 'High' in latest_features_dict_all_i1:        print(f"hh {latest_features_dict_all_i1['High']}")
                # if 'Low' in latest_features_dict_all_i1:         print(f"ll {latest_features_dict_all_i1['Low']}")
                # if 'Close' in latest_features_dict_all_i1:       print(f"cl {latest_features_dict_all_i1['Close']}")
                # if 'Bar_CLp' in latest_features_dict_all_i1:     print(f"clp {latest_features_dict_all_i1['Bar_CLp']}")
                # if 'Volume' in latest_features_dict_all_i1:      print(f"vol {latest_features_dict_all_i1['Volume']}")

                # โหลด entry condition ที่ดีที่สุดจากการทดสอบ (สำหรับแสดงผลเท่านั้น)
                best_entry_name = load_best_entry_condition(symbol, timeframe)
                print(f"🎯 Legacy entry condition: {best_entry_name} (ไม่ใช้แล้ว)")
                print(f"🎯 ใช้ Multi-Model Architecture conditions แทน")

                # ⚠️ ปิดการใช้ entry_func เพราะจะเขียนทับ tech_signal_buy, tech_signal_sell ที่คำนวณแล้ว
                # และใช้เงื่อนไขจาก Multi-Model Architecture แทน
                # entry_conditions_funcs = get_entry_conditions_functions()
                # entry_func = entry_conditions_funcs.get(best_entry_name, entry_conditions_funcs["entry_v1"])
                # tech_signal_buy, tech_signal_sell = entry_func(...)

                print(f"🔍 Using Multi-Model tech signals: BUY={tech_signal_buy}, SELL={tech_signal_sell}")

                entry_price = 0.0 # Default SL
                sl_price = 0.0 # Default SL
                tp_price = 0.0 # Default TP

                # ใช้ค่า symbol parameters ที่กำหนดไว้แล้วข้างบน

                # ใช้ข้อมูล Indicator จากแท่งก่อนหน้า (index -2) สำหรับ ATR, Support, Resistance
                # ต้องแน่ใจว่าคอลัมน์เหล่านี้ถูกคำนวณและมีค่าใน indicator_features หรือ df_ft
                # และต้องจัดการค่า NaN ที่อาจเกิดขึ้น
                # ในโค้ดเดิมของคุณ ATR, Support, Resistance ถูกคำนวณแยกก่อนสร้าง indicator_features
                # ตรวจสอบว่า indicator_features มีคอลัมน์ 'ATR', 'Support', 'Resistance' ที่มีค่าสำหรับแท่ง -2
                # หรือใช้ series atr, support, resistance ที่คำนวณไว้ก่อนหน้าโดยตรง
                # สมมติว่าใช้คอลัมน์ใน indicator_features
                prev_atr = indicator_features['ATR'].iloc[-2] if 'ATR' in indicator_features.columns and len(indicator_features) >= 2 and not pd.isna(indicator_features['ATR'].iloc[-2]) else np.nan
                prev_support = indicator_features['Support'].iloc[-2] if 'Support' in indicator_features.columns and len(indicator_features) >= 2 and not pd.isna(indicator_features['Support'].iloc[-2]) else np.nan
                prev_resistance = indicator_features['Resistance'].iloc[-2] if 'Resistance' in indicator_features.columns and len(indicator_features) >= 2 and not pd.isna(indicator_features['Resistance'].iloc[-2]) else np.nan

                # print(f"probability_tp_hit {probability_tp_hit} time_condition {time_condition} buy {tech_signal_buy} sell {tech_signal_sell}")

                # ทดสอบระบบ ส่งสัญญาณ Buy หรือ Sell
                # tech_signal_buy = True
                # tech_signal_sell = True

                # *** ใช้ Multi-class prediction เป็นหลักในการตัดสินใจ Signal ***
                # รวมกับเงื่อนไขทางเทคนิคเป็นตัวกรอง (ถ้าเปิดใช้งาน)

                print(f"\n🎯 SIGNAL DECISION PROCESS:")
                print(f"   Model Prediction: {predicted_signal} (Confidence: {probability_tp_hit:.4f})")
                print(f"   Technical Conditions: {'ENABLED' if ENABLE_TECHNICAL_CONDITIONS else 'DISABLED'}")

                if ENABLE_TECHNICAL_CONDITIONS:
                    # ใช้เงื่อนไขทางเทคนิคร่วมกับ Model Prediction
                    print(f"   Decision Logic: Model + Technical Conditions")

                    # Debug: แสดงค่าที่ใช้ในการตัดสินใจ
                    print(f"🔍 DEBUG Decision Values:")
                    print(f"   predicted_signal: '{predicted_signal}'")
                    print(f"   predicted_signal in ['BUY', 'STRONG_BUY']: {predicted_signal in ['BUY', 'STRONG_BUY']}")
                    print(f"   predicted_signal in ['SELL', 'STRONG_SELL']: {predicted_signal in ['SELL', 'STRONG_SELL']}")
                    print(f"   tech_signal_buy: {tech_signal_buy}")
                    print(f"   tech_signal_sell: {tech_signal_sell}")

                    if predicted_signal in ["BUY", "STRONG_BUY"] and tech_signal_buy:
                        signal = "BUY"
                        print(f"🎯 FINAL DECISION: {signal} (Model: {predicted_signal}, Tech: ✅)")
                    elif predicted_signal in ["SELL", "STRONG_SELL"] and tech_signal_sell:
                        signal = "SELL"
                        print(f"🎯 FINAL DECISION: {signal} (Model: {predicted_signal}, Tech: ✅)")
                    elif predicted_signal == "HOLD":
                        signal = "HOLD"
                        print(f"🎯 FINAL DECISION: {signal} (Model predicted HOLD)")
                    else:
                        # ถ้า model ทำนาย BUY/SELL แต่เงื่อนไขทางเทคนิคไม่ผ่าน ให้ HOLD
                        signal = "HOLD"
                        waiting_for = f"Waiting for tech conditions (Model: {predicted_signal})"
                        print(f"🎯 FINAL DECISION: {signal} (Model: {predicted_signal}, Tech: ❌) - {waiting_for}")
                        print(f"🔍 DEBUG: Why HOLD? BUY_check: {predicted_signal in ['BUY', 'STRONG_BUY']} & {tech_signal_buy}, SELL_check: {predicted_signal in ['SELL', 'STRONG_SELL']} & {tech_signal_sell}")
                else:
                    # ใช้เฉพาะ Model Prediction (ไม่ตรวจสอบเงื่อนไขทางเทคนิค)
                    print(f"   Decision Logic: Model Prediction ONLY (Technical conditions ignored)")
                    if predicted_signal in ["BUY", "STRONG_BUY"]:
                        signal = "BUY"
                        print(f"🎯 FINAL DECISION: {signal} (Model: {predicted_signal}, Tech: DISABLED)")
                    elif predicted_signal in ["SELL", "STRONG_SELL"]:
                        signal = "SELL"
                        print(f"🎯 FINAL DECISION: {signal} (Model: {predicted_signal}, Tech: DISABLED)")
                    else:
                        signal = "HOLD"
                        print(f"🎯 FINAL DECISION: {signal} (Model predicted HOLD, Tech: DISABLED)")

                if signal == "BUY":

                    # ใช้ข้อมูลจากแท่งล่าสุด (index -1) สำหรับ Entry Price
                    # ถ้า MT5 ส่งบาร์ล่าสุดที่ **ปิดแล้ว** มา และ EA เทรดเมื่อบาร์ปิด ให้ใช้ df_ft['Close'].iloc[-1]
                    # ถ้า MT5 ส่งบาร์ที่ **กำลังก่อตัว** มา และ EA เทรดที่ราคา Open ของบาร์นั้น ให้ใช้ df_ft['Open'].iloc[-1]
                    # สมมติว่าใช้ Close ของบาร์ล่าสุดที่ปิดแล้วเป็นราคา Entry
                    entry_price = df_ft['Open'].iloc[-1] + (symbol_spread + 2) * symbol_points
                    
                    # คำนวณ SL สำหรับ Buy
                    # sl_atr: Entry Price - Multiplier * ATR(Previous Bar)
                    sl_atr = entry_price - input_stop_loss_atr * prev_atr if not pd.isna(prev_atr) else np.inf

                    # sl_prev_bars: Minimum Low of the last 3 bars (excluding current) or Entry Price - 2*Points
                    # Corresponds to df['Low'].iloc[i-3:i].min() in the original logic when i is the last index
                    # This means Low at indices -4, -3, -2 relative to the latest bar (-1)
                    if len(df_ft) >= 4: # Ensure we have at least 4 bars to access -4, -3, -2
                        sl_prev_bars = min(df_ft['Low'].iloc[-4:-1].min(), entry_price - 2*symbol_points)
                    else:
                        sl_prev_bars = entry_price - 2*symbol_points # Fallback if not enough bars for iloc slicing

                    # sl_support: Previous bar's Support level
                    sl_support = prev_support if not pd.isna(prev_support) else np.inf

                    # SL Final for BUY: The highest of the candidates (closest to entry)
                    sl_candidates = [sl_atr, sl_prev_bars, sl_support]

                    # กรองค่า np.inf ออกก่อนหา max
                    valid_sl_candidates = [x for x in sl_candidates if not np.isinf(x)]
                    if valid_sl_candidates:
                        sl_price = max(valid_sl_candidates)
                        sl_price -= (2) * symbol_points
                    else: # ถ้าทุกคอลัมน์ที่ใช้คำนวณ SL เป็น NaN
                        sl_price = entry_price - 100 * symbol_points # ตั้ง SL Default เช่น 10 points จาก Entry
                        sl_price -= (2) * symbol_points
                        print(f"Warning: Indicators/Past Bars were NaN for BUY SL calculation for {symbol} ({timeframe}). Using default SL.")

                    # คำนวณ TP สำหรับ Buy: Entry Price + (Entry Price - Calculated SL) * Ratio
                    tp_price = entry_price + (entry_price - sl_price) * input_take_profit

                    # ใช้ Resistance ในการปรับ TP (เลือกค่าต่ำสุดระหว่าง TP คำนวณกับ Resistance)
                    # if not pd.isna(prev_resistance) and prev_resistance > entry_price: # ตรวจสอบว่า Resistance สูงกว่า Entry Price ก่อนใช้
                    #     tp_price = min(tp_price, prev_resistance)

                    # ตรวจสอบ TP ขั้นต่ำ (ต้องสูงกว่า Entry Price)
                    if tp_price <= entry_price:
                        tp_price = entry_price + 2*symbol_points # ตั้ง TP ขั้นต่ำเพื่อหลีกเลี่ยง TP <= Entry และให้มี Gap

                    open_price_1 = df_ft['Open'].iloc[-1] # คือแท่งปัจจุบัน (ยังไม่จบ)
                    close_price_1 = df_ft['Close'].iloc[-1]
                    open_price_2 = df_ft['Open'].iloc[-2] # แท่งก่อนหน้า (จบไปแล้ว)
                    close_price_2 = df_ft['Close'].iloc[-2]

                    fmt = f".{symbol_digits}f"
                    if USE_MULTI_MODEL_ARCHITECTURE:
                        print(f"\n🤖 Multi-Model: Model={model_used if 'model_used' in locals() else 'unknown'} Signal={predicted_signal} Confidence={probability_tp_hit:.4f}")
                    else:
                        print(f"\n📊 Single Model: Class={prediction_class} Signal={predicted_signal} Confidence={probability_tp_hit:.4f}")
                    print(f"Final Decision: {signal} (time_condition={time_condition})")
                    print(f"SL {sl_price:{fmt}} TP {tp_price:{fmt}} RR={(tp_price-entry_price)/(entry_price-sl_price):0.3f}")
                    print(
                        f"symbol {symbol} digit {symbol_digits} spread {symbol_spread} point {symbol_points} : "
                        f"(i-1) op {open_price_1:{fmt}} cl {close_price_1:{fmt}} : "
                        f"(i-2) op {open_price_2:{fmt}} cl {close_price_2:{fmt}} : "
                        f"\nentry {entry_price:{fmt}} : "
                        f"cal atr {prev_atr:{fmt}} support {prev_support:{fmt}} resistance {prev_resistance:{fmt}} : "
                        f"atr {sl_atr:{fmt}} bars {sl_prev_bars:{fmt}} support {sl_support:{fmt}} : "
                        f"\nop {entry_price:{fmt}} sl {sl_price:{fmt}} tp {tp_price:{fmt}} rr {(tp_price-entry_price)/(entry_price-sl_price):0.3f}"
                    )

                    if Telegram_Open:
                        fmt = f".{symbol_digits}f"
                        if USE_MULTI_MODEL_ARCHITECTURE:
                            MESSAGE = (
                                f"Python Signal : {symbol} {timeframe} : {signal} {probability_tp_hit:.4f}\n"
                                f"Multi-Model: {predicted_signal} (Model: {model_used if 'model_used' in locals() else 'unknown'})\n"
                                f"OP {entry_price:{fmt}} SL {sl_price:{fmt}} TP {tp_price:{fmt}} RR {(tp_price-entry_price)/(entry_price-sl_price):0.3f}"
                            )
                        else:
                            MESSAGE = (
                                f"Python Signal : {symbol} {timeframe} : {signal} {probability_tp_hit:.4f}\n"
                                f"Single Model: {predicted_signal} (Class {prediction_class})\n"
                                f"OP {entry_price:{fmt}} SL {sl_price:{fmt}} TP {tp_price:{fmt}} RR {(tp_price-entry_price)/(entry_price-sl_price):0.3f}"
                            )
                        payload = {'chat_id': CHAT_ID, 'text': MESSAGE}
                        response = requests.post(url, data=payload)

                        # print(response.json())

                if signal == "SELL":

                    entry_price = df_ft['Open'].iloc[-1]

                    # คำนวณ SL สำหรับ Sell
                    # sl_atr: Entry Price + Multiplier * ATR(Previous Bar)
                    sl_atr = entry_price + input_stop_loss_atr * prev_atr if not pd.isna(prev_atr) else -np.inf

                    # sl_prev_bars: Maximum High of the last 3 bars (excluding current) or Entry Price + 2*Points
                    # Corresponds to df['High'].iloc[i-3:i].max() in the original logic
                    if len(df_ft) >= 4: # Ensure we have at least 4 bars to access -4, -3, -2
                        sl_prev_bars = max(df_ft['High'].iloc[-4:-1].max(), entry_price + 2*symbol_points)
                    else:
                        sl_prev_bars = entry_price + 2*symbol_points # Fallback if not enough bars for iloc slicing

                    # sl_resistance: Previous bar's Resistance level
                    sl_resistance = prev_resistance if not pd.isna(prev_resistance) else -np.inf

                    # SL Final for SELL: The lowest of the candidates (closest to entry)
                    sl_candidates = [sl_atr, sl_prev_bars, sl_resistance]
                    # กรองค่า -np.inf ออกก่อนหา min
                    valid_sl_candidates = [x for x in sl_candidates if not np.isinf(x)]
                    if valid_sl_candidates:
                        sl_price = min(valid_sl_candidates)
                        sl_price += (symbol_spread + 2) * symbol_points
                    else: # ถ้าทุกคอลัมน์ที่ใช้คำนวณ SL เป็น NaN
                        sl_price = entry_price + 100 * symbol_points # ตั้ง SL Default
                        sl_price += (symbol_spread + 2) * symbol_points
                        print(f"Warning: Indicators/Past Bars were NaN for SELL SL calculation for {symbol} ({timeframe}). Using default SL.")

                    # คำนวณ TP สำหรับ Sell: Entry Price - (Calculated SL - Entry Price) * Ratio
                    tp_price = entry_price - (sl_price - entry_price) * input_take_profit

                    # ใช้ Support ในการปรับ TP (เลือกค่าสูงสุดระหว่าง TP คำนวณกับ Support)
                    # if not pd.isna(prev_support) and prev_support < entry_price: # ตรวจสอบว่า Support ต่ำกว่า Entry Price ก่อนใช้
                    #     tp_price = max(tp_price, prev_support)

                    # ตรวจสอบ TP ขั้นต่ำ (ต้องต่ำกว่า Entry Price)
                    if tp_price >= entry_price:
                        tp_price = entry_price - 2*symbol_points

                    open_price_1 = df_ft['Open'].iloc[-1] # คือแท่งปัจจุบัน (ยังไม่จบ)
                    close_price_1 = df_ft['Close'].iloc[-1]
                    open_price_2 = df_ft['Open'].iloc[-2] # แท่งก่อนหน้า (จบไปแล้ว)
                    close_price_2 = df_ft['Close'].iloc[-2]

                    fmt = f".{symbol_digits}f"
                    if USE_MULTI_MODEL_ARCHITECTURE:
                        print(f"\n🤖 Multi-Model: Model={model_used if 'model_used' in locals() else 'unknown'} Signal={predicted_signal} Confidence={probability_tp_hit:.4f}")
                    else:
                        print(f"\n📊 Single Model: Class={prediction_class} Signal={predicted_signal} Confidence={probability_tp_hit:.4f}")
                    print(f"Final Decision: {signal} (time_condition={time_condition})")
                    print(f"SL {sl_price:{fmt}} TP {tp_price:{fmt}} RR={(entry_price-tp_price)/(sl_price-entry_price):0.3f}")
                    print(
                        f"symbol {symbol} digit {symbol_digits} spread {symbol_spread} point {symbol_points} : "
                        f"(i-1) op {open_price_1:{fmt}} cl {close_price_1:{fmt}} : "
                        f"(i-2) op {open_price_2:{fmt}} cl {close_price_2:{fmt}} : "
                        f"\nentry {entry_price:{fmt}} : "
                        f"cal atr {prev_atr:{fmt}} support {prev_support:{fmt}} resistance {prev_resistance:{fmt}} : "
                        f"atr {sl_atr:{fmt}} bars {sl_prev_bars:{fmt}} resistance {sl_resistance:{fmt}} : "
                        f"\nop {entry_price:{fmt}} sl {sl_price:{fmt}} tp {tp_price:{fmt}} rr {(entry_price-tp_price)/(sl_price-entry_price):0.3f}"
                    )

                    if Telegram_Open:
                        fmt = f".{symbol_digits}f"
                        if USE_MULTI_MODEL_ARCHITECTURE:
                            MESSAGE = (
                                f"Python Signal : {symbol} {timeframe} : {signal} {probability_tp_hit:.4f}\n"
                                f"Multi-Model: {predicted_signal} (Model: {model_used if 'model_used' in locals() else 'unknown'})\n"
                                f"OP {entry_price:{fmt}} SL {sl_price:{fmt}} TP {tp_price:{fmt}} RR {(entry_price-tp_price)/(sl_price-entry_price):0.3f}"
                            )
                        else:
                            MESSAGE = (
                                f"Python Signal : {symbol} {timeframe} : {signal} {probability_tp_hit:.4f}\n"
                                f"Single Model: {predicted_signal} (Class {prediction_class})\n"
                                f"OP {entry_price:{fmt}} SL {sl_price:{fmt}} TP {tp_price:{fmt}} RR {(entry_price-tp_price)/(sl_price-entry_price):0.3f}"
                            )
                        payload = {'chat_id': CHAT_ID, 'text': MESSAGE}
                        response = requests.post(url, data=payload)
                        print(response.json())

                # *** อัปเดตการแสดงผลสำหรับ HOLD ***
                if signal == "HOLD":
                    if USE_MULTI_MODEL_ARCHITECTURE:
                        print(f"\n🤖 Multi-Model: Model={model_used if 'model_used' in locals() else 'unknown'} Signal={predicted_signal} Confidence={probability_tp_hit:.4f}")
                    else:
                        print(f"\n📊 Single Model: Class={prediction_class} Signal={predicted_signal} Confidence={probability_tp_hit:.4f}")
                    print(f"Final Decision: {signal} ({waiting_for if waiting_for else 'No strong signal'})")

                    waiting_for_list = []

                    # print(f"\nprobability_tp_hit {probability_tp_hit} time_condition {time_condition} signal {signal}")

                    # ตรวจสอบว่าขาดเงื่อนไขอะไรไปบ้างสำหรับ BUY
                    if not tech_signal_buy:
                        missing_buy_conditions = [cond for cond, is_true in tech_signal_buy_conditions.items() if not is_true]
                        if missing_buy_conditions:
                            waiting_for_list.append(f"BUY : {', '.join(missing_buy_conditions)}")

                    # ตรวจสอบว่าขาดเงื่อนไขอะไรไปบ้างสำหรับ SELL
                    if not tech_signal_sell:
                        missing_sell_conditions = [cond for cond, is_true in tech_signal_sell_conditions.items() if not is_true]
                        if missing_sell_conditions:
                            waiting_for_list.append(f"SELL : {', '.join(missing_sell_conditions)}")

                    # print(f"[{datetime.datetime.now()}] {timeframe} {symbol} Model Confidence High ({probability_tp_hit:.4f}) but Technicals not met. Waiting for: {waiting_for}")

                    # if not waiting_for_list:
                    #     # กรณีที่โมเดลผ่าน แต่ไม่ BUY/SELL อาจจะมีเงื่อนไขอื่นๆ ที่ซับซ้อน หรือเงื่อนไขเทคนิคขัดแย้งกันเอง
                    #     # แต่ตามโครงสร้างโค้ดน่าจะเข้าเงื่อนไขใดเงื่อนไขหนึ่งถ้า tech_signal_buy หรือ tech_signal_sell เป็น True
                    #     # ถ้ามาถึงตรงนี้โดยที่ waiting_for_list ว่าง อาจจะต้องตรวจสอบ logic หรือเงื่อนไขเพิ่มเติม
                    #     waiting_for = "Technical conditions not met, unclear what specific condition is missing based on current rules."
                    #     print(f"[{datetime.datetime. द्रव()}] Model Confidence High ({probability_tp_hit:.4f}) but Technicals not met. Reason unclear based on defined conditions.")
                    # else:
                    #     waiting_for = " / ".join(waiting_for_list)
                    #     print(f"[{datetime.datetime.now()}] Model Confidence High ({probability_tp_hit:.4f}) but Technicals not met. Waiting for: {waiting_for}")

                # print(f"[{datetime.datetime.now()}] Model confidence ({probability_tp_hit:.4f}) >= {model_confidence_threshold:.2f}. Generated Signal: {signal}")

            else:
                # โมเดลเห็นศักยภาพ แต่ไม่อยู่ในเงื่อนไขเวลาที่กำหนด
                signal = "HOLD"
                waiting_for = "Time condition (6 <= Entry_Hour <= 20) not met."
                print(f"[{datetime.datetime.now()}] Model Confidence High ({probability_tp_hit:.4f}), Technicals checked, but Time condition not met. Waiting for: {waiting_for}")

        else: 
            # โมเดลไม่เห็นศักยภาพ (Probability TP Hit ต่ำกว่า Threshold)
            signal = "HOLD"
            waiting_for = f"Model confidence ({probability_tp_hit:.4f}) below threshold ({model_confidence_threshold:.2f})."
            print(f"[{datetime.datetime.now()}] Model confidence ({probability_tp_hit:.4f}) below threshold ({model_confidence_threshold:.2f}). No signal potential detected.")

        # --- เพิ่ม: Print Signal และ Confidence Probability ที่คำนวณได้ ---
        # print(f"\n[{datetime.datetime.now()}] Prediction Result for {symbol} ({timeframe}):")
        # print(f"  Signal: {signal}")
        # print(f"  Confidence (Probability TP Hit): {probability_tp_hit:.4f}")
        # if waiting_for: # แสดงเหตุผลที่รอ ถ้ามีการระบุไว้
        #     print(f"  Waiting Reason: {waiting_for}")
        # print("------------------------------------------------------------")

        if waiting_for: # แสดงเหตุผลที่รอ ถ้ามีการระบุไว้
            print(f"[{datetime.datetime.now()}] {timeframe} {symbol} Signal {signal} Confidence {probability_tp_hit:.4f} Waiting {waiting_for}")
        else:
            print(f"[{datetime.datetime.now()}] {timeframe} {symbol} Signal {signal} Confidence {probability_tp_hit:.4f}")

        # --- จบส่วน Print ---

        # --- ส่วนส่ง Signal หรือข้อมูลกลับไปที่ MT5 (ต้องเพิ่ม Logic จริงๆ ที่นี่) ---
        # ... (ไม่ได้เพิ่ม Logic ส่งกลับในโค้ดนี้) ...

        # --- ส่วนที่เพิ่ม: Store the calculated signal and confidence ---
        # Store the signal and confidence in the global dictionary
        with signals_lock: # <--- บรรทัดนี้เริ่มต้นส่วนที่บันทึก Signal โดยใช้ Lock
            signals_key = (symbol, timeframe) # ใช้ symbol และ timeframe เป็น key
            latest_signals_data[signals_key] = { # <--- บรรทัดนี้บันทึกข้อมูลลง Global Variable
                "symbol": symbol,
                "timeframe": timeframe, # Store enum
                "timeframe_str": timeframe_map.get(timeframe, 'UNKNOWN'), # Store string for easy reading
                "signal": signal,
                "class": predicted_signal,  # เพิ่ม class level (STRONG_BUY, BUY, HOLD, SELL, STRONG_SELL)
                "confidence": float(probability_tp_hit), # Ensure it's a standard float
                "entry_price": float(entry_price),
                "sl_price": float(sl_price),
                "tp_price": float(tp_price),
                "best_entry": float(entry_price),  # ใช้ entry_price เป็น best_entry
                "nBars_SL": int(num_nBars_SL),  # จำนวนแท่งสำหรับ SL
                "threshold": float(model_confidence_threshold),  # เกณฑ์การตัดสินใจ
                "time_filters": f"Days:{days_filter},Hours:{hours_filter}",  # การกรองเวลา
                "spread": int(symbol_spread),  # ค่า spread
                # *** เพิ่มข้อมูล Multi-Model Analysis ***
                "market_condition": market_condition if 'market_condition' in locals() else "unknown",
                "action_type": action_type if 'action_type' in locals() else "none",
                "scenario_used": scenario_name if 'scenario_name' in locals() else "none",
                # *** เพิ่มข้อมูลการวิเคราะห์ทั้ง 2 ระบบ (แปลงเป็น JSON serializable) ***
                "analysis_summary": prepare_analysis_summary(analysis_results) if 'analysis_results' in locals() else {},
                "timestamp": latest_bar_dt if 'latest_bar_dt' in locals() else datetime.datetime.now(MT5_TIMEZONE) # Use bar time if available, otherwise current time
            }
        print(f"[{datetime.datetime.now()}] Stored latest signal for {symbol} (enum: {timeframe}): {signal} ({probability_tp_hit:.4f})") # <--- บรรทัดนี้ Print ยืนยันการบันทึก

    # ... (โค้ดส่วนจัดการ Error ใน process_data_and_trade - ควรมี Logic การบันทึก Error Signal ด้วย) ...
    except Exception as e:
        # Enhanced error logging
        signal = "ERROR"
        probability_tp_hit = 0.0
        entry_price = 0.0
        sl_price = 0.0
        tp_price = 0.0

        # Store ERROR signal in case of failure
        with signals_lock:
            signals_key = (symbol, timeframe)
            latest_signals_data[signals_key] = {
            "symbol": symbol,
            "timeframe": timeframe,
            "timeframe_str": timeframe_map.get(timeframe, 'UNKNOWN'),
            "signal": signal,
            "confidence": float(probability_tp_hit),
            "entry_price": float(entry_price),
            "sl_price": float(sl_price),
            "tp_price": float(tp_price),
            "timestamp": latest_bar_dt if 'latest_bar_dt' in locals() else datetime.datetime.now(MT5_TIMEZONE)
            }
        print(f"[{datetime.datetime.now()}] ❌ ERROR in process_data_and_trade for {symbol} M{timeframe}")
        print(f"[{datetime.datetime.now()}] ⚠️ Exception: {str(e)}")
        print(f"[{datetime.datetime.now()}] 📊 Error details:")
        import traceback
        traceback.print_exc()

        # บันทึก error ลงไฟล์
        try:
            with open('server_error.log', 'a', encoding='utf-8') as f:
                f.write(f"\n[{datetime.datetime.now()}] ERROR in {symbol} M{timeframe}:\n")
                f.write(f"Exception: {str(e)}\n")
                f.write(f"Traceback:\n")
                traceback.print_exc(file=f)
                f.write("\n" + "="*50 + "\n")
        except:
            pass

    print(f"[{datetime.datetime.now()}] Finished processing for {symbol} ({timeframe}).")

# --- HTTP Flask Route to receive data ---
@app.route('/data', methods=['POST'])
def receive_data():
    """Receives JSON data (batch of bars) from MT5 EA via HTTP POST.
        Processes the data and returns the latest processed signal if available.
    """

    try:
        print(f"\n[{datetime.datetime.now()}] 📨 Received HTTP POST request")
        # --- เปลี่ยนการรับข้อมูล: คาดหวัง JSON Object ที่มี key เป็น symbol, timeframe_str, และ bars (เป็น Array) ---
        data = request.get_json(force=True, silent=False)
        print(f"[{datetime.datetime.now()}] 📊 Data received: symbol={data.get('symbol')}, timeframe_str={data.get('timeframe_str')}, bars_count={len(data.get('bars', []))}")

        # ดึงข้อมูลพื้นฐาน
        symbol = data.get('symbol')
        timeframe_str = data.get('timeframe_str')
        bar_time_ts = data.get('time')
        bar_open = data.get('open')
        bar_high = data.get('high')
        bar_low = data.get('low')
        bar_close = data.get('close')
        bar_volume = data.get('tick_volume')
        bars_list = data.get('bars') # <--- ดึง Array ของบาร์ออกมา

        # ตรวจสอบข้อมูลที่ได้รับ
        if not all([symbol, timeframe_str, bars_list]) or not isinstance(bars_list, list) or len(bars_list) == 0:
            print("Received incomplete or incorrectly formatted data.")
            print(f"Received data: {data}")
            return jsonify({"status": "ERROR", "message": "Incomplete or incorrectly formatted data received."}), 400

        # --- แปลง timeframe_str เป็น enum ---
        timeframe_int = timeframe_code_map.get(timeframe_str) # timeframe str PERIOD_H1 enum 60
        if timeframe_int is None:
            print(f"Invalid or unknown timeframe string received: {timeframe_str}")
            return jsonify({"status": "ERROR", "message": f"Invalid or unknown timeframe {timeframe_str}"}), 400

        # --- สร้าง DataFrame จาก List ของ Bar Objects ---
        try:
            print(f"[{datetime.datetime.now()}] 🔄 Creating DataFrame from {len(bars_list)} bars")
            # แปลง list ของ dict เป็น DataFrame
            df_batch = pd.DataFrame(bars_list)
            print(f"[{datetime.datetime.now()}] ✅ DataFrame created: {df_batch.shape}")
            print(f"[{datetime.datetime.now()}] 📊 Columns: {list(df_batch.columns)}")

            # ตรวจสอบว่าคอลัมน์ที่จำเป็นมีครบ
            required_cols = ['time', 'open', 'high', 'low', 'close', 'tick_volume']
            missing_cols = [col for col in required_cols if col not in df_batch.columns]
            if missing_cols:
                print(f"❌ Missing required columns in received bar data: {missing_cols}")
                return jsonify({"status": "ERROR", "message": "Missing required bar data columns."}), 400
            print(f"[{datetime.datetime.now()}] ✅ All required columns present")

            # แปลง Timestamp (integer) เป็น Datetime Objects และตั้งเป็น Index
            print(f"[{datetime.datetime.now()}] 🕒 Converting timestamps to datetime")
            # ใช้ unit='s' เพื่อระบุว่าเป็น Unix timestamp (วินาที)
            df_batch['time'] = pd.to_datetime(df_batch['time'], unit='s', utc=True).dt.tz_convert(MT5_TIMEZONE) # แปลงเป็น Timezone ของ MT5 Server
            df_batch.set_index('time', inplace=True)
            print(f"[{datetime.datetime.now()}] ✅ Timestamps converted, time range: {df_batch.index[0]} to {df_batch.index[-1]}")

            # *** ส่วนที่เพิ่ม: เรียงลำดับ Index (เวลา) จากน้อยไปมาก (เก่าไปใหม่) ***
            df_batch.sort_index(ascending=True, inplace=True) # <--- เพิ่มบรรทัดนี้
            print(f"[{datetime.datetime.now()}] ✅ Data sorted by time")

            # เปลี่ยนชื่อคอลัมน์ tick_volume เป็น volume และเปลี่ยนเป็นตัวพิมพ์เล็ก
            df_batch.rename(columns={'tick_volume': 'volume'}, inplace=True)
            df_batch.columns = df_batch.columns.str.lower() # เปลี่ยนชื่อคอลัมน์ราคาเป็นตัวพิมพ์เล็กทั้งหมด

            # ตรวจสอบว่า DataFrame ถูกสร้างขึ้นมาถูกต้อง
            if df_batch.empty:
                print("Received bar list is empty after processing.")
                return jsonify({"status": "ERROR", "message": "Received bar data is empty."}), 400

            # Print ข้อมูลที่ได้รับ Batch แรกและสุดท้ายเพื่อตรวจสอบ (ตอนนี้ head จะเป็นแท่งเก่าสุด, tail จะเป็นแท่งใหม่สุด)
            # print(f"[{datetime.datetime.now()}] Received batch of {len(df_batch)} bars for {symbol} {timeframe_str}. Time range: {df_batch.index.min()} to {df_batch.index.max()}")
            # print(f"\nข้อมูลดิบที่โหลดและ แปลงเป็นตัวเลข (เรียงตามเวลา):") # เปลี่ยนข้อความ Print
            # print("\n✅ ข้อมูลดิบที่โหลด : df_batch")
            # print(df_batch.info())
            # print(df_batch)

        except Exception as e:
            print(f"Error processing received bar list into DataFrame: {e}")
            traceback.print_exc()
            return jsonify({"status": "ERROR", "message": f"Error processing bar data: {e}"}), 400


        # --- อัปเดต market_data_store ด้วย Batch Data ---
        cleaned_symbol = symbol.replace('#', '')
        key = (cleaned_symbol, timeframe_int)

        with data_lock:
            if key not in market_data_store:
                print(f"\n[{datetime.datetime.now()}] Initializing data store for {cleaned_symbol} ({timeframe_str})")
                # สร้าง DF เปล่าด้วยคอลัมน์ตัวพิมพ์เล็กตามที่ MQL5 ส่งมา
                market_data_store[key] = pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume'])
                market_data_store[key].index.name = 'time'

            # *** วิธีที่ 1 (แนะนำสำหรับตอนนี้): แทนที่ข้อมูลเดิมด้วย Batch ใหม่ทั้งหมด ***
            # วิธีนี้ง่ายที่สุดในการเริ่มต้นและทำให้แน่ใจว่ามีข้อมูลล่าสุด
            # ข้อเสีย: ถ้า Batch ไม่ครบถ้วนตามที่ Python ต้องการ จะทำให้คำนวณ Indicator ไม่ได้
            # คุณต้องแน่ใจว่า MQL5 ส่งข้อมูลย้อนหลังครบจำนวน required_bars เสมอ
            required_bars = 210 # *** ต้องตรงกับ BARS_FOR_PYTHON ใน MQL5 ***
            if len(df_batch) < required_bars:
                print(f"Warning: Received batch size ({len(df_batch)}) is less than required ({required_bars}). Processing with available data.")
                # อาจจะตัดสินใจไม่ประมวลผล หรือ ประมวลผลเท่าที่มีก็ได้ ขึ้นอยู่กับ Logic
                # ถ้าไม่ประมวลผล ให้ return ไปเลย
                # return jsonify({"status": "WARNING", "message": f"Received incomplete batch ({len(df_batch)} bars)."}), 200
                # ถ้าประมวลผลต่อ ก็ไปขั้นตอนต่อไป

            # แทนที่ข้อมูลทั้งหมดใน store ด้วย batch ใหม่
            market_data_store[key] = df_batch.copy()
            print(f"[{datetime.datetime.now()}] Replaced data store for {cleaned_symbol} ({timeframe_str}) with {len(market_data_store[key])} bars from batch.")


            # *** วิธีที่ 2 (ซับซ้อนกว่า): รวมข้อมูล Batch เข้ากับข้อมูลเก่าใน store (ถ้ามี) ***
            # วิธีนี้ต้องจัดการกับข้อมูลที่ซ้ำกัน หรือเรียงลำดับข้อมูลให้ถูกต้อง
            # if not market_data_store[key].empty:
            #     # Concatenate และลบข้อมูลที่ซ้ำกัน (เก็บข้อมูลล่าสุด)
            #     combined_df = pd.concat([market_data_store[key], df_batch])
            #     market_data_store[key] = combined_df[~combined_df.index.duplicated(keep='last')].sort_index()
            # else:
            #     market_data_store[key] = df_batch.copy()

            # # จำกัดขนาดข้อมูลใน store
            # market_data_store[key] = market_data_store[key].tail(max(required_bars + 50, 500)) # เก็บอย่างน้อย 500 หรือตาม required_bars + buffer
            # print(f"[{datetime.datetime.now()}] Updated data store for {cleaned_symbol} ({timeframe_str}) with {len(market_data_store[key])} bars.")

        # --- เรียกใช้ฟังก์ชันประมวลผลใน Thread แยก ---
        print(f"[{datetime.datetime.now()}] 🚀 Starting data processing for {cleaned_symbol} M{timeframe_int}")
        # ตอนนี้ process_data_and_trade จะใช้ข้อมูลจาก market_data_store ที่เพิ่งอัปเดต
        # หรืออาจจะปรับ process_data_and_trade ให้รับ df_batch เข้ามาตรงๆ ก็ได้
        # แต่เพื่อให้เข้ากับโครงสร้างเดิม เราจะให้มันอ่านจาก store

        print(f"[{datetime.datetime.now()}] 🔄 Creating processing thread for {cleaned_symbol} M{timeframe_int}")
        processing_thread = threading.Thread(target=process_data_and_trade, args=(cleaned_symbol, timeframe_int))
        processing_thread.start()
        processing_thread.join()
        print(f"[{datetime.datetime.now()}] ✅ Processing thread completed for {cleaned_symbol} M{timeframe_int}")

        # --- ส่วนที่ดึง Signal และ Confidence จาก latest_signals_data และส่งกลับใน Response ---
        # ตอนนี้ เมื่อมาถึงส่วนนี้ การประมวลผลใน processing_thread เสร็จแล้ว
        # latest_signals_data ควรจะมีค่า Signal ล่าสุดของบาร์ที่เพิ่งได้รับ

        response_signal = "HOLD" # Default response
        response_class = "HOLD" # Default class level
        response_confidence = 0.0
        response_entry_price = 0.0
        response_sl_price = 0.0
        response_tp_price = 0.0
        response_best_entry = 0.0
        response_nBars_SL = 0
        response_threshold = 0.0
        response_time_filters = ""
        response_spread = 0
        response_message = "Data received and processing started. Signal processing in progress."
        response_signal_bar_timestamp = None # Timestamp ของแท่งที่เกี่ยวข้องกับ Signal ที่ส่งกลับ

        signals_key = (cleaned_symbol, timeframe_int)

        # ดึงข้อมูล Signal ล่าสุดออกมา โดยใช้ signals_lock
        with signals_lock:
            latest_sig = latest_signals_data.get(signals_key, None)

            if latest_sig:
                # response_signal = latest_sig["signal"]
                # response_confidence = latest_sig["confidence"]

                response_signal = latest_sig.get("signal", "HOLD") # ใช้ .get() เพื่อป้องกัน KeyError
                response_class = latest_sig.get("class", "HOLD") # ดึง class level
                response_confidence = latest_sig.get("confidence", 0.0) # [147]
                response_entry_price = latest_sig.get("entry_price", 0.0) # ดึง entry price ที่เก็บไว้
                response_sl_price = latest_sig.get("sl_price", 0.0) # ดึง SL ที่เก็บไว้
                response_tp_price = latest_sig.get("tp_price", 0.0) # ดึง TP ที่เก็บไว้
                response_best_entry = latest_sig.get("best_entry", 0.0) # ดึง best_entry ที่เก็บไว้
                response_nBars_SL = latest_sig.get("nBars_SL", 0) # ดึง nBars_SL ที่เก็บไว้
                response_threshold = latest_sig.get("threshold", 0.0) # ดึง threshold ที่เก็บไว้
                response_time_filters = latest_sig.get("time_filters", "") # ดึง time_filters ที่เก็บไว้
                response_spread = latest_sig.get("spread", 0) # ดึง spread ที่เก็บไว้

                if latest_sig["timestamp"]:
                    response_signal_bar_timestamp = latest_sig["timestamp"].timestamp()
                response_message = f"Latest signal: {response_signal} ({response_confidence:.4f}) for bar at {latest_sig['timestamp'].strftime('%Y.%m.%d %H:%M')}"
            # else: ถ้ายังไม่มี Signal ใน latest_signals_data ก็จะใช้ค่า Default

        # Return the JSON response including the latest known signal and confidence
        # MT5 EA จะต้อง Parse JSON response body นี้
        # ใน Response นี้ เราจะส่ง timestamp ของบาร์ล่าสุดที่ได้รับใน Batch กลับไปด้วย
        latest_received_bar_timestamp = df_batch.index.max().timestamp() if not df_batch.empty else None

        # return jsonify({
        #     "status": "OK",
        #     "message": response_message,
        #     "signal": response_signal, # ส่งค่า Signal กลับไปใน Response
        #     "confidence": response_confidence, # ส่งค่า Confidence กลับไปใน Response
        #     "bar_timestamp": latest_received_bar_timestamp, # Timestamp ของบาร์ล่าสุดใน Batch ที่ได้รับ
        #     "signal_bar_timestamp": response_signal_bar_timestamp # Timestamp ของแท่งที่ Signal ที่ส่งกลับใช้คำนวณ
        # }), 200

        # *** ส่วนที่เพิ่ม: สร้าง Dictionary ที่จะถูกแปลงเป็น JSON และ Print ออกมา ***

        symbol_info = symbol_info_map[cleaned_symbol]
        digits = symbol_info["Digits"]

        response_confidence = round(response_confidence, 4)
        response_entry_price = round(response_entry_price, digits)
        response_sl_price = round(response_sl_price, digits)
        response_tp_price = round(response_tp_price, digits)

        # *** ดึงข้อมูล Multi-Model Analysis จาก latest signal ***
        response_market_condition = "unknown"
        response_action_type = "none"
        response_scenario_used = "none"
        response_analysis_summary = {}

        # *** เพิ่มข้อมูลสำหรับแสดงผลทั้ง 2 ระบบ ***
        response_trend_following_threshold = 0.5
        response_trend_following_nbars = 6
        response_counter_trend_threshold = 0.5
        response_counter_trend_nbars = 6

        # *** เพิ่มข้อมูล confidence ของทั้ง 2 ระบบ ***
        response_trend_following_buy_confidence = 0.0
        response_trend_following_sell_confidence = 0.0
        response_counter_trend_buy_confidence = 0.0
        response_counter_trend_sell_confidence = 0.0

        if latest_sig:
            response_market_condition = latest_sig.get("market_condition", "unknown")
            response_action_type = latest_sig.get("action_type", "none")
            response_scenario_used = latest_sig.get("scenario_used", "none")
            response_analysis_summary = latest_sig.get("analysis_summary", {})

            # โหลดพารามิเตอร์ทั้ง 2 ระบบ
            try:
                tf_threshold = load_scenario_threshold(cleaned_symbol, timeframe_int, 'trend_following')
                tf_nbars = load_scenario_nbars(cleaned_symbol, timeframe_int, 'trend_following')
                ct_threshold = load_scenario_threshold(cleaned_symbol, timeframe_int, 'counter_trend')
                ct_nbars = load_scenario_nbars(cleaned_symbol, timeframe_int, 'counter_trend')

                if tf_threshold is not None:
                    response_trend_following_threshold = tf_threshold
                if tf_nbars is not None:
                    response_trend_following_nbars = tf_nbars
                if ct_threshold is not None:
                    response_counter_trend_threshold = ct_threshold
                if ct_nbars is not None:
                    response_counter_trend_nbars = ct_nbars

            except Exception as e:
                print(f"⚠️ Error loading dual system parameters: {e}")

            # *** ดึงข้อมูล confidence จาก analysis_summary ***
            if response_analysis_summary:
                try:
                    # Trend Following confidence
                    if 'trend_following' in response_analysis_summary:
                        tf_data = response_analysis_summary['trend_following']
                        if 'buy' in tf_data:
                            response_trend_following_buy_confidence = float(tf_data['buy'].get('confidence', 0.0))
                        if 'sell' in tf_data:
                            response_trend_following_sell_confidence = float(tf_data['sell'].get('confidence', 0.0))

                    # Counter Trend confidence
                    if 'counter_trend' in response_analysis_summary:
                        ct_data = response_analysis_summary['counter_trend']
                        if 'buy' in ct_data:
                            response_counter_trend_buy_confidence = float(ct_data['buy'].get('confidence', 0.0))
                        if 'sell' in ct_data:
                            response_counter_trend_sell_confidence = float(ct_data['sell'].get('confidence', 0.0))

                except Exception as e:
                    print(f"⚠️ Error extracting confidence values: {e}")

        response_payload = {
            "status": "OK",
            "message": response_message,
            "signal": response_signal,          # ค่า Signal ที่จะส่ง (BUY/SELL/HOLD)
            "class": response_class,            # ค่า Class Level ที่จะส่ง (STRONG_BUY/BUY/HOLD/SELL/STRONG_SELL)
            "confidence": response_confidence,   # ค่า Confidence ที่จะส่ง
            "bar_timestamp": latest_received_bar_timestamp, # Timestamp ของบาร์ล่าสุดใน Batch ที่ได้รับ
            "signal_bar_timestamp": response_signal_bar_timestamp, # Timestamp ของแท่งที่ Signal ที่ส่งกลับใช้คำนวณ
            "symbol": cleaned_symbol,          # *** เพิ่ม: Symbol ที่ใช้คำนวณ Signal ***
            "timeframe_str": timeframe_str,     # *** เพิ่ม: Timeframe String ที่ใช้คำนวณ Signal ***
            "entry_price": response_entry_price,
            "sl_price": response_sl_price, # เพิ่ม SL เข้าไป
            "tp_price": response_tp_price,  # เพิ่ม TP เข้าไป
            "best_entry": response_best_entry,  # เพิ่ม best_entry เข้าไป
            "nBars_SL": response_nBars_SL,      # เพิ่ม nBars_SL เข้าไป
            "threshold": response_threshold,    # เพิ่ม threshold เข้าไป
            "time_filters": response_time_filters, # เพิ่ม time_filters เข้าไป
            "spread": response_spread,           # เพิ่ม spread เข้าไป
            # *** เพิ่มข้อมูล Multi-Model Analysis ***
            "market_condition": response_market_condition,  # สถานการณ์ตลาด (uptrend/downtrend/sideways)
            "action_type": response_action_type,            # ประเภทการกระทำ (buy/sell/none)
            "scenario_used": response_scenario_used,        # scenario ที่ใช้ (trend_following/counter_trend/none)
            # *** เพิ่มข้อมูลทั้ง 2 ระบบสำหรับ MT5 Display ***
            "trend_following_threshold": response_trend_following_threshold,
            "trend_following_nbars": response_trend_following_nbars,
            "counter_trend_threshold": response_counter_trend_threshold,
            "counter_trend_nbars": response_counter_trend_nbars,
            # *** เพิ่มข้อมูล confidence ของทั้ง 2 ระบบ ***
            "trend_following_buy_confidence": response_trend_following_buy_confidence,
            "trend_following_sell_confidence": response_trend_following_sell_confidence,
            "counter_trend_buy_confidence": response_counter_trend_buy_confidence,
            "counter_trend_sell_confidence": response_counter_trend_sell_confidence,
            "analysis_summary": response_analysis_summary   # ผลการวิเคราะห์ทั้ง 2 ระบบ
        }

        print(f"\n[{datetime.datetime.now()}] Sending JSON Response to MT5: {response_payload}")

        return jsonify(response_payload), 200 # ใช้ response_payload แทน dictionary ที่สร้างตรงๆ

    except BadRequest as e:
        print(f"[{datetime.datetime.now()}] Received data but failed due to Bad Request: {e}")
        print(f"Raw request data (bytes): {request.data}")
        return jsonify({"status": "ERROR", "message": f"Bad Request: {e}"}), 400

    except Exception as e:
        print(f"[{datetime.datetime.now()}] Error processing request: {e}")
        traceback.print_exc()
        return jsonify({"status": "ERROR", "message": f"Server Error: {e}"}), 500

# --- Optional: Endpoint to Get Signal ---
# ... (ไม่ได้เพิ่มในโค้ดนี้) ...

# --- Main Execution ---
if __name__ == "__main__":
    # initialize_mt5() # Uncomment ถ้า Python ต้องใช้ mt5.py ในการส่งคำสั่งเทรดเอง

    # Setup logging to file
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('server_debug.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    print(f"🚀 Starting HTTP Server on http://{HTTP_HOST}:{HTTP_PORT}")
    print(f"🔄 Using Multi-Model Architecture: {USE_MULTI_MODEL_ARCHITECTURE}")
    print(f"📁 Model base path: {MODEL_BASE_PATH}")
    print(f"📁 Threshold base path: {THRESHOLD_BASE_PATH}")
    print(f"📝 Debug log will be saved to: server_debug.log")

    try:
        app.run(host=HTTP_HOST, port=HTTP_PORT, debug=False)
    except Exception as e:
        print(f"Failed to start Flask server: {e}")
        traceback.print_exc()

    print("Server stopped.")
    # mt5.shutdown() # Uncomment ถ้ามีการ initialize_mt5
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขปัญหา AUC ใน multiclass classification
"""

import os
import sys
import pandas as pd
import numpy as np

# เพิ่ม path สำหรับ import
sys.path.append('.')

def create_multiclass_test_data():
    """สร้างข้อมูลทดสอบ multiclass ที่มีปัญหา class imbalance"""
    np.random.seed(42)
    n_samples = 1000
    
    # สร้างข้อมูล features
    data = {
        'Date': pd.date_range('2023-01-01', periods=n_samples, freq='30min').strftime('%Y.%m.%d'),
        'Time': pd.date_range('2023-01-01', periods=n_samples, freq='30min').strftime('%H:%M'),
        'Open': np.random.uniform(1800, 2000, n_samples),
        'High': np.random.uniform(1800, 2000, n_samples),
        'Low': np.random.uniform(1800, 2000, n_samples),
        'Close': np.random.uniform(1800, 2000, n_samples),
        'Volume': np.random.randint(100, 1000, n_samples),
    }
    
    # สร้าง features เพิ่มเติม
    for i in range(20):
        data[f'Feature_{i}'] = np.random.uniform(-1, 1, n_samples)
    
    df = pd.DataFrame(data)
    
    # สร้าง target multiclass ที่มี class imbalance รุนแรง
    # เพื่อจำลองปัญหาที่เกิดขึ้นจริง
    df['target'] = np.random.choice([0, 1, 2, 3, 4], n_samples, p=[0.6, 0.05, 0.25, 0.05, 0.05])
    
    return df

def test_auc_calculation():
    """ทดสอบการคำนวณ AUC ที่แก้ไขแล้ว"""
    
    print("🧪 ทดสอบการแก้ไขปัญหา AUC ใน multiclass classification")
    print("="*70)
    
    try:
        # Import ฟังก์ชันที่ต้องการ
        from python_LightGBM_16_Signal import train_scenario_model, USE_MULTI_MODEL_ARCHITECTURE
        
        print(f"📊 USE_MULTI_MODEL_ARCHITECTURE = {USE_MULTI_MODEL_ARCHITECTURE}")
        
        if not USE_MULTI_MODEL_ARCHITECTURE:
            print("❌ กรุณาเปิด USE_MULTI_MODEL_ARCHITECTURE = True")
            return
        
        # สร้างข้อมูลทดสอบที่มี class imbalance
        print("📊 สร้างข้อมูลทดสอบ multiclass ที่มี class imbalance...")
        df = create_multiclass_test_data()
        
        # แยกข้อมูล
        features = [col for col in df.columns if col.startswith('Feature_') or col in ['Open', 'High', 'Low', 'Close']]
        X = df[features]
        y = df['target']
        
        print(f"✅ ข้อมูลทดสอบ: {len(X)} samples, {len(features)} features")
        print(f"📊 Class distribution: {y.value_counts().sort_index().to_dict()}")
        print(f"📊 Unique classes: {sorted(y.unique())}")
        
        # คำนวณ class imbalance ratio
        class_counts = y.value_counts().sort_index()
        max_count = class_counts.max()
        min_count = class_counts.min()
        imbalance_ratio = max_count / min_count
        print(f"📊 Class imbalance ratio: {imbalance_ratio:.2f}:1")
        
        # ทดสอบการเทรน scenario model
        scenarios = ['trend_following', 'counter_trend']
        
        for scenario in scenarios:
            print(f"\n🤖 ทดสอบ scenario: {scenario}")
            print("-" * 50)
            
            try:
                result = train_scenario_model(
                    X=X,
                    y=y,
                    scenario_name=scenario,
                    symbol='AUDUSD',  # ใช้ symbol ใหม่
                    timeframe=30
                )
                
                if result:
                    print(f"✅ {scenario} training สำเร็จ")
                    
                    # ตรวจสอบ metrics ที่ได้
                    accuracy = result.get('accuracy', 0)
                    f1_score = result.get('f1_score', 0)
                    auc = result.get('auc', 0)
                    
                    print(f"   📊 Accuracy: {accuracy:.4f}")
                    print(f"   📊 F1 Score: {f1_score:.4f}")
                    print(f"   📊 AUC: {auc:.4f}")
                    
                    # ตรวจสอบว่า AUC มีค่าที่สมเหตุสมผล
                    if auc > 0 and auc <= 1:
                        print(f"   ✅ AUC อยู่ในช่วงที่ถูกต้อง (0-1)")
                    else:
                        print(f"   ⚠️ AUC อยู่นอกช่วงที่ถูกต้อง")
                    
                    # ตรวจสอบว่า AUC ไม่ใช่ default value
                    if auc != 0.5:
                        print(f"   ✅ AUC ไม่ใช่ค่า default (0.5)")
                    else:
                        print(f"   ⚠️ AUC เป็นค่า default (0.5) - อาจมีปัญหาในการคำนวณ")
                        
                else:
                    print(f"❌ {scenario} training ล้มเหลว - result เป็น None")
                    
            except Exception as e:
                print(f"❌ เกิดข้อผิดพลาดใน {scenario}: {e}")
                import traceback
                traceback.print_exc()
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def test_different_class_distributions():
    """ทดสอบกับ class distributions ที่แตกต่างกัน"""
    
    print(f"\n🔬 ทดสอบกับ class distributions ที่แตกต่างกัน")
    print("="*60)
    
    # ทดสอบกับ distributions ต่างๆ
    test_cases = [
        {
            'name': 'Balanced',
            'probs': [0.2, 0.2, 0.2, 0.2, 0.2],
            'description': 'Classes กระจายเท่าๆ กัน'
        },
        {
            'name': 'Moderate Imbalance',
            'probs': [0.4, 0.2, 0.2, 0.1, 0.1],
            'description': 'Class imbalance ปานกลาง'
        },
        {
            'name': 'Severe Imbalance',
            'probs': [0.7, 0.1, 0.1, 0.05, 0.05],
            'description': 'Class imbalance รุนแรง'
        },
        {
            'name': 'Extreme Imbalance',
            'probs': [0.9, 0.05, 0.03, 0.01, 0.01],
            'description': 'Class imbalance รุนแรงมาก'
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📊 ทดสอบ: {test_case['name']}")
        print(f"   {test_case['description']}")
        print(f"   Probabilities: {test_case['probs']}")
        
        try:
            # สร้างข้อมูลทดสอบ
            np.random.seed(42)
            n_samples = 1000
            
            # สร้าง features
            X_test = pd.DataFrame({
                f'Feature_{i}': np.random.uniform(-1, 1, n_samples) 
                for i in range(10)
            })
            
            # สร้าง target ตาม distribution
            y_test = np.random.choice([0, 1, 2, 3, 4], n_samples, p=test_case['probs'])
            
            # ตรวจสอบ class distribution
            class_counts = pd.Series(y_test).value_counts().sort_index()
            print(f"   Class counts: {class_counts.to_dict()}")
            
            # คำนวณ imbalance ratio
            max_count = class_counts.max()
            min_count = class_counts.min()
            imbalance_ratio = max_count / min_count if min_count > 0 else float('inf')
            print(f"   Imbalance ratio: {imbalance_ratio:.2f}:1")
            
            # ตรวจสอบว่ามี classes ครบหรือไม่
            unique_classes = sorted(np.unique(y_test))
            missing_classes = set(range(5)) - set(unique_classes)
            
            if missing_classes:
                print(f"   ⚠️ Missing classes: {missing_classes}")
            else:
                print(f"   ✅ มี classes ครบทั้ง 5 classes")
                
        except Exception as e:
            print(f"   ❌ เกิดข้อผิดพลาด: {e}")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🏗️ Test AUC Fix for Multiclass Classification")
    print("="*60)
    
    # ทดสอบการแก้ไข AUC
    test_auc_calculation()
    
    # ทดสอบกับ class distributions ต่างๆ
    test_different_class_distributions()
    
    print(f"\n✅ การทดสอบเสร็จสิ้น")
    print(f"\n📋 สรุปการแก้ไข AUC:")
    print(f"   ✅ ใช้ accuracy scoring แทน roc_auc_ovr ใน RandomizedSearchCV")
    print(f"   ✅ ตรวจสอบ classes ใน training และ test data")
    print(f"   ✅ คำนวณ AUC อย่างปลอดภัยด้วย error handling")
    print(f"   ✅ ใช้ accuracy แทน AUC เมื่อคำนวณไม่ได้")
    print(f"   ✅ รองรับ class imbalance และ missing classes")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# Test server status

import requests
import json
import time

def test_server():
    url = "http://127.0.0.1:54321"
    
    # Test if server is running
    try:
        response = requests.get(f"{url}/test", timeout=5)
        print(f"✅ Server is running! Status: {response.status_code}")
        print(f"Response: {response.text}")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running or not accessible")
        return False
    except Exception as e:
        print(f"❌ Error testing server: {e}")
        return False

def test_process_data():
    url = "http://127.0.0.1:54321/process_data"
    
    # Test data
    test_data = {
        "symbol": "XAUUSD",
        "timeframe": 30,
        "bars": [
            {
                "time": "2024-01-01 10:00:00",
                "open": 2000.0,
                "high": 2010.0,
                "low": 1990.0,
                "close": 2005.0,
                "volume": 1000
            }
        ]
    }
    
    try:
        response = requests.post(
            url, 
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        print(f"✅ Process data test! Status: {response.status_code}")
        print(f"Response: {response.text}")
        return True
    except Exception as e:
        print(f"❌ Error testing process_data: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testing server status...")
    
    # Wait a bit for server to start
    time.sleep(2)
    
    if test_server():
        print("\n🔍 Testing process_data endpoint...")
        test_process_data()
    else:
        print("❌ Cannot test process_data - server not running")

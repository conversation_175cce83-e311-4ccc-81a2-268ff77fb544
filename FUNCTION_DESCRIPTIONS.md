# 📚 คำบรรยายหน้าที่ของฟังก์ชันทั้งหมด - python_LightGBM_16_Signal.py

## 🎯 ภาพรวม
เอกสารนี้อธิบายหน้าที่และการทำงานของฟังก์ชันทั้งหมดใน `python_LightGBM_16_Signal.py` เพื่อให้เข้าใจการทำงานของระบบ Multi-Model LightGBM Trading System

---

## 🏗️ **1. ฟังก์ชันหลักของระบบ (Core System Functions)**

### **1.1 main(run_identifier=None, group_name=None, input_files=None)**
```python
"""จุดเริ่มต้นของโปรแกรม วนลูปไฟล์, เรียกใช้ฟังก์ชันต่างๆ, สรุปผล, วิเคราะห์, และบันทึกผลลัพธ์"""
```
**หน้าที่:**
- ควบคุมการทำงานหลักของโปรแกรม
- วนลูปประมวลผลไฟล์ข้อมูลทั้งหมด
- เรียกใช้ฟังก์ชันการเทรนและวิเคราะห์
- สรุปผลลัพธ์และสร้างรายงาน

### **1.2 parse_arguments()**
```python
"""Parse command line arguments"""
```
**หน้าที่:**
- แปลงและตรวจสอบ command line arguments
- กำหนดค่า default สำหรับ symbols และ timeframes
- ส่งคืนค่าพารามิเตอร์ที่ผู้ใช้กำหนด

---

## 🤖 **2. ฟังก์ชัน Multi-Model Architecture**

### **2.1 detect_market_scenario(row)**
```python
"""ตรวจจับสถานการณ์ตลาดสำหรับการเลือกโมเดลที่เหมาะสม"""
```
**หน้าที่:**
- วิเคราะห์ความสัมพันธ์ระหว่างราคาปัจจุบันกับ EMA200
- จำแนกสถานการณ์ตลาดเป็น: uptrend, downtrend, sideways
- ใช้เป็นเกณฑ์ในการเลือกโมเดลที่เหมาะสม

### **2.2 get_applicable_scenarios(market_condition, action_type)**
```python
"""หา scenarios ที่เหมาะสมตามสถานการณ์ตลาดและประเภทการเทรด"""
```
**หน้าที่:**
- เลือก scenarios ที่เหมาะสมตามสถานการณ์ตลาด
- จับคู่ market condition กับ action type (buy/sell)
- ส่งคืนรายชื่อ scenarios ที่เหมาะสม

### **2.3 filter_data_by_scenario(df, scenario_name)**
```python
"""กรองข้อมูลตาม scenario ที่กำหนด"""
```
**หน้าที่:**
- กรองข้อมูลให้เหลือเฉพาะที่ตรงกับ scenario
- ใช้เงื่อนไขจาก MARKET_SCENARIOS
- เตรียมข้อมูลสำหรับการเทรนโมเดลเฉพาะทาง

### **2.4 prepare_scenario_data(df, scenario_name, target_column='Target')**
```python
"""เตรียมข้อมูลสำหรับ scenario เฉพาะ"""
```
**หน้าที่:**
- เตรียม features และ targets สำหรับ scenario เฉพาะ
- กรองข้อมูลและทำความสะอาด
- ส่งคืนข้อมูลที่พร้อมสำหรับการเทรน

### **2.5 train_scenario_model(X, y, scenario_name, symbol, timeframe)**
```python
"""เทรนโมเดลสำหรับ scenario เฉพาะ"""
```
**หน้าที่:**
- เทรนโมเดล LightGBM สำหรับ scenario เฉพาะ
- ใช้ hyperparameters ที่เหมาะสมกับข้อมูล
- บันทึกโมเดลที่เทรนแล้ว

### **2.6 train_all_scenario_models(df, symbol, timeframe)**
```python
"""เทรนโมเดลทั้ง 4 scenarios"""
```
**หน้าที่:**
- เทรนโมเดลสำหรับทั้ง 4 scenarios
- จัดการการแจกแจงข้อมูลในแต่ละ scenario
- ส่งคืน dictionary ของโมเดลทั้งหมด

### **2.7 load_scenario_models(symbol, timeframe)**
```python
"""โหลดโมเดลทั้ง 4 scenarios"""
```
**หน้าที่:**
- โหลดโมเดลที่เทรนไว้แล้วจากไฟล์
- ตรวจสอบความสมบูรณ์ของโมเดล
- ส่งคืน dictionary ของโมเดลที่โหลดแล้ว

### **2.8 select_appropriate_model(row, action_type, loaded_models)**
```python
"""เลือกโมเดลที่เหมาะสมตามสถานการณ์ตลาดและประเภทการเทรด"""
```
**หน้าที่:**
- เลือกโมเดลที่เหมาะสมตามสถานการณ์ปัจจุบัน
- พิจารณาทั้ง market condition และ action type
- ส่งคืนโมเดลที่เหมาะสมที่สุด

### **2.9 predict_with_scenario_model(row, action_type, loaded_models)**
```python
"""ทำนายด้วยโมเดลที่เหมาะสมตามสถานการณ์"""
```
**หน้าที่:**
- ใช้โมเดลที่เหมาะสมทำนายผลลัพธ์
- ประมวลผล features สำหรับการทำนาย
- ส่งคืนผลการทำนายและความมั่นใจ

---

## 📊 **3. ฟังก์ชันการวิเคราะห์และรายงาน (Analysis & Reporting)**

### **3.1 analyze_time_filter_advanced(trade_df, min_win_rate=0.40)**
```python
"""วิเคราะห์ time filter แบบละเอียดและสร้างแนะนำการเทรดรายวัน"""
```
**หน้าที่:**
- วิเคราะห์ประสิทธิภาพการเทรดตามวันและเวลา
- หาช่วงเวลาที่เหมาะสมสำหรับการเทรด
- สร้างคำแนะนำการเทรดรายวัน

### **3.2 generate_trading_schedule_summary(group_name=None)**
```python
"""สร้างสรุปแนะนำการเทรดรายวันจากผลการวิเคราะห์ time filters ทั้งหมด"""
```
**หน้าที่:**
- รวบรวมผลการวิเคราะห์ time filters
- สร้างตารางแนะนำการเทรดรายวัน
- จัดกลุ่มตาม timeframe (M30, M60)

### **3.3 print_trading_schedule_summary(output_folder, group_name=None)**
```python
"""แสดงสรุปแนะนำการเทรดรายวันในรูปแบบที่อ่านง่าย และบันทึกเป็นไฟล์ .txt"""
```
**หน้าที่:**
- แสดงผลสรุปการเทรดในรูปแบบที่อ่านง่าย
- บันทึกเป็นไฟล์ .txt สำหรับการอ้างอิง
- แยกไฟล์ตาม timeframe

### **3.4 generate_all_trading_schedule_summaries(output_folder)**
```python
"""สร้างไฟล์สรุปการเทรดรายวันทั้งหมด (M30, M60, และรวม)"""
```
**หน้าที่:**
- สร้างไฟล์สรุปสำหรับทุก timeframe
- สร้างไฟล์รวมและไฟล์แยกตาม timeframe
- จัดการโครงสร้างไฟล์ output

### **3.5 analyze_parameter_stability(all_results)**
```python
"""วิเคราะห์ความเสถียรของพารามิเตอร์จากผลการเทรนทั้งหมด"""
```
**หน้าที่:**
- วิเคราะห์ความเสถียรของ hyperparameters
- คำนวณ coefficient of variation
- ประเมินความน่าเชื่อถือของพารามิเตอร์

### **3.6 analyze_model_performance_detailed(all_results)**
```python
"""วิเคราะห์ประสิทธิภาพโมเดลแบบละเอียด M30 vs H1 และเปรียบเทียบ"""
```
**หน้าที่:**
- เปรียบเทียบประสิทธิภาพระหว่าง timeframes
- วิเคราะห์ประสิทธิภาพตาม symbol type
- สร้างรายงานเปรียบเทียบแบบละเอียด

### **3.7 analyze_cross_asset_feature_importance(input_files, importance_files_dir)**
```python
"""วิเคราะห์ feature importance ข้ามหลาย asset เพื่อหา features ที่สำคัญร่วมกัน"""
```
**หน้าที่:**
- วิเคราะห์ความสำคัญของ features ข้าม assets
- หา features ที่สำคัญอย่างสม่ำเสมอ
- บันทึกรายชื่อ features ที่แนะนำ

---

## 🔧 **4. ฟังก์ชันยูทิลิตี้ (Utility Functions)**

### **4.1 calculate_stats(subset)**
```python
"""คำนวณ win rate และ expectancy จากชุด trade"""
```
**หน้าที่:**
- คำนวณ win rate (อัตราการชนะ)
- คำนวณ expectancy (ความคาดหวังผลกำไร)
- คำนวณ average win และ average loss
- ส่งคืนสถิติการเทรดที่สำคัญ

### **4.2 floor_price(value, digits)**
```python
"""ปัดเศษราคาขึ้น/ลงตามจำนวน digits ที่กำหนด"""
```
**หน้าที่:**
- ปัดเศษราคาให้ตรงกับ tick size
- ใช้สำหรับการคำนวณ entry/exit prices
- รองรับการเทรดในตลาดจริง

### **4.3 safe_json_serialize(obj)**
```python
"""แปลงข้อมูล numpy/pandas ให้เป็นชนิดที่ serialize เป็น JSON ได้"""
```
**หน้าที่:**
- แปลงข้อมูล numpy/pandas เป็น Python native types
- รองรับการบันทึกเป็น JSON
- จัดการ data types ที่ซับซ้อน

### **4.4 get_lgbm_params(y=None, use_scale_pos_weight=True)**
```python
"""คืนค่า parameter dictionary สำหรับ LightGBM (ปรับตาม class imbalance)"""
```
**หน้าที่:**
- กำหนดพารามิเตอร์ที่เหมาะสมสำหรับ LightGBM
- ปรับพารามิเตอร์ตาม class imbalance
- รองรับทั้ง binary และ multiclass classification

---

## 📈 **5. ฟังก์ชันการเทรดและ Backtesting**

### **5.1 create_trade_cycles_with_model(df, trained_model, scaler, model_features)**
```python
"""สร้างรายการซื้อขาย (trade cycles) โดยใช้โมเดล ML ช่วยตัดสินใจเข้า/ออกเทรด"""
```
**หน้าที่:**
- สร้างสัญญาณการซื้อขายด้วย ML model
- ตรวจสอบ entry conditions ทางเทคนิค
- คำนวณ stop loss และ take profit
- ติดตาม look-ahead bias

### **5.2 create_trade_cycles_with_multi_model(df, loaded_models)**
```python
"""สร้างสัญญาณการซื้อขายด้วย Multi-Model Architecture"""
```
**หน้าที่:**
- ใช้ multi-model approach ในการสร้างสัญญาณ
- เลือกโมเดลตามสถานการณ์ตลาด
- รวมผลการทำนายจากหลายโมเดล

### **5.3 is_high_quality_entry(df, i, symbol="GOLD")**
```python
"""ตรวจสอบคุณภาพของจุดเข้าเทรด"""
```
**หน้าที่:**
- ประเมินคุณภาพของสัญญาณเข้าเทรด
- ตรวจสอบเงื่อนไขทางเทคนิคเพิ่มเติม
- กรองสัญญาณที่มีคุณภาพต่ำ

---

## 📊 **6. ฟังก์ชันการแสดงผลและกราฟ (Visualization)**

### **6.1 plot_feature_importance(model, features, model_name, symbol, timeframe)**
```python
"""สร้างและบันทึกกราฟ feature importance ของโมเดล"""
```
**หน้าที่:**
- สร้างกราฟแสดงความสำคัญของ features
- แสดงทั้ง gain และ split importance
- บันทึกกราฟเป็นไฟล์ PNG

### **6.2 plot_candlestick_with_entries_and_exits(df, trade_df, file_name)**
```python
"""สร้างกราฟแท่งเทียนพร้อมจุดเข้า-ออกการเทรด"""
```
**หน้าที่:**
- สร้างกราฟ candlestick chart
- แสดงจุดเข้าและออกการเทรด
- ใช้ Plotly สำหรับการแสดงผลแบบ interactive

### **6.3 safe_plot_results(df, symbol, timeframe)**
```python
"""พล็อตกราฟผลลัพธ์ (accuracy, auc, ฯลฯ) แบบปลอดภัย (มี error handling)"""
```
**หน้าที่:**
- สร้างกราฟผลลัพธ์การเทรนโมเดล
- มี error handling ที่แข็งแกร่ง
- รองรับข้อมูลที่ไม่สมบูรณ์

---

## 🔄 **7. ฟังก์ชันการประมวลผลข้อมูล (Data Processing)**

### **7.1 add_market_scenario_column(df)**
```python
"""เพิ่มคอลัมน์ market_scenario ใน DataFrame"""
```
**หน้าที่:**
- เพิ่มคอลัมน์ระบุสถานการณ์ตลาด
- ใช้ฟังก์ชัน detect_market_scenario
- เตรียมข้อมูลสำหรับ multi-model training

### **7.2 time_series_cv(X, y, timeframe, n_splits=5)**
```python
"""ทำ Time Series Cross-Validation สำหรับข้อมูลอนุกรมเวลา"""
```
**หน้าที่:**
- ทำ cross-validation แบบ time series
- รักษาลำดับเวลาของข้อมูล
- ป้องกัน data leakage

---

## 🎯 **สรุป**

ฟังก์ชันทั้งหมดใน `python_LightGBM_16_Signal.py` ถูกออกแบบมาเพื่อทำงานร่วมกันเป็นระบบ Multi-Model Trading System ที่สมบูรณ์ โดยแบ่งหน้าที่ออกเป็น:

1. **Core System** - ควบคุมการทำงานหลัก
2. **Multi-Model** - จัดการโมเดลหลายตัว
3. **Analysis** - วิเคราะห์และรายงาน
4. **Utility** - ฟังก์ชันช่วยเหลือ
5. **Trading** - การเทรดและ backtesting
6. **Visualization** - การแสดงผล
7. **Data Processing** - การประมวลผลข้อมูล

แต่ละฟังก์ชันมีหน้าที่เฉพาะและทำงานร่วมกันเพื่อสร้างระบบการเทรดที่มีประสิทธิภาพและน่าเชื่อถือ

---

## 🔧 **8. ฟังก์ชันเพิ่มเติม (Additional Functions)**

### **8.1 load_and_process_data(csv_file)**
```python
"""โหลดและประมวลผลข้อมูลจากไฟล์ CSV พร้อมสร้าง technical indicators และ features"""
```
**หน้าที่:**
- โหลดข้อมูล OHLC จากไฟล์ CSV
- สร้าง technical indicators ทั้งหมด
- สร้าง features สำหรับ ML model
- ตรวจสอบคุณภาพข้อมูล

### **8.2 train_and_evaluate(df, trade_df, symbol, timeframe)**
```python
"""เทรนและประเมินผลโมเดล LightGBM แบบดั้งเดิม (single model)"""
```
**หน้าที่:**
- เทรนโมเดล LightGBM แบบเดียว
- แบ่งข้อมูลเป็น train/validation/test
- ประเมินผลด้วย metrics ต่างๆ
- บันทึกโมเดลและผลลัพธ์

### **8.3 hyperparameter_tuning(X, y, symbol, timeframe)**
```python
"""ปรับแต่ง hyperparameters ของโมเดล LightGBM ด้วย RandomizedSearchCV"""
```
**หน้าที่:**
- ค้นหา hyperparameters ที่เหมาะสม
- ใช้ Time Series Cross-Validation
- ป้องกัน overfitting
- บันทึกพารามิเตอร์ที่ดีที่สุด

### **8.4 find_optimal_threshold(model, val_data)**
```python
"""หาค่า threshold ที่เหมาะสมสำหรับการตัดสินใจ buy/sell"""
```
**หน้าที่:**
- ทดสอบ threshold ต่างๆ (0.1-0.9)
- ประเมินผลด้วย expectancy และ win rate
- เลือก threshold ที่ให้ผลลัพธ์ดีที่สุด
- บันทึกค่า optimal threshold

### **8.5 find_optimal_nbars_sl(val_df, entry_func)**
```python
"""หาค่า nBars SL ที่เหมาะสมสำหรับการกำหนด stop loss"""
```
**หน้าที่:**
- ทดสอบค่า nBars SL ต่างๆ (2-10 bars)
- ประเมินผลด้วย backtest บน validation set
- เลือกค่าที่ให้ risk/reward ดีที่สุด
- บันทึกค่า optimal nBars SL

### **8.6 backtest(df, entry_condition_func, nBars_SL)**
```python
"""ทำ backtesting เพื่อทดสอบประสิทธิภาพของกลยุทธ์การเทรด"""
```
**หน้าที่:**
- จำลองการเทรดในอดีต
- ตรวจสอบ temporal dependence
- ป้องกัน look-ahead bias
- คำนวณผลลัพธ์การเทรด

### **8.7 select_features(df, target_column, symbol, timeframe)**
```python
"""เลือก features ที่เหมาะสมสำหรับการเทรนโมเดล"""
```
**หน้าที่:**
- วิเคราะห์ correlation ระหว่าง features
- เลือก features ที่มีความสัมพันธ์กับ target
- ลบ features ที่ซ้ำซ้อน
- โหลด must-have features จากไฟล์

### **8.8 create_multiclass_target(trade_df)**
```python
"""สร้าง target variable แบบ multiclass (5 classes) จากผลกำไร"""
```
**หน้าที่:**
- แปลงผลกำไรเป็น 5 classes
- strong_sell, weak_sell, no_trade, weak_buy, strong_buy
- ใช้เกณฑ์ points สำหรับการแบ่งกลุ่ม
- รองรับ multiclass classification

### **8.9 analyze_temporal_dependence(df, symbol, timeframe)**
```python
"""วิเคราะห์ temporal dependence และ stationarity ของข้อมูล"""
```
**หน้าที่:**
- ตรวจสอบ stationarity ด้วย ADF test
- วิเคราะห์ช่วงเวลาระหว่างข้อมูล
- ตรวจหาข้อมูลที่ขาดหาย
- สร้างรายงาน temporal analysis

### **8.10 check_look_ahead_bias(df, model_features, start_index)**
```python
"""ตรวจสอบ look-ahead bias ในการใช้ features"""
```
**หน้าที่:**
- ตรวจสอบว่า features ไม่ใช้ข้อมูลจากอนาคต
- วิเคราะห์ NaN values ใน features
- ระบุ features ที่อาจมีปัญหา
- ป้องกันการใช้ข้อมูลที่ไม่ควรมี

---

## 📋 **9. ฟังก์ชันการจัดการไฟล์และข้อมูล (File & Data Management)**

### **9.1 save_results_to_json(results, output_path)**
```python
"""บันทึกผลลัพธ์เป็นไฟล์ JSON พร้อม error handling"""
```
**หน้าที่:**
- บันทึกผลลัพธ์เป็น JSON format
- จัดการ data types ที่ซับซ้อน
- มี error handling ที่แข็งแกร่ง
- รองรับ numpy/pandas objects

### **9.2 load_must_have_features(symbol, timeframe)**
```python
"""โหลดรายชื่อ features ที่จำเป็นจากไฟล์ที่วิเคราะห์ไว้แล้ว"""
```
**หน้าที่:**
- โหลด features ที่คัดเลือกไว้แล้ว
- ตรวจสอบความสมบูรณ์ของไฟล์
- ส่งคืนรายชื่อ features ที่แนะนำ
- รองรับการ fallback เมื่อไฟล์ไม่พบ

### **9.3 create_output_directories(symbol, timeframe)**
```python
"""สร้างโครงสร้างโฟลเดอร์สำหรับบันทึกผลลัพธ์"""
```
**หน้าที่:**
- สร้างโฟลเดอร์ models, results, plots
- จัดระเบียบไฟล์ตาม symbol และ timeframe
- ตรวจสอบสิทธิ์การเขียนไฟล์
- รองรับการสร้างโฟลเดอร์ซ้อน

---

## 🎯 **10. ฟังก์ชันการตรวจสอบและ Validation**

### **10.1 validate_data_quality(df)**
```python
"""ตรวจสอบคุณภาพของข้อมูลก่อนการประมวลผล"""
```
**หน้าที่:**
- ตรวจสอบ missing values
- ตรวจสอบ duplicate timestamps
- ตรวจสอบความสมเหตุสมผลของราคา
- รายงานปัญหาที่พบ

### **10.2 validate_model_performance(results)**
```python
"""ตรวจสอบประสิทธิภาพของโมเดลว่าผ่านเกณฑ์ขั้นต่ำหรือไม่"""
```
**หน้าที่:**
- ตรวจสอบ accuracy, AUC, F1-score
- ตรวจสอบ win rate และ expectancy
- เปรียบเทียบกับเกณฑ์ขั้นต่ำ
- แนะนำการปรับปรุง

### **10.3 validate_trading_signals(trade_df)**
```python
"""ตรวจสอบความถูกต้องของสัญญาณการเทรด"""
```
**หน้าที่:**
- ตรวจสอบ entry/exit prices
- ตรวจสอบ stop loss และ take profit
- ตรวจสอบ timing ของสัญญาณ
- รายงานสัญญาณที่ผิดปกติ

---

## 📊 **สรุปการจัดกลุ่มฟังก์ชัน**

| กลุ่ม | จำนวนฟังก์ชัน | หน้าที่หลัก |
|-------|---------------|-------------|
| **Core System** | 2 | ควบคุมการทำงานหลัก |
| **Multi-Model** | 9 | จัดการโมเดลหลายตัว |
| **Analysis & Reporting** | 7 | วิเคราะห์และรายงาน |
| **Utility** | 4 | ฟังก์ชันช่วยเหลือ |
| **Trading & Backtesting** | 8 | การเทรดและทดสอบ |
| **Visualization** | 3 | การแสดงผล |
| **Data Processing** | 2 | การประมวลผลข้อมูล |
| **File & Data Management** | 3 | จัดการไฟล์และข้อมูล |
| **Validation** | 3 | ตรวจสอบและ validation |

**รวมทั้งหมด: 41 ฟังก์ชัน**

ระบบนี้ออกแบบมาให้มีความยืดหยุ่น, น่าเชื่อถือ, และสามารถขยายได้ เพื่อรองรับการเทรดในตลาดการเงินที่ซับซ้อน

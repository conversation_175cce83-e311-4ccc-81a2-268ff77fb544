#!/usr/bin/env python3
"""
Test Price Adjustment and Multi-Trade System
ทดสอบระบบการชดเชยราคาและการเทรดหลายไม้

การทดสอบ:
1. การชดเชยราคาเมื่อราคาเปลี่ยนแปลง
2. การเทรดหลายไม้พร้อมอัตราส่วน TP ที่แตกต่างกัน
3. ระบบ Break Even
4. การปิดไม้วันศุกร์
"""

import requests
import json
import time
import datetime

# Configuration
PYTHON_SERVER_URL = "http://127.0.0.1:5000/data"

def create_test_signal_data(symbol, timeframe, signal_type, entry_price, sl_price, tp_price):
    """สร้างข้อมูลสัญญาณทดสอบ"""
    current_time = datetime.datetime.now()
    
    # สร้างข้อมูลแท่งเทียนจำลอง
    bars_data = []
    for i in range(5):
        bar_time = current_time - datetime.timedelta(minutes=30*i)
        bar_data = {
            "time": int(bar_time.timestamp()),
            "open": entry_price + (i * 0.0001),
            "high": entry_price + 0.0010 + (i * 0.0001),
            "low": entry_price - 0.0010 + (i * 0.0001),
            "close": entry_price + (i * 0.0001),
            "tick_volume": 1000 + (i * 100)
        }
        bars_data.append(bar_data)
    
    test_data = {
        "symbol": symbol,
        "timeframe_str": timeframe,
        "time": int(current_time.timestamp()),
        "open": entry_price,
        "high": entry_price + 0.0010,
        "low": entry_price - 0.0010,
        "close": entry_price,
        "tick_volume": 1500,
        "bars": bars_data
    }
    
    return test_data

def test_buy_signal_price_adjustment():
    """ทดสอบสัญญาณ BUY พร้อมการชดเชยราคา"""
    print("\n🟢 ทดสอบสัญญาณ BUY พร้อมการชดเชยราคา")
    print("=" * 80)
    
    # ตัวอย่างจากโจทย์: Open Bid 1.17513 Ask 1.17538 SL 1.17214 TP 1.17862
    symbol = "EURUSD"
    timeframe = "PERIOD_M30"
    
    # ราคาจาก Python (ราคาเดิม)
    python_entry = 1.17538  # Ask เดิม
    python_sl = 1.17214
    python_tp = 1.17862
    
    print(f"📊 Python Signal Data:")
    print(f"   Entry (Ask): {python_entry:.5f}")
    print(f"   SL: {python_sl:.5f}")
    print(f"   TP: {python_tp:.5f}")
    print(f"   SL Size: {python_entry - python_sl:.5f}")
    print(f"   TP Size: {python_tp - python_entry:.5f}")
    
    # จำลองราคาใหม่ที่ MT5 (ราคาขยับไป)
    new_bid = 1.17528  # +15 points
    new_ask = 1.17553  # +15 points
    
    print(f"\n📈 Current MT5 Prices:")
    print(f"   New Bid: {new_bid:.5f}")
    print(f"   New Ask: {new_ask:.5f}")
    print(f"   Price Movement: +{(new_ask - python_entry) * 100000:.1f} points")
    
    # คำนวณการชดเชย
    sl_size = python_entry - python_sl  # 0.00324
    adjusted_entry = new_ask  # 1.17553
    adjusted_sl = python_sl  # ใช้ค่าเดิม 1.17214
    adjusted_tp = adjusted_entry + (python_tp - python_entry)  # 1.17553 + 0.00324 = 1.17877
    
    print(f"\n🔧 Price Adjustment Calculation:")
    print(f"   Adjusted Entry: {adjusted_entry:.5f} (Current Ask)")
    print(f"   Adjusted SL: {adjusted_sl:.5f} (Keep Original)")
    print(f"   Adjusted TP: {adjusted_tp:.5f} (Entry + TP Size)")
    print(f"   Expected TP: 1.17877 (from example)")
    
    # ส่งข้อมูลไปยัง Python server
    test_data = create_test_signal_data(symbol, timeframe, "BUY", python_entry, python_sl, python_tp)
    
    try:
        response = requests.post(
            PYTHON_SERVER_URL,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"\n✅ Server Response:")
            print(f"   Signal: {response_data.get('signal', 'N/A')}")
            print(f"   Class: {response_data.get('class', 'N/A')}")
            print(f"   Entry Price: {response_data.get('entry_price', 0.0):.5f}")
            print(f"   SL Price: {response_data.get('sl_price', 0.0):.5f}")
            print(f"   TP Price: {response_data.get('tp_price', 0.0):.5f}")
            print(f"   Best Entry: {response_data.get('best_entry', 0.0):.5f}")
        else:
            print(f"❌ Server Error: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Connection Error: {e}")

def test_sell_signal_price_adjustment():
    """ทดสอบสัญญาณ SELL พร้อมการชดเชยราคา"""
    print("\n🔴 ทดสอบสัญญาณ SELL พร้อมการชดเชยราคา")
    print("=" * 80)
    
    # ตัวอย่างจากโจทย์: Open Bid 1.17513 Ask 1.17538 SL 1.17862 TP 1.17164
    symbol = "GBPUSD"
    timeframe = "PERIOD_H1"
    
    # ราคาจาก Python (ราคาเดิม)
    python_entry = 1.17513  # Bid เดิม
    python_sl = 1.17862
    python_tp = 1.17164
    
    print(f"📊 Python Signal Data:")
    print(f"   Entry (Bid): {python_entry:.5f}")
    print(f"   SL: {python_sl:.5f}")
    print(f"   TP: {python_tp:.5f}")
    print(f"   SL Size: {python_sl - python_entry:.5f}")
    print(f"   TP Size: {python_entry - python_tp:.5f}")
    
    # จำลองราคาใหม่ที่ MT5 (ราคาขยับลง)
    new_bid = 1.17504  # -9 points
    new_ask = 1.17529  # -9 points
    
    print(f"\n📉 Current MT5 Prices:")
    print(f"   New Bid: {new_bid:.5f}")
    print(f"   New Ask: {new_ask:.5f}")
    print(f"   Price Movement: {(new_bid - python_entry) * 100000:.1f} points")
    
    # คำนวณการชดเชย
    sl_size = python_sl - python_entry  # 0.00349
    adjusted_entry = new_bid  # 1.17504
    adjusted_sl = python_sl  # ใช้ค่าเดิม 1.17862
    adjusted_tp = adjusted_entry - (python_entry - python_tp)  # 1.17504 - 0.00349 = 1.17155
    
    print(f"\n🔧 Price Adjustment Calculation:")
    print(f"   Adjusted Entry: {adjusted_entry:.5f} (Current Bid)")
    print(f"   Adjusted SL: {adjusted_sl:.5f} (Keep Original)")
    print(f"   Adjusted TP: {adjusted_tp:.5f} (Entry - TP Size)")
    print(f"   Expected TP: 1.17155 (from example)")
    
    # ส่งข้อมูลไปยัง Python server
    test_data = create_test_signal_data(symbol, timeframe, "SELL", python_entry, python_sl, python_tp)
    
    try:
        response = requests.post(
            PYTHON_SERVER_URL,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"\n✅ Server Response:")
            print(f"   Signal: {response_data.get('signal', 'N/A')}")
            print(f"   Class: {response_data.get('class', 'N/A')}")
            print(f"   Entry Price: {response_data.get('entry_price', 0.0):.5f}")
            print(f"   SL Price: {response_data.get('sl_price', 0.0):.5f}")
            print(f"   TP Price: {response_data.get('tp_price', 0.0):.5f}")
            print(f"   Best Entry: {response_data.get('best_entry', 0.0):.5f}")
        else:
            print(f"❌ Server Error: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Connection Error: {e}")

def test_multi_trade_lot_calculation():
    """ทดสอบการคำนวณ Lot สำหรับการเทรดหลายไม้"""
    print("\n🎯 ทดสอบการคำนวณ Lot สำหรับการเทรดหลายไม้")
    print("=" * 80)
    
    test_cases = [
        {"total_lot": 0.01, "expected_trades": 1, "description": "กรณี 0.01: เปิด 1 ไม้"},
        {"total_lot": 0.02, "expected_trades": 2, "description": "กรณี 0.02: เปิด 2 ไม้"},
        {"total_lot": 0.03, "expected_trades": 3, "description": "กรณี 0.03: เปิด 3 ไม้"},
        {"total_lot": 0.04, "expected_trades": 3, "description": "กรณี 0.04: เปิด 3 ไม้ แบ่งเฉลี่ย"},
        {"total_lot": 0.05, "expected_trades": 3, "description": "กรณี 0.05: เปิด 3 ไม้ แบ่งเฉลี่ย"},
        {"total_lot": 0.06, "expected_trades": 3, "description": "กรณี 0.06: เปิด 3 ไม้ แบ่งเฉลี่ย"},
    ]
    
    for i, case in enumerate(test_cases, 1):
        total_lot = case["total_lot"]
        print(f"\n📋 Test Case {i}: {case['description']}")
        print(f"   Total Lot: {total_lot:.2f}")
        
        # จำลองการคำนวณ (ตาม logic ใน MT5)
        if total_lot <= 0.01:
            lots = [total_lot]
            tp_ratios = [1]
        elif total_lot <= 0.02:
            lots = [0.01, total_lot - 0.01]
            tp_ratios = [1, 2]
        elif total_lot <= 0.03:
            lots = [0.01, 0.01, total_lot - 0.02]
            tp_ratios = [1, 2, 3]
        else:
            base_lot = total_lot / 3.0
            remainder = total_lot - (base_lot * 3.0)
            lots = [base_lot, base_lot, base_lot + remainder]
            if remainder > 0.005:
                lots = [base_lot + remainder, base_lot, base_lot]
            tp_ratios = [1, 2, 3]
        
        print(f"   Expected Trades: {len(lots)}")
        for j, (lot, ratio) in enumerate(zip(lots, tp_ratios)):
            print(f"   Trade {j+1}: Lot={lot:.2f}, TP Ratio=1:{ratio}")
        
        total_calculated = sum(lots)
        print(f"   Total Calculated: {total_calculated:.2f}")
        print(f"   Difference: {abs(total_lot - total_calculated):.4f}")

def main():
    """ฟังก์ชันหลักสำหรับการทดสอบ"""
    print("🔧 Enhanced Trading System Test")
    print(f"⏰ เวลาทดสอบ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 Python Server: {PYTHON_SERVER_URL}")
    
    # ทดสอบการชดเชยราคาสำหรับ BUY
    test_buy_signal_price_adjustment()
    
    # ทดสอบการชดเชยราคาสำหรับ SELL
    test_sell_signal_price_adjustment()
    
    # ทดสอบการคำนวณ Lot สำหรับการเทรดหลายไม้
    test_multi_trade_lot_calculation()
    
    print("\n🎉 การทดสอบเสร็จสิ้น")
    print("=" * 80)
    print("📋 สรุปฟีเจอร์ที่ทดสอบ:")
    print("   ✅ การชดเชยราคาเมื่อราคาเปลี่ยนแปลง (BUY/SELL)")
    print("   ✅ การคำนวณ Lot สำหรับการเทรดหลายไม้")
    print("   ✅ อัตราส่วน TP ที่แตกต่างกัน (1:1, 1:2, 1:3)")
    print("   ✅ การแบ่ง Lot อย่างเหมาะสม")
    print("\n📝 ฟีเจอร์เพิ่มเติมใน MT5:")
    print("   🔄 Break Even (เลื่อน SL ไปที่จุดเข้าเมื่อกำไร = SL)")
    print("   📅 Friday Close (ปิดไม้ทั้งหมดวันศุกร์ 21:00)")
    print("   🎛️ การเปิด/ปิดฟีเจอร์ต่างๆ ผ่าน Global Variables")

if __name__ == "__main__":
    main()

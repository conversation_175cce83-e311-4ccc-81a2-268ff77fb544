#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบ MT5 WebRequest Server ที่แก้ไขปัญหาแล้ว
"""

import requests
import json
import time
from datetime import datetime

def test_server_request():
    """ทดสอบการส่งข้อมูลไปยัง server"""
    
    # ข้อมูลทดสอบ (จำลองข้อมูลจาก MT5)
    test_data = {
        "symbol": "GOLD",
        "timeframe_str": "PERIOD_H1",
        "time": 1752253200.0,  # timestamp
        "open": 2650.50,
        "high": 2655.00,
        "low": 2645.00,
        "close": 2652.75,
        "tick_volume": 1000,
        "bars": [
            # ข้อมูล 10 แท่งล่าสุด (จำลอง)
            {
                "time": 1752249600.0,
                "open": 2648.00,
                "high": 2653.00,
                "low": 2647.00,
                "close": 2650.50,
                "tick_volume": 950
            },
            {
                "time": 1752253200.0,
                "open": 2650.50,
                "high": 2655.00,
                "low": 2645.00,
                "close": 2652.75,
                "tick_volume": 1000
            }
            # เพิ่มข้อมูลแท่งอื่นๆ ตามต้องการ
        ]
    }
    
    try:
        print("🚀 ส่งข้อมูลทดสอบไปยัง MT5 WebRequest Server...")
        print(f"📊 Symbol: {test_data['symbol']}")
        print(f"⏰ Timeframe: {test_data['timeframe_str']}")
        print(f"💰 Price: {test_data['close']}")
        
        # ส่ง POST request ไปยัง server
        response = requests.post(
            'http://127.0.0.1:5000/data',
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("\n✅ ได้รับการตอบกลับจาก Server:")
            print(f"📊 Status: {result.get('status', 'Unknown')}")
            print(f"🎯 Signal: {result.get('signal', 'Unknown')}")
            print(f"📈 Class: {result.get('class', 'Unknown')}")
            print(f"🔥 Confidence: {result.get('confidence', 0.0):.4f}")
            print(f"💰 Entry Price: {result.get('entry_price', 0.0)}")
            print(f"🛑 SL Price: {result.get('sl_price', 0.0)}")
            print(f"🎯 TP Price: {result.get('tp_price', 0.0)}")
            print(f"📏 nBars_SL: {result.get('nBars_SL', 0)}")
            print(f"🎚️ Threshold: {result.get('threshold', 0.0)}")
            print(f"📅 Time Filters: {result.get('time_filters', '')}")
            print(f"📊 Spread: {result.get('spread', 0)}")
            print(f"💬 Message: {result.get('message', '')}")
            
            return True
        else:
            print(f"❌ Server ตอบกลับด้วย status code: {response.status_code}")
            print(f"📝 Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ ไม่สามารถเชื่อมต่อกับ Server ได้")
        print("💡 ตรวจสอบว่า Server กำลังทำงานอยู่หรือไม่")
        return False
    except requests.exceptions.Timeout:
        print("❌ การเชื่อมต่อ timeout")
        return False
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def test_multiple_requests():
    """ทดสอบการส่งข้อมูลหลายครั้ง"""
    print("\n🔄 ทดสอบการส่งข้อมูลหลายครั้ง...")
    
    symbols = ["GOLD", "EURUSD", "GBPUSD"]
    timeframes = ["PERIOD_H1", "PERIOD_M30"]
    
    for i, symbol in enumerate(symbols):
        for j, timeframe in enumerate(timeframes):
            print(f"\n📊 ทดสอบครั้งที่ {i*len(timeframes) + j + 1}: {symbol} {timeframe}")
            
            test_data = {
                "symbol": symbol,
                "timeframe_str": timeframe,
                "time": 1752253200.0 + i*3600 + j*1800,  # เวลาต่างกัน
                "open": 1.1000 + i*0.01,
                "high": 1.1010 + i*0.01,
                "low": 1.0990 + i*0.01,
                "close": 1.1005 + i*0.01,
                "tick_volume": 1000 + i*100,
                "bars": [
                    {
                        "time": 1752249600.0 + i*3600,
                        "open": 1.0995 + i*0.01,
                        "high": 1.1005 + i*0.01,
                        "low": 1.0985 + i*0.01,
                        "close": 1.1000 + i*0.01,
                        "tick_volume": 950 + i*50
                    }
                ]
            }
            
            try:
                response = requests.post(
                    'http://127.0.0.1:5000/data',
                    json=test_data,
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"   ✅ {result.get('signal', 'Unknown')} (Confidence: {result.get('confidence', 0.0):.4f})")
                else:
                    print(f"   ❌ Error: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ Exception: {e}")
            
            # รอสักครู่ระหว่างการส่ง request
            time.sleep(1)

def check_server_status():
    """ตรวจสอบสถานะของ server"""
    print("🔍 ตรวจสอบสถานะ Server...")
    
    try:
        # ลองเชื่อมต่อกับ server
        response = requests.get('http://127.0.0.1:5000/', timeout=5)
        
        if response.status_code == 200:
            print("✅ Server กำลังทำงานอยู่")
            return True
        else:
            print(f"⚠️ Server ตอบกลับด้วย status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Server ไม่ได้ทำงาน")
        print("💡 เริ่ม Server ด้วยคำสั่ง: python python_to_mt5_WebRequest_server_12_Signal.py")
        return False
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def main():
    """ฟังก์ชันหลักสำหรับทดสอบ"""
    print("🚀 เริ่มทดสอบ MT5 WebRequest Server ที่แก้ไขแล้ว")
    print("="*80)
    
    # ตรวจสอบสถานะ server
    if not check_server_status():
        print("\n❌ ไม่สามารถเชื่อมต่อกับ Server ได้")
        print("💡 กรุณาเริ่ม Server ก่อนทดสอบ")
        return
    
    # ทดสอบการส่งข้อมูลครั้งเดียว
    print("\n" + "="*50)
    if not test_server_request():
        print("❌ การทดสอบล้มเหลว")
        return
    
    # ทดสอบการส่งข้อมูลหลายครั้ง
    print("\n" + "="*50)
    test_multiple_requests()
    
    print("\n" + "="*80)
    print("✅ การทดสอบเสร็จสิ้น!")
    print("🎉 MT5 WebRequest Server ทำงานได้ปกติ")

if __name__ == "__main__":
    main()

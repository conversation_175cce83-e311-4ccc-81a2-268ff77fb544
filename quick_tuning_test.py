#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
การทดสอบ Hyperparameter Tuning แบบเร็ว
สำหรับตรวจสอบการทำงานของระบบ tuning ก่อนใช้งานจริง
"""

import sys
import os
import pandas as pd
import numpy as np
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split, RandomizedSearchCV, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score, classification_report, confusion_matrix
import lightgbm as lgb
import json
import datetime
import time

# เพิ่ม path สำหรับ import ฟังก์ชันจากไฟล์หลัก
sys.path.append('.')

def create_quick_test_data(n_samples=1000, n_features=20, random_state=42):
    """สร้างข้อมูลทดสอบขนาดเล็กสำหรับการทดสอบเร็ว"""
    print(f"📊 สร้างข้อมูลทดสอบ: {n_samples:,} samples, {n_features} features")
    
    X, y = make_classification(
        n_samples=n_samples,
        n_features=n_features,
        n_informative=int(n_features * 0.7),
        n_redundant=int(n_features * 0.2),
        n_clusters_per_class=2,
        weights=[0.8, 0.2],  # 20% positive class
        flip_y=0.02,
        random_state=random_state
    )
    
    # สร้างชื่อ features
    feature_names = [f"Feature_{i:02d}" for i in range(n_features)]
    X_df = pd.DataFrame(X, columns=feature_names)
    
    # เพิ่ม DateTime
    start_date = datetime.datetime(2023, 1, 1)
    dates = [start_date + datetime.timedelta(hours=i) for i in range(n_samples)]
    X_df['DateTime'] = dates
    
    print(f"✅ Class distribution: {np.bincount(y)} (ratio {np.sum(y==0)/np.sum(y==1):.1f}:1)")
    return X_df, y

def test_basic_lgbm_params():
    """ทดสอบพื้นฐานของ get_lgbm_params"""
    print(f"\n🧪 ทดสอบ Basic LightGBM Parameters")
    print("-" * 50)
    
    try:
        from python_LightGBM_15_Tuning import get_lgbm_params
        
        # ทดสอบกับข้อมูล imbalanced
        y_test = np.array([0] * 800 + [1] * 200)  # 4:1 ratio
        params = get_lgbm_params(y=y_test, use_scale_pos_weight=True)
        
        print(f"✅ Parameters loaded successfully")
        print(f"   - Objective: {params['objective']}")
        print(f"   - Learning rate: {params['learning_rate']}")
        print(f"   - Num leaves: {params['num_leaves']}")
        print(f"   - Scale pos weight: {params.get('scale_pos_weight', 'N/A')}")
        
        return params
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_param_distribution():
    """ทดสอบ parameter distribution"""
    print(f"\n🧪 ทดสอบ Parameter Distribution")
    print("-" * 50)
    
    try:
        from python_LightGBM_15_Tuning import param_dist
        
        print(f"✅ Parameter distribution loaded")
        print(f"   - จำนวนพารามิเตอร์: {len(param_dist)}")
        
        total_combinations = 1
        for param, values in param_dist.items():
            print(f"   - {param}: {len(values)} choices")
            total_combinations *= len(values)
        
        print(f"   - Total combinations: {total_combinations:,}")
        return param_dist
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_quick_hyperparameter_search():
    """ทดสอบ hyperparameter search แบบเร็ว"""
    print(f"\n🧪 ทดสอบ Quick Hyperparameter Search")
    print("-" * 50)
    
    # สร้างข้อมูลทดสอบ
    X, y = create_quick_test_data(n_samples=800, n_features=15)
    
    # แบ่งข้อมูล (temporal split)
    split_idx = int(len(X) * 0.7)
    X_train = X.iloc[:split_idx].drop('DateTime', axis=1)
    y_train = y[:split_idx]
    X_test = X.iloc[split_idx:].drop('DateTime', axis=1)
    y_test = y[split_idx:]
    
    print(f"📊 Data split: Train={len(X_train)}, Test={len(X_test)}")
    
    # Parameter distribution แบบเล็ก
    quick_param_dist = {
        'learning_rate': [0.01, 0.05, 0.1],
        'num_leaves': [15, 31, 63],
        'max_depth': [-1, 5, 10],
        'min_data_in_leaf': [10, 20, 30],
        'feature_fraction': [0.8, 1.0],
        'lambda_l1': [0, 0.1],
        'lambda_l2': [0, 0.1]
    }
    
    print(f"🔍 Quick parameter search...")
    start_time = time.time()
    
    try:
        # Base model
        lgb_estimator = lgb.LGBMClassifier(
            objective='binary',
            metric='auc',
            n_estimators=100,  # ลดลงเพื่อความเร็ว
            random_state=42,
            verbosity=-1
        )
        
        # RandomizedSearchCV
        search = RandomizedSearchCV(
            lgb_estimator,
            param_distributions=quick_param_dist,
            n_iter=10,  # ลดลงเพื่อความเร็ว
            scoring='roc_auc',
            cv=3,  # ใช้ KFold แทน TimeSeriesSplit เพื่อความเร็ว
            verbose=0,
            random_state=42,
            n_jobs=-1
        )
        
        # Fit
        search.fit(X_train, y_train)
        
        # ผลลัพธ์
        elapsed_time = time.time() - start_time
        print(f"⏱️ Search completed in {elapsed_time:.1f} seconds")
        print(f"✅ Best AUC: {search.best_score_:.4f}")
        print(f"🎯 Best params: {search.best_params_}")
        
        # ทดสอบกับ test set
        best_model = search.best_estimator_
        test_pred = best_model.predict_proba(X_test)[:, 1]
        test_auc = roc_auc_score(y_test, test_pred)
        print(f"📊 Test AUC: {test_auc:.4f}")
        
        # เปรียบเทียบกับ default
        default_model = lgb.LGBMClassifier(
            objective='binary',
            n_estimators=100,
            random_state=42,
            verbosity=-1
        )
        default_model.fit(X_train, y_train)
        default_pred = default_model.predict_proba(X_test)[:, 1]
        default_auc = roc_auc_score(y_test, default_pred)
        
        improvement = ((test_auc - default_auc) / default_auc * 100) if default_auc > 0 else 0
        print(f"📈 Improvement over default: {improvement:+.2f}% (Default AUC: {default_auc:.4f})")
        
        return {
            'best_params': search.best_params_,
            'best_cv_score': search.best_score_,
            'test_auc': test_auc,
            'default_auc': default_auc,
            'improvement': improvement,
            'search_time': elapsed_time
        }
        
    except Exception as e:
        print(f"❌ Error in hyperparameter search: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_parameter_sensitivity_quick():
    """ทดสอบ parameter sensitivity แบบเร็ว"""
    print(f"\n🧪 ทดสอบ Parameter Sensitivity (Quick)")
    print("-" * 50)
    
    # สร้างข้อมูลทดสอบ
    X, y = create_quick_test_data(n_samples=600, n_features=10)
    
    # แบ่งข้อมูล
    X_train, X_test, y_train, y_test = train_test_split(
        X.drop('DateTime', axis=1), y, test_size=0.3, random_state=42, stratify=y
    )
    
    print(f"📊 Data: Train={len(X_train)}, Test={len(X_test)}")
    
    # Base parameters
    base_params = {
        'objective': 'binary',
        'metric': 'auc',
        'n_estimators': 100,
        'learning_rate': 0.05,
        'num_leaves': 31,
        'random_state': 42,
        'verbosity': -1
    }
    
    # ทดสอบ learning rate
    print(f"\n📈 Learning Rate Sensitivity:")
    lr_values = [0.01, 0.05, 0.1, 0.2]
    lr_results = []
    
    for lr in lr_values:
        params = base_params.copy()
        params['learning_rate'] = lr
        
        model = lgb.LGBMClassifier(**params)
        model.fit(X_train, y_train)
        pred = model.predict_proba(X_test)[:, 1]
        auc = roc_auc_score(y_test, pred)
        lr_results.append(auc)
        
        print(f"   LR={lr:4.2f}: AUC={auc:.4f}")
    
    # ทดสอบ num_leaves
    print(f"\n🌿 Num Leaves Sensitivity:")
    leaves_values = [15, 31, 63, 127]
    leaves_results = []
    
    for leaves in leaves_values:
        params = base_params.copy()
        params['num_leaves'] = leaves
        
        model = lgb.LGBMClassifier(**params)
        model.fit(X_train, y_train)
        pred = model.predict_proba(X_test)[:, 1]
        auc = roc_auc_score(y_test, pred)
        leaves_results.append(auc)
        
        print(f"   Leaves={leaves:3d}: AUC={auc:.4f}")
    
    # หาค่าที่ดีที่สุด
    best_lr = lr_values[np.argmax(lr_results)]
    best_leaves = leaves_values[np.argmax(leaves_results)]
    
    print(f"\n🎯 Best parameters found:")
    print(f"   - Best Learning Rate: {best_lr} (AUC: {max(lr_results):.4f})")
    print(f"   - Best Num Leaves: {best_leaves} (AUC: {max(leaves_results):.4f})")
    
    return {
        'lr_sensitivity': dict(zip(lr_values, lr_results)),
        'leaves_sensitivity': dict(zip(leaves_values, leaves_results)),
        'best_lr': best_lr,
        'best_leaves': best_leaves
    }

def main():
    """ฟังก์ชันหลักสำหรับการทดสอบเร็ว"""
    print("🚀 Quick Hyperparameter Tuning Test")
    print("=" * 60)
    
    results = {}
    
    # 1. ทดสอบ basic parameters
    print(f"\n1️⃣ Testing Basic Parameters...")
    basic_result = test_basic_lgbm_params()
    results['basic_params'] = basic_result is not None
    
    # 2. ทดสอบ parameter distribution
    print(f"\n2️⃣ Testing Parameter Distribution...")
    param_dist_result = test_param_distribution()
    results['param_dist'] = param_dist_result is not None
    
    # 3. ทดสอบ hyperparameter search
    print(f"\n3️⃣ Testing Hyperparameter Search...")
    search_result = test_quick_hyperparameter_search()
    results['hyperparameter_search'] = search_result
    
    # 4. ทดสอบ parameter sensitivity
    print(f"\n4️⃣ Testing Parameter Sensitivity...")
    sensitivity_result = test_parameter_sensitivity_quick()
    results['parameter_sensitivity'] = sensitivity_result
    
    # สรุปผลลัพธ์
    print(f"\n📊 Summary of Quick Tests")
    print("=" * 60)
    
    passed_tests = 0
    total_tests = 4
    
    if results['basic_params']:
        print("✅ Basic Parameters: PASSED")
        passed_tests += 1
    else:
        print("❌ Basic Parameters: FAILED")
    
    if results['param_dist']:
        print("✅ Parameter Distribution: PASSED")
        passed_tests += 1
    else:
        print("❌ Parameter Distribution: FAILED")
    
    if results['hyperparameter_search']:
        print("✅ Hyperparameter Search: PASSED")
        search_data = results['hyperparameter_search']
        print(f"   - Best CV AUC: {search_data['best_cv_score']:.4f}")
        print(f"   - Test AUC: {search_data['test_auc']:.4f}")
        print(f"   - Improvement: {search_data['improvement']:+.2f}%")
        print(f"   - Search time: {search_data['search_time']:.1f}s")
        passed_tests += 1
    else:
        print("❌ Hyperparameter Search: FAILED")
    
    if results['parameter_sensitivity']:
        print("✅ Parameter Sensitivity: PASSED")
        sens_data = results['parameter_sensitivity']
        print(f"   - Best LR: {sens_data['best_lr']}")
        print(f"   - Best Leaves: {sens_data['best_leaves']}")
        passed_tests += 1
    else:
        print("❌ Parameter Sensitivity: FAILED")
    
    print(f"\n🎯 Overall Result: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Hyperparameter tuning system is ready.")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
    
    return results

if __name__ == "__main__":
    main()

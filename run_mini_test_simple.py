#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
สคริปต์ทดสอบแบบย่อ - รัน 1 ไฟล์จากแต่ละกลุ่ม (ไม่ลบโฟลเดอร์เดิม)
"""

import os
import sys

def run_mini_test():
    """
    รันการทดสอบแบบย่อ
    """
    print("🚀 เริ่มการทดสอบแบบย่อ")
    print("=" * 50)
    
    # การตั้งค่าสำหรับการทดสอบ
    NUM_TRAINING_ROUNDS = 1
    
    test_groups = {
        "M30": ["CSV_Files_Fixed/GOLD_M30_FIXED.csv"],
        "M60": ["CSV_Files_Fixed/GOLD_H1_FIXED.csv"]
    }
    
    Steps_to_do = True
    Save_File = True
    
    # Import โค้ดหลัก
    try:
        import python_LightGBM_15_Tuning as main_module
        
        # อัพเดทการตั้งค่า
        original_rounds = main_module.NUM_TRAINING_ROUNDS
        original_groups = main_module.test_groups.copy()
        original_steps = main_module.Steps_to_do
        original_save = main_module.Save_File
        
        main_module.NUM_TRAINING_ROUNDS = NUM_TRAINING_ROUNDS
        main_module.test_groups = test_groups
        main_module.Steps_to_do = Steps_to_do
        main_module.Save_File = Save_File
        
        print(f"📊 จำนวนรอบการเทรน: {NUM_TRAINING_ROUNDS}")
        print(f"📁 กลุ่มทดสอบ: {list(test_groups.keys())}")
        print(f"🔍 Debug mode: {Steps_to_do}")
        print(f"💾 บันทึกไฟล์: {Save_File}")
        
        # สร้างโฟลเดอร์ทดสอบ
        test_output_folder = "Test_LightGBM/results_mini_test"
        os.makedirs(test_output_folder, exist_ok=True)
        
        # รันการทดสอบ
        for group_name, group_files in test_groups.items():
            print(f"\n=== เริ่มการเทรน กลุ่ม {group_name} ===")
            
            # กำหนด output_folder เฉพาะกลุ่ม
            output_folder = f"{test_output_folder}/{group_name}"
            os.makedirs(output_folder, exist_ok=True)
            print(f"📁 ใช้โฟลเดอร์ทดสอบ: {output_folder}")
            
            input_files = group_files
            
            # เทรน 1 รอบ
            for run_i in range(NUM_TRAINING_ROUNDS):
                current_run_identifier = run_i + 1
                print(f"\n### เริ่มการเทรนรอบที่ {current_run_identifier}/{NUM_TRAINING_ROUNDS} ของกลุ่ม {group_name} ###")
                
                try:
                    # เรียกใช้ฟังก์ชัน main ที่แก้ไขแล้ว
                    main_module.main(run_identifier=current_run_identifier, group_name=group_name, input_files=input_files)
                    print(f"✅ เทรนกลุ่ม {group_name} สำเร็จ")
                    
                    # ตรวจสอบผลลัพธ์
                    check_results(output_folder, group_name)
                    
                except Exception as e:
                    print(f"❌ เกิดข้อผิดพลาดในกลุ่ม {group_name}: {e}")
                    import traceback
                    traceback.print_exc()
            
            print(f"=== เสร็จสิ้นการเทรนกลุ่ม {group_name} ===")
        
        # สร้างสรุปการเทรดรายวัน
        print(f"\n🏗️ กำลังสร้างสรุปการเทรดรายวัน...")
        try:
            main_module.print_trading_schedule_summary()
            print("✅ สร้างสรุปการเทรดรายวันสำเร็จ")
        except Exception as e:
            print(f"⚠️ ไม่สามารถสร้างสรุปการเทรดรายวัน: {e}")
        
        # คืนค่าการตั้งค่าเดิม
        main_module.NUM_TRAINING_ROUNDS = original_rounds
        main_module.test_groups = original_groups
        main_module.Steps_to_do = original_steps
        main_module.Save_File = original_save
        
        print("\n✅ การทดสอบแบบย่อเสร็จสิ้น")
        
        # ตรวจสอบผลลัพธ์รวม
        check_final_results(test_output_folder)
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        import traceback
        traceback.print_exc()

def check_results(output_folder, group_name):
    """
    ตรวจสอบผลลัพธ์ของแต่ละกลุ่ม
    """
    print(f"\n🔍 ตรวจสอบผลลัพธ์กลุ่ม {group_name}")
    
    if os.path.exists(output_folder):
        files = os.listdir(output_folder)
        print(f"📁 ไฟล์ในโฟลเดอร์ {output_folder}: {len(files)} ไฟล์")
        
        # ตรวจสอบไฟล์ที่คาดหวัง
        expected_prefixes = ['030_' if group_name == 'M30' else '060_']
        unexpected_prefixes = ['060_' if group_name == 'M30' else '030_']
        
        expected_files = [f for f in files if any(f.startswith(prefix) for prefix in expected_prefixes)]
        unexpected_files = [f for f in files if any(f.startswith(prefix) for prefix in unexpected_prefixes)]
        
        print(f"  ✅ ไฟล์ที่ถูกต้อง ({group_name}): {len(expected_files)} ไฟล์")
        if expected_files:
            print(f"     ตัวอย่าง: {expected_files[:3]}")
        
        if unexpected_files:
            print(f"  ⚠️ ไฟล์ที่ไม่ควรอยู่: {len(unexpected_files)} ไฟล์")
            print(f"     ตัวอย่าง: {unexpected_files[:3]}")
        else:
            print(f"  ✅ ไม่พบไฟล์ที่ไม่ควรอยู่")
    else:
        print(f"❌ ไม่พบโฟลเดอร์: {output_folder}")

def check_final_results(test_output_folder):
    """
    ตรวจสอบผลลัพธ์รวม
    """
    print(f"\n🔍 ตรวจสอบผลลัพธ์รวม")
    print("=" * 50)
    
    # ตรวจสอบโครงสร้างโฟลเดอร์
    if os.path.exists(test_output_folder):
        print(f"📁 โฟลเดอร์ทดสอบ: {test_output_folder}")
        
        for timeframe in ['M30', 'M60']:
            timeframe_folder = os.path.join(test_output_folder, timeframe)
            if os.path.exists(timeframe_folder):
                files = os.listdir(timeframe_folder)
                print(f"  📂 {timeframe}: {len(files)} ไฟล์")
                
                # ตรวจสอบการปนเปื้อน
                if timeframe == 'M30':
                    m30_files = [f for f in files if f.startswith('030_')]
                    m60_files = [f for f in files if f.startswith('060_')]
                    print(f"    ✅ ไฟล์ M30: {len(m30_files)}")
                    if m60_files:
                        print(f"    ⚠️ ไฟล์ M60 ที่ไม่ควรอยู่: {len(m60_files)}")
                    else:
                        print(f"    ✅ ไม่มีไฟล์ M60 ปนเปื้อน")
                        
                elif timeframe == 'M60':
                    m30_files = [f for f in files if f.startswith('030_')]
                    m60_files = [f for f in files if f.startswith('060_')]
                    print(f"    ✅ ไฟล์ M60: {len(m60_files)}")
                    if m30_files:
                        print(f"    ⚠️ ไฟล์ M30 ที่ไม่ควรอยู่: {len(m30_files)}")
                    else:
                        print(f"    ✅ ไม่มีไฟล์ M30 ปนเปื้อน")
            else:
                print(f"  ❌ ไม่พบโฟลเดอร์: {timeframe}")
    
    # ตรวจสอบไฟล์สรุปการเทรด
    schedule_file = "Test_LightGBM/results/daily_trading_schedule_summary.txt"
    if os.path.exists(schedule_file):
        print(f"\n📄 ตรวจสอบไฟล์สรุปการเทรด: {schedule_file}")
        
        with open(schedule_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "GOLD" in content:
            print("  ✅ พบสัญลักษณ์ GOLD ในสรุป")
        else:
            print("  ⚠️ ไม่พบสัญลักษณ์ GOLD ในสรุป")
            
        if "AUDUSD EURGBP" in content and content.count("AUDUSD EURGBP") > 3:
            print("  ⚠️ ยังคงแสดงเฉพาะ 'AUDUSD EURGBP'")
        else:
            print("  ✅ ไม่พบปัญหาการแสดงสัญลักษณ์เดิม")
    else:
        print(f"\n❌ ไม่พบไฟล์สรุปการเทรด: {schedule_file}")

if __name__ == "__main__":
    print("🔧 การทดสอบแบบย่อ - ตรวจสอบการแก้ไข")
    print("=" * 60)
    
    run_mini_test()
    
    print("\n📋 สรุปการทดสอบ:")
    print("1. ตรวจสอบการแยก timeframe ในโฟลเดอร์")
    print("2. ตรวจสอบการดึงสัญลักษณ์ที่ถูกต้อง")
    print("3. ตรวจสอบการสร้างสรุปการเทรดรายวัน")
    print("4. ยืนยันว่าไม่มีการปนเปื้อนระหว่าง timeframe")

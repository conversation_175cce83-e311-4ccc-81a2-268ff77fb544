# การจัดวางการเรียกใช้ Code สำหรับ Multi-Model Architecture

## 📋 ฟังก์ชันที่เพิ่มเข้าไปใน python_LightGBM_16_Signal.py

### ✅ **ฟังก์ชันหลัก:**

1. **`find_optimal_threshold_multi_model()`** - หา optimal threshold แยกตาม scenario
2. **`find_optimal_nbars_sl_multi_model()`** - หา optimal nBars_SL แยกตาม scenario
3. **`load_scenario_threshold()`** - โหลด threshold สำหรับ scenario ที่กำหนด
4. **`load_scenario_nbars()`** - โหลด nBars_SL สำหรับ scenario ที่กำหนด
5. **`get_optimal_parameters()`** - ดึงพารามิเตอร์ที่เหมาะสมตามสถานการณ์ตลาด
6. **`predict_with_optimal_parameters()`** - ทำนายด้วยพารามิเตอร์ที่เหมาะสม
7. **`run_multi_model_optimization()`** - รันการหา optimal parameters แบบครบวงจร

## 🔄 การจัดลำดับการทำงาน

### **Phase 1: การเทรนโมเดล**
```python
# ใน python_LightGBM_16_Signal.py
USE_MULTI_MODEL_ARCHITECTURE = True
python python_LightGBM_16_Signal.py
```

**ผลลัพธ์:**
```
LightGBM_Multi/models/
├─ trend_following/
│   ├─ 060_GOLD_trained.pkl
│   ├─ 060_GOLD_features.pkl
│   └─ 060_GOLD_scaler.pkl
└─ counter_trend/
    ├─ 060_GOLD_trained.pkl
    ├─ 060_GOLD_features.pkl
    └─ 060_GOLD_scaler.pkl
```

### **Phase 2: การหา Optimal Parameters (อัตโนมัติ)**
```python
# เพิ่มใน run_main_analysis() แล้ว
if USE_MULTI_MODEL_ARCHITECTURE:
    for symbol, timeframe in trained_models:
        run_multi_model_optimization(symbol, timeframe)
```

**ผลลัพธ์:**
```
LightGBM_Multi/thresholds/
├─ 060_GOLD_trend_following_optimal_threshold.pkl
├─ 060_GOLD_counter_trend_optimal_threshold.pkl
├─ 060_GOLD_trend_following_optimal_nBars_SL.pkl
├─ 060_GOLD_counter_trend_optimal_nBars_SL.pkl
└─ optimization_summary.json
```

### **Phase 3: การใช้งานใน Production**
```python
# ใน MT5 WebRequest Server หรือ Trading System
result = predict_with_optimal_parameters(symbol, timeframe, market_data, action_type)
```

## 📊 การเรียกใช้งานแต่ละฟังก์ชัน

### **1. การโหลดโมเดล**
```python
loaded_models = load_scenario_models(symbol, timeframe)
# Returns: {'trend_following': {...}, 'counter_trend': {...}}
```

### **2. การหา Optimal Threshold**
```python
optimal_thresholds = find_optimal_threshold_multi_model(
    models_dict=loaded_models,
    val_df=validation_data,
    symbol=symbol,
    timeframe=timeframe
)
# Returns: {'trend_following': 0.65, 'counter_trend': 0.58}
```

### **3. การหา Optimal nBars_SL**
```python
optimal_nbars = find_optimal_nbars_sl_multi_model(
    models_dict=loaded_models,
    val_df=validation_data,
    symbol=symbol,
    timeframe=timeframe
)
# Returns: {'trend_following': 8, 'counter_trend': 6}
```

### **4. การโหลดพารามิเตอร์**
```python
# โหลดแยกตาม scenario
threshold = load_scenario_threshold(symbol, timeframe, "trend_following")
nbars = load_scenario_nbars(symbol, timeframe, "trend_following")

# หรือโหลดตามสถานการณ์
params = get_optimal_parameters(symbol, timeframe, "uptrend", "buy")
# Returns: {'scenario': 'trend_following', 'threshold': 0.65, 'nBars_SL': 8}
```

### **5. การทำนาย**
```python
result = predict_with_optimal_parameters(symbol, timeframe, market_data, "buy")
# Returns: {
#     'success': True,
#     'prediction': True,
#     'confidence': 0.72,
#     'threshold': 0.65,
#     'nBars_SL': 8,
#     'scenario': 'trend_following',
#     'market_condition': 'uptrend'
# }
```

## 🔧 การตรวจสอบและ Debug

### **ตรวจสอบการ Load/Save:**
```python
# ตรวจสอบไฟล์ที่บันทึก
import os
thresholds_dir = "LightGBM_Multi/thresholds"
files = os.listdir(thresholds_dir)
print(f"Threshold files: {[f for f in files if 'threshold' in f]}")
print(f"nBars_SL files: {[f for f in files if 'nBars_SL' in f]}")

# ตรวจสอบการโหลด
threshold = load_scenario_threshold("GOLD", 60, "trend_following")
print(f"Loaded threshold: {threshold}")
```

### **ตรวจสอบการทำงานของ Scenario Selection:**
```python
test_cases = [
    {'market': 'uptrend', 'action': 'buy', 'expected': 'trend_following'},
    {'market': 'uptrend', 'action': 'sell', 'expected': 'counter_trend'},
    {'market': 'downtrend', 'action': 'buy', 'expected': 'counter_trend'},
    {'market': 'downtrend', 'action': 'sell', 'expected': 'trend_following'}
]

for case in test_cases:
    params = get_optimal_parameters("GOLD", 60, case['market'], case['action'])
    print(f"{case['market']} + {case['action']} → {params['scenario']} (expected: {case['expected']})")
```

## 🚀 การใช้งานใน Production

### **ใน MT5 WebRequest Server:**
```python
def handle_trading_request(symbol, timeframe, market_data, action_type):
    """
    จัดการคำขอการเทรดจาก MT5
    """
    try:
        # ทำนายด้วยพารามิเตอร์ที่เหมาะสม
        result = predict_with_optimal_parameters(
            symbol, timeframe, market_data, action_type
        )
        
        if result['success'] and result['prediction']:
            return {
                'signal': action_type.upper(),
                'confidence': result['confidence'],
                'threshold': result['threshold'],
                'nBars_SL': result['nBars_SL'],
                'scenario': result['scenario']
            }
        else:
            return {'signal': 'HOLD'}
            
    except Exception as e:
        return {'signal': 'HOLD', 'error': str(e)}
```

### **การ Monitor และ Log:**
```python
def log_prediction_result(result, symbol, timeframe):
    """
    บันทึก log การทำนาย
    """
    log_entry = {
        'timestamp': datetime.now().isoformat(),
        'symbol': symbol,
        'timeframe': timeframe,
        'prediction': result['prediction'],
        'confidence': result['confidence'],
        'scenario': result['scenario'],
        'threshold': result['threshold'],
        'nBars_SL': result['nBars_SL']
    }
    
    # บันทึกลง log file
    with open('prediction_log.json', 'a') as f:
        f.write(json.dumps(log_entry) + '\n')
```

## 📁 โครงสร้างไฟล์ที่สมบูรณ์

```
LightGBM_Multi/
├─ models/
│   ├─ trend_following/
│   │   ├─ 060_GOLD_trained.pkl
│   │   ├─ 060_GOLD_features.pkl
│   │   └─ 060_GOLD_scaler.pkl
│   └─ counter_trend/
│       ├─ 060_GOLD_trained.pkl
│       ├─ 060_GOLD_features.pkl
│       └─ 060_GOLD_scaler.pkl
├─ thresholds/
│   ├─ 060_GOLD_trend_following_optimal_threshold.pkl
│   ├─ 060_GOLD_counter_trend_optimal_threshold.pkl
│   ├─ 060_GOLD_trend_following_optimal_nBars_SL.pkl
│   ├─ 060_GOLD_counter_trend_optimal_nBars_SL.pkl
│   └─ GOLD_60_time_filters.pkl
├─ results/
│   ├─ trend_following/
│   ├─ counter_trend/
│   └─ M60/
├─ feature_importance/
│   └─ 060_must_have_features.pkl
└─ optimization_summary.json
```

## ⚠️ ข้อควรระวัง

### **1. การจัดการ Error:**
- ตรวจสอบการมีอยู่ของไฟล์ก่อนโหลด
- ใช้ default values เมื่อโหลดไม่สำเร็จ
- Log errors สำหรับ debugging

### **2. การ Performance:**
- Cache โมเดลที่โหลดแล้ว
- ใช้ batch processing เมื่อเป็นไปได้
- Monitor memory usage

### **3. การ Maintenance:**
- อัปเดตพารามิเตอร์เป็นระยะ
- ตรวจสอบประสิทธิภาพอย่างสม่ำเสมอ
- Backup ไฟล์สำคัญ

## 📝 ไฟล์ที่เกี่ยวข้อง

1. **`python_LightGBM_16_Signal.py`** - ไฟล์หลักที่มีฟังก์ชันทั้งหมด
2. **`complete_multi_model_workflow.py`** - ตัวอย่างการใช้งานครบวงจร
3. **`test_multi_model_optimization.py`** - ทดสอบฟังก์ชันต่างๆ
4. **`MULTI_MODEL_CODE_ORGANIZATION.md`** - คู่มือการจัดวาง code

## 🎯 ขั้นตอนถัดไป

1. **รันการเทรนโมเดล:** `USE_MULTI_MODEL_ARCHITECTURE = True`
2. **ทดสอบการทำงาน:** `python complete_multi_model_workflow.py`
3. **ปรับใช้ใน Production:** นำฟังก์ชันไปใช้ใน MT5 WebRequest Server
4. **Monitor และปรับปรุง:** ติดตามประสิทธิภาพและปรับแต่งพารามิเตอร์

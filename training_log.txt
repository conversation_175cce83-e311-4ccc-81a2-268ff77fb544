﻿
==================================================
สร้างและตรวจสอบ โฟลเดอร์

🏗️ เปิดใช้งาน __name__

🏗️ เปิดใช้งาน parse arguments
ℹ️ ไม่มี command line arguments เฉพาะ - จะรันการวิเคราะห์แบบปกติ

🏗️ เปิดใช้งาน __name__ and len()
🚀 เริ่มต้นการทำงาน LightGBM Multi-class Trading Analysis
================================================================================
🎯 จำนวนรอบการเทรนที่กำหนด: 1
================================================================================
📊 เริ่มการเทรนโมเดล...

🏗️ เปิดใช้งาน run main analysis

=== เริ่มการเทรน กลุ่ม M60 ทั้งหมด 1 รอบ ===

### เริ่มการเทรนรอบที่ 1/1 ของกลุ่ม M60 ###

🏗️ เปิดใช้งาน main
📁 ใช้ output_folder สำหรับกลุ่ม M60: Test_LightGBM/results/M60
------------------------------------------------------------
กำลังทดสอบไฟล์: CSV_Files_Fixed/GOLD_H1_FIXED.csv รอบที่ 1/1
------------------------------------------------------------

🏗️ เปิดใช้งาน parse filename
ข้อมูลพื้นฐาน LightGBM 60 GOLD

🏗️ เปิดใช้งาน load optimal threshold
✅ Loaded optimal threshold สำเร็จ GOLD 60: 0.3500

🏗️ เปิดใช้งาน load optimal nbars
✅ Loaded optimal nBars SL สำเร็จ GOLD 60: 3

🏗️ เปิดใช้งาน load and process data
✅ ข้อมูล : df_features
<class 'pandas.core.frame.DataFrame'>
RangeIndex: 71843 entries, 0 to 71842
Data columns (total 7 columns):
 #   Column  Non-Null Count  Dtype  
---  ------  --------------  -----  
 0   Date    71843 non-null  object 
 1   Time    71843 non-null  object 
 2   Open    71843 non-null  float64
 3   High    71843 non-null  float64
 4   Low     71843 non-null  float64
 5   Close   71843 non-null  float64
 6   Volume  71843 non-null  int64  
dtypes: float64(4), int64(1), object(2)
memory usage: 3.8+ MB
None
         Date      Time     Open     High      Low    Close  Volume
0  2013.05.01  00:00:00  1475.49  1477.75  1440.17  1457.65   66920
1  2013.05.02  00:00:00  1456.97  1473.45  1448.53  1466.86   88650
2  2013.05.03  00:00:00  1466.90  1487.81  1455.95  1470.24   86136
3  2013.05.06  00:00:00  1470.15  1478.76  1464.36  1469.59   75641
4  2013.05.07  00:00:00  1469.28  1470.42  1441.17  1452.08   93750
             Date      Time     Open     High      Low    Close  Volume
71838  2025.04.30  19:00:00  3308.16  3314.29  3307.19  3310.75    8032
71839  2025.04.30  20:00:00  3310.72  3313.46  3304.94  3305.29    6843
71840  2025.04.30  21:00:00  3305.27  3307.80  3300.39  3301.72    6617
71841  2025.04.30  22:00:00  3301.88  3305.77  3292.13  3295.86    6753
71842  2025.04.30  23:00:00  3295.81  3297.99  3283.02  3288.42    5395

✅ ข้อมูล df ก่อนทำ Lag Features
         Date      Time  ...  EMA_diff_x_RSI14  ADX_14_x_BBwidth
0  2013.05.01  00:00:00  ...               NaN               NaN
1  2013.05.02  00:00:00  ...               NaN               NaN
2  2013.05.03  00:00:00  ...               NaN               NaN
3  2013.05.06  00:00:00  ...               NaN               NaN
4  2013.05.07  00:00:00  ...               NaN               NaN

[5 rows x 107 columns]

✅ ข้อมูล df_combined หลังสร้าง Features:
<class 'pandas.core.frame.DataFrame'>
RangeIndex: 71843 entries, 0 to 71842
Columns: 220 entries, Date to Volume_MA_20
dtypes: datetime64[ns](1), float64(196), int32(17), int64(4), object(2)
memory usage: 115.9+ MB
None
         Date      Time   DateTime  ...  Close_MA_20  Close_Std_20  Volume_MA_20
0  2013.05.01  00:00:00 2013-05-01  ...          NaN           NaN           NaN
1  2013.05.02  00:00:00 2013-05-02  ...  1457.650000           NaN  66920.000000
2  2013.05.03  00:00:00 2013-05-03  ...  1462.255000      6.512453  77785.000000
3  2013.05.06  00:00:00 2013-05-06  ...  1464.916667      6.516090  80568.666667
4  2013.05.07  00:00:00 2013-05-07  ...  1466.085000      5.810878  79336.750000

[5 rows x 220 columns]
✅ สร้าง Indicators และ Features เรียบร้อย (ลบแถวที่ขาดหายไป 50 จาก 71843 แถว)

✅ ข้อมูล df หลังสร้างรวม df_ft_combined
<class 'pandas.core.frame.DataFrame'>
Index: 71793 entries, 50 to 71842
Columns: 220 entries, Date to Volume_MA_20
dtypes: datetime64[ns](1), float64(196), int32(17), int64(4), object(2)
memory usage: 116.4+ MB
None
             Date      Time  ... Close_Std_20  Volume_MA_20
50     2013.05.13  05:00:00  ...     9.915127      10538.20
51     2013.05.13  06:00:00  ...     8.490744      10560.10
52     2013.05.13  07:00:00  ...     7.324672      10546.50
53     2013.05.13  08:00:00  ...     6.975964      10454.30
54     2013.05.13  09:00:00  ...     6.139670      10385.70
...           ...       ...  ...          ...           ...
71838  2025.04.30  19:00:00  ...    14.959636       8542.60
71839  2025.04.30  20:00:00  ...    14.525772       8644.00
71840  2025.04.30  21:00:00  ...    14.154798       8848.40
71841  2025.04.30  22:00:00  ...    13.616630       9075.40
71842  2025.04.30  23:00:00  ...    13.153573       9245.15

[71793 rows x 220 columns]

🔍 ตรวจสอบ Temporal Dependence และคุณสมบัติอนุกรมเวลา
- ข้อมูลเรียงตามเวลา: ใช่ (ควรเป็น 'ใช่')
- ช่วงเวลาข้อมูล: 2013-05-13 05:00:00 ถึง 2025-04-30 23:00:00
- ระยะเวลารวม: 4370 days 18:00:00
- ช่วงห่างระหว่างบันทึก (เฉลี่ย): 0 days 01:27:40.095832404
- ช่วงห่างระหว่างบันทึก (สูงสุด): 3 days 07:00:00
- ช่วงห่างระหว่างบันทึก (ต่ำสุด): 0 days 01:00:00
- จำนวนช่วงเวลาที่หายไป: 2155 (จากทั้งหมด 71792 ช่วง)
⚠️ เตือน : พบช่วงเวลาที่หายไปซึ่งอาจส่งผลต่อการวิเคราะห์
- จำนวน timestamp ที่ซ้ำกัน: 0

🔍 ตรวจสอบ Stationarity ของข้อมูล:

🏗️ เปิดใช้งาน check stationarity
📊 ผลการทดสอบ Stationarity สำหรับ Close:
ADF Statistic: 2.9049
p-value: 1.0000
Critical Values:
   1%: -3.4304
   5%: -2.8616
   10%: -2.5668

🏗️ เปิดใช้งาน check stationarity
📊 ผลการทดสอบ Stationarity สำหรับ Returns:
ADF Statistic: -56.6878
p-value: 0.0000
Critical Values:
   1%: -3.4304
   5%: -2.8616
   10%: -2.5668

💾 บันทึกรายงาน Temporal Analysis ที่: Test_LightGBM/results\060_GOLD_temporal_report.json

📌 จำนวน Missing Values หลังการประมวลผลเบื้องต้น:
Series([], dtype: int64)

📌 จำนวน Missing Values หลังการประมวลผล:
Series([], dtype: int64)
             Date      Time  ... Close_Std_20  Volume_MA_20
50     2013.05.13  05:00:00  ...     9.915127      10538.20
51     2013.05.13  06:00:00  ...     8.490744      10560.10
52     2013.05.13  07:00:00  ...     7.324672      10546.50
53     2013.05.13  08:00:00  ...     6.975964      10454.30
54     2013.05.13  09:00:00  ...     6.139670      10385.70
...           ...       ...  ...          ...           ...
71838  2025.04.30  19:00:00  ...    14.959636       8542.60
71839  2025.04.30  20:00:00  ...    14.525772       8644.00
71840  2025.04.30  21:00:00  ...    14.154798       8848.40
71841  2025.04.30  22:00:00  ...    13.616630       9075.40
71842  2025.04.30  23:00:00  ...    13.153573       9245.15

[71793 rows x 220 columns]

🏗️ เปิดใช้งาน check data quality
==================================================
Data Quality Check for CSV_Files_Fixed/GOLD_H1_FIXED.csv
==================================================

[4] Duplicate Rows: 0

🔍 ตรวจสอบข้อมูลก่อนสร้าง trade cycles:
ช่วงเวลาข้อมูล: 2013.05.13 ถึง 2025.04.30
ค่าเฉลี่ย Close: 1591.26
ค่า EMA50 ล่าสุด: 3306.59
ค่า RSI14 ล่าสุด: 35.41
ค่า MACD ล่าสุด: -3.32
Unique values in df['Entry_DayOfWeek']: [0 1 2 3 4 5 6]

🔍 กำลังสร้าง trade cycles...

⚠️ ไม่พบไฟล์รายชื่อ Features ที่ Model ใช้: Test_LightGBM/models/060_GOLD\LightGBM_060_GOLD_features.pkl
🔍 ตรวจสอบ columns
['Date', 'Time', 'DateTime', 'Open', 'High', 'Low', 'Close', 'Volume', 'Entry_DayOfWeek', 'Entry_Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CLp', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'EMA50', 'EMA100', 'EMA200', 'EMA_diff', 'MA_Cross', 'Price_above_EMA50', 'Rolling_Vol_5', 'Rolling_Vol_15', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'RSI14', 'RSI_signal', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_14', 'DMP_14', 'DMN_14', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'Support', 'Resistance', 'PullBack_Up', 'PullBack_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_above_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_Up', 'RSI14_x_PullBack_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20']
🔎 ใช้ entry_func (Legacy): default

🏗️ เปิดใช้งาน create trade cycles with model
❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม
🔍 ใช้ start_index = 50 (ข้อมูลทั้งหมด 71793 แถว)

🔍 ตรวจสอบ Temporal Dependence สำหรับการเทรด
- ช่วงเวลาที่จะทำ backtest: 2013-05-15 07:00:00 ถึง 2025-04-30 23:00:00
- จำนวนแท่งข้อมูลทั้งหมดสำหรับ backtest: 71743

ความถี่ของช่วงเวลาระหว่างแท่งข้อมูล:
DateTime
0 days 01:00:00    69637
0 days 02:00:00     1440
2 days 02:00:00      335
2 days 01:00:00      234
0 days 04:00:00       33
Name: count, dtype: int64

▶️ เริ่ม Backtest จาก index: 50 (เพื่อให้ Indicators คำนวณได้ครบ)

🔍 ตรวจสอบ Look-Ahead Bias ก่อนเริ่ม Backtest
⚠️ ไม่พบ model_features ข้ามการตรวจสอบ Look-Ahead Bias

✅ การตรวจสอบ Look-Ahead Bias เสร็จสมบูรณ์ ไม่มีปัญหาที่ตรวจพบโดยอัตโนมัติ

❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม

🔍 สรุปการตรวจสอบ Look-Ahead Bias
- จำนวนการทำนายทั้งหมด: 71743
- จำนวนครั้งที่พบ NaN ใน Features: 0
- จำนวนครั้งที่พบ Features อาจมีปัญหา: 0
✅ ไม่พบ ปัญหาการใช้ข้อมูลจากอนาคต (Look-Ahead Bias)

📌 ข้อมูลก่อนตรวจสอบ:
จำนวนแถวข้อมูลทั้งหมด: 71793 ตัวอย่างข้อมูล df
          Date      Time  ... Close_Std_20  Volume_MA_20
50  2013.05.13  05:00:00  ...     9.915127       10538.2
51  2013.05.13  06:00:00  ...     8.490744       10560.1
52  2013.05.13  07:00:00  ...     7.324672       10546.5
53  2013.05.13  08:00:00  ...     6.975964       10454.3
54  2013.05.13  09:00:00  ...     6.139670       10385.7

[5 rows x 220 columns]
จำนวนการซื้อขายที่พบ: 3691 ตัวอย่างข้อมูล trade_df
            Entry Time  ...  Volume Spike at Entry
0  2013.05.16 05:00:00  ...               0.942704
1  2013.05.16 18:00:00  ...               1.623436
2  2013.05.16 21:00:00  ...               0.896398
3  2013.05.17 09:00:00  ...               0.872021
4  2013.05.17 16:00:00  ...               2.189390

[5 rows x 20 columns]

📊 สถิติการซื้อขาย:
========================================
ประเภท              ค่าสถิติ            
----------------------------------------

🏗️ เปิดใช้งาน analyze trade performance
📈 สถิติสำหรับ Buy Trades:
Win%                40.32               
Expectancy          -8.49               
📈 สถิติสำหรับ Sell Trades:
Win%                36.50               
Expectancy          -30.87              
📈 สถิติสำหรับ Buy_sell Trades:
Win%                38.26               
Expectancy          -20.58              
========================================

📊 สถิติรายวัน:
วัน       Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
Monday    27.49          662                 
Tuesday   27.77          839                 
Wednesday 28.39          775                 
Thursday  29.86          653                 
Friday    27.03          762                 
========================================

📊 สถิติรายชั่วโมง:
ชั่วโมง   Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
4         22.56          133                 
5         32.97          185                 
6         23.40          47                  
7         12.50          16                  
8         43.33          30                  
9         31.30          131                 
10        27.11          391                 
11        32.32          396                 
12        31.25          256                 
13        27.37          179                 
14        30.57          193                 
15        27.15          302                 
16        23.08          429                 
17        25.69          327                 
18        25.83          240                 
19        28.74          174                 
20        30.43          138                 
21        29.84          124                 
========================================

🔍 กำลังรวม features กับ trade data...
🔍 ทดลองพิมพ์ trade_df
               Entry Time  ...  Volume Spike at Entry
0     2013.05.16 05:00:00  ...               0.942704
1     2013.05.16 18:00:00  ...               1.623436
2     2013.05.16 21:00:00  ...               0.896398
3     2013.05.17 09:00:00  ...               0.872021
4     2013.05.17 16:00:00  ...               2.189390
...                   ...  ...                    ...
3686  2025.04.28 04:00:00  ...               0.908728
3687  2025.04.28 11:00:00  ...               1.007098
3688  2025.04.29 17:00:00  ...               2.244135
3689  2025.04.29 21:00:00  ...               0.871237
3690  2025.04.30 04:00:00  ...               1.079036

[3691 rows x 20 columns]

🔍 ทดลองพิมพ์ df
             Date      Time  ... Close_Std_20  Volume_MA_20
50     2013.05.13  05:00:00  ...     9.915127      10538.20
51     2013.05.13  06:00:00  ...     8.490744      10560.10
52     2013.05.13  07:00:00  ...     7.324672      10546.50
53     2013.05.13  08:00:00  ...     6.975964      10454.30
54     2013.05.13  09:00:00  ...     6.139670      10385.70
...           ...       ...  ...          ...           ...
71838  2025.04.30  19:00:00  ...    14.959636       8542.60
71839  2025.04.30  20:00:00  ...    14.525772       8644.00
71840  2025.04.30  21:00:00  ...    14.154798       8848.40
71841  2025.04.30  22:00:00  ...    13.616630       9075.40
71842  2025.04.30  23:00:00  ...    13.153573       9245.15

[71793 rows x 220 columns]

ตัวอย่าง Entry_DateTime ใน trade_df: 0   2013-05-16 05:00:00
1   2013-05-16 18:00:00
2   2013-05-16 21:00:00
3   2013-05-17 09:00:00
4   2013-05-17 16:00:00
Name: Entry_DateTime, dtype: datetime64[ns]
ตัวอย่าง DateTime ใน df: 50   2013-05-13 05:00:00
51   2013-05-13 06:00:00
52   2013-05-13 07:00:00
53   2013-05-13 08:00:00
54   2013-05-13 09:00:00
Name: DateTime, dtype: datetime64[ns]
🔍 กำลังทำการ merge_asof โดยใช้คอลัมน์ 213 features : ['DateTime', 'Entry_DayOfWeek', 'Entry_Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CLp', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'EMA50', 'EMA100', 'EMA200', 'EMA_diff', 'MA_Cross', 'Price_above_EMA50', 'Rolling_Vol_5', 'Rolling_Vol_15', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'RSI14', 'RSI_signal', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_14', 'DMP_14', 'DMN_14', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'Support', 'Resistance', 'PullBack_Up', 'PullBack_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_above_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_Up', 'RSI14_x_PullBack_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20']

📌 ตรวจสอบ Missing Values หลัง Merge:
✅ ไม่พบ Missing Values ใน DataFrame

📌 ข้อมูลหลังรวม features:
จำนวนแถว: 3691
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
            Entry Time        EMA50       EMA100      RSI14
0  2013.05.16 05:00:00  1413.303408  1422.486838  44.466719
1  2013.05.16 18:00:00  1400.848848  1412.793296  53.924160
2  2013.05.16 21:00:00  1399.284563  1411.194828  58.992087
3  2013.05.17 09:00:00  1393.311487  1405.057289  34.939486
4  2013.05.17 16:00:00  1389.366208  1401.258871  35.588420

🔍 กำลังสร้าง target variable...

🏗️ เปิดใช้งาน process trade targets

🏗️ เปิดใช้งาน create multiclass target
📊 Multi-class Target Statistics:
  Class 0 (strong_sell): 1373 samples (37.2%)
  Class 1 (weak_sell): 200 samples (5.4%)
  Class 2 (no_trade): 1176 samples (31.9%)
  Class 3 (weak_buy): 151 samples (4.1%)
  Class 4 (strong_buy): 791 samples (21.4%)
✅ Multi-class Target ถูกต้อง: 5 classes พร้อมใช้งาน

📊 Multi-class Target Distribution:
Target_Multiclass
0    1373
1     200
2    1176
3     151
4     791
Name: count, dtype: int64
Class 0 (strong_sell): 1373 trades, Profit range: -1988.0 to -60.0
Class 1 (weak_sell): 200 trades, Profit range: -60.0 to -20.0
Class 2 (no_trade): 1176 trades, Profit range: -20.0 to 20.0
Class 3 (weak_buy): 151 trades, Profit range: 20.0 to 60.0
Class 4 (strong_buy): 791 trades, Profit range: 60.0 to 3003.0

📊 การกระจายของ Target ต่างๆ:
1. Target หลัก (Binary):
Main_Target
0    3524
1     167
Name: count, dtype: int64

2. Target สำหรับ Buy Trades:
Target_Buy
0    1579
1      91
Name: count, dtype: int64

3. Target สำหรับ Sell Trades:
Target_Sell
0    1945
1      76
Name: count, dtype: int64

📌 ข้อมูลหลังสร้าง target variable:
จำนวนแถว: 3691
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
            Entry Time        EMA50       EMA100      RSI14
0  2013.05.16 05:00:00  1413.303408  1422.486838  44.466719
1  2013.05.16 18:00:00  1400.848848  1412.793296  53.924160
2  2013.05.16 21:00:00  1399.284563  1411.194828  58.992087
3  2013.05.17 09:00:00  1393.311487  1405.057289  34.939486
4  2013.05.17 16:00:00  1389.366208  1401.258871  35.588420

📌 ข้อมูลหลังสร้าง target:
การกระจายของ Target:
Target
0    3524
1     167
Name: count, dtype: int64

🔍 เริ่มกระบวนการเลือก Features...

🏗️ เปิดใช้งาน select features
ℹ️ Potential features for model input (Pre-Trade Only): ['Entry_DayOfWeek', 'Entry_Hour', 'IsWeekend', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'EMA_diff', 'MA_Cross', 'Price_above_EMA50', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Vol_5', 'Rolling_Vol_15', 'RSI_signal', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_15', 'ADX_zone_25', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'PullBack_Up', 'PullBack_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_above_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_Up', 'RSI14_x_PullBack_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth']+['Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20'] = 200 features considered.

🔍 ความสัมพันธ์กับ Target (ทั้งหมด):
Target                            1.000000
Price_above_EMA50_x_RSI_signal    0.270554
RSI_Overbought                    0.240798
ADX_Deep                          0.190588
Price_Range                       0.178529
                                    ...   
Ratio_Buy                         0.000030
IsWeekend                              NaN
RSI_Divergence_i2                      NaN
RSI_Divergence_i4                      NaN
RSI_Divergence_i6                      NaN
Name: Target, Length: 201, dtype: float64

🚫 Features ถูกตัดออกเนื่องจาก Multicollinearity สูง: ['EMA50_x_RollingVol5', 'RSI_x_VolumeSpike', 'ADX_14_Lag_3', 'ADX_14_Lag_2', 'RSI_ROC_i6', 'ADX_14_Lag_1', 'ATR_x_PriceRange']
\ืเริ่มขั้นตอนการตรวจสอบ features ที่ต้องใช้จากการทำ analyze cross asset feature importance

👍 โหลดรายชื่อ Features ที่จำเป็นจากไฟล์: Test_LightGBM\feature_importance\060_must_have_features.pkl (0 Features)

featuresasset_feature_importance : len  0
[]

📌 สรุป Features ที่จะใช้สำหรับโมเดล:
จำนวน Features: 26
1. Price_above_EMA50_x_RSI_signal
2. RSI_Overbought
3. ADX_Deep
4. Price_Range
5. RSI_Oversold
6. Volume_Change_1
7. Rolling_Vol_5
8. RSI_ROC_i2
9. MACD_signal_x_RSI14
10. Volume_Spike
11. Volume_Change_2
12. RSI_ROC_i4
13. ATR_ROC_i2
14. Bar_TL
15. RSI_ROC_i8
16. ADX_14_Lag_5
17. STO_overbought
18. Volume_Change_3
19. ATR_Deep
20. Rolling_Vol_15
21. RSI14_x_PriceMove
22. ADX_zone_25
23. Momentum5_x_Volatility10
24. RSI14_x_Volume
25. RSI14_x_StochK
26. STO_Oversold

🔍 กำลังตรวจสอบข้อมูลก่อนฝึกโมเดล...

📊 การกระจายของ Target:
Target
0    0.954755
1    0.045245
Name: proportion, dtype: float64
อัตราส่วน Class Imbalance: 0.05
⚠️ พบ Class Imbalance รุนแรง (อัตราส่วนน้อยกว่า 20%)
✅ ไม่พบ missing values ใน features

📊 สถิติพื้นฐานของ features:
                                 count  ...           max
Price_above_EMA50_x_RSI_signal  3691.0  ...  1.000000e+00
RSI_Overbought                  3691.0  ...  1.000000e+00
ADX_Deep                        3691.0  ...  1.000000e+00
Price_Range                     3691.0  ...  4.462000e+01
RSI_Oversold                    3691.0  ...  1.000000e+00
Volume_Change_1                 3691.0  ...  4.389049e+00
Rolling_Vol_5                   3691.0  ...  1.968890e-02
RSI_ROC_i2                      3691.0  ...  7.091340e-01
MACD_signal_x_RSI14             3691.0  ...  8.705492e+01
Volume_Spike                    3691.0  ...  5.861974e+00
Volume_Change_2                 3691.0  ...  8.453537e+00
RSI_ROC_i4                      3691.0  ...  7.919501e-01
ATR_ROC_i2                      3691.0  ...  4.950817e-01
Bar_TL                          3691.0  ...  1.000000e+00
RSI_ROC_i8                      3691.0  ...  8.089597e-01
ADX_14_Lag_5                    3691.0  ...  7.834542e+01
STO_overbought                  3691.0  ...  1.000000e+00
Volume_Change_3                 3691.0  ...  9.583851e+00
ATR_Deep                        3691.0  ...  1.000000e+00
Rolling_Vol_15                  3691.0  ...  1.535970e-02
RSI14_x_PriceMove               3691.0  ...  2.901090e+03
ADX_zone_25                     3691.0  ...  1.000000e+00
Momentum5_x_Volatility10        3691.0  ...  1.041825e+03
RSI14_x_Volume                  3691.0  ...  2.935653e+06
RSI14_x_StochK                  3691.0  ...  7.991387e+03
STO_Oversold                    3691.0  ...  1.000000e+00

[26 rows x 8 columns]

🔍 กำลังตรวจสอบความสัมพันธ์ระหว่าง features...
✅ ไม่พบ features ที่มีความสัมพันธ์สูงเกินไป

🔍 กำลังแบ่งข้อมูลเป็น train/val/test...

การกระจายของ Target ในชุดข้อมูล:

📊 ใช้ Target Column: Target_Multiclass
Train: Target_Multiclass
0    0.366757
2    0.316170
4    0.205059
1    0.062782
3    0.049232
Name: proportion, dtype: float64
Val: Target_Multiclass
0    0.371274
2    0.338753
4    0.211382
1    0.047425
3    0.031165
Name: proportion, dtype: float64
Test: Target_Multiclass
0    0.388363
2    0.305819
4    0.244926
1    0.035183
3    0.025710
Name: proportion, dtype: float64

🏗️ เปิดใช้งาน analyze time filters

📊 Time Filter Analysis for GOLD:
📅 Recommended Days: ['Thursday']
⏰ Recommended Hours: [8, 11, 15, 19]
✅ บันทึก time filter ที่: Test_LightGBM/thresholds/GOLD_60_time_filters.pkl

🏗️ เปิดใช้งาน find optimal nbars sl
🔍 Validation set info: 738 rows, columns: ['Date', 'Time', 'DateTime', 'Open', 'High', 'Low', 'Close', 'Volume', 'Entry_DayOfWeek', 'Entry_Hour']...
🔍 Sample data range: 2264 to 3001
🔍 Entry function: None
🔍 Entry function type: <class 'dict'>

🏗️ เปิดใช้งาน backtest
backtest results for nBars_SL = 2 : expectancy = -50.2619, win_rate = 0.4286

🏗️ เปิดใช้งาน backtest
backtest results for nBars_SL = 3 : expectancy = -48.6905, win_rate = 0.4286

🏗️ เปิดใช้งาน backtest
backtest results for nBars_SL = 4 : expectancy = -54.5476, win_rate = 0.4286

🏗️ เปิดใช้งาน backtest
backtest results for nBars_SL = 5 : expectancy = -57.5714, win_rate = 0.4286

🏗️ เปิดใช้งาน backtest
backtest results for nBars_SL = 6 : expectancy = -70.2143, win_rate = 0.4286

🏗️ เปิดใช้งาน backtest
backtest results for nBars_SL = 7 : expectancy = -75.7143, win_rate = 0.4286

🏗️ เปิดใช้งาน backtest
backtest results for nBars_SL = 8 : expectancy = -76.6667, win_rate = 0.4286

🏗️ เปิดใช้งาน backtest
backtest results for nBars_SL = 9 : expectancy = -79.0238, win_rate = 0.4286

🏗️ เปิดใช้งาน backtest
backtest results for nBars_SL = 10 : expectancy = -79.4524, win_rate = 0.4286

nBars grid search (val set):
nBars  WinRate    Expectancy   NumTrades 
2      0.43       -50.26       42        
3      0.43       -48.69       42        
4      0.43       -54.55       42        
5      0.43       -57.57       42        
6      0.43       -70.21       42        
7      0.43       -75.71       42        
8      0.43       -76.67       42        
9      0.43       -79.02       42        
10     0.43       -79.45       42        

🏗️ เปิดใช้งาน save optimal nbars
✅ บันทึก nBars SL ที่: Test_LightGBM/thresholds/GOLD_60_optimal_nBars_SL.pkl
✅ Optimal nBars SL for GOLD 60: 3

🔍 กำลังทำ Feature Scaling...
✅ ทำ Feature Scaling เรียบร้อยแล้ว

✅ แสดงช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล (train/val/test)
Train: 2013-05-16 ถึง 2021-05-26 (2933 วัน, 2214 records)
Val: 2021-05-26 ถึง 2023-06-01 (737 วัน, 738 records)
Test: 2023-06-01 ถึง 2025-04-30 (700 วัน, 739 records)

✅ ข้อมูล df และ trade_df หลังจาก load and process data
จำนวน columns ใน df: 220
จำนวน columns ใน trade_df: 239

[INFO] จำนวน Features หลัง load and process data: 26
[INFO] รายชื่อ Features: ['Price_above_EMA50_x_RSI_signal', 'RSI_Overbought', 'ADX_Deep', 'Price_Range', 'RSI_Oversold', 'Volume_Change_1', 'Rolling_Vol_5', 'RSI_ROC_i2', 'MACD_signal_x_RSI14', 'Volume_Spike', 'Volume_Change_2', 'RSI_ROC_i4', 'ATR_ROC_i2', 'Bar_TL', 'RSI_ROC_i8', 'ADX_14_Lag_5', 'STO_overbought', 'Volume_Change_3', 'ATR_Deep', 'Rolling_Vol_15', 'RSI14_x_PriceMove', 'ADX_zone_25', 'Momentum5_x_Volatility10', 'RSI14_x_Volume', 'RSI14_x_StochK', 'STO_Oversold']
💾 บันทึกกราฟ Autocorrelation ที่: Test_LightGBM/results/M60\060_GOLD_target_autocorrelation.png

🏗️ เปิดใช้งาน analyze sl tp performance
📊 สถิติการทำงานของ SL/TP:
==================================================
ประเภทการออก        จำนวน     อัตราส่วน 
--------------------------------------------------
TP Hit              167       4.52%
SL Hit              1479      40.07%
Technical Exit      2045      55.41%
SL + Tech Exit      3524      95.48%
==================================================
กำไรเฉลี่ยเมื่อ TP Hit: 1139.01
ขาดทุนเฉลี่ยเมื่อ SL Hit: -139.45
กำไร/ขาดทุนเฉลี่ยเมื่อออกด้วยสัญญาณเทคนิค: -19.41
อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:8.17

ผลรวม SL + Technical Exit:
- จำนวนเทรดรวม: 3524
- อัตราส่วน: 95.48%
- กำไร/ขาดทุนเฉลี่ย: -69.79
- อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:16.32

การออกด้วยสัญญาณเทคนิค:
- กำไรเฉลี่ยเมื่อชน TP: 183.95
- ขาดทุนเฉลี่ยเมื่อชน SL: -169.68
- อัตราการชน TP: 42.49%
- อัตราการชน SL: 57.51%

🏗️ เปิดใช้งาน analyze time performance
📊 ประสิทธิภาพตามวันในสัปดาห์:
                Profit                        Target
                 count       mean      sum      mean
Entry_DayOfWeek                                     
Monday             662 -22.172205 -14678.0  0.042296
Tuesday            839 -14.069130 -11804.0  0.042908
Wednesday          775 -14.100645 -10928.0  0.045161
Thursday           653 -15.552833 -10156.0  0.039816
Friday             762 -10.715223  -8165.0  0.055118

📊 ประสิทธิภาพตามชั่วโมง:
           Profit                         Target
            count        mean      sum      mean
Entry_Hour                                      
4             133  -49.751880  -6617.0  0.022556
5             185   12.362162   2287.0  0.059459
6              47  -75.574468  -3552.0  0.000000
7              16 -140.562500  -2249.0  0.062500
8              30  135.966667   4079.0  0.166667
9             131  -23.396947  -3065.0  0.022901
10            391  -26.283887 -10277.0  0.030691
11            396    2.237374    886.0  0.040404
12            256  -18.132812  -4642.0  0.023438
13            179  -23.078212  -4131.0  0.027933
14            193    0.823834    159.0  0.056995
15            302  -56.089404 -16939.0  0.056291
16            429   10.212121   4381.0  0.079254
17            327  -13.449541  -4398.0  0.061162
18            240   -9.700000  -2328.0  0.037500
19            174  -17.034483  -2964.0  0.028736
20            138  -26.449275  -3650.0  0.028986
21            124  -21.862903  -2711.0  0.040323
💾 บันทึกกราฟวิเคราะห์เวลา ที่: Test_LightGBM/results\060_GOLD_time_analysis.png

========================================
🤖 เริ่มการเทรนโมเดล LightGBM
========================================
🔄 ใช้ Single Model Architecture (แบบเดิม)

🏗️ เปิดใช้งาน train and evaluate
✅ ข้อมูลผ่านการตรวจสอบเบื้องต้น
   Train: 2214 samples, 26 features
   Val: 738 samples
   Test: 739 samples
🔍 ตรวจสอบ Temporal Dependence ในชุด Train/Val/Test

⚙️ กำลัง Scaling Features...
✅ Scaling Features เสร็จสิ้น

🔍 การตรวจสอบ Data Quality และ Class Imbalance
============================================================
Train class distribution: Target_Multiclass
0    812
2    700
4    454
1    139
3    109
Name: count, dtype: int64
Train class ratio (0:1): 5.84
Minority class ratio: 0.063
⚠️ CAUTION: Moderate class imbalance detected
   - ควรใช้ class_weight='balanced'

Data Quality Check:
- Train samples: 2214
- Val samples: 738
- Test samples: 739
- Features: 26
✅ No missing values detected
✅ No infinite values detected
⚙️ กำลังทำ SMOTE balancing...
SMOTE: ใช้ k_neighbors=5 (min_class_count=109)
SMOTE: ก่อน balance 0=812, 1=139 | หลัง balance 0=812, 1=812

🚀 กำลังสร้างโมเดล LGBMClassifier ใหม่

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 5
  📊 Classes: [0 1 2 3 4]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 5 classes
  🎯 Adaptive Parameters: lr=0.027, leaves=19 (Symbol: GOLD, TF: 60)
  🎯 Multi-class Class Weights: {0: 0.545320197044335, 1: 3.1856115107913667, 2: 0.6325714285714286, 3: 4.062385321100917, 4: 0.9753303964757709}
Class Ratio (0:1): 1.00:1
📁 สร้างโฟลเดอร์สำหรับ tuning files: Test_LightGBM/models/060_GOLD

🔍 ตรวจสอบสถานะ Hyperparameter Tuning:
   do_hyperparameter_tuning = False
   flag_file = Test_LightGBM/models/060_GOLD\060_GOLD_tuning_flag.json
   param_file = Test_LightGBM/models/060_GOLD\060_GOLD_best_params.json
   flag_file exists = True
   param_file exists = True

✅ โหลด best hyperparameters จากไฟล์ Test_LightGBM/models/060_GOLD\060_GOLD_best_params.json
   - Best AUC: 0.687851160054623
   - Tuning Date: 2025-07-07T19:03:32.806218
   - CV Method: TimeSeriesSplit
✅ สร้างโมเดล LGBMClassifier ใหม่สำเร็จ
⚙️ กำลัง Fit โมเดลหลัก...
Training until validation scores don't improve for 200 rounds
[100]	valid_0's multi_logloss: 0.73649	valid_0's multi_error: 0.310298
[200]	valid_0's multi_logloss: 0.747706	valid_0's multi_error: 0.300813
[300]	valid_0's multi_logloss: 0.789605	valid_0's multi_error: 0.314363
Early stopping, best iteration is:
[140]	valid_0's multi_logloss: 0.735423	valid_0's multi_error: 0.296748
✅ ฝึกโมเดลหลักสำเร็จ

🏗️ เปิดใช้งาน validate model quality

🔍 Model Quality Validation for GOLD 60:
  📊 Accuracy: 0.703 ✅ (min: 0.6)
  📈 AUC: 0.905 ✅ (min: 0.7)
  📋 Samples: 738 ✅ (min: 200)
  🎯 Overall: ✅ PASS

🏗️ เปิดใช้งาน find best threshold on val
Threshold grid search (val set):
⚠️ ไม่มี threshold ไหนที่มี trade >= 100 ใน validation set จะใช้ threshold default 0.35 แทน

🏗️ เปิดใช้งาน save optimal threshold
✅ บันทึก threshold ที่: Test_LightGBM/thresholds/GOLD_60_optimal_threshold.pkl
✅ บันทึกค่า threshold ที่เหมาะสมสำหรับ GOLD 60: 0.3500
-> เสร็จสิ้นการ Fit โมเดล LightGBM หลัก
-> กำลังเรียกตรวจสอบ features ของโมเดลหลัก
-> เสร็จสิ้นตรวจสอบ features

==================================================
  การทดสอบเปรียบเทียบกับ RandomForest  
==================================================
-> กำลังเรียก test random forest

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance:
                       Feature  Importance
             RSI14_x_PriceMove    0.147491
           MACD_signal_x_RSI14    0.102361
                    RSI_ROC_i2    0.102086
                    RSI_ROC_i8    0.051017
                  ADX_14_Lag_5    0.049609
                   Price_Range    0.044731
                    RSI_ROC_i4    0.043572
                RSI14_x_Volume    0.042778
                 Rolling_Vol_5    0.042557
      Momentum5_x_Volatility10    0.041139
               Volume_Change_1    0.041129
                RSI14_x_StochK    0.040741
                    ATR_ROC_i2    0.039641
                  Volume_Spike    0.038903
               Volume_Change_3    0.037668
               Volume_Change_2    0.037033
                Rolling_Vol_15    0.036101
                        Bar_TL    0.030037
                   ADX_zone_25    0.008103
                      ADX_Deep    0.007248
                      ATR_Deep    0.005373
Price_above_EMA50_x_RSI_signal    0.004916
                STO_overbought    0.001803
                  STO_Oversold    0.001782
                RSI_Overbought    0.001158
                  RSI_Oversold    0.001025

📈 RandomForest Performance:
              precision    recall  f1-score   support

           0       0.75      0.89      0.82       287
           1       0.63      0.46      0.53        26
           2       0.52      0.49      0.50       226
           3       1.00      0.11      0.19        19
           4       0.73      0.67      0.70       181

    accuracy                           0.68       739
   macro avg       0.73      0.52      0.55       739
weighted avg       0.68      0.68      0.67       739

💾 บันทึก RandomForest importance ที่: Test_LightGBM/results\060_GOLD_random_forest_feature_importance.csv
-> เสร็จสิ้น test random forest

📊 การกระจายของคลาส:
Train - 0: 812, 1: 139
Test - 0: 287, 1: 26

🔍 เริ่มทำ Cross-Validation...
-> กำลังเรียก time series cv

🏗️ เปิดใช้งาน time series cv
🔁 เริ่มทำ Time Series Cross-Validation (n_splits=5)

📊 Fold 1/5:
  - Train size: 2 ตัวอย่าง (0.1% ของข้อมูลทั้งหมด)
  - Val size:   590 ตัวอย่าง
  - การกระจายคลาสใน Train: {4: 0.5, 0: 0.5}
⚠️ เตือน : ใน Fold 1 พบคลาสใน validation ที่ไม่มีใน training: {1, 2, 3}
  - Training classes: [0, 4]
  - Validation classes: [0, 1, 2, 3, 4]

📊 Fold 2/5:
  - Train size: 592 ตัวอย่าง (20.1% ของข้อมูลทั้งหมด)
  - Val size:   590 ตัวอย่าง
  - การกระจายคลาสใน Train: {0: 0.3344594594594595, 2: 0.3293918918918919, 4: 0.21452702702702703, 1: 0.06756756756756757, 3: 0.05405405405405406}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 5
  📊 Classes: [0 1 2 3 4]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 5 classes
  🎯 Adaptive Parameters: lr=0.023, leaves=18 (Symbol: UNKNOWN, TF: 60)
  🎯 Multi-class Class Weights: {0: 0.597979797979798, 1: 2.96, 2: 0.6071794871794872, 3: 3.7, 4: 0.9322834645669291}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 144 trees)
  📊 ผลลัพธ์ Fold 2:
    - Accuracy:  0.6305
    - AUC:       0.8760
    - F1 Score:  0.6234
    - Precision: 0.6376
    - Recall:    0.6305

📊 Fold 3/5:
  - Train size: 1182 ตัวอย่าง (40.0% ของข้อมูลทั้งหมด)
  - Val size:   590 ตัวอย่าง
  - การกระจายคลาสใน Train: {2: 0.338409475465313, 0: 0.33587140439932317, 4: 0.20135363790186125, 1: 0.06345177664974619, 3: 0.06091370558375635}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 5
  📊 Classes: [0 1 2 3 4]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 5 classes
  🎯 Adaptive Parameters: lr=0.023, leaves=18 (Symbol: UNKNOWN, TF: 60)
  🎯 Multi-class Class Weights: {0: 0.5954659949622166, 1: 3.152, 2: 0.591, 3: 3.283333333333333, 4: 0.9932773109243698}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 96 trees)
  📊 ผลลัพธ์ Fold 3:
    - Accuracy:  0.6864
    - AUC:       0.8974
    - F1 Score:  0.6589
    - Precision: 0.6900
    - Recall:    0.6864

📊 Fold 4/5:
  - Train size: 1772 ตัวอย่าง (60.0% ของข้อมูลทั้งหมด)
  - Val size:   590 ตัวอย่าง
  - การกระจายคลาสใน Train: {0: 0.3606094808126411, 2: 0.32505643340857787, 4: 0.1935665914221219, 1: 0.06659142212189616, 3: 0.05417607223476298}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 5
  📊 Classes: [0 1 2 3 4]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 5 classes
  🎯 Adaptive Parameters: lr=0.023, leaves=18 (Symbol: UNKNOWN, TF: 60)
  🎯 Multi-class Class Weights: {0: 0.5546165884194053, 1: 3.0033898305084747, 2: 0.6152777777777778, 3: 3.691666666666667, 4: 1.0332361516034985}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 236 trees)
  📊 ผลลัพธ์ Fold 4:
    - Accuracy:  0.7085
    - AUC:       0.9071
    - F1 Score:  0.6936
    - Precision: 0.6895
    - Recall:    0.7085

📊 Fold 5/5:
  - Train size: 2362 ตัวอย่าง (80.0% ของข้อมูลทั้งหมด)
  - Val size:   590 ตัวอย่าง
  - การกระจายคลาสใน Train: {0: 0.36833192209991533, 2: 0.31329381879762913, 4: 0.20618120237087215, 1: 0.06350550381033022, 3: 0.04868755292125317}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 5
  📊 Classes: [0 1 2 3 4]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 5 classes
  🎯 Adaptive Parameters: lr=0.023, leaves=18 (Symbol: UNKNOWN, TF: 60)
  🎯 Multi-class Class Weights: {0: 0.5429885057471264, 1: 3.1493333333333333, 2: 0.6383783783783784, 3: 4.107826086956522, 4: 0.9700205338809035}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 84 trees)
  📊 ผลลัพธ์ Fold 5:
    - Accuracy:  0.6695
    - AUC:       0.9022
    - F1 Score:  0.6393
    - Precision: 0.6738
    - Recall:    0.6695

✅ การทำ Time Series Cross-Validation เสร็จสมบูรณ์
📌 ผลลัพธ์เฉลี่ย:
  - Accuracy:  0.6737
  - AUC:       0.8957
  - F1 Score:  0.6538
  - Precision: 0.6727
  - Recall:    0.6737
-> เสร็จสิ้น time series cv

  การประเมินผลโมเดลแบบละเอียด symbol GOLD timeframe 60

🏗️ เปิดใช้งาน enhanced evaluation
🔍 เริ่มการประเมินผลโมเดลแบบละเอียด symbol GOLD timeframe 60...
📁 สร้างโฟลเดอร์สำหรับ timeframe symbol GOLD timeframe 60 ที่: Test_LightGBM/results/M60\060_GOLD
📊 Multi-class Classification: 5 classes

🏗️ เปิดใช้งาน evaluate multiclass model

Classification Report (Multi-class):
              precision    recall  f1-score   support
0              0.773006  0.878049  0.822186  287.0000
1              0.512195  0.807692  0.626866   26.0000
2              0.582781  0.389381  0.466844  226.0000
3              0.650000  0.684211  0.666667   19.0000
4              0.721393  0.801105  0.759162  181.0000
accuracy       0.702300  0.702300  0.702300    0.7023
macro avg      0.647875  0.712087  0.668345  739.0000
weighted avg   0.689852  0.702300  0.687209  739.0000
📊 กำลังสร้าง visualization...
✅ บันทึกกราฟประสิทธิภาพที่: Test_LightGBM/results/M60\060_GOLD\060_GOLD_performance_curves.png
✅ บันทึกรายงานการประเมินที่: Test_LightGBM/results/M60\060_GOLD\060_GOLD_evaluation_report.csv
🎯 การประเมินผลสำหรับ symbol GOLD timeframe 60 เสร็จสมบูรณ์!

📊 ผลการประเมินแบบละเอียด symbol GOLD timeframe 60
AUC-ROC: 0.5000
AUC-PR: 0.5000
              precision    recall  f1-score   support
0              0.773006  0.878049  0.822186  287.0000
1              0.512195  0.807692  0.626866   26.0000
2              0.582781  0.389381  0.466844  226.0000
3              0.650000  0.684211  0.666667   19.0000
4              0.721393  0.801105  0.759162  181.0000
accuracy       0.702300  0.702300  0.702300    0.7023
macro avg      0.647875  0.712087  0.668345  739.0000
weighted avg   0.689852  0.702300  0.687209  739.0000

📌 สรุปผลลัพธ์แบบละเอียด:
- Accuracy: 0.7023
- F1_macro: 0.6683
- F1_weighted: 0.6872
- Precision_macro: 0.6479
- Precision_weighted: 0.6899
- Recall_macro: 0.7121
- Recall_weighted: 0.7023
- Auc_ovr: 0.9038
- Auc_ovo: 0.9117
- Log_loss: 0.7068
- Strong_sell_precision: 0.7730
- Strong_sell_recall: 0.8780
- Strong_sell_f1: 0.8222
- Weak_sell_precision: 0.5122
- Weak_sell_recall: 0.8077
- Weak_sell_f1: 0.6269
- No_trade_precision: 0.5828
- No_trade_recall: 0.3894
- No_trade_f1: 0.4668
- Weak_buy_precision: 0.6500
- Weak_buy_recall: 0.6842
- Weak_buy_f1: 0.6667
- Strong_buy_precision: 0.7214
- Strong_buy_recall: 0.8011
- Strong_buy_f1: 0.7592
- Timeframe: 60.0000

กำลังบันทึกโมเดลที่: Test_LightGBM/models/060_GOLD\LightGBM_060_GOLD_trained.pkl
✅ บันทึกโมเดลเรียบร้อย (ขนาด: 1206.09 KB)

กำลังบันทึก features ที่: Test_LightGBM/models/060_GOLD\LightGBM_060_GOLD_features.pkl
✅ บันทึก features เรียบร้อย (จำนวน features: 26)

กำลังบันทึก Scaler ที่: Test_LightGBM/models/060_GOLD\LightGBM_060_GOLD_scaler.pkl
✅ บันทึก Scaler เรียบร้อย (ขนาด: 2.02 KB)

📝 บันทึกประวัติการเทรนที่: Test_LightGBM/training_history\060_GOLD_training_history.csv
-> กำลังสร้าง Feature Importance
✅ พิมพ์ feature ก่อนส่งเข้า plot_feature_importance
['Price_above_EMA50_x_RSI_signal', 'RSI_Overbought', 'ADX_Deep', 'Price_Range', 'RSI_Oversold', 'Volume_Change_1', 'Rolling_Vol_5', 'RSI_ROC_i2', 'MACD_signal_x_RSI14', 'Volume_Spike', 'Volume_Change_2', 'RSI_ROC_i4', 'ATR_ROC_i2', 'Bar_TL', 'RSI_ROC_i8', 'ADX_14_Lag_5', 'STO_overbought', 'Volume_Change_3', 'ATR_Deep', 'Rolling_Vol_15', 'RSI14_x_PriceMove', 'ADX_zone_25', 'Momentum5_x_Volatility10', 'RSI14_x_Volume', 'RSI14_x_StochK', 'STO_Oversold']

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ LightGBM symbol GOLD timeframe 60

📊 Feature Importance (Normalized Scores - Gain and Split):
                       Feature   Gain  Split
             RSI14_x_PriceMove 0.4536 0.1729
           MACD_signal_x_RSI14 0.1290 0.0803
                    RSI_ROC_i2 0.0970 0.0886
                        Bar_TL 0.0427 0.0226
                  ADX_14_Lag_5 0.0285 0.0586
                   Price_Range 0.0242 0.0423
                    ATR_ROC_i2 0.0231 0.0514
                RSI14_x_StochK 0.0195 0.0440
                RSI14_x_Volume 0.0185 0.0444
                Rolling_Vol_15 0.0177 0.0462
                    RSI_ROC_i4 0.0164 0.0433
                    RSI_ROC_i8 0.0163 0.0413
               Volume_Change_1 0.0162 0.0415
                  Volume_Spike 0.0149 0.0396
      Momentum5_x_Volatility10 0.0131 0.0350
               Volume_Change_2 0.0128 0.0356
               Volume_Change_3 0.0125 0.0337
                 Rolling_Vol_5 0.0113 0.0327
Price_above_EMA50_x_RSI_signal 0.0086 0.0138
                   ADX_zone_25 0.0084 0.0093
                      ADX_Deep 0.0076 0.0105
                      ATR_Deep 0.0052 0.0075
                  STO_Oversold 0.0019 0.0021
                STO_overbought 0.0007 0.0019
                  RSI_Oversold 0.0001 0.0005
                RSI_Overbought 0.0001 0.0003

💾 บันทึก Feature Importance ละเอียดที่: Test_LightGBM/results/M60\060_GOLD_feature_importance.csv
💾 บันทึกกราฟ Feature Importance ที่: Test_LightGBM/results/M60\060_GOLD_feature_importance.png
-> เสร็จสิ้น Feature Importance

🔍 Top 5 Most Important Features (Gain):
RSI14_x_PriceMove: 0.4536 (Gain), 0.1729 (Split)
MACD_signal_x_RSI14: 0.1290 (Gain), 0.0803 (Split)
RSI_ROC_i2: 0.0970 (Gain), 0.0886 (Split)
Bar_TL: 0.0427 (Gain), 0.0226 (Split)
ADX_14_Lag_5: 0.0285 (Gain), 0.0586 (Split)

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: Test_LightGBM/results/M60\060_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: Test_LightGBM/results/M60\060_GOLD_feature_importance_comparison.csv

📊 เปรียบเทียบผลลัพธ์:
| Metric      | CV Avg    | Test Set |
|-------------|-----------|----------|
| Accuracy    | 0.6737    | 0.7023 |
| AUC         | 0.8957    | 0.9038 |
| F1 Score    | 0.6538    | 0.6683 |
-> train and evaluate ทำงานเสร็จสิ้น

🏗️ เปิดใช้งาน validate model quality

🔍 Model Quality Validation for GOLD 60:
  📊 Accuracy: 0.703 ✅ (min: 0.6)
  📈 AUC: 0.905 ✅ (min: 0.7)
  📋 Samples: 738 ✅ (min: 200)
  🎯 Overall: ✅ PASS
✅ คืนค่าผลลัพธ์สำเร็จ: model=LGBMClassifier, scaler=StandardScaler

✅ ข้อมูล df และ trade_df หลังจาก train and evaluate
จำนวน columns ใน df: 220
จำนวน columns ใน trade_df: 239

[INFO] จำนวน Features หลัง train and evaluate: 26
[INFO] รายชื่อ Features หลัง train: {20: 'RSI14_x_PriceMove', 8: 'MACD_signal_x_RSI14', 7: 'RSI_ROC_i2', 13: 'Bar_TL', 15: 'ADX_14_Lag_5', 3: 'Price_Range', 12: 'ATR_ROC_i2', 24: 'RSI14_x_StochK', 23: 'RSI14_x_Volume', 19: 'Rolling_Vol_15', 11: 'RSI_ROC_i4', 14: 'RSI_ROC_i8', 5: 'Volume_Change_1', 9: 'Volume_Spike', 22: 'Momentum5_x_Volatility10', 10: 'Volume_Change_2', 17: 'Volume_Change_3', 6: 'Rolling_Vol_5', 0: 'Price_above_EMA50_x_RSI_signal', 21: 'ADX_zone_25', 2: 'ADX_Deep', 18: 'ATR_Deep', 25: 'STO_Oversold', 16: 'STO_overbought', 4: 'RSI_Oversold', 1: 'RSI_Overbought'}

📊 เปรียบเทียบผลลัพธ์:
| Metric      | CV Avg    | Test Set |
|-------------|-----------|----------|
| Accuracy    | 0.6737    | 0.7023 |
| AUC         | 0.8957    | 0.9038 |
| F1 Score    | 0.6538    | 0.6683 |

✅ ข้อมูล df และ trade_df ก่อนเข้า entry conditions
จำนวน columns ใน df: 220
จำนวน columns ใน trade_df: 239

[INFO] ตรวจสอบ Features ก่อนเข้าลูป entry condition: 26 features
[INFO] รายชื่อ Features: ['Price_above_EMA50_x_RSI_signal', 'RSI_Overbought', 'ADX_Deep', 'Price_Range', 'RSI_Oversold', 'Volume_Change_1', 'Rolling_Vol_5', 'RSI_ROC_i2', 'MACD_signal_x_RSI14', 'Volume_Spike', 'Volume_Change_2', 'RSI_ROC_i4', 'ATR_ROC_i2', 'Bar_TL', 'RSI_ROC_i8', 'ADX_14_Lag_5', 'STO_overbought', 'Volume_Change_3', 'ATR_Deep', 'Rolling_Vol_15', 'RSI14_x_PriceMove', 'ADX_zone_25', 'Momentum5_x_Volatility10', 'RSI14_x_Volume', 'RSI14_x_StochK', 'STO_Oversold']

========================================
✅ Testing entry condition: trend_following_buy
========================================
✅ ข้อมูล df ก่อนเข้า entry conditions : create trade cycles with model
จำนวน columns ใน df: 220
🎯 ใช้ Single Model สำหรับ trend_following_buy

🏗️ เปิดใช้งาน create trade cycles with model
❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม
🔍 ใช้ start_index = 50 (ข้อมูลทั้งหมด 2952 แถว)

🔍 ตรวจสอบ Temporal Dependence สำหรับการเทรด
- ช่วงเวลาที่จะทำ backtest: 2013-05-15 07:00:00 ถึง 2013-10-31 13:00:00
- จำนวนแท่งข้อมูลทั้งหมดสำหรับ backtest: 2902

ความถี่ของช่วงเวลาระหว่างแท่งข้อมูล:
DateTime
0 days 01:00:00    2924
2 days 01:00:00      24
0 days 04:00:00       3
Name: count, dtype: int64

▶️ เริ่ม Backtest จาก index: 50 (เพื่อให้ Indicators คำนวณได้ครบ)

🔍 ตรวจสอบ Look-Ahead Bias ก่อนเริ่ม Backtest

🏗️ เปิดใช้งาน enhanced look ahead check
🔍 ตรวจสอบ Look-Ahead Bias แบบละเอียด
กำลังตรวจสอบ Features จำนวน 2 รายการ...

ตัวอย่างการคำนวณ Features (5 แถวแรกหลังจาก dropna):
ไม่มี Features ที่เลือกไว้สำหรับแสดงตัวอย่าง
(แสดงตัวอย่าง 10 Features แรก: ['Entry_DayOfWeek', 'Entry_Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CLp', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL'])

📌 แถวที่ 0 (เวลา: 2013-05-13 05:00:00)
Entry_DayOfWeek: 0 (ก่อนหน้า: N/A)
Entry_Hour: 5 (ก่อนหน้า: N/A)
IsMorning: 0 (ก่อนหน้า: N/A)
IsAfternoon: 0 (ก่อนหน้า: N/A)
IsEvening: 0 (ก่อนหน้า: N/A)
IsNight: 0 (ก่อนหน้า: N/A)
Bar_CLp: 1430.77000 (ก่อนหน้า: N/A)
Bar_CL: 1.00000 (ก่อนหน้า: N/A)
Bar_CL_OC: 0.00000 (ก่อนหน้า: N/A)
Bar_CL_HL: 0.00000 (ก่อนหน้า: N/A)

📌 แถวที่ 1 (เวลา: 2013-05-13 06:00:00)
Entry_DayOfWeek: 0 (ก่อนหน้า: N/A)
Entry_Hour: 6 (ก่อนหน้า: N/A)
IsMorning: 0 (ก่อนหน้า: N/A)
IsAfternoon: 0 (ก่อนหน้า: N/A)
IsEvening: 0 (ก่อนหน้า: N/A)
IsNight: 0 (ก่อนหน้า: N/A)
Bar_CLp: 1431.43000 (ก่อนหน้า: 1430.77000)
Bar_CL: -1.00000 (ก่อนหน้า: 1.00000)
Bar_CL_OC: 0.00000 (ก่อนหน้า: 0.00000)
Bar_CL_HL: 0.00000 (ก่อนหน้า: 0.00000)

📌 แถวที่ 2 (เวลา: 2013-05-13 07:00:00)
Entry_DayOfWeek: 0 (ก่อนหน้า: N/A)
Entry_Hour: 7 (ก่อนหน้า: N/A)
IsMorning: 0 (ก่อนหน้า: N/A)
IsAfternoon: 0 (ก่อนหน้า: N/A)
IsEvening: 0 (ก่อนหน้า: N/A)
IsNight: 0 (ก่อนหน้า: N/A)
Bar_CLp: 1430.78000 (ก่อนหน้า: 1431.43000)
Bar_CL: 1.00000 (ก่อนหน้า: -1.00000)
Bar_CL_OC: 0.00000 (ก่อนหน้า: 0.00000)
Bar_CL_HL: 0.00000 (ก่อนหน้า: 0.00000)

✅ ไม่พบ Features ที่น่าสงสัยว่ามี Look-Ahead Bias ในจุดที่ตรวจสอบ

✅ การตรวจสอบ Look-Ahead Bias เสร็จสมบูรณ์ ไม่มีปัญหาที่ตรวจพบโดยอัตโนมัติ

❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม

🔍 สรุปการตรวจสอบ Look-Ahead Bias
- จำนวนการทำนายทั้งหมด: 2902
- จำนวนครั้งที่พบ NaN ใน Features: 0
- จำนวนครั้งที่พบ Features อาจมีปัญหา: 0
✅ ไม่พบ ปัญหาการใช้ข้อมูลจากอนาคต (Look-Ahead Bias)

After trend_following_buy: trades generated = 50

✅ ข้อมูล trade_df หลังจาก entry conditions : create trade cycles with model
จำนวน columns ใน trade_df: 20

--- trend_following_buy ---
Exit Condition
SL Hit            29
Technical Exit    15
TP Hit             6
Name: count, dtype: int64
count      50.000000
mean       36.100000
std       452.153498
min      -777.000000
25%      -108.000000
50%         0.000000
75%         0.000000
max      1685.000000
Name: Profit, dtype: float64

🏗️ เปิดใช้งาน analyze trade performance

========================================
✅ Testing entry condition: counter_trend_sell
========================================
✅ ข้อมูล df ก่อนเข้า entry conditions : create trade cycles with model
จำนวน columns ใน df: 220
🎯 ใช้ Single Model สำหรับ counter_trend_sell

🏗️ เปิดใช้งาน create trade cycles with model
❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม
🔍 ใช้ start_index = 50 (ข้อมูลทั้งหมด 2952 แถว)

🔍 ตรวจสอบ Temporal Dependence สำหรับการเทรด
- ช่วงเวลาที่จะทำ backtest: 2013-05-15 07:00:00 ถึง 2013-10-31 13:00:00
- จำนวนแท่งข้อมูลทั้งหมดสำหรับ backtest: 2902

ความถี่ของช่วงเวลาระหว่างแท่งข้อมูล:
DateTime
0 days 01:00:00    2924
2 days 01:00:00      24
0 days 04:00:00       3
Name: count, dtype: int64

▶️ เริ่ม Backtest จาก index: 50 (เพื่อให้ Indicators คำนวณได้ครบ)

🔍 ตรวจสอบ Look-Ahead Bias ก่อนเริ่ม Backtest

🏗️ เปิดใช้งาน enhanced look ahead check
🔍 ตรวจสอบ Look-Ahead Bias แบบละเอียด
กำลังตรวจสอบ Features จำนวน 2 รายการ...

ตัวอย่างการคำนวณ Features (5 แถวแรกหลังจาก dropna):
ไม่มี Features ที่เลือกไว้สำหรับแสดงตัวอย่าง
(แสดงตัวอย่าง 10 Features แรก: ['Entry_DayOfWeek', 'Entry_Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CLp', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL'])

📌 แถวที่ 0 (เวลา: 2013-05-13 05:00:00)
Entry_DayOfWeek: 0 (ก่อนหน้า: N/A)
Entry_Hour: 5 (ก่อนหน้า: N/A)
IsMorning: 0 (ก่อนหน้า: N/A)
IsAfternoon: 0 (ก่อนหน้า: N/A)
IsEvening: 0 (ก่อนหน้า: N/A)
IsNight: 0 (ก่อนหน้า: N/A)
Bar_CLp: 1430.77000 (ก่อนหน้า: N/A)
Bar_CL: 1.00000 (ก่อนหน้า: N/A)
Bar_CL_OC: 0.00000 (ก่อนหน้า: N/A)
Bar_CL_HL: 0.00000 (ก่อนหน้า: N/A)

📌 แถวที่ 1 (เวลา: 2013-05-13 06:00:00)
Entry_DayOfWeek: 0 (ก่อนหน้า: N/A)
Entry_Hour: 6 (ก่อนหน้า: N/A)
IsMorning: 0 (ก่อนหน้า: N/A)
IsAfternoon: 0 (ก่อนหน้า: N/A)
IsEvening: 0 (ก่อนหน้า: N/A)
IsNight: 0 (ก่อนหน้า: N/A)
Bar_CLp: 1431.43000 (ก่อนหน้า: 1430.77000)
Bar_CL: -1.00000 (ก่อนหน้า: 1.00000)
Bar_CL_OC: 0.00000 (ก่อนหน้า: 0.00000)
Bar_CL_HL: 0.00000 (ก่อนหน้า: 0.00000)

📌 แถวที่ 2 (เวลา: 2013-05-13 07:00:00)
Entry_DayOfWeek: 0 (ก่อนหน้า: N/A)
Entry_Hour: 7 (ก่อนหน้า: N/A)
IsMorning: 0 (ก่อนหน้า: N/A)
IsAfternoon: 0 (ก่อนหน้า: N/A)
IsEvening: 0 (ก่อนหน้า: N/A)
IsNight: 0 (ก่อนหน้า: N/A)
Bar_CLp: 1430.78000 (ก่อนหน้า: 1431.43000)
Bar_CL: 1.00000 (ก่อนหน้า: -1.00000)
Bar_CL_OC: 0.00000 (ก่อนหน้า: 0.00000)
Bar_CL_HL: 0.00000 (ก่อนหน้า: 0.00000)

✅ ไม่พบ Features ที่น่าสงสัยว่ามี Look-Ahead Bias ในจุดที่ตรวจสอบ

✅ การตรวจสอบ Look-Ahead Bias เสร็จสมบูรณ์ ไม่มีปัญหาที่ตรวจพบโดยอัตโนมัติ

❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม

🔍 สรุปการตรวจสอบ Look-Ahead Bias
- จำนวนการทำนายทั้งหมด: 2902
- จำนวนครั้งที่พบ NaN ใน Features: 0
- จำนวนครั้งที่พบ Features อาจมีปัญหา: 0
✅ ไม่พบ ปัญหาการใช้ข้อมูลจากอนาคต (Look-Ahead Bias)

After counter_trend_sell: trades generated = 0

✅ ข้อมูล trade_df หลังจาก entry conditions : create trade cycles with model
จำนวน columns ใน trade_df: 20

--- counter_trend_sell ---
No trades generated.

========================================
✅ Testing entry condition: counter_trend_buy
========================================
✅ ข้อมูล df ก่อนเข้า entry conditions : create trade cycles with model
จำนวน columns ใน df: 220
🎯 ใช้ Single Model สำหรับ counter_trend_buy

🏗️ เปิดใช้งาน create trade cycles with model
❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม
🔍 ใช้ start_index = 50 (ข้อมูลทั้งหมด 2952 แถว)

🔍 ตรวจสอบ Temporal Dependence สำหรับการเทรด
- ช่วงเวลาที่จะทำ backtest: 2013-05-15 07:00:00 ถึง 2013-10-31 13:00:00
- จำนวนแท่งข้อมูลทั้งหมดสำหรับ backtest: 2902

ความถี่ของช่วงเวลาระหว่างแท่งข้อมูล:
DateTime
0 days 01:00:00    2924
2 days 01:00:00      24
0 days 04:00:00       3
Name: count, dtype: int64

▶️ เริ่ม Backtest จาก index: 50 (เพื่อให้ Indicators คำนวณได้ครบ)

🔍 ตรวจสอบ Look-Ahead Bias ก่อนเริ่ม Backtest

🏗️ เปิดใช้งาน enhanced look ahead check
🔍 ตรวจสอบ Look-Ahead Bias แบบละเอียด
กำลังตรวจสอบ Features จำนวน 2 รายการ...

ตัวอย่างการคำนวณ Features (5 แถวแรกหลังจาก dropna):
ไม่มี Features ที่เลือกไว้สำหรับแสดงตัวอย่าง
(แสดงตัวอย่าง 10 Features แรก: ['Entry_DayOfWeek', 'Entry_Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CLp', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL'])

📌 แถวที่ 0 (เวลา: 2013-05-13 05:00:00)
Entry_DayOfWeek: 0 (ก่อนหน้า: N/A)
Entry_Hour: 5 (ก่อนหน้า: N/A)
IsMorning: 0 (ก่อนหน้า: N/A)
IsAfternoon: 0 (ก่อนหน้า: N/A)
IsEvening: 0 (ก่อนหน้า: N/A)
IsNight: 0 (ก่อนหน้า: N/A)
Bar_CLp: 1430.77000 (ก่อนหน้า: N/A)
Bar_CL: 1.00000 (ก่อนหน้า: N/A)
Bar_CL_OC: 0.00000 (ก่อนหน้า: N/A)
Bar_CL_HL: 0.00000 (ก่อนหน้า: N/A)

📌 แถวที่ 1 (เวลา: 2013-05-13 06:00:00)
Entry_DayOfWeek: 0 (ก่อนหน้า: N/A)
Entry_Hour: 6 (ก่อนหน้า: N/A)
IsMorning: 0 (ก่อนหน้า: N/A)
IsAfternoon: 0 (ก่อนหน้า: N/A)
IsEvening: 0 (ก่อนหน้า: N/A)
IsNight: 0 (ก่อนหน้า: N/A)
Bar_CLp: 1431.43000 (ก่อนหน้า: 1430.77000)
Bar_CL: -1.00000 (ก่อนหน้า: 1.00000)
Bar_CL_OC: 0.00000 (ก่อนหน้า: 0.00000)
Bar_CL_HL: 0.00000 (ก่อนหน้า: 0.00000)

📌 แถวที่ 2 (เวลา: 2013-05-13 07:00:00)
Entry_DayOfWeek: 0 (ก่อนหน้า: N/A)
Entry_Hour: 7 (ก่อนหน้า: N/A)
IsMorning: 0 (ก่อนหน้า: N/A)
IsAfternoon: 0 (ก่อนหน้า: N/A)
IsEvening: 0 (ก่อนหน้า: N/A)
IsNight: 0 (ก่อนหน้า: N/A)
Bar_CLp: 1430.78000 (ก่อนหน้า: 1431.43000)
Bar_CL: 1.00000 (ก่อนหน้า: -1.00000)
Bar_CL_OC: 0.00000 (ก่อนหน้า: 0.00000)
Bar_CL_HL: 0.00000 (ก่อนหน้า: 0.00000)

✅ ไม่พบ Features ที่น่าสงสัยว่ามี Look-Ahead Bias ในจุดที่ตรวจสอบ

✅ การตรวจสอบ Look-Ahead Bias เสร็จสมบูรณ์ ไม่มีปัญหาที่ตรวจพบโดยอัตโนมัติ

❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม

🔍 สรุปการตรวจสอบ Look-Ahead Bias
- จำนวนการทำนายทั้งหมด: 2902
- จำนวนครั้งที่พบ NaN ใน Features: 0
- จำนวนครั้งที่พบ Features อาจมีปัญหา: 0
✅ ไม่พบ ปัญหาการใช้ข้อมูลจากอนาคต (Look-Ahead Bias)

After counter_trend_buy: trades generated = 3

✅ ข้อมูล trade_df หลังจาก entry conditions : create trade cycles with model
จำนวน columns ใน trade_df: 20

--- counter_trend_buy ---
Exit Condition
SL Hit            2
Technical Exit    1
Name: count, dtype: int64
count      3.000000
mean    -173.333333
std      512.477642
min     -750.000000
25%     -375.000000
50%        0.000000
75%      115.000000
max      230.000000
Name: Profit, dtype: float64

🏗️ เปิดใช้งาน analyze trade performance

========================================
✅ Testing entry condition: trend_following_sell
========================================
✅ ข้อมูล df ก่อนเข้า entry conditions : create trade cycles with model
จำนวน columns ใน df: 220
🎯 ใช้ Single Model สำหรับ trend_following_sell

🏗️ เปิดใช้งาน create trade cycles with model
❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม
🔍 ใช้ start_index = 50 (ข้อมูลทั้งหมด 2952 แถว)

🔍 ตรวจสอบ Temporal Dependence สำหรับการเทรด
- ช่วงเวลาที่จะทำ backtest: 2013-05-15 07:00:00 ถึง 2013-10-31 13:00:00
- จำนวนแท่งข้อมูลทั้งหมดสำหรับ backtest: 2902

ความถี่ของช่วงเวลาระหว่างแท่งข้อมูล:
DateTime
0 days 01:00:00    2924
2 days 01:00:00      24
0 days 04:00:00       3
Name: count, dtype: int64

▶️ เริ่ม Backtest จาก index: 50 (เพื่อให้ Indicators คำนวณได้ครบ)

🔍 ตรวจสอบ Look-Ahead Bias ก่อนเริ่ม Backtest

🏗️ เปิดใช้งาน enhanced look ahead check
🔍 ตรวจสอบ Look-Ahead Bias แบบละเอียด
กำลังตรวจสอบ Features จำนวน 2 รายการ...

ตัวอย่างการคำนวณ Features (5 แถวแรกหลังจาก dropna):
ไม่มี Features ที่เลือกไว้สำหรับแสดงตัวอย่าง
(แสดงตัวอย่าง 10 Features แรก: ['Entry_DayOfWeek', 'Entry_Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CLp', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL'])

📌 แถวที่ 0 (เวลา: 2013-05-13 05:00:00)
Entry_DayOfWeek: 0 (ก่อนหน้า: N/A)
Entry_Hour: 5 (ก่อนหน้า: N/A)
IsMorning: 0 (ก่อนหน้า: N/A)
IsAfternoon: 0 (ก่อนหน้า: N/A)
IsEvening: 0 (ก่อนหน้า: N/A)
IsNight: 0 (ก่อนหน้า: N/A)
Bar_CLp: 1430.77000 (ก่อนหน้า: N/A)
Bar_CL: 1.00000 (ก่อนหน้า: N/A)
Bar_CL_OC: 0.00000 (ก่อนหน้า: N/A)
Bar_CL_HL: 0.00000 (ก่อนหน้า: N/A)

📌 แถวที่ 1 (เวลา: 2013-05-13 06:00:00)
Entry_DayOfWeek: 0 (ก่อนหน้า: N/A)
Entry_Hour: 6 (ก่อนหน้า: N/A)
IsMorning: 0 (ก่อนหน้า: N/A)
IsAfternoon: 0 (ก่อนหน้า: N/A)
IsEvening: 0 (ก่อนหน้า: N/A)
IsNight: 0 (ก่อนหน้า: N/A)
Bar_CLp: 1431.43000 (ก่อนหน้า: 1430.77000)
Bar_CL: -1.00000 (ก่อนหน้า: 1.00000)
Bar_CL_OC: 0.00000 (ก่อนหน้า: 0.00000)
Bar_CL_HL: 0.00000 (ก่อนหน้า: 0.00000)

📌 แถวที่ 2 (เวลา: 2013-05-13 07:00:00)
Entry_DayOfWeek: 0 (ก่อนหน้า: N/A)
Entry_Hour: 7 (ก่อนหน้า: N/A)
IsMorning: 0 (ก่อนหน้า: N/A)
IsAfternoon: 0 (ก่อนหน้า: N/A)
IsEvening: 0 (ก่อนหน้า: N/A)
IsNight: 0 (ก่อนหน้า: N/A)
Bar_CLp: 1430.78000 (ก่อนหน้า: 1431.43000)
Bar_CL: 1.00000 (ก่อนหน้า: -1.00000)
Bar_CL_OC: 0.00000 (ก่อนหน้า: 0.00000)
Bar_CL_HL: 0.00000 (ก่อนหน้า: 0.00000)

✅ ไม่พบ Features ที่น่าสงสัยว่ามี Look-Ahead Bias ในจุดที่ตรวจสอบ

✅ การตรวจสอบ Look-Ahead Bias เสร็จสมบูรณ์ ไม่มีปัญหาที่ตรวจพบโดยอัตโนมัติ

❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม

🔍 สรุปการตรวจสอบ Look-Ahead Bias
- จำนวนการทำนายทั้งหมด: 2902
- จำนวนครั้งที่พบ NaN ใน Features: 0
- จำนวนครั้งที่พบ Features อาจมีปัญหา: 0
✅ ไม่พบ ปัญหาการใช้ข้อมูลจากอนาคต (Look-Ahead Bias)

After trend_following_sell: trades generated = 82

✅ ข้อมูล trade_df หลังจาก entry conditions : create trade cycles with model
จำนวน columns ใน trade_df: 20

--- trend_following_sell ---
Exit Condition
SL Hit            47
Technical Exit    31
TP Hit             4
Name: count, dtype: int64
count      82.000000
mean       -7.731707
std       280.755157
min      -813.000000
25%       -97.250000
50%         0.000000
75%         0.000000
max      1125.000000
Name: Profit, dtype: float64

🏗️ เปิดใช้งาน analyze trade performance

🏗️ เปิดใช้งาน save trading summary to file
💾 บันทึกสรุปการเทรด GOLD 60 ที่: Test_LightGBM/results\060_GOLD_trading_summary.txt

========================================
ช่วงเวลาที่จะทำ backtest: 2013-05-13 05:00:00 ถึง 2013-10-31 13:00:00 (124 วัน) -> min_trades = 12
⭐ Entry Condition ที่ดีที่สุดสำหรับ GOLD 60: trend_following_buy (Expectancy=62.24, WinRate=37.93, NumTrades=50)
✅ บันทึก best_entry: Test_LightGBM/results/M60\060_GOLD_best_entry.pkl

🏗️ เปิดใช้งาน save entry condition summary

✅ บันทึกผลการทดสอบรอบที่ ที่ไฟล์ .txt แล้ว
สิ้นสุดการทดสอบ entry condition
========================================

ผลลัพธ์การทดสอบโดยสรุป:
                             File  Timeframe    Model           Timestamp  Status Accuracy    AUC F1 Score Precision Recall CV Accuracy CV AUC  CV F1
CSV_Files_Fixed/GOLD_H1_FIXED.csv         60 LightGBM 2025-07-09 17:05:20 Success   0.7023 0.9038   0.6683    0.6479 0.7121      0.6737 0.8957 0.6538

💾 บันทึกผลลัพธ์ทั้งหมดที่: Test_LightGBM/results/M60\060_GOLD_final_results.csv
✅ บันทึกผลลัพธ์การฝึกโมเดลเรียบร้อยแล้ว

🏗️ เปิดใช้งาน analyze results
📈 การวิเคราะห์ผลลัพธ์โดยรวม:

🏆 โมเดลที่ดีที่สุด (ตาม F1 Score):
ไฟล์: CSV_Files_Fixed/GOLD_H1_FIXED.csv
Timeframe: M60
F1 Score: 0.6683

📌 สรุปสถิติ:
       Timeframe  Accuracy       AUC        F1  CV_Accuracy    CV_AUC
count        1.0    1.0000  1.000000  1.000000     1.000000  1.000000
mean        60.0    0.7023  0.903777  0.668345     0.673729  0.895691
std          NaN       NaN       NaN       NaN          NaN       NaN
min         60.0    0.7023  0.903777  0.668345     0.673729  0.895691
25%         60.0    0.7023  0.903777  0.668345     0.673729  0.895691
50%         60.0    0.7023  0.903777  0.668345     0.673729  0.895691
75%         60.0    0.7023  0.903777  0.668345     0.673729  0.895691
max         60.0    0.7023  0.903777  0.668345     0.673729  0.895691

🏆 โมเดลที่ดีที่สุด (ตาม F1 Score):
ไฟล์: CSV_Files_Fixed/GOLD_H1_FIXED.csv
Timeframe: M60
F1 Score: 0.6683

💾 บันทึกผลการวิเคราะห์ที่: Test_LightGBM/results\M60_performance_analysis.txt

🏗️ เปิดใช้งาน safe plot results
✅ บันทึกกราฟ Test Accuracy ที่: Test_LightGBM/results/plots\performance_Test_Accuracy_comparison.png
✅ บันทึกกราฟ auc ที่: Test_LightGBM/results/plots\performance_auc_comparison.png
✅ บันทึกกราฟ f1_score ที่: Test_LightGBM/results/plots\performance_f1_score_comparison.png
✅ บันทึกกราฟ precision ที่: Test_LightGBM/results/plots\performance_precision_comparison.png
✅ บันทึกกราฟ recall ที่: Test_LightGBM/results/plots\performance_recall_comparison.png
✅ บันทึกกราฟ cv_accuracy ที่: Test_LightGBM/results/plots\performance_cv_accuracy_comparison.png
✅ บันทึกกราฟ cv_auc ที่: Test_LightGBM/results/plots\performance_cv_auc_comparison.png
✅ บันทึกกราฟ cv_f1 ที่: Test_LightGBM/results/plots\performance_cv_f1_comparison.png

🏗️ เปิดใช้งาน save enhanced report
✅ บันทึกรายงานละเอียดที่: Test_LightGBM/results/M60\detailed_report.json
✅ บันทึกรายงานสรุปที่: Test_LightGBM/results/M60\060_GOLD_final_results.csv

========================================
⏱️ สรุปเวลาการประมวลผลทั้งหมด
========================================
1. เวลาทั้งหมดที่ใช้ในการประมวลผล: 135.8461 วินาที จำนวนไฟล์ 1
2. เวลาเฉลี่ยที่ใช้ในการประมวลผลต่อ 1 ไฟล์ (1 ไฟล์ รอบที่ 1): 135.8461 วินาที
✅ การประมวลผลเสร็จสิ้น
📊 ส่งคืนผลลัพธ์: 0 รายการ
==================================================
⏱️ สรุปเวลาการประมวลผลทั้งหมด
1. เวลาทั้งหมดที่ใช้ในการประมวลผล: 135.8586 วินาที จำนวนไฟล์ 1 เทรนทั้งหมด 1 รอบ
2. เวลาเฉลี่ยที่ใช้ในการประมวลผลต่อ ชุดไฟล์ (1): 135.8586 วินาที
=== การเทรนทั้งหมด 1 รอบเสร็จสิ้น ===
==================================================

🏗️ กำลังสร้างสรุปการเทรดรายวัน...

🏗️ เปิดใช้งาน generate all trading schedule summaries
🏗️ เริ่มสร้างไฟล์สรุปการเทรดรายวันทั้งหมด

📊 สร้างสรุปสำหรับ M30...

🏗️ เปิดใช้งาน print trading schedule summary

🏗️ เปิดใช้งาน generate trading schedule summary
🔍 ดึงสัญลักษณ์ได้ทั้งหมด: ['AUDUSD', 'EURGBP', 'EURUSD', 'GBPUSD', 'GOLD', 'NZDUSD', 'USDCAD', 'USDJPY']
⚠️ ไม่พบข้อมูล time_filters สำหรับ Monday ใช้ข้อมูลจำลอง
⚠️ ไม่พบข้อมูล time_filters สำหรับ Tuesday ใช้ข้อมูลจำลอง
⚠️ ไม่พบข้อมูล time_filters สำหรับ Wednesday ใช้ข้อมูลจำลอง
⚠️ ไม่พบข้อมูล time_filters สำหรับ Thursday ใช้ข้อมูลจำลอง
⚠️ ไม่พบข้อมูล time_filters สำหรับ Friday ใช้ข้อมูลจำลอง
================================================================================
📅 สรุปแนะนำการเทรดรายวัน (Trading Schedule Summary)
================================================================================

✅ วันจันทร์ (Monday)
   📊 จากสถิติ W% 42.00%
   📈 Expectancy: 30.00
   🎯 คู่ที่พิจารณา: AUDUSD EURGBP EURUSD
   ⏰ กำหนดช่วงเวลา: 10:00 - 16:00
   📋 ข้อมูลจาก 2 combinations

✅ วันอังคาร (Tuesday)
   📊 จากสถิติ W% 44.00%
   📈 Expectancy: 35.00
   🎯 คู่ที่พิจารณา: EURGBP EURUSD GBPUSD
   ⏰ กำหนดช่วงเวลา: 08:00 - 17:00
   📋 ข้อมูลจาก 3 combinations

✅ วันพุธ (Wednesday)
   📊 จากสถิติ W% 46.00%
   📈 Expectancy: 40.00
   🎯 คู่ที่พิจารณา: EURUSD GBPUSD GOLD
   ⏰ กำหนดช่วงเวลา: 09:00 - 15:00
   📋 ข้อมูลจาก 4 combinations

✅ วันพฤหัสบดี (Thursday)
   📊 จากสถิติ W% 48.00%
   📈 Expectancy: 45.00
   🎯 คู่ที่พิจารณา: GBPUSD GOLD NZDUSD
   ⏰ กำหนดช่วงเวลา: 15:00 - 20:00
   📋 ข้อมูลจาก 5 combinations

✅ วันศุกร์ (Friday)
   📊 จากสถิติ W% 50.00%
   📈 Expectancy: 50.00
   🎯 คู่ที่พิจารณา: GOLD NZDUSD USDCAD
   ⏰ กำหนดช่วงเวลา: 08:00 - 20:00
   📋 ข้อมูลจาก 6 combinations

================================================================================
💡 หมายเหตุ:
   - W% = Win Rate (อัตราการชนะ)
   - Expectancy = ผลตอบแทนเฉลี่ยต่อการเทรด
   - ช่วงเวลาเป็นเวลาท้องถิ่น (Local Time)
   - ควรตรวจสอบข่าวสารและเหตุการณ์สำคัญก่อนเทรด
================================================================================

💾 บันทึกสรุปการเทรดรายวันที่: Test_LightGBM/results\M30_daily_trading_schedule_summary.txt

📊 สร้างสรุปสำหรับ M60...

🏗️ เปิดใช้งาน print trading schedule summary

🏗️ เปิดใช้งาน generate trading schedule summary
🔍 ดึงสัญลักษณ์ได้ทั้งหมด: ['AUDUSD', 'EURGBP', 'EURUSD', 'GBPUSD', 'GOLD', 'NZDUSD', 'USDCAD', 'USDJPY']
✅ โหลด time filters สำเร็จ: Test_LightGBM/thresholds/AUDUSD_60_time_filters.pkl
✅ โหลด time filters สำเร็จ: Test_LightGBM/thresholds/EURGBP_60_time_filters.pkl
✅ โหลด time filters สำเร็จ: Test_LightGBM/thresholds/EURUSD_60_time_filters.pkl
✅ โหลด time filters สำเร็จ: Test_LightGBM/thresholds/GBPUSD_60_time_filters.pkl
✅ โหลด time filters สำเร็จ: Test_LightGBM/thresholds/GOLD_60_time_filters.pkl
✅ โหลด time filters สำเร็จ: Test_LightGBM/thresholds/NZDUSD_60_time_filters.pkl
✅ โหลด time filters สำเร็จ: Test_LightGBM/thresholds/USDCAD_60_time_filters.pkl
✅ โหลด time filters สำเร็จ: Test_LightGBM/thresholds/USDJPY_60_time_filters.pkl
================================================================================
📅 สรุปแนะนำการเทรดรายวัน (Trading Schedule Summary)
================================================================================

❌ วันจันทร์ (Monday)
   📊 จากสถิติ W% 12.93%
   📈 Expectancy: -26.52
   🎯 ไม่ควรเข้าเทรดวันนี้
   📋 ข้อมูลจาก 8 combinations

❌ วันอังคาร (Tuesday)
   📊 จากสถิติ W% 13.14%
   📈 Expectancy: -19.80
   🎯 ไม่ควรเข้าเทรดวันนี้
   📋 ข้อมูลจาก 8 combinations

❌ วันพุธ (Wednesday)
   📊 จากสถิติ W% 17.23%
   📈 Expectancy: -22.81
   🎯 ไม่ควรเข้าเทรดวันนี้
   📋 ข้อมูลจาก 8 combinations

❌ วันพฤหัสบดี (Thursday)
   📊 จากสถิติ W% 19.53%
   📈 Expectancy: -1.95
   🎯 ไม่ควรเข้าเทรดวันนี้
   📋 ข้อมูลจาก 8 combinations

❌ วันศุกร์ (Friday)
   📊 จากสถิติ W% 18.12%
   📈 Expectancy: -4.17
   🎯 ไม่ควรเข้าเทรดวันนี้
   📋 ข้อมูลจาก 8 combinations

================================================================================
💡 หมายเหตุ:
   - W% = Win Rate (อัตราการชนะ)
   - Expectancy = ผลตอบแทนเฉลี่ยต่อการเทรด
   - ช่วงเวลาเป็นเวลาท้องถิ่น (Local Time)
   - ควรตรวจสอบข่าวสารและเหตุการณ์สำคัญก่อนเทรด
================================================================================

💾 บันทึกสรุปการเทรดรายวันที่: Test_LightGBM/results\M60_daily_trading_schedule_summary.txt

📊 สร้างสรุปรวม M30+M60...

🏗️ เปิดใช้งาน print trading schedule summary

🏗️ เปิดใช้งาน generate trading schedule summary
🔍 ดึงสัญลักษณ์ได้ทั้งหมด: ['AUDUSD', 'EURGBP', 'EURUSD', 'GBPUSD', 'GOLD', 'NZDUSD', 'USDCAD', 'USDJPY']
✅ โหลด time filters สำเร็จ: Test_LightGBM/thresholds/AUDUSD_60_time_filters.pkl
✅ โหลด time filters สำเร็จ: Test_LightGBM/thresholds/EURGBP_60_time_filters.pkl
✅ โหลด time filters สำเร็จ: Test_LightGBM/thresholds/EURUSD_60_time_filters.pkl
✅ โหลด time filters สำเร็จ: Test_LightGBM/thresholds/GBPUSD_60_time_filters.pkl
✅ โหลด time filters สำเร็จ: Test_LightGBM/thresholds/GOLD_60_time_filters.pkl
✅ โหลด time filters สำเร็จ: Test_LightGBM/thresholds/NZDUSD_60_time_filters.pkl
✅ โหลด time filters สำเร็จ: Test_LightGBM/thresholds/USDCAD_60_time_filters.pkl
✅ โหลด time filters สำเร็จ: Test_LightGBM/thresholds/USDJPY_60_time_filters.pkl
================================================================================
📅 สรุปแนะนำการเทรดรายวัน (Trading Schedule Summary)
================================================================================

❌ วันจันทร์ (Monday)
   📊 จากสถิติ W% 12.93%
   📈 Expectancy: -26.52
   🎯 ไม่ควรเข้าเทรดวันนี้
   📋 ข้อมูลจาก 8 combinations

❌ วันอังคาร (Tuesday)
   📊 จากสถิติ W% 13.14%
   📈 Expectancy: -19.80
   🎯 ไม่ควรเข้าเทรดวันนี้
   📋 ข้อมูลจาก 8 combinations

❌ วันพุธ (Wednesday)
   📊 จากสถิติ W% 17.23%
   📈 Expectancy: -22.81
   🎯 ไม่ควรเข้าเทรดวันนี้
   📋 ข้อมูลจาก 8 combinations

❌ วันพฤหัสบดี (Thursday)
   📊 จากสถิติ W% 19.53%
   📈 Expectancy: -1.95
   🎯 ไม่ควรเข้าเทรดวันนี้
   📋 ข้อมูลจาก 8 combinations

❌ วันศุกร์ (Friday)
   📊 จากสถิติ W% 18.12%
   📈 Expectancy: -4.17
   🎯 ไม่ควรเข้าเทรดวันนี้
   📋 ข้อมูลจาก 8 combinations

================================================================================
💡 หมายเหตุ:
   - W% = Win Rate (อัตราการชนะ)
   - Expectancy = ผลตอบแทนเฉลี่ยต่อการเทรด
   - ช่วงเวลาเป็นเวลาท้องถิ่น (Local Time)
   - ควรตรวจสอบข่าวสารและเหตุการณ์สำคัญก่อนเทรด
================================================================================

💾 บันทึกสรุปการเทรดรายวันที่: Test_LightGBM/results\daily_trading_schedule_summary.txt

✅ สร้างไฟล์สรุปการเทรดรายวันทั้งหมดสำเร็จ
📁 ไฟล์ที่สร้าง:
   - Test_LightGBM/results/M30_daily_trading_schedule_summary.txt
   - Test_LightGBM/results/M60_daily_trading_schedule_summary.txt
   - Test_LightGBM/results/daily_trading_schedule_summary.txt

📊 สรุปผลการเทรนทั้งหมด:
   • จำนวนผลลัพธ์ที่เก็บได้: 0
   • กลุ่มที่เทรน: ['M60']
   • จำนวนรอบต่อกลุ่ม: 1

📈 เริ่มการวิเคราะห์เปรียบเทียบประสิทธิภาพ...

🏗️ เปิดใช้งาน analyze performance comparison

================================================================================
📊 การวิเคราะห์เปรียบเทียบประสิทธิภาพ M30 vs H1
================================================================================

🏆 TOP PERFORMERS:
------------------------------------------------------------

📈 Accuracy Champions:
  M30: NZDUSD = 87.52%
  H1:  AUDUSD = 84.86%
  🥇 Winner: M30 (+2.66%)

📈 AUC Champions:
  M30: GOLD = 96.40%
  H1:  NZDUSD = 95.36%
  🥇 Winner: M30 (+1.04%)

📈 F1 Champions:
  M30: GOLD = 83.83%
  H1:  GOLD = 81.41%
  🥇 Winner: M30 (+2.42%)

⚠️ PROBLEM CASES:
------------------------------------------------------------
  ❌ NZDUSD:
    - M30 CV not working
    - H1 CV not working
  ❌ USDCAD:
    - H1 CV not working
  ❌ USDJPY:
    - M30 AUC = 0
    - H1 AUC = 0

📊 SUMMARY:
------------------------------------------------------------
✅ Working symbols: 5/8 (AUDUSD, EURGBP, EURUSD, GBPUSD, GOLD)
❌ Problem symbols: 3/8 (NZDUSD, USDCAD, USDJPY)

📈 Average Performance (Working Symbols Only):
  M30: Acc=74.1%, F1=65.7%
  H1:  Acc=79.8%, F1=76.0%
  🏆 H1 performs better overall (+5.8% Accuracy)

💡 เริ่มสร้างคำแนะนำการเทรด...

🏗️ เปิดใช้งาน create trading recommendations

================================================================================
📅 คำแนะนำการเทรดรายวัน (Updated Trading Schedule)
================================================================================

🚨 CURRENT ANALYSIS SHOWS POOR DAILY PERFORMANCE
📊 All days show negative expectancy - Need strategy revision!
------------------------------------------------------------
❌ Monday       | Win Rate:  18.8% | Expectancy:  -14.8
❌ Tuesday      | Win Rate:  17.6% | Expectancy:  -14.9
❌ Wednesday    | Win Rate:  16.8% | Expectancy:  -27.5
❌ Thursday     | Win Rate:  18.4% | Expectancy:  -12.7
❌ Friday       | Win Rate:  16.9% | Expectancy:  -15.1

💡 RECOMMENDED ACTIONS:
------------------------------------------------------------
1. 🔧 Improve Entry Conditions:
   - Current win rates (16-18%) are too low
   - Target: >30% win rate for profitable trading
   - Focus on high-probability setups only

2. 🎯 Symbol-Specific Trading:
   - GOLD: Best performer (84% accuracy)
   - USDCAD: Good performer (85% accuracy)
   - EURUSD/GBPUSD: Moderate performers (77-78%)
   - AVOID: USDJPY (44% accuracy)

3. ⏰ Time-Based Optimization:
   - Focus on specific hours with better performance
   - Avoid low-volume periods
   - Consider market session overlaps

4. 🛡️ Risk Management:
   - Reduce position sizes until win rate improves
   - Implement stricter entry filters
   - Consider paper trading first
⚠️ ไม่พบผลลัพธ์จากการเทรนโมเดล - ข้ามการวิเคราะห์ครบถ้วน

🏗️ เปิดใช้งาน analyze performance comparison

================================================================================
📊 การวิเคราะห์เปรียบเทียบประสิทธิภาพ M30 vs H1
================================================================================

🏆 TOP PERFORMERS:
------------------------------------------------------------

📈 Accuracy Champions:
  M30: NZDUSD = 87.52%
  H1:  AUDUSD = 84.86%
  🥇 Winner: M30 (+2.66%)

📈 AUC Champions:
  M30: GOLD = 96.40%
  H1:  NZDUSD = 95.36%
  🥇 Winner: M30 (+1.04%)

📈 F1 Champions:
  M30: GOLD = 83.83%
  H1:  GOLD = 81.41%
  🥇 Winner: M30 (+2.42%)

⚠️ PROBLEM CASES:
------------------------------------------------------------
  ❌ NZDUSD:
    - M30 CV not working
    - H1 CV not working
  ❌ USDCAD:
    - H1 CV not working
  ❌ USDJPY:
    - M30 AUC = 0
    - H1 AUC = 0

📊 SUMMARY:
------------------------------------------------------------
✅ Working symbols: 5/8 (AUDUSD, EURGBP, EURUSD, GBPUSD, GOLD)
❌ Problem symbols: 3/8 (NZDUSD, USDCAD, USDJPY)

📈 Average Performance (Working Symbols Only):
  M30: Acc=74.1%, F1=65.7%
  H1:  Acc=79.8%, F1=76.0%
  🏆 H1 performs better overall (+5.8% Accuracy)

🏗️ เปิดใช้งาน create trading recommendations

================================================================================
📅 คำแนะนำการเทรดรายวัน (Updated Trading Schedule)
================================================================================

🚨 CURRENT ANALYSIS SHOWS POOR DAILY PERFORMANCE
📊 All days show negative expectancy - Need strategy revision!
------------------------------------------------------------
❌ Monday       | Win Rate:  18.8% | Expectancy:  -14.8
❌ Tuesday      | Win Rate:  17.6% | Expectancy:  -14.9
❌ Wednesday    | Win Rate:  16.8% | Expectancy:  -27.5
❌ Thursday     | Win Rate:  18.4% | Expectancy:  -12.7
❌ Friday       | Win Rate:  16.9% | Expectancy:  -15.1

💡 RECOMMENDED ACTIONS:
------------------------------------------------------------
1. 🔧 Improve Entry Conditions:
   - Current win rates (16-18%) are too low
   - Target: >30% win rate for profitable trading
   - Focus on high-probability setups only

2. 🎯 Symbol-Specific Trading:
   - GOLD: Best performer (84% accuracy)
   - USDCAD: Good performer (85% accuracy)
   - EURUSD/GBPUSD: Moderate performers (77-78%)
   - AVOID: USDJPY (44% accuracy)

3. ⏰ Time-Based Optimization:
   - Focus on specific hours with better performance
   - Avoid low-volume periods
   - Consider market session overlaps

4. 🛡️ Risk Management:
   - Reduce position sizes until win rate improves
   - Implement stricter entry filters
   - Consider paper trading first

✅ การทำงานทั้งหมดเสร็จสิ้น!
================================================================================

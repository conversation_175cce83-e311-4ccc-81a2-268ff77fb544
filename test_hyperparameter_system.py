#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบระบบ Hyperparameter Tuning สำหรับ Multi-Model Architecture
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_test_data():
    """สร้างข้อมูลทดสอบ"""
    print("🔧 สร้างข้อมูลทดสอบ...")
    
    # สร้างข้อมูล OHLC พื้นฐาน
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='H')
    n_samples = len(dates)
    
    # สร้างข้อมูลราคา
    np.random.seed(42)
    price_base = 1.1000
    price_changes = np.random.normal(0, 0.001, n_samples).cumsum()
    close_prices = price_base + price_changes
    
    # สร้าง OHLC
    high_prices = close_prices + np.random.uniform(0, 0.002, n_samples)
    low_prices = close_prices - np.random.uniform(0, 0.002, n_samples)
    open_prices = np.roll(close_prices, 1)
    open_prices[0] = close_prices[0]
    
    # สร้าง DataFrame
    df = pd.DataFrame({
        'Time': dates,
        'Open': open_prices,
        'High': high_prices,
        'Low': low_prices,
        'Close': close_prices,
        'Volume': np.random.randint(100, 1000, n_samples)
    })
    
    # เพิ่ม indicators พื้นฐาน
    df['SMA_20'] = df['Close'].rolling(20).mean()
    df['EMA200'] = df['Close'].ewm(span=200).mean()
    df['RSI'] = 50 + np.random.normal(0, 15, n_samples)  # Mock RSI
    
    # สร้าง target
    df['Target'] = (df['Close'].shift(-1) > df['Close']).astype(int)
    df['Target_Multiclass'] = np.random.choice([0, 1, 2, 3, 4], n_samples, 
                                               p=[0.2, 0.2, 0.2, 0.2, 0.2])
    
    # ลบ NaN
    df = df.dropna()
    
    return df

def test_single_model():
    """ทดสอบ Single Model Architecture"""
    print("\n🔍 ทดสอบ Single Model Architecture")
    print("="*50)
    
    # สร้างข้อมูลทดสอบ
    df = create_test_data()
    
    # บันทึกเป็น CSV
    os.makedirs('CSV_Files_Fixed', exist_ok=True)
    test_file = 'CSV_Files_Fixed/EURUSD_M30_FIXED.csv'
    df.to_csv(test_file, sep='\t', index=False)
    print(f"✅ บันทึกข้อมูลทดสอบ: {test_file}")
    
    # เปลี่ยนเป็น Single Model
    print("🔧 เปลี่ยนเป็น Single Model Architecture...")
    
    # อ่านไฟล์ python_LightGBM_16_Signal.py
    with open('python_LightGBM_16_Signal.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # เปลี่ยน USE_MULTI_MODEL_ARCHITECTURE เป็น False
    content = content.replace(
        'USE_MULTI_MODEL_ARCHITECTURE = True',
        'USE_MULTI_MODEL_ARCHITECTURE = False'
    )
    
    with open('python_LightGBM_16_Signal.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ เปลี่ยนเป็น Single Model แล้ว")
    
    # รัน hyperparameter tuning
    print("🚀 รัน hyperparameter tuning...")
    os.system('python python_LightGBM_16_Signal.py --force-retune')
    
    # ตรวจสอบผลลัพธ์
    models_dir = 'LightGBM_Model_Single/models'
    if os.path.exists(models_dir):
        print(f"✅ พบโฟลเดอร์ models: {models_dir}")
        
        # ตรวจสอบไฟล์ที่สร้าง
        for root, dirs, files in os.walk(models_dir):
            for file in files:
                if file.endswith('_best_params.json'):
                    print(f"   📄 {file}")
    else:
        print(f"❌ ไม่พบโฟลเดอร์ models: {models_dir}")

def test_multi_model():
    """ทดสอบ Multi-Model Architecture"""
    print("\n🔍 ทดสอบ Multi-Model Architecture")
    print("="*50)
    
    # เปลี่ยนเป็น Multi-Model
    print("🔧 เปลี่ยนเป็น Multi-Model Architecture...")
    
    # อ่านไฟล์ python_LightGBM_16_Signal.py
    with open('python_LightGBM_16_Signal.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # เปลี่ยน USE_MULTI_MODEL_ARCHITECTURE เป็น True
    content = content.replace(
        'USE_MULTI_MODEL_ARCHITECTURE = False',
        'USE_MULTI_MODEL_ARCHITECTURE = True'
    )
    
    with open('python_LightGBM_16_Signal.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ เปลี่ยนเป็ Multi-Model แล้ว")
    
    # รัน hyperparameter tuning
    print("🚀 รัน hyperparameter tuning...")
    os.system('python python_LightGBM_16_Signal.py --force-retune')
    
    # ตรวจสอบผลลัพธ์
    models_dir = 'LightGBM_Model_Multi/models'
    if os.path.exists(models_dir):
        print(f"✅ พบโฟลเดอร์ models: {models_dir}")
        
        # ตรวจสอบไฟล์ที่สร้าง
        for root, dirs, files in os.walk(models_dir):
            for file in files:
                if file.endswith('_best_params.json'):
                    print(f"   📄 {file}")
    else:
        print(f"❌ ไม่พบโฟลเดอร์ models: {models_dir}")

def test_parameter_stability():
    """ทดสอบ check_parameter_stability.py"""
    print("\n🔍 ทดสอบ Parameter Stability Analysis")
    print("="*50)
    
    # รัน check_parameter_stability.py
    print("🚀 รัน parameter stability analysis...")
    os.system('python check_parameter_stability.py')

def main():
    """ฟังก์ชันหลัก"""
    print("🧪 ทดสอบระบบ Hyperparameter Tuning")
    print("="*60)
    
    try:
        # ทดสอบ Single Model
        test_single_model()
        
        # ทดสอบ Multi-Model
        test_multi_model()
        
        # ทดสอบ Parameter Stability
        test_parameter_stability()
        
        print("\n✅ การทดสอบเสร็จสิ้น")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

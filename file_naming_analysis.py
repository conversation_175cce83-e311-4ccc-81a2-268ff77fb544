#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
วิเคราะห์ปัญหาการบันทึกและโหลดไฟล์ในระบบ Multi-Model Architecture

Created: 2025-01-11
Author: AI Assistant
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
import json
from pathlib import Path

# เพิ่ม path สำหรับ import
sys.path.append('.')

def analyze_file_naming_patterns():
    """
    วิเคราะห์รูปแบบการตั้งชื่อไฟล์ในระบบ
    """
    print("🔍 วิเคราะห์รูปแบบการตั้งชื่อไฟล์")
    print("="*60)
    
    # รูปแบบการตั้งชื่อไฟล์ที่พบในโค้ด
    naming_patterns = {
        "Old System (Single Model)": {
            "threshold": "{symbol}_{timeframe}_optimal_threshold.pkl",
            "nBars_SL": "{symbol}_{timeframe}_optimal_nBars_SL.pkl", 
            "time_filters": "{symbol}_{timeframe}_time_filters.pkl"
        },
        "New System (Multi-Model)": {
            "threshold": "{timeframe:03d}_{symbol}_{scenario}_optimal_threshold.pkl",
            "nBars_SL": "{timeframe:03d}_{symbol}_{scenario}_optimal_nBars_SL.pkl",
            "time_filters": "{symbol}_{timeframe}_time_filters.pkl"  # ยังใช้รูปแบบเดิม
        }
    }
    
    print("📋 รูปแบบการตั้งชื่อไฟล์:")
    for system, patterns in naming_patterns.items():
        print(f"\n🔸 {system}:")
        for file_type, pattern in patterns.items():
            print(f"   {file_type}: {pattern}")
    
    # ตัวอย่างชื่อไฟล์
    symbol = "GOLD"
    timeframe = 60
    scenario = "trend_following"
    
    print(f"\n📊 ตัวอย่างชื่อไฟล์สำหรับ {symbol} M{timeframe}:")
    
    print(f"\n🔸 Old System:")
    print(f"   threshold: {symbol}_{timeframe}_optimal_threshold.pkl")
    print(f"   nBars_SL: {symbol}_{timeframe}_optimal_nBars_SL.pkl")
    print(f"   time_filters: {symbol}_{timeframe}_time_filters.pkl")
    
    print(f"\n🔸 New System:")
    print(f"   threshold: {str(timeframe).zfill(3)}_{symbol}_{scenario}_optimal_threshold.pkl")
    print(f"   nBars_SL: {str(timeframe).zfill(3)}_{symbol}_{scenario}_optimal_nBars_SL.pkl")
    print(f"   time_filters: {symbol}_{timeframe}_time_filters.pkl")
    
    return naming_patterns

def check_existing_files():
    """
    ตรวจสอบไฟล์ที่มีอยู่จริง
    """
    print("\n🔍 ตรวจสอบไฟล์ที่มีอยู่จริง")
    print("="*50)
    
    thresholds_dir = Path("LightGBM_Multi/thresholds")
    
    if not thresholds_dir.exists():
        print(f"❌ ไม่พบโฟลเดอร์: {thresholds_dir}")
        return {}
    
    files = list(thresholds_dir.glob("*.pkl"))
    
    print(f"📁 โฟลเดอร์: {thresholds_dir}")
    print(f"📊 จำนวนไฟล์: {len(files)} ไฟล์")
    
    # จำแนกประเภทไฟล์
    file_categories = {
        'threshold_old': [],
        'threshold_new': [],
        'nbars_old': [],
        'nbars_new': [],
        'time_filters': [],
        'other': []
    }
    
    for file in files:
        file_name = file.name
        
        if 'optimal_threshold' in file_name:
            if file_name.startswith(('060_', '030_')):  # รูปแบบใหม่
                file_categories['threshold_new'].append(file_name)
            else:  # รูปแบบเดิม
                file_categories['threshold_old'].append(file_name)
        elif 'optimal_nBars_SL' in file_name:
            if file_name.startswith(('060_', '030_')):  # รูปแบบใหม่
                file_categories['nbars_new'].append(file_name)
            else:  # รูปแบบเดิม
                file_categories['nbars_old'].append(file_name)
        elif 'time_filters' in file_name:
            file_categories['time_filters'].append(file_name)
        else:
            file_categories['other'].append(file_name)
    
    print(f"\n📋 การจำแนกไฟล์:")
    for category, file_list in file_categories.items():
        if file_list:
            print(f"\n🔸 {category} ({len(file_list)} ไฟล์):")
            for file_name in file_list:
                print(f"   - {file_name}")
        else:
            print(f"\n🔸 {category}: ไม่มีไฟล์")
    
    return file_categories

def analyze_function_usage():
    """
    วิเคราะห์การใช้งานฟังก์ชันต่างๆ
    """
    print("\n🔍 วิเคราะห์การใช้งานฟังก์ชัน")
    print("="*50)
    
    functions_analysis = {
        "Old System Functions": {
            "save_optimal_threshold()": {
                "file_pattern": "{symbol}_{timeframe}_optimal_threshold.pkl",
                "usage": "ระบบเดิม - Single Model"
            },
            "load_optimal_threshold()": {
                "file_pattern": "{symbol}_{timeframe}_optimal_threshold.pkl", 
                "usage": "ระบบเดิม - Single Model"
            },
            "save_optimal_nbars()": {
                "file_pattern": "{symbol}_{timeframe}_optimal_nBars_SL.pkl",
                "usage": "ระบบเดิม - Single Model"
            },
            "load_optimal_nbars()": {
                "file_pattern": "{symbol}_{timeframe}_optimal_nBars_SL.pkl",
                "usage": "ระบบเดิม - Single Model"
            },
            "load_time_filters()": {
                "file_pattern": "{symbol}_{timeframe}_time_filters.pkl",
                "usage": "ทั้งระบบเดิมและใหม่"
            }
        },
        "New System Functions": {
            "find_optimal_threshold_multi_model()": {
                "file_pattern": "{timeframe:03d}_{symbol}_{scenario}_optimal_threshold.pkl",
                "usage": "ระบบใหม่ - Multi-Model"
            },
            "find_optimal_nbars_sl_multi_model()": {
                "file_pattern": "{timeframe:03d}_{symbol}_{scenario}_optimal_nBars_SL.pkl",
                "usage": "ระบบใหม่ - Multi-Model"
            },
            "load_scenario_threshold()": {
                "file_pattern": "{timeframe:03d}_{symbol}_{scenario}_optimal_threshold.pkl",
                "usage": "ระบบใหม่ - Multi-Model"
            },
            "load_scenario_nbars()": {
                "file_pattern": "{timeframe:03d}_{symbol}_{scenario}_optimal_nBars_SL.pkl",
                "usage": "ระบบใหม่ - Multi-Model"
            }
        }
    }
    
    for system, functions in functions_analysis.items():
        print(f"\n🔸 {system}:")
        for func_name, details in functions.items():
            print(f"   {func_name}:")
            print(f"     Pattern: {details['file_pattern']}")
            print(f"     Usage: {details['usage']}")
    
    return functions_analysis

def identify_naming_conflicts():
    """
    ระบุความขัดแย้งในการตั้งชื่อไฟล์
    """
    print("\n⚠️ ระบุความขัดแย้งในการตั้งชื่อไฟล์")
    print("="*50)
    
    conflicts = []
    
    # ความขัดแย้งที่พบ
    conflict_1 = {
        "issue": "รูปแบบการตั้งชื่อไฟล์ไม่สอดคล้องกัน",
        "old_system": "{symbol}_{timeframe}_optimal_threshold.pkl",
        "new_system": "{timeframe:03d}_{symbol}_{scenario}_optimal_threshold.pkl",
        "impact": "ฟังก์ชันเดิมไม่สามารถโหลดไฟล์ใหม่ได้"
    }
    
    conflict_2 = {
        "issue": "time_filters ยังใช้รูปแบบเดิม",
        "pattern": "{symbol}_{timeframe}_time_filters.pkl",
        "impact": "ไม่มีปัญหา แต่ไม่สอดคล้องกับระบบใหม่"
    }
    
    conflict_3 = {
        "issue": "การเรียกใช้งานในระบบหลัก",
        "problem": "ระบบหลักยังเรียกใช้ฟังก์ชันเดิม (load_optimal_threshold, load_optimal_nbars)",
        "impact": "ไม่สามารถใช้ค่าที่ optimize ด้วย Multi-Model ได้"
    }
    
    conflicts = [conflict_1, conflict_2, conflict_3]
    
    for i, conflict in enumerate(conflicts, 1):
        print(f"\n🔸 ความขัดแย้งที่ {i}: {conflict['issue']}")
        for key, value in conflict.items():
            if key != 'issue':
                print(f"   {key}: {value}")
    
    return conflicts

def suggest_solutions():
    """
    เสนอแนวทางแก้ไข
    """
    print("\n💡 เสนอแนวทางแก้ไข")
    print("="*50)
    
    solutions = {
        "Solution 1: Unified Naming Convention": {
            "description": "ใช้รูปแบบการตั้งชื่อไฟล์เดียวกันทั้งระบบ",
            "approach": "แก้ไขฟังก์ชันเดิมให้รองรับรูปแบบใหม่",
            "pros": ["สอดคล้องกัน", "ไม่ต้องเปลี่ยนโค้ดมาก"],
            "cons": ["ต้องแก้ไขฟังก์ชันเดิม", "อาจกระทบระบบที่มีอยู่"]
        },
        "Solution 2: Backward Compatibility": {
            "description": "สร้างฟังก์ชันที่รองรับทั้งรูปแบบเดิมและใหม่",
            "approach": "ตรวจสอบไฟล์ทั้งสองรูปแบบ",
            "pros": ["ไม่กระทบระบบเดิม", "รองรับทั้งสองระบบ"],
            "cons": ["โค้ดซับซ้อนขึ้น", "ต้องดูแลสองรูปแบบ"]
        },
        "Solution 3: Migration Strategy": {
            "description": "ย้ายไฟล์เดิมไปรูปแบบใหม่",
            "approach": "สร้างสคริปต์ย้ายไฟล์และอัปเดตระบบ",
            "pros": ["ระบบสะอาด", "ใช้รูปแบบเดียว"],
            "cons": ["ต้องย้ายไฟล์เดิม", "อาจสูญหายข้อมูล"]
        }
    }
    
    for solution_name, details in solutions.items():
        print(f"\n🔸 {solution_name}:")
        print(f"   Description: {details['description']}")
        print(f"   Approach: {details['approach']}")
        print(f"   Pros: {', '.join(details['pros'])}")
        print(f"   Cons: {', '.join(details['cons'])}")
    
    return solutions

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🚀 วิเคราะห์ปัญหาการบันทึกและโหลดไฟล์ในระบบ Multi-Model")
    print("="*80)
    
    # 1. วิเคราะห์รูปแบบการตั้งชื่อไฟล์
    naming_patterns = analyze_file_naming_patterns()
    
    # 2. ตรวจสอบไฟล์ที่มีอยู่
    existing_files = check_existing_files()
    
    # 3. วิเคราะห์การใช้งานฟังก์ชัน
    functions_analysis = analyze_function_usage()
    
    # 4. ระบุความขัดแย้ง
    conflicts = identify_naming_conflicts()
    
    # 5. เสนอแนวทางแก้ไข
    solutions = suggest_solutions()
    
    print("\n" + "="*80)
    print("📊 สรุปการวิเคราะห์")
    print("="*80)
    
    print(f"✅ รูปแบบการตั้งชื่อไฟล์: 2 รูปแบบ (เดิม/ใหม่)")
    print(f"✅ ไฟล์ที่มีอยู่: {sum(len(files) for files in existing_files.values())} ไฟล์")
    print(f"✅ ฟังก์ชันที่วิเคราะห์: {sum(len(funcs) for funcs in functions_analysis.values())} ฟังก์ชัน")
    print(f"⚠️ ความขัดแย้งที่พบ: {len(conflicts)} ประเด็น")
    print(f"💡 แนวทางแก้ไข: {len(solutions)} วิธี")
    
    print(f"\n🎯 แนะนำ: Solution 2 (Backward Compatibility)")
    print(f"   - รองรับทั้งระบบเดิมและใหม่")
    print(f"   - ไม่กระทบการใช้งานปัจจุบัน")
    print(f"   - ค่อยๆ migrate ไปรูปแบบใหม่")

if __name__ == "__main__":
    main()

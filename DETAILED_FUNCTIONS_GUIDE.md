# 🔧 คู่มือฟังก์ชันหลักและการทำงานแบบละเอียด

## 📋 รายการฟังก์ชันหลักทั้งหมด

### **🏗️ Data Processing Functions**

#### **1. load_and_process_data(csv_file)**
```python
def load_and_process_data(csv_file):
    """โหลดและประมวลผลข้อมูลจากไฟล์ CSV"""
    # Input: ไฟล์ CSV ข้อมูลราคา
    # Output: DataFrame พร้อม technical indicators
    # Process: โหลด → validate → add indicators → return
```

#### **2. add_technical_indicators(df)**
```python
def add_technical_indicators(df):
    """เพิ่ม technical indicators ทั้งหมด"""
    # EMA: 20, 50, 100, 200
    # RSI: 14 periods
    # MACD: 12, 26, 9
    # ATR: 14 periods
    # Bollinger Bands: 20, 2
    # Volume indicators
```

#### **3. create_features(df)**
```python
def create_features(df):
    """สร้าง features สำหรับ ML model"""
    # Price features: OHLC ratios, gaps
    # Time features: hour, day_of_week, session
    # Lag features: price/volume/indicator lags
    # Interaction features: RSI×Volume, MACD×ATR
    # Rolling statistics: MA, Std ต่างๆ
```

### **🤖 Model Training Functions**

#### **4. train_and_evaluate(df, trade_df) - Single Model**
```python
def train_and_evaluate(df, trade_df):
    """เทรนโมเดลแบบเดิม (1 โมเดล)"""
    # 1. Prepare features and targets
    # 2. Split data (time-based)
    # 3. Train LightGBM model
    # 4. Evaluate performance
    # 5. Save model and results
```

#### **5. train_all_scenario_models(df, trade_df) - Multi Model**
```python
def train_all_scenario_models(df, trade_df):
    """เทรนโมเดลแบบใหม่ (4 โมเดล)"""
    models = {}
    for scenario in ['trend_following_buy', 'counter_trend_sell', 
                     'counter_trend_buy', 'trend_following_sell']:
        # 1. Filter data by market scenario
        # 2. Prepare scenario-specific data
        # 3. Train specialized model
        # 4. Save model with scenario name
        models[scenario] = trained_model
    return models
```

#### **6. detect_market_scenario(row)**
```python
def detect_market_scenario(row):
    """ตรวจจับสถานการณ์ตลาด"""
    close = row['Close']
    high = row['High']
    low = row['Low']
    ema200 = row['EMA200']
    
    if close > ema200 and low > ema200:
        return 'uptrend'      # ราคาเหนือ EMA200 ชัดเจน
    elif close < ema200 and high < ema200:
        return 'downtrend'    # ราคาใต้ EMA200 ชัดเจน
    else:
        return 'sideways'     # ราคารอบๆ EMA200
```

### **📊 Trading Signal Functions**

#### **7. create_trade_cycles_with_model(df, model, features)**
```python
def create_trade_cycles_with_model():
    """สร้างสัญญาณการซื้อขายด้วย ML model"""
    trades = []
    
    for i in range(start_index, len(df)):
        # 1. ตรวจสอบ entry conditions
        prev_dict = create_prev_dict(df.iloc[i-1])
        
        if entry_func['buy'](prev_dict):
            # 2. ทำนายด้วย ML model (ถ้ามี)
            if model:
                prediction = model.predict(features[i])
                if prediction < threshold:
                    continue
            
            # 3. สร้าง buy trade
            trade = create_buy_trade(df, i)
            trades.append(trade)
            
        elif entry_func['sell'](prev_dict):
            # สร้าง sell trade
            trade = create_sell_trade(df, i)
            trades.append(trade)
    
    return pd.DataFrame(trades)
```

#### **8. create_trade_cycles_with_multi_model(df, models)**
```python
def create_trade_cycles_with_multi_model():
    """สร้างสัญญาณด้วย Multi-Model Architecture"""
    
    for i in range(start_index, len(df)):
        # 1. ตรวจจับสถานการณ์ตลาดปัจจุบัน
        current_scenario = detect_market_scenario(df.iloc[i])
        
        # 2. เลือกโมเดลที่เหมาะสม
        if current_scenario == 'uptrend':
            model = models['trend_following_buy']
            entry_func = entry_conditions['trend_following_buy']
        elif current_scenario == 'downtrend':
            model = models['trend_following_sell']
            entry_func = entry_conditions['trend_following_sell']
        # ... other scenarios
        
        # 3. ทำนายและสร้างสัญญาณ
        prediction = model.predict(features[i])
        if prediction > threshold:
            trade = create_trade(df, i, entry_func)
```

### **🎯 Optimization Functions**

#### **9. hyperparameter_tuning(X, y, symbol, timeframe)**
```python
def hyperparameter_tuning():
    """ปรับแต่ง hyperparameters"""
    
    # Parameter grid
    param_dist = {
        'learning_rate': [0.01, 0.05, 0.1, 0.15, 0.2],
        'num_leaves': [10, 20, 31, 50],
        'max_depth': [3, 5, 7, 10, -1],
        'min_data_in_leaf': [5, 10, 20, 50],
        'feature_fraction': [0.6, 0.7, 0.8, 0.9, 1.0]
    }
    
    # RandomizedSearchCV with TimeSeriesSplit
    tscv = TimeSeriesSplit(n_splits=5)
    search = RandomizedSearchCV(
        lgb.LGBMClassifier(),
        param_dist,
        cv=tscv,
        scoring='roc_auc',
        n_iter=50
    )
    
    search.fit(X, y)
    return search.best_params_
```

#### **10. find_optimal_threshold(model, val_data)**
```python
def find_optimal_threshold():
    """หาค่า threshold ที่เหมาะสม"""
    
    thresholds = np.arange(0.1, 1.0, 0.1)
    best_threshold = 0.5
    best_metric = -float('inf')
    
    for threshold in thresholds:
        # Backtest with this threshold
        trades = backtest_with_threshold(val_data, threshold)
        
        # Calculate performance metric
        if len(trades) > 0:
            expectancy = trades['Profit'].mean()
            win_rate = (trades['Profit'] > 0).mean()
            metric = expectancy * win_rate  # Combined metric
            
            if metric > best_metric:
                best_metric = metric
                best_threshold = threshold
    
    return best_threshold
```

#### **11. find_optimal_nbars_sl(val_df, entry_func)**
```python
def find_optimal_nbars_sl():
    """หาค่า nBars SL ที่เหมาะสม"""
    
    nBars_range = range(2, 11)  # ทดสอบ 2-10 bars
    best_nbars = 5
    best_metric = -float('inf')
    
    for nBars in nBars_range:
        # Backtest with this nBars SL
        results = backtest(val_df, entry_func, nBars)
        
        if results['num_trades'] >= 5:  # ต้องมีเทรดอย่างน้อย 5 รายการ
            metric = results['expectancy']
            
            if metric > best_metric:
                best_metric = metric
                best_nbars = nBars
    
    return best_nbars
```

### **📈 Analysis Functions**

#### **12. analyze_time_performance(trade_df)**
```python
def analyze_time_performance():
    """วิเคราะห์ผลการเทรดตามเวลา"""
    
    # Daily analysis
    daily_stats = trade_df.groupby('DayOfWeek').agg({
        'Profit': ['mean', 'count'],
        'Win': 'mean'
    })
    
    # Hourly analysis  
    hourly_stats = trade_df.groupby('Hour').agg({
        'Profit': ['mean', 'count'],
        'Win': 'mean'
    })
    
    # Create time filters
    good_days = daily_stats[daily_stats[('Win', 'mean')] > 0.4].index
    good_hours = hourly_stats[hourly_stats[('Win', 'mean')] > 0.4].index
    
    return {
        'daily_stats': daily_stats,
        'hourly_stats': hourly_stats,
        'time_filters': {
            'good_days': good_days,
            'good_hours': good_hours
        }
    }
```

#### **13. analyze_feature_importance(model, features)**
```python
def analyze_feature_importance():
    """วิเคราะห์ความสำคัญของ features"""
    
    # Get feature importance from model
    importance = model.feature_importances_
    feature_names = features.columns
    
    # Create importance DataFrame
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': importance
    }).sort_values('importance', ascending=False)
    
    # Plot top 20 features
    plt.figure(figsize=(10, 8))
    sns.barplot(data=importance_df.head(20), 
                x='importance', y='feature')
    plt.title('Top 20 Feature Importance')
    
    return importance_df
```

### **💾 Utility Functions**

#### **14. save_models(models, symbol, timeframe)**
```python
def save_models():
    """บันทึกโมเดลและข้อมูลที่เกี่ยวข้อง"""
    
    model_dir = f"Test_LightGBM/models/{timeframe:03d}_{symbol}"
    os.makedirs(model_dir, exist_ok=True)
    
    if USE_MULTI_MODEL_ARCHITECTURE:
        # Save multiple models
        for scenario, model in models.items():
            model_file = f"{model_dir}/LightGBM_{scenario}.pkl"
            joblib.dump(model, model_file)
    else:
        # Save single model
        model_file = f"{model_dir}/LightGBM_{timeframe:03d}_{symbol}.pkl"
        joblib.dump(models, model_file)
    
    # Save features list
    features_file = f"{model_dir}/LightGBM_{timeframe:03d}_{symbol}_features.pkl"
    joblib.dump(feature_names, features_file)
```

#### **15. generate_trading_summary(results, symbol, timeframe)**
```python
def generate_trading_summary():
    """สร้างรายงานสรุปการเทรด"""
    
    summary = f"""
========================================
📊 สรุปผลการเทรด {symbol} {timeframe}
========================================
จำนวนเทรดทั้งหมด: {len(trade_df)}
Win Rate: {win_rate:.2%}
Expectancy: {expectancy:.2f}
Profit Factor: {profit_factor:.2f}
Max Drawdown: {max_drawdown:.2%}
Sharpe Ratio: {sharpe_ratio:.2f}

📈 สถิติรายวัน:
{daily_stats}

📈 สถิติรายชั่วโมง:
{hourly_stats}

🎯 Entry Condition ที่ดีที่สุด: {best_entry_name}
🎯 Threshold ที่เหมาะสม: {optimal_threshold}
🎯 nBars SL ที่เหมาะสม: {optimal_nbars}
========================================
"""
    
    # Save to file
    output_file = f"Test_LightGBM/results/{timeframe:03d}_{symbol}_trading_summary.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(summary)
```

## 🔄 การทำงานแบบ Step-by-Step

### **Main Execution Flow:**
1. **Parse Arguments** → รับ symbols และ timeframes
2. **Load Data** → โหลดข้อมูลจาก CSV
3. **Process Data** → สร้าง indicators และ features
4. **Generate Signals** → สร้างสัญญาณการซื้อขาย
5. **Train Models** → เทรนโมเดล (Single/Multi)
6. **Optimize** → ปรับแต่ง hyperparameters, threshold, nBars
7. **Evaluate** → ประเมินผลและวิเคราะห์
8. **Save Results** → บันทึกโมเดลและรายงาน

### **Multi-Model Specific Flow:**
1. **Detect Market Scenarios** → แยกข้อมูลตามสถานการณ์ตลาด
2. **Train Scenario Models** → เทรนโมเดลแยกตาม scenario
3. **Dynamic Model Selection** → เลือกโมเดลตามสถานการณ์ปัจจุบัน
4. **Specialized Predictions** → ทำนายด้วยโมเดลที่เหมาะสม

---

**หมายเหตุ:** ฟังก์ชันทั้งหมดออกแบบมาให้ทำงานร่วมกันเป็นระบบเดียว โดยมีการส่งผ่านข้อมูลและผลลัพธ์ระหว่างฟังก์ชันอย่างเป็นระบบ

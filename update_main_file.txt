คำแนะนำการอัปเดตไฟล์หลัก
==================================================


# อัปเดต test_groups ให้ใช้ไฟล์ที่แก้ไขแล้ว
test_groups = {
    "M30": [
        "CSV_Files_Fixed/AUDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/EURGBP_M30_FIXED.csv", 
        "CSV_Files_Fixed/EURUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GBPUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GOLD_M30_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_M30_FIXED.csv"
    ],
    "M60": [
        "CSV_Files_Fixed/AUDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/EURGBP_H1_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_H1_FIXED.csv", 
        "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/GOLD_H1_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_H1_FIXED.csv"
    ]
}

# เปลี่ยนวิธีการอ่าน CSV
# จาก:
# df = pd.read_csv(file, header=None)
# df = df[0].str.split('\t', expand=True)
# df = df.drop(index=0).reset_index(drop=True)

# เป็น:
df = pd.read_csv(file)  # อ่านไฟล์ที่มี header
# ไม่ต้องแยก column หรือลบแถวแรกอีกต่อไป

#!/bin/bash

echo "========================================"
echo "🖥️ การติดตั้งสำหรับ python_LightGBM_15_Tuning.py"
echo "========================================"

echo ""
echo "🔍 ตรวจสอบ Python..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 ไม่พบ! กรุณาติดตั้ง Python 3.9-3.11 ก่อน"
    echo "💡 Ubuntu/Debian: sudo apt update && sudo apt install python3 python3-pip python3-venv"
    echo "💡 CentOS/RHEL: sudo yum install python3 python3-pip"
    echo "💡 macOS: brew install python3"
    exit 1
fi

python3 --version

echo ""
echo "🔍 ตรวจสอบ pip..."
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 ไม่พบ! กรุณาติดตั้ง pip3"
    exit 1
fi

pip3 --version

echo ""
echo "📦 อัปเดต pip..."
python3 -m pip install --upgrade pip

echo ""
echo "🏗️ สร้าง Virtual Environment..."
python3 -m venv trading_env
if [ $? -ne 0 ]; then
    echo "⚠️ ไม่สามารถสร้าง virtual environment ได้"
    echo "💡 ติดตั้ง python3-venv: sudo apt install python3-venv"
fi

echo ""
echo "🔧 เปิดใช้งาน Virtual Environment..."
source trading_env/bin/activate

echo ""
echo "📊 ติดตั้ง Core Data Science Libraries..."
pip install numpy==1.24.3
pip install pandas==2.0.3
pip install scipy==1.11.1

echo ""
echo "🤖 ติดตั้ง Machine Learning Libraries..."
pip install scikit-learn==1.3.0
pip install lightgbm==4.0.0
pip install imbalanced-learn==0.11.0
pip install joblib==1.3.1

echo ""
echo "📈 ติดตั้ง Technical Analysis Libraries..."
echo "⚠️ กำลังติดตั้ง TA-Lib (อาจใช้เวลานาน)..."

# ติดตั้ง dependencies สำหรับ TA-Lib
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "🐧 ตรวจพบ Linux - ติดตั้ง dependencies..."
    sudo apt-get update
    sudo apt-get install -y build-essential wget
    
    # ดาวน์โหลดและติดตั้ง TA-Lib C library
    wget http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz
    tar -xzf ta-lib-0.4.0-src.tar.gz
    cd ta-lib/
    ./configure --prefix=/usr
    make
    sudo make install
    cd ..
    rm -rf ta-lib ta-lib-0.4.0-src.tar.gz
    
elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 ตรวจพบ macOS - ติดตั้ง dependencies..."
    if command -v brew &> /dev/null; then
        brew install ta-lib
    else
        echo "⚠️ Homebrew ไม่พบ กรุณาติดตั้ง Homebrew ก่อน"
        echo "💡 /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
    fi
fi

pip install TA-Lib==0.4.25
if [ $? -ne 0 ]; then
    echo "❌ TA-Lib ติดตั้งไม่สำเร็จ"
    echo "💡 วิธีแก้:"
    echo "   Linux: sudo apt-get install build-essential"
    echo "   macOS: brew install ta-lib"
fi

pip install pandas-ta==0.3.14b0

echo ""
echo "📊 ติดตั้ง Visualization Libraries..."
pip install matplotlib==3.7.2
pip install seaborn==0.12.2
pip install plotly==5.15.0

echo ""
echo "📈 ติดตั้ง Statistical Analysis..."
pip install statsmodels==0.14.0

echo ""
echo "🧪 ทดสอบการติดตั้ง..."
python test_installation.py

echo ""
echo "========================================"
echo "✅ การติดตั้งเสร็จสิ้น!"
echo "========================================"
echo ""
echo "💡 วิธีใช้งาน:"
echo "   1. เปิด Terminal"
echo "   2. รัน: source trading_env/bin/activate"
echo "   3. รัน: python python_LightGBM_15_Tuning.py"
echo ""
echo "📁 ไฟล์สำคัญ:"
echo "   - setup_guide_new_machine.md (คู่มือละเอียด)"
echo "   - requirements.txt (รายการ libraries)"
echo "   - test_installation.py (ทดสอบการติดตั้ง)"
echo ""

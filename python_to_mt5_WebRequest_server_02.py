#+------------------------------------------------------------------+
#|                                                http_server.py    |
#|                    Receives Bar Data from MT5 via HTTP (Flask) |
#+------------------------------------------------------------------+
from flask import Flask, request, jsonify
import json
import time
import datetime
import MetaTrader5 as mt5
import pandas as pd
import threading
import pandas_ta as ta # Make sure pandas_ta is installed
from werkzeug.exceptions import BadRequest # นำเข้า BadRequest เพื่อจับ Error จาก Flask/Werkzeug
import os # Import os for path joining and existence checks
import joblib # Import joblib for loading models and scalers
import numpy as np # Import numpy for numerical operations
import traceback # Import traceback for detailed error logging
import pickle # Import pickle library (สำหรับโหลด features_list ถ้าบันทึกด้วย pickle)
import sys # Import sys
import pytz # Import pytz for timezone handling (recommended for timestamps)

# --- Import necessary components from python_LightGBM.py ---
try:
    # เพิ่ม Path ของโฟลเดอร์ที่เก็บ python_LightGBM.py เข้าไปใน sys.path ชั่วคราว
    # แก้ไข path นี้ให้ชี้ไปยังโฟลเดอร์ที่เก็บไฟล์ python_LightGBM.py ของคุณ
    python_lightgbm_folder = r'C:\Users\<USER>\test_gold' # *** แก้ Path นี้ ***
    if python_lightgbm_folder not in sys.path:
        sys.path.append(python_lightgbm_folder)
        print(f"Added {python_lightgbm_folder} to sys.path")

    # Import specific functions needed
    # เรายังคงใช้ load_model, load_scaler, select_features
    # แม้ว่า logic select_features จะถูกย้ายมาแล้ว แต่ฟังก์ชันนี้อาจยังถูกใช้ในส่วนอื่น
    from python_LightGBM import load_model, load_scaler, select_features, safe_json_serialize

    # Define the model confidence threshold
    model_confidence_threshold = 0.50 # ใช้ค่า Threshold จากโมเดลของคุณ

    # Define the base path for your models
    MODEL_BASE_PATH = r'C:\Users\<USER>\test_gold\models' # *** แก้ Path นี้ ***
    print(f"Model base path set to: {MODEL_BASE_PATH}")

    # กำหนด Timezone ของ MT5 Server (มักจะเป็น UTC)
    MT5_TIMEZONE = pytz.utc # หรือ pytz.timezone('Etc/UTC') หรือ Timezone ของ Server Broker

except ImportError as e:
    print(f"Error: Could not import components from python_LightGBM.py - {e}")
    print("Please ensure python_LightGBM.py is in the specified path or in Python's path.")
    print("Exiting server initialization.")
    exit()
except Exception as e:
    print(f"An unexpected error occurred during import from python_LightGBM.py: {e}")
    traceback.print_exc()
    print("Exiting server initialization.")
    exit()

# --- Configuration ---
HTTP_PORT = 54321
HTTP_HOST = '127.0.0.1'

# MT5 Connection Configuration (ยังคงไว้ถ้าต้องการใช้ MT5 Library ใน Python)
# Note: These are needed only if Python needs to place trades directly via mt5.py library
# If MT5 EA places trades based on the signal, this might not be strictly necessary for the server itself.
# คุณอาจจะเรียก initialize_mt5() ตรงนี้ ถ้าต้องการใช้ MT5 Library สำหรับส่งคำสั่งเทรด
# initialize_mt5()

# --- Add Timeframe Mapping ---
timeframe_map = {
    "PERIOD_M1": mt5.TIMEFRAME_M1,
    "PERIOD_M2": mt5.TIMEFRAME_M2,
    "PERIOD_M3": mt5.TIMEFRAME_M3,
    "PERIOD_M4": mt5.TIMEFRAME_M4,
    "PERIOD_M5": mt5.TIMEFRAME_M5,
    "PERIOD_M6": mt5.TIMEFRAME_M6,
    "PERIOD_M10": mt5.TIMEFRAME_M10,
    "PERIOD_M12": mt5.TIMEFRAME_M12,
    "PERIOD_M15": mt5.TIMEFRAME_M15,
    "PERIOD_M20": mt5.TIMEFRAME_M20,
    "PERIOD_M30": mt5.TIMEFRAME_M30,
    "PERIOD_H1": mt5.TIMEFRAME_H1,
    "PERIOD_H2": mt5.TIMEFRAME_H2,
    "PERIOD_H3": mt5.TIMEFRAME_H3,
    "PERIOD_H4": mt5.TIMEFRAME_H4,
    "PERIOD_H6": mt5.TIMEFRAME_H6,
    "PERIOD_H8": mt5.TIMEFRAME_H8,
    "PERIOD_H12": mt5.TIMEFRAME_H12,
    "PERIOD_D1": mt5.TIMEFRAME_D1,
    "PERIOD_W1": mt5.TIMEFRAME_W1,
    "PERIOD_MN1": mt5.TIMEFRAME_MN1,
}

# --- Map MT5 Enum Timeframe to your Folder Code (e.g., H1 -> 060) ---
# *** ตรวจสอบและแก้ไข mapping นี้ให้ตรงกับชื่อโฟลเดอร์โมเดลของคุณ ***
timeframe_folder_code_map = {
    mt5.TIMEFRAME_M1: "001",
    mt5.TIMEFRAME_M5: "005",
    mt5.TIMEFRAME_M15: "015",
    mt5.TIMEFRAME_M30: "030",
    mt5.TIMEFRAME_H1: "060",
    mt5.TIMEFRAME_H4: "240",
    mt5.TIMEFRAME_D1: "D1",
    mt5.TIMEFRAME_W1: "W1",
    mt5.TIMEFRAME_MN1: "MN1",
    # เพิ่ม Timeframe ที่เหลือตาม folder structure ของคุณ
}

# --- Global Data Storage ---
market_data_store = {}
data_lock = threading.Lock()

# --- Flask App Setup ---
app = Flask(__name__)

# --- Model Loading Cache ---
loaded_models = {}
model_lock = threading.Lock()

def load_model_components(symbol, timeframe_enum):
    """
    Loads the trained model, scaler, and feature list for a given symbol and timeframe.
    Uses a cache to avoid reloading.
    """
    # --- แก้ไข: ลบ Suffix '#' ออกจากชื่อ Symbol หากมี ---
    cleaned_symbol = symbol.replace('#', '')
    # print(f"Original Symbol: {symbol}, Cleaned Symbol: {cleaned_symbol}") # Print น้อยลง

    # ใช้ cleaned_symbol แทน symbol เดิมในการสร้าง key และ path
    key = (cleaned_symbol, timeframe_enum)
    with model_lock:
        if key in loaded_models:
            # print(f"Loading model components from cache for {cleaned_symbol} (enum: {timeframe_enum})") # Print น้อยลง
            return loaded_models[key]

        timeframe_code = timeframe_folder_code_map.get(timeframe_enum)
        if timeframe_code is None:
            print(f"Error: No folder code mapping found for timeframe enum: {timeframe_enum}")
            return None, None, None

        # Construct the path to the model files using the cleaned_symbol
        model_dir = os.path.join(MODEL_BASE_PATH, f"{cleaned_symbol}_{timeframe_code}")
        model_path = os.path.join(model_dir, f"LightGBM_{cleaned_symbol}_{timeframe_code}_trained.pkl")
        scaler_path = os.path.join(model_dir, f"LightGBM_{cleaned_symbol}_{timeframe_code}_scaler.pkl")
        features_list_path = os.path.join(model_dir, f"LightGBM_{cleaned_symbol}_{timeframe_code}_features.pkl")

        print(f"Attempting to load model components from: {model_dir}")
        # ... rest of the function remains the same ...
        # ตรวจสอบ FileNotFoundError และ Exception ต่างๆ

        if not os.path.exists(model_path):
            print(f"Error: Model file not found at {model_path}")
            return None, None, None
        if not os.path.exists(scaler_path):
            print(f"Error: Scaler file not found at {scaler_path}")
            return None, None, None
        if not os.path.exists(features_list_path):
            print(f"Error: Features list file not found at {features_list_path}")
            return None, None, None

        try:
            model = load_model(model_path)
            scaler = load_scaler(scaler_path)
            with open(features_list_path, 'rb') as f:
                try:
                    features_list = pickle.load(f)
                except Exception:
                    f.seek(0)
                    features_list = joblib.load(f)

            print(f"Successfully loaded model components for {cleaned_symbol} (enum: {timeframe_enum})")

            # Store in cache using the cleaned_symbol
            loaded_models[key] = (model, scaler, features_list)

            return model, scaler, features_list

        except FileNotFoundError as e:
            print(f"Error loading model components: File not found - {e}")
            traceback.print_exc()
            return None, None, None
        except Exception as e:
            print(f"Error loading model components for {cleaned_symbol} (enum: {timeframe_enum}): {e}")
            traceback.print_exc()
            return None, None, None

# --- Data Processing Function (จะเรียกใช้ใน Thread แยก) ---
def process_data_and_trade(symbol, timeframe_enum):
    """
    Calculates indicators, runs model, determines trade signal and confidence.
    This runs in a separate thread.
    """
    cleaned_symbol = symbol.replace('#', '')
    print(f"[{datetime.datetime.now()}] Processing data for {cleaned_symbol} (enum: {timeframe_enum})...")

    df = None
    with data_lock:
        key = (cleaned_symbol, timeframe_enum) # ใช้ cleaned_symbol เป็น key
        if key in market_data_store and len(market_data_store[key]) > 0:
            df = market_data_store[key].copy()
        else:
            print(f"[{datetime.datetime.now()}] No sufficient data yet for {cleaned_symbol} (enum: {timeframe_enum}). Waiting for more bars.")
            return

    # --- ตรวจสอบจำนวนข้อมูลที่เพียงพอสำหรับการคำนวณ Indicator ---
    # คุณต้องรู้ว่า Indicator ที่ใช้ในโมเดลต้องการข้อมูลย้อนหลังกี่แท่ง
    # จากโค้ดใน load_and_process_data Indicator ที่ต้องการข้อมูลย้อนหลังมากที่สุดคือ EMA(200), Rolling(20) และ Features ที่มีการ shift(2)
    # Required bars ควรเป็นค่าสูงสุดที่จำเป็นสำหรับการคำนวณ Indicator ทั้งหมด + จำนวนแท่งที่ต้องใช้ในการสร้าง Features (ปกติคือ 1 หรือ 2 สำหรับ shift)
    # เช่น ถ้า EMA200 ต้องการ 200 แท่งก่อนหน้า และ Bar_FVG ใช้ shift(2) ซึ่งต้องใช้แท่ง i, i-1, i-2
    # ถ้าเราคำนวณ Indicator ที่แท่ง i และใช้ค่า Features ที่แท่ง i-1 เพื่อทำนายแท่ง i+1
    # คุณต้องมีข้อมูลถึงแท่ง i ครบถ้วน และ Indicator ที่ i คำนวณจาก i, i-1,... ย้อนหลังไป
    # จำนวนแท่งขั้นต่ำ = Max(Window Sizes) + Max(Shift)
    # Max Window Size จากโค้ด: EMA200 (200), Rolling(20), Rolling(50), ATR(14), RSI(14), BB(20) => Max = 200
    # Max Shift จากโค้ด: Bar_FVG ใช้ shift(2) => Max Shift = 2
    # จำนวนแท่งขั้นต่ำที่ควรมีใน DataFrame ก่อน dropna() = 200 + 2 = 202
    # แต่หลังจาก dropna() จะเหลือข้อมูลน้อยลง
    # เพื่อให้แน่ใจว่ามีข้อมูลพอสำหรับแถวสุดท้าย (แถวที่ใช้ทำนาย) หลัง dropna
    # อาจจะต้องเก็บข้อมูลมากกว่า Required Bars เล็กน้อย
    required_bars = 202 # ปรับตามการวิเคราะห์ Indicator และ Shift ของคุณ
    if len(df) < required_bars:
        print(f"[{datetime.datetime.now()}] Not enough data ({len(df)} bars) for {cleaned_symbol} (enum: {timeframe_enum}). Minimum required: {required_bars}. Skipping processing.")
        # Optional: ถ้าข้อมูลไม่พอ คุณอาจจะลองขอข้อมูลย้อนหลังเพิ่มเติมจาก MT5 ตรงนี้ได้
        return

    signal = "HOLD" # Default signal
    probability_tp_hit = 0.0 # Default confidence

    try:
        # --- 1. คำนวณ Indicators และสร้าง Features ทั้งหมดที่ใช้ในโมเดล ---
        # คัดลอก Logic จาก load_and_process_data ส่วน "3. สร้าง technical indicators" มาที่นี่
        df_features = df.copy() # ทำงานกับสำเนา

        # --- เพิ่มฟีเจอร์วันและเวลา ---
        # ใน Server, Bar Time มาเป็น datetime อยู่แล้ว ไม่ต้อง Parse Date/Time string
        # ใช้ df.index (ซึ่งเป็น datetime index) ได้เลย
        # df['DateTime'] = df.index # index คือ datetime อยู่แล้ว
        df_features['Entry_DayOfWeek'] = df_features.index.dayofweek  # 0=Monday, 6=Sunday
        df_features['Entry_Hour'] = df_features.index.hour

        # --- สร้างฟีเจอร์สำหรับช่วงเวลาที่สำคัญ ---
        df_features['IsMorning'] = ((df_features['Entry_Hour'] >= 8) & (df_features['Entry_Hour'] < 12)).astype(int)
        df_features['IsAfternoon'] = ((df_features['Entry_Hour'] >= 12) & (df_features['Entry_Hour'] < 16)).astype(int)
        df_features['IsEvening'] = ((df_features['Entry_Hour'] >= 16) & (df_features['Entry_Hour'] < 20)).astype(int)
        df_features['IsNight'] = ((df_features['Entry_Hour'] >= 20) | (df_features['Entry_Hour'] < 4)).astype(int) # ตรวจสอบ Logic ช่วงเวลาอีกครั้งให้ถูกต้อง

        # --- Price action Features ---
        df_features["Bar_CL"] = 0.0
        df_features.loc[df_features['Close'] > df_features['Open'], "Bar_CL"] = 1.0
        df_features.loc[df_features['Close'] < df_features['Open'], "Bar_CL"] = -1.0

        df_features["Bar_CL_OC"] = 0.0
        df_features.loc[df_features['Close'] > np.maximum(df_features['Open'].shift(1), df_features['Close'].shift(1)), "Bar_CL_OC"] = 1.0
        df_features.loc[df_features['Close'] < np.minimum(df_features['Open'].shift(1), df_features['Close'].shift(1)), "Bar_CL_OC"] = -1.0

        df_features["Bar_CL_HL"] = 0.0
        df_features.loc[(df_features['Close'] > df_features['High'].shift(1)) & (df_features['Close'] > df_features['Open']), "Bar_CL_HL"] = 1.0
        df_features.loc[(df_features['Close'] < df_features['Low'].shift(1)) & (df_features['Close'] < df_features['Open']), "Bar_CL_HL"] = -1.0

        df_features["Bar_SW"] = 0.0
        df_features.loc[(df_features['Close'] > df_features['Open']) & (df_features['High'] > df_features['High'].shift(1)) & (df_features['Low'] > df_features['Low'].shift(1)), "Bar_SW"] = 1.0
        df_features.loc[(df_features['Close'] < df_features['Open']) & (df_features['High'] < df_features['High'].shift(1)) & (df_features['Low'] < df_features['Low'].shift(1)), "Bar_SW"] = -1.0

        df_features["Bar_TL"] = 0.0
        df_features.loc[(df_features['Close'] > df_features['Open']) & (df_features['High'] < df_features['High'].shift(1)) & (df_features['Low'] < df_features['Low'].shift(1)), "Bar_TL"] = 1.0
        df_features.loc[(df_features['Close'] < df_features['Open']) & (df_features['High'] > df_features['High'].shift(1)) & (df_features['Low'] > df_features['Low'].shift(1)), "Bar_TL"] = 1.0

        df_features["Bar_DTB"] = 0.0
        df_features.loc[(df_features['Close'] > df_features['Open']) & (df_features['Low'] == df_features['Low'].shift(1)), "Bar_DTB"] = 1.0
        df_features.loc[(df_features['Close'] < df_features['Open']) & (df_features['High'] == df_features['High'].shift(1)), "Bar_DTB"] = -1.0

        df_features["Bar_OSB"] = 0.0
        df_features.loc[(df_features['Close'] > df_features['Open']) & (df_features['High'] > df_features['High'].shift(1)) & (df_features['Low'] < df_features['Low'].shift(1)), "Bar_OSB"] = 1.0
        df_features.loc[(df_features['Close'] < df_features['Open']) & (df_features['High'] > df_features['High'].shift(1)) & (df_features['Low'] < df_features['Low'].shift(1)), "Bar_OSB"] = -1.0

        df_features["Bar_FVG"] = 0.0
        # ตรวจสอบ Bar_FVG: ใช้ High/Low.shift(2) และ Close/Open ปัจจุบัน
        df_features.loc[(df_features["Low"] > df_features["High"].shift(2)) & (df_features["Close"] > df_features["Open"]), "Bar_FVG"] = 1.0
        df_features.loc[(df_features["High"] < df_features["Low"].shift(2)) & (df_features["Close"] < df_features["Open"]), "Bar_FVG"] = -1.0

        df_features["Bar_longwick"] = 0.0
        epsilon = 1e-9
        lower_wick = (np.minimum(df_features['Open'], df_features['Close']) - df_features['Low']).replace(0, epsilon)
        upper_wick = (df_features['High'] - np.maximum(df_features['Open'], df_features['Close'])).replace(0, epsilon)
        pinbar_up = (df_features['High'] - np.minimum(df_features['Open'], df_features['Close'])) / lower_wick
        pinbar_down = (np.maximum(df_features['Open'], df_features['Close']) - df_features['Low']) / upper_wick
        df_features.loc[pinbar_up > pinbar_down, "Bar_longwick"] = pinbar_up
        df_features.loc[pinbar_down > pinbar_up, "Bar_longwick"] = -1 * pinbar_down

        df_features['Price_Range'] = df_features["High"] - df_features["Low"]
        df_features['Price_Move'] = df_features["Close"] - df_features["Open"]

        df_features['Price_Strangth'] = 0.0
        body_size_oc = np.abs(df_features["Close"]-df_features["Open"])
        body_size_ocp = np.abs(df_features["Close"].shift(1)-df_features["Open"].shift(1))
        body_size_hl = np.abs(df_features["High"]-df_features["Low"])
        body_size_hlp = np.abs(df_features["High"].shift(1)-df_features["Low"].shift(1))
        df_features.loc[(df_features["Close"] < df_features["Open"]) & (body_size_oc < body_size_ocp) & (body_size_hl < body_size_hlp), "Price_Strangth"] = 1
        df_features.loc[(df_features["Close"] > df_features["Open"]) & (body_size_oc < body_size_ocp) & (body_size_hl < body_size_hlp), "Price_Strangth"] = -1

        # --- Volume_MA20 ---
        df_features['Volume_MA20'] = df_features['Volume'].rolling(20, min_periods=1).mean().shift(1)
        # จัดการ NaN สำหรับ Volume_MA20 (เหมือนใน load_and_process_data)
        # การ fillna(df['Volume'].mean()) อาจจะไม่เหมาะสมถ้า DataFrame สั้นมาก
        # อาจจะปล่อยให้ dropna() จัดการไปเลย หรือพิจารณา fill method อื่น
        # สำหรับตอนนี้ ให้เหมือนต้นฉบับ
        df_features['Volume_MA20'].fillna(df_features['Volume'].mean(), inplace=True)
        df_features['Volume_Spike'] = df_features['Volume'] / (df_features['Volume_MA20'] + 1e-10)

        # --- EMA Calculation ---
        # ใช้ ewm().mean().shift(1) เหมือนต้นฉบับ
        df_features['EMA50'] = df_features['Close'].ewm(span=50, min_periods=1).mean().shift(1)
        df_features['EMA100'] = df_features['Close'].ewm(span=100, min_periods=1).mean().shift(1)
        df_features['EMA200'] = df_features['Close'].ewm(span=200, min_periods=1).mean().shift(1)

        # --- EMA Related Features ---
        df_features['EMA_diff'] = (df_features['EMA50'] - df_features['EMA200'])
        df_features['MA_Cross'] = (df_features['EMA50'] > df_features['EMA200']).astype(int)
        df_features["Price_above_EMA50"] = (df_features["Close"] > df_features["EMA50"]).astype(int)

        # --- ความผันผวนระยะสั้น ---
        # pct_change() ก็สร้าง NaN ที่แถวแรก
        df_features['Rolling_Vol_5'] = df_features['Close'].pct_change().rolling(5).std().shift(1)
        df_features['Rolling_Vol_15'] = df_features['Close'].pct_change().rolling(15).std().shift(1)

        # --- ระยะทางจาก EMA ---
        # อาจจะสร้าง Inf/NaN ถ้า EMA เป็น 0 หรือเข้าใกล้ 0 ควรจัดการ
        df_features['Dist_EMA50'] = (df_features['Close'] - df_features['EMA50']) / (df_features['EMA50'] + 1e-10) # เพิ่ม epsilon กันหารด้วย 0
        df_features['Dist_EMA100'] = (df_features['Close'] - df_features['EMA100']) / (df_features['EMA100'] + 1e-10)
        df_features['Dist_EMA200'] = (df_features['Close'] - df_features['EMA200']) / (df_features['EMA200'] + 1e-10)

        # --- MACD Calculation ---
        # ใช้ ta.macd().shift(1) เหมือนต้นฉบับ
        macd = ta.macd(df_features["Close"]).shift(1)
        df_features = pd.concat([df_features, macd], axis=1)
        macd_line_col = 'MACD_12_26_9'
        # ตรวจสอบว่าคอลัมน์ MACD_12_26_9 มีอยู่ใน df_features หลังจาก concat และ shift
        if macd_line_col not in df_features.columns:
            print(f"Warning: MACD line column '{macd_line_col}' not found after calculation.")
            # อาจจะต้องหยุดประมวลผลหรือจัดการในจุดนี้
            return # หรือไปขั้นตอน dropna ซึ่งอาจจะลบแถวนี้ออก

        macd_signal_col = 'MACDs_12_26_9'
        # ตรวจสอบ Signal line
        if macd_signal_col not in df_features.columns:
            print(f"Warning: MACD signal column '{macd_signal_col}' not found after calculation.")
            # อาจจะต้องหยุดประมวลผลหรือจัดการในจุดนี้
            return

        # --- Features ที่ใช้ MACD ---
        df_features["MACD_line_feature"] = 0.0 # เปลี่ยนชื่อคอลัมน์ Feature เพื่อไม่ให้ชนกับคอลัมน์ Indicator
        df_features.loc[df_features[macd_line_col] > 0.0, "MACD_line_feature"] = 1
        df_features.loc[df_features[macd_line_col] < 0.0, "MACD_line_feature"] = -1

        df_features["MACD_deep"] = 0.0
        df_features.loc[df_features[macd_line_col] > df_features[macd_line_col].shift(1), "MACD_deep"] = 1
        df_features.loc[df_features[macd_line_col] < df_features[macd_line_col].shift(1), "MACD_deep"] = -1

        df_features["MACD_signal_feature"] = 0.0 # เปลี่ยนชื่อคอลัมน์ Feature
        df_features.loc[df_features[macd_line_col] > df_features[macd_signal_col], "MACD_signal_feature"] = 1
        df_features.loc[df_features[macd_line_col] < df_features[macd_signal_col], "MACD_signal_feature"] = -1

        # --- RSI Calculation ---
        # ใช้ Logic เดิมที่คำนวณแล้ว shift(1)
        window = 14
        delta = df_features["Close"].diff(1)
        gain = pd.Series(np.where(delta > 0, delta, 0), index=df_features.index)
        loss = pd.Series(np.where(delta < 0, -delta, 0), index=df_features.index)

        avg_gain = gain.rolling(window=window, min_periods=window).mean()
        avg_loss = loss.rolling(window=window, min_periods=window).mean()
        # ป้องกันหารด้วยศูนย์
        rs = avg_gain / (avg_loss + 1e-10)

        df_features["RSI14"] = (100 - (100 / (1 + rs))).shift(1) # คำนวณ RSI ที่แท่งปัจจุบัน แล้ว shift กลับ

        # --- Features ที่ใช้ RSI ---
        df_features["RSI_signal"] = np.select(
            [df_features["RSI14"] < 30, df_features["RSI14"] > 70],
            [-1, 1],
            default=0
        )
        df_features['RSI_Overbought'] = (df_features['RSI14'] > 70).astype(int)
        df_features['RSI_Oversold'] = (df_features['RSI14'] < 30).astype(int)

        # --- RSI Divergence ---
        df_features['RSI_Shift'] = df_features['RSI14'].shift(2)
        df_features['RSI_Divergence'] = np.where(
            (df_features['Close'] > df_features['Close'].shift(2)) & (df_features['RSI14'] < df_features['RSI_Shift']),
            1, np.where(
                (df_features['Close'] < df_features['Close'].shift(2)) & (df_features['RSI14'] > df_features['RSI_Shift']),
                -1, 0
            )
        )

        # --- Stochastic Oscillator Calculation ---
        # ใช้ ta.stoch().shift(1) เหมือนต้นฉบับ
        stoch = ta.stoch(high=df_features["High"], low=df_features["Low"], close=df_features["Close"]).shift(1)
        df_features = pd.concat([df_features, stoch], axis=1)
        stoch_k_col = 'STOCHk_14_3_3'
        # ตรวจสอบคอลัมน์ Stoch K
        if stoch_k_col not in df_features.columns:
            print(f"Warning: Stoch K column '{stoch_k_col}' not found after calculation.")
            return

        stoch_d_col = 'STOCHd_14_3_3'
        # ตรวจสอบคอลัมน์ Stoch D
        if stoch_d_col not in df_features.columns:
            print(f"Warning: Stoch D column '{stoch_d_col}' not found after calculation.")
            return

        # --- Features ที่ใช้ Stochastic ---
        df_features["STO_cross"] = 0.0
        df_features.loc[df_features[stoch_k_col] > df_features[stoch_d_col], "STO_cross"] = 1
        df_features.loc[df_features[stoch_k_col] < df_features[stoch_d_col], "STO_cross"] = -1

        df_features["STO_zone"] = 0.0
        df_features.loc[df_features[stoch_k_col] > 50, "STO_zone"] = 1
        df_features.loc[df_features[stoch_k_col] < 50, "STO_zone"] = -1

        df_features["STO_overbought_feature"] = 0.0 # เปลี่ยนชื่อคอลัมน์ Feature
        df_features['STO_overbought_feature'] = (df_features[stoch_k_col] > 80).astype(int)

        df_features["STO_Oversold_feature"] = 0.0 # เปลี่ยนชื่อคอลัมน์ Feature
        df_features['STO_Oversold_feature'] = (df_features[stoch_k_col] < 20).astype(int)

        # --- Bollinger Bands ---
        # ใช้ Rolling Mean/Std แล้ว shift(1) ที่ผลลัพธ์
        window_bb = 20
        # การคำนวณ BB ต้องระบุ window สำหรับ mean และ std
        rolling_mean_bb = df_features["Close"].rolling(window=window_bb, min_periods=1).mean() # ใช้ min_periods=1 เหมือน EMA ถ้าต้องการค่าในช่วงแรก
        rolling_std_bb = df_features["Close"].rolling(window=window_bb, min_periods=1).std()

        # แก้ไข: shift ผลลัพธ์สุดท้ายของการคำนวณ Upper/Lower BB
        df_features["Upper_BB"] = (rolling_mean_bb + (rolling_std_bb * 2)).shift(1)
        df_features["Lower_BB"] = (rolling_mean_bb - (rolling_std_bb * 2)).shift(1)

        # BB_width ใช้ Upper_BB และ Lower_BB ที่ shift แล้ว
        df_features["BB_width"] = df_features["Upper_BB"] - df_features["Lower_BB"]

        # --- ADX Calculation ---
        # ใช้ ta.adx().shift(1) เหมือนต้นฉบับ
        adx = ta.adx(high=df_features["High"], low=df_features["Low"], close=df_features["Close"]).shift(1)
        df_features = pd.concat([df_features, adx], axis=1)

        adx_col = 'ADX_14'
        # ตรวจสอบคอลัมน์ ADX
        if adx_col not in df_features.columns:
            print(f"Warning: ADX column '{adx_col}' not found after calculation.")
            return

        dmp_col = 'DMP_14'
        # ตรวจสอบคอลัมน์ DMP
        if dmp_col not in df_features.columns:
            print(f"Warning: DMP column '{dmp_col}' not found after calculation.")
            return

        dmn_col = 'DMN_14'
        # ตรวจสอบคอลัมน์ DMN
        if dmn_col not in df_features.columns:
            print(f"Warning: DMN column '{dmn_col}' not found after calculation.")
            return

        # --- Features ที่ใช้ ADX ---
        df_features["ADX_zone"] = df_features[adx_col] # ใช้คอลัมน์ ADX_14 ที่ shift แล้ว

        df_features["ADX_cross"] = 0.0
        df_features.loc[df_features[dmp_col] > df_features[dmn_col], "ADX_cross"] = 1
        df_features.loc[df_features[dmn_col] > df_features[dmp_col], "ADX_cross"] = -1

        # --- ATR Calculation ---
        # คำนวณ Rolling Mean ของ True Range แล้ว shift(1) ที่ผลลัพธ์
        window_atr = 14 # ใช้ window RSI (14) สำหรับ ATR ตามโค้ดเดิม
        tr1 = df_features['High'] - df_features['Low']
        tr2 = (df_features['High'] - df_features['Close'].shift()).abs()
        tr3 = (df_features['Low'] - df_features['Close'].shift()).abs()
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

        # แก้ไข: shift ผลลัพธ์สุดท้ายของการคำนวณ ATR Mean
        # ใช้ min_periods=1 เหมือน Indicators อื่นๆ ถ้าต้องการ
        atr = true_range.rolling(window_atr, min_periods=1).mean().shift(1)
        df_features['ATR'] = atr

        # --- SR Calculation ---
        # คำนวณ Rolling Min/Max แล้ว shift(1) ที่ผลลัพธ์
        lookback = 50
        df_features['Support'] = df_features['Low'].rolling(lookback, min_periods=1).min().shift(1) # ใช้ min_periods=1
        df_features['Resistance'] = df_features['High'].rolling(lookback, min_periods=1).max().shift(1) # ใช้ min_periods=1

        # --- กำหนด Lag periods ที่ต้องการ ---
        # ต้องรู้ Timeframe ที่เป็น Enum เพื่อใช้ในการตัดสินใจ lags
        # timeframe_enum ถูกส่งเข้ามาในฟังก์ชันแล้ว
        # แปลง enum กลับเป็น int เพื่อเปรียบเทียบ
        timeframe_int = timeframe_enum # หรือหากต้องการ int value ของ enum เช่น int(timeframe_enum)

        if timeframe_int >= 240: # mt5.TIMEFRAME_H4 มีค่าเป็น 16388 ซึ่ง > 240 -> ใช้ค่า int ของ enum
            lags = [1, 2, 3, 5, 10]
        else:
            lags = [1, 2, 3, 5, 10, 15, 20, 30, 50]

        # --- สร้าง Lag Features สำหรับคอลัมน์สำคัญ ---
        price_cols = ['Close', 'Open', 'High', 'Low', 'Volume']
        for col in price_cols:
            if col in df_features.columns: # ตรวจสอบว่าคอลัมน์มีอยู่จริง
                for lag in lags:
                    df_features[f'{col}_Lag_{lag}'] = df_features[col].shift(lag)
            else:
                print(f"Warning: Price column '{col}' not found in df_features for Lag Features.")

        # --- สร้าง Lag Features สำหรับ Indicators ---
        # ใช้ชื่อคอลัมน์ Feature ที่คุณสร้าง ไม่ใช่ชื่อคอลัมน์ Indicator เดิม
        indicator_cols_for_lag = ['RSI14', 'MACD_line_feature', 'ATR', 'BB_width', 'EMA50', 'EMA200'] # ใช้ชื่อคอลัมน์หลังคำนวณ/shift ที่ต้องการสร้าง Lag

        for indicator in indicator_cols_for_lag:
            if indicator in df_features.columns: # ตรวจสอบว่าคอลัมน์ Indicator มีอยู่จริง
                for lag in [1, 2, 3, 5]:
                    df_features[f'{indicator}_Lag_{lag}'] = df_features[indicator].shift(lag)
            else:
                print(f"Warning: Indicator column '{indicator}' not found in df_features for Lag Features.")

        # --- สร้าง Features แบบเปลี่ยนแปลงจาก Lag (Returns, Changes) ---
        for lag in [1, 2, 3, 5]:
            # pct_change() ใช้ค่าปัจจุบันเทียบกับค่า lag ก่อนหน้า
            df_features[f'Close_Return_{lag}'] = df_features['Close'].pct_change(lag)
            # Volume_Change ใช้ Volume ปัจจุบันเทียบกับ Volume lag ก่อนหน้า
            df_features[f'Volume_Change_{lag}'] = df_features['Volume'].diff(lag) / (df_features['Volume'].shift(lag) + 1e-10)

        # --- สร้าง Rolling Features จาก Lag (หมายถึง Rolling Average/Std ของ Price/Volume) ---
        for window in [3, 5, 10, 20]:
            # คำนวณ Rolling Mean/Std แล้ว shift(1) เหมือนต้นฉบับ
            df_features[f'Close_MA_{window}'] = df_features['Close'].rolling(window, min_periods=1).mean().shift(1)
            df_features[f'Volume_MA_{window}'] = df_features['Volume'].rolling(window, min_periods=1).mean().shift(1)
            df_features[f'Close_Std_{window}'] = df_features['Close'].rolling(window, min_periods=1).std().shift(1)

        # --- ลบแถวที่มีค่า NaN ที่เกิดจากการสร้าง Indicators และ Features ---
        initial_rows = len(df_features)
        # ตรวจสอบว่า df_features ยังมีข้อมูลอยู่ก่อน dropna
        if not df_features.empty:
            df_features = df_features.dropna()
            print(f"✅ สร้าง Indicators และ Features เรียบร้อย (ลบแถวที่ขาดหายไป {initial_rows - len(df_features)} จาก {initial_rows} แถว)")
        else:
            print(f"Warning: df_features is empty before dropna.")
            return # ไม่มีข้อมูลให้ประมวลผลต่อ

        # ตรวจสอบว่ามีข้อมูลเหลืออยู่หลัง dropna
        if df_features.empty:
            print(f"Warning: No data left in df_features after dropna.")
            return # ไม่มีข้อมูลให้ประมวลผลต่อ

        # --- 2. โหลดโมเดล, Scaler, และ Feature List ---
        # ใช้ cleaned_symbol ในการโหลดโมเดล
        model, scaler, features_list = load_model_components(cleaned_symbol, timeframe_enum)

        if model is None or scaler is None or features_list is None:
            print(f"[{datetime.datetime.now()}] Failed to load model components for {cleaned_symbol} (enum: {timeframe_enum}). Cannot make prediction.")
            return

        # --- 3. เตรียม Feature Input สำหรับโมเดล ---
        try:
            # ดึงข้อมูลแถวสุดท้าย (ล่าสุด) ที่มีค่า Indicator ครบ
            # ใช้ features_list เพื่อเลือกเฉพาะคอลัมน์ที่โมเดลคาดหวัง
            # ตรวจสอบว่า df_features มีแถวข้อมูลเพียงพอ (อย่างน้อย 1 แถว)
            if df_features.empty:
                print(f"Warning: df_features is empty when attempting to select features.")
                return

            # ตรวจสอบว่า features_list เป็น list หรือ iterable และไม่ว่าง
            if not isinstance(features_list, (list, tuple)) or not features_list:
                print(f"Error: Loaded features_list is invalid or empty: {features_list}")
                return

            # ตรวจสอบว่าคอลัมน์ใน features_list มีอยู่ใน df_features
            missing_cols = [col for col in features_list if col not in df_features.columns]
            if missing_cols:
                print(f"Error: Missing required feature columns in df_features: {missing_cols}")
                print(f"Available columns: {df_features.columns.tolist()}")
                return

            latest_bar_features_df = df_features.tail(1)[features_list]

        except KeyError as e:
            print(f"[{datetime.datetime.now()}] Error selecting features for {cleaned_symbol} (enum: {timeframe_enum}): Missing feature - {e}")
            print(f"Available columns after indicator calculation: {df_features.columns.tolist()}")
            print(f"Expected features according to features_list: {features_list}")
            traceback.print_exc()
            return
        except Exception as e:
            print(f"[{datetime.datetime.now()}] Error preparing features for {cleaned_symbol} (enum: {timeframe_enum}): {e}")
            traceback.print_exc()
            return

        # แปลงเป็น numpy array เพื่อ Scale
        features_array = latest_bar_features_df.values

        # Scale Features โดยใช้ Scaler ที่โหลดมา
        scaled_features = scaler.transform(features_array)

        # --- 4. ทำนายผลด้วยโมเดล ---
        prediction_proba = model.predict_proba(scaled_features)[:, 1] # Probability of class 1 (TP Hit)
        probability_tp_hit = prediction_proba[0] # ดึงค่าแรกออกมา

        # --- 5. ตัดสินใจ Signal ---
        if probability_tp_hit >= model_confidence_threshold:
            # *** คุณต้องระบุ Logic การตัดสินใจ BUY/SELL ที่นี่ ให้ตรงกับโมเดลของคุณ ***
            # ตัวอย่าง Logic (สมมติว่าโมเดลนี้ทำนายโอกาส BUY TP Hit)
            # หากโมเดลของคุณทำนายทั้ง Buy และ Sell โดยมี Target คนละค่า อาจต้องใช้ predict() หรือ logic อื่นๆ
            # สำหรับโมเดลที่ทำนาย TP Hit อย่างเดียว คุณต้องมี Logic อื่นเสริมเพื่อระบุทิศทาง
            # เช่น ใช้ร่วมกับ Indicator อื่น หรือโมเดลอื่น
            # หากโมเดล TP Hit ของคุณใช้ทำนายโอกาส Buy TP Hit และมีโมเดล Sell TP Hit แยกต่างหาก
            # คุณต้องรันโมเดลทั้งสองและตัดสินใจจากผลลัพธ์ทั้งคู่

            # ตัวอย่าง: สมมติว่าถ้า probability_tp_hit สูงกว่าเกณฑ์ และ MACD_signal_feature เป็น 1 (MACD Bullish) ให้ Buy
            # และถ้า probability_tp_hit สูงกว่าเกณฑ์ และ MACD_signal_feature เป็น -1 (MACD Bearish) ให้ Sell
            # คุณอาจต้องใช้ Features อื่นๆ ในการตัดสินใจทิศทาง
            latest_features_dict = latest_bar_features_df.iloc[0].to_dict() # ดึงค่า features ล่าสุดมาในรูป dict

            # ตรวจสอบว่าคอลัมน์ MACD_signal_feature มีอยู่ใน latest_features_dict ก่อนใช้งาน
            if 'MACD_signal_feature' in latest_features_dict:
                if latest_features_dict['MACD_signal_feature'] > 0: # สมมติ MACD_signal_feature > 0 คือ Bullish
                    signal = "BUY"
                elif latest_features_dict['MACD_signal_feature'] < 0: # สมมติ MACD_signal_feature < 0 คือ Bearish
                    signal = "SELL"
                else:
                    signal = "HOLD" # ถ้า MACD_signal_feature เป็น 0 หรือค่าอื่น
            else:
                # หากไม่มี MACD_signal_feature ให้ใช้ Logic อื่น หรือ Default เป็น HOLD
                print("Warning: 'MACD_signal_feature' not found in latest features. Cannot use it for Buy/Sell decision.")
                # ตัวอย่าง: ถ้าไม่มี MACD_signal_feature แต่ probability สูง ให้ Default เป็น Buy (ปรับตาม Logic คุณ)
                signal = "BUY" # หรือ "HOLD" แล้วแต่กลยุทธ์

            print(f"[{datetime.datetime.now()}] Model confidence ({probability_tp_hit:.4f}) >= {model_confidence_threshold:.2f}. Generated Signal: {signal}")
            # หากโมเดลของคุณมี Logic ซับซ้อนกว่านี้ ให้นำมาใส่ที่นี่

        else:
            signal = "HOLD"
            # print(f"[{datetime.datetime.now()}] Model confidence ({probability_tp_hit:.4f}) < {model_confidence_threshold:.2f}. Signal: {signal}") # อาจจะ Print เฉพาะเมื่อมี Signal ที่น่าสนใจ

        # --- เพิ่ม: Print Signal และ Confidence Probability ที่คำนวณได้ ---
        print(f"[{datetime.datetime.now()}] Prediction Result for {cleaned_symbol} (enum: {timeframe_enum}):")
        print(f"  Signal: {signal}")
        print(f"  Confidence (Probability TP Hit): {probability_tp_hit:.4f}")
        print("------------------------------------------------------------")
        # --- จบส่วน Print ---

        # --- ส่วนส่ง Signal หรือข้อมูลกลับไปที่ MT5 (ต้องเพิ่ม Logic จริงๆ ที่นี่) ---
        # ในตัวอย่างนี้ เราแค่ Print ออกมา
        # หากต้องการส่งกลับใน Response ของ /data
        # คุณจะต้องหาทางส่งค่า signal และ probability_tp_hit ออกไปจาก thread นี้
        # หรือเก็บค่านี้ไว้ใน Global variable แล้วให้ /data ดึงไปใช้
        # *** หรือ ถ้า MT5 EA ของคุณรอ Response ที่มี Signal ใน WebRequest เดียวกัน ***
        # คุณจะต้องเก็บ signal และ probability_tp_hit ไว้ใน Global variable ที่ receive_data สามารถเข้าถึงได้
        # โดยอาจจะใช้ lock แยกสำหรับ Global signal variable
        # แล้ว receive_data จะต้องรอให้ thread ประมวลผลเสร็จ หรือออกแบบให้ receive_data กลับมา poll
        # (แนวทางการเก็บใน Global variable แล้ว receive_data ส่งกลับไปเป็น Response จะทำให้ Server thread ถูก block ถ้าคำนวณนาน)

        # Example: Store latest signal and confidence in a global variable
        global latest_signals_data
        with data_lock: # หรือ lock แยกสำหรับ signals
            latest_signals_data[(cleaned_symbol, timeframe_enum)] = {
                "signal": signal,
                "confidence": probability_tp_hit,
                "timestamp": datetime.datetime.now(MT5_TIMEZONE) # ใช้ Timezone ของ MT5
            }
        print(f"[{datetime.datetime.now()}] Stored latest signal for {cleaned_symbol} (enum: {timeframe_enum}): {signal} ({probability_tp_hit:.4f})")

    except Exception as e:
        print(f"[{datetime.datetime.now()}] Error during indicator calculation, model prediction, or decision for {cleaned_symbol} (enum: {timeframe_enum}): {e}")
        traceback.print_exc()
        signal = "ERROR"
        probability_tp_hit = 0.0

    # Optional: Print 10 แท่งล่าสุดใน DataFrame หลังจากประมวลผล
    # if df_features is not None and not df_features.empty:
    #      print(f"[{datetime.datetime.now()}] Current df_features tail for {cleaned_symbol} (enum: {timeframe_enum}):\n{df_features.tail(10)}")

    print(f"[{datetime.datetime.now()}] Finished processing for {cleaned_symbol} (enum: {timeframe_enum}).")

# --- HTTP Flask Route to receive data ---
@app.route('/data', methods=['POST'])
def receive_data():
    """Receives JSON data from MT5 EA via HTTP POST."""

    try:
        data = request.get_json(force=True, silent=False)

        symbol = data.get('symbol')
        timeframe_str = data.get('timeframe_str')
        bar_time_ts = data.get('time')
        bar_open = data.get('open')
        bar_high = data.get('high')
        bar_low = data.get('low')
        bar_close = data.get('close')
        bar_volume = data.get('tick_volume')

        # --- Print ข้อมูลที่ได้รับเพื่อตรวจสอบ (ปรับปรุงให้สั้นลง) ---
        try:
            # แปลง Timestamp เป็น Datetime โดยใช้ MT5_TIMEZONE
            received_bar_dt_print = datetime.datetime.fromtimestamp(bar_time_ts, tz=pytz.utc).astimezone(MT5_TIMEZONE) # Assume timestamp is UTC
            print(f"[{datetime.datetime.now()}] Received bar for {symbol} {timeframe_str} at {received_bar_dt_print} (Close: {bar_close:.5f})")
        except Exception:
            print(f"[{datetime.datetime.now()}] Received bar for {symbol} {timeframe_str} with invalid timestamp {bar_time_ts} (Close: {bar_close:.5f})")
        # --- จบส่วน Print ข้อมูลที่ได้รับ ---

        if not all([symbol, timeframe_str, bar_time_ts, bar_open, bar_high, bar_low, bar_close]):
            print("Received incomplete data after parsing")
            print(f"Received data: {data}")
            return jsonify({"status": "ERROR", "message": "Incomplete data received after parsing"}), 400

        timeframe_enum = timeframe_map.get(timeframe_str)
        if timeframe_enum is None:
            print(f"Invalid or unknown timeframe string received: {timeframe_str}")
            return jsonify({"status": "ERROR", "message": f"Invalid or unknown timeframe {timeframe_str}"}), 400

        try:
            # แปลง Timestamp เป็น Datetime โดยใช้ MT5_TIMEZONE
            bar_dt = datetime.datetime.fromtimestamp(bar_time_ts, tz=pytz.utc).astimezone(MT5_TIMEZONE) # Assume timestamp is UTC
        except Exception as e:
            print(f"Error converting timestamp {bar_time_ts}: {e}")
            return jsonify({"status": "ERROR", "message": f"Invalid timestamp {bar_time_ts}"}), 400

        new_data_row = {
            'time': bar_dt,
            'open': bar_open,
            'high': bar_high,
            'low': bar_low,
            'close': bar_close,
            'volume': bar_volume if bar_volume is not None else 0
        }
        new_data = pd.DataFrame([new_data_row])
        new_data.set_index('time', inplace=True)

        with data_lock:
            # ใช้ cleaned_symbol เป็น key ใน Data Store ด้วย
            cleaned_symbol = symbol.replace('#', '')
            key = (cleaned_symbol, timeframe_enum)
            if key not in market_data_store:
                print(f"[{datetime.datetime.now()}] Initializing data store for {cleaned_symbol} ({timeframe_str})")
                market_data_store[key] = pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume'])
                market_data_store[key].index.name = 'time'

            # ตรวจสอบว่าแท่งที่ได้รับเป็นแท่งใหม่ (เวลาไม่ซ้ำ)
            # หรือถ้าเป็นแท่งเวลาเดิม อาจจะต้องมี Logic อัปเดตข้อมูลแท่งนั้น
            # โค้ดปัจจุบันแค่เช็คว่าเวลาใหม่หรือไม่ ถ้าซ้ำจะไม่เพิ่ม
            if new_data.index[0] not in market_data_store[key].index:
                market_data_store[key] = pd.concat([market_data_store[key], new_data])
                # จำกัดขนาดข้อมูล
                required_bars = 202 # *** ต้องตรงกับ required_bars ใน process_data_and_trade ***
                # เก็บข้อมูลให้เพียงพอสำหรับ Required Bars ที่คำนวณ Indicator + Safety buffer
                market_data_store[key] = market_data_store[key].tail(max(required_bars + 50, 500)) # เก็บอย่างน้อย 500 หรือตาม required_bars + buffer
                print(f"[{datetime.datetime.now()}] Added new bar for {cleaned_symbol} ({timeframe_str}) at {bar_dt}. Total bars: {len(market_data_store[key])}")

            # else: ถ้าเวลาซ้ำ อาจจะเป็นการอัปเดตแท่งปัจจุบัน ซึ่งโค้ดนี้ยังไม่ได้ handle การอัปเดต
            #      pass # ไม่ต้องทำอะไร ถ้าเวลาซ้ำ (ถือว่าได้รับข้อมูลซ้ำ หรือเป็นแท่งปัจจุบันที่ยังไม่ปิด)

        # เรียกใช้ฟังก์ชันประมวลผลใน Thread แยก
        # ส่ง original symbol ไปให้ process_data_and_trade เพื่อให้มันจัดการ clean เอง
        processing_thread = threading.Thread(target=process_data_and_trade, args=(symbol, timeframe_enum))
        processing_thread.start()

        # ส่ง HTTP 200 OK กลับไปทันที
        # *** ถ้าต้องการให้ Signal กลับไปใน Response นี้ ต้องแก้ไขส่วนนี้ ***
        # แนวทางการเก็บใน global variable แล้ว EA มา poll น่าจะเหมาะสมกว่า
        # return jsonify({"status": "OK", "message": "Data received and processing started. Signal will be processed."}), 200

        # Example: Send back a simple JSON response confirming receipt
        return jsonify({"status": "OK", "message": "Data received and processing started."}), 200

    except BadRequest as e:
        print(f"[{datetime.datetime.now()}] Received data but failed due to Bad Request: {e}")
        print(f"Raw request data (bytes): {request.data}")
        return jsonify({"status": "ERROR", "message": f"Bad Request: {e}"}), 400

    except Exception as e:
        print(f"[{datetime.datetime.now()}] Error processing request: {e}")
        traceback.print_exc()
        return jsonify({"status": "ERROR", "message": f"Server Error: {e}"}), 500

# --- Optional: Endpoint to Get Signal ---
# คุณสามารถสร้าง Endpoint นี้เพื่อให้ MT5 EA เรียกมาขอ Signal ล่าสุดได้
# ต้องเพิ่ม Global variable สำหรับเก็บ signals เช่น latest_signals_data
# และต้องเพิ่ม lock สำหรับตัวแปรนี้
# latest_signals_data = {}
#
# @app.route('/get_signal/<string:symbol>/<string:timeframe_str>', methods=['GET'])
# def get_signal(symbol, timeframe_str):
#     try:
#         timeframe_enum = timeframe_map.get(timeframe_str.upper()) # แปลงเป็นตัวใหญ่เผื่อ MT5 ส่งมาไม่เหมือนเดิม
#         if timeframe_enum is None:
#             return jsonify({"status": "ERROR", "message": f"Invalid or unknown timeframe {timeframe_str}"}), 400
#
#         cleaned_symbol = symbol.replace('#', '') # Clean symbol ที่ได้รับจาก MT5
#         key = (cleaned_symbol, timeframe_enum)
#
#         signal_info = None
#         with data_lock: # ใช้ lock เดียวกัน หรือ lock แยกสำหรับ signals
#             signal_info = latest_signals_data.get(key, None)
#
#         if signal_info:
#              # อาจจะเช็คว่า Signal นี้ยังใหม่พอ (เช่น เวลาของ signal ตรงกับเวลาของแท่งล่าสุดที่ MT5 ส่งมา)
#              # คุณอาจจะต้องส่งเวลาของแท่งล่าสุดจาก MT5 ใน request นี้ด้วยเพื่อเปรียบเทียบ
#              # For simplicity, just return the latest stored signal
#              return jsonify({
#                  "status": "OK",
#                  "symbol": cleaned_symbol,
#                  "timeframe": timeframe_str.upper(),
#                  "signal": signal_info["signal"],
#                  "confidence": signal_info["confidence"],
#                  "timestamp": signal_info["timestamp"].timestamp() # ส่ง timestamp กลับไปเป็น Unix timestamp
#              }), 200
#         else:
#             # ถ้ายังไม่มี Signal ใน Store
#             return jsonify({"status": "OK", "symbol": cleaned_symbol, "timeframe": timeframe_str.upper(), "signal": "HOLD", "confidence": 0.0, "message": "No signal available yet"}), 200
#
#     except Exception as e:
#         print(f"Error in /get_signal endpoint for {symbol} {timeframe_str}: {e}")
#         traceback.print_exc()
#         return jsonify({"status": "ERROR", "message": f"Server Error: {e}"}), 500

# --- Main Execution ---
if __name__ == "__main__":
    # initialize_mt5() # Uncomment ถ้า Python ต้องใช้ mt5.py ในการส่งคำสั่งเทรดเอง

    print(f"Starting HTTP Server on http://{HTTP_HOST}:{HTTP_PORT}")
    try:
        # Running with debug=False for performance
        # threaded=True is default and necessary for handling multiple requests/threads
        app.run(host=HTTP_HOST, port=HTTP_PORT, debug=False)
    except Exception as e:
        print(f"Failed to start Flask server: {e}")
        traceback.print_exc()

    print("Server stopped.")
    # mt5.shutdown() # Uncomment ถ้ามีการ initialize_mt5
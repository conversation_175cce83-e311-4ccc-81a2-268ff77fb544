#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ตรวจสอบการปรับปรุงพารามิเตอร์หลังจากการทดสอบ quick_tuning_test.py
"""

import sys
import os
import pandas as pd
import numpy as np
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.metrics import roc_auc_score
import lightgbm as lgb
import json

# เพิ่ม path สำหรับ import ฟังก์ชันจากไฟล์หลัก
sys.path.append('.')

def create_test_data(n_samples=1000, random_state=42):
    """สร้างข้อมูลทดสอบเหมือนใน quick_tuning_test.py"""
    X, y = make_classification(
        n_samples=n_samples,
        n_features=15,
        n_informative=10,
        n_redundant=3,
        n_clusters_per_class=2,
        weights=[0.8, 0.2],  # 20% positive class
        flip_y=0.02,
        random_state=random_state
    )
    
    feature_names = [f"Feature_{i:02d}" for i in range(15)]
    X_df = pd.DataFrame(X, columns=feature_names)
    
    return X_df, y

def test_old_vs_new_defaults():
    """เปรียบเทียบพารามิเตอร์เก่าและใหม่"""
    print("🔍 เปรียบเทียบ Default Parameters")
    print("="*60)
    
    # พารามิเตอร์เก่า (ก่อนการปรับปรุง)
    old_params = {
        'objective': 'binary',
        'metric': 'auc',
        'learning_rate': 0.02,
        'num_leaves': 31,
        'min_data_in_leaf': 30,
        'n_estimators': 100,
        'random_state': 42,
        'verbosity': -1
    }
    
    # โหลดพารามิเตอร์ใหม่จากไฟล์
    try:
        from python_LightGBM_15_Tuning import get_lgbm_params
        
        # สร้างข้อมูลทดสอบ class imbalance
        y_test = np.array([0] * 800 + [1] * 200)  # 4:1 ratio
        new_params_full = get_lgbm_params(y=y_test, use_scale_pos_weight=True)
        
        # เลือกเฉพาะพารามิเตอร์ที่เปรียบเทียบได้
        new_params = {
            'objective': new_params_full['objective'],
            'metric': 'auc',
            'learning_rate': new_params_full['learning_rate'],
            'num_leaves': new_params_full['num_leaves'],
            'min_data_in_leaf': new_params_full['min_data_in_leaf'],
            'n_estimators': 100,
            'random_state': 42,
            'verbosity': -1
        }
        
        print("📊 การเปรียบเทียบพารามิเตอร์:")
        print("-"*60)
        
        comparison_params = ['learning_rate', 'num_leaves', 'min_data_in_leaf']
        
        for param in comparison_params:
            old_val = old_params[param]
            new_val = new_params[param]
            change = "↗️" if new_val > old_val else "↘️" if new_val < old_val else "➡️"
            print(f"{param:20}: {old_val:6} → {new_val:6} {change}")
        
        return old_params, new_params
        
    except Exception as e:
        print(f"❌ ไม่สามารถโหลดพารามิเตอร์ใหม่ได้: {e}")
        return None, None

def test_performance_comparison():
    """ทดสอบประสิทธิภาพของพารามิเตอร์เก่าและใหม่"""
    print(f"\n🏁 ทดสอบประสิทธิภาพ")
    print("="*60)
    
    # สร้างข้อมูลทดสอบ
    X, y = create_test_data(n_samples=800)
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    print(f"📊 ข้อมูลทดสอบ: Train={len(X_train)}, Test={len(X_test)}")
    print(f"📊 Class distribution: {np.bincount(y_test)}")
    
    # ได้พารามิเตอร์จากฟังก์ชันก่อนหน้า
    old_params, new_params = test_old_vs_new_defaults()
    
    if old_params is None or new_params is None:
        print("❌ ไม่สามารถทดสอบได้เนื่องจากไม่สามารถโหลดพารามิเตอร์")
        return
    
    results = {}
    
    # ทดสอบพารามิเตอร์เก่า
    print(f"\n🔄 ทดสอบพารามิเตอร์เก่า...")
    try:
        old_model = lgb.LGBMClassifier(**old_params)
        old_model.fit(X_train, y_train)
        old_pred = old_model.predict_proba(X_test)[:, 1]
        old_auc = roc_auc_score(y_test, old_pred)
        results['old'] = old_auc
        print(f"✅ Old Parameters AUC: {old_auc:.4f}")
    except Exception as e:
        print(f"❌ Error with old parameters: {e}")
        results['old'] = None
    
    # ทดสอบพารามิเตอร์ใหม่
    print(f"\n🔄 ทดสอบพารามิเตอร์ใหม่...")
    try:
        new_model = lgb.LGBMClassifier(**new_params)
        new_model.fit(X_train, y_train)
        new_pred = new_model.predict_proba(X_test)[:, 1]
        new_auc = roc_auc_score(y_test, new_pred)
        results['new'] = new_auc
        print(f"✅ New Parameters AUC: {new_auc:.4f}")
    except Exception as e:
        print(f"❌ Error with new parameters: {e}")
        results['new'] = None
    
    # เปรียบเทียบผลลัพธ์
    if results['old'] is not None and results['new'] is not None:
        improvement = ((results['new'] - results['old']) / results['old']) * 100
        print(f"\n📈 ผลการเปรียบเทียบ:")
        print("-"*40)
        print(f"Old Parameters AUC: {results['old']:.4f}")
        print(f"New Parameters AUC: {results['new']:.4f}")
        print(f"Improvement: {improvement:+.2f}%")
        
        if improvement > 0:
            print("✅ การปรับปรุงให้ผลดีขึ้น!")
        elif improvement > -1:
            print("➡️ ผลลัพธ์ใกล้เคียงกัน (ยอมรับได้)")
        else:
            print("⚠️ ผลลัพธ์แย่ลง - ควรพิจารณาปรับปรุงเพิ่มเติม")
    
    return results

def check_param_dist_updates():
    """ตรวจสอบการอัปเดต param_dist"""
    print(f"\n🔧 ตรวจสอบการอัปเดต param_dist")
    print("="*60)
    
    try:
        from python_LightGBM_15_Tuning import param_dist
        
        print("📋 param_dist ที่อัปเดตแล้ว:")
        print("-"*40)
        
        key_params = ['learning_rate', 'num_leaves', 'min_data_in_leaf']
        
        for param in key_params:
            if param in param_dist:
                values = param_dist[param]
                print(f"{param:20}: {values}")
                
                # ตรวจสอบว่าค่าที่ดีจากการทดสอบอยู่ในช่วงหรือไม่
                if param == 'learning_rate':
                    if 0.1 in values and 0.2 in values:
                        print(f"  ✅ รวมค่าที่ดีจากการทดสอบ (0.1, 0.2)")
                    else:
                        print(f"  ⚠️ ไม่รวมค่าที่ดีจากการทดสอบ (0.1, 0.2)")
                
                elif param == 'num_leaves':
                    if 15 in values:
                        print(f"  ✅ รวมค่าที่ดีจากการทดสอบ (15)")
                    else:
                        print(f"  ⚠️ ไม่รวมค่าที่ดีจากการทดสอบ (15)")
                
                elif param == 'min_data_in_leaf':
                    if 10 in values:
                        print(f"  ✅ รวมค่าที่ดีจากการทดสอบ (10)")
                    else:
                        print(f"  ⚠️ ไม่รวมค่าที่ดีจากการทดสอบ (10)")
        
        # คำนวณจำนวนการผสมผสาน
        total_combinations = 1
        for param, values in param_dist.items():
            total_combinations *= len(values)
        
        print(f"\n📊 สถิติ param_dist:")
        print(f"  - จำนวนพารามิเตอร์: {len(param_dist)}")
        print(f"  - จำนวนการผสมผสาน: {total_combinations:,}")
        print(f"  - RandomizedSearchCV จะทดสอบ: 50 การผสมผสาน")
        
        return True
        
    except Exception as e:
        print(f"❌ ไม่สามารถตรวจสอบ param_dist ได้: {e}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🔍 ตรวจสอบการปรับปรุงพารามิเตอร์")
    print("="*80)
    
    # 1. เปรียบเทียบ default parameters
    old_params, new_params = test_old_vs_new_defaults()
    
    # 2. ทดสอบประสิทธิภาพ
    if old_params and new_params:
        performance_results = test_performance_comparison()
    
    # 3. ตรวจสอบ param_dist
    param_dist_ok = check_param_dist_updates()
    
    # สรุปผลลัพธ์
    print(f"\n🎯 สรุปการตรวจสอบ")
    print("="*60)
    
    if old_params and new_params:
        print("✅ การโหลดพารามิเตอร์: สำเร็จ")
        print("✅ การเปรียบเทียบประสิทธิภาพ: สำเร็จ")
    else:
        print("❌ การโหลดพารามิเตอร์: ล้มเหลว")
    
    if param_dist_ok:
        print("✅ การตรวจสอบ param_dist: สำเร็จ")
    else:
        print("❌ การตรวจสอบ param_dist: ล้มเหลว")
    
    print(f"\n🚀 พร้อมสำหรับการใช้งานจริง!")
    print("💡 ขั้นตอนถัดไป: python python_LightGBM_15_Tuning.py")

if __name__ == "__main__":
    main()

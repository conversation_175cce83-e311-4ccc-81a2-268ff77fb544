#+------------------------------------------------------------------+
#|                                     Python Server Endpoint.py    |
#|                      Receives Bar Data from MT5 via HTTP (Flask) |
#+------------------------------------------------------------------+
from flask import Flask, request, jsonify
import json
import time
import datetime
import MetaTrader5 as mt5
import pandas as pd
import threading
import pandas_ta as ta # Make sure pandas_ta is installed
from werkzeug.exceptions import BadRequest # นำเข้า BadRequest เพื่อจับ Error จาก Flask/Werkzeug
import os # Import os for path joining and existence checks
import joblib # Import joblib for loading models and scalers
import numpy as np # Import numpy for numerical operations
import traceback # Import traceback for detailed error logging
import pickle # Import pickle library (สำหรับโหลด features_list ถ้าบันทึกด้วย pickle)
import sys # Import sys
import pytz # Import pytz for timezone handling (recommended for timestamps)

# --- Import necessary components from python_LightGBM.py ---
try:
    # เพิ่ม Path ของโฟลเดอร์ที่เก็บ python_LightGBM.py เข้าไปใน sys.path ชั่วคราว
    # แก้ไข path นี้ให้ชี้ไปยังโฟลเดอร์ที่เก็บไฟล์ python_LightGBM.py ของคุณ
    python_lightgbm_folder = r'C:\Users\<USER>\test_gold' # *** แก้ Path นี้ ***
    if python_lightgbm_folder not in sys.path:
        sys.path.append(python_lightgbm_folder)
        print(f"Added {python_lightgbm_folder} to sys.path")

    # Import specific functions needed
    from python_LightGBM_02 import load_model, load_scaler, select_features, safe_json_serialize

    # Define the model confidence threshold
    model_confidence_threshold = 0.50 # ใช้ค่า Threshold จากโมเดลของคุณ

    # Define the base path for your models
    MODEL_BASE_PATH = r'C:\Users\<USER>\test_gold\models' # *** แก้ Path นี้ ***
    print(f"Model base path set to: {MODEL_BASE_PATH}")

    # กำหนด Timezone ของ MT5 Server (มักจะเป็น UTC)
    MT5_TIMEZONE = pytz.utc # หรือ pytz.timezone('Etc/UTC') หรือ Timezone ของ Server Broker

except ImportError as e:
    print(f"Error: Could not import components from python_LightGBM.py - {e}")
    print("Please ensure python_LightGBM.py is in the specified path or in Python's path.")
    print("Exiting server initialization.")
    exit()
except Exception as e:
    print(f"An unexpected error occurred during import from python_LightGBM.py: {e}")
    traceback.print_exc()
    print("Exiting server initialization.")
    exit()

# --- Configuration ---
HTTP_PORT = 54321
HTTP_HOST = '127.0.0.1'

# MT5 Connection Configuration (ยังคงไว้ถ้าต้องการใช้ MT5 Library ใน Python)
# Note: These are needed only if Python needs to place trades directly via mt5.py library
# If MT5 EA places trades based on the signal, this might not be strictly necessary for the server itself.
# คุณอาจจะเรียก initialize_mt5() ตรงนี้ ถ้าต้องการใช้ MT5 Library สำหรับส่งคำสั่งเทรด
# initialize_mt5()

# MT5 Connection Configuration (ยังคงไว้ถ้าต้องการใช้ MT5 Library ใน Python)
# def initialize_mt5():
#     """Initialize connection to the MetaTrader 5 terminal"""
#     print("Initializing MT5 connection...")
#     if not mt5.is_initialized():
#         if not mt5.initialize(login=MT5_LOGIN, password=MT5_PASSWORD, server=MT5_SERVER, path=MT5_PATH, timeout=5000):
#             print(f"MT5 initialize() failed, error code = {mt5.last_error()}")
#             return False
#         print("MT5 initialized successfully.")
#         account_info = mt5.account_info()
#         if account_info:
#             print(f"Connected to account: {account_info.login}")
#         else:
#             print(f"Failed to get account info after initialize, error code = {mt5.last_error()}")
#             return False
#     else:
#         print("MT5 already initialized.")
#         account_info = mt5.account_info()
#         if account_info:
#             print(f"Connected to account: {account_info.login}")
#         else:
#              print(f"MT5 was initialized but failed to get account info, error code = {mt5.last_error()}")
#              return False
#
#     return True

# --- Add Timeframe Mapping ---
timeframe_map = {
    "PERIOD_M1": mt5.TIMEFRAME_M1,
    "PERIOD_M2": mt5.TIMEFRAME_M2,
    "PERIOD_M3": mt5.TIMEFRAME_M3,
    "PERIOD_M4": mt5.TIMEFRAME_M4,
    "PERIOD_M5": mt5.TIMEFRAME_M5,
    "PERIOD_M6": mt5.TIMEFRAME_M6,
    "PERIOD_M10": mt5.TIMEFRAME_M10,
    "PERIOD_M12": mt5.TIMEFRAME_M12,
    "PERIOD_M15": mt5.TIMEFRAME_M15,
    "PERIOD_M20": mt5.TIMEFRAME_M20,
    "PERIOD_M30": mt5.TIMEFRAME_M30,
    "PERIOD_H1": mt5.TIMEFRAME_H1,
    "PERIOD_H2": mt5.TIMEFRAME_H2,
    "PERIOD_H3": mt5.TIMEFRAME_H3,
    "PERIOD_H4": mt5.TIMEFRAME_H4,
    "PERIOD_H6": mt5.TIMEFRAME_H6,
    "PERIOD_H8": mt5.TIMEFRAME_H8,
    "PERIOD_H12": mt5.TIMEFRAME_H12,
    "PERIOD_D1": mt5.TIMEFRAME_D1,
    "PERIOD_W1": mt5.TIMEFRAME_W1,
    "PERIOD_MN1": mt5.TIMEFRAME_MN1,
}

timeframe_code_map = {
    "PERIOD_M1": 1, "PERIOD_M2": 5, "PERIOD_M3": 15, "PERIOD_M30": 30, "PERIOD_H1": 60, "PERIOD_H4": 240
    # เพิ่ม Timeframe ที่เหลือตาม folder structure ของคุณ
}

# --- Global Data Storage ---
market_data_store = {}
data_lock = threading.Lock()

# --- Dictionary to store the latest signal and confidence for each symbol/timeframe ---
# This will be used to send the signal back in the HTTP response.
# Key: (cleaned_symbol, timeframe_int)
# Value: {"signal": str, "confidence": float, "timestamp": datetime}
latest_signals_data = {}
signals_lock = threading.Lock() # Lock for accessing latest_signals_data

# --- Flask App Setup ---
app = Flask(__name__)

# --- Model Loading Cache ---
loaded_models = {}
model_lock = threading.Lock()

def load_model_components(symbol, timeframe):
    """
    Loads the trained model, scaler, and feature list for a given symbol and timeframe.
    Uses a cache to avoid reloading.
    """
    
    key = (symbol, timeframe)
    with model_lock:
        if key in loaded_models:
            # print(f"Loading model components from cache for {symbol} (enum: {timeframe})") # Print น้อยลง
            return loaded_models[key]

        model_name = "LightGBM"

        model_dir = os.path.join(MODEL_BASE_PATH, f"{symbol}_{str(timeframe).zfill(3)}")
        model_path = os.path.join(model_dir, f"LightGBM_{symbol}_{str(timeframe).zfill(3)}_trained.pkl")
        scaler_path = os.path.join(model_dir, f"LightGBM_{symbol}_{str(timeframe).zfill(3)}_scaler.pkl")
        features_list_path = os.path.join(model_dir, f"LightGBM_{symbol}_{str(timeframe).zfill(3)}_features.pkl")

        print(f"Attempting to load model components from: {model_dir}")

        if not os.path.exists(model_path):
            print(f"Error: Model file not found at {model_path}")
            return None, None, None
        if not os.path.exists(scaler_path):
            print(f"Error: Scaler file not found at {scaler_path}")
            return None, None, None
        if not os.path.exists(features_list_path):
            print(f"Error: Features list file not found at {features_list_path}")
            return None, None, None

        try:
            print(f"ทดสอบ model_path ก่อนโหลด model : {model_path} scaler : {scaler_path}")
            
            model = load_model(model_name,symbol,timeframe)
            scaler = load_scaler(model_name,symbol,timeframe)
            with open(features_list_path, 'rb') as f:
                try:
                    features_list = pickle.load(f)
                except Exception:
                    f.seek(0)
                    features_list = joblib.load(f)

            print(f"Successfully loaded model components for {symbol} (enum: {timeframe})")

            loaded_models[key] = (model, scaler, features_list)

            return model, scaler, features_list

        except FileNotFoundError as e:
            print(f"Error loading model components: File not found - {e}")
            traceback.print_exc()
            return None, None, None
        except Exception as e:
            print(f"Error loading model components for {symbol} (enum: {timeframe}): {e}")
            traceback.print_exc()
            return None, None, None

# --- Data Processing Function (จะเรียกใช้ใน Thread แยก) ---
def process_data_and_trade(symbol, timeframe):
    """
    Calculates indicators, runs model, determines trade signal and confidence.
    This runs in a separate thread.
    """

    print(f"[{datetime.datetime.now()}] Processing data for {symbol} ({timeframe})...")

    df = None
    with data_lock:
        key = (symbol, timeframe)
        if key in market_data_store and len(market_data_store[key]) > 0:
            df = market_data_store[key].copy()
            latest_bar_dt = df.index[-1] # Get the timestamp of the latest bar
        else:
            print(f"[{datetime.datetime.now()}] No sufficient data yet for {symbol} ({timeframe}). Waiting for more bars.")
            return

    # --- แก้ไข: เปลี่ยนชื่อคอลัมน์ให้เป็นตัวพิมพ์ใหญ่เพื่อให้ตรงกับโค้ด Indicator ---
    # คอลัมน์ที่ต้องการเปลี่ยน: 'open', 'high', 'low', 'close', 'volume' ให้เป็น: 'Open', 'High', 'Low', 'Close', 'Volume'
    rename_map = {
        'open': 'Open',
        'high': 'High',
        'low': 'Low',
        'close': 'Close',
        'volume': 'Volume'
    }
    # ใช้ .rename() เพื่อเปลี่ยนชื่อคอลัมน์ โดยใช้ errors='ignore' เพื่อไม่ให้ error ถ้าคอลัมน์ไม่มี
    df_features = df.rename(columns=rename_map, errors='ignore')

    # ตรวจสอบว่าเปลี่ยนชื่อคอลัมน์หลักสำเร็จหรือไม่
    required_price_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
    if not all(col in df_features.columns for col in required_price_cols):
        print(f"Error: Missing required price columns after renaming: {[col for col in required_price_cols if col not in df_features.columns]}")
        # Print คอลัมน์ที่มีอยู่เพื่อ Debug
        print(f"Available columns in df_features: {df_features.columns.tolist()}")
        return # หยุดการประมวลผลถ้าคอลัมน์หลักไม่ครบ
    # --- จบการแก้ไข ---

    # --- ตรวจสอบจำนวนข้อมูลที่เพียงพอสำหรับการคำนวณ Indicator ---
    required_bars = 205 # *** ต้องตรงกับ BARS_FOR_PYTHON ใน MQL5 ***
    if len(df_features) < required_bars:
        print(f"[{datetime.datetime.now()}] Not enough data ({len(df_features)} bars) for {symbol} (enum: {timeframe}). Minimum required: {required_bars}. Skipping processing.")
        # *** ถ้าข้อมูลน้อยกว่าที่ต้องการ อาจจะคืนค่า Signal เป็น HOLD/ERROR ***
        signal = "HOLD"
        probability_tp_hit = 0.0
        with signals_lock:
            signals_key = (symbol, timeframe)
            latest_signals_data[signals_key] = {
                "symbol": symbol,
                "timeframe_enum": timeframe,
                "timeframe_str": timeframe_map.get(timeframe, 'UNKNOWN'),
                "signal": signal,
                "confidence": float(probability_tp_hit),
                "timestamp": latest_bar_dt if 'latest_bar_dt' in locals() else datetime.datetime.now(MT5_TIMEZONE)
            }
        print(f"[{datetime.datetime.now()}] Stored default signal due to insufficient data for {symbol} (enum: {timeframe}).")
        return # หยุดการประมวลผล

    signal = "HOLD" # Default signal
    probability_tp_hit = 0.0 # Default confidence

    try:
        # --- 1. คำนวณ Indicators และสร้าง Features ทั้งหมดที่ใช้ในโมเดล ---
        # คัดลอก Logic จาก load_and_process_data ส่วน "3. สร้าง technical indicators" มาที่นี่
        # ใช้ df_features ที่เปลี่ยนชื่อคอลัมน์เป็นตัวพิมพ์ใหญ่แล้ว

        # แสดงผล
        print("\n✅ ข้อมูล : df_features")
        print(df_features.info())
        print(df_features.head())
        print(df_features.tail())

        # --- เพิ่มฟีเจอร์วันและเวลา ---
        # ใน Server, Bar Time มาเป็น datetime index อยู่แล้ว
        df_features['DateTime'] = df_features.index

        print("\n✅ ตรวจสอบ DateTime")
        print(df_features.head())

        print(f"\n✅ เริ่มคำนวณ Time action")
        df_features['Entry_DayOfWeek'] = df_features.index.dayofweek
        df_features['Entry_Hour'] = df_features.index.hour

        # --- สร้างฟีเจอร์สำหรับช่วงเวลาที่สำคัญ ---
        df_features['IsMorning'] = ((df_features['Entry_Hour'] >= 8) & (df_features['Entry_Hour'] < 12)).astype(int)
        df_features['IsAfternoon'] = ((df_features['Entry_Hour'] >= 12) & (df_features['Entry_Hour'] < 16)).astype(int)
        df_features['IsEvening'] = ((df_features['Entry_Hour'] >= 16) & (df_features['Entry_Hour'] < 20)).astype(int)
        df_features['IsNight'] = ((df_features['Entry_Hour'] >= 20) | (df_features['Entry_Hour'] < 4)).astype(int) # ตรวจสอบ Logic ช่วงเวลาอีกครั้งให้ถูกต้อง

        # ใช้คอลัมน์ตัวพิมพ์ใหญ่ ('Close', 'Open') และ .shift(1)
        df_features["Bar_CLp"] = df_features['Close'].shift(1)

        # --- Price action Features ---
        print(f"✅ เริ่มคำนวณ Price action")
        df_features["Bar_CL"] = 0.0
        df_features.loc[df_features['Close'] > df_features['Open'], "Bar_CL"] = 1.0
        df_features.loc[df_features['Close'] < df_features['Open'], "Bar_CL"] = -1.0

        df_features["Bar_CL_OC"] = 0.0
        df_features.loc[df_features['Close'] > np.maximum(df_features['Open'].shift(1), df_features['Close'].shift(1)), "Bar_CL_OC"] = 1.0
        df_features.loc[df_features['Close'] < np.minimum(df_features['Open'].shift(1), df_features['Close'].shift(1)), "Bar_CL_OC"] = -1.0

        df_features["Bar_CL_HL"] = 0.0
        df_features.loc[(df_features['Close'] > df_features['High'].shift(1)) & (df_features['Close'] > df_features['Open']), "Bar_CL_HL"] = 1.0
        df_features.loc[(df_features['Close'] < df_features['Low'].shift(1)) & (df_features['Close'] < df_features['Open']), "Bar_CL_HL"] = -1.0

        df_features["Bar_SW"] = 0.0
        df_features.loc[(df_features['Close'] > df_features['Open']) & (df_features['High'] > df_features['High'].shift(1)) & (df_features['Low'] > df_features['Low'].shift(1)), "Bar_SW"] = 1.0
        df_features.loc[(df_features['Close'] < df_features['Open']) & (df_features['High'] < df_features['High'].shift(1)) & (df_features['Low'] < df_features['Low'].shift(1)), "Bar_SW"] = -1.0

        df_features["Bar_TL"] = 0.0
        df_features.loc[(df_features['Close'] > df_features['Open']) & (df_features['High'] < df_features['High'].shift(1)) & (df_features['Low'] < df_features['Low'].shift(1)), "Bar_TL"] = 1.0
        df_features.loc[(df_features['Close'] < df_features['Open']) & (df_features['High'] > df_features['High'].shift(1)) & (df_features['Low'] > df_features['Low'].shift(1)), "Bar_TL"] = 1.0

        df_features["Bar_DTB"] = 0.0
        df_features.loc[(df_features['Close'] > df_features['Open']) & (df_features['Low'] == df_features['Low'].shift(1)), "Bar_DTB"] = 1.0
        df_features.loc[(df_features['Close'] < df_features['Open']) & (df_features['High'] == df_features['High'].shift(1)), "Bar_DTB"] = -1.0

        df_features["Bar_OSB"] = 0.0
        df_features.loc[(df_features['Close'] > df_features['Open']) & (df_features['High'] > df_features['High'].shift(1)) & (df_features['Low'] < df_features['Low'].shift(1)), "Bar_OSB"] = 1.0
        df_features.loc[(df_features['Close'] < df_features['Open']) & (df_features['High'] > df_features['High'].shift(1)) & (df_features['Low'] < df_features['Low'].shift(1)), "Bar_OSB"] = -1.0

        df_features["Bar_FVG"] = 0.0
        df_features.loc[(df_features["Low"] > df_features["High"].shift(2)) & (df_features["Close"] > df_features["Open"]), "Bar_FVG"] = 1.0
        df_features.loc[(df_features["High"] < df_features["Low"].shift(2)) & (df_features["Close"] < df_features["Open"]), "Bar_FVG"] = -1.0

        print(f"✅ เริ่มคำนวณ Pin Bar")
        df_features["Bar_longwick"] = 0.0
        epsilon = 1e-9
        lower_wick = (np.minimum(df_features['Open'], df_features['Close']) - df_features['Low']).replace(0, epsilon)
        upper_wick = (df_features['High'] - np.maximum(df_features['Open'], df_features['Close'])).replace(0, epsilon)
        pinbar_up = lower_wick / (df_features['High'] - np.minimum(df_features['Open'], df_features['Close']))
        pinbar_down = upper_wick / (np.maximum(df_features['Open'], df_features['Close']) - df_features['Low'])
        df_features.loc[pinbar_up > pinbar_down, "Bar_longwick"] = pinbar_up
        df_features.loc[pinbar_down > pinbar_up, "Bar_longwick"] = -1 * pinbar_down
        
        print(f"✅ เริ่มคำนวณ Range Bar")
        df_features['Price_Range'] = df_features["High"] - df_features["Low"]
        df_features['Price_Move'] = df_features["Close"] - df_features["Open"]

        print(f"✅ เริ่มคำนวณ Price Strangth")
        df_features['Price_Strangth'] = 0.0
        body_size_oc = np.abs(df_features["Close"] - df_features["Open"])
        body_size_ocp = np.abs(df_features["Close"].shift(1) - df_features["Open"].shift(1))
        body_size_hl = np.abs(df_features["High"] - df_features["Low"])
        body_size_hlp = np.abs(df_features["High"].shift(1) - df_features["Low"].shift(1))
        
        df_features.loc[(body_size_oc < body_size_ocp) & (body_size_hl < body_size_hlp) & (df_features["Close"] < df_features["Open"]), "Price_Strangth"] = 1
        df_features.loc[(body_size_oc < body_size_ocp) & (body_size_hl < body_size_hlp) & (df_features["Close"] > df_features["Open"]), "Price_Strangth"] = -1

        # --- Volume_MA20, Volume_Spike ---
        print(f"✅ เริ่มคำนวณ Volume")
        df_features['Volume_MA20'] = df_features['Volume'].rolling(20, min_periods=1).mean()
        # แก้ PerformanceWarning ใน .fillna() ถ้าเกิดขึ้น
        # df_features['Volume_MA20'].fillna(df_features['Volume'].mean(), inplace=True) # บรรทัดเดิม
        df_features['Volume_MA20'] = df_features['Volume_MA20'].fillna(df_features['Volume'].mean()) # แก้เป็นแบบไม่ inplace
        df_features['Volume_Spike'] = df_features['Volume'] / (df_features['Volume_MA20'] + 1e-10)

        # --- EMA Calculation ---
        print(f"✅ เริ่มคำนวณ EMA")
        df_features['EMA50'] = df_features['Close'].ewm(span=50, min_periods=1).mean()
        df_features['EMA100'] = df_features['Close'].ewm(span=100, min_periods=1).mean()
        df_features['EMA200'] = df_features['Close'].ewm(span=200, min_periods=1).mean()

        # --- EMA Related Features ---
        df_features['EMA_diff'] = (df_features['EMA50'] - df_features['EMA200'])

        df_features['MA_Cross'] = 0.0
        df_features.loc[(df_features['EMA50'] > df_features['EMA200']), "MA_Cross"] = 1.0
        df_features.loc[(df_features['EMA50'] < df_features['EMA200']), "MA_Cross"] = -1.0

        df_features["Price_above_EMA50"] = 0.0
        df_features.loc[(df_features["Close"] > df_features["EMA50"]), "Price_above_EMA50"] = 1.0
        df_features.loc[(df_features["Close"] < df_features["EMA50"]), "Price_above_EMA50"] = -1.0

        # --- ความผันผวนระยะสั้น ---
        df_features['Rolling_Vol_5'] = df_features['Close'].pct_change().rolling(5, min_periods=1).std()
        df_features['Rolling_Vol_15'] = df_features['Close'].pct_change().rolling(15, min_periods=1).std()

        # --- ระยะทางจาก EMA ---
        df_features['Dist_EMA50'] = (df_features['Close'] - df_features['EMA50']) / (df_features['EMA50'] + 1e-10) # ป้องกันหารด้วย 0
        df_features['Dist_EMA100'] = (df_features['Close'] - df_features['EMA100']) / (df_features['EMA100'] + 1e-10) # ป้องกันหารด้วย 0
        df_features['Dist_EMA200'] = (df_features['Close'] - df_features['EMA200']) / (df_features['EMA200'] + 1e-10) # ป้องกันหารด้วย 0

        # --- RSI Calculation ---
        print(f"✅ เริ่มคำนวณ RSI df")
        # window_rsi = 14
        # delta = df_features["Close"].diff(1)
        # gain = pd.Series(np.where(delta > 0, delta, 0), index=df_features.index)
        # loss = pd.Series(np.where(delta < 0, -delta, 0), index=df_features.index)
        # avg_gain = gain.rolling(window=window_rsi, min_periods=window_rsi).mean()
        # avg_loss = loss.rolling(window=window_rsi, min_periods=window_rsi).mean()
        # rs = avg_gain / (avg_loss + 1e-10)
        # df_features["RSI14"] = (100 - (100 / (1 + rs)))

        window_rsi = 14
        delta = df_features["Close"].diff(1)
        gain = pd.Series(np.where(delta > 0, delta, 0), index=df_features.index)
        loss = pd.Series(np.where(delta < 0, -delta, 0), index=df_features.index)
        avg_gain = gain.ewm(span=window_rsi, adjust=False).mean() # มักใช้ EWM สำหรับ RSI Calculation
        avg_loss = loss.ewm(span=window_rsi, adjust=False).mean() # มักใช้ EWM สำหรับ RSI Calculation
        # Handle division by zero explicitly for rs calculation
        rs = avg_gain / avg_loss
        # Replace inf values with NaN to handle division by zero if avg_loss is 0
        rs = rs.replace([np.inf, -np.inf], np.nan)
        # Replace NaN in rs resulting from division by zero or initial periods
        rs = rs.fillna(0) # Or another appropriate value if needed
        # ปรับการคำนวณ RSI14 ให้ใช้ EWM และจัดการค่า NaN/inf
        df_features["RSI14"] = 100 - (100 / (1 + rs))
        # เติม NaN ใน RSI14 ช่วงแรกที่คำนวณไม่ได้
        df_features["RSI14"].iloc[:window_rsi-1] = np.nan # เติม NaN ใน window_rsi - 1 แถวแรก

        # --- Indicator Calculation (MACD, RSI, Stochastic, BB, ADX, ATR, SR) ---
        # คำนวณ Indicators หลักก่อน
        print(f"✅ เริ่มคำนวณ MACD, RSI, Stochastic, BB, ADX, ATR, SR")
        macd = ta.macd(df_features["Close"])
        stoch = ta.stoch(high=df_features["High"], low=df_features["Low"], close=df_features["Close"])
        adx = ta.adx(high=df_features["High"], low=df_features["Low"], close=df_features["Close"])

        window_bb = 20
        rolling_mean_bb = df_features["Close"].rolling(window=window_bb, min_periods=1).mean()
        rolling_std_bb = df_features["Close"].rolling(window=window_bb, min_periods=1).std()
        bb_upper = (rolling_mean_bb + (rolling_std_bb * 2))
        bb_lower = (rolling_mean_bb - (rolling_std_bb * 2))
        bb_width = bb_upper - bb_lower

        window_atr = 14
        if all(col in df_features.columns for col in ['High', 'Low', 'Close']):
            tr1 = df_features['High'] - df_features['Low']
            if len(df_features) > 1:
                tr2 = (df_features['High'] - df_features['Close'].shift()).abs()
                tr3 = (df_features['Low'] - df_features['Close'].shift()).abs()
                true_range_df = pd.concat([tr1, tr2, tr3], axis=1) # สามารถ concat ตรงนี้ได้
                true_range = true_range_df.max(axis=1)
            else:
                true_range = pd.Series(np.nan, index=df_features.index)

            atr = true_range.rolling(window_atr, min_periods=1).mean()
        else:
            atr = pd.Series(np.nan, index=df_features.index) # สร้าง Series ว่างถ้าคอลัมน์ไม่ครบ

        lookback_sr = 50
        if all(col in df_features.columns for col in ['Low', 'High']):
            support = df_features['Low'].rolling(lookback_sr, min_periods=1).min()
            resistance = df_features['High'].rolling(lookback_sr, min_periods=1).max()
        else:
            support = pd.Series(np.nan, index=df_features.index)
            resistance = pd.Series(np.nan, index=df_features.index)

        print("\n✅ ข้อมูล df_features ก่อนทำ indicator_features")
        print(df_features.info())
        print(df_features.head())

        print("\n✅ ตรวจอบข้อมูล จากราคาปิดก่อนหน้า 1 แท่ง")
        print(df_features[["Open","High","Low","Close","Volume","Bar_CL","Bar_CLp"]].head())
        print(df_features[["Open","High","Low","Close","Volume","Bar_CL","Bar_CLp"]].tail())

        # --- สร้าง Features ที่ใช้ Indicators ---
        # สร้าง DataFrame เปล่าสำหรับเก็บ Features ที่คำนวณจาก Indicators
        indicator_features = pd.DataFrame(index=df_features.index)

        print(f"✅ เริ่มคำนวณ MACD Features")
        macd_line_col = 'MACD_12_26_9'
        macd_signal_col = 'MACDs_12_26_9'
        indicator_features["MACD_12_26_9"] = macd[macd_line_col]
        indicator_features["MACDs_12_26_9"] = macd[macd_signal_col]
        indicator_features["MACDh_12_26_9"] = macd[macd_line_col] - macd[macd_signal_col]

        if macd_line_col in macd.columns:
            indicator_features["MACD_line"] = (macd[macd_line_col] > 0.0).astype(int) - (macd[macd_line_col] < 0.0).astype(int) # Convert to 1, 0, -1
        if macd_line_col in macd.columns and not macd[macd_line_col].shift(1).isnull().all():
            indicator_features["MACD_deep"] = (macd[macd_line_col] > macd[macd_line_col].shift(1)).astype(int) - (macd[macd_line_col] < macd[macd_line_col].shift(1)).astype(int)
        if macd_line_col in macd.columns and macd_signal_col in macd.columns:
            indicator_features["MACD_signal"] = (macd[macd_line_col] > macd[macd_signal_col]).astype(int) - (macd[macd_line_col] < macd[macd_signal_col]).astype(int)

        print(f"✅ เริ่มคำนวณ RSI Features")
        if 'RSI14' in df_features.columns: # RSI ถูกคำนวณไว้ใน df_features ตรงๆ ก่อนหน้านี้
            indicator_features["RSI_signal"] = np.select(
                [df_features["RSI14"] < 30, df_features["RSI14"] > 70],
                [-1, 1],
                default=0
            )

            # RSI_Overbought / Oversold
            # การใช้ np.select แบบนี้สำหรับเงื่อนไขเดียวอาจทำให้สับสน
            # indicator_features['RSI_Overbought'] = np.select((df_features['RSI14'] > 70).astype(int), 1, default = 0) # <-- โค้ดเดิม
            # indicator_features['RSI_Oversold'] = np.select((df_features['RSI14'] < 30).astype(int), 1, default = 0) # <-- โค้ดเดิม
            # แก้ไขเป็นวิธีที่ชัดเจนกว่า
            indicator_features['RSI_Overbought'] = (df_features['RSI14'] > 70).astype(int)
            indicator_features['RSI_Oversold'] = (df_features['RSI14'] < 30).astype(int)

            # RSI_ROC
            divisor = df_features['RSI14'] + 1e-10
            indicator_features['RSI_ROC_i2'] = (df_features['RSI14'] - df_features['RSI14'].shift(2)) / divisor
            indicator_features['RSI_ROC_i4'] = (df_features['RSI14'] - df_features['RSI14'].shift(4)) / divisor
            indicator_features['RSI_ROC_i6'] = (df_features['RSI14'] - df_features['RSI14'].shift(6)) / divisor
            indicator_features['RSI_ROC_i8'] = (df_features['RSI14'] - df_features['RSI14'].shift(8)) / divisor

            # RSI_Divergence
            # เงื่อนไข: ตรวจสอบว่า Close.shift(X) และ RSI14.shift(X) ไม่เป็น NaN ก่อนคำนวณ Divergence
            # แก้ไข: ชื่อคอลัมน์ df_features['RSI'] เป็น df_features['RSI14']
            # แก้ไข: ใน else block ให้กำหนดค่าเริ่มต้น (เช่น 0 หรือ np.nan)

            close_shift_2 = df_features['Close'].shift(2)
            rsi14_shift_2 = df_features['RSI14'].shift(2)
            # ใช้ np.isnan().any() เพื่อตรวจสอบ NaN ใน Series ที่ได้จากการ shift (แม้ว่า .isnull().all() ก็ใช้ได้ แต่ .any() อาจชัดเจนกว่าในบริบทนี้)
            if not (pd.isna(close_shift_2).any() or pd.isna(rsi14_shift_2).any()): # ตรวจสอบว่าไม่มี NaN ในแถวที่จะเปรียบเทียบ
                indicator_features['RSI_Divergence_i2'] = np.where(
                    (df_features['Close'] > close_shift_2) & (df_features['RSI14'] < rsi14_shift_2), # แก้ไข: ใช้ rsi14_shift_2
                        1, np.where(
                            (df_features['Close'] < close_shift_2) & (df_features['RSI14'] > rsi14_shift_2), # แก้ไข: ใช้ rsi14_shift_2
                            -1, 0
                    )
                )
            else:
                indicator_features['RSI_Divergence_i2'] = 0 # หรือ np.nan ขึ้นอยู่กับความต้องการ

            close_shift_4 = df_features['Close'].shift(4)
            rsi14_shift_4 = df_features['RSI14'].shift(4)
            if not (pd.isna(close_shift_4).any() or pd.isna(rsi14_shift_4).any()):
                indicator_features['RSI_Divergence_i4'] = np.where(
                    (df_features['Close'] > close_shift_4) & (df_features['RSI14'] < rsi14_shift_4),
                        1, np.where(
                            (df_features['Close'] < close_shift_4) & (df_features['RSI14'] > rsi14_shift_4),
                            -1, 0
                    )
                )
            else:
                indicator_features['RSI_Divergence_i4'] = 0

            close_shift_6 = df_features['Close'].shift(6)
            rsi14_shift_6 = df_features['RSI14'].shift(6)
            if not (pd.isna(close_shift_6).any() or pd.isna(rsi14_shift_6).any()):
                indicator_features['RSI_Divergence_i4'] = np.where(
                    (df_features['Close'] > close_shift_6) & (df_features['RSI14'] < rsi14_shift_6),
                        1, np.where(
                            (df_features['Close'] < close_shift_6) & (df_features['RSI14'] > rsi14_shift_6),
                            -1, 0
                    )
                )
            else:
                indicator_features['RSI_Divergence_i4'] = 0

        # Stochastic Features
        print(f"✅ เริ่มคำนวณ STO")
        stoch_k_col = 'STOCHk_14_3_3'
        stoch_d_col = 'STOCHd_14_3_3'

        if stoch_k_col in stoch.columns and stoch_d_col in stoch.columns:
            indicator_features["STO_cross"] = (stoch[stoch_k_col] > stoch[stoch_d_col]).astype(int) - (stoch[stoch_k_col] < stoch[stoch_d_col]).astype(int)
            indicator_features["STO_zone"] = (stoch[stoch_k_col] > 50).astype(int) - (stoch[stoch_k_col] < 50).astype(int)
            indicator_features["STO_overbought"] = (stoch[stoch_k_col] > 80).astype(int)
            indicator_features["STO_Oversold"] = (stoch[stoch_k_col] < 20).astype(int)

        print(f"✅ เริ่มคำนวณ ADX")
        adx_col = 'ADX_14'
        dmp_col = 'DMP_14'
        dmn_col = 'DMN_14'

        indicator_features["ADX_14"] = adx[adx_col]
        indicator_features["DMP_14"] = adx[dmp_col]
        indicator_features["DMN_14"] = adx[dmn_col]

        indicator_features["ADX_zone"] = adx[adx_col]

        indicator_features["ADX_Deep"] = (indicator_features["ADX_14"] > indicator_features["ADX_14"].shift(1)).astype(int) - (indicator_features["ADX_14"] < indicator_features["ADX_14"].shift(1)).astype(int)

        indicator_features["ADX_zone_25"] = (indicator_features["ADX_14"] > 25).astype(int)
        indicator_features["ADX_zone_15"] = (indicator_features["ADX_14"] > 15).astype(int)

        if dmp_col in adx.columns and dmn_col in adx.columns:
            indicator_features["ADX_cross"] = (adx[dmp_col] > adx[dmn_col]).astype(int) - (adx[dmp_col] < adx[dmn_col]).astype(int)

        # ATR Feature
        print(f"✅ เริ่มคำนวณ ATR")
        indicator_features['ATR'] = atr # ATR Series ที่คำนวณไว้ก่อนหน้า

        indicator_features['ATR_Deep'] = (indicator_features["ATR"] > indicator_features["ATR"].shift(1)).astype(int) - (indicator_features["ATR"] < indicator_features["ATR"].shift(1)).astype(int)

        indicator_features['ATR_ROC_i2'] = (indicator_features['ATR'] - indicator_features['ATR'].shift(2)) / (indicator_features['ATR'] + 1e-10)
        indicator_features['ATR_ROC_i4'] = (indicator_features['ATR'] - indicator_features['ATR'].shift(4)) / (indicator_features['ATR'] + 1e-10)
        indicator_features['ATR_ROC_i6'] = (indicator_features['ATR'] - indicator_features['ATR'].shift(6)) / (indicator_features['ATR'] + 1e-10)
        indicator_features['ATR_ROC_i8'] = (indicator_features['ATR'] - indicator_features['ATR'].shift(8)) / (indicator_features['ATR'] + 1e-10)

        # BB Width Feature
        print(f"✅ เริ่มคำนวณ BB")
        indicator_features['BB_width'] = bb_width # BB_width Series ที่คำนวณไว้ก่อนหน้า

        # SR Features
        print(f"✅ เริ่มคำนวณ S and R")
        indicator_features['Support'] = support # 'Low'
        indicator_features['Resistance'] = resistance # 'High'

        indicator_features['PullBack_Up'] = (df_features['Close'] - indicator_features['Support']) / (indicator_features['Resistance'] - indicator_features['Support'] + 1e-10)
        indicator_features['PullBack_Down'] = (indicator_features['Resistance'] - df_features['Close']) / (indicator_features['Resistance'] - indicator_features['Support'] + 1e-10)

        print("\n✅ ข้อมูล indicator_features ก่อนทำ Lag Features")
        print(indicator_features.info())
        print(indicator_features.head())

        # --- กำหนด Lag periods ที่ต้องการ ---
        print(f"✅ เริ่มคำนวณ Lag Features")
        # ใช้ timeframe_enum ที่รับมาตรงๆ
        if timeframe >= mt5.TIMEFRAME_H4: # 240 เป็นค่า Enum สำหรับ H4
            lags = [1, 2, 3, 5, 10]
        else: # รวมถึง H1 (Enum 60) และ Timeframe ที่เล็กกว่า
            lags = [1, 2, 3, 5, 10, 15, 20, 30, 50]

        # --- สร้าง Lag Features (ปรับปรุงเพื่อ Performance) ---
        # สร้าง DataFrame เปล่าสำหรับเก็บ Lag Features
        lag_features = pd.DataFrame(index=df_features.index)

        # Lag Features สำหรับคอลัมน์ราคา/Volume
        price_cols_upper = ['Close', 'Open', 'High', 'Low', 'Volume']
        for col in price_cols_upper:
            if col in df_features.columns:
                for lag in lags:
                    lag_features[f'{col}_Lag_{lag}'] = df_features[col].shift(lag)
            else:
                print(f"Warning: Price column '{col}' not found in df_features for Lag Features.")

        # Lag Features สำหรับ Indicators
        # ใช้ชื่อคอลัมน์ Indicators หลักที่คำนวณได้ก่อนหน้านี้ (ไม่ใช่ Features ที่สร้างจาก Indicators)
        indicator_cols_for_lag = [
            'RSI14',
            'EMA50', 'EMA100', 'EMA200',
            'ATR', 'BB_width', # Indicators ที่คำนวณตรงๆ
            # Indicators ที่ได้จาก pandas_ta (ต้องตรวจสอบว่าคอลัมน์มีอยู่)
            macd_line_col, macd_signal_col, # MACD Lines
            stoch_k_col, stoch_d_col, # Stoch Lines
            adx_col, dmp_col, dmn_col # ADX Lines
        ]

        # กรองเฉพาะคอลัมน์ที่มีอยู่จริงใน df_features หรือ DataFrame indicator_features
        # หรือใน Series ที่คำนวณ Indicators หลัก
        existing_indicator_cols_for_lag = [
            'RSI14' if 'RSI14' in df_features.columns else None,
            'EMA50' if 'EMA50' in df_features.columns else None,
            'EMA100' if 'EMA100' in df_features.columns else None,
            'EMA200' if 'EMA200' in df_features.columns else None,
            'ATR' if 'ATR' in indicator_features.columns else None, # หรือ atr.name ถ้า atr เป็น Series
            'BB_width' if 'BB_width' in indicator_features.columns else None, # หรือ bb_width.name
            macd_line_col if macd_line_col in macd.columns else None,
            macd_signal_col if macd_signal_col in macd.columns else None,
            stoch_k_col if stoch_k_col in stoch.columns else None,
            stoch_d_col if stoch_d_col in stoch.columns else None,
            adx_col if adx_col in adx.columns else None,
            dmp_col if dmp_col in adx.columns else None,
            dmn_col if dmn_col in adx.columns else None
        ]
        existing_indicator_cols_for_lag = [col for col in existing_indicator_cols_for_lag if col is not None] # กรอง None ออก

        # รวม df_features และ indicator_features (และ Series indicators หลัก) ชั่วคราวเพื่อทำ Lag
        # หรือเข้าถึง Series indicators หลักโดยตรง
        temp_df_for_lag = pd.concat([df_features[['Close', 'Open', 'High', 'Low', 'Volume', 'RSI14', 'EMA50', 'EMA100', 'EMA200']],
                                    macd, stoch, adx, atr.rename('ATR'), bb_width.rename('BB_width'), support.rename('Support'), resistance.rename('Resistance')], axis=1)

        for indicator in existing_indicator_cols_for_lag:
            # ตรวจสอบว่าคอลัมน์ Indicator มีอยู่จริงใน temp_df_for_lag
            if indicator in temp_df_for_lag.columns:
                for lag in [1, 2, 3, 5]: # ใช้ lag periods ที่ใช้กับ indicators
                    lag_features[f'{indicator}_Lag_{lag}'] = temp_df_for_lag[indicator].shift(lag)
            else:
                print(f"Warning: Indicator column '{indicator}' not found in combined data for Lag Features.")

        # --- สร้าง Features แบบเปลี่ยนแปลงจาก Lag (Returns, Changes) (ปรับปรุงเพื่อ Performance) ---
        # สร้าง DataFrame เปล่าสำหรับเก็บ Features Returns/Changes
        returns_changes_features = pd.DataFrame(index=df_features.index)

        for lag in [1, 2, 3, 5]: # ใช้ lag periods ที่ใช้กับ returns/changes
            if 'Close' in df_features.columns:
                returns_changes_features[f'Close_Return_{lag}'] = df_features['Close'].pct_change(lag)
            else:
                print(f"Warning: 'Close' column not found for Close_Return_{lag}.")
                returns_changes_features[f'Close_Return_{lag}'] = np.nan

            if 'Volume' in df_features.columns:
                returns_changes_features[f'Volume_Change_{lag}'] = df_features['Volume'].diff(lag) / (df_features['Volume'].shift(lag) + 1e-10)
            else:
                print(f"Warning: 'Volume' column not found for Volume_Change_{lag}.")
                returns_changes_features[f'Volume_Change_{lag}'] = np.nan

        # --- สร้าง Rolling Features (ปรับปรุงเพื่อ Performance) ---

        # สร้าง DataFrame เปล่าสำหรับเก็บ Rolling Features
        rolling_features = pd.DataFrame(index=df_features.index)

        for window in [3, 5, 10, 20]: # ใช้ window sizes ที่ใช้กับ rolling features
            if 'Close' in df_features.columns:
                rolling_features[f'Close_MA_{window}'] = df_features['Close'].rolling(window, min_periods=1).mean().shift(1)
                rolling_features[f'Close_Std_{window}'] = df_features['Close'].rolling(window, min_periods=1).std().shift(1)
            else:
                print(f"Warning: 'Close' column not found for Close Rolling Features (window {window}).")
                rolling_features[f'Close_MA_{window}'] = np.nan
                rolling_features[f'Close_Std_{window}'] = np.nan

            if 'Volume' in df_features.columns:
                rolling_features[f'Volume_MA_{window}'] = df_features['Volume'].rolling(window, min_periods=1).mean().shift(1)
            else:
                print(f"Warning: 'Volume' column not found for Volume_MA_{window}.")
                rolling_features[f'Volume_MA_{window}'] = np.nan

        # --- รวม Features ที่สร้างขึ้นทั้งหมดเข้าด้วยกัน ---
        # ใช้ pd.concat(axis=1) ครั้งเดียว เพื่อ Performance
        # รวม df_features (ที่มี price, time features, basic price action, EMA, Volume_MA20)
        # indicator_features (Features จาก Indicators หลัก)
        # lag_features (Lag ของ Price และ Indicators หลัก)
        # returns_changes_features (Returns และ Changes)
        # rolling_features (Rolling MA/Std)
        df_features_combined = pd.concat([df_features[['DateTime','Open', 'High', 'Low', 'Close', 'Volume', # ราคาเดิม
                                                    'Entry_DayOfWeek', 'Entry_Hour', # Features เวลา
                                                    'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', # ช่วงเวลา
                                                    'Bar_CLp', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', # Price Action
                                                    'Price_Range', 'Price_Move', 'Price_Strangth', # Price Movement
                                                    'Volume_MA20', 'Volume_Spike', # Volume Features
                                                    'EMA50', 'EMA100', 'EMA200', 'EMA_diff', 'MA_Cross', 'Price_above_EMA50', # EMA Features
                                                    'Rolling_Vol_5', 'Rolling_Vol_15', # Volatility
                                                    'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', # Distance to EMA
                                                    'RSI14' # RSI Calculation
                                                    ]], # เลือกคอลัมน์เดิมที่ยังต้องการเก็บไว้ก่อน concat
                                                indicator_features, # Features จาก Indicators หลัก (ที่สร้างไว้ใน indicator_features DF)
                                                lag_features, # Lag Features
                                                returns_changes_features, # Returns/Changes Features
                                                rolling_features # Rolling Features
                                            ], axis=1)

        # ตรวจสอบข้อมูลหลังสร้าง Features
        print(f"\n✅ ข้อมูล df_features_combined หลังสร้าง Features:")
        print(df_features_combined.info())
        print(df_features_combined.head())

        # --- ลบแถวที่มีค่า NaN ที่เกิดจากการสร้าง Indicators และ Features ---
        initial_rows = len(df_features_combined)
        if not df_features_combined.empty:
            df_features = df_features_combined.dropna() # ตอนนี้ df_features จะเป็น DF ที่รวม features แล้ว
            print(f"✅ สร้าง Indicators และ Features เรียบร้อย (ลบแถวที่ขาดหายไป {initial_rows - len(df_features)} จาก {initial_rows} แถว)")
        else:
            print(f"Warning: df_features_combined is empty before dropna.")
            return

        if df_features.empty:
            print(f"Warning: No data left in df_features after dropna.")
            return

        print(f"\n✅ ข้อมูล df_features หลังสร้างรวม df_features_combined")
        print(df_features.info())
        print(df_features.columns.tolist())
        print(df_features.head())
        print(df_features.tail())

        # --- 2. โหลดโมเดล, Scaler, และ Feature List ---
        # model, scaler, features_list = None, None, None
        model, scaler, features_list = load_model_components(symbol, timeframe)

        if model is None or scaler is None or features_list is None:
            print(f"[{datetime.datetime.now()}] Failed to load model components for {symbol} ({timeframe}). Cannot make prediction.")
            return

        # --- 3. เตรียม Feature Input สำหรับโมเดล และ Features สำหรับตัดสินใจ ---
        try:
            if df_features.empty: # ตรวจสอบอีกครั้ง (แม้จะเช็คข้างบนแล้ว)
                print(f"Warning: df_features is empty when attempting to select features.")
                return

            if not isinstance(features_list, (list, tuple)) or not features_list:
                    print(f"Error: Loaded features_list is invalid or empty: {features_list}")
                    return

            missing_cols = [col for col in features_list if col not in df_features.columns]
            if missing_cols:
                print(f"Error: Missing required feature columns in df_features for prediction: {missing_cols}")
                print(f"Available columns: {df_features.columns.tolist()}")
                print(f"Expected features according to features_list: {features_list}")
                return

            # *** แก้ไขตรงนี้: แยก DataFrame สำหรับทำนาย และ Dictionary สำหรับตัดสินใจ ***

            # 1. DataFrame สำหรับทำนาย (Predict) - ใช้ข้อมูลของแท่ง **ก่อนสุดท้าย** และ subset ด้วย features_list
            # .iloc[-2:-1] เลือกแถวที่ 2 จากท้าย (ตำแหน่ง -2) ถึงแถวที่ 1 จากท้าย (ไม่รวมตำแหน่ง -1)
            # ผลลัพธ์คือ DataFrame ที่มี 1 แถว
            prediction_features_df = df_features.iloc[-2:-1][features_list]

            # 2. DataFrame/Dictionary สำหรับตัดสินใจ (Decision) - ใช้ข้อมูลของแท่ง **สุดท้าย** (ล่าสุด) ทุกคอลัมน์
            # .tail(1) เลือกแถวสุดท้าย
            latest_bar_for_decision_df = df_features.tail(1)
            # สร้าง Dictionary จากแถวสุดท้ายนี้
            latest_features_dict = latest_bar_for_decision_df.iloc[0].to_dict() # ใช้ .iloc[0] เพราะ latest_bar_for_decision_df มีแถวเดียว

            # ดึง Timestamp ของแท่งล่าสุด (สำหรับบันทึกใน latest_signals_data)
            latest_bar_dt = latest_bar_for_decision_df.index[0]

        except KeyError as e:
            print(f"[{datetime.datetime.now()}] Error selecting features for {symbol} ({timeframe}): Missing feature - {e}") # ใช้ timeframe_enum
            print(f"Available columns after indicator calculation: {df_features.columns.tolist()}")
            # ในกรณีนี้ Error Missing Feature อาจมาจาก features_list ที่ใช้สร้าง prediction_features_df
            print(f"Expected prediction features according to features_list: {features_list}")
            traceback.print_exc()
            return

        except Exception as e:
            print(f"[{datetime.datetime.now()}] Error preparing features for {symbol} ({timeframe}): {e}") # ใช้ timeframe_enum
            traceback.print_exc()
            return

        # --- Scale Feature Input สำหรับโมเดล (ใช้ prediction_features_df) ---
        # features_array = latest_bar_features_df.values # <-- โค้ดเดิม
        features_array = prediction_features_df.values # <--- ใช้ DataFrame สำหรับทำนาย
        scaled_features_array = scaler.transform(features_array) # ได้ numpy array

        # *** ส่วนที่เพิ่มเพื่อแปลง scaled numpy array กลับเป็น DataFrame พร้อม feature names ***
        # สร้าง DataFrame จาก numpy array โดยใช้ features_list เป็นชื่อคอลัมน์ และ Index จาก prediction_features_df
        # scaled_features_df = pd.DataFrame(scaled_features_array, columns=features_list, index=latest_bar_features_df.index) # <-- โค้ดเดิม
        scaled_features_df = pd.DataFrame(scaled_features_array, columns=features_list, index=prediction_features_df.index) # <--- ใช้ Index จาก DataFrame สำหรับทำนาย
        # *** จบส่วนที่เพิ่ม ***

        print(f"\n✅ แสดงข้อมูลที่ model ใช้ทำนาย (แท่งก่อนสุดท้าย)") # แก้ไขข้อความ Print
        print(f"prediction features df (len {len(scaled_features_df)}) :")
        print(scaled_features_df)

        # ใช้ scaled_features_df (DataFrame ที่มี Feature Names) ในการทำนาย
        prediction_proba = model.predict_proba(scaled_features_df)[:, 1] # Probability of class 1 (TP Hit)
        probability_tp_hit = prediction_proba[0] # เพราะ prediction_features_df มีแถวเดียว

        # --- 5. ตัดสินใจ Signal ---
        signal = "HOLD" # Default

        rsi_level = 40

        if probability_tp_hit >= model_confidence_threshold:

            # ดึงข้อมูลแถวสุดท้าย (ล่าสุด) ที่มีค่า Feature ครบถ้วน
            latest_bar_features_df_all_i2 = df_features.tail(2) # ดึงทั้งหมดที่มีใน df_features
            latest_features_dict_all_i2 = latest_bar_features_df_all_i2.iloc[0].to_dict()
            # print(f"\n✅ latest features dict (dictionary): {latest_features_dict_all_i2}") # แสดง Feature ที่เลือกใช้ (จะเห็นเป็น dictionary แล้ว)

            # ดึงข้อมูลเพื่อตรวจสอบ : ก่อนการซื้อ-ขาย
            print(f"\n✅ ทดสอบข้อมูลแท่ก่อนหน้า 1 แท่ง : latest_features_dict_all_i2 (tail(2))")
            if 'DateTime' in latest_features_dict_all_i2:    print(f"time {latest_features_dict_all_i2['DateTime']}")
            if 'Entry_Hour' in latest_features_dict_all_i2:  print(f"entry hour {latest_features_dict_all_i2['Entry_Hour']}")
            if 'Open' in latest_features_dict_all_i2:        print(f"op {latest_features_dict_all_i2['Open']}")
            if 'High' in latest_features_dict_all_i2:        print(f"hh {latest_features_dict_all_i2['High']}")
            if 'Low' in latest_features_dict_all_i2:         print(f"ll {latest_features_dict_all_i2['Low']}")
            if 'Close' in latest_features_dict_all_i2:       print(f"cl {latest_features_dict_all_i2['Close']}")
            if 'Bar_CLp' in latest_features_dict_all_i2:     print(f"clp {latest_features_dict_all_i2['Bar_CLp']}")
            if 'Volume' in latest_features_dict_all_i2:      print(f"vol {latest_features_dict_all_i2['Volume']}")

                        # ดึงข้อมูลแถวสุดท้าย (ล่าสุด) ที่มีค่า Feature ครบถ้วน
            latest_bar_features_df_all_i1 = df_features.tail(1) # ดึงทั้งหมดที่มีใน df_features
            latest_features_dict_all_i1 = latest_bar_features_df_all_i1.iloc[0].to_dict()
            # print(f"\n✅ latest features dict (dictionary): {latest_features_dict_all_i1}") # แสดง Feature ที่เลือกใช้ (จะเห็นเป็น dictionary แล้ว)

            # ดึงข้อมูลเพื่อตรวจสอบ : ก่อนการซื้อ-ขาย
            print(f"\n✅ ทดสอบข้อมูลแท่ปัจจุบัน : latest_features_dict_all_i1 (tail(1))")
            if 'DateTime' in latest_features_dict_all_i1:    print(f"time {latest_features_dict_all_i1['DateTime']}")
            if 'Entry_Hour' in latest_features_dict_all_i1:  print(f"entry hour {latest_features_dict_all_i1['Entry_Hour']}")
            if 'Open' in latest_features_dict_all_i1:        print(f"op {latest_features_dict_all_i1['Open']}")
            if 'High' in latest_features_dict_all_i1:        print(f"hh {latest_features_dict_all_i1['High']}")
            if 'Low' in latest_features_dict_all_i1:         print(f"ll {latest_features_dict_all_i1['Low']}")
            if 'Close' in latest_features_dict_all_i1:       print(f"cl {latest_features_dict_all_i1['Close']}")
            if 'Bar_CLp' in latest_features_dict_all_i1:     print(f"clp {latest_features_dict_all_i1['Bar_CLp']}")
            if 'Volume' in latest_features_dict_all_i1:      print(f"vol {latest_features_dict_all_i1['Volume']}")

            time_condition = (6 <= latest_features_dict_all_i1['Entry_Hour'] <= 20)

            tech_signal_buy = (
                            (latest_features_dict_all_i2['Close'] > latest_features_dict_all_i2['Open']) and
                            (latest_features_dict_all_i2['Close'] > latest_features_dict_all_i2['EMA200']) and
                            (latest_features_dict_all_i2['MACD_signal'] == 1.0) and
                            (latest_features_dict_all_i2['RSI_signal'] > rsi_level) and
                            (latest_features_dict_all_i2['STO_cross'] == 1.0) and
                            (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80)
                        )
            
            tech_signal_sell = (
                            (latest_features_dict_all_i2['Close'] < latest_features_dict_all_i2['Open']) and
                            (latest_features_dict_all_i2['Close'] < latest_features_dict_all_i2['EMA200']) and
                            (latest_features_dict_all_i2['MACD_signal'] == -1.0) and
                            (latest_features_dict_all_i2['RSI_signal'] < (100-rsi_level)) and
                            (latest_features_dict_all_i2['STO_cross'] == -1.0) and
                            (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80)
                        )

            if time_condition:
                if tech_signal_buy:    signal = "BUY"
                elif tech_signal_sell: signal = "SELL"

            print(f"\n[{datetime.datetime.now()}] Model confidence ({probability_tp_hit:.4f}) >= {model_confidence_threshold:.2f}. Generated Signal: {signal}")
        # else: signal = "HOLD"

        # --- เพิ่ม: Print Signal และ Confidence Probability ที่คำนวณได้ ---
        print(f"[{datetime.datetime.now()}] Prediction Result for {symbol} ({timeframe}):")
        print(f"  Signal: {signal}")
        print(f"  Confidence (Probability TP Hit): {probability_tp_hit:.4f}")
        print("------------------------------------------------------------")
        # --- จบส่วน Print ---

        # --- ส่วนส่ง Signal หรือข้อมูลกลับไปที่ MT5 (ต้องเพิ่ม Logic จริงๆ ที่นี่) ---
        # ... (ไม่ได้เพิ่ม Logic ส่งกลับในโค้ดนี้) ...

        # --- ส่วนที่เพิ่ม: Store the calculated signal and confidence ---
        # Store the signal and confidence in the global dictionary
        with signals_lock: # <--- บรรทัดนี้เริ่มต้นส่วนที่บันทึก Signal โดยใช้ Lock
            signals_key = (symbol, timeframe) # ใช้ symbol และ timeframe เป็น key
            latest_signals_data[signals_key] = { # <--- บรรทัดนี้บันทึกข้อมูลลง Global Variable
                "symbol": symbol,
                "timeframe": timeframe, # Store enum
                "timeframe_str": timeframe_map.get(timeframe, 'UNKNOWN'), # Store string for easy reading
                "signal": signal,
                "confidence": float(probability_tp_hit), # Ensure it's a standard float
                "timestamp": latest_bar_dt if 'latest_bar_dt' in locals() else datetime.datetime.now(MT5_TIMEZONE) # Use bar time if available, otherwise current time
            }
        print(f"[{datetime.datetime.now()}] Stored latest signal for {symbol} (enum: {timeframe}): {signal} ({probability_tp_hit:.4f})") # <--- บรรทัดนี้ Print ยืนยันการบันทึก

    # ... (โค้ดส่วนจัดการ Error ใน process_data_and_trade - ควรมี Logic การบันทึก Error Signal ด้วย) ...
    except Exception as e:
        # ... (Print Error) ...
        signal = "ERROR"
        probability_tp_hit = 0.0
        # Store ERROR signal in case of failure
        with signals_lock:
            signals_key = (symbol, timeframe)
            latest_signals_data[signals_key] = {
            "symbol": symbol,
            "timeframe": timeframe,
            "timeframe_str": timeframe_map.get(timeframe, 'UNKNOWN'),
            "signal": signal,
            "confidence": float(probability_tp_hit),
            "timestamp": latest_bar_dt if 'latest_bar_dt' in locals() else datetime.datetime.now(MT5_TIMEZONE)
            }
        print(f"[{datetime.datetime.now()}] Stored latest signal for {symbol} (enum: {timeframe}) with ERROR status.")
        print(f"⚠️ เกิดข้อผิดพลาด : {str(e)}")

    print(f"[{datetime.datetime.now()}] Finished processing for {symbol} ({timeframe}).")

# --- HTTP Flask Route to receive data ---
@app.route('/data', methods=['POST'])
def receive_data():
    """Receives JSON data (batch of bars) from MT5 EA via HTTP POST.
        Processes the data and returns the latest processed signal if available.
    """

    try:
        # --- เปลี่ยนการรับข้อมูล: คาดหวัง JSON Object ที่มี key เป็น symbol, timeframe_str, และ bars (เป็น Array) ---
        data = request.get_json(force=True, silent=False)

        # ดึงข้อมูลพื้นฐาน
        symbol = data.get('symbol')
        timeframe_str = data.get('timeframe_str')
        bar_time_ts = data.get('time')
        bar_open = data.get('open')
        bar_high = data.get('high')
        bar_low = data.get('low')
        bar_close = data.get('close')
        bar_volume = data.get('tick_volume')
        bars_list = data.get('bars') # <--- ดึง Array ของบาร์ออกมา

        # ตรวจสอบข้อมูลที่ได้รับ
        if not all([symbol, timeframe_str, bars_list]) or not isinstance(bars_list, list) or len(bars_list) == 0:
            print("Received incomplete or incorrectly formatted data.")
            print(f"Received data: {data}")
            return jsonify({"status": "ERROR", "message": "Incomplete or incorrectly formatted data received."}), 400

        # --- แปลง timeframe_str เป็น enum ---
        timeframe_int = timeframe_code_map.get(timeframe_str) # timeframe str PERIOD_H1 enum 60
        if timeframe_int is None:
            print(f"Invalid or unknown timeframe string received: {timeframe_str}")
            return jsonify({"status": "ERROR", "message": f"Invalid or unknown timeframe {timeframe_str}"}), 400

        # --- สร้าง DataFrame จาก List ของ Bar Objects ---
        try:
            # แปลง list ของ dict เป็น DataFrame
            df_batch = pd.DataFrame(bars_list)

            # ตรวจสอบว่าคอลัมน์ที่จำเป็นมีครบ
            required_cols = ['time', 'open', 'high', 'low', 'close', 'tick_volume']
            if not all(col in df_batch.columns for col in required_cols):
                print(f"Missing required columns in received bar data: {[col for col in required_cols if col not in df_batch.columns]}")
                return jsonify({"status": "ERROR", "message": "Missing required bar data columns."}), 400

            # แปลง Timestamp (integer) เป็น Datetime Objects และตั้งเป็น Index
            # ใช้ unit='s' เพื่อระบุว่าเป็น Unix timestamp (วินาที)
            df_batch['time'] = pd.to_datetime(df_batch['time'], unit='s', utc=True).dt.tz_convert(MT5_TIMEZONE) # แปลงเป็น Timezone ของ MT5 Server
            df_batch.set_index('time', inplace=True)

            # *** ส่วนที่เพิ่ม: เรียงลำดับ Index (เวลา) จากน้อยไปมาก (เก่าไปใหม่) ***
            df_batch.sort_index(ascending=True, inplace=True) # <--- เพิ่มบรรทัดนี้

            # เปลี่ยนชื่อคอลัมน์ tick_volume เป็น volume และเปลี่ยนเป็นตัวพิมพ์เล็ก
            df_batch.rename(columns={'tick_volume': 'volume'}, inplace=True)
            df_batch.columns = df_batch.columns.str.lower() # เปลี่ยนชื่อคอลัมน์ราคาเป็นตัวพิมพ์เล็กทั้งหมด

            # ตรวจสอบว่า DataFrame ถูกสร้างขึ้นมาถูกต้อง
            if df_batch.empty:
                print("Received bar list is empty after processing.")
                return jsonify({"status": "ERROR", "message": "Received bar data is empty."}), 400

            # Print ข้อมูลที่ได้รับ Batch แรกและสุดท้ายเพื่อตรวจสอบ (ตอนนี้ head จะเป็นแท่งเก่าสุด, tail จะเป็นแท่งใหม่สุด)
            print(f"[{datetime.datetime.now()}] Received batch of {len(df_batch)} bars for {symbol} {timeframe_str}. Time range: {df_batch.index.min()} to {df_batch.index.max()}")
            print(f"\nข้อมูลดิบที่โหลดและ แปลงเป็นตัวเลข (เรียงตามเวลา):") # เปลี่ยนข้อความ Print
            
            print("\n✅ ข้อมูลดิบที่โหลด : df_batch")
            print(df_batch.info())
            print(df_batch)

        except Exception as e:
            print(f"Error processing received bar list into DataFrame: {e}")
            traceback.print_exc()
            return jsonify({"status": "ERROR", "message": f"Error processing bar data: {e}"}), 400


        # --- อัปเดต market_data_store ด้วย Batch Data ---
        cleaned_symbol = symbol.replace('#', '')
        key = (cleaned_symbol, timeframe_int)

        with data_lock:
            if key not in market_data_store:
                print(f"\n[{datetime.datetime.now()}] Initializing data store for {cleaned_symbol} ({timeframe_str})")
                # สร้าง DF เปล่าด้วยคอลัมน์ตัวพิมพ์เล็กตามที่ MQL5 ส่งมา
                market_data_store[key] = pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume'])
                market_data_store[key].index.name = 'time'

            # *** วิธีที่ 1 (แนะนำสำหรับตอนนี้): แทนที่ข้อมูลเดิมด้วย Batch ใหม่ทั้งหมด ***
            # วิธีนี้ง่ายที่สุดในการเริ่มต้นและทำให้แน่ใจว่ามีข้อมูลล่าสุด
            # ข้อเสีย: ถ้า Batch ไม่ครบถ้วนตามที่ Python ต้องการ จะทำให้คำนวณ Indicator ไม่ได้
            # คุณต้องแน่ใจว่า MQL5 ส่งข้อมูลย้อนหลังครบจำนวน required_bars เสมอ
            required_bars = 205 # *** ต้องตรงกับ BARS_FOR_PYTHON ใน MQL5 ***
            if len(df_batch) < required_bars:
                print(f"Warning: Received batch size ({len(df_batch)}) is less than required ({required_bars}). Processing with available data.")
                # อาจจะตัดสินใจไม่ประมวลผล หรือ ประมวลผลเท่าที่มีก็ได้ ขึ้นอยู่กับ Logic
                # ถ้าไม่ประมวลผล ให้ return ไปเลย
                # return jsonify({"status": "WARNING", "message": f"Received incomplete batch ({len(df_batch)} bars)."}), 200
                # ถ้าประมวลผลต่อ ก็ไปขั้นตอนต่อไป

            # แทนที่ข้อมูลทั้งหมดใน store ด้วย batch ใหม่
            market_data_store[key] = df_batch.copy()
            print(f"[{datetime.datetime.now()}] Replaced data store for {cleaned_symbol} ({timeframe_str}) with {len(market_data_store[key])} bars from batch.")


            # *** วิธีที่ 2 (ซับซ้อนกว่า): รวมข้อมูล Batch เข้ากับข้อมูลเก่าใน store (ถ้ามี) ***
            # วิธีนี้ต้องจัดการกับข้อมูลที่ซ้ำกัน หรือเรียงลำดับข้อมูลให้ถูกต้อง
            # if not market_data_store[key].empty:
            #     # Concatenate และลบข้อมูลที่ซ้ำกัน (เก็บข้อมูลล่าสุด)
            #     combined_df = pd.concat([market_data_store[key], df_batch])
            #     market_data_store[key] = combined_df[~combined_df.index.duplicated(keep='last')].sort_index()
            # else:
            #     market_data_store[key] = df_batch.copy()

            # # จำกัดขนาดข้อมูลใน store
            # market_data_store[key] = market_data_store[key].tail(max(required_bars + 50, 500)) # เก็บอย่างน้อย 500 หรือตาม required_bars + buffer
            # print(f"[{datetime.datetime.now()}] Updated data store for {cleaned_symbol} ({timeframe_str}) with {len(market_data_store[key])} bars.")

        # --- เรียกใช้ฟังก์ชันประมวลผลใน Thread แยก ---
        # ตอนนี้ process_data_and_trade จะใช้ข้อมูลจาก market_data_store ที่เพิ่งอัปเดต
        # หรืออาจจะปรับ process_data_and_trade ให้รับ df_batch เข้ามาตรงๆ ก็ได้
        # แต่เพื่อให้เข้ากับโครงสร้างเดิม เราจะให้มันอ่านจาก store
        print(f"\nก่อนส่งเข้า process data and trade : symbol {cleaned_symbol} timeframe enum {timeframe_int}")
        processing_thread = threading.Thread(target=process_data_and_trade, args=(cleaned_symbol, timeframe_int)) # *** แก้ไข: ใช้ cleaned_symbol ***
        processing_thread.start()
        # *** เพิ่มบรรทัดนี้: รอให้เธรดประมวลผลทำงานเสร็จ ***
        processing_thread.join()
        print(f"\nจบขั้นตอน process data and trade (Main thread waited)") # เปลี่ยนข้อความนิดหน่อย

        # --- ส่วนที่ดึง Signal และ Confidence จาก latest_signals_data และส่งกลับใน Response ---
        # ตอนนี้ เมื่อมาถึงส่วนนี้ การประมวลผลใน processing_thread เสร็จแล้ว
        # latest_signals_data ควรจะมีค่า Signal ล่าสุดของบาร์ที่เพิ่งได้รับ

        response_signal = "HOLD" # Default response
        response_confidence = 0.0
        response_message = "Data received and processing started. Signal processing in progress."
        response_signal_bar_timestamp = None # Timestamp ของแท่งที่เกี่ยวข้องกับ Signal ที่ส่งกลับ

        signals_key = (cleaned_symbol, timeframe_int)

        # ดึงข้อมูล Signal ล่าสุดออกมา โดยใช้ signals_lock
        with signals_lock:
            latest_sig = latest_signals_data.get(signals_key, None)

            if latest_sig:
                response_signal = latest_sig["signal"]
                response_confidence = latest_sig["confidence"]
                if latest_sig["timestamp"]:
                    response_signal_bar_timestamp = latest_sig["timestamp"].timestamp()
                response_message = f"Latest signal: {response_signal} ({response_confidence:.4f}) for bar at {latest_sig['timestamp'].strftime('%Y.%m.%d %H:%M')}"
            # else: ถ้ายังไม่มี Signal ใน latest_signals_data ก็จะใช้ค่า Default

        # Return the JSON response including the latest known signal and confidence
        # MT5 EA จะต้อง Parse JSON response body นี้
        # ใน Response นี้ เราจะส่ง timestamp ของบาร์ล่าสุดที่ได้รับใน Batch กลับไปด้วย
        latest_received_bar_timestamp = df_batch.index.max().timestamp() if not df_batch.empty else None

        # return jsonify({
        #     "status": "OK",
        #     "message": response_message,
        #     "signal": response_signal, # ส่งค่า Signal กลับไปใน Response
        #     "confidence": response_confidence, # ส่งค่า Confidence กลับไปใน Response
        #     "bar_timestamp": latest_received_bar_timestamp, # Timestamp ของบาร์ล่าสุดใน Batch ที่ได้รับ
        #     "signal_bar_timestamp": response_signal_bar_timestamp # Timestamp ของแท่งที่ Signal ที่ส่งกลับใช้คำนวณ
        # }), 200

        # *** ส่วนที่เพิ่ม: สร้าง Dictionary ที่จะถูกแปลงเป็น JSON และ Print ออกมา ***
        response_payload = {
            "status": "OK",
            "message": response_message,
            "signal": response_signal, # ค่า Signal ที่จะส่ง
            "confidence": response_confidence, # ค่า Confidence ที่จะส่ง
            "bar_timestamp": latest_received_bar_timestamp, # Timestamp ของบาร์ล่าสุดใน Batch ที่ได้รับ
            "signal_bar_timestamp": response_signal_bar_timestamp # Timestamp ของแท่งที่ Signal ที่ส่งกลับใช้คำนวณ
        }

        print(f"\n[{datetime.datetime.now()}] Sending JSON Response to MT5: {response_payload}")

        return jsonify(response_payload), 200 # ใช้ response_payload แทน dictionary ที่สร้างตรงๆ

    except BadRequest as e:
        print(f"[{datetime.datetime.now()}] Received data but failed due to Bad Request: {e}")
        print(f"Raw request data (bytes): {request.data}")
        return jsonify({"status": "ERROR", "message": f"Bad Request: {e}"}), 400

    except Exception as e:
        print(f"[{datetime.datetime.now()}] Error processing request: {e}")
        traceback.print_exc()
        return jsonify({"status": "ERROR", "message": f"Server Error: {e}"}), 500

# --- Optional: Endpoint to Get Signal ---
# ... (ไม่ได้เพิ่มในโค้ดนี้) ...

# --- Main Execution ---
if __name__ == "__main__":
    # initialize_mt5() # Uncomment ถ้า Python ต้องใช้ mt5.py ในการส่งคำสั่งเทรดเอง

    print(f"Starting HTTP Server on http://{HTTP_HOST}:{HTTP_PORT}")
    try:
        app.run(host=HTTP_HOST, port=HTTP_PORT, debug=False)
    except Exception as e:
        print(f"Failed to start Flask server: {e}")
        traceback.print_exc()

    print("Server stopped.")
    # mt5.shutdown() # Uncomment ถ้ามีการ initialize_mt5
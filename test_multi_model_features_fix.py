#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Multi-Model Features Fix
ทดสอบการแก้ไขปัญหาการโหลด features ใน Multi-Model Architecture
"""

import os
import sys
import pickle
import pandas as pd
import numpy as np

# เพิ่ม path สำหรับ import
sys.path.append('.')

def create_test_directory_structure():
    """
    สร้างโครงสร้างไดเรกทอรีสำหรับการทดสอบ
    """
    print("📁 Creating test directory structure...")
    
    # สร้างโครงสร้างสำหรับ Multi-Model
    multi_model_dirs = [
        "LightGBM_Multi/models/trend_following",
        "LightGBM_Multi/models/counter_trend",
        "LightGBM_Multi/thresholds"
    ]
    
    # สร้างโครงสร้างสำหรับ Single Model
    single_model_dirs = [
        "LightGBM_Single/models/060_GOLD",
        "LightGBM_Single/thresholds"
    ]
    
    all_dirs = multi_model_dirs + single_model_dirs
    
    for dir_path in all_dirs:
        os.makedirs(dir_path, exist_ok=True)
        print(f"   ✅ Created: {dir_path}")
    
    return True

def create_mock_features_files():
    """
    สร้างไฟล์ features จำลองสำหรับการทดสอบ
    """
    print("\n📄 Creating mock features files...")
    
    # สร้าง features list จำลอง
    mock_features = [
        'RSI14', 'MACD_12_26_9', 'EMA50', 'EMA200', 'Volume_MA20',
        'ATR', 'BB_width', 'ADX_14', 'Price_Range', 'Volume_Spike',
        'RSI14_Lag_1', 'RSI14_Lag_2', 'EMA50_Lag_1', 'EMA50_Lag_2',
        'Close_Lag_1', 'Close_Lag_2', 'Volume_Lag_1', 'Volume_Lag_2',
        'RSI14_x_ATR', 'EMA_diff_x_ATR', 'MACD_signal_x_RSI14'
    ]
    
    # สร้างไฟล์สำหรับ Multi-Model Architecture
    multi_model_files = [
        "LightGBM_Multi/models/trend_following/060_GOLD_features.pkl",
        "LightGBM_Multi/models/counter_trend/060_GOLD_features.pkl"
    ]
    
    for file_path in multi_model_files:
        with open(file_path, 'wb') as f:
            pickle.dump(mock_features, f)
        print(f"   ✅ Created: {file_path} ({len(mock_features)} features)")
    
    # สร้างไฟล์สำหรับ Single Model Architecture
    single_model_file = "LightGBM_Single/models/060_GOLD/LightGBM_060_GOLD_features.pkl"
    with open(single_model_file, 'wb') as f:
        pickle.dump(mock_features, f)
    print(f"   ✅ Created: {single_model_file} ({len(mock_features)} features)")
    
    return mock_features

def test_features_loading_logic():
    """
    ทดสอบ logic การโหลด features
    """
    print("\n🧪 Testing features loading logic...")
    
    # Test parameters
    test_folder = "LightGBM_Multi"
    symbol = "GOLD"
    timeframe = 60
    model_name = "LightGBM"
    
    # Test 1: Multi-Model Architecture
    print("\n1. Testing Multi-Model Architecture:")
    USE_MULTI_MODEL_ARCHITECTURE = True
    
    if USE_MULTI_MODEL_ARCHITECTURE:
        scenario_dir = f"{test_folder}/models/trend_following"
        model_features_path = os.path.join(scenario_dir, f"{str(timeframe).zfill(3)}_{symbol}_features.pkl")
        print(f"   🔄 Multi-Model path: {model_features_path}")
    else:
        model_dir = f"{test_folder}/models/{str(timeframe).zfill(3)}_{symbol}"
        model_features_path = os.path.join(model_dir, f"{model_name}_{str(timeframe).zfill(3)}_{symbol}_features.pkl")
        print(f"   📊 Single-Model path: {model_features_path}")
    
    # ตรวจสอบว่าไฟล์มีอยู่หรือไม่
    if os.path.exists(model_features_path):
        try:
            with open(model_features_path, 'rb') as f:
                features = pickle.load(f)
            print(f"   ✅ Successfully loaded {len(features)} features")
            print(f"   📋 First 5 features: {features[:5]}")
            return True
        except Exception as e:
            print(f"   ❌ Error loading features: {e}")
            return False
    else:
        print(f"   ❌ File not found: {model_features_path}")
        return False

def test_single_model_fallback():
    """
    ทดสอบ fallback ไปยัง Single Model
    """
    print("\n2. Testing Single Model fallback:")
    USE_MULTI_MODEL_ARCHITECTURE = False
    
    test_folder = "LightGBM_Single"
    symbol = "GOLD"
    timeframe = 60
    model_name = "LightGBM"
    
    if USE_MULTI_MODEL_ARCHITECTURE:
        scenario_dir = f"{test_folder}/models/trend_following"
        model_features_path = os.path.join(scenario_dir, f"{str(timeframe).zfill(3)}_{symbol}_features.pkl")
        print(f"   🔄 Multi-Model path: {model_features_path}")
    else:
        model_dir = f"{test_folder}/models/{str(timeframe).zfill(3)}_{symbol}"
        model_features_path = os.path.join(model_dir, f"{model_name}_{str(timeframe).zfill(3)}_{symbol}_features.pkl")
        print(f"   📊 Single-Model path: {model_features_path}")
    
    # ตรวจสอบว่าไฟล์มีอยู่หรือไม่
    if os.path.exists(model_features_path):
        try:
            with open(model_features_path, 'rb') as f:
                features = pickle.load(f)
            print(f"   ✅ Successfully loaded {len(features)} features")
            print(f"   📋 First 5 features: {features[:5]}")
            return True
        except Exception as e:
            print(f"   ❌ Error loading features: {e}")
            return False
    else:
        print(f"   ❌ File not found: {model_features_path}")
        return False

def test_file_existence():
    """
    ทดสอบการมีอยู่ของไฟล์
    """
    print("\n📂 Testing file existence:")
    
    files_to_check = [
        "LightGBM_Multi/models/trend_following/060_GOLD_features.pkl",
        "LightGBM_Multi/models/counter_trend/060_GOLD_features.pkl",
        "LightGBM_Single/models/060_GOLD/LightGBM_060_GOLD_features.pkl"
    ]
    
    all_exist = True
    for file_path in files_to_check:
        exists = os.path.exists(file_path)
        print(f"   {file_path}: {'✅' if exists else '❌'}")
        if not exists:
            all_exist = False
    
    return all_exist

def simulate_original_problem():
    """
    จำลองปัญหาเดิมที่เกิดขึ้น
    """
    print("\n🔍 Simulating original problem:")
    
    # สถานการณ์เดิม: ใช้ Multi-Model แต่หา Single Model path
    test_folder = "LightGBM_Multi"
    symbol = "GOLD"
    timeframe = 60
    model_name = "LightGBM"
    
    # Path แบบเดิม (ผิด)
    old_model_dir = f"{test_folder}/models/{str(timeframe).zfill(3)}_{symbol}"
    old_model_features_path = os.path.join(old_model_dir, f"{model_name}_{str(timeframe).zfill(3)}_{symbol}_features.pkl")
    
    print(f"   ❌ Old (incorrect) path: {old_model_features_path}")
    print(f"   📁 Directory exists: {os.path.exists(old_model_dir)}")
    print(f"   📄 File exists: {os.path.exists(old_model_features_path)}")
    
    # Path แบบใหม่ (ถูก)
    new_scenario_dir = f"{test_folder}/models/trend_following"
    new_model_features_path = os.path.join(new_scenario_dir, f"{str(timeframe).zfill(3)}_{symbol}_features.pkl")
    
    print(f"   ✅ New (correct) path: {new_model_features_path}")
    print(f"   📁 Directory exists: {os.path.exists(new_scenario_dir)}")
    print(f"   📄 File exists: {os.path.exists(new_model_features_path)}")
    
    return os.path.exists(new_model_features_path)

def cleanup_test_files():
    """
    ทำความสะอาดไฟล์ทดสอบ
    """
    print("\n🧹 Cleaning up test files...")
    
    import shutil
    
    dirs_to_remove = ["LightGBM_Multi", "LightGBM_Single"]
    
    for dir_path in dirs_to_remove:
        if os.path.exists(dir_path):
            shutil.rmtree(dir_path)
            print(f"   ✅ Removed: {dir_path}")

def run_comprehensive_test():
    """
    รันการทดสอบแบบครอบคลุม
    """
    print("🧪 Multi-Model Features Fix - Comprehensive Test")
    print("=" * 80)
    
    try:
        # 1. สร้างโครงสร้างไดเรกทอรี
        create_test_directory_structure()
        
        # 2. สร้างไฟล์ features จำลอง
        mock_features = create_mock_features_files()
        
        # 3. ทดสอบการมีอยู่ของไฟล์
        files_exist = test_file_existence()
        
        # 4. จำลองปัญหาเดิม
        problem_fixed = simulate_original_problem()
        
        # 5. ทดสอบ logic การโหลด
        multi_model_test = test_features_loading_logic()
        single_model_test = test_single_model_fallback()
        
        # สรุปผลการทดสอบ
        print(f"\n📊 Test Results Summary")
        print("=" * 80)
        print(f"Files Creation: {'✅ PASSED' if files_exist else '❌ FAILED'}")
        print(f"Problem Fixed: {'✅ PASSED' if problem_fixed else '❌ FAILED'}")
        print(f"Multi-Model Loading: {'✅ PASSED' if multi_model_test else '❌ FAILED'}")
        print(f"Single-Model Loading: {'✅ PASSED' if single_model_test else '❌ FAILED'}")
        
        all_passed = all([files_exist, problem_fixed, multi_model_test, single_model_test])
        
        if all_passed:
            print(f"\n🎉 All tests passed! The features loading fix is working correctly.")
            print(f"\n💡 Key Improvements:")
            print(f"   ✅ Multi-Model Architecture now loads features from scenario folders")
            print(f"   ✅ Single Model Architecture still works as before")
            print(f"   ✅ Proper path construction based on USE_MULTI_MODEL_ARCHITECTURE flag")
            print(f"   ✅ Backward compatibility maintained")
        else:
            print(f"\n⚠️ Some tests failed. Please check the implementation.")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # ทำความสะอาด
        cleanup_test_files()

if __name__ == "__main__":
    success = run_comprehensive_test()
    
    print(f"\n📝 Test completed.")
    if success:
        print(f"💡 The Multi-Model features loading fix is ready for production!")
        print(f"🎯 Now the system will correctly load features based on the architecture type.")
    else:
        print(f"⚠️ Please review and fix any remaining issues.")

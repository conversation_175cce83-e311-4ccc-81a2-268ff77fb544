//+------------------------------------------------------------------+
//|                                    MT5_Status_Manager_Fixed.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

//--- Input parameters
input string TelegramBotToken  = "**********************************************";
input string ChatID            = "6546140292";
input int    ReportInterval    = 300;  // รายงานทุก 5 นาที (300 วินาที)
input bool   EnableNewBarReport = true;  // รายงานเมื่อมีแท่งใหม่
input bool   EnableStatusReport = true;  // รายงานสถานะทั่วไป
input bool   EnablePositionChange = true; // รายงานเมื่อมีการเปลี่ยนแปลงออเดอร์

//--- Global variables
string MessageText = "";
datetime LastBarTime = 0;
int LastPositionCount = 0;
double LastTotalProfit = 0.0;
datetime LastReportTime = 0;
bool LastAlgoTradingStatus = false;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("🚀 MT5 Status Manager เริ่มทำงาน");
   
   // เก็บสถานะเริ่มต้น
   LastBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
   LastPositionCount = PositionsTotal();
   LastTotalProfit = GetTotalProfit();
   LastReportTime = TimeCurrent();
   LastAlgoTradingStatus = TerminalInfoInteger(TERMINAL_TRADE_ALLOWED);
   
   // ส่งรายงานเริ่มต้น
   SendInitialReport();
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   MessageText = "🔴 MT5 Status Manager หยุดทำงาน\n";
   MessageText += "เหตุผล: " + GetDeinitReasonText(reason);
   SendTelegram(MessageText);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // ตรวจสอบแท่งใหม่
   if(EnableNewBarReport)
   {
      CheckNewBar();
   }
   
   // ตรวจสอบการเปลี่ยนแปลงออเดอร์
   if(EnablePositionChange)
   {
      CheckPositionChanges();
   }
   
   // รายงานสถานะตามช่วงเวลา
   if(EnableStatusReport)
   {
      CheckPeriodicReport();
   }
}

//+------------------------------------------------------------------+
//| ตรวจสอบแท่งใหม่                                                    |
//+------------------------------------------------------------------+
void CheckNewBar()
{
   datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
   
   if(currentBarTime != LastBarTime && LastBarTime != 0)
   {
      SendNewBarReport();
      LastBarTime = currentBarTime;
   }
   else if(LastBarTime == 0)
   {
      LastBarTime = currentBarTime;
   }
}

//+------------------------------------------------------------------+
//| ตรวจสอบการเปลี่ยนแปลงออเดอร์                                        |
//+------------------------------------------------------------------+
void CheckPositionChanges()
{
   int currentPositionCount = PositionsTotal();
   
   if(currentPositionCount != LastPositionCount)
   {
      SendPositionChangeReport(LastPositionCount, currentPositionCount);
      LastPositionCount = currentPositionCount;
   }
}

//+------------------------------------------------------------------+
//| ตรวจสอบรายงานตามช่วงเวลา                                           |
//+------------------------------------------------------------------+
void CheckPeriodicReport()
{
   datetime currentTime = TimeCurrent();
   
   if(currentTime - LastReportTime >= ReportInterval)
   {
      SendPeriodicStatusReport();
      LastReportTime = currentTime;
   }
}

//+------------------------------------------------------------------+
//| ส่งรายงานเริ่มต้น                                                 |
//+------------------------------------------------------------------+
void SendInitialReport()
{
   MessageText = "🚀 MT5 Status Manager เริ่มทำงาน\n\n";
   MessageText += GetCurrentStatusReport();
   SendTelegram(MessageText);
}

//+------------------------------------------------------------------+
//| ส่งรายงานแท่งใหม่                                                  |
//+------------------------------------------------------------------+
void SendNewBarReport()
{
   MessageText = "📊 แท่งใหม่: " + _Symbol + " " + GetTimeframeText() + "\n";
   MessageText += "⏰ เวลา: " + TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES) + "\n\n";
   MessageText += GetCurrentStatusReport();
   SendTelegram(MessageText);
}

//+------------------------------------------------------------------+
//| ส่งรายงานการเปลี่ยนแปลงออเดอร์                                      |
//+------------------------------------------------------------------+
void SendPositionChangeReport(int oldCount, int newCount)
{
   MessageText = "🔄 การเปลี่ยนแปลงออเดอร์\n";
   MessageText += "จำนวนเดิม: " + IntegerToString(oldCount) + " ไม้\n";
   MessageText += "จำนวนใหม่: " + IntegerToString(newCount) + " ไม้\n";
   
   if(newCount > oldCount)
   {
      MessageText += "✅ เปิดออเดอร์ใหม่: " + IntegerToString(newCount - oldCount) + " ไม้\n";
   }
   else if(newCount < oldCount)
   {
      MessageText += "❌ ปิดออเดอร์: " + IntegerToString(oldCount - newCount) + " ไม้\n";
   }
   
   MessageText += "\n" + GetCurrentStatusReport();
   SendTelegram(MessageText);
}

//+------------------------------------------------------------------+
//| ส่งรายงานสถานะตามช่วงเวลา                                          |
//+------------------------------------------------------------------+
void SendPeriodicStatusReport()
{
   MessageText = "📋 รายงานสถานะประจำ\n";
   MessageText += "⏰ เวลา: " + TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES) + "\n\n";
   MessageText += GetCurrentStatusReport();
   SendTelegram(MessageText);
}

//+------------------------------------------------------------------+
//| รับรายงานสถานะปัจจุบัน                                             |
//+------------------------------------------------------------------+
string GetCurrentStatusReport()
{
   string report = "";
   
   // สถานะ Algo Trading
   bool algoEnabled = TerminalInfoInteger(TERMINAL_TRADE_ALLOWED);
   report += "🤖 Algo Trading: " + (algoEnabled ? "✅ เปิด" : "❌ ปิด") + "\n";
   
   // จำนวนออเดอร์ทั้งหมด
   int totalPositions = PositionsTotal();
   report += "📊 จำนวนไม้: " + IntegerToString(totalPositions) + " ไม้\n";
   
   // กำไร/ขาดทุนรวม
   double totalProfit = GetTotalProfit();
   string profitText = (totalProfit >= 0) ? "💰 กำไร: +" : "💸 ขาดทุน: ";
   report += profitText + DoubleToString(totalProfit, 2) + " USD\n";
   
   // ขาดทุนปัจจุบันตาม SL
   double potentialLoss = GetPotentialLoss();
   if(potentialLoss < 0)
   {
      report += "⚠️ ขาดทุนสูงสุด: " + DoubleToString(potentialLoss, 2) + " USD\n";
   }
   
   // รายละเอียดออเดอร์
   if(totalPositions > 0)
   {
      report += "\n📋 รายละเอียดออเดอร์:\n";
      report += GetPositionDetails();
   }
   
   return report;
}

//+------------------------------------------------------------------+
//| คำนวณกำไร/ขาดทุนรวม                                               |
//+------------------------------------------------------------------+
double GetTotalProfit()
{
   double totalProfit = 0.0;
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      string symbol = PositionGetSymbol(i);
      if(symbol != "")
      {
         if(PositionSelect(symbol))
         {
            totalProfit += PositionGetDouble(POSITION_PROFIT) + PositionGetDouble(POSITION_SWAP);
         }
      }
   }
   
   return totalProfit;
}

//+------------------------------------------------------------------+
//| คำนวณขาดทุนสูงสุดตาม SL                                            |
//+------------------------------------------------------------------+
double GetPotentialLoss()
{
   double potentialLoss = 0.0;
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      string symbol = PositionGetSymbol(i);
      if(symbol != "")
      {
         if(PositionSelect(symbol))
         {
            double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
            double sl = PositionGetDouble(POSITION_SL);
            double volume = PositionGetDouble(POSITION_VOLUME);
            ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            
            if(sl > 0)
            {
               double pointValue = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_VALUE);
               double pointSize = SymbolInfoDouble(symbol, SYMBOL_POINT);
               
               double priceDiff = 0;
               if(type == POSITION_TYPE_BUY)
               {
                  priceDiff = openPrice - sl;
               }
               else
               {
                  priceDiff = sl - openPrice;
               }
               
               double loss = (priceDiff / pointSize) * pointValue * volume;
               potentialLoss -= loss;
            }
         }
      }
   }
   
   return potentialLoss;
}

//+------------------------------------------------------------------+
//| รับรายละเอียดออเดอร์                                               |
//+------------------------------------------------------------------+
string GetPositionDetails()
{
   string details = "";
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      string symbol = PositionGetSymbol(i);
      if(symbol != "")
      {
         if(PositionSelect(symbol))
         {
            ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double volume = PositionGetDouble(POSITION_VOLUME);
            double profit = PositionGetDouble(POSITION_PROFIT);
            
            string typeText = (type == POSITION_TYPE_BUY) ? "BUY" : "SELL";
            string profitText = (profit >= 0) ? "+" : "";
            
            details += StringFormat("%s %s %.2f (P/L: %s%.2f)\n", 
                                   symbol, typeText, volume, profitText, profit);
         }
      }
   }
   
   return details;
}

//+------------------------------------------------------------------+
//| รับข้อความ Timeframe                                              |
//+------------------------------------------------------------------+
string GetTimeframeText()
{
   switch(Period())
   {
      case PERIOD_M1:  return "M1";
      case PERIOD_M5:  return "M5";
      case PERIOD_M15: return "M15";
      case PERIOD_M30: return "M30";
      case PERIOD_H1:  return "H1";
      case PERIOD_H4:  return "H4";
      case PERIOD_D1:  return "D1";
      default: return "Unknown";
   }
}

//+------------------------------------------------------------------+
//| รับข้อความเหตุผลการหยุดทำงาน                                        |
//+------------------------------------------------------------------+
string GetDeinitReasonText(int reason)
{
   switch(reason)
   {
      case REASON_PROGRAM: return "โปรแกรมหยุดทำงาน";
      case REASON_REMOVE: return "ถูกลบออกจากชาร์ต";
      case REASON_RECOMPILE: return "คอมไพล์ใหม่";
      case REASON_CHARTCHANGE: return "เปลี่ยนชาร์ต";
      case REASON_CHARTCLOSE: return "ปิดชาร์ต";
      case REASON_PARAMETERS: return "เปลี่ยนพารามิเตอร์";
      case REASON_ACCOUNT: return "เปลี่ยนบัญชี";
      default: return "ไม่ทราบสาเหตุ";
   }
}

//+------------------------------------------------------------------+
//| ส่งข้อความไป Telegram                                             |
//+------------------------------------------------------------------+
void SendTelegram(string message)
{
   string url = "https://api.telegram.org/bot" + TelegramBotToken + "/sendMessage";
   string data = "chat_id=" + ChatID + "&text=" + message;
   
   char post[];
   StringToCharArray(data, post);
   
   char result[];
   string headers;
   int timeout = 5000;
   
   ResetLastError();
   string result_headers = "";
   int res = WebRequest("POST", url, headers, timeout, post, result, result_headers);
   
   if (res == -1) {
      Print("Failed to send message: ", GetLastError());
   } else {
      Print("Message sent successfully: ", CharArrayToString(result));
   }
}

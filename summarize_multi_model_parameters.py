#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
สคริปต์สำหรับสรุปพารามิเตอร์ Multi-Model Architecture
รวบรวมข้อมูล threshold, nBars_SL และ time_filters แยกตาม timeframe และ symbol

การใช้งาน:
    python summarize_multi_model_parameters.py

ผลลัพธ์:
    - แสดงสรุปบนหน้าจอ
    - บันทึกไฟล์สรุปใน LightGBM_Multi/summaries/
    - สร้างไฟล์ JSON สำหรับการใช้งานโปรแกรม
"""

import sys
import os

# เพิ่ม path สำหรับ import
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def main():
    """ฟังก์ชันหลักสำหรับการสรุปพารามิเตอร์"""
    
    print("🚀 Multi-Model Parameters Summary Tool")
    print("=" * 60)
    
    try:
        # Import ฟังก์ชันจาก python_LightGBM_17_Signal.py
        from python_LightGBM_17_Signal import (
            summarize_multi_model_parameters,
            run_parameter_summary_only,
            USE_MULTI_MODEL_ARCHITECTURE
        )
        
        # ตรวจสอบการตั้งค่า
        if not USE_MULTI_MODEL_ARCHITECTURE:
            print("❌ USE_MULTI_MODEL_ARCHITECTURE = False")
            print("💡 กรุณาแก้ไขการตั้งค่าใน python_LightGBM_17_Signal.py:")
            print("   USE_MULTI_MODEL_ARCHITECTURE = True")
            return
        
        print("✅ USE_MULTI_MODEL_ARCHITECTURE = True")
        print("📊 เริ่มการสรุปพารามิเตอร์...\n")
        
        # รันการสรุปพารามิเตอร์
        run_parameter_summary_only()
        
    except ImportError as e:
        print(f"❌ ไม่สามารถ import ฟังก์ชันที่จำเป็น: {e}")
        print("💡 กรุณาตรวจสอบว่าไฟล์ python_LightGBM_17_Signal.py อยู่ในโฟลเดอร์เดียวกัน")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def show_usage():
    """แสดงวิธีการใช้งาน"""
    print("""
📖 วิธีการใช้งาน Multi-Model Parameters Summary

1. 🔧 ตั้งค่า:
   - ตรวจสอบว่า USE_MULTI_MODEL_ARCHITECTURE = True
   - ตรวจสอบว่าได้เทรนโมเดลและหา optimal parameters แล้ว

2. 🚀 รันสคริปต์:
   python summarize_multi_model_parameters.py

3. 📊 ผลลัพธ์:
   - สรุปแสดงบนหน้าจอ
   - ไฟล์สรุปใน LightGBM_Multi/summaries/
   - ข้อมูล JSON สำหรับโปรแกรม

4. 📁 ไฟล์ที่สร้าง:
   - multi_model_parameters_summary.txt (สรุปรวม)
   - M30_parameters_summary.txt (เฉพาะ M30)
   - M60_parameters_summary.txt (เฉพาะ M60)
   - multi_model_parameters.json (ข้อมูล JSON)

5. 📋 โครงสร้างข้อมูลที่สรุป:
   - Time Filters (วันและเวลาที่แนะนำ)
   - Trend Following Model (threshold + nBars_SL)
   - Counter Trend Model (threshold + nBars_SL)
   - แยกตาม Symbol และ Timeframe

6. 💡 เคล็ดลับ:
   - ใช้ไฟล์ JSON สำหรับการโหลดข้อมูลในโปรแกรม
   - ไฟล์ .txt สำหรับการอ่านและตรวจสอบ
   - ตรวจสอบความสมบูรณ์ของข้อมูลจากสถิติที่แสดง
""")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        show_usage()
    else:
        main()

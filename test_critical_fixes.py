#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข Critical Issues:
- NZDUSD M30: CV_AUC = 0.5 → target > 0.75
- USDJPY M30: F1 = 0.375 → target > 0.55
"""

import os
import sys
import time
import pandas as pd
import numpy as np
from datetime import datetime

def test_critical_fixes():
    """ทดสอบการแก้ไข critical issues"""
    print("🔥 ทดสอบการแก้ไข Critical Issues")
    print("=" * 80)
    
    # เกณฑ์ความสำเร็จ
    success_criteria = {
        'NZDUSD M30': {
            'critical': {'CV_AUC': 0.75},
            'metric': 'CV_AUC'
        },
        'USDJPY M30': {
            'critical': {'F1': 0.55},
            'metric': 'F1'
        }
    }
    
    print("🎯 เกณฑ์ความสำเร็จ:")
    for symbol, criteria in success_criteria.items():
        metric = criteria['metric']
        target = criteria['critical'][metric]
        print(f"   • {symbol}: {metric} > {target}")
    
    print(f"\n⏱️ เริ่มการทดสอบ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success_criteria

def run_training():
    """รัน training สำหรับ 2 symbols"""
    print(f"\n🚀 รัน Training สำหรับ 2 Critical Symbols")
    print("=" * 60)
    
    start_time = time.time()
    
    # รัน main script
    print("💻 คำสั่ง: python python_LightGBM_15_Tuning.py")
    exit_code = os.system("python python_LightGBM_15_Tuning.py")
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n⏱️ เวลาที่ใช้: {duration:.2f} วินาที ({duration/60:.1f} นาที)")
    
    if exit_code == 0:
        print("✅ Training สำเร็จ")
        return True
    else:
        print("❌ Training ไม่สำเร็จ")
        return False

def analyze_results(success_criteria):
    """วิเคราะห์ผลลัพธ์"""
    print(f"\n📊 วิเคราะห์ผลลัพธ์")
    print("=" * 60)
    
    results = {}
    
    # อ่านผลลัพธ์จากไฟล์
    result_files = {
        'NZDUSD M30': 'Test_LightGBM/results/M30/NZDUSD_M30_performance_analysis.txt',
        'USDJPY M30': 'Test_LightGBM/results/M30/USDJPY_M30_performance_analysis.txt'
    }
    
    for symbol, file_path in result_files.items():
        print(f"\n📂 {symbol}:")
        
        if not os.path.exists(file_path):
            print(f"   ❌ ไม่พบไฟล์ผลลัพธ์: {file_path}")
            results[symbol] = None
            continue
        
        try:
            # อ่านไฟล์ผลลัพธ์
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # แยกข้อมูล
            lines = content.split('\n')
            metrics = {}
            
            for line in lines:
                if 'Accuracy:' in line:
                    metrics['Accuracy'] = float(line.split(':')[1].strip())
                elif 'AUC:' in line:
                    metrics['AUC'] = float(line.split(':')[1].strip())
                elif 'F1:' in line:
                    metrics['F1'] = float(line.split(':')[1].strip())
                elif 'CV_AUC:' in line:
                    metrics['CV_AUC'] = float(line.split(':')[1].strip())
            
            results[symbol] = metrics
            
            # แสดงผลลัพธ์
            print(f"   📊 ผลลัพธ์:")
            for metric, value in metrics.items():
                print(f"      {metric}: {value:.3f}")
            
        except Exception as e:
            print(f"   ❌ เกิดข้อผิดพลาดในการอ่านไฟล์: {str(e)}")
            results[symbol] = None
    
    return results

def evaluate_success(results, success_criteria):
    """ประเมินความสำเร็จ"""
    print(f"\n🎯 ประเมินความสำเร็จ")
    print("=" * 60)
    
    total_symbols = len(success_criteria)
    passed_symbols = 0
    
    for symbol, criteria in success_criteria.items():
        print(f"\n📊 {symbol}:")
        
        if symbol not in results or results[symbol] is None:
            print(f"   ❌ ไม่มีข้อมูลผลลัพธ์")
            continue
        
        metrics = results[symbol]
        target_metric = criteria['metric']
        target_value = criteria['critical'][target_metric]
        
        if target_metric in metrics:
            current_value = metrics[target_metric]
            passed = current_value >= target_value
            
            if passed:
                print(f"   ✅ {target_metric}: {current_value:.3f} >= {target_value:.3f} (ผ่าน)")
                passed_symbols += 1
            else:
                print(f"   ❌ {target_metric}: {current_value:.3f} < {target_value:.3f} (ไม่ผ่าน)")
                
                # คำนวณ improvement needed
                improvement = ((target_value - current_value) / current_value) * 100
                print(f"      💡 ต้องปรับปรุง: +{improvement:.1f}%")
        else:
            print(f"   ❌ ไม่พบ {target_metric} ในผลลัพธ์")
    
    # สรุปผล
    success_rate = (passed_symbols / total_symbols) * 100
    print(f"\n📈 สรุปผลการประเมิน:")
    print(f"   ผ่านเกณฑ์: {passed_symbols}/{total_symbols} symbols ({success_rate:.0f}%)")
    
    if success_rate >= 100:
        print(f"   🎉 สถานะ: ✅ ผ่านเกณฑ์ทั้งหมด - พร้อมรัน Full Training!")
    elif success_rate >= 50:
        print(f"   ⚠️ สถานะ: 🔄 ผ่านบางส่วน - ต้องปรับปรุงเพิ่มเติม")
    else:
        print(f"   ❌ สถานะ: 🔧 ไม่ผ่านเกณฑ์ - ต้องแก้ไขเพิ่มเติม")
    
    return passed_symbols, total_symbols

def generate_recommendations(passed_symbols, total_symbols, results):
    """สร้างคำแนะนำ"""
    print(f"\n💡 คำแนะนำการดำเนินการต่อ")
    print("=" * 60)
    
    if passed_symbols == total_symbols:
        print("🎉 การแก้ไขสำเร็จ!")
        print("📋 ขั้นตอนถัดไป:")
        print("   1. เปลี่ยน NUM_TRAINING_ROUNDS = 10")
        print("   2. รัน full training ทั้งหมด 8 symbols × 2 timeframes")
        print("   3. รอผลลัพธ์ 3-4 ชั่วโมง")
        print("   4. วิเคราะห์ parameter stability")
        
    elif passed_symbols > 0:
        print("⚠️ การแก้ไขบางส่วนสำเร็จ")
        print("📋 ขั้นตอนถัดไป:")
        print("   1. วิเคราะห์ symbols ที่ยังไม่ผ่าน")
        print("   2. ปรับปรุง feature engineering เพิ่มเติม")
        print("   3. ปรับ class weight หรือ threshold")
        print("   4. ทดสอบใหม่")
        
    else:
        print("❌ การแก้ไขยังไม่สำเร็จ")
        print("📋 ขั้นตอนถัดไป:")
        print("   1. ตรวจสอบ data quality อีกครั้ง")
        print("   2. ลองใช้ advanced feature engineering")
        print("   3. พิจารณาใช้ ensemble methods")
        print("   4. ปรับ cross validation strategy")
    
    # แสดงผลลัพธ์ที่ดีที่สุด
    if results:
        print(f"\n📊 ผลลัพธ์ที่ดีที่สุด:")
        for symbol, metrics in results.items():
            if metrics:
                best_metric = max(metrics.items(), key=lambda x: x[1])
                print(f"   {symbol}: {best_metric[0]} = {best_metric[1]:.3f}")

def main():
    """ฟังก์ชันหลัก"""
    print("🔥 ทดสอบการแก้ไข Critical Issues")
    print("=" * 80)
    print("🎯 เป้าหมาย:")
    print("   • NZDUSD M30: แก้ไข CV_AUC = 0.5 → > 0.75")
    print("   • USDJPY M30: ปรับปรุง F1 = 0.375 → > 0.55")
    print("=" * 80)
    
    # 1. ตั้งค่าเกณฑ์ความสำเร็จ
    success_criteria = test_critical_fixes()
    
    # 2. รัน training
    training_success = run_training()
    
    if not training_success:
        print("❌ Training ไม่สำเร็จ - หยุดการทดสอบ")
        return
    
    # 3. วิเคราะห์ผลลัพธ์
    results = analyze_results(success_criteria)
    
    # 4. ประเมินความสำเร็จ
    passed_symbols, total_symbols = evaluate_success(results, success_criteria)
    
    # 5. สร้างคำแนะนำ
    generate_recommendations(passed_symbols, total_symbols, results)
    
    # 6. สรุปสุดท้าย
    print(f"\n🎉 สรุปการทดสอบ Critical Fixes")
    print("=" * 80)
    
    success_rate = (passed_symbols / total_symbols) * 100
    
    print(f"📊 ผลการทดสอบ:")
    print(f"   • ผ่านเกณฑ์: {passed_symbols}/{total_symbols} symbols ({success_rate:.0f}%)")
    print(f"   • เวลาที่ใช้: ประมาณ 5-10 นาที")
    
    if success_rate >= 100:
        print(f"   • สถานะ: ✅ พร้อมรัน Full Training")
    elif success_rate >= 50:
        print(f"   • สถานะ: ⚠️ ต้องปรับปรุงเพิ่มเติม")
    else:
        print(f"   • สถานะ: ❌ ต้องแก้ไขเพิ่มเติม")
    
    print(f"\n🚀 ขั้นตอนถัดไป:")
    if success_rate >= 100:
        print("   1. เปลี่ยน NUM_TRAINING_ROUNDS = 10")
        print("   2. รัน full training")
    else:
        print("   1. วิเคราะห์ปัญหาที่เหลือ")
        print("   2. ปรับปรุงเพิ่มเติม")
        print("   3. ทดสอบใหม่")

if __name__ == "__main__":
    main()

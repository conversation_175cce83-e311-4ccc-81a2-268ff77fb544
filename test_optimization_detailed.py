#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข optimization อย่างละเอียดพร้อมแสดงผลย่อย

Created: 2025-01-11
Author: AI Assistant
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
import json
from pathlib import Path

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_detailed_optimization():
    """
    ทดสอบการ optimization อย่างละเอียดพร้อมแสดงผลย่อย
    """
    print("🚀 ทดสอบการ optimization อย่างละเอียดพร้อมแสดงผลย่อย")
    print("="*80)
    
    try:
        from python_LightGBM_16_Signal import (
            run_multi_model_optimization,
            parse_filename
        )
        
        # รายการไฟล์ที่จะทดสอบ
        test_files = [
            "CSV_Files_Fixed/EURUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/GOLD_H1_FIXED.csv", 
            "CSV_Files_Fixed/USDJPY_H1_FIXED.csv"
        ]
        
        all_results = {}
        
        for file in test_files:
            print(f"\n{'='*60}")
            print(f"📊 ทดสอบไฟล์: {file}")
            print(f"{'='*60}")
            
            try:
                # Parse filename
                file_info = parse_filename(file)
                symbol = file_info["Name_Currency"]
                timeframe = file_info["Timeframe_Currency"]
                
                print(f"🔸 Symbol: {symbol}")
                print(f"🔸 Timeframe: M{timeframe}")
                
                # รัน optimization
                print(f"\n🚀 เริ่มการหา optimal parameters...")
                
                result = run_multi_model_optimization(symbol, timeframe)
                
                if result:
                    print(f"\n✅ Optimization สำเร็จสำหรับ {symbol} M{timeframe}")
                    
                    # เก็บผลลัพธ์
                    all_results[f"{symbol}_{timeframe}"] = result
                    
                    # แสดงผลรายละเอียด
                    print(f"\n📊 ผลลัพธ์รายละเอียด:")
                    print(f"   Symbol: {result.get('symbol')}")
                    print(f"   Timeframe: {result.get('timeframe')}")
                    print(f"   Scenarios: {result.get('scenarios')}")
                    print(f"   Validation samples: {result.get('validation_samples')}")
                    
                    print(f"\n🎯 Optimal Thresholds:")
                    for scenario, threshold in result.get('optimal_thresholds', {}).items():
                        print(f"   {scenario}: {threshold:.4f}")
                    
                    print(f"\n📏 Optimal nBars_SL:")
                    for scenario, nbars in result.get('optimal_nbars', {}).items():
                        print(f"   {scenario}: {nbars}")
                    
                    # ตรวจสอบไฟล์ที่สร้าง
                    print(f"\n📁 ตรวจสอบไฟล์ที่สร้าง:")
                    
                    thresholds_dir = Path("LightGBM_Multi/thresholds")
                    
                    # ไฟล์ threshold
                    for scenario in result.get('scenarios', []):
                        threshold_file = thresholds_dir / f"{str(timeframe).zfill(3)}_{symbol}_{scenario}_optimal_threshold.pkl"
                        if threshold_file.exists():
                            with open(threshold_file, 'rb') as f:
                                saved_threshold = pickle.load(f)
                            print(f"   ✅ {threshold_file.name}: {saved_threshold:.4f}")
                        else:
                            print(f"   ❌ ไม่พบ: {threshold_file.name}")
                    
                    # ไฟล์ nBars_SL
                    for scenario in result.get('scenarios', []):
                        nbars_file = thresholds_dir / f"{str(timeframe).zfill(3)}_{symbol}_{scenario}_optimal_nBars_SL.pkl"
                        if nbars_file.exists():
                            with open(nbars_file, 'rb') as f:
                                saved_nbars = pickle.load(f)
                            print(f"   ✅ {nbars_file.name}: {saved_nbars}")
                        else:
                            print(f"   ❌ ไม่พบ: {nbars_file.name}")
                    
                else:
                    print(f"❌ Optimization ล้มเหลวสำหรับ {symbol} M{timeframe}")
                    
            except Exception as e:
                print(f"❌ เกิดข้อผิดพลาดกับ {file}: {e}")
        
        return all_results
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return {}
    except Exception as e:
        print(f"❌ Error: {e}")
        return {}

def analyze_optimization_results(results):
    """
    วิเคราะห์ผลการ optimization
    """
    print(f"\n🔍 วิเคราะห์ผลการ optimization")
    print("="*60)
    
    if not results:
        print("❌ ไม่มีผลลัพธ์ให้วิเคราะห์")
        return
    
    print(f"📊 จำนวน symbols ที่ประมวลผล: {len(results)}")
    
    # วิเคราะห์ threshold
    print(f"\n🎯 การวิเคราะห์ Threshold:")
    threshold_stats = {}
    
    for symbol_key, result in results.items():
        symbol = result.get('symbol')
        thresholds = result.get('optimal_thresholds', {})
        
        print(f"\n   {symbol_key}:")
        for scenario, threshold in thresholds.items():
            print(f"     {scenario}: {threshold:.4f}")
            
            if scenario not in threshold_stats:
                threshold_stats[scenario] = []
            threshold_stats[scenario].append(threshold)
    
    # สถิติ threshold
    print(f"\n📈 สถิติ Threshold:")
    for scenario, values in threshold_stats.items():
        if values:
            mean_val = np.mean(values)
            std_val = np.std(values)
            min_val = np.min(values)
            max_val = np.max(values)
            
            print(f"   {scenario}:")
            print(f"     Mean: {mean_val:.4f}")
            print(f"     Std: {std_val:.4f}")
            print(f"     Range: {min_val:.4f} - {max_val:.4f}")
    
    # วิเคราะห์ nBars_SL
    print(f"\n📏 การวิเคราะห์ nBars_SL:")
    nbars_stats = {}
    
    for symbol_key, result in results.items():
        symbol = result.get('symbol')
        nbars = result.get('optimal_nbars', {})
        
        print(f"\n   {symbol_key}:")
        for scenario, nbars_val in nbars.items():
            print(f"     {scenario}: {nbars_val}")
            
            if scenario not in nbars_stats:
                nbars_stats[scenario] = []
            nbars_stats[scenario].append(nbars_val)
    
    # สถิติ nBars_SL
    print(f"\n📊 สถิติ nBars_SL:")
    for scenario, values in nbars_stats.items():
        if values:
            mean_val = np.mean(values)
            std_val = np.std(values)
            min_val = np.min(values)
            max_val = np.max(values)
            
            print(f"   {scenario}:")
            print(f"     Mean: {mean_val:.2f}")
            print(f"     Std: {std_val:.2f}")
            print(f"     Range: {min_val} - {max_val}")
    
    # ตรวจสอบความแตกต่าง
    print(f"\n🔍 การตรวจสอบความแตกต่าง:")
    
    # Threshold
    for scenario, values in threshold_stats.items():
        if values:
            unique_values = len(set(values))
            if unique_values == 1:
                print(f"   ⚠️ {scenario} threshold: ค่าเดียวกันทั้งหมด ({values[0]:.4f})")
            else:
                print(f"   ✅ {scenario} threshold: มีความแตกต่าง ({unique_values} ค่าที่แตกต่าง)")
    
    # nBars_SL
    for scenario, values in nbars_stats.items():
        if values:
            unique_values = len(set(values))
            if unique_values == 1:
                print(f"   ⚠️ {scenario} nBars_SL: ค่าเดียวกันทั้งหมด ({values[0]})")
            else:
                print(f"   ✅ {scenario} nBars_SL: มีความแตกต่าง ({unique_values} ค่าที่แตกต่าง)")

def check_optimization_files():
    """
    ตรวจสอบไฟล์ optimization ที่สร้างขึ้น
    """
    print(f"\n📁 ตรวจสอบไฟล์ optimization ที่สร้างขึ้น")
    print("="*60)
    
    thresholds_dir = Path("LightGBM_Multi/thresholds")
    
    if not thresholds_dir.exists():
        print(f"❌ ไม่พบโฟลเดอร์: {thresholds_dir}")
        return
    
    files = list(thresholds_dir.glob("*.pkl"))
    
    print(f"📊 จำนวนไฟล์ทั้งหมด: {len(files)} ไฟล์")
    
    # จำแนกประเภทไฟล์
    file_categories = {
        'threshold_files': [],
        'nbars_files': [],
        'time_filter_files': [],
        'other_files': []
    }
    
    for file in files:
        file_name = file.name
        
        if 'optimal_threshold' in file_name:
            file_categories['threshold_files'].append(file_name)
        elif 'optimal_nBars_SL' in file_name:
            file_categories['nbars_files'].append(file_name)
        elif 'time_filters' in file_name:
            file_categories['time_filter_files'].append(file_name)
        else:
            file_categories['other_files'].append(file_name)
    
    # แสดงผลการจำแนก
    for category, file_list in file_categories.items():
        count = len(file_list)
        print(f"\n🔸 {category}: {count} ไฟล์")
        
        if count > 0:
            for file_name in sorted(file_list):
                # อ่านค่าจากไฟล์
                file_path = thresholds_dir / file_name
                try:
                    with open(file_path, 'rb') as f:
                        value = pickle.load(f)
                    
                    if isinstance(value, (int, float)):
                        print(f"   {file_name}: {value}")
                    else:
                        print(f"   {file_name}: {type(value).__name__}")
                        
                except Exception as e:
                    print(f"   {file_name}: ❌ ไม่สามารถอ่านได้ ({e})")

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🚀 ทดสอบการ optimization อย่างละเอียดพร้อมแสดงผลย่อย")
    print("="*80)
    
    # Test 1: รัน optimization แบบละเอียด
    results = test_detailed_optimization()
    
    # Test 2: วิเคราะห์ผลลัพธ์
    analyze_optimization_results(results)
    
    # Test 3: ตรวจสอบไฟล์ที่สร้าง
    check_optimization_files()
    
    print("\n" + "="*80)
    print("📊 สรุปผลการทดสอบ")
    print("="*80)
    
    success_count = len(results)
    total_count = 3  # EURUSD, GOLD, USDJPY
    
    print(f"✅ Optimization สำเร็จ: {success_count}/{total_count} symbols")
    
    if success_count > 0:
        print(f"🎉 การ optimization ทำงานได้แล้ว!")
        print(f"💡 ตรวจสอบความแตกต่างของค่าที่ได้ในส่วนวิเคราะห์ข้างต้น")
    else:
        print(f"⚠️ การ optimization ยังไม่ทำงาน")

if __name__ == "__main__":
    main()

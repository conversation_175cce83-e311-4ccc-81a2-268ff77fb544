#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
แก้ไขไฟล์ EURUSD_M30_FIXED.csv เฉพาะ
"""

import pandas as pd
import os

def fix_eurusd_file():
    """แก้ไขไฟล์ EURUSD เฉพาะ"""
    
    # อ่านไฟล์ต้นฉบับ
    source_file = 'MT5_250711/EURUSD#_M30_201907080000_202507112330.csv'
    print(f'🔄 อ่านไฟล์: {source_file}')
    
    # ตรวจสอบว่าไฟล์มีอยู่หรือไม่
    if not os.path.exists(source_file):
        print(f'❌ ไม่พบไฟล์: {source_file}')
        return False
    
    try:
        # อ่านด้วย tab separator
        df = pd.read_csv(source_file, sep='\t', header=None)
        print(f'📊 ข้อมูลที่อ่านได้: {df.shape[0]} rows, {df.shape[1]} columns')
        
        # ตั้งชื่อคอลัมน์
        if df.shape[1] >= 9:
            df.columns = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'TickVol', 'Vol', 'Col_8']
        else:
            print(f'❌ จำนวนคอลัมน์ไม่ถูกต้อง: {df.shape[1]}')
            return False
            
        print(f'✅ ตั้งชื่อคอลัมน์เสร็จ')
        
        # แสดงตัวอย่างข้อมูล
        print(f'📋 ตัวอย่างข้อมูล 3 แถวแรก:')
        print(df.head(3))
        
        # สร้างโฟลเดอร์ถ้าไม่มี
        output_dir = 'CSV_Files_Fixed'
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # บันทึกไฟล์ใหม่
        output_file = 'CSV_Files_Fixed/EURUSD_M30_FIXED.csv'
        df.to_csv(output_file, index=False)
        print(f'💾 บันทึกไฟล์ที่: {output_file}')
        
        # ตรวจสอบไฟล์ที่บันทึก
        df_check = pd.read_csv(output_file)
        print(f'✅ ตรวจสอบไฟล์ใหม่: {df_check.shape[0]} rows, {df_check.shape[1]} columns')
        
        # แสดงตัวอย่างข้อมูลที่บันทึก
        print(f'📋 ตัวอย่างข้อมูลที่บันทึก:')
        print(df_check.head(3))
        
        return True
        
    except Exception as e:
        print(f'❌ เกิดข้อผิดพลาด: {str(e)}')
        return False

if __name__ == "__main__":
    print("🔧 แก้ไขไฟล์ EURUSD_M30_FIXED.csv")
    print("=" * 50)
    
    success = fix_eurusd_file()
    
    if success:
        print("\n🎉 แก้ไขสำเร็จ!")
    else:
        print("\n❌ แก้ไขไม่สำเร็จ!")

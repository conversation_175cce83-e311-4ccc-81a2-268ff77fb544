#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขค่าที่หายไปในไฟล์ summary
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import subprocess

def test_missing_values_fix():
    """ทดสอบการแก้ไขค่าที่หายไป"""
    
    print("🧪 ทดสอบการแก้ไขค่าที่หายไปในไฟล์ summary")
    print("="*70)
    
    # ตั้งค่าการทดสอบ
    symbol = "GOLD"
    timeframe = "H1"
    
    print(f"📋 การตั้งค่าการทดสอบ:")
    print(f"  Symbol: {symbol}")
    print(f"  Timeframe: {timeframe}")
    
    # ตรวจสอบไฟล์ข้อมูล
    csv_file = f"CSV_Files_Fixed/{symbol}_{timeframe}_FIXED.csv"
    if not os.path.exists(csv_file):
        print(f"❌ ไม่พบไฟล์: {csv_file}")
        return False
    
    file_size = os.path.getsize(csv_file)
    print(f"  File: {csv_file}")
    print(f"  File Size: {file_size:,} bytes")
    
    try:
        # เรียกใช้โค้ดหลักแบบจำกัดเวลา
        print(f"\n🚀 เริ่มการทดสอบการเทรน...")
        print(f"⏰ เริ่มเมื่อ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # เปลี่ยนการตั้งค่าให้เทรนเร็วขึ้น
        os.environ['QUICK_TEST'] = '1'
        
        # เรียกใช้โค้ดหลัก
        result = subprocess.run([
            'python', 'python_LightGBM_15_Tuning.py'
        ], capture_output=True, text=True, timeout=180, encoding='utf-8', errors='ignore')
        
        print(f"⏰ เสร็จเมื่อ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        if result.returncode == 0:
            print(f"✅ การเทรนเสร็จสิ้น!")
        else:
            print(f"⚠️ การเทรนมีปัญหา (return code: {result.returncode})")
            if result.stderr:
                print(f"Error: {result.stderr[:500]}...")
        
        # ตรวจสอบไฟล์ผลลัพธ์
        print(f"\n🔍 ตรวจสอบไฟล์ผลลัพธ์...")
        
        # 1. ตรวจสอบ trading summary files
        summary_files = []
        for file in os.listdir('.'):
            if file.endswith('_trading_summary.txt') and 'GOLD' in file:
                summary_files.append(file)
        
        if not summary_files:
            print(f"❌ ไม่พบไฟล์ trading summary")
            return False
        
        # เรียงตามเวลาแก้ไขล่าสุด
        summary_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        latest_file = summary_files[0]
        
        print(f"📁 ไฟล์ล่าสุด: {latest_file}")
        print(f"📅 แก้ไขล่าสุด: {datetime.fromtimestamp(os.path.getmtime(latest_file)).strftime('%Y-%m-%d %H:%M:%S')}")
        
        # อ่านและวิเคราะห์ไฟล์
        with open(latest_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"\n📊 วิเคราะห์เนื้อหาไฟล์:")
        
        # ตรวจสอบ ML metrics
        ml_metrics = {}
        lines = content.split('\n')
        
        for line in lines:
            if 'F1 Score:' in line:
                f1_value = float(line.split(':')[1].strip())
                ml_metrics['f1'] = f1_value
            elif 'Precision:' in line:
                precision_value = float(line.split(':')[1].strip())
                ml_metrics['precision'] = precision_value
            elif 'Recall:' in line:
                recall_value = float(line.split(':')[1].strip())
                ml_metrics['recall'] = recall_value
        
        print(f"  🤖 ML Metrics:")
        for key, value in ml_metrics.items():
            print(f"    - {key}: {value}")
        
        # ตรวจสอบ entry conditions
        entry_conditions = {}
        in_entry_section = False
        
        for line in lines:
            if '📈 สรุปเงื่อนไขเข้าเทรดแต่ละแบบ:' in line:
                in_entry_section = True
                continue
            elif in_entry_section and line.strip().startswith('='):
                break
            elif in_entry_section and ':' in line and 'เทรด' in line:
                parts = line.strip().split('|')
                if len(parts) >= 2:
                    condition_name = parts[0].split(':')[0].strip()
                    trades_part = parts[1].strip()
                    if 'เทรด' in trades_part:
                        trades_count = int(trades_part.split('เทรด')[1].split('ครั้ง')[0].strip())
                        entry_conditions[condition_name] = trades_count
        
        print(f"  📈 Entry Conditions:")
        for condition, trades in entry_conditions.items():
            print(f"    - {condition}: {trades} เทรด")
        
        # 2. ตรวจสอบ daily trading schedule
        schedule_file = "daily_trading_schedule_summary.txt"
        if os.path.exists(schedule_file):
            print(f"\n📅 ตรวจสอบ Daily Trading Schedule:")
            
            with open(schedule_file, 'r', encoding='utf-8') as f:
                schedule_content = f.read()
            
            # หา win rates ในแต่ละวัน
            schedule_lines = schedule_content.split('\n')
            daily_stats = {}
            
            current_day = None
            for line in schedule_lines:
                if 'วัน' in line and '(' in line:
                    current_day = line.strip().split('(')[0].strip()
                elif current_day and 'W%' in line:
                    win_rate_str = line.split('W%')[1].split('%')[0].strip()
                    try:
                        win_rate = float(win_rate_str)
                        daily_stats[current_day] = win_rate
                    except:
                        daily_stats[current_day] = 0.0
            
            print(f"  📊 Daily Win Rates:")
            for day, win_rate in daily_stats.items():
                print(f"    - {day}: {win_rate:.2f}%")
        else:
            print(f"❌ ไม่พบไฟล์ daily_trading_schedule_summary.txt")
        
        # ตรวจสอบความถูกต้อง
        print(f"\n🔍 ตรวจสอบความถูกต้อง:")
        
        issues = []
        
        # 1. ตรวจสอบ ML metrics
        if 'f1' in ml_metrics and ml_metrics['f1'] == 0.0:
            issues.append("F1 Score ยังคงเป็น 0.0000")
        elif 'f1' in ml_metrics and ml_metrics['f1'] > 0:
            print(f"  ✅ F1 Score มีค่า: {ml_metrics['f1']:.4f}")
        
        if 'precision' in ml_metrics and ml_metrics['precision'] == 0.0:
            issues.append("Precision ยังคงเป็น 0.0000")
        elif 'precision' in ml_metrics and ml_metrics['precision'] > 0:
            print(f"  ✅ Precision มีค่า: {ml_metrics['precision']:.4f}")
        
        if 'recall' in ml_metrics and ml_metrics['recall'] == 0.0:
            issues.append("Recall ยังคงเป็น 0.0000")
        elif 'recall' in ml_metrics and ml_metrics['recall'] > 0:
            print(f"  ✅ Recall มีค่า: {ml_metrics['recall']:.4f}")
        
        # 2. ตรวจสอบ entry conditions
        if entry_conditions:
            all_zero = all(count == 0 for count in entry_conditions.values())
            if all_zero:
                issues.append("Entry conditions ทุกแบบยังคงแสดง 0 เทรด")
            else:
                print(f"  ✅ Entry conditions มีจำนวนเทรดที่ถูกต้อง")
        
        # 3. ตรวจสอบ daily schedule
        if 'daily_stats' in locals():
            all_zero_daily = all(rate == 0.0 for rate in daily_stats.values())
            if all_zero_daily:
                issues.append("Daily trading schedule ทุกวันยังคงแสดง 0.00%")
            else:
                print(f"  ✅ Daily trading schedule มีข้อมูลที่ถูกต้อง")
        
        # สรุปผล
        if issues:
            print(f"\n❌ ยังพบปัญหา:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        else:
            print(f"\n✅ การแก้ไขสำเร็จ! ไม่พบปัญหาค่าที่หายไป")
            return True
            
    except subprocess.TimeoutExpired:
        print(f"⏰ การทดสอบหมดเวลา (3 นาที)")
        return False
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {str(e)}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    
    print("🧪 ทดสอบการแก้ไขค่าที่หายไปในไฟล์ summary")
    print("="*70)
    
    success = test_missing_values_fix()
    
    print(f"\n🏁 สรุปผลการทดสอบ")
    print("="*70)
    
    if success:
        print("✅ การแก้ไขค่าที่หายไปสำเร็จ!")
        print("💡 ปัญหาที่แก้ไขแล้ว:")
        print("  1. ✅ F1 Score, Precision, Recall: ใช้ค่า macro สำหรับ multi-class")
        print("  2. ✅ Entry Conditions: แสดงจำนวนเทรดที่ถูกต้อง")
        print("  3. ✅ Daily Trading Schedule: มีข้อมูลสถิติรายวัน")
        
        print(f"\n🚀 ระบบพร้อมใช้งาน:")
        print("  - ML metrics แสดงค่าที่ถูกต้อง")
        print("  - Entry conditions แสดงจำนวนเทรดจริง")
        print("  - Daily schedule มีข้อมูลสถิติ")
        
    else:
        print("❌ การแก้ไขยังไม่สมบูรณ์!")
        print("💡 ต้องตรวจสอบเพิ่มเติม:")
        print("  - การส่งผ่าน ML metrics จาก multi-class evaluation")
        print("  - การแสดงจำนวนเทรดใน entry conditions")
        print("  - การสร้างข้อมูล daily trading schedule")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)

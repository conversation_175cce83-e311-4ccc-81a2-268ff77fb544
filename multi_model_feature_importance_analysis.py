#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
วิเคราะห์การทำงานของ Feature Importance ใน Multi-Model Architecture
ตรวจสอบการบันทึกไฟล์และการเรียกใช้งาน analyze_cross_asset_feature_importance()

Created: 2025-01-11
Author: AI Assistant
"""

import os
import sys
import pandas as pd
import pickle
import json
from pathlib import Path

# Configuration
BASE_PATH = "LightGBM_Multi"
TIMEFRAME = 60

def check_feature_importance_structure():
    """
    ตรวจสอบโครงสร้างไฟล์ Feature Importance ใน Multi-Model Architecture
    """
    print("🔍 ตรวจสอบโครงสร้างไฟล์ Feature Importance")
    print("="*70)
    
    # 1. ตรวจสอบโฟลเดอร์หลัก
    base_path = Path(BASE_PATH)
    if not base_path.exists():
        print(f"❌ ไม่พบโฟลเดอร์ {BASE_PATH}")
        return False
    
    # 2. ตรวจสอบโฟลเดอร์ feature_importance
    feature_importance_dir = base_path / "feature_importance"
    print(f"\n📁 Feature Importance Directory: {feature_importance_dir}")
    print(f"   Exists: {feature_importance_dir.exists()}")
    
    if feature_importance_dir.exists():
        files = list(feature_importance_dir.glob("*.pkl"))
        print(f"   PKL files: {len(files)}")
        for file in files:
            print(f"     - {file.name}")
    
    # 3. ตรวจสอบโฟลเดอร์ results
    results_dir = base_path / "results"
    print(f"\n📁 Results Directory: {results_dir}")
    print(f"   Exists: {results_dir.exists()}")
    
    if results_dir.exists():
        # ตรวจสอบ scenario folders
        scenario_folders = ["trend_following", "counter_trend", "M60"]
        
        for scenario in scenario_folders:
            scenario_dir = results_dir / scenario
            print(f"\n📂 {scenario}:")
            print(f"   Path: {scenario_dir}")
            print(f"   Exists: {scenario_dir.exists()}")
            
            if scenario_dir.exists():
                # หาไฟล์ feature importance
                feature_files = list(scenario_dir.glob("*feature_importance*.csv"))
                comparison_files = list(scenario_dir.glob("*feature_importance_comparison*.csv"))
                
                print(f"   Feature Importance CSV: {len(feature_files)}")
                for file in feature_files:
                    print(f"     - {file.name}")
                
                print(f"   Feature Importance Comparison CSV: {len(comparison_files)}")
                for file in comparison_files:
                    print(f"     - {file.name}")
    
    return True

def analyze_must_have_features():
    """
    วิเคราะห์ไฟล์ must_have_features.pkl
    """
    print("\n🔍 วิเคราะห์ไฟล์ must_have_features.pkl")
    print("="*50)
    
    pkl_path = Path(BASE_PATH) / "feature_importance" / f"{str(TIMEFRAME).zfill(3)}_must_have_features.pkl"
    
    print(f"📄 File path: {pkl_path}")
    print(f"📄 File exists: {pkl_path.exists()}")
    
    if pkl_path.exists():
        try:
            with open(pkl_path, 'rb') as f:
                features = pickle.load(f)
            
            print(f"📊 Number of features: {len(features)}")
            print(f"📊 Features type: {type(features)}")
            
            if isinstance(features, list):
                print("\n📋 Features list:")
                for i, feature in enumerate(features, 1):
                    print(f"  {i}. {feature}")
            else:
                print(f"\n📋 Features content: {features}")
                
            return features
            
        except Exception as e:
            print(f"❌ Error loading file: {e}")
            return None
    else:
        print("⚠️ File not found")
        return None

def analyze_feature_importance_files():
    """
    วิเคราะห์ไฟล์ Feature Importance CSV ในแต่ละ scenario
    """
    print("\n🔍 วิเคราะห์ไฟล์ Feature Importance CSV")
    print("="*50)
    
    results_dir = Path(BASE_PATH) / "results"
    scenarios = ["trend_following", "counter_trend", "M60"]
    symbols = ["AUDUSD", "GOLD", "USDJPY"]
    
    analysis_results = {}
    
    for scenario in scenarios:
        scenario_dir = results_dir / scenario
        print(f"\n📂 Scenario: {scenario}")
        
        if not scenario_dir.exists():
            print(f"   ❌ Directory not found: {scenario_dir}")
            continue
        
        scenario_results = {}
        
        for symbol in symbols:
            # หาไฟล์ feature importance สำหรับ symbol นี้
            pattern = f"{str(TIMEFRAME).zfill(3)}_{symbol}_feature_importance.csv"
            file_path = scenario_dir / pattern
            
            print(f"\n   🔍 {symbol}:")
            print(f"     File: {pattern}")
            print(f"     Exists: {file_path.exists()}")
            
            if file_path.exists():
                try:
                    df = pd.read_csv(file_path)
                    print(f"     Rows: {len(df)}")
                    print(f"     Columns: {list(df.columns)}")
                    
                    # แสดง top 5 features
                    if 'Feature' in df.columns:
                        if 'Gain' in df.columns:
                            top_features = df.nlargest(5, 'Gain')['Feature'].tolist()
                        elif 'Importance' in df.columns:
                            top_features = df.nlargest(5, 'Importance')['Feature'].tolist()
                        else:
                            top_features = df['Feature'].head(5).tolist()
                        
                        print(f"     Top 5 features: {top_features}")
                        
                        scenario_results[symbol] = {
                            'file_exists': True,
                            'num_features': len(df),
                            'columns': list(df.columns),
                            'top_5_features': top_features
                        }
                    else:
                        print(f"     ⚠️ No 'Feature' column found")
                        scenario_results[symbol] = {
                            'file_exists': True,
                            'num_features': len(df),
                            'columns': list(df.columns),
                            'error': 'No Feature column'
                        }
                        
                except Exception as e:
                    print(f"     ❌ Error reading file: {e}")
                    scenario_results[symbol] = {
                        'file_exists': True,
                        'error': str(e)
                    }
            else:
                scenario_results[symbol] = {'file_exists': False}
        
        analysis_results[scenario] = scenario_results
    
    return analysis_results

def check_cross_asset_analysis_compatibility():
    """
    ตรวจสอบความเข้ากันได้สำหรับ analyze_cross_asset_feature_importance
    """
    print("\n🔍 ตรวจสอบความเข้ากันได้สำหรับ Cross-Asset Analysis")
    print("="*60)
    
    results_dir = Path(BASE_PATH) / "results"
    
    # ตรวจสอบว่า M60 folder มีไฟล์ feature importance หรือไม่
    m60_dir = results_dir / "M60"
    print(f"📁 M60 Directory: {m60_dir}")
    print(f"   Exists: {m60_dir.exists()}")
    
    if m60_dir.exists():
        symbols = ["AUDUSD", "GOLD", "USDJPY"]
        expected_files = []
        existing_files = []
        
        for symbol in symbols:
            expected_file = f"{str(TIMEFRAME).zfill(3)}_{symbol}_feature_importance.csv"
            file_path = m60_dir / expected_file
            
            expected_files.append(expected_file)
            if file_path.exists():
                existing_files.append(expected_file)
                print(f"   ✅ {expected_file}")
            else:
                print(f"   ❌ {expected_file}")
        
        print(f"\n📊 Summary:")
        print(f"   Expected files: {len(expected_files)}")
        print(f"   Existing files: {len(existing_files)}")
        print(f"   Coverage: {len(existing_files)}/{len(expected_files)} ({len(existing_files)/len(expected_files)*100:.1f}%)")
        
        if len(existing_files) >= 2:  # ต้องมีอย่างน้อย 2 ไฟล์
            print("   ✅ เพียงพอสำหรับ Cross-Asset Analysis")
            return True
        else:
            print("   ⚠️ ไม่เพียงพอสำหรับ Cross-Asset Analysis (ต้องการอย่างน้อย 2 ไฟล์)")
            return False
    else:
        print("   ❌ M60 directory not found")
        return False

def simulate_cross_asset_analysis():
    """
    จำลองการทำงานของ analyze_cross_asset_feature_importance
    """
    print("\n🔍 จำลองการทำงานของ Cross-Asset Analysis")
    print("="*50)
    
    results_dir = Path(BASE_PATH) / "results" / "M60"
    
    if not results_dir.exists():
        print("❌ M60 directory not found")
        return
    
    symbols = ["AUDUSD", "GOLD", "USDJPY"]
    all_features = {}
    processed_files = 0
    
    for symbol in symbols:
        file_path = results_dir / f"{str(TIMEFRAME).zfill(3)}_{symbol}_feature_importance.csv"
        
        if file_path.exists():
            try:
                df = pd.read_csv(file_path)
                processed_files += 1
                
                print(f"📄 Processing {symbol}: {len(df)} features")
                
                # ใช้ Gain หรือ Importance column
                importance_col = 'Gain' if 'Gain' in df.columns else 'Importance'
                
                if importance_col in df.columns and 'Feature' in df.columns:
                    # เลือก top 15 features
                    top_features = df.nlargest(15, importance_col)
                    
                    for _, row in top_features.iterrows():
                        feature = row['Feature']
                        importance = row[importance_col]
                        
                        if feature not in all_features:
                            all_features[feature] = {
                                'importances': [],
                                'symbols': []
                            }
                        
                        all_features[feature]['importances'].append(importance)
                        if symbol not in all_features[feature]['symbols']:
                            all_features[feature]['symbols'].append(symbol)
                
            except Exception as e:
                print(f"❌ Error processing {symbol}: {e}")
    
    print(f"\n📊 Analysis Results:")
    print(f"   Processed files: {processed_files}")
    print(f"   Total unique features: {len(all_features)}")
    
    # วิเคราะห์ features ที่ปรากฏในหลาย assets
    feature_summary = []
    for feature, data in all_features.items():
        avg_importance = sum(data['importances']) / len(data['importances'])
        asset_count = len(data['symbols'])
        
        feature_summary.append({
            'feature': feature,
            'avg_importance': avg_importance,
            'asset_count': asset_count,
            'symbols': data['symbols']
        })
    
    # เรียงตาม asset_count และ avg_importance
    feature_summary.sort(key=lambda x: (x['asset_count'], x['avg_importance']), reverse=True)
    
    print(f"\n📋 Top Features by Asset Count:")
    for i, item in enumerate(feature_summary[:10], 1):
        print(f"   {i}. {item['feature']}")
        print(f"      Asset count: {item['asset_count']}")
        print(f"      Avg importance: {item['avg_importance']:.4f}")
        print(f"      Symbols: {item['symbols']}")
    
    # จำลองการเลือก features ตามเกณฑ์
    min_assets_threshold = 2
    overall_top_n = 8
    
    # เลือก features ที่ปรากฏในอย่างน้อย 2 assets
    qualified_features = [item for item in feature_summary if item['asset_count'] >= min_assets_threshold]
    
    # เลือก top 8
    selected_features = [item['feature'] for item in qualified_features[:overall_top_n]]
    
    print(f"\n🎯 Selected Features (min_assets={min_assets_threshold}, top_n={overall_top_n}):")
    print(f"   Qualified features: {len(qualified_features)}")
    print(f"   Selected features: {len(selected_features)}")
    
    for i, feature in enumerate(selected_features, 1):
        print(f"   {i}. {feature}")
    
    return selected_features

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🚀 วิเคราะห์การทำงานของ Feature Importance ใน Multi-Model Architecture")
    print("="*80)
    
    # 1. ตรวจสอบโครงสร้างไฟล์
    if not check_feature_importance_structure():
        return
    
    # 2. วิเคราะห์ must_have_features.pkl
    must_have_features = analyze_must_have_features()
    
    # 3. วิเคราะห์ไฟล์ Feature Importance CSV
    csv_analysis = analyze_feature_importance_files()
    
    # 4. ตรวจสอบความเข้ากันได้สำหรับ Cross-Asset Analysis
    cross_asset_compatible = check_cross_asset_analysis_compatibility()
    
    # 5. จำลองการทำงานของ Cross-Asset Analysis
    if cross_asset_compatible:
        simulated_features = simulate_cross_asset_analysis()
        
        # เปรียบเทียบกับ must_have_features
        if must_have_features:
            print(f"\n🔍 เปรียบเทียบผลลัพธ์:")
            print(f"   Current must_have_features: {len(must_have_features)}")
            print(f"   Simulated selected features: {len(simulated_features) if simulated_features else 0}")
            
            if simulated_features:
                common_features = set(must_have_features) & set(simulated_features)
                print(f"   Common features: {len(common_features)}")
                print(f"   Common: {list(common_features)}")
    
    print("\n" + "="*80)
    print("✅ การวิเคราะห์เสร็จสิ้น")

if __name__ == "__main__":
    main()

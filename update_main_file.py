#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
อัปเดตไฟล์หลัก python_LightGBM_15_Tuning.py ให้ใช้ไฟล์ CSV ที่แก้ไขแล้ว
"""

import os
import shutil
from datetime import datetime

def backup_main_file():
    """สำรองไฟล์หลักก่อนแก้ไข"""
    
    main_file = "python_LightGBM_15_Tuning.py"
    backup_file = f"python_LightGBM_15_Tuning_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
    
    if os.path.exists(main_file):
        shutil.copy2(main_file, backup_file)
        print(f"💾 สำรองไฟล์หลักไปยัง: {backup_file}")
        return backup_file
    else:
        print(f"❌ ไม่พบไฟล์หลัก: {main_file}")
        return None

def update_test_groups():
    """อัปเดต test_groups ให้ใช้ไฟล์ที่แก้ไขแล้ว"""
    
    main_file = "python_LightGBM_15_Tuning.py"
    
    # อ่านไฟล์หลัก
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # test_groups ใหม่
    new_test_groups = '''test_groups = {
    "M30": [
        "CSV_Files_Fixed/AUDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/EURGBP_M30_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GBPUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GOLD_M30_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_M30_FIXED.csv"
    ],
    "M60": [
        "CSV_Files_Fixed/AUDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/EURGBP_H1_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/GOLD_H1_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_H1_FIXED.csv"
    ]
}'''
    
    # ค้นหาและแทนที่ test_groups
    import re
    
    # Pattern สำหรับ test_groups ปัจจุบัน
    pattern = r'test_groups\s*=\s*\{[^}]*\}'
    
    # ค้นหา test_groups ที่ไม่ได้ comment
    matches = list(re.finditer(pattern, content, re.DOTALL))
    
    if matches:
        # ใช้ match แรกที่ไม่ได้ comment
        for match in matches:
            start_pos = match.start()
            # ตรวจสอบว่าไม่ได้ comment
            line_start = content.rfind('\n', 0, start_pos) + 1
            line_content = content[line_start:start_pos]
            
            if not line_content.strip().startswith('#'):
                # แทนที่ test_groups
                content = content[:match.start()] + new_test_groups + content[match.end():]
                print("✅ อัปเดต test_groups สำเร็จ")
                break
        else:
            print("❌ ไม่พบ test_groups ที่ไม่ได้ comment")
            return None
    else:
        print("❌ ไม่พบ test_groups ในไฟล์")
        return None
    
    return content

def update_csv_reading():
    """อัปเดตวิธีการอ่าน CSV"""
    
    main_file = "python_LightGBM_15_Tuning.py"
    
    # อ่านไฟล์หลัก
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # ค้นหาและแทนที่วิธีการอ่าน CSV
    old_csv_reading = '''        # df = pd.read_csv(file, header=None)

        # อ่านไฟล์แบบธรรมดา (1 คอลัมน์ก่อน)
        df = pd.read_csv(file, header=None)
        # แยกคอลัมน์ด้วยตัว '\\t'
        df = df[0].str.split('\\t', expand=True)
        # ลบแถวแรกออก (เพราะมันเป็น <DATE> <TIME> ...)
        df = df.drop(index=0).reset_index(drop=True)'''
    
    new_csv_reading = '''        # อ่านไฟล์ CSV ที่แก้ไขแล้ว (มี header และ comma separator)
        df = pd.read_csv(file)
        
        # ลบแถวแรกออก (เพราะมันเป็น <DATE> <TIME> ...)
        df = df.drop(index=0).reset_index(drop=True)'''
    
    if old_csv_reading in content:
        content = content.replace(old_csv_reading, new_csv_reading)
        print("✅ อัปเดตวิธีการอ่าน CSV สำเร็จ")
    else:
        print("⚠️ ไม่พบรูปแบบการอ่าน CSV ที่คาดหวัง")
        print("💡 อาจต้องแก้ไขด้วยตนเอง")
    
    return content

def verify_fixed_files():
    """ตรวจสอบไฟล์ที่แก้ไขแล้ว"""
    
    print("\n🔍 ตรวจสอบไฟล์ที่แก้ไขแล้ว")
    print("=" * 60)
    
    fixed_dir = "CSV_Files_Fixed"
    
    if not os.path.exists(fixed_dir):
        print(f"❌ ไม่พบโฟลเดอร์: {fixed_dir}")
        return False
    
    expected_files = [
        "AUDUSD_M30_FIXED.csv", "EURGBP_M30_FIXED.csv", "EURUSD_M30_FIXED.csv",
        "GBPUSD_M30_FIXED.csv", "GOLD_M30_FIXED.csv", "NZDUSD_M30_FIXED.csv",
        "USDCAD_M30_FIXED.csv", "USDJPY_M30_FIXED.csv",
        "AUDUSD_H1_FIXED.csv", "EURGBP_H1_FIXED.csv", "EURUSD_H1_FIXED.csv",
        "GBPUSD_H1_FIXED.csv", "GOLD_H1_FIXED.csv", "NZDUSD_H1_FIXED.csv",
        "USDCAD_H1_FIXED.csv", "USDJPY_H1_FIXED.csv"
    ]
    
    missing_files = []
    existing_files = []
    
    for file_name in expected_files:
        file_path = os.path.join(fixed_dir, file_name)
        if os.path.exists(file_path):
            existing_files.append(file_name)
        else:
            missing_files.append(file_name)
    
    print(f"📊 สถิติไฟล์:")
    print(f"   • ไฟล์ที่คาดหวัง: {len(expected_files)}")
    print(f"   • ไฟล์ที่มีอยู่: {len(existing_files)}")
    print(f"   • ไฟล์ที่หายไป: {len(missing_files)}")
    
    if missing_files:
        print(f"\n❌ ไฟล์ที่หายไป:")
        for file_name in missing_files:
            print(f"   • {file_name}")
        return False
    else:
        print(f"\n✅ ไฟล์ครบถ้วนทั้งหมด")
        return True

def test_updated_file():
    """ทดสอบไฟล์ที่อัปเดตแล้ว"""
    
    print("\n🧪 ทดสอบไฟล์ที่อัปเดตแล้ว")
    print("=" * 60)
    
    try:
        # ลองอ่านไฟล์ที่อัปเดตแล้ว
        with open("python_LightGBM_15_Tuning.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # ตรวจสอบว่ามี test_groups ใหม่
        if "CSV_Files_Fixed/" in content:
            print("✅ พบ test_groups ใหม่ที่ใช้ไฟล์ที่แก้ไขแล้ว")
        else:
            print("❌ ไม่พบ test_groups ใหม่")
            return False
        
        # ตรวจสอบว่าไม่มี syntax error
        try:
            compile(content, "python_LightGBM_15_Tuning.py", "exec")
            print("✅ ไม่มี syntax error")
        except SyntaxError as e:
            print(f"❌ พบ syntax error: {e}")
            return False
        
        print("✅ ไฟล์ผ่านการทดสอบเบื้องต้น")
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    
    print("🔧 อัปเดตไฟล์หลัก python_LightGBM_15_Tuning.py")
    print("=" * 80)
    print("🎯 วัตถุประสงค์:")
    print("   • อัปเดต test_groups ให้ใช้ไฟล์ที่แก้ไขแล้ว")
    print("   • ปรับปรุงวิธีการอ่าน CSV")
    print("   • ทดสอบการทำงานของไฟล์ที่อัปเดต")
    print("=" * 80)
    
    # 1. ตรวจสอบไฟล์ที่แก้ไขแล้ว
    if not verify_fixed_files():
        print("\n❌ ไฟล์ที่แก้ไขแล้วไม่ครบถ้วน")
        print("💡 รัน fix_all_csv_files.py ก่อน")
        return
    
    # 2. สำรองไฟล์หลัก
    backup_file = backup_main_file()
    if not backup_file:
        return
    
    try:
        # 3. อัปเดต test_groups
        print(f"\n🔄 อัปเดต test_groups...")
        content = update_test_groups()
        if not content:
            return
        
        # 4. อัปเดตวิธีการอ่าน CSV
        print(f"\n🔄 อัปเดตวิธีการอ่าน CSV...")
        content = update_csv_reading()
        
        # 5. บันทึกไฟล์ที่อัปเดต
        with open("python_LightGBM_15_Tuning.py", 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"\n💾 บันทึกไฟล์ที่อัปเดตแล้ว")
        
        # 6. ทดสอบไฟล์ที่อัปเดต
        if test_updated_file():
            print(f"\n🎉 อัปเดตไฟล์หลักสำเร็จ!")
            print(f"📋 สิ่งที่เปลี่ยนแปลง:")
            print(f"   • test_groups ใช้ไฟล์จาก CSV_Files_Fixed/")
            print(f"   • วิธีการอ่าน CSV เปลี่ยนเป็น pd.read_csv(file)")
            print(f"   • ไม่ต้องใช้ str.split() อีกต่อไป")
            
            print(f"\n✅ พร้อมใช้งาน!")
            print(f"💡 ทดสอบด้วยการรัน: python python_LightGBM_15_Tuning.py")
        else:
            print(f"\n❌ การทดสอบไม่ผ่าน")
            print(f"🔄 กู้คืนไฟล์เดิมจาก: {backup_file}")
            shutil.copy2(backup_file, "python_LightGBM_15_Tuning.py")
            
    except Exception as e:
        print(f"\n❌ เกิดข้อผิดพลาด: {e}")
        if backup_file:
            print(f"🔄 กู้คืนไฟล์เดิมจาก: {backup_file}")
            shutil.copy2(backup_file, "python_LightGBM_15_Tuning.py")

if __name__ == "__main__":
    main()
